
"use client";
import { useState, useEffect } from "react";
import React from "react";
import { Tooltip,} from '@mantine/core';
import { useSearchParams } from "next/navigation";
import Icon from '@mdi/react';

import { Accordion } from '@mantine/core';
import { mdiClipboardPulse,mdiTooth,mdiGraphql,mdiPill,mdiFileDocument,mdiCurrencyUsd,mdiSeatReclineExtra,mdiStethoscope,mdiAccountTie,mdiFileSign} from '@mdi/js';
import {
 

  Paper,

  Card,
  
  

} from '@mantine/core';
import "~/styles/tab.css";
import {Dentaire} from './Dentaire'
import {Parodontie} from './Parodontie'
import {Prescription} from './Prescription'
import {Certificats} from './Certificats'
import {EtatFinancier} from './EtatFinancier'
import {PlanSoins} from './PlanSoins'
// Mapping des paramètres URL vers les numéros d'onglets
const tabMapping: { [key: string]: number } = {
  'Dentaire': 1,
  'Parodontie': 2,
  'Prescription': 3,
  'Certificats': 4,
  'EtatFinancier': 5,
  'PlanSoins': 6,
 
 
};

function  SettingsPage() {
  
  const [toggleState, setToggleState] = useState(1);
  const searchParams = useSearchParams();

  // Effet pour lire les paramètres d'URL et définir l'onglet actif
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);
 

const icons = [
  //  { icon: <CalendarDays style={iconStyle} key="Settings" />, label: "General Settings" },
  {
    icon: (
      <Tooltip label="Dentaire"  position="bottom">
        <Icon path={mdiTooth} size={1} />
      </Tooltip>
    ),
    label: "Dentaire",
    key: "Dentaire",
  },
    {
    icon: (
      <Tooltip label="Parodontie"  position="bottom">
        <Icon path={mdiGraphql} size={1} />
      </Tooltip>
    ),
    label: "Parodontie",
    key: "Parodontie",
  },
    {
    icon: (
      <Tooltip label="Prescription"  position="bottom">
        <Icon path={mdiPill} size={1} />
      </Tooltip>
    ),
    label: "Prescription",
    key: "Prescription",
  },
    {
    icon: (
      <Tooltip label="Certificats et courriers"  position="bottom">
        <Icon path={mdiFileDocument} size={1} />
      </Tooltip>
    ),
    label: "Certificats",
    key: "Certificats",
  },
    {
    icon: (
      <Tooltip label="État financier"  position="bottom">
        <Icon path={mdiCurrencyUsd} size={1} />
      </Tooltip>
    ),
    label: "État financier",
    key: "etatfinancier",
  },
    {
    icon: (
      <Tooltip label="Plan de soins"  position="bottom">
        <Icon path={mdiClipboardPulse} size={1} />
      </Tooltip>
    ),
    label: "Plan de soins",
    key: "PlanSoins",
  },

];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
     case 1:
          return (<Dentaire/> )
     case 2:
          return (<Parodontie/> )
           case 3:
          return (<Prescription/> )
           case 4:
          return (<Certificats/> )
           case 5:
          return (<EtatFinancier/> )
           case 6:
          return (<PlanSoins/> )      
       
        default:
          return null;
  }
};
  return (
    <>
     
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
   <Paper p="xl" radius="md" withBorder mb="xl"w={"100%"} >
    
          {/* <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="blue">
                <Icon path={mdiClipboardPulse} size={1} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Salle d&apos;attente</Text>
                <Text fw={700} size="xl">24</Text>
              </div>
            </Group>
          </Card> */}

       
              <Accordion variant="contained">
      <Accordion.Item value="photos">
        
        <Accordion.Control icon={<Icon path={mdiSeatReclineExtra} size={1}  color="#3979ce" />}>
      
          Salle d&apos;attente
       
          
        </Accordion.Control>
        <Accordion.Panel>
          <Card withBorder p="md" radius="md">
          Salle d&apos;attente
        </Card>
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="print">
        <Accordion.Control icon={<Icon path={mdiStethoscope} size={1}  color="#3979ce"  />}>
        Visites ouvertes
        </Accordion.Control>
        <Accordion.Panel>
          <Card withBorder p="md" radius="md">
          Content
          </Card>
          </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="camera" >
        <Accordion.Control
          icon={<Icon path={mdiAccountTie} size={1}  color="#3979ce"  />}
        >
         Mlle RACHIE ALAM
        </Accordion.Control>
        <Accordion.Panel><Card withBorder p="md" radius="md">
          Content
          </Card></Accordion.Panel>
      </Accordion.Item>
        <Accordion.Item value="Fiche">
        <Accordion.Control
          icon={<Icon path={mdiFileSign} size={1}  color="#3979ce"  />}
        >
         Fiche de synthèse
        </Accordion.Control>
        <Accordion.Panel><Card withBorder p="md" radius="md">
          Content
          </Card></Accordion.Panel>
      </Accordion.Item>
    </Accordion>
      </Paper>
    

    </>
  );
}

export default SettingsPage;

 

 