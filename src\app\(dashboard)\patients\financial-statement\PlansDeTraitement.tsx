import { useState } from 'react';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronDown, IconChevronUp, IconSearch, IconSelector ,IconMinus,IconPlus, IconX} from '@tabler/icons-react';
import {
  Center,
  Group,
  keys,
  ScrollArea,
  Table,
  Text,
  TextInput,
  UnstyledButton,
  Select,
  ActionIcon,
  Card,
  //ThemeIcon,
  Tooltip,
  Modal,
  Switch, 
   Title,
   NumberInput,Button
} from '@mantine/core';
import classes from './TableFinancier.module.css';
import { modals } from '@mantine/modals';
import Icon from '@mdi/react';
import { mdiChevronLeft,mdiChevronRight ,mdiPageLast,mdiPageFirst,mdiDetails,mdiCurrencyUsd,mdiFolderLock,mdiViewHeadline,mdiPlaylistPlus} from '@mdi/js';
import { AccountBalance } from './AccountBalance';
import { FinancialHeader } from './FinancialHeader';
import { VisitTableLegend } from './VisitTableLegend';
import { ProcedureTable } from './ProcedureTable';
export interface TeethProcedure {
  code: string;
  name: string;
  teeth: string;
  price: number;
  discount: number;
  progress: number;
  comment?: string;
}
const procedures: TeethProcedure[] = [
  {
    code: 'EME',
    name: 'Empreinte D’étude',
    teeth: 'Plusieurs dents',
    price: 200,
    discount: 0,
    progress: 100,
    comment: '',
  },
  {
    code: 'DET',
    name: 'Détartrage',
    teeth: 'Plusieurs dents',
    price: 500,
    discount: 0,
    progress: 100,
    comment: '',
  },
];
 const mockData = {
    patient: { sign: -1, value: -200.0 },
    organizations: { value: 100.0 },
    balance: { sign: -1, value: -100.0 },
  };
interface RowData {
  date: string;
  montant_du: string;
  montant_encaisse: string;
  avancement: string;
  remise: string;
  etat: string;
  resteAregler: string;
  vide: string;
 
 
}

interface ThProps {
  children: React.ReactNode;
  reversed: boolean;
  sorted: boolean;
  onSort: () => void;
}

function Th({ children, reversed, sorted, onSort }: ThProps) {
  const Icon = sorted ? (reversed ? IconChevronUp : IconChevronDown) : IconSelector;
  return (
    <Table.Th className={classes.th}>
      <UnstyledButton onClick={onSort} className={classes.control}>
        <Group justify="space-between">
          <Text fw={500} fz="sm">
            {children}
          </Text>
          <Center className={classes.icon}>
            <Icon size={16} stroke={1.5} />
          </Center>
        </Group>
      </UnstyledButton>
    </Table.Th>
  );
}

function filterData(data: RowData[], search: string) {
  const query = search.toLowerCase().trim();
  return data.filter((item) =>
    keys(data[0]).some((key) => item[key].toLowerCase().includes(query))
  );
}

function sortData(
  data: RowData[],
  payload: { sortBy: keyof RowData | null; reversed: boolean; search: string }
) {
  const { sortBy } = payload;

  if (!sortBy) {
    return filterData(data, payload.search);
  }

  return filterData(
    [...data].sort((a, b) => {
      if (payload.reversed) {
        return b[sortBy].localeCompare(a[sortBy]);
      }

      return a[sortBy].localeCompare(b[sortBy]);
    }),
    payload.search
  );
}
const data = [
  {
    date: '26/04/2025',
    montant_du: '1 300,00',
    montant_encaisse: '0,00',
    avancement: '1 300,00(100.00 %)',
    remise: '0,00',
    etat: '',
    resteAregler: '1 300,00',
    vide:'',

  },

];
interface Plan {
  id: number;
  title: string;
  financial_status: {
    is_exempt: boolean;
    total: number;
    remaining_amount: number;
  };
}
type FinancialStatus = {
  is_exempt: boolean;
  has_remaining_amount: boolean;
  organization_affectation: {
    patient_remaining_amount: number;
  };
};
type Payment = {
  id: number;
  payment_date: string; // ISO date string
  payer: string;
  date: string; // ISO date string
  received_amount: number;
  plan_amount: number;
};
interface Traitement {
  showProcedures: boolean;
  onToggleProcedures: (checked: boolean) => void;
  onConfirm: () => void;
  plans: Plan[];
  selectedPlanId: number | null;
  onChange: (id: number) => void;
    payments: Payment[];
     financialStatus: FinancialStatus;
  paymentsCount: number;
  showForm: boolean;
  readOnly: boolean;
  closePlan: (e: React.MouseEvent<HTMLButtonElement>) => void;
  addPayment: () => void;
  cancel: () => void;
}

export function PlansDeTraitement({
  showProcedures,
  onToggleProcedures,
  onConfirm,
  plans,
  selectedPlanId,
  onChange,
  payments ,
financialStatus,
  showForm,
  readOnly,
  closePlan,
  addPayment,
  cancel,
}: Traitement) {
  const [search, setSearch] = useState('');
  const [sortedData, setSortedData] = useState(data);
  const [sortBy, setSortBy] = useState<keyof RowData | null>(null);
  const [reverseSortDirection, setReverseSortDirection] = useState(false);
 const [Reglementopened, { open:ReglementOpen, close:ReglementClose }] = useDisclosure(false);
    const [isVisible, setIsVisible] = useState(false); // State to control sidebar visibility
const toggleSidebar = () => {
       setIsVisible((prev) => !prev);
    };
    const openConfirmModal = () => {
    modals.openConfirmModal({
      title: 'Confirmation',
      centered: true,
      children: (
        <p>Êtes-vous sûr de vouloir clôturer ce plan de traitement&nbsp;?</p>
      ),
      labels: { confirm: 'Oui', cancel: 'Non' },
      confirmProps: { color: 'red' },
      onConfirm: onConfirm,
    });
  };
  // État pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
// États pour les éléments de la facture
  const [elementsTraitement, ] = useState([
    { code: 'Aucun élément trouvé', description: '', qte: 0, prix: 0, remise: 0, montant: 0 }
  ]);
  // Calcul du total
  const total = elementsTraitement.reduce((sum, item) => sum + item.montant, 0);

  const patient = {
    id: 5,
    full_name: 'Mr CAMAL HASSAN',
    gender: 'M',
    age: 45,
    file_number: '123456',
    default_insurance: 'CNSS',
  };

  const goBack = () => {
    window.history.back();
  };

  const refreshPatientFinancialStatus = (e: React.MouseEvent) => {
    console.log('Refreshing...');
  };

  const setSorting = (field: keyof RowData) => {
    const reversed = field === sortBy ? !reverseSortDirection : false;
    setReverseSortDirection(reversed);
    setSortBy(field);
    setSortedData(sortData(data, { sortBy: field, reversed, search }));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.currentTarget;
    setSearch(value);
    setSortedData(sortData(data, { sortBy, reversed: reverseSortDirection, search: value }));
  };
  const rows = sortedData.map((row) => (
    <Table.Tr key={row.date}>
      <Table.Td>{row.date}</Table.Td>
      <Table.Td>{row.montant_du}</Table.Td>
      <Table.Td>{row.montant_encaisse}</Table.Td>
       <Table.Td>{row.avancement}</Table.Td>
      <Table.Td>{row.remise}</Table.Td>
      <Table.Td>
      
      {row.etat === financial_status.payment_status === Réglé(e) ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#4caf50" }} />: ""};
      {row.etat === financial_status.payment_status === Réglé(e) partiellement ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#ff9800" }} />: ""};
      {row.etat === financial_status.payment_status === Non Réglé(e) ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#f44336" }} />: ""};
      {row.etat === financial_status.payment_status === Dispensé(e)/Clôturé(e) ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#9e9e9e" }} />: ""}; 
      </Table.Td>
       <Table.Td>{row.resteAregler}</Table.Td>
     <Table.Td>{row.vide}</Table.Td>
     <Table.Td>

      <Group gap="xs">
      <Tooltip label="Modifier">
      <ActionIcon
        variant="subtle"
        color="#3799ce"
        size="sm"
         onClick={(event) => {
       event.preventDefault();
       toggleSidebar();
       }}
      >
       <Icon path={mdiDetails} size={1} />
      </ActionIcon>
      </Tooltip>
      <Tooltip label="Modifier">
      <ActionIcon
        variant="subtle"
        color="#3799ce"
        size="sm"
        onClick={() => {ReglementOpen()}}
      >
       <Icon path={mdiCurrencyUsd} size={1} />
      </ActionIcon>
      </Tooltip>
      <Tooltip label="Modifier">
      <ActionIcon
        variant="subtle"
        color="#3799ce"
        size="sm"
       //onClick={() => {VouloirOpen()}}
       onClick={openConfirmModal}
      > 
       <Icon path={mdiFolderLock} size={1} />
      </ActionIcon>
      </Tooltip>
      </Group>
     </Table.Td>                      
     
    </Table.Tr>

  ));
 

const datas = (plans ?? []).map((plan) => ({
    value: plan.id.toString(),
    label: (
      <Group justify="space-between">
        <div>
          <Text size="sm" fw={500}>{plan.title}</Text>
        </div>
        {!plan.financial_status.is_exempt ? (
          <Text size="sm" c="dimmed">
            {plan.financial_status.remaining_amount.toLocaleString('fr-FR', {
              style: 'currency',
              currency: 'EUR',
            })}
            {' / '}
            {plan.financial_status.total.toLocaleString('fr-FR', {
              style: 'currency',
              currency: 'EUR',
            })}
          </Text>
        ) : (
          <Text size="sm" c="dimmed">
            {plan.financial_status.remaining_amount.toLocaleString('fr-FR', {
              style: 'currency',
              currency: 'EUR',
            })}
          </Text>
        )}
      </Group>
    ),
  }));
  type Plan = {
  id: number;
  title: string;
  financial_status: {
    gross_total: number;
    remaining_amount: number;
    global_discount: number;
    global_percentage_discount: number;
  };
};


const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
const vm = this;
vm.selectedPlan =
  vm.selectedPlan.financial_status.gross_total = [
  {
    items: [ /* liste de plans */ ]
  },
  {
    items: [ /* autre groupe de plans */ ]
  }
];

  const allPlans = (plans ?? []).flatMap(group => group.items ?? []);
  const handleSelectPlan = (planId: string | null) => {
  // Tu peux faire quelque chose ici, par exemple :
  console.log("Plan sélectionné :", planId);
  // ou setSelectedPlan(planId);
};
  return (
    <>
    <Card   radius="md" withBorder w={'100%'} h={70}>
       <FinancialHeader
      patient={patient}
      patientContainer={true}
      onGoBack={goBack}
      onRefresh={refreshPatientFinancialStatus}
    />

    </Card>
      <Card  radius="0"  w={'100%'} h={70} shadow="0px">
        <Group>
         <TextInput
        placeholder="Search by any field"
        mb="md"
        leftSection={<IconSearch size={16} stroke={1.5} />}
        value={search}
        onChange={handleSearchChange}
        w={'60%'}
      />
  <AccountBalance {...mockData} />
  </Group>
      </Card>
    <ScrollArea>
     
      <Table horizontalSpacing="md" verticalSpacing="xs" miw={700} layout="fixed"  striped highlightOnHover withTableBorder withColumnBorders>
        <Table.Tbody>
          <Table.Tr>
            <Th
              sorted={sortBy === 'date'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('date')}
            >
              Date
            </Th>
            <Th
              sorted={sortBy === 'montant_du'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('montant_du')}
            >
              Montant dû
            </Th>
            <Th
              sorted={sortBy === 'montant_encaisse'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('montant_encaisse')}
            >
              Montant encaissé
            </Th>
             <Th
              sorted={sortBy === 'avancement'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('avancement')}
            >
              Avancement
            </Th>
             <Th
              sorted={sortBy === 'remise'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('remise')}
            >
              Remise
            </Th>
             <Th
              sorted={sortBy === 'etat'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('etat')}
            >
              État
            </Th>
             <Th
              sorted={sortBy === 'resteAregler'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('resteAregler')}
            >
              Reste à régler
            </Th>
           <Th
              sorted={sortBy === 'vide'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('vide')}
            >
              -
            </Th>
          </Table.Tr>
         
        </Table.Tbody>
        <Table.Tbody>
          {rows.length > 0 ? (
              <>
        {rows}
      </>
             
          ) : (
            <Table.Tr>
              <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                  Aucun élément trouvé.
                </Text>
              </Table.Td>
            </Table.Tr>
          )}
        </Table.Tbody>
         {/* Affichage conditionnel */}
    {isVisible && (
      <Table.Tr>
        <Table.Td colSpan={Object.keys(data[0]).length}>
          <Card>
            <ProcedureTable data={procedures} />
          </Card>
        </Table.Td>
      </Table.Tr>
    )}
      </Table>

           

      <VisitTableLegend />
 
    </ScrollArea>
     <div className="border-t border-gray-300 bg-gray-50 p-3 w-full">
            <Group justify="space-between" align="center">
              <Group gap="sm" align="center">
                <Text size="sm" className="text-gray-600">Page</Text>
                <Select
                  value={currentPage.toString()}
                  onChange={(value) => setCurrentPage(Number(value) || 1)}
                  data={['1', '2', '3', '4', '5']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                 <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    
                    <Icon path={mdiPageFirst} size={1} />
                  </ActionIcon>
                   <Select
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(Number(value) || 5)}
                  data={['5', '10', '20', '50']}
                  size="xs"
                  className="w-16"
                />
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiPageLast} size={1} />
                  </ActionIcon>
                </Group>
                
               
                <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                <Text size="sm" className="text-gray-600">K</Text>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconMinus size={12} />
                    
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconPlus size={12} />
                  </ActionIcon>
                </Group>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiChevronLeft} size={1} />
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiChevronRight} size={1} />
                  </ActionIcon>
                </Group>
              </Group>

              <Text size="lg" fw={600} className="text-gray-800">
                Total : {total.toFixed(2)}
              </Text>
            </Group>
          </div>
         

    
       <Modal opened={Reglementopened} onClose={ReglementClose} withCloseButton={false} size="xl">
         <Group justify="space-between" align="center" px="md" py="sm">
      <Group align="center">
       <Icon path={mdiViewHeadline} size={1} />
        <Title order={3} fw={600}>
          Règlement de plan de traitement
        </Title>
      </Group>

      <Group align="center" gap="xs">
        <Switch
          checked={showProcedures}
          onChange={(event) => onToggleProcedures(event.currentTarget.checked)}
          label="Afficher détails"
        />

        <Tooltip label="Fermer">
          <ActionIcon variant="subtle" color="red" onClick={ReglementClose}>
            <IconX size={20} />
          </ActionIcon>
        </Tooltip>
      </Group>
    </Group>
    <Group>
      <Select
      label="Plan de traitement"
      placeholder="Sélectionnez un plan"
      data={datas}
      value={selectedPlanId?.toString() ?? null}
      onChange={(value) => {
        const selectedId = value ? parseInt(value, 10) : null;
        if (selectedId !== null) onChange(selectedId);
      }}
      searchable
      nothingFound="Aucun plan trouvé"
      withAsterisk
      w={"50%"}
    />
      <Select
  data={allPlans.map(plan => ({
    value: String(plan.id),
    label: plan.title,
  }))}
  placeholder="Sélectionner un plan"
  onChange={(value) => handleSelectPlan(value)}
  w={"50%"}
/>
{selectedPlan && (
  <NumberInput
    value={selectedPlan.financial_status.gross_total}
    label="Montant dû"
    disabled
  />
)}
 {selectedPlan && (
        <div>
          <label>Montant dû</label>
          <input value={selectedPlan.financial_status.gross_total} disabled />
        </div>
      )}
    </Group>
     <div style={{ overflowX: 'auto' }}>
      <Table striped highlightOnHover withColumnBorders>
        <thead>
          <tr>
            <th>Date de paiement</th>
            <th>Patient / Payeur</th>
            <th>Date</th>
            <th style={{ textAlign: 'right' }}>Montant encaissé</th>
            <th style={{ textAlign: 'right' }}>Montant E. (Plan)</th>
          </tr>
        </thead>
        <tbody>
          {payments.length === 0 ? (
            <tr>
              <td colSpan={5}>
                <Text ta="center" fw={500}>
                  Aucun élément trouvé.
                </Text>
              </td>
            </tr>
          ) : (
            payments.map((payment) => (
              <tr key={payment.id}>
                <td>{new Date(payment.payment_date).toLocaleDateString()}</td>
                <td>{payment.payer}</td>
                <td>{new Date(payment.date).toLocaleDateString()}</td>
                <td style={{ textAlign: 'right' }}>
                  {payment.received_amount.toFixed(2)}
                </td>
                <td style={{ textAlign: 'right' }}>
                  {payment.plan_amount.toFixed(2)}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </Table>
    </div>
     <Group justify="center" style={{ marginTop: 20 }}>
      {/* Bouton Dispenser (exempt) */}
      {!financialStatus.is_exempt && (
        <Button
          color="red"
          onClick={closePlan}
          disabled={!financialStatus.has_remaining_amount}
          aria-label="exemptPlan"
          variant="filled"
        >
          Dispenser
        </Button>
      )}

      <div style={{ flex: 1 }} />

      {/* Bouton Ajouter */}
      {!showForm && (
        <Button
          leftSection={<Icon path={mdiPlaylistPlus} size={1} />}
          color="blue"
          onClick={addPayment}
          disabled={
            !financialStatus.has_remaining_amount ||
            financialStatus.organization_affectation.patient_remaining_amount === 0
          }
          aria-label="add new payment"
          variant="filled"
        >
          Ajouter
        </Button>
      )}

      {/* Bouton Annuler */}
      {(!readOnly && showForm) || (
        <Button
          color="red"
          onClick={cancel}
          aria-label="cancel"
          variant="filled"
        >
          Annuler
        </Button>
      )}
    </Group>
      </Modal>
       
          </>
  );
}

