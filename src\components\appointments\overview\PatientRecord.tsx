import React from 'react';
import { Container, Tabs, Box } from '@mantine/core';
import PatientInfoForm from './PatientInfoForm';
import { IconUser, IconShield, IconNotes, IconRuler, IconFiles } from '@tabler/icons-react';

// Import the PatientFormData interface from PatientInfoForm
interface PatientFormData {
  patientImage: File | null;
  title?: string;
  firstName?: string;
  lastName?: string;
  birthDate?: string;
  gender?: 'M' | 'F' | 'E';
  ethnicity?: string;
  fatherName?: string;
  motherName?: string;
  idNumber?: string;
  phone?: string;
  maritalStatus?: string;
  profession?: string;
  primaryDoctor?: string;
  referredBy?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  country?: string;
  city?: string;
  prefecture?: string;
  address?: string;
  comments?: string;
  paperFileNumber?: string;
  pricing?: string;
  isFavorite?: boolean;
  isInsured?: boolean;
  needsCompletion?: boolean;
  isDeceased?: boolean;
}

const PatientRecord: React.FC = () => {
  const handleSubmit = (data: PatientFormData) => {
    console.log('Form submitted:', data);
    // Handle form submission
  };

  const handleCancel = () => {
    console.log('Form cancelled');
    // Handle cancellation
  };

  return (
    <Container size="xl" className="py-4">
      <Tabs defaultValue="patient-info">
        <Tabs.List className="mb-4">
          <Tabs.Tab value="patient-info" leftSection={<IconUser size={16} />}>
            Fiche patient
          </Tabs.Tab>
          <Tabs.Tab value="assurance" leftSection={<IconShield size={16} />}>
            Assurance
          </Tabs.Tab>
          <Tabs.Tab value="fiche-medicale" leftSection={<IconNotes size={16} />}>
            Fiche médicale
          </Tabs.Tab>
          <Tabs.Tab value="biometrie" leftSection={<IconRuler size={16} />}>
            Biométrie
          </Tabs.Tab>
          <Tabs.Tab value="pieces-jointes" leftSection={<IconFiles size={16} />}>
            Pièces jointes
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="patient-info">
          <PatientInfoForm onSubmit={handleSubmit} onCancel={handleCancel} />
        </Tabs.Panel>

        <Tabs.Panel value="assurance">
          <Box className="p-4 bg-white rounded-md shadow-sm">
            Contenu de l&apos;onglet Assurance
          </Box>
        </Tabs.Panel>

        <Tabs.Panel value="fiche-medicale">
          <Box className="p-4 bg-white rounded-md shadow-sm">
            Contenu de l&apos;onglet Fiche médicale
          </Box>
        </Tabs.Panel>

        <Tabs.Panel value="biometrie">
          <Box className="p-4 bg-white rounded-md shadow-sm">
            Contenu de l&apos;onglet Biométrie
          </Box>
        </Tabs.Panel>

        <Tabs.Panel value="pieces-jointes">
          <Box className="p-4 bg-white rounded-md shadow-sm">
            Contenu de l&apos;onglet Pièces jointes
          </Box>
        </Tabs.Panel>
      </Tabs>
    </Container>
  );
};

export default PatientRecord;