// components/EncasementHeader.tsx
import { Group, Title, ActionIcon, Anchor, Box, Text } from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiCurrencyUsd, mdiFormatListBulleted } from '@mdi/js';

type EncasementHeaderProps = {
  readOnly: boolean;
};

export const EncasementHeader = ({ readOnly }: EncasementHeaderProps) => {
  return (
    <Box className="encasement-header" py="sm" px="md" bg="var(--mantine-color-blue-light)">
      <Group justify="space-between" align="center">
        <Group>
         💰 <Icon path={mdiCurrencyUsd} size={1.2} color="var(--mantine-color-blue-7)" />
          <Title order={3}>
            {!readOnly ? '  Nouvel encaissement' : ''}
          </Title>
        </Group>

        <Anchor href="/pratisoft/payment/" underline="never">
          <Group gap={4}>
            <ActionIcon variant="subtle" size="lg" aria-label="encasement list">
              <Icon path={mdiFormatListBulleted} size={1} />
            </ActionIcon>
            <Text size="sm">Recettes</Text>
          </Group>
        </Anchor>
      </Group>
    </Box>
  );
};
