import React from 'react'
import { Group} from '@mantine/core';
import classes from '~/styles/layout.module.css';
import FontSizeSlider from "./FontSizeSlider";
import { useFontSize } from "~/contexts/FontSizeContext";
import FullscreenToggle from './FullscreenToggle';
import Messages from "./Iconbar/Messages";
import Notifications from "./Iconbar/Notifications";
import Languages from "./Iconbar/Languages";
import UserActive from "./Iconbar/UserActive";
import dynamic from "next/dynamic";
const SwitchColorModes = dynamic(() => import("./Iconbar/SwitchColorModes"), { ssr: false });
function NavBarButton() {
    const { fontSize } = useFontSize();
    const groupClassName = (() => {
      if (fontSize === 10) return "h-[36px] pt-3";
      if (fontSize === 12) return "h-[39px] pt-2";
      if (fontSize === 14) return "h-[38px] pt-1.5";
      if (fontSize === 16) return "h-[38px] ";
      if (fontSize === 18 || fontSize === 20) return "h-[40px] ";
      if (fontSize === 22) return "h-[40px] ";
      return ""; // Default case
    })();
  return (
    <div className={`${classes.navBarButton}${groupClassName}  flex-1 absolute top-0 right-0 `} >
     
    <Group
          bg={"var(--mantine-bg-color)"}
         //className={`${groupClassName}`}
         style={{ borderBottom: "1px solid var(--border-color)",zIndex: "999",}}
         //justify="flex-end"
         w={"400px"}
      >
        <FullscreenToggle/><span className="-mx-3 text-[var(--bg-base-200)]">|</span>
        <FontSizeSlider /> <span className="-mx-3 text-[var(--bg-base-200)]">|</span>
        <Messages /><span className="-mx-3.5 text-[var(--bg-base-200)]">|</span>
        <Notifications /><span className="-mx-3.5 text-[var(--bg-base-200)]">|</span>
        <SwitchColorModes /> <span className="-mx-3.5 text-[var(--bg-base-200)] " >|</span>
        <Languages /><span className="-mx-3.5 text-[var(--bg-base-200)]">|</span>
        <UserActive /> 
    </Group>
  
    </div>
  )
}

export default NavBarButton