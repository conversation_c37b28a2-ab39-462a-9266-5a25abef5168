

import React, { useState, useMemo, useEffect } from 'react';
import { Table, ScrollArea, Text, Center, Loader, Select, Group, ActionIcon } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { IconMinus, IconPlus } from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiPageFirst, mdiPageLast, mdiChevronLeft, mdiChevronRight } from '@mdi/js';
interface PaymentBalance {
  value: number;
  sign: number;
}

interface EncashmentQuery {
  start_date: Date | string | null;
  end_date: Date | string | null;
  payment_mode: { id: string; value: string } | null;
  physician: { id: string; full_name: string } | null;
}

interface AccountBalance {
  patient: PaymentBalance;
  organizations: PaymentBalance;
  balance: PaymentBalance;
}
interface Encasement {
  id: string | number;
  encasement_date: string; // ISO string ou format date
  payer_type: string;
  payer_name: string;
  payment_mode: { type: { value: string } };
  total_amount: number;
  consumed_amount: number;
  remaining_amount: number;
  payments?: any[]; // optionnel, selon usage
}
interface Props {
  paymentModes: { id: string; value: string }[];
  physicians: { id: string; full_name: string }[];
  encasementQuery: EncashmentQuery;
  setEncasementQuery: React.Dispatch<React.SetStateAction<EncashmentQuery>>;
  getEncasementData: () => void;
  accountBalance: AccountBalance;
  encasements: Encasement[];
  loading: boolean;
}

export function Encaissements({
  paymentModes,
  physicians,
  encasementQuery,
  setEncasementQuery,
  getEncasementData,
  accountBalance,
  encasements,
   loading
}: Props) {
  // Handle changes and call getEncasementData on update
  useEffect(() => {
    getEncasementData();
  }, [encasementQuery.start_date, encasementQuery.end_date, encasementQuery.payment_mode, encasementQuery.physician]);
 const [sortAsc, setSortAsc] = useState(true);
 const [currentPage, setCurrentPage] = useState(1);
 const [itemsPerPage, setItemsPerPage] = useState(5);
 const total = 0; // Vous devrez calculer le total réel

  // fonction de tri par date
  const sortedEncasements = useMemo(() => {
    return [...encasements].sort((a, b) => {
      const dateA = new Date(a.encasement_date).getTime();
      const dateB = new Date(b.encasement_date).getTime();
      return sortAsc ? dateA - dateB : dateB - dateA;
    });
  }, [encasements, sortAsc]);

  // format monétaire FR
  const formatCurrency = (value: number) =>
    value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
  return (
    <>
    <Group align="center" gap="md" wrap="nowrap" style={{ width: '100%' }}>
      {/* Date début */}
      <DatePickerInput
        label="Date début"
        placeholder="Sélectionner une date"
        value={encasementQuery.start_date}
        onChange={(date) => setEncasementQuery((q) => ({ ...q, start_date: date }))}
        style={{ maxWidth: 200 }}
      />

      {/* Date Fin */}
      <DatePickerInput
        label="Date Fin"
        placeholder="Sélectionner une date"
        value={encasementQuery.end_date}
        onChange={(date) => setEncasementQuery((q) => ({ ...q, end_date: date }))}
        style={{ maxWidth: 200 }}
      />

      {/* Mode de paiement */}
      <Select
        label="Mode de paiement"
        placeholder="Mode de paiement"
        searchable
        value={encasementQuery.payment_mode?.id || null}
        onChange={(id) => {
          const selected = paymentModes.find((pm) => pm.id === id) || null;
          setEncasementQuery((q) => ({ ...q, payment_mode: selected }));
        }}
        data={paymentModes.map((pm) => ({ value: pm.id, label: pm.value }))}
        style={{ maxWidth: 200 }}
      />

      {/* Docteur */}
      <Select
        label="Docteur"
        placeholder="Docteur"
        searchable
        value={encasementQuery.physician?.id || null}
        onChange={(id) => {
          const selected = physicians.find((p) => p.id === id) || null;
          setEncasementQuery((q) => ({ ...q, physician: selected }));
        }}
        data={physicians.map((p) => ({ value: p.id, label: p.full_name }))}
        style={{ maxWidth: 200 }}
      />

      {/* Balance display */}
      <Group gap="sm" style={{ marginLeft: 'auto', minWidth: 300 }}>
        <Text fw={700}>Balance :</Text>

        <Text>Patient</Text>
        <Text
          c={accountBalance.patient.sign > 0 ? 'green' : accountBalance.patient.sign < 0 ? 'red' : 'dimmed'}
          fw={500}
        >
          {accountBalance.patient.value.toLocaleString(undefined, { style: 'currency', currency: 'EUR' })}
        </Text>

        <Text>Organismes</Text>
        <Text
          c={accountBalance.organizations.value < 0 ? 'red' : 'dimmed'}
          fw={500}
        >
          {accountBalance.organizations.value.toLocaleString(undefined, { style: 'currency', currency: 'EUR' })}
        </Text>

        <Text>Total</Text>
        <Text
          c={accountBalance.balance.sign > 0 ? 'green' : accountBalance.balance.sign < 0 ? 'red' : 'dimmed'}
          fw={500}
        >
          {accountBalance.balance.value.toLocaleString(undefined, { style: 'currency', currency: 'EUR' })}
        </Text>
      </Group>

    </Group>
     <ScrollArea>
      <Table striped highlightOnHover withTableBorder>
        <thead>
          <tr>
            <th
              style={{ cursor: 'pointer', userSelect: 'none' }}
              onClick={() => setSortAsc(!sortAsc)}
              aria-sort={sortAsc ? 'ascending' : 'descending'}
              scope="col"
            >
              Date{' '}
              <span style={{ fontSize: 12 }}>
                {sortAsc ? '▲' : '▼'}
              </span>
            </th>
            <th scope="col">Patient / Payeur</th>
            <th scope="col">Mode</th>
            <th style={{ textAlign: 'right' }} scope="col">
              Montant encaissé
            </th>
            <th style={{ textAlign: 'right' }} scope="col">
              Montant consommé
            </th>
            <th style={{ textAlign: 'right' }} scope="col">
              Reliquat
            </th>
            <th scope="col" style={{ width: 50 }}></th>
          </tr>
        </thead>
        <tbody>
          {loading && (
            <tr>
              <td colSpan={7}>
                <Center>
                  <Loader />
                </Center>
              </td>
            </tr>
          )}
          {!loading && sortedEncasements.length === 0 && (
            <tr>
              <td colSpan={7}>
                <Text ta="center" c="dimmed">
                  Aucun encaissement trouvé
                </Text>
              </td>
            </tr>
          )}
          {!loading &&
            sortedEncasements.map((encasement) => (
              <tr key={encasement.id}>
                <td>{encasement.encasement_date}</td>
                <td>
                  {encasement.payer_type !== 'O'
                    ? encasement.payer_name
                    : 'Organisme'}
                </td>
                <td>{encasement.payment_mode.type.value}</td>
                <td style={{ textAlign: 'right' }}>
                  {formatCurrency(encasement.total_amount)}
                </td>
                <td style={{ textAlign: 'right' }}>
                  {formatCurrency(encasement.consumed_amount)}
                </td>
                <td style={{ textAlign: 'right' }}>
                  {formatCurrency(encasement.remaining_amount)}
                </td>
                <td>{/* Actions ici */}</td>
              </tr>
            ))}
        </tbody>
      </Table>

    </ScrollArea>
     <div className="border-t border-gray-300 bg-gray-50 p-3 w-full">
            <Group justify="space-between" align="center">
              <Group gap="sm" align="center">
                <Text size="sm" className="text-gray-600">Page</Text>
                <Select
                  value={currentPage.toString()}
                  onChange={(value) => setCurrentPage(Number(value) || 1)}
                  data={['1', '2', '3', '4', '5']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                 <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    
                    <Icon path={mdiPageFirst} size={1} />
                  </ActionIcon>
                   <Select
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(Number(value) || 5)}
                  data={['5', '10', '20', '50']}
                  size="xs"
                  className="w-16"
                />
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiPageLast} size={1} />
                  </ActionIcon>
                </Group>
                
               
                <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                <Text size="sm" className="text-gray-600">K</Text>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconMinus size={12} />
                    
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconPlus size={12} />
                  </ActionIcon>
                </Group>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiChevronLeft} size={1} />
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiChevronRight} size={1} />
                  </ActionIcon>
                </Group>
              </Group>

              <Text size="lg" fw={600} className="text-gray-800">
                Total : {total.toFixed(2)}
              </Text>
            </Group>
          </div>
    </>
  );
}



