'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  NumberInput,
  Select,
  ActionIcon,
  Divider,
  Card,
  Stack,
  Text,
  Badge,
  Textarea,
  Table,
  ScrollArea,
  Modal,
  FileInput,
  Image,
  SimpleGrid,
  Tooltip,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconPlus,
  IconSearch,
  IconList,
  IconFile,
  IconCalendar,
  IconBuilding,
  IconCurrencyEuro,
  IconUpload,

  IconX,
  IconEdit,
  IconTrash,

} from '@tabler/icons-react';

interface ANouveauItem {
  id: string;
  numero: string;
  date: Date | null;
  fournisseur: string;
  factureNumber: string;
  montantTotal: number;
  status: 'draft' | 'validated' | 'paid';
  images?: string[];
  description?: string;
}

export default function ANouveauPage() {
  const [items, setItems] = useState<ANouveauItem[]>([
    {
      id: '1',
      numero: '1',
      date: new Date('2022-09-15'),
      fournisseur: 'Fournisseur A',
      factureNumber: 'F-001',
      montantTotal: 1250.00,
      status: 'validated',
      images: [],
      description: 'Description du premier élément'
    },
    {
      id: '2',
      numero: '2',
      date: new Date('2022-09-16'),
      fournisseur: 'Fournisseur B',
      factureNumber: 'F-002',
      montantTotal: 850.50,
      status: 'draft',
      images: [],
      description: 'Description du deuxième élément'
    }
  ]);

  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [opened, { open, close }] = useDisclosure(false);
  const [editingItem, setEditingItem] = useState<ANouveauItem | null>(null);
  const [viewMode, setViewMode] = useState<'form' | 'list'>('form');

  const form = useForm({
    initialValues: {
      numero: '',
      date: null as Date | null,
      fournisseur: '',
      factureNumber: '',
      montantTotal: 0,
      description: '',
    },
    validate: {
      numero: (value) => (!value ? 'Le numéro est requis' : null),
      date: (value) => (!value ? 'La date est requise' : null),
      fournisseur: (value) => (!value ? 'Le fournisseur est requis' : null),
      factureNumber: (value) => (!value ? 'Le numéro de facture est requis' : null),
      montantTotal: (value) => (value <= 0 ? 'Le montant doit être supérieur à 0' : null),
    },
  });

  const fournisseurs = [
    'Fournisseur A',
    'Fournisseur B',
    'Fournisseur C',
    'Fournisseur D',
  ];

  const handleImageUpload = (files: File[]) => {
    setSelectedImages(files);
    
    // Create preview URLs
    const previews = files.map(file => URL.createObjectURL(file));
    setPreviewImages(previews);
  };

  const removeImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    const newPreviews = previewImages.filter((_, i) => i !== index);
    
    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(previewImages[index]);
    
    setSelectedImages(newImages);
    setPreviewImages(newPreviews);
  };

  const handleSubmit = (values: typeof form.values) => {
    const newItem: ANouveauItem = {
      id: editingItem?.id || Date.now().toString(),
      numero: values.numero,
      date: values.date,
      fournisseur: values.fournisseur,
      factureNumber: values.factureNumber,
      montantTotal: values.montantTotal,
      status: 'draft',
      images: previewImages,
      description: values.description,
    };

    if (editingItem) {
      setItems(items.map(item => item.id === editingItem.id ? newItem : item));
      notifications.show({
        title: 'Succès',
        message: 'Élément modifié avec succès',
        color: 'green',
      });
    } else {
      setItems([...items, newItem]);
      notifications.show({
        title: 'Succès',
        message: 'Nouvel élément créé avec succès',
        color: 'green',
      });
    }

    form.reset();
    setSelectedImages([]);
    setPreviewImages([]);
    setEditingItem(null);
    close();
  };

  const handleEdit = (item: ANouveauItem) => {
    setEditingItem(item);
    form.setValues({
      numero: item.numero,
      date: item.date,
      fournisseur: item.fournisseur,
      factureNumber: item.factureNumber,
      montantTotal: item.montantTotal,
      description: item.description || '',
    });
    setPreviewImages(item.images || []);
    open();
  };

  const handleDelete = (id: string) => {
    setItems(items.filter(item => item.id !== id));
    notifications.show({
      title: 'Succès',
      message: 'Élément supprimé avec succès',
      color: 'red',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'validated': return 'green';
      case 'paid': return 'blue';
      default: return 'gray';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'validated': return 'Validé';
      case 'paid': return 'Payé';
      default: return 'Brouillon';
    }
  };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconFile size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            A-nouveau N°: {items.length + 1}
          </Title>
        </Group>
        <Group>
          <Button
            variant={viewMode === 'form' ? 'filled' : 'outline'}
            leftSection={<IconPlus size={16} />}
            onClick={() => {
              setViewMode('form');
              setEditingItem(null);
              form.reset();
              open();
            }}
          >
            Nouveau
          </Button>
          <Button
            variant={viewMode === 'list' ? 'filled' : 'outline'}
            leftSection={<IconList size={16} />}
            onClick={() => setViewMode('list')}
          >
            Liste des a-nouveaux
          </Button>
        </Group>
      </Group>

      {/* Main Content */}
      {viewMode === 'list' ? (
        <Card withBorder>
          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>N°</Table.Th>
                  <Table.Th>Date</Table.Th>
                  <Table.Th>Fournisseur</Table.Th>
                  <Table.Th>Facture N°</Table.Th>
                  <Table.Th>Montant Total</Table.Th>
                  <Table.Th>Statut</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {items.map((item) => (
                  <Table.Tr key={item.id}>
                    <Table.Td>{item.numero}</Table.Td>
                    <Table.Td>
                      {item.date ? item.date.toLocaleDateString('fr-FR') : '-'}
                    </Table.Td>
                    <Table.Td>{item.fournisseur}</Table.Td>
                    <Table.Td>{item.factureNumber}</Table.Td>
                    <Table.Td>{item.montantTotal.toFixed(2)} €</Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(item.status)}>
                        {getStatusLabel(item.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <Tooltip label="Modifier">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleEdit(item)}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Supprimer">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            onClick={() => handleDelete(item.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </ScrollArea>
        </Card>
      ) : (
        <Grid>
          <Grid.Col span={12}>
            <Card withBorder p="lg">
              <Text size="lg" fw={500} mb="md">
                Créer un nouveau A-nouveau
              </Text>
              <Button onClick={open} leftSection={<IconPlus size={16} />}>
                Ouvrir le formulaire
              </Button>
            </Card>
          </Grid.Col>
        </Grid>
      )}

      {/* Modal Form */}
      <Modal
        opened={opened}
        onClose={close}
        title={
          <Group>
            <IconFile size={20} />
            <Text fw={500}>
              {editingItem ? 'Modifier A-nouveau' : 'Nouveau A-nouveau'}
            </Text>
          </Group>
        }
        size="lg"
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="N° A-nouveau"
                  placeholder="Numéro"
                  leftSection={<IconFile size={16} />}
                  {...form.getInputProps('numero')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <DatePickerInput
                  label="Date"
                  placeholder="Sélectionner une date"
                  leftSection={<IconCalendar size={16} />}
                  {...form.getInputProps('date')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Select
              label="Fournisseur"
              placeholder="Sélectionner un fournisseur"
              data={fournisseurs}
              leftSection={<IconBuilding size={16} />}
              rightSection={<IconSearch size={16} />}
              searchable
              {...form.getInputProps('fournisseur')}
              required
            />

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Facture N°"
                  placeholder="Numéro de facture"
                  leftSection={<IconFile size={16} />}
                  {...form.getInputProps('factureNumber')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Montant Total"
                  placeholder="0.00"
                  leftSection={<IconCurrencyEuro size={16} />}
                  decimalScale={2}
                  fixedDecimalScale
                  {...form.getInputProps('montantTotal')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Description"
              placeholder="Description optionnelle"
              rows={3}
              {...form.getInputProps('description')}
            />

            <Divider label="Images" labelPosition="left" />

            <FileInput
              label="Ajouter des images"
              placeholder="Sélectionner des fichiers"
              leftSection={<IconUpload size={16} />}
              multiple
              accept="image/*"
              onChange={handleImageUpload}
            />

            {previewImages.length > 0 && (
              <SimpleGrid cols={3} spacing="sm">
                {previewImages.map((preview, index) => (
                  <div key={index} className="relative">
                    <Image
                      src={preview}
                      alt={`Preview ${index + 1}`}
                      height={100}
                      fit="cover"
                      radius="md"
                    />
                    <ActionIcon
                      size="sm"
                      color="red"
                      variant="filled"
                      className="absolute top-1 right-1"
                      onClick={() => removeImage(index)}
                    >
                      <IconX size={12} />
                    </ActionIcon>
                  </div>
                ))}
              </SimpleGrid>
            )}

            <Group justify="flex-end" mt="md">
              <Button variant="outline" onClick={close}>
                Annuler
              </Button>
              <Button type="submit" leftSection={<IconPlus size={16} />}>
                {editingItem ? 'Modifier' : 'Créer'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Paper>
  );
}
