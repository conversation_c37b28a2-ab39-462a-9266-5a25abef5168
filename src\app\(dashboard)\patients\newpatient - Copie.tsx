'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Container,
  Title,
  TextInput,
  Button,
  Group,
  Stack,
  Card,
  Textarea,
  Select,
  Divider,
  Text,
  Alert,

} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {  IconCheck, IconAlertCircle } from '@tabler/icons-react';
import Link from 'next/link';
import patientService from '@/services/patientService';
//import userManagementService from '@/services/userManagementService';

export default function AddPatientPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [doctors, setDoctors] = useState<{ value: string; label: string }[]>([]);
  const [visitDurations, setVisitDurations] = useState<{ value: string; label: string }[]>([]);

  // Calculate age based on date of birth
  const calculateAge = (dateOfBirth: Date | null) => {
    if (!dateOfBirth) return '';
    const today = new Date();
    let age = today.getFullYear() - dateOfBirth.getFullYear();
    const monthDiff = today.getMonth() - dateOfBirth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
      age--;
    }
    return age.toString();
  };

  // Update visit durations based on visit type
  const updateVisitDurations = (visitType: string) => {
    switch (visitType) {
      case 'diagnosis':
        setVisitDurations([
          { value: '20', label: '20 minutes' },
          { value: '30', label: '30 minutes' },
        ]);
        break;
      case 'examination':
        setVisitDurations([
          { value: '15', label: '15 minutes' },
          { value: '20', label: '20 minutes' },
        ]);
        break;
      case 're-diagnosis':
        setVisitDurations([
          { value: '10', label: '10 minutes' },
          { value: '15', label: '15 minutes' },
        ]);
        break;
      default:
        setVisitDurations([]);
    }
  };

  // Custom handlers for form fields
  const handleDateOfBirthChange = (value: Date | null) => {
    form.setFieldValue('dateOfBirth', value);
    if (value) {
      form.setFieldValue('age', calculateAge(value));
    } else {
      form.setFieldValue('age', '');
    }
  };

  const handleGenderChange = (value: string | null) => {
    form.setFieldValue('gender', value || '');
    // Clear ID fields if gender is child
    if (value === 'child') {
      form.setFieldValue('nationalIdNumber', '');
      form.setFieldValue('passportNumber', '');
    }
  };

  const handleVisitTypeChange = (value: string | null) => {
    form.setFieldValue('visitType', value || '');
    form.setFieldValue('visitDuration', '');
    if (value) {
      updateVisitDurations(value);
    }
  };

  // Fetch doctors on component mount
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        // In a real implementation, you would fetch doctors from an API
        // For now, we'll use a mock list
        const mockDoctors = [
          { value: 'doctor-1', label: 'Dr. John Smith' },
          { value: 'assistant-1', label: 'Dr. Jane Doe (Assistant)' },
          { value: 'assistant-2', label: 'Dr. Robert Johnson (Assistant)' },
        ];
        setDoctors(mockDoctors);
      } catch (error) {
        console.error('Error fetching doctors:', error);
      }
    };

    fetchDoctors();
  }, []);

  const form = useForm({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: null as Date | null,
      age: '',
      gender: '',
      nationalIdNumber: '',
      passportNumber: '',
      maritalStatus: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      medicalHistory: '',
      allergies: '',
      insuranceCompany: '',
      insurancePolicyNumber: '',
      emergencyContactName: '',
      emergencyContactPhone: '',
      emergencyContactRelationship: '',
      doctorAssigned: '',
      visitType: '',
      visitDuration: '',
      agenda: '',
      comments: '',
      diagnosticRoom: '',
      additionalNotes: '',
    },

    // Add event handlers for form fields
    transformValues: (values) => {
      // If date of birth is set, calculate and set age
      if (values.dateOfBirth && !values.age) {
        values.age = calculateAge(values.dateOfBirth);
      }
      return values;
    },
    validate: {
      firstName: (value) => (value.length < 1 ? 'First name is required' : null),
      lastName: (value) => (value.length < 1 ? 'Last name is required' : null),
      email: (value) => (/^\S+@\S+\.\S+$/.test(value) ? null : 'Invalid email'),
      phone: (value) => (value.length < 10 ? 'Valid phone number is required' : null),
      dateOfBirth: (value) => (!value ? 'Date of birth is required' : null),
      gender: (value) => (!value ? 'Gender is required' : null),
      visitType: (value) => (!value ? 'Visit type is required' : null),
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    setLoading(true);

    try {
      // Format the data to match the API expectations
      const patientData = {
        first_name: values.firstName,
        last_name: values.lastName,
        email: values.email,
        phone_number: values.phone,
        date_of_birth: values.dateOfBirth ? new Date(values.dateOfBirth).toISOString().split('T')[0] : undefined,
        age: values.age,
        gender: values.gender,
        national_id_number: values.gender !== 'child' ? values.nationalIdNumber : undefined,
        passport_number: values.gender !== 'child' ? values.passportNumber : undefined,
        marital_status: values.maritalStatus,
        address: values.address ? `${values.address}, ${values.city}, ${values.state} ${values.zipCode}` : undefined,
        medical_history: values.medicalHistory,
        insurance_company: values.insuranceCompany,
        insurance_policy_number: values.insurancePolicyNumber,
        doctor_assigned: values.doctorAssigned,
        visit_type: values.visitType,
        visit_duration: values.visitDuration,
        agenda: values.agenda,
        comments: values.comments,
        diagnostic_room: values.diagnosticRoom,
        additional_notes: values.additionalNotes,
      };

      // Send the data to the API via the patient service
      const result = await patientService.createPatient(patientData);

      if (result) {
        notifications.show({
          title: 'Success',
          message: 'Patient added successfully',
          color: 'green',
          icon: <IconCheck size={16} />,
        });

        // Redirect to patients list
        router.push('/patients');
      } else {
        throw new Error('Failed to create patient');
      }
    } catch (error:any) {
      console.error('Error adding patient:', error);

      // Extract detailed error message if available
      const errorMessage = error?.response?.data?.detail ||
                          error?.response?.data?.message ||
                          error?.message ||
                          'Failed to add patient. Please try again.';

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container size="lg" py="xl">
     {/* <Group mb="lg">
         <Button
          variant="outline"
          leftSection={<IconArrowLeft size={16} />}
          component={Link}
          href="/patients"
        >
          Back to Patients
        </Button>
      </Group> */}

      {/* <Title order={1} mb="lg">Add New Patient</Title> */}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Card withBorder p="xl" radius="md">
          <Stack>
            <Title order={3}>Personal Information</Title>

            <Group grow>
              <TextInput
                label="First Name"
                placeholder="John"
                required
                {...form.getInputProps('firstName')}
              />

              <TextInput
                label="Last Name"
                placeholder="Doe"
                required
                {...form.getInputProps('lastName')}
              />
            </Group>

            <Group grow>
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                required
                {...form.getInputProps('email')}
              />

              <TextInput
                label="Phone"
                placeholder="(*************"
                required
                {...form.getInputProps('phone')}
              />
            </Group>

            <Group grow>
              <DateInput
                label="Date of Birth"
                placeholder="Select date"
                valueFormat="YYYY-MM-DD"
                clearable
                required
                {...form.getInputProps('dateOfBirth', {
                  type: 'input',
                  onChange: handleDateOfBirthChange
                })}
              />

              <TextInput
                label="Age"
                placeholder="Calculated from DOB"
                readOnly
                {...form.getInputProps('age')}
              />
            </Group>

            <Group grow>
              <Select
                label="Gender"
                placeholder="Select gender"
                required
                data={[
                  { value: 'male', label: 'Male' },
                  { value: 'female', label: 'Female' },
                  { value: 'child', label: 'Child' },
                  { value: 'other', label: 'Other' },
                ]}
                {...form.getInputProps('gender', {
                  type: 'input',
                  onChange: handleGenderChange
                })}
              />

              <Select
                label="Marital Status"
                placeholder="Select marital status"
                data={[
                  { value: 'single', label: 'Single' },
                  { value: 'married', label: 'Married' },
                ]}
                {...form.getInputProps('maritalStatus')}
              />
            </Group>

            {form.values.gender !== 'child' && (
              <Group grow>
                <TextInput
                  label="National ID Number"
                  placeholder="Enter national ID number"
                  {...form.getInputProps('nationalIdNumber')}
                />

                <TextInput
                  label="Passport Number"
                  placeholder="Enter passport number"
                  {...form.getInputProps('passportNumber')}
                />
              </Group>
            )}

            <Divider my="md" />

            <Title order={3}>Address</Title>

            <TextInput
              label="Street Address"
              placeholder="123 Main St"
              {...form.getInputProps('address')}
            />

            <Group grow>
              <TextInput
                label="City"
                placeholder="New York"
                {...form.getInputProps('city')}
              />

              <TextInput
                label="State/Province"
                placeholder="NY"
                {...form.getInputProps('state')}
              />

              <TextInput
                label="Zip/Postal Code"
                placeholder="10001"
                {...form.getInputProps('zipCode')}
              />
            </Group>

            <Divider my="md" />

            <Title order={3}>Medical Information</Title>

            <Textarea
              label="Medical History"
              placeholder="Enter any relevant medical history..."
              minRows={3}
              {...form.getInputProps('medicalHistory')}
            />

            <Textarea
              label="Allergies"
              placeholder="Enter any allergies..."
              minRows={2}
              {...form.getInputProps('allergies')}
            />

            <Divider my="md" />

            <Title order={3}>Insurance Information</Title>

            <Group grow>
              <Select
                label="Insurance Company"
                placeholder="Select insurance company"
                data={[
                  { value: 'aetna', label: 'Aetna' },
                  { value: 'blue-cross', label: 'Blue Cross Blue Shield' },
                  { value: 'cigna', label: 'Cigna' },
                  { value: 'humana', label: 'Humana' },
                  { value: 'kaiser', label: 'Kaiser Permanente' },
                  { value: 'medicare', label: 'Medicare' },
                  { value: 'medicaid', label: 'Medicaid' },
                  { value: 'united', label: 'UnitedHealthcare' },
                  { value: 'other', label: 'Other' },
                  { value: 'none', label: 'No Insurance' },
                ]}
                {...form.getInputProps('insuranceCompany')}
              />

              <TextInput
                label="Policy Number"
                placeholder="Enter insurance policy number"
                {...form.getInputProps('insurancePolicyNumber')}
              />
            </Group>

            <Divider my="md" />

            <Title order={3}>Visit Information</Title>

            <Group grow>
              <Select
                label="Doctor Assigned"
                placeholder="Select doctor"
                data={doctors}
                {...form.getInputProps('doctorAssigned')}
              />

              <Select
                label="Visit Type"
                placeholder="Select visit type"
                required
                data={[
                  { value: 'diagnosis', label: 'Diagnosis' },
                  { value: 'examination', label: 'Examination' },
                  { value: 're-diagnosis', label: 'Re-diagnosis' },
                ]}
                {...form.getInputProps('visitType', {
                  type: 'input',
                  onChange: handleVisitTypeChange
                })}
              />
            </Group>

            <Group grow>
              <Select
                label="Visit Duration"
                placeholder="Select duration"
                data={visitDurations}
                disabled={!form.values.visitType}
                {...form.getInputProps('visitDuration')}
              />

              <Select
                label="Diagnostic Room"
                placeholder="Select room"
                data={[
                  { value: 'room-a', label: 'Room A' },
                  { value: 'room-b', label: 'Room B' },
                ]}
                {...form.getInputProps('diagnosticRoom')}
              />
            </Group>

            <Group grow>
              <Select
                label="Agenda"
                placeholder="Select agenda"
                data={[
                  { value: 'regular', label: 'Regular Visit' },
                  { value: 'follow-up', label: 'Follow-up' },
                  { value: 'emergency', label: 'Emergency' },
                  { value: 'consultation', label: 'Consultation' },
                ]}
                {...form.getInputProps('agenda')}
              />
            </Group>

            <Textarea
              label="Comments"
              placeholder="Enter any additional comments..."
              minRows={2}
              {...form.getInputProps('comments')}
            />

            <Divider my="md" />

            <Title order={3}>Emergency Contact</Title>

            <TextInput
              label="Name"
              placeholder="Jane Doe"
              {...form.getInputProps('emergencyContactName')}
            />

            <Group grow>
              <TextInput
                label="Phone"
                placeholder="(*************"
                {...form.getInputProps('emergencyContactPhone')}
              />

              <TextInput
                label="Relationship"
                placeholder="Spouse"
                {...form.getInputProps('emergencyContactRelationship')}
              />
            </Group>

            <Divider my="md" />

            <Title order={3}>Additional Information</Title>

            <Textarea
              label="Additional Notes"
              placeholder="Enter any additional notes or information about the patient..."
              minRows={3}
              {...form.getInputProps('additionalNotes')}
            />

            <Alert color="blue" icon={<IconAlertCircle size={16} />} mt="md">
              <Text fw={500}>Privacy Notice</Text>
              <Text size="sm">
                Patient information is protected under HIPAA regulations. This data will be stored securely
                and only accessed by authorized personnel.
              </Text>
            </Alert>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" component={Link} href="/patients">
                Cancel
              </Button>
              <Button type="submit" loading={loading}>
                Add Patient
              </Button>
            </Group>
          </Stack>
        </Card>
      </form>
    </Container>
  );
}
