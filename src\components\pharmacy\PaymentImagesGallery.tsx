'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  ActionIcon,
  Card,
  Stack,
  Text,
  Badge,
  Modal,
  FileInput,
  Image,
  SimpleGrid,
  Pagination,
  Box,
  Center,
  Textarea,
  Divider,
  NumberInput,
  Table,
  ScrollArea,

} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconPhoto,
  IconUpload,
  IconEye,
  IconEdit,
  IconTrash,
  IconFilter,
  IconReceipt,
  IconCalendar,
  IconUser,
  
  IconFileInvoice,

  IconBuildingBank,
  IconCheck,
  IconX,
  IconClock,
  IconAlertTriangle,
  IconDownload,
  IconShare,
  IconPrinter,
  IconMail,
 
} from '@tabler/icons-react';

interface PaymentImageItem {
  id: string;
  documentType: string;
  documentNumber: string;
  imageUrl: string;
  thumbnail: string;
  supplierName: string;
  amount: number;
  currency: string;
  paymentDate: Date;
  dueDate: Date;
  status: 'paid' | 'pending' | 'overdue' | 'cancelled';
  paymentMethod: string;
  category: string;
  uploadedBy: string;
  uploadDate: Date;
  description: string;
  tags: string[];
  reference: string;
  bankAccount: string;
}

export default function PaymentImagesGallery() {
  const [paymentImages, setPaymentImages] = useState<PaymentImageItem[]>([
    {
      id: '1',
      documentType: 'Facture',
      documentNumber: 'FAC-2024-001',
      imageUrl: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=300&h=200&fit=crop',
      supplierName: 'Laboratoire Pharma Plus',
      amount: 2450.00,
      currency: 'EUR',
      paymentDate: new Date('2024-01-15'),
      dueDate: new Date('2024-02-15'),
      status: 'paid',
      paymentMethod: 'Virement bancaire',
      category: 'Médicaments',
      uploadedBy: 'Dr. Martin',
      uploadDate: new Date('2024-01-16'),
      description: 'Facture pour commande de médicaments janvier 2024',
      tags: ['facture', 'médicaments', 'laboratoire'],
      reference: 'REF-2024-001',
      bankAccount: 'FR76 1234 5678 9012 3456 789',
    },
    {
      id: '2',
      documentType: 'Reçu',
      documentNumber: 'REC-2024-002',
      imageUrl: 'https://images.unsplash.com/photo-*************-b95a79798f07?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-*************-b95a79798f07?w=300&h=200&fit=crop',
      supplierName: 'Équipements Médicaux SA',
      amount: 890.50,
      currency: 'EUR',
      paymentDate: new Date('2024-01-12'),
      dueDate: new Date('2024-01-25'),
      status: 'pending',
      paymentMethod: 'Chèque',
      category: 'Équipements',
      uploadedBy: 'Comptable Claire',
      uploadDate: new Date('2024-01-13'),
      description: 'Reçu pour achat d\'équipements médicaux',
      tags: ['reçu', 'équipements', 'matériel'],
      reference: 'REF-2024-002',
      bankAccount: 'FR76 9876 5432 1098 7654 321',
    },
    {
      id: '3',
      documentType: 'Facture',
      documentNumber: 'FAC-2024-003',
      imageUrl: 'https://images.unsplash.com/photo-*************-c8848c66ca85?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-*************-c8848c66ca85?w=300&h=200&fit=crop',
      supplierName: 'Services Informatiques Pro',
      amount: 1200.00,
      currency: 'EUR',
      paymentDate: new Date('2024-01-08'),
      dueDate: new Date('2024-01-08'),
      status: 'overdue',
      paymentMethod: 'Carte bancaire',
      category: 'Services',
      uploadedBy: 'Gestionnaire IT',
      uploadDate: new Date('2024-01-09'),
      description: 'Facture pour maintenance système informatique',
      tags: ['facture', 'informatique', 'maintenance'],
      reference: 'REF-2024-003',
      bankAccount: 'FR76 1111 2222 3333 4444 555',
    },
    {
      id: '4',
      documentType: 'Bon de commande',
      documentNumber: 'BC-2024-004',
      imageUrl: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=300&h=200&fit=crop',
      supplierName: 'Fournitures Bureau Plus',
      amount: 345.75,
      currency: 'EUR',
      paymentDate: new Date('2024-01-20'),
      dueDate: new Date('2024-02-20'),
      status: 'cancelled',
      paymentMethod: 'Espèces',
      category: 'Fournitures',
      uploadedBy: 'Secrétaire Admin',
      uploadDate: new Date('2024-01-21'),
      description: 'Bon de commande pour fournitures de bureau',
      tags: ['bon-commande', 'fournitures', 'bureau'],
      reference: 'REF-2024-004',
      bankAccount: 'FR76 5555 6666 7777 8888 999',
    },
  ]);

  const [selectedImage, setSelectedImage] = useState<PaymentImageItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  const [opened, { open, close }] = useDisclosure(false);
  const [viewerOpened, { open: openViewer, close: closeViewer }] = useDisclosure(false);
  const [uploadOpened, { open: openUpload, close: closeUpload }] = useDisclosure(false);

  const form = useForm({
    initialValues: {
      documentType: '',
      documentNumber: '',
      supplierName: '',
      amount: 0,
      paymentMethod: '',
      category: '',
      description: '',
      tags: '',
      reference: '',
      bankAccount: '',
      file: null as File | null,
    },
  });

  const categories = [
    'Médicaments',
    'Équipements',
    'Services',
    'Fournitures',
    'Maintenance',
    'Assurance',
    'Utilities',
  ];

  const documentTypes = [
    'Facture',
    'Reçu',
    'Bon de commande',
    'Devis',
    'Avoir',
    'Relevé bancaire',
  ];

  const paymentMethods = [
    'Virement bancaire',
    'Chèque',
    'Carte bancaire',
    'Espèces',
    'Prélèvement',
  ];

  const statusOptions = [
    { value: 'paid', label: 'Payé', color: 'green' },
    { value: 'pending', label: 'En attente', color: 'orange' },
    { value: 'overdue', label: 'En retard', color: 'red' },
    { value: 'cancelled', label: 'Annulé', color: 'gray' },
  ];

  const itemsPerPage = 12;

  const filteredImages = paymentImages.filter((item) => {
    const matchesSearch = item.supplierName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.documentNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || item.category === selectedCategory;
    const matchesStatus = !selectedStatus || item.status === selectedStatus;
    const matchesPaymentMethod = !selectedPaymentMethod || item.paymentMethod === selectedPaymentMethod;
    return matchesSearch && matchesCategory && matchesStatus && matchesPaymentMethod;
  });

  const totalPages = Math.ceil(filteredImages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedImages = filteredImages.slice(startIndex, startIndex + itemsPerPage);

  const getStatusColor = (status: string) => {
    const statusMap = {
      'paid': 'green',
      'pending': 'orange',
      'overdue': 'red',
      'cancelled': 'gray',
    };
    return statusMap[status as keyof typeof statusMap] || 'gray';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <IconCheck size={16} />;
      case 'pending': return <IconClock size={16} />;
      case 'overdue': return <IconAlertTriangle size={16} />;
      case 'cancelled': return <IconX size={16} />;
      default: return <IconReceipt size={16} />;
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const handleImageUpload = async (values: typeof form.values) => {
    if (!values.file) return;

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newItem: PaymentImageItem = {
        id: Date.now().toString(),
        documentType: values.documentType,
        documentNumber: values.documentNumber,
        imageUrl: URL.createObjectURL(values.file),
        thumbnail: URL.createObjectURL(values.file),
        supplierName: values.supplierName,
        amount: values.amount,
        currency: 'EUR',
        paymentDate: new Date(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        status: 'pending',
        paymentMethod: values.paymentMethod,
        category: values.category,
        uploadedBy: 'Utilisateur actuel',
        uploadDate: new Date(),
        description: values.description,
        tags: values.tags.split(',').map(tag => tag.trim()),
        reference: values.reference,
        bankAccount: values.bankAccount,
      };

      setPaymentImages(prev => [newItem, ...prev]);
      form.reset();
      closeUpload();

      notifications.show({
        title: 'Succès',
        message: 'Document ajouté avec succès',
        color: 'green',
      });

    } catch {
  notifications.show({
    title: 'Erreur',
    message: 'Erreur lors de l\'ajout du document',
    color: 'red',
  });
    }
  };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconReceipt size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Galerie Documents Financiers
          </Title>
        </Group>
        <Group>
          <Button
            leftSection={<IconUpload size={16} />}
            onClick={openUpload}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Ajouter Document
          </Button>
          <Button
            variant="outline"
            leftSection={<IconFilter size={16} />}
            onClick={open}
          >
            Filtres
          </Button>
        </Group>
      </Group>

      {/* Search and Filters */}
      <Grid mb="md">
        <Grid.Col span={3}>
          <TextInput
            placeholder="Rechercher documents..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Select
            placeholder="Catégorie"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Select
            placeholder="Statut"
            data={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Select
            placeholder="Mode paiement"
            data={paymentMethods}
            value={selectedPaymentMethod}
            onChange={setSelectedPaymentMethod}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Group>
            <ActionIcon
              variant={viewMode === 'grid' ? 'filled' : 'outline'}
              onClick={() => setViewMode('grid')}
            >
              <IconPhoto size={16} />
            </ActionIcon>
            <ActionIcon
              variant={viewMode === 'table' ? 'filled' : 'outline'}
              onClick={() => setViewMode('table')}
            >
              <IconFileInvoice size={16} />
            </ActionIcon>
          </Group>
        </Grid.Col>
      </Grid>

      {/* Content Display */}
      {paginatedImages.length === 0 ? (
        <Center h={300}>
          <Stack align="center">
            <IconReceipt size={48} className="text-gray-400" />
            <Text c="dimmed">Aucun document trouvé</Text>
          </Stack>
        </Center>
      ) : viewMode === 'grid' ? (
        <SimpleGrid
          cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
          spacing="md"
          mb="xl"
        >
          {paginatedImages.map((item) => (
            <Card key={item.id} withBorder shadow="sm" className="hover:shadow-md transition-shadow">
              <Card.Section>
                <Box pos="relative">
                  <Image
                    src={item.thumbnail}
                    height={200}
                    alt={item.documentNumber}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedImage(item);
                      openViewer();
                    }}
                  />
                  <Badge
                    pos="absolute"
                    top={8}
                    right={8}
                    color={getStatusColor(item.status)}
                    leftSection={getStatusIcon(item.status)}
                  >
                    {statusOptions.find(s => s.value === item.status)?.label}
                  </Badge>
                  <Badge
                    pos="absolute"
                    top={8}
                    left={8}
                    color="blue"
                    variant="light"
                  >
                    {item.documentType}
                  </Badge>
                </Box>
              </Card.Section>

              <Stack gap="xs" mt="md">
                <Group justify="space-between">
                  <Text fw={500} size="sm" truncate>
                    {item.documentNumber}
                  </Text>
                  <Text size="xs" fw={600} c="blue">
                    {formatCurrency(item.amount, item.currency)}
                  </Text>
                </Group>

                <Text size="xs" c="dimmed" truncate>
                  {item.supplierName}
                </Text>

                <Text size="xs" c="dimmed" lineClamp={2}>
                  {item.description}
                </Text>

                <Group justify="space-between">
                  <Badge size="xs" color="gray" variant="light">
                    {item.category}
                  </Badge>
                  <Text size="xs" c="dimmed">
                    {item.paymentDate.toLocaleDateString()}
                  </Text>
                </Group>

                <Group justify="space-between" mt="xs">
                  <Text size="xs" c="dimmed">
                    {item.paymentMethod}
                  </Text>
                  <Group gap="xs">
                    <ActionIcon size="sm" variant="subtle" onClick={() => {
                      setSelectedImage(item);
                      openViewer();
                    }}>
                      <IconEye size={14} />
                    </ActionIcon>
                    <ActionIcon size="sm" variant="subtle">
                      <IconEdit size={14} />
                    </ActionIcon>
                    <ActionIcon size="sm" variant="subtle">
                      <IconPrinter size={14} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>
      ) : (
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Document</Table.Th>
                <Table.Th>Fournisseur</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Montant</Table.Th>
                <Table.Th>Date</Table.Th>
                <Table.Th>Statut</Table.Th>
                <Table.Th>Paiement</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedImages.map((item) => (
                <Table.Tr key={item.id}>
                  <Table.Td>
                    <Group gap="sm">
                      <Image
                        src={item.thumbnail}
                        width={50}
                        height={35}
                        alt={item.documentNumber}
                        className="cursor-pointer rounded"
                        onClick={() => {
                          setSelectedImage(item);
                          openViewer();
                        }}
                      />
                      <Stack gap={2}>
                        <Text fw={500} size="sm">{item.documentNumber}</Text>
                        <Text size="xs" c="dimmed">{item.reference}</Text>
                      </Stack>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{item.supplierName}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge size="sm" color="blue" variant="light">
                      {item.documentType}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text fw={600} c="blue">
                      {formatCurrency(item.amount, item.currency)}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{item.paymentDate.toLocaleDateString()}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={getStatusColor(item.status)}
                      leftSection={getStatusIcon(item.status)}
                    >
                      {statusOptions.find(s => s.value === item.status)?.label}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{item.paymentMethod}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon size="sm" variant="subtle" onClick={() => {
                        setSelectedImage(item);
                        openViewer();
                      }}>
                        <IconEye size={14} />
                      </ActionIcon>
                      <ActionIcon size="sm" variant="subtle">
                        <IconEdit size={14} />
                      </ActionIcon>
                      <ActionIcon size="sm" variant="subtle">
                        <IconPrinter size={14} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Group justify="center" mt="xl">
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
          />
        </Group>
      )}

      {/* Upload Modal */}
      <Modal opened={uploadOpened} onClose={closeUpload} title="Ajouter un document financier" size="lg">
        <form onSubmit={form.onSubmit(handleImageUpload)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Type de document"
                  placeholder="Sélectionner le type"
                  data={documentTypes}
                  {...form.getInputProps('documentType')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Numéro de document"
                  placeholder="Ex: FAC-2024-001"
                  {...form.getInputProps('documentNumber')}
                  required
                />
              </Grid.Col>
            </Grid>

            <FileInput
              label="Image du document"
              placeholder="Sélectionner une image"
              accept="image/*"
              leftSection={<IconUpload size={16} />}
              {...form.getInputProps('file')}
              required
            />

            <TextInput
              label="Nom du fournisseur"
              placeholder="Ex: Laboratoire Pharma Plus"
              {...form.getInputProps('supplierName')}
              required
            />

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Montant (EUR)"
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                  {...form.getInputProps('amount')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Mode de paiement"
                  placeholder="Sélectionner le mode"
                  data={paymentMethods}
                  {...form.getInputProps('paymentMethod')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Catégorie"
                  placeholder="Sélectionner une catégorie"
                  data={categories}
                  {...form.getInputProps('category')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Référence"
                  placeholder="Ex: REF-2024-001"
                  {...form.getInputProps('reference')}
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label="Compte bancaire"
              placeholder="Ex: FR76 1234 5678 9012 3456 789"
              {...form.getInputProps('bankAccount')}
            />

            <Textarea
              label="Description"
              placeholder="Description du document"
              rows={3}
              {...form.getInputProps('description')}
            />

            <TextInput
              label="Tags"
              placeholder="Tags séparés par des virgules"
              {...form.getInputProps('tags')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" onClick={closeUpload}>
                Annuler
              </Button>
              <Button type="submit" leftSection={<IconUpload size={16} />}>
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Document Viewer Modal */}
      <Modal
        opened={viewerOpened}
        onClose={closeViewer}
        title={selectedImage?.documentNumber}
        size="xl"
        centered
      >
        {selectedImage && (
          <Stack>
            <Image
              src={selectedImage.imageUrl}
              alt={selectedImage.documentNumber}
              fit="contain"
              h={400}
            />

            <Grid>
              <Grid.Col span={8}>
                <Stack gap="xs">
                  <Group>
                    <Text fw={500} size="lg">{selectedImage.documentNumber}</Text>
                    <Badge color="blue">{selectedImage.documentType}</Badge>
                    <Badge
                      color={getStatusColor(selectedImage.status)}
                      leftSection={getStatusIcon(selectedImage.status)}
                    >
                      {statusOptions.find(s => s.value === selectedImage.status)?.label}
                    </Badge>
                  </Group>

                  <Text size="sm" c="dimmed">{selectedImage.description}</Text>

                  <Group gap="xs">
                    {selectedImage.tags.map((tag, index) => (
                      <Badge key={index} size="sm" variant="light">
                        {tag}
                      </Badge>
                    ))}
                  </Group>

                  <Divider />

                  <Group>
                    <Text size="sm" fw={500}>Montant:</Text>
                    <Text size="lg" fw={600} c="blue">
                      {formatCurrency(selectedImage.amount, selectedImage.currency)}
                    </Text>
                  </Group>

                  <Group>
                    <Text size="sm">Fournisseur: {selectedImage.supplierName}</Text>
                  </Group>

                  <Group>
                    <Text size="sm">Mode de paiement: {selectedImage.paymentMethod}</Text>
                  </Group>

                  <Group>
                    <Text size="sm">Référence: {selectedImage.reference}</Text>
                  </Group>
                </Stack>
              </Grid.Col>

              <Grid.Col span={4}>
                <Stack gap="xs">
                  <Group>
                    <IconReceipt size={16} />
                    <Text size="sm">{selectedImage.category}</Text>
                  </Group>

                  <Group>
                    <IconCalendar size={16} />
                    <Text size="sm">Payé le: {selectedImage.paymentDate.toLocaleDateString()}</Text>
                  </Group>

                  <Group>
                    <IconCalendar size={16} />
                    <Text size="sm">Échéance: {selectedImage.dueDate.toLocaleDateString()}</Text>
                  </Group>

                  <Group>
                    <IconUser size={16} />
                    <Text size="sm">{selectedImage.uploadedBy}</Text>
                  </Group>

                  <Group>
                    <IconBuildingBank size={16} />
                    <Text size="xs" c="dimmed">{selectedImage.bankAccount}</Text>
                  </Group>
                </Stack>
              </Grid.Col>
            </Grid>

            <Group justify="space-between" mt="md">
              <Group>
                <ActionIcon variant="outline">
                  <IconShare size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconDownload size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconPrinter size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconMail size={16} />
                </ActionIcon>
              </Group>

              <Group>
                <Button variant="outline" leftSection={<IconEdit size={16} />}>
                  Modifier
                </Button>
                <Button variant="outline" color="red" leftSection={<IconTrash size={16} />}>
                  Supprimer
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Filters Modal */}
      <Modal opened={opened} onClose={close} title="Filtres avancés">
        <Stack>
          <Select
            label="Catégorie"
            placeholder="Toutes les catégories"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />

          <Select
            label="Statut du paiement"
            placeholder="Tous les statuts"
            data={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />

          <Select
            label="Mode de paiement"
            placeholder="Tous les modes"
            data={paymentMethods}
            value={selectedPaymentMethod}
            onChange={setSelectedPaymentMethod}
            clearable
          />

          <DatePickerInput
            label="Date de paiement"
            placeholder="Sélectionner une date"
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={close}>
              Fermer
            </Button>
            <Button onClick={close}>
              Appliquer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Paper>
  );
}
