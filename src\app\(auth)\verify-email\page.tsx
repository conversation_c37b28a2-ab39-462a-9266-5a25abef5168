'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
//import Image from 'next/legacy/image'
import {
  Paper,
  Title,
  Container,
  Button,
  Text,
  Alert,
  Stack,
  Center,
  Loader,
  Box,
 
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconCheck } from '@tabler/icons-react';
import authService from '~/services/authService';
import Languages from "~/layout/navBarButton/Iconbar/Languagespage";
import { useTranslation } from 'react-i18next';

import dynamic from "next/dynamic";
const SwitchColorMode = dynamic(() => import("~/components/SwitchColorMode"), { ssr: false });
export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState('');

  const token = searchParams ? searchParams.get('token') : null;
  const email = searchParams ? searchParams.get('email') : null;
  const { t, } = useTranslation('auth');
  useEffect(() => {
    const verifyEmail = async () => {
      if (!token || !email) {
        setError('Missing verification token or email');
        setLoading(false);
        return;
      }

      try {
        await authService.verifyEmail(token, email);
        setVerified(true);

        notifications.show({
          title: 'Email Verified',
          message: 'Your email has been verified successfully. You can now log in.',
          color: 'green',
        });

        // Redirect to login page after a short delay
        setTimeout(() => {
          router.push('/login?verified=true');
        }, 3000);
      } catch (error) {
        console.error('Email verification error:', error);
        setError('Failed to verify email. The verification link may be invalid or expired.');

        notifications.show({
          title: 'Verification Failed',
          message: 'Failed to verify your email. Please try again or contact support.',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    };

    verifyEmail();
  }, [token, email, router]);

  if (loading) {
    return (
      <Container size="sm">
           <Center mb={10}>
          <Box >
            <Center >
          {/* <Image
              src="/logo.svg"
               alt="Doctor Portal logo"
              width={96}
              height={96}
            /> */}
            </Center>
            <Title order={1} ta="center" style={{ color: '#1c7ed6' }}>
              Doctor Portal
            </Title>
            <Text ta="center" c="dimmed">
              Manage your practice efficiently
            </Text> 
          </Box>
        </Center>
        <Center style={{ height: '60vh' }}>
          <Stack align="center" gap="md">
            <Loader size="lg" />
            <Text>Verifying your email...</Text>
          </Stack>
        </Center>
      </Container>
    );
  }

  return (
    <>
    <>
    <title>{t('page-title')} | Doctor Portal </title>
    <meta
      name="description"
      content={t('description')}
    />
    </>
    <Container size="sm">
    <Stack>
          {/* <Title ta="center">{t('Welcome')}</Title> */}
          <Center mb={10}>
          <Box >
            <Center >
          {/* <Image
              src="/logo.svg"
               alt="Doctor Portal logo"
              width={96}
              height={96}
            /> */}
            </Center>
            <Title order={1} ta="center" style={{ color: '#1c7ed6' }}>
              Doctor Portal
            </Title>
            <Text ta="center" c="dimmed">
              Manage your practice efficiently
            </Text> 
          </Box>
        </Center>
      <Paper radius="md" p="xl" withBorder style={{
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
        <Title order={2} ta="center" mt="md" mb="md">
          Email Verification
        </Title>

        {verified ? (
          <Stack>
            <Alert icon={<IconCheck size={16} />} title="Email Verified" color="green">
              Your email has been verified successfully. You will be redirected to the login page.
            </Alert>

            <Text c="dimmed" size="sm" ta="center">
              If you are not redirected automatically, please click the button below.
            </Text>

            <Button component={Link} href="/login?verified=true" fullWidth mt="md">
              Go to Login
            </Button>
          </Stack>
        ) : (
          <Stack>
            <Alert icon={<IconAlertCircle size={16} />} title="Verification Failed" color="red">
              {error || 'Failed to verify your email. The verification link may be invalid or expired.'}
            </Alert>

            <Text c="dimmed" size="sm" ta="center">
              Please try again or contact support if the problem persists.
            </Text>

            <Button component={Link} href="/login" fullWidth mt="md">
              Go to Login
            </Button>
          </Stack>
        )}
      </Paper>
      </Stack> 
        {/* <div className="fixed right-10 top-[calc(50%-12px)] "> */}
        {/* <div className='absolute md:right-10 md:top-[calc(50%-0px)] top-[calc(6%-0px)] right-6 '> */}
        <div className='absolute md:right-10 md:top-[calc(50%-0px)] top-[calc(6%-0px)] right-6 '>
        <SwitchColorMode />
        <Languages />
       </div>
    </Container>
    </>
  );
}
