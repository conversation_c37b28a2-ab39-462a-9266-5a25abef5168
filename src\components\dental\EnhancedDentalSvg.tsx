"use client";
import React, { useState, useCallback } from 'react';
import { Checkbox, Group, Stack, Text, Divider } from '@mantine/core';
import { DentalSvg } from '@/components/TDentalSvgMin';
import { ColorTarget } from '@/types/dental';
import { Dantal } from '@/data/Tdantal';

export interface ToothSelection {
  toothNumber: number;
  isSelected: boolean;
  paths: PathSelection[];
}

export interface PathSelection {
  pathId: string;
  isSelected: boolean;
  color?: {
    fill?: string;
    stroke?: string;
  };
  treatment?: string;
}

interface EnhancedDentalSvgProps {
  patientId: string;
  specialty: 'therapeutic' | 'esthetic' | 'prosthodontic' | 'surgery' | 'orthodontic';
  onToothSelectionChange?: (selections: ToothSelection[]) => void;
  onPathSelectionChange?: (toothNumber: number, pathId: string, isSelected: boolean) => void;
  onTreatmentApply?: (toothNumber: number, pathIds: string[], treatment: string, color: string) => void;
  activeButton?: string;
  currentColor?: string;
  currentColorTarget?: ColorTarget;
}

export const EnhancedDentalSvg: React.FC<EnhancedDentalSvgProps> = ({
  patientId: _patientId, // eslint-disable-line @typescript-eslint/no-unused-vars
  specialty: _specialty, // eslint-disable-line @typescript-eslint/no-unused-vars
  onToothSelectionChange,
  onPathSelectionChange,
  onTreatmentApply,
  activeButton,
  currentColor,
  currentColorTarget: _currentColorTarget // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  // État pour les sélections de dents (32 dents)
  const [toothSelections, setToothSelections] = useState<ToothSelection[]>(() =>
    Array.from({ length: 32 }, (_, i) => ({
      toothNumber: i + 1,
      isSelected: false,
      paths: Array.from({ length: 20 }, (_, j) => ({
        pathId: `${j + 1}`,
        isSelected: false
      }))
    }))
  );

  // Gestionnaire pour la sélection globale d'une dent
  const handleToothSelection = useCallback((toothNumber: number, isSelected: boolean) => {
    setToothSelections(prev => {
      const updated = prev.map(tooth =>
        tooth.toothNumber === toothNumber
          ? {
              ...tooth,
              isSelected,
              // Si on désélectionne la dent, désélectionner tous ses paths
              paths: isSelected ? tooth.paths : tooth.paths.map(p => ({ ...p, isSelected: false }))
            }
          : tooth
      );

      onToothSelectionChange?.(updated);
      return updated;
    });
  }, [onToothSelectionChange]);

  // Gestionnaire pour la sélection d'un path spécifique
  const handlePathSelection = useCallback((toothNumber: number, pathId: string, isSelected: boolean) => {
    setToothSelections(prev => {
      const updated = prev.map(tooth =>
        tooth.toothNumber === toothNumber
          ? {
              ...tooth,
              paths: tooth.paths.map(path =>
                path.pathId === pathId
                  ? { ...path, isSelected }
                  : path
              )
            }
          : tooth
      );

      onPathSelectionChange?.(toothNumber, pathId, isSelected);
      return updated;
    });
  }, [onPathSelectionChange]);

  // Gestionnaire pour l'application d'un traitement
  const handleTreatmentApplication = useCallback(() => {
    if (!activeButton || !currentColor) return;

    toothSelections.forEach(tooth => {
      if (tooth.isSelected) {
        const selectedPaths = tooth.paths
          .filter(path => path.isSelected)
          .map(path => path.pathId);

        if (selectedPaths.length > 0) {
          onTreatmentApply?.(tooth.toothNumber, selectedPaths, activeButton, currentColor);
        }
      }
    });
  }, [toothSelections, activeButton, currentColor, onTreatmentApply]);

  // Gestionnaire pour la sélection/désélection de tous les paths d'une dent
  const handleSelectAllPaths = useCallback((toothNumber: number, isSelected: boolean) => {
    setToothSelections(prev =>
      prev.map(tooth =>
        tooth.toothNumber === toothNumber
          ? {
              ...tooth,
              paths: tooth.paths.map(path => ({ ...path, isSelected }))
            }
          : tooth
      )
    );
  }, []);

  return (
    <div className="enhanced-dental-svg">
      {/* Panneau de contrôle des sélections */}
      <div className="selection-controls mb-4 p-4 border rounded">
        <Text size="sm" fw={600} mb="md">Sélection des dents et parties</Text>

        {/* Grille des dents avec cases à cocher */}
        <div className="teeth-grid grid grid-cols-8 gap-2 mb-4">
          {toothSelections.map(tooth => (
            <div key={tooth.toothNumber} className="tooth-control border rounded p-2">
              <Checkbox
                label={`Dent ${tooth.toothNumber}`}
                checked={tooth.isSelected}
                onChange={(event) => handleToothSelection(tooth.toothNumber, event.currentTarget.checked)}
                size="xs"
              />

              {/* Sous-sélections des paths si la dent est sélectionnée */}
              {tooth.isSelected && (
                <Stack gap="xs" mt="xs">
                  <Group gap="xs">
                    <Text size="xs">Parties:</Text>
                    <Checkbox
                      label="Tout"
                      size="xs"
                      checked={tooth.paths.every(p => p.isSelected)}
                      indeterminate={tooth.paths.some(p => p.isSelected) && !tooth.paths.every(p => p.isSelected)}
                      onChange={(event) => handleSelectAllPaths(tooth.toothNumber, event.currentTarget.checked)}
                    />
                  </Group>

                  <div className="paths-grid grid grid-cols-4 gap-1">
                    {tooth.paths.slice(0, 12).map(path => (
                      <Checkbox
                        key={path.pathId}
                        label={path.pathId}
                        size="xs"
                        checked={path.isSelected}
                        onChange={(event) => handlePathSelection(tooth.toothNumber, path.pathId, event.currentTarget.checked)}
                      />
                    ))}
                  </div>
                </Stack>
              )}
            </div>
          ))}
        </div>

        <Divider my="md" />

        {/* Bouton d'application du traitement */}
        {activeButton && currentColor && (
          <Group gap="md">
            <Text size="sm">
              Traitement actif: <strong>{activeButton}</strong>
              <span className="ml-2 inline-block w-4 h-4 rounded" style={{ backgroundColor: currentColor }}></span>
            </Text>
            <button
              onClick={handleTreatmentApplication}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              disabled={!toothSelections.some(t => t.isSelected && t.paths.some(p => p.isSelected))}
            >
              Appliquer le traitement
            </button>
          </Group>
        )}
      </div>

      {/* SVG dentaire existant */}
      <div className="dental-svg-container">
        {Dantal.map((svgData) => (
          <DentalSvg
            key={svgData.svg_id}
            svgData={svgData}
            isHidingMode={false}
            highlightedPaths={{}}
            isPathSelectionActive={true}
            hiddenPaths={{}}
            isBrokenRedStrokeActive={false}
            isGradientEffectActive={false}
            isGradientBottomEffectActive={false}
            onPathClick={(pathId: string, svgId: string) => {
              // Logique existante + mise à jour des sélections
              const toothNumber = parseInt(svgId);
              handlePathSelection(toothNumber, pathId, true);
            }}
          />
        ))}
      </div>
    </div>
  );
};
