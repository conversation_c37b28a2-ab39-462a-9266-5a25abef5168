'use client';
import { EncasementHeader } from '@/components/EncasementHeader';
import { OrganizationForm } from '@/components/OrganizationForm';
// import { PayeePayerSection } from '@/components/PayeePayerSection';
import { Group,  } from '@mantine/core';
import React, { useState } from 'react';
import {

  Box,
  Card,
Modal,
  Button,
  Select,
  TextInput,
  Table,
  Text,
  Radio,
  Textarea,
  ActionIcon,
  Indicator,
  NumberInput,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconPlus,
  IconMinus,
  IconMicrophone,
  IconPaperclip,
  IconX,
  IconMenu2,
  IconPrinter,
 
  IconDeviceFloppy,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import { mdiContentSaveSettings ,mdiPlus,mdiRefresh,} from '@mdi/js';

export const Nouvel_encaissement = () => {
   const readOnly = false;
     const [beneficiaire, setBeneficiaire] = useState('Patient');
     const [tiersPayant, setTiersPayant] = useState('');
     const [choisirPatient, setChoisirPatient] = useState('ABADI SOUAD');
        const [isOrganismeModalOpen, setIsOrganismeModalOpen] = useState(false);
    
     const [payeur, setPayeur] = useState('Patient');
     const [tiersPayantPayeur, setTiersPayantPayeur] = useState('');
     const [autre, setAutre] = useState('');
     const [datePaiement, setDatePaiement] = useState<Date | null>(new Date('2022-09-16'));
     const [montantEncaisse, setMontantEncaisse] = useState(0);
     const [docteur, setDocteur] = useState('MEDECIN YOUSSEF');
     const [mode, setMode] = useState('Espèce');
     const [banque, setBanque] = useState('');
     const [dateEcheance, setDateEcheance] = useState<Date | null>(new Date('2022-09-16'));
     const [ref, setRef] = useState('');
     const [commentaire, setCommentaire] = useState('');
   
     // États pour la pagination
     const [currentPage, setCurrentPage] = useState(1);
     const [itemsPerPage, setItemsPerPage] = useState(5); 
  return (
       <>
      <EncasementHeader readOnly={readOnly} />
      <Box className="w-full h-full bg-gray-50">
             {/* Contenu principal */}
             <div className="flex h-[600px]">
               {/* Section gauche */}
               <Card
                 shadow="none"
                 padding="sm"
                 radius={0}
                 className="w-160 bg-white border-r border-gray-200"
               >
                 <div className="space-y-3">
                   {/* Bénéficiaire */}
                     <Text size="xs" fw={500} className="text-gray-700 mb-2">
                       Bénéficiaire
                     </Text>
                  
                 {/* ---------------------------------- */}
                 <Radio.Group
                withAsterisk
                value={beneficiaire}
                onChange={setBeneficiaire}
              >
                <Group mt="xs">
                  <Radio value="Patient" label="Patient" />
                  <Radio value="Tiers payant" label="Tiers payant" />
                 
                 <Select
                    placeholder="Choisir un patient"
                    className={beneficiaire === 'Tiers payant' ? 'hidden' : ''}
                    data={[
                         'ABADI SOUAD',
                         'BENALI MOHAMED',
                         'CHAKIR FATIMA',
                         'DOUIRI HASSAN',
                         'EL AMRANI AICHA',
                         'FASSI YOUSSEF',
                         'GHARBI NADIA',
                         'HAMIDI OMAR',
                         'IDRISSI KHADIJA',
                         'JAMAL RACHID'
                       ]}
                  />
                    <Group  >
                  <Select
                    placeholder="Organisme"
                   
                    data={[
                         'ATLANTA',
                         'ATLANTA/SANAD',
                         'AXA',
                         'AXA ASSURANCE MAROC',
                         'Allianz',
                         'Aucune',
                         'Autre',
                         'BANK AL MAGHREB',
                         'BP',
                         'CMIM',
                         'CNOPS',
                         'CNSS',
                         'CNCF',
                         'CNIA SAADA',
                         'Es Saada',
                         'FAR',
                         'LYDEC',
                         'MAMDA',
                         'MAROCAINE VIE',
                         'MAMT',
                         'MCMA',
                         'MNE',
                         'MUFRAS',
                         'MUTUELLE',
                         'NM',
                         'OCP',
                         'ONE',
                         'RAM',
                         'RMA WATANYA',
                         'SAHAM ASSURANCE',
                         'SANAD',
                         'WAFA ASSURANCE',
                         'ZURICH'
                       ]}
                       className={beneficiaire !== 'Tiers payant' ? 'hidden' : ''}
                  />
                   <div  className={beneficiaire !== 'Tiers payant' ? 'hidden' : ''}>
                              <ActionIcon  size="input-sm" variant="default" 
                              aria-label="ActionIcon the same size as inputs"
                              mr={8}
                              onClick={() => setIsOrganismeModalOpen(true)}
                              >
                              <Icon path={mdiPlus} size={1} />
                              </ActionIcon>
                              <ActionIcon  size="input-sm" variant="default" 
                              aria-label="ActionIcon the same size as inputs"
                              //onClick={() => ()}
                              >
                              <Icon path={ mdiRefresh} size={1} />
                              </ActionIcon>
                                  </div>
                                   </Group>
                                
                                  
                                 
                </Group>
              </Radio.Group>
                 {/* ---------------------------------- */}
               
                   {/* Payeur */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-2">
                       Payeur
                     </Text>
                     <Radio.Group
                       value={payeur}
                       onChange={setPayeur}
                       size="xs"
                     >
                       <div className="space-y-1">
                         <Radio value="Patient" label="Patient" />
                         <div className="flex items-center gap-2">
                           <Radio value="Tiers payant" label="Tiers payant" />
                           <TextInput
                             value={tiersPayantPayeur}
                             onChange={(e) => setTiersPayantPayeur(e.target.value)}
                             size="xs"
                             className="flex-1"
                             disabled={payeur !== 'Tiers payant'}
                           />
                         </div>
                         <div className="flex items-center gap-2">
                           <Radio value="Autre" label="Autre" />
                           <TextInput
                             value={autre}
                             onChange={(e) => setAutre(e.target.value)}
                             size="xs"
                             className="flex-1"
                             disabled={payeur !== 'Autre'}
                           />
                         </div>
                       </div>
                     </Radio.Group>
                   </div>
                 </div>
               </Card>
     
               {/* Section droite */}
               <Card
                 shadow="none"
                 padding="sm"
                 radius={0}
                 className="w-80 bg-white border-r border-gray-200"
               >
                 <div className="space-y-3">
                   {/* Date de paiement */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Date de paiement *
                     </Text>
                     <DatePickerInput
                       value={datePaiement}
                       onChange={setDatePaiement}
                       size="xs"
                       className="w-full"
                       placeholder="16/09/2022"
                     />
                   </div>
     
                   {/* Montant encaissé */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Montant encaissé *
                     </Text>
                     <NumberInput
                       value={montantEncaisse}
                       onChange={(value) => setMontantEncaisse(Number(value) || 0)}
                       size="xs"
                       className="w-full"
                       placeholder="0,00"
                       decimalScale={2}
                       fixedDecimalScale
                     />
                   </div>
     
                   {/* Docteur */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Docteur *
                     </Text>
                     <Group gap="xs">
                       <Select
                         value={docteur}
                         onChange={(value) => setDocteur(value || 'MEDECIN YOUSSEF')}
                         size="xs"
                         className="flex-1"
                         data={['MEDECIN YOUSSEF', 'Dr. SMITH', 'Dr. MARTIN']}
                       />
                       <ActionIcon
                         variant="light"
                         color="red"
                         size="sm"
                       >
                         <IconMinus size={14} />
                       </ActionIcon>
                     </Group>
                   </div>
     
                   {/* Mode */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Mode
                     </Text>
                     <Group gap="xs">
                       <Select
                         value={mode}
                         onChange={(value) => setMode(value || 'Espèce')}
                         size="xs"
                         className="flex-1"
                         data={['Espèce', 'Carte', 'Chèque', 'Virement']}
                       />
                       <ActionIcon
                         variant="light"
                         color="blue"
                         size="sm"
                       >
                         <IconPlus size={14} />
                       </ActionIcon>
                       <ActionIcon
                         variant="light"
                         color="red"
                         size="sm"
                       >
                         <IconX size={14} />
                       </ActionIcon>
                     </Group>
                   </div>
     
                   {/* Banque */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Banque
                     </Text>
                     <TextInput
                       value={banque}
                       onChange={(e) => setBanque(e.target.value)}
                       size="xs"
                       className="w-full"
                     />
                   </div>
     
                   {/* Date d'échéance */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Date d&apos;échéance
                     </Text>
                     <Group gap="xs">
                       <DatePickerInput
                         value={dateEcheance}
                         onChange={setDateEcheance}
                         size="xs"
                         className="flex-1"
                         placeholder="16/09/2022"
                       />
                       <ActionIcon
                         variant="light"
                         color="blue"
                         size="sm"
                       >
                         <IconPlus size={14} />
                       </ActionIcon>
                       <ActionIcon
                         variant="light"
                         color="red"
                         size="sm"
                       >
                         <IconX size={14} />
                       </ActionIcon>
                     </Group>
                   </div>
     
                   {/* Réf. */}
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Réf.
                     </Text>
                     <TextInput
                       value={ref}
                       onChange={(e) => setRef(e.target.value)}
                       size="xs"
                       className="w-full"
                     />
                   </div>
                 </div>
               </Card>
     
               {/* Zone principale du contenu */}
               <div className="flex-1 bg-white flex flex-col">
                 {/* Section commentaire */}
                 <Card
                   shadow="none"
                   padding="sm"
                   radius={0}
                   className="bg-white border-b border-gray-200"
                 >
                   <div>
                     <Text size="xs" fw={500} className="text-gray-700 mb-1">
                       Commentaire
                     </Text>
                     <Group gap="xs" align="end">
                       <Textarea
                         value={commentaire}
                         onChange={(e) => setCommentaire(e.target.value)}
                         size="xs"
                         className="flex-1"
                         rows={2}
                       />
                       <ActionIcon
                         variant="light"
                         color="blue"
                         size="sm"
                       >
                         <IconMicrophone size={14} />
                       </ActionIcon>
                       <ActionIcon
                         variant="light"
                         color="blue"
                         size="sm"
                       >
                         <IconPaperclip size={14} />
                       </ActionIcon>
                     </Group>
                   </div>
     
                   {/* Pièces jointes */}
                   <div className="mt-2">
                     <Group gap="xs" align="center">
                       <ActionIcon
                         variant="light"
                         color="blue"
                         size="sm"
                       >
                         <IconPaperclip size={14} />
                       </ActionIcon>
                       <Text size="xs" className="text-gray-600">
                         Pièces jointes
                       </Text>
                       <div className="bg-yellow-100 px-2 py-1 rounded text-xs text-gray-700">
                         Aucun fichier trouvé
                       </div>
                     </Group>
                   </div>
                 </Card>
     
                 {/* Tableau */}
                 <div className="flex-1 overflow-auto">
                   <Table
                     striped={false}
                     highlightOnHover={true}
                     withTableBorder={true}
                     withColumnBorders={true}
                     className="h-full"
                   >
                     <Table.Thead className="bg-gray-50 sticky top-0">
                       <Table.Tr>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           <input type="checkbox" className="w-3 h-3" />
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Date
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Type
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Bénéficiaire
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Montant dû
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Montant encaissé
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Avancement
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           État
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Reste à régler
                         </Table.Th>
                         <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-xs">
                           Remise
                         </Table.Th>
                         <Table.Th className="bg-gray-100 text-gray-700 font-medium text-xs">
                           Montant encaissé
                         </Table.Th>
                       </Table.Tr>
                     </Table.Thead>
                     <Table.Tbody>
                       <Table.Tr>
                         <Table.Td colSpan={11} className="text-center py-8">
                           <Text size="xs" className="text-gray-500">
                             Aucun élément trouvé
                           </Text>
                         </Table.Td>
                       </Table.Tr>
                     </Table.Tbody>
                   </Table>
                 </div>
               </div>
             </div>
     
             {/* Footer avec boutons de sélection et légende */}
             <Card
               shadow="none"
               padding="sm"
               radius={0}
               className="bg-white border-t border-gray-200"
             >
               <Group justify="space-between" align="center">
                 <Group gap="sm" align="center">
                   <Button
                     variant="outline"
                     color="gray"
                     size="xs"
                     leftSection={<IconMenu2 size={14} />}
                   >
                     Tout sélectionner
                   </Button>
                   <Button
                     variant="outline"
                     color="gray"
                     size="xs"
                     leftSection={<IconMenu2 size={14} />}
                   >
                     Tout désélectionner
                   </Button>
     
                   {/* Légende des statuts */}
                   <Group gap="sm" align="center" className="ml-4">
                     <Group gap="xs" align="center">
                       <Indicator color="green" size={8} />
                       <Text size="xs" className="text-gray-600">Réglé(e)</Text>
                     </Group>
                     <Group gap="xs" align="center">
                       <Indicator color="orange" size={8} />
                       <Text size="xs" className="text-gray-600">Réglé(e) Partiellement</Text>
                     </Group>
                     <Group gap="xs" align="center">
                       <Indicator color="red" size={8} />
                       <Text size="xs" className="text-gray-600">Non Réglé(e)</Text>
                     </Group>
                     <Group gap="xs" align="center">
                       <Indicator color="gray" size={8} />
                       <Text size="xs" className="text-gray-600">Dispensé(e)/Clôturé(e)</Text>
                     </Group>
                   </Group>
                 </Group>
     
                 <Group gap="sm" align="center">
                   <Text size="xs" className="text-gray-600">Page</Text>
                   <Select
                     value={currentPage.toString()}
                     onChange={(value) => setCurrentPage(Number(value) || 1)}
                     data={['1']}
                     size="xs"
                     className="w-12"
                   />
                   <Text size="xs" className="text-gray-600">Lignes par Page</Text>
                   <Select
                     value={itemsPerPage.toString()}
                     onChange={(value) => setItemsPerPage(Number(value) || 5)}
                     data={['5', '10', '25']}
                     size="xs"
                     className="w-12"
                   />
                   <Text size="xs" className="text-gray-600">0 - 0 de 0</Text>
                 </Group>
               </Group>
             </Card>
     
             {/* Boutons d'action */}
             <Card
               shadow="none"
               padding="md"
               radius={0}
               className="bg-gray-50 border-t border-gray-200"
             >
               <Group justify="space-between" align="center">
                 <Group gap="xs">
                   <ActionIcon
                     variant="subtle"
                     color="gray"
                     size="sm"
                   >
                     <IconPrinter size={16} />
                   </ActionIcon>
                   <ActionIcon
                     variant="subtle"
                     color="gray"
                     size="sm"
                   >
                     <Icon path={mdiContentSaveSettings} size={1} />
                   </ActionIcon>
                   <ActionIcon
                     variant="subtle"
                     color="gray"
                     size="sm"
                   >
                     <IconDeviceFloppy size={16} />
                   </ActionIcon>
                   <ActionIcon
                     variant="subtle"
                     color="gray"
                     size="sm"
                   >
                     <IconMenu2 size={16} />
                   </ActionIcon>
                 </Group>
     
                 <Group gap="sm">
                   <Button
                     variant="outline"
                     color="gray"
                     size="sm"
                   >
                     Désactiver
                   </Button>
                   <Button
                     variant="outline"
                     color="blue"
                     size="sm"
                   >
                     Invalider top-liste
                   </Button>
                   <Button
                     color="red"
                     size="sm"
                   >
                     Annuler
                   </Button>
                   <Button
                     variant="outline"
                     color="gray"
                     size="sm"
                   >
                     Enregistrer et quitter
                   </Button>
                   <Button
                     color="blue"
                     size="sm"
                   >
                     Enregistrer
                   </Button>
                 </Group>
               </Group>
             </Card>
           </Box>

                 <Modal opened={isOrganismeModalOpen}
                   onClose={() => setIsOrganismeModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered
                   >
               <OrganizationForm
                  onCancel={() => setIsOrganismeModalOpen(false)}
                  onSubmit={(data) => console.log('Données soumises:', data)}
                />
                 </Modal>
                
    </>
  )
}
