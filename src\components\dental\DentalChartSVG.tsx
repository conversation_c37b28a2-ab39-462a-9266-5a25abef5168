'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>rid, Card, Stack, Title, Group, Badge, Button, Text, Tabs } from '@mantine/core';
import { IconMedicalCross, IconSparkles, IconTool, IconScalpel, IconAdjustments } from '@tabler/icons-react';
import ToothSVGAnatomy from './ToothSVGAnatomy';

interface ToothModification {
  id: string;
  toothNumber: number;
  partId: string;
  wallId?: string;
  modificationType: string;
  statusCode: string;
  isApplied: boolean;
  color?: string;
  material?: string;
  notes?: string;
  timestamp: string;
}

interface DentalChartProps {
  patientId?: string;
  onModificationsChange?: (modifications: ToothModification[]) => void;
}

const DentalChartSVG: React.FC<DentalChartProps> = ({
  patientId,
  onModificationsChange,
}) => {
  const [allModifications, setAllModifications] = useState<ToothModification[]>([]);
  const [activeSpecialty, setActiveSpecialty] = useState<string>('esthetic');
  const [selectedTooth, setSelectedTooth] = useState<number | null>(null);

  // Configuration des 32 dents
  const upperTeeth = Array.from({ length: 16 }, (_, i) => i + 1);  // 1-16
  const lowerTeeth = Array.from({ length: 16 }, (_, i) => i + 17); // 17-32

  // Spécialités dentaires
  const specialties = [
    {
      id: 'esthetic',
      name: 'Dentisterie Esthétique',
      icon: IconSparkles,
      color: '#E91E63',
      modifications: ['color_change', 'whitening', 'veneer', 'composite']
    },
    {
      id: 'prosthetic',
      name: 'Prothèses Thérapeutiques',
      icon: IconTool,
      color: '#2196F3',
      modifications: ['crown', 'bridge', 'implant', 'filling']
    },
    {
      id: 'surgery',
      name: 'Chirurgie',
      icon: IconScalpel,
      color: '#FF5722',
      modifications: ['extraction', 'surgery', 'bone_graft', 'cavity']
    },
    {
      id: 'orthodontics',
      name: 'Orthopédie',
      icon: IconAdjustments,
      color: '#4CAF50',
      modifications: ['braces', 'aligners', 'movement']
    },
  ];

  // Gérer les modifications d'une dent
  const handleToothModifications = (toothNumber: number, modifications: ToothModification[]) => {
    setAllModifications(prev => {
      // Supprimer les anciennes modifications de cette dent
      const filtered = prev.filter(mod => mod.toothNumber !== toothNumber);
      // Ajouter les nouvelles modifications
      const updated = [...filtered, ...modifications];
      
      // Notifier le parent
      if (onModificationsChange) {
        onModificationsChange(updated);
      }
      
      return updated;
    });
  };

  // Obtenir les modifications d'une dent
  const getToothModifications = (toothNumber: number): ToothModification[] => {
    return allModifications.filter(mod => mod.toothNumber === toothNumber);
  };

  // Obtenir le nombre de modifications par spécialité pour une dent
  const getModificationCountBySpecialty = (toothNumber: number, specialty: string): number => {
    const toothMods = getToothModifications(toothNumber);
    const specialtyMods = specialties.find(s => s.id === specialty)?.modifications || [];
    
    return toothMods.filter(mod => 
      specialtyMods.includes(mod.modificationType) && mod.isApplied
    ).length;
  };

  // Déterminer la position et le quadrant d'une dent
  const getToothPosition = (toothNumber: number) => {
    if (toothNumber <= 16) {
      return {
        position: 'upper' as const,
        quadrant: toothNumber <= 8 ? 'right' as const : 'left' as const
      };
    } else {
      return {
        position: 'lower' as const,
        quadrant: toothNumber <= 24 ? 'left' as const : 'right' as const
      };
    }
  };

  // Sauvegarder les modifications vers le backend
  const saveModificationsToBackend = async () => {
    try {
      // TODO: Implémenter l'appel API
      console.log('Saving modifications to backend:', allModifications);
      
      const response = await fetch('/api/tooth-modifications/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patient_id: patientId,
          modifications: allModifications
        })
      });
      
      if (response.ok) {
        console.log('Modifications saved successfully');
      }
    } catch (error) {
      console.error('Error saving modifications:', error);
    }
  };

  // Statistiques globales
  const getGlobalStats = () => {
    const totalModifications = allModifications.filter(mod => mod.isApplied).length;
    const modifiedTeeth = new Set(allModifications.filter(mod => mod.isApplied).map(mod => mod.toothNumber)).size;
    
    const specialtyStats = specialties.map(specialty => ({
      name: specialty.name,
      count: allModifications.filter(mod => 
        specialty.modifications.includes(mod.modificationType) && mod.isApplied
      ).length
    }));
    
    return { totalModifications, modifiedTeeth, specialtyStats };
  };

  const stats = getGlobalStats();

  return (
    <Stack gap="lg">
      {/* En-tête avec statistiques */}
      <Card withBorder p="md">
        <Group justify="space-between" mb="md">
          <div>
            <Title order={3}>
              <IconMedicalCross size={24} style={{ marginRight: 8 }} />
              Carte Dentaire Anatomique (32 Dents)
            </Title>
            <Text c="dimmed" size="sm">
              Gestion des modifications par spécialité avec identifiants de statut
            </Text>
          </div>
          <Group>
            <Badge variant="light" size="lg">
              {stats.modifiedTeeth}/32 dents modifiées
            </Badge>
            <Badge variant="filled" size="lg">
              {stats.totalModifications} modifications
            </Badge>
          </Group>
        </Group>

        {/* Statistiques par spécialité */}
        <Group>
          {stats.specialtyStats.map((stat, index) => (
            <Badge
              key={index}
              variant="light"
              color={specialties[index].color}
              size="sm"
            >
              {stat.name}: {stat.count}
            </Badge>
          ))}
        </Group>
      </Card>

      {/* Onglets par spécialité */}
      <Tabs value={activeSpecialty} onChange={setActiveSpecialty}>
        <Tabs.List>
          {specialties.map((specialty) => {
            const IconComponent = specialty.icon;
            return (
              <Tabs.Tab
                key={specialty.id}
                value={specialty.id}
                leftSection={<IconComponent size={16} />}
                color={specialty.color}
              >
                {specialty.name}
              </Tabs.Tab>
            );
          })}
        </Tabs.List>

        {specialties.map((specialty) => (
          <Tabs.Panel key={specialty.id} value={specialty.id} pt="md">
            <Stack gap="md">
              <Text size="sm" c="dimmed">
                Modifications disponibles: {specialty.modifications.join(', ')}
              </Text>

              {/* Dents Supérieures (1-16) */}
              <Card withBorder p="md">
                <Title order={5} mb="md" c="blue">
                  Dents Supérieures (1-16)
                </Title>
                <Grid>
                  {upperTeeth.map((toothNumber) => {
                    const { position, quadrant } = getToothPosition(toothNumber);
                    const modCount = getModificationCountBySpecialty(toothNumber, specialty.id);
                    
                    return (
                      <Grid.Col key={toothNumber} span={{ base: 3, sm: 2, md: 1.5 }}>
                        <div style={{ position: 'relative' }}>
                          <ToothSVGAnatomy
                            toothNumber={toothNumber}
                            position={position}
                            quadrant={quadrant}
                            onModificationChange={(mods) => handleToothModifications(toothNumber, mods)}
                          />
                          
                          {/* Indicateur de modifications pour cette spécialité */}
                          {modCount > 0 && (
                            <Badge
                              size="xs"
                              color={specialty.color}
                              style={{
                                position: 'absolute',
                                top: -5,
                                right: -5,
                                zIndex: 10,
                              }}
                            >
                              {modCount}
                            </Badge>
                          )}
                        </div>
                      </Grid.Col>
                    );
                  })}
                </Grid>
              </Card>

              {/* Dents Inférieures (17-32) */}
              <Card withBorder p="md">
                <Title order={5} mb="md" c="orange">
                  Dents Inférieures (17-32)
                </Title>
                <Grid>
                  {lowerTeeth.map((toothNumber) => {
                    const { position, quadrant } = getToothPosition(toothNumber);
                    const modCount = getModificationCountBySpecialty(toothNumber, specialty.id);
                    
                    return (
                      <Grid.Col key={toothNumber} span={{ base: 3, sm: 2, md: 1.5 }}>
                        <div style={{ position: 'relative' }}>
                          <ToothSVGAnatomy
                            toothNumber={toothNumber}
                            position={position}
                            quadrant={quadrant}
                            onModificationChange={(mods) => handleToothModifications(toothNumber, mods)}
                          />
                          
                          {/* Indicateur de modifications pour cette spécialité */}
                          {modCount > 0 && (
                            <Badge
                              size="xs"
                              color={specialty.color}
                              style={{
                                position: 'absolute',
                                top: -5,
                                right: -5,
                                zIndex: 10,
                              }}
                            >
                              {modCount}
                            </Badge>
                          )}
                        </div>
                      </Grid.Col>
                    );
                  })}
                </Grid>
              </Card>
            </Stack>
          </Tabs.Panel>
        ))}
      </Tabs>

      {/* Actions */}
      <Card withBorder p="md">
        <Group justify="space-between">
          <div>
            <Text fw={600}>Actions</Text>
            <Text size="sm" c="dimmed">
              Gérer les modifications et sauvegarder les changements
            </Text>
          </div>
          <Group>
            <Button
              variant="light"
              onClick={() => setAllModifications([])}
              disabled={allModifications.length === 0}
            >
              Réinitialiser
            </Button>
            <Button
              onClick={saveModificationsToBackend}
              disabled={allModifications.length === 0}
            >
              Sauvegarder ({allModifications.filter(m => m.isApplied).length} modifications)
            </Button>
          </Group>
        </Group>
      </Card>
    </Stack>
  );
};

export default DentalChartSVG;
