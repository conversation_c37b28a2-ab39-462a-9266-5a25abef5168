'use client';

import { useState, useEffect } from 'react';
import {
  Paper,
  Title,
  Text,
  TextInput,
  Button,
  Group,
  Stack,
  Alert,
  Divider,
  Code
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconArrowRight } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import LicenseError from './LicenseError';
import licenseService from '@/services/licenseService';

// Define types for better type safety (removed unused LicenseData interface)

interface ApiErrorResponse {
  response?: {
    status?: number;
    data?: {
      detail?: string;
      message?: string;
      license_number?: string | string[];
      activation_code?: string | string[];
      [key: string]: unknown;
    };
  };
  request?: unknown;
  message?: string;
}

// Type guard to check if error is an API error
function isApiError(error: unknown): error is ApiErrorResponse {
  return typeof error === 'object' && error !== null && 'response' in error;
}

// Type guard to check if error has request property
function hasRequest(error: unknown): error is { request: unknown } {
  return typeof error === 'object' && error !== null && 'request' in error;
}

interface LicenseActivationProps {
  onActivationSuccess?: () => void;
}

export default function LicenseActivation({
  onActivationSuccess
}: LicenseActivationProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [license, setLicense] = useState<unknown>(null);

  const form = useForm({
    initialValues: {
      license_number: '', // Never pre-fill the license number for security reasons
      activation_code: '',
      transaction_id: '',
    },
    validate: {
      license_number: (value) => (!value ? 'License number is required' : null),
      activation_code: (value) => (!value ? 'Activation code is required' : null),
    },
  });

  useEffect(() => {
    // Check if license was already activated in this session
    try {
      const alreadyActivated = sessionStorage.getItem('licenseActivated') === 'true';
      if (alreadyActivated) {
        console.info('License was already activated in this session');
        setSuccess(true);
        return;
      }
    } catch {
      // Ignore storage errors
    }

    // Fetch the current license to get the transaction ID
    const fetchLicense = async () => {
      try {
        setLoading(true);
        const licenseData = await licenseService.getLicense();
        setLicense(licenseData);

        // Only pre-fill the transaction ID field
        if (licenseData && typeof licenseData === 'object' && licenseData !== null && 'transaction_id' in licenseData) {
          const transactionId = (licenseData as { transaction_id: string }).transaction_id;
          form.setFieldValue('transaction_id', transactionId);
          console.log('Pre-filled transaction ID:', transactionId);
        }
      } catch (err: unknown) {
        console.error('Error fetching license:', err);
        // Don't show an error to the user, just log it
        // The user might not have a license yet
      } finally {
        setLoading(false);
      }
    };

    fetchLicense();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input format before sending to server
      const licenseNumber = values.license_number.trim();
      const activationCode = values.activation_code.trim();

      // Basic client-side validation
      if (!licenseNumber) {
        setError('Please enter a valid license number');
        return;
      }

      if (!activationCode) {
        setError('Please enter a valid activation code');
        return;
      }

      console.log('Submitting license activation:', {
        license_number: licenseNumber,
        activation_code: activationCode,
      });

      const response = await api.post('/api/auth/license/activate/', {
        license_number: licenseNumber,
        activation_code: activationCode,
      });

      console.log('License activation successful:', response.data);

      setSuccess(true);
      notifications.show({
        title: 'License Activated',
        message: 'Your license has been successfully activated.',
        color: 'green',
        icon: <IconCheck size="1.1rem" />,
      });

      // Set a flag to indicate successful activation
      // This will prevent multiple redirects if the component re-renders
      try {
        sessionStorage.setItem('licenseActivated', 'true');
      } catch {
        // Ignore storage errors
      }

      // Redirect to login page after successful activation
      console.info('License activated successfully. Redirecting to login page...');

      // Use a timeout to allow the success message to be seen
      setTimeout(() => {
        try {
          // Try using the Next.js router first
          router.push('/login');
        } catch {
          // Fallback to basic navigation if the router fails
          window.location.href = '/login';
        }
      }, 1500);

      if (onActivationSuccess) {
        onActivationSuccess();
      }
    } catch (err: unknown) {
      console.error('License activation error:', err);

      // Handle different types of errors with user-friendly messages
      if (isApiError(err) && err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);

        // Map error codes to user-friendly messages
        if (err.response.status === 400) {
          // Handle validation errors
          if (err.response.data && typeof err.response.data === 'object') {
            // Check for license number errors
            if (err.response.data.license_number) {
              if (typeof err.response.data.license_number === 'string') {
                setError(`The license number you entered is invalid: ${err.response.data.license_number}`);
              } else if (Array.isArray(err.response.data.license_number)) {
                setError(`The license number you entered is invalid: ${err.response.data.license_number[0]}`);
              } else {
                setError('The license number you entered is invalid. Please check and try again.');
              }
              return;
            }

            // Check for activation code errors
            if (err.response.data.activation_code) {
              if (typeof err.response.data.activation_code === 'string') {
                setError(`The activation code you entered is invalid: ${err.response.data.activation_code}`);
              } else if (Array.isArray(err.response.data.activation_code)) {
                setError(`The activation code you entered is invalid: ${err.response.data.activation_code[0]}`);
              } else {
                setError('The activation code you entered is invalid. Please check and try again.');
              }
              return;
            }

            // Check for detail message
            if (err.response.data.detail) {
              if (err.response.data.detail.includes('License number not found')) {
                setError('The license number you entered does not exist in our system. Please check and try again.');
              } else if (err.response.data.detail.includes('Invalid activation code')) {
                setError('The activation code you entered is incorrect. Please check and try again.');
              } else if (err.response.data.detail.includes('already')) {
                setError(err.response.data.detail);
              } else {
                setError(err.response.data.detail);
              }
              return;
            }

            // Generic validation error
            setError('There was a problem with the information you provided. Please check your license number and activation code.');
          } else {
            setError('Please check your license number and activation code and try again.');
          }
        } else if (err.response.status === 401) {
          setError('Your session has expired. Please log in again to activate your license.');
        } else if (err.response.status === 403) {
          setError('You do not have permission to activate this license. Please contact support.');
        } else if (err.response.status === 404) {
          setError('The license number you entered does not exist in our system. Please check and try again.');
        } else if (err.response.status && err.response.status >= 500) {
          setError('We\'re experiencing technical difficulties. Please try again later or contact support.');
        } else {
          setError('An error occurred while activating your license. Please try again or contact support.');
        }
      } else if (hasRequest(err)) {
        // The request was made but no response was received
        setError('Unable to connect to our servers. Please check your internet connection and try again.');
      } else {
        // Something happened in setting up the request
        setError('An unexpected error occurred. Please try again or contact support.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Paper p="xl" radius="md" withBorder>
        <Stack gap="md">
          <Alert
            icon={<IconCheck size="1.1rem" />}
            title="License Activated Successfully"
            color="green"
            variant="filled"
          >
            Your license has been successfully activated. You can now log in to access all features.
          </Alert>

          <Text size="sm" mt="md">
            Thank you for choosing our platform. Please log in to start your practice management journey!
          </Text>

          <Button
            component="a"
            href="/login"
            size="lg"
            fullWidth
            leftSection={<IconArrowRight size="1.1rem" />}
            color="brand"
            loading={true}
          >
            Redirecting to Login Page...
          </Button>

          <Text size="xs" c="dimmed" ta="center">
            You will be redirected to the login page automatically
          </Text>
        </Stack>
      </Paper>
    );
  }

  return (
    <Paper p="xl" radius="md" withBorder>
      <Title order={3} mb="md">Activate Your License</Title>

      <Text size="sm" mb="lg" c="dimmed">
        Enter your license number and activation code to activate your account.
        If you don&apos;t have an activation code, please contact support.
      </Text>

      {error && (
        <LicenseError
          message={error}
          onRetry={() => {
            setError(null);
            form.reset();
          }}
        />
      )}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack>
          <TextInput
            label="License Number"
            placeholder="Enter your license number"
            required
            {...form.getInputProps('license_number')}
          />

          <TextInput
            label="Activation Code"
            placeholder="Enter your activation code"
            required
            {...form.getInputProps('activation_code')}
          />

          <TextInput
            label="Transaction ID"
            placeholder="Enter your transaction ID"
            value={form.values.transaction_id}
            onChange={(event) => form.setFieldValue('transaction_id', event.currentTarget.value)}
          />

          <Group grow>
            <Button
              type="submit"
              loading={loading}
              fullWidth
              mt="md"
            >
              Activate License
            </Button>

            <Button
              onClick={async () => {
                try {
                  if (!form.values.transaction_id) {
                    notifications.show({
                      title: 'Transaction ID Required',
                      message: 'Please enter a transaction ID.',
                      color: 'red',
                    });
                    return;
                  }

                  setLoading(true);

                  // Use the license number from the license object, not from the form
                  const licenseNumber = license && typeof license === 'object' && license !== null && 'license_number' in license
                    ? (license as { license_number: string }).license_number
                    : '';

                  const response = await licenseService.updateTransactionID({
                    license_number: licenseNumber,
                    transaction_id: form.values.transaction_id,
                  });

                  // Get the formatted transaction ID from the response
                  const formattedTransactionId = response.license?.transaction_id || form.values.transaction_id;

                  // Update the license state with the new data
                  if (response.license) {
                    setLicense(response.license);
                  }

                  notifications.show({
                    title: 'Transaction ID Updated',
                    message: `Your transaction ID has been successfully updated to: ${formattedTransactionId}`,
                    color: 'green',
                    icon: <IconCheck size="1.1rem" />,
                    autoClose: 5000, // Keep the notification visible for 5 seconds
                  });

                  // Keep the transaction ID in the field for reference
                  form.setFieldValue('transaction_id', formattedTransactionId);
                } catch (err: unknown) {
                  console.error('Transaction ID update error:', err);

                  let errorMessage = 'An error occurred while updating your transaction ID.';

                  if (isApiError(err) && err.response) {
                    console.error('Error response status:', err.response.status);
                    console.error('Error response data:', err.response.data);

                    if (err.response.data) {
                      if (err.response.data.message) {
                        errorMessage = err.response.data.message;
                      } else if (err.response.data.detail) {
                        errorMessage = err.response.data.detail;
                      } else if (err.response.status === 400) {
                        errorMessage = 'Invalid license number or transaction ID. Please check and try again.';
                      } else if (err.response.status === 401) {
                        errorMessage = 'Your session has expired. Please log in again.';
                      } else if (err.response.status === 403) {
                        errorMessage = 'You do not have permission to update this license.';
                      } else if (err.response.status === 404) {
                        errorMessage = 'License not found. Please check the license number and try again.';
                      }
                    }
                  } else if (hasRequest(err)) {
                    errorMessage = 'Unable to connect to the server. Please check your internet connection.';
                  }

                  notifications.show({
                    title: 'Error',
                    message: errorMessage,
                    color: 'red',
                  });
                } finally {
                  setLoading(false);
                }
              }}
              variant="outline"
              color="blue"
              disabled={loading}
              mt="md"
            >
              Update Transaction ID
            </Button>
          </Group>
        </Stack>
      </form>

      <Divider my="lg" label="Need Help?" labelPosition="center" />

      <Text size="sm" mb="md">
        If you don&apos;t have an activation code or are experiencing issues, please contact our support team:
      </Text>

      <Code block>
        <EMAIL>
        +1 (555) 123-4567
      </Code>
    </Paper>
  );
}
