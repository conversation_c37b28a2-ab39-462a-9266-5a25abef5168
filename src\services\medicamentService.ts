import api from '../lib/api';

// Interfaces pour les médicaments
export interface Medicament {
  id: string;
  nom: string;
  nombreUtilisation: number;
  couleur?: string;
  dosage?: string;
  forme?: string;
  dateCreation?: string;
  dateModification?: string;
}

export interface MedicamentFilters {
  dateDebut?: Date | null;
  dateFin?: Date | null;
  sourceDonnees?: string;
  search?: string;
}

export interface MedicamentStats {
  totalUtilisations: number;
  totalMedicaments: number;
  medicamentLePlusUtilise: Medicament | null;
}

// Helper function pour les appels API avec fallback vers données mock
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: any,
  params?: any
) => {
  try {
    console.log(`Making API call to: ${endpoint}`);
    const config = { params };

    if (method === 'get') {
      const response = await api.get(endpoint, config);
      console.log('API response:', response.data);
      return response.data;
    } else {
      const response = await api[method](endpoint, data, config);
      console.log('API response:', response.data);
      return response.data;
    }
  } catch (error) {
    console.warn(`API call failed for ${endpoint}, using mock data:`, error);
    
    // Fallback vers données mock en cas d'échec API
    return getMockMedicaments(params);
  }
};

// Données mock pour le développement
const getMockMedicaments = (filters?: MedicamentFilters): Medicament[] => {
  return [
    {
      id: '1',
      nom: 'Actav 1 g / 125 mg | sachet',
      nombreUtilisation: 3,
      couleur: '#E8F5E8',
      dosage: '1 g / 125 mg',
      forme: 'sachet'
    },
    {
      id: '2',
      nom: 'Augmentin 1g /125 mg | sachet',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
      dosage: '1g / 125 mg',
      forme: 'sachet'
    },
    {
      id: '3',
      nom: 'Bi-rodogyl 1,5 ml / 250 mg | comprimé',
      nombreUtilisation: 4,
      couleur: '#E8F5E8',
      dosage: '1,5 ml / 250 mg',
      forme: 'comprimé'
    },
    {
      id: '4',
      nom: 'Brufen 400 mg | Comprimé',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
      dosage: '400 mg',
      forme: 'Comprimé'
    },
    {
      id: '5',
      nom: 'Cataflan 25 mg | Dragée',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
      dosage: '25 mg',
      forme: 'Dragée'
    },
    {
      id: '6',
      nom: 'Di-indo 25 mg | Comprimé',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
      dosage: '25 mg',
      forme: 'Comprimé'
    },
    {
      id: '7',
      nom: 'Doliprane 500 mg | sachet',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
      dosage: '500 mg',
      forme: 'sachet'
    },
    {
      id: '8',
      nom: 'Ery 250 mg | sachet',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
      dosage: '250 mg',
      forme: 'sachet'
    },
    {
      id: '9',
      nom: 'Solumedrol 120mg / 2ml | injection',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
      dosage: '120mg / 2ml',
      forme: 'injection'
    },
    {
      id: '10',
      nom: 'Spectrum 500 mg | Comprimé',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
      dosage: '500 mg',
      forme: 'Comprimé'
    },
    {
      id: '11',
      nom: 'Surgam 200 mg | Comprimé',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
      dosage: '200 mg',
      forme: 'Comprimé'
    },
  ];
};

// Service principal pour les médicaments
export const medicamentService = {
  // Récupérer la liste des médicaments avec filtres
  async getMedicaments(filters?: MedicamentFilters): Promise<Medicament[]> {
    const params = {
      date_debut: filters?.dateDebut?.toISOString(),
      date_fin: filters?.dateFin?.toISOString(),
      source_donnees: filters?.sourceDonnees,
      search: filters?.search,
    };

    return await makeApiCall('/api/pharmacy/medicaments/', 'get', null, params);
  },

  // Récupérer les statistiques d'utilisation
  async getMedicamentStats(filters?: MedicamentFilters): Promise<MedicamentStats> {
    try {
      const params = {
        date_debut: filters?.dateDebut?.toISOString(),
        date_fin: filters?.dateFin?.toISOString(),
        source_donnees: filters?.sourceDonnees,
      };

      const response = await makeApiCall('/api/pharmacy/medicaments/stats/', 'get', null, params);
      return response;
    } catch (error) {
      // Fallback vers calcul local
      const medicaments = await this.getMedicaments(filters);
      const totalUtilisations = medicaments.reduce((sum, med) => sum + med.nombreUtilisation, 0);
      const medicamentLePlusUtilise = medicaments.reduce((max, med) => 
        med.nombreUtilisation > (max?.nombreUtilisation || 0) ? med : max, 
        medicaments[0] || null
      );

      return {
        totalUtilisations,
        totalMedicaments: medicaments.length,
        medicamentLePlusUtilise,
      };
    }
  },

  // Créer un nouveau médicament
  async createMedicament(medicament: Omit<Medicament, 'id'>): Promise<Medicament> {
    return await makeApiCall('/api/pharmacy/medicaments/', 'post', medicament);
  },

  // Mettre à jour un médicament
  async updateMedicament(id: string, medicament: Partial<Medicament>): Promise<Medicament> {
    return await makeApiCall(`/api/pharmacy/medicaments/${id}/`, 'put', medicament);
  },

  // Supprimer un médicament
  async deleteMedicament(id: string): Promise<void> {
    return await makeApiCall(`/api/pharmacy/medicaments/${id}/`, 'delete');
  },

  // Exporter les données
  async exportMedicaments(filters?: MedicamentFilters, format: 'excel' | 'pdf' = 'excel'): Promise<Blob> {
    const params = {
      date_debut: filters?.dateDebut?.toISOString(),
      date_fin: filters?.dateFin?.toISOString(),
      source_donnees: filters?.sourceDonnees,
      format,
    };

    try {
      const response = await api.get('/api/pharmacy/medicaments/export/', {
        params,
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Export failed:', error);
      throw new Error('Échec de l\'export des données');
    }
  },
};

export default medicamentService;
