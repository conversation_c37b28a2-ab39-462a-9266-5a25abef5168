// frontend/dental_medicine/src/services/estimateService.ts

// Types pour les métadonnées de modification
export interface ToothModificationMetadata {
  highlighted_paths?: Record<string, PathHighlight>;
  timestamp?: string;
  doctor_notes?: string;
  cost_estimate?: number;
  duration_minutes?: number;
  materials_used?: string[];
  treatment_priority?: 'low' | 'medium' | 'high' | 'urgent';
  follow_up_required?: boolean;
  complications?: string[];
}

// Types pour les highlights de path SVG
export interface PathHighlight {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  opacity?: number;
  visible?: boolean;
}

// Types pour les modifications dentaires spécifiques
export type PreventiveModificationType = 'cleaning' | 'fluoride' | 'sealant';
export type EstheticModificationType = 'whitening_1' | 'whitening_2' | 'whitening_3' | 'whitening_4' | 'veneer';
export type RestorativeModificationType = 'restoration_temporary_1' | 'restoration_temporary_2' | 'restoration_amalgam' | 'restoration_glass_ionomer';
export type EndodonticModificationType = 'root_temporary_1' | 'root_temporary_2' | 'root_calcium_1' | 'root_calcium_2' | 'root_calcium_3' | 'root_calcium_4' | 'root_gutta_percha_1' | 'root_gutta_percha_2' | 'post_care';
export type ProstheticModificationType = 'onlay_1' | 'onlay_2' | 'crown_permanent_1' | 'crown_permanent_2' | 'crown_temporary' | 'crown_gold_1' | 'crown_gold_2' | 'crown_zirconia_1' | 'crown_zirconia_2' | 'crown_zirconia_3' | 'denture_1' | 'denture_2' | 'denture_3' | 'bridge_1' | 'bridge_2';
export type SurgicalModificationType = 'extraction' | 'implant_1' | 'implant_2' | 'implant_3' | 'implant_4' | 'implant_5' | 'implant_6' | 'implant_7' | 'bone_1' | 'bone_2' | 'bone_3' | 'bone_4' | 'bone_5' | 'bone_6' | 'bone_7' | 'resection' | 'teeth_crown';

// Type union pour tous les types de modifications
export type DentalModificationType =
  | PreventiveModificationType
  | EstheticModificationType
  | RestorativeModificationType
  | EndodonticModificationType
  | ProstheticModificationType
  | SurgicalModificationType
  | `path_${string}`; // Fallback pour les paths non mappés

// Types pour les spécialités dentaires
export type DentalSpecialty = 'esthetic' | 'prosthetic' | 'surgery' | 'orthodontics' | 'general';

// Configuration de l'API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

// Désactiver temporairement les appels API pour éviter l'erreur 500
const ENABLE_API_CALLS = false;

// Simple fetch wrapper pour les appels API
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      // TODO: Ajouter l'authentification si nécessaire
      // 'Authorization': `Bearer ${token}`,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

// Types pour les modifications dentaires
export interface ToothModification {
  id?: string;
  tooth_number: number;
  svg_id: string;
  path_id: string;
  modification_type: DentalModificationType;
  status: 'not_applied' | 'planned' | 'applied';
  is_visible: boolean;
  color?: string;
  stroke?: string;
  metadata?: ToothModificationMetadata;
  created_at?: string;
  updated_at?: string;
}

export interface EstimateSession {
  id?: string;
  patient_id?: string;
  session_name: string;
  modifications: ToothModification[];
  created_at?: string;
  updated_at?: string;
}

// Fonctions utilitaires pour le typage
export const getModificationTypeFromPath = (pathId: string): DentalModificationType => {
  return (PATH_MODIFICATION_MAPPING[pathId] || `path_${pathId}`) as DentalModificationType;
};

export const getSpecialtyFromModificationType = (modificationType: DentalModificationType): DentalSpecialty => {
  if (['cleaning', 'fluoride', 'sealant'].includes(modificationType)) return 'esthetic';
  if (modificationType.startsWith('whitening') || modificationType === 'veneer') return 'esthetic';
  if (modificationType.startsWith('restoration') || modificationType.startsWith('root') || modificationType === 'post_care') return 'prosthetic';
  if (modificationType.startsWith('onlay') || modificationType.startsWith('crown') || modificationType.startsWith('denture') || modificationType.startsWith('bridge')) return 'prosthetic';
  if (modificationType === 'extraction' || modificationType.startsWith('implant') || modificationType.startsWith('bone') || modificationType === 'resection') return 'surgery';
  if (modificationType === 'teeth_crown') return 'orthodontics';
  return 'general';
};

export const isValidPathId = (pathId: string): boolean => {
  return pathId in PATH_MODIFICATION_MAPPING;
};

export const getAllSupportedPathIds = (): string[] => {
  return Object.keys(PATH_MODIFICATION_MAPPING);
};

export const getModificationsBySpecialty = (specialty: DentalSpecialty): DentalModificationType[] => {
  return Object.values(PATH_MODIFICATION_MAPPING).filter(
    modificationType => getSpecialtyFromModificationType(modificationType as DentalModificationType) === specialty
  ) as DentalModificationType[];
};

// Mapping des path IDs vers les types de modifications
export const PATH_MODIFICATION_MAPPING: Record<string, string> = {
  // Préventif
  '17': 'cleaning',
  '18': 'fluoride',
  '19': 'sealant',

  // Esthétique
  '20': 'whitening_1',
  '21': 'whitening_2',
  '22': 'whitening_3',
  '23': 'whitening_4',
  '37': 'veneer',

  // Restaurations
  '24': 'restoration_temporary_1',
  '25': 'restoration_temporary_2',
  '26': 'restoration_amalgam',
  '27': 'restoration_glass_ionomer',

  // Endodontie
  '28': 'root_temporary_1',
  '29': 'root_temporary_2',
  '30': 'root_calcium_1',
  '31': 'root_calcium_2',
  '32': 'root_calcium_3',
  '33': 'root_calcium_4',
  '34': 'root_gutta_percha_1',
  '35': 'root_gutta_percha_2',
  '36': 'post_care',

  // Prothèses
  '38': 'onlay_1',
  '39': 'onlay_2',
  '40': 'crown_permanent_1',
  '41': 'crown_permanent_2',
  '42': 'crown_temporary',
  '43': 'crown_gold_1',
  '44': 'crown_gold_2',
  '45': 'crown_zirconia_1',
  '46': 'crown_zirconia_2',
  '47': 'crown_zirconia_3',
  '48': 'denture_1',
  '49': 'denture_2',
  '50': 'denture_3',
  '51': 'bridge_1',
  '52': 'bridge_2',

  // Chirurgie et Implants
  '53': 'extraction',
  '54': 'implant_1',
  '55': 'implant_2',
  '56': 'implant_3',
  '57': 'implant_4',
  '58': 'implant_5',
  '59': 'implant_6',
  '60': 'implant_7',
  '61': 'bone_1',
  '62': 'bone_2',
  '63': 'bone_3',
  '64': 'bone_4',
  '65': 'bone_5',
  '66': 'bone_6',
  '67': 'bone_7',
  '68': 'resection',
  '69': 'teeth_crown',
};

class EstimateService {
  private currentSession: EstimateSession | null = null;
  private autoSaveTimeout: NodeJS.Timeout | null = null;
  private readonly AUTO_SAVE_DELAY = 2000; // 2 secondes

  // Initialiser une session d'estimation
  async initializeSession(patientId?: string, sessionName: string = 'Session par défaut'): Promise<EstimateSession> {
    // Mode local temporaire pour éviter les erreurs 404
    if (!ENABLE_API_CALLS) {
      this.currentSession = {
        id: `local-${Date.now()}`,
        patient_id: patientId,
        session_name: sessionName,
        modifications: [],
        created_at: new Date().toISOString(),
      };

      return this.currentSession;
    }

    try {
      const response = await apiCall('/api/estimate-sessions/', {
        method: 'POST',
        body: JSON.stringify({
          patient_id: patientId,
          session_name: sessionName,
          modifications: []
        })
      });

      this.currentSession = response;
      return this.currentSession!; // Non-null assertion car nous venons de l'assigner
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de la session:', error);

      // Fallback: créer une session locale
      this.currentSession = {
        id: `local-${Date.now()}`,
        patient_id: patientId,
        session_name: sessionName,
        modifications: [],
        created_at: new Date().toISOString(),
      };

      return this.currentSession;
    }
  }

  // Sauvegarder une modification de manière réactive
  async saveModificationReactive(
    svgId: string,
    pathId: string,
    isVisible: boolean,
    highlightedPaths?: Record<string, PathHighlight>
  ): Promise<void> {
    if (!this.currentSession) {
      console.warn('⚠️ Aucune session active. Initialisation automatique...');
      await this.initializeSession();
    }

    const toothNumber = parseInt(svgId);
    const modificationType = getModificationTypeFromPath(pathId);

    // Créer l'objet modification
    const modification: ToothModification = {
      tooth_number: toothNumber,
      svg_id: svgId,
      path_id: pathId,
      modification_type: modificationType,
      status: isVisible ? 'applied' : 'not_applied',
      is_visible: isVisible,
      metadata: {
        highlighted_paths: highlightedPaths,
        timestamp: new Date().toISOString(),
      }
    };

    // Ajouter les couleurs si disponibles
    const pathKey = `${svgId}-${pathId}`;
    if (highlightedPaths && highlightedPaths[pathKey]) {
      modification.color = highlightedPaths[pathKey].fill;
      modification.stroke = highlightedPaths[pathKey].stroke;
    }

    // Mettre à jour la session locale
    this.updateLocalSession(modification);

    // Programmer la sauvegarde automatique
    this.scheduleAutoSave();

    console.log('🔄 Modification sauvegardée (réactive):', modification);
  }

  // Mettre à jour la session locale
  private updateLocalSession(modification: ToothModification): void {
    if (!this.currentSession) return;

    const existingIndex = this.currentSession.modifications.findIndex(
      mod => mod.svg_id === modification.svg_id && mod.path_id === modification.path_id
    );

    if (existingIndex >= 0) {
      // Mettre à jour la modification existante
      this.currentSession.modifications[existingIndex] = {
        ...this.currentSession.modifications[existingIndex],
        ...modification,
        updated_at: new Date().toISOString(),
      };
    } else {
      // Ajouter une nouvelle modification
      this.currentSession.modifications.push({
        ...modification,
        id: `mod-${Date.now()}-${Math.random()}`,
        created_at: new Date().toISOString(),
      });
    }
  }

  // Programmer la sauvegarde automatique
  private scheduleAutoSave(): void {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    this.autoSaveTimeout = setTimeout(() => {
      this.saveToBackend();
    }, this.AUTO_SAVE_DELAY);
  }

  // Sauvegarder vers le backend
  private async saveToBackend(): Promise<void> {
    if (!this.currentSession) return;

    // Mode local temporaire pour éviter les erreurs 500
    if (!ENABLE_API_CALLS) {
      console.log('🔄 Mode local: Sauvegarde en localStorage uniquement');
      this.saveToLocalStorage();
      return;
    }

    try {
      // Utiliser l'API de mise à jour en lot pour les modifications
      const response = await apiCall('/api/tooth-modifications/bulk_update/', {
        method: 'POST',
        body: JSON.stringify({
          session_id: this.currentSession.id,
          modifications: this.currentSession.modifications
        })
      });

      console.log('💾 Modifications sauvegardées vers le backend:', response);

      // Mettre à jour la session
      await apiCall(`/api/estimate-sessions/${this.currentSession.id}/`, {
        method: 'PUT',
        body: JSON.stringify({
          ...this.currentSession,
          updated_at: new Date().toISOString(),
        })
      });

    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error);

      // Sauvegarder en localStorage comme fallback
      this.saveToLocalStorage();
    }
  }

  // Sauvegarder en localStorage (fallback)
  private saveToLocalStorage(): void {
    if (!this.currentSession) return;

    try {
      localStorage.setItem('dental_estimate_session', JSON.stringify(this.currentSession));
      console.log('💾 Session sauvegardée en localStorage (fallback)');
    } catch (error) {
      console.error('❌ Erreur localStorage:', error);
    }
  }

  // Charger une session existante
  async loadSession(sessionId: string): Promise<EstimateSession | null> {
    // Mode local temporaire pour éviter les erreurs 500
    if (!ENABLE_API_CALLS) {
      console.log('🔄 Mode local: Chargement depuis localStorage uniquement');
      return this.loadFromLocalStorage();
    }

    try {
      const response = await apiCall(`/api/estimate-sessions/${sessionId}/`);
      this.currentSession = response;
      console.log('📂 Session chargée:', this.currentSession);
      return this.currentSession;
    } catch (error) {
      console.error('❌ Erreur lors du chargement:', error);

      // Essayer de charger depuis localStorage
      return this.loadFromLocalStorage();
    }
  }

  // Charger depuis localStorage
  private loadFromLocalStorage(): EstimateSession | null {
    try {
      const saved = localStorage.getItem('dental_estimate_session');
      if (saved) {
        this.currentSession = JSON.parse(saved);
        console.log('📂 Session chargée depuis localStorage:', this.currentSession);
        return this.currentSession;
      }
    } catch (error) {
      console.error('❌ Erreur localStorage:', error);
    }
    return null;
  }

  // Obtenir la session actuelle
  getCurrentSession(): EstimateSession | null {
    return this.currentSession;
  }

  // Obtenir les modifications pour une dent spécifique
  getToothModifications(toothNumber: number): ToothModification[] {
    if (!this.currentSession) return [];

    return this.currentSession.modifications.filter(
      mod => mod.tooth_number === toothNumber
    );
  }

  // Obtenir les statistiques des modifications
  getModificationStats(): {
    total: number;
    applied: number;
    planned: number;
    not_applied: number;
    by_type: Record<string, number>;
  } {
    if (!this.currentSession) {
      return { total: 0, applied: 0, planned: 0, not_applied: 0, by_type: {} };
    }

    const stats = {
      total: this.currentSession.modifications.length,
      applied: 0,
      planned: 0,
      not_applied: 0,
      by_type: {} as Record<string, number>,
    };

    this.currentSession.modifications.forEach(mod => {
      // Compter par statut
      stats[mod.status]++;

      // Compter par type
      if (!stats.by_type[mod.modification_type]) {
        stats.by_type[mod.modification_type] = 0;
      }
      stats.by_type[mod.modification_type]++;
    });

    return stats;
  }

  // Forcer la sauvegarde immédiate
  async forceSave(): Promise<void> {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
      this.autoSaveTimeout = null;
    }
    await this.saveToBackend();
  }

  // Réinitialiser la session
  resetSession(): void {
    this.currentSession = null;
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
      this.autoSaveTimeout = null;
    }
    localStorage.removeItem('dental_estimate_session');
  }
}

// Instance singleton
export const estimateService = new EstimateService();
export default estimateService;
