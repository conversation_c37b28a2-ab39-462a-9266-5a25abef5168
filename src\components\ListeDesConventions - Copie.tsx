import { useState } from 'react';
import cx from 'clsx';
import { Avatar, Checkbox,  ScrollArea, Table, Text } from '@mantine/core';
import classes from './TableListeDesConventions.module.css';
import { TextInput, ActionIcon, Group, Tooltip } from '@mantine/core';
import { Icon } from '@mdi/react';
import { Pagination } from '@mantine/core';
import {
  mdiMagnify,
  mdiFilterVariant,
  mdiFileExcel,
  mdiReload,
  mdiDotsVertical,
   mdiFormatListBulleted,
  mdiClose,
 
} from '@mdi/js';
import {
  
  IconFilter,
 
} from '@tabler/icons-react';
import {
  Box,
  Button,
  Title,
} from '@mantine/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
function exportToExcel(data: any[], filename = 'export.xlsx') {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Feuille1');

  const blob = new Blob([XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })], {
    type: 'application/octet-stream',
  });

  saveAs(blob, filename);
}
type ListeDesConventionsFormProps = {
  onCancel: () => void;
  onSubmit: (data: any) => void;
  onSearch: (data: any) => void;
   onFilter: (data: any) => void;
    onExport: (data: any) => void; 
    onReload: (data: any) => void;
    page: (data: any) => void;
     onPageChange: (data: any) => void;
      totalPages: (data: any) => void;
};
const data = [
  {
    id: '1',
    avatar:
      'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-1.png',
    name: 'Robert Wolfkisser',
    job: 'Engineer',
    email: '<EMAIL>',
  },
  {
    id: '2',
    avatar:
      'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-7.png',
    name: 'Jill Jailbreaker',
    job: 'Engineer',
    email: '<EMAIL>',
  },
  {
    id: '3',
    avatar:
      'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png',
    name: 'Henry Silkeater',
    job: 'Designer',
    email: '<EMAIL>',
  },
  {
    id: '4',
    avatar:
      'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-3.png',
    name: 'Bill Horsefighter',
    job: 'Designer',
    email: '<EMAIL>',
  },
  {
    id: '5',
    avatar:
      'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-10.png',
    name: 'Jeremy Footviewer',
    job: 'Manager',
    email: '<EMAIL>',
  },
];
export function ListeDesConventions({ onCancel,  onSearch, onFilter, onExport, onReload,page, onPageChange, totalPages }: ListeDesConventionsFormProps) {

  const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
 const toggleSidebar = () => {
       setIsSidebarVisible(!isSidebarVisible);
     };

   const [selection, setSelection] = useState(['1']);
  const toggleRow = (id: string) =>
    setSelection((current) =>
      current.includes(id) ? current.filter((item) => item !== id) : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) => (current.length === data.length ? [] : data.map((item) => item.id)));

  const rows = data.map((item) => {
    const selected = selection.includes(item.id);
    return (
      <Table.Tr key={item.id} className={cx({ [classes.rowSelected]: selected })}>
        <Table.Td>
          <Checkbox checked={selection.includes(item.id)} onChange={() => toggleRow(item.id)} />
        </Table.Td>
        <Table.Td>
          <Group gap="sm">
            <Avatar size={26} src={item.avatar} radius={26} />
            <Text size="sm" fw={500}>
              {item.name}
            </Text>
          </Group>
        </Table.Td>
        <Table.Td>{item.email}</Table.Td>
        <Table.Td>{item.job}</Table.Td>
      </Table.Tr>
    );
  });
  return (
    <Box>
      {/* Toolbar */}
      <Group justify="space-between" px="md" py="xs" bg="var(--mantine-color-blue-light)">
        <Group>
          <Icon path={mdiFormatListBulleted} size={1} />
          <Title order={3}>Liste des conventions</Title>
        </Group>
        <Button
          variant="subtle"
          color="gray"
          onClick={onCancel}
          leftSection={<Icon path={mdiClose} size={0.9} />}
        />
      </Group>
       <Group >
                <div className='w-[19%] mt-0'>
                 {isSidebarVisible && (
      
              <Tabs value={tabIndex} onChange={setTabIndex}>
                    <Tabs.List>
                      <Tabs.Tab value="0">Filtre avancé</Tabs.Tab>
                      <Tabs.Tab value="1">Règles de mise en forme</Tabs.Tab>
                      <Tabs.Tab value="2">3</Tabs.Tab>
                    </Tabs.List>
              
                    <Tabs.Panel value="0" pt="xs">
                      <FilterList />
                    </Tabs.Panel>
              
                    <Tabs.Panel value="1" pt="xs">
                  <StyleRulesTab
                    styleRules={vm.styleRules}
                    isCreate={vm.is_create}
                    onStartCreate={() => setVm((prev) => ({ ...prev, is_create: true }))}
                    columns={vm.columns}      // ✅ maintenant reconnu
                    model={vm.mnModel}
                    draftRule={vm.draftRule}
                  />
                      <Tabs.Panel value="2" pt="xs">
      
                        {/* if click plus icon shwo tabs */}
                   <NouvelleRegle
        columns={columns}
        styleRules={styleRules}
        isCreate={isCreate}
        onCancel={handleCancel}
        onSave={handleSave}
      />
      
               </Tabs.Panel>
              </Tabs.Panel>
                    
                  </Tabs>
       
        
              
              )}
              </div>
              {/* Tableau */}
              <div className={isSidebarVisible ?  "w-[80%]": "w-full "}>
             <Group justify="space-between" mb="md">
      <Group>
           
        <Tooltip label="Recherche">
          <Icon path={mdiMagnify} size={1} />
        </Tooltip>
        <TextInput
          placeholder="Rechercher"
          onChange={(e) => onSearch(e.currentTarget.value)}
        />
        <ActionIcon onClick={onFilter}>
          <Icon path={mdiFilterVariant} size={1} />
        </ActionIcon>
      </Group>

      <Group>
        <ActionIcon onClick={onReload}>
          <Icon path={mdiReload} size={1} />
        </ActionIcon>
        <ActionIcon onClick={onExport}>
          <Icon path={mdiFileExcel} size={1} />
        </ActionIcon>
        <ActionIcon>
          <Icon path={mdiDotsVertical} size={1} />
        </ActionIcon>
      </Group>
         </Group>
         <ScrollArea>
      <Table miw={800} verticalSpacing="sm">
        <Table.Thead>
          <Table.Tr>
            <Table.Th w={40}>
              <Checkbox
                onChange={toggleAll}
                checked={selection.length === data.length}
                indeterminate={selection.length > 0 && selection.length !== data.length}
              />
            </Table.Th>
            <Table.Th>User</Table.Th>
            <Table.Th>Email</Table.Th>
            <Table.Th>Job</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
       <Pagination value={page} onChange={onPageChange} total={totalPages} />
    </ScrollArea>
                  </div>
              </Group>
      
      
        
      
     
    </Box>
  );
}


