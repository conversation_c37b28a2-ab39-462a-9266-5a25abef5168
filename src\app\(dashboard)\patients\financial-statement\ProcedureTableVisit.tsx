import { Table } from '@mantine/core';


interface TeethProcedure {
  code: string;
  Actes: string;
  Qté: number;
  Honoraire: number;
  Total: number;
  Commentaire?: string;
}

interface Props {
  data: TeethProcedure[];
}

export function ProcedureTableVisit({ data }: Props) {
  return (
    <Table horizontalSpacing="md" verticalSpacing="xs" miw={700} layout="fixed"   striped highlightOnHover withTableBorder withColumnBorders>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>Code</Table.Th>
          <Table.Th>Actes</Table.Th>
          <Table.Th>Qté</Table.Th>
          <Table.Th>Honoraire</Table.Th>
          <Table.Th>Total</Table.Th>
          <Table.Th>Commentaire</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {data.length === 0 ? (
          <Table.Tr>
            <Table.Td colSpan={8} style={{ textAlign: 'center' }}>
              Aucun élément trouvé.
            </Table.Td>
          </Table.Tr>
        ) : (
          data.map((item, index) => (
            <Table.Tr key={index}>
              <Table.Td>{item.code}</Table.Td>
              <Table.Td>{item.Actes}</Table.Td>
              <Table.Td>{item.Qté}</Table.Td>
              <Table.Td>{item.Honoraire.toFixed(2)}</Table.Td>
              <Table.Td>{(item.Total).toFixed(2)}</Table.Td>
              <Table.Td>{item.Commentaire || ''}</Table.Td>
            </Table.Tr>
          ))
        )}
      </Table.Tbody>
    </Table>
  );
}
