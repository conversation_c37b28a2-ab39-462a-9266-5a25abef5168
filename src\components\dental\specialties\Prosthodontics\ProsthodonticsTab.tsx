

import React, { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Tabs, rem, Button, Group, Text, Dialog, Menu, Container, Flex, Badge, Switch } from '@mantine/core';
import { IconTool, IconSettings, IconPhoto, IconSquareRoundedPlusFilled, IconBrain, IconRefresh } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { DentalSpecialtyTabProps, SaveManagerRef, ModificationState, SVGPathStyle } from '../../shared/types';
import { useSaveManager } from '../../shared/SaveManager';
import ProsthodonticControls from './ProsthodonticControls';
import ProsthodonticProcedures from './ProsthodonticProcedures';
import { DentalSvgWrapper } from '../../shared/DentalSvgWrapper';
import { Dan<PERSON>, DantalB } from '@/utils/Tdantal';
import { useSmartPathManager } from '../../shared/useSmartPathManager';

export const ProsthodonticsTab = forwardRef<SaveManagerRef, DentalSpecialtyTabProps>(({
  onModificationChange,

}, ref) => {

  // États locaux pour la prosthodontie
  const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>({});
  const [highlightedPaths, ] = useState<Record<string, SVGPathStyle>>({});
  const [clickedIds, ] = useState<string[]>([]);
  const [activeButton, setActiveButton] = useState<string>('');
  const [targetPath, setTargetPath] = useState<string>('');
  const [opened, {  close }] = useDisclosure(false);
  const [isSmartModeEnabled, setIsSmartModeEnabled] = useState<boolean>(true);

  // Hook pour la gestion intelligente des paths
  const { applySmartTreatment, conflictDialog, resetToothToBase } = useSmartPathManager({
    hiddenPaths,
    setHiddenPaths,
    onModificationChange,
    highlightedPaths
  });

  // État de modification pour cette spécialité
  const modificationState: ModificationState = {
    hiddenPaths,
    highlightedPaths,
    clickedIds,
    activeButton,
    targetPath
  };

  // Gestionnaire de sauvegarde
  const { save, hasChanges, SaveManagerComponent } = useSaveManager(
    modificationState,
    onModificationChange,
    'prosthodontic'
  );

  // Exposer les méthodes via ref
 useImperativeHandle(ref, () => ({
    triggerSave: async () => {
      await save();
    },
    hasUnsavedChanges: () => hasChanges()
  }), [save, hasChanges]);

  // Gestionnaire de clic sur SVG avec mode intelligent
  const handleSvgClick = useCallback(async (svgId: string) => {
    // Fonction de fallback pour l'ancien système
    const togglePathVisibility = (pathId: string, visible: boolean) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths(prev => {
        const newHiddenPaths = { ...prev, [key]: visible };

        // Sauvegarder automatiquement si onModificationChange est disponible
        if (onModificationChange) {
          onModificationChange(svgId, pathId, !visible, highlightedPaths)
            .catch(error => console.error('Erreur sauvegarde prosthodontie:', error));
        }

        return newHiddenPaths;
      });
    };

    // Appliquer le traitement selon le bouton actif
    if (activeButton) {
      console.log(`🦷 Application du traitement ${activeButton} sur la dent ${svgId}`);

      if (isSmartModeEnabled) {
        // Mode intelligent : utiliser le système de gestion des conflits
        console.log(`🧠 Mode intelligent activé pour ${activeButton}`);

        try {
          await applySmartTreatment(activeButton, svgId);
        } catch (error) {
          console.error('Erreur lors de l\'application intelligente:', error);
          // Fallback vers l'ancien système en cas d'erreur
          applyLegacyTreatment(activeButton, togglePathVisibility);
        }
      } else {
        // Mode classique : utiliser l'ancien système
        console.log(`🔧 Mode classique pour ${activeButton}`);
        applyLegacyTreatment(activeButton, togglePathVisibility);
      }
    }
  }, [activeButton, targetPath, onModificationChange, highlightedPaths, isSmartModeEnabled, applySmartTreatment]);

  // Fonction pour appliquer les traitements en mode classique avec reset
  const applyLegacyTreatment = useCallback((treatment: string, svgId: string) => {
    console.log(`🔧 Mode classique: Application de ${treatment} sur dent ${svgId}`);

    // Reset de la dent à l'état de base d'abord
    setHiddenPaths(prev => {
      const newHiddenPaths = { ...prev };

      // Cacher tous les paths de traitement pour cette dent (paths 17-100)
      for (let i = 17; i <= 100; i++) {
        newHiddenPaths[`${svgId}-${i}`] = true;
      }

      // Garder les paths de base visibles (paths 1-16)
      for (let i = 1; i <= 16; i++) {
        newHiddenPaths[`${svgId}-${i}`] = false;
      }

      // Appliquer le traitement spécifique
      switch (treatment) {
        case 'veneer':
          console.log('✨ Application de facettes prosthodontiques (mode classique)');
          newHiddenPaths[`${svgId}-37`] = false; // Afficher facettes
          break;
        case 'onlay':
          console.log('🔧 Application d\'onlays (mode classique)');
          newHiddenPaths[`${svgId}-39`] = false; // Afficher onlays
          break;
        case 'crown':
          console.log('👑 Application de couronnes (mode classique)');
          newHiddenPaths[`${svgId}-41`] = false; // Afficher couronnes
          break;
        case 'extraction':
          console.log('🦷 Marquage pour extraction (mode classique)');
          newHiddenPaths[`${svgId}-53`] = false; // Afficher extraction
          break;
        case 'bonding':
          console.log('🔗 Application de collage composite (mode classique)');
          newHiddenPaths[`${svgId}-22`] = false; // Afficher collage
          break;
        case 'bridge':
          console.log('🌉 Application de bridge (mode classique)');
          newHiddenPaths[`${svgId}-52`] = false; // Afficher bridge
          break;
        case 'denture':
          console.log('🦷 Application de prothèse dentaire (mode classique)');
          newHiddenPaths[`${svgId}-48`] = false; // Afficher prothèse
          break;
        case 'implant':
          console.log('🔩 Application d\'implant (mode classique)');
          newHiddenPaths[`${svgId}-56`] = false; // Afficher implant
          break;
        default:
          if (targetPath) {
            newHiddenPaths[`${svgId}-${targetPath}`] = false;
          }
          break;
      }

      return newHiddenPaths;
    });

    // Sauvegarder les modifications
    if (onModificationChange) {
      const pathToSave = getPathForTreatment(treatment);
      if (pathToSave) {
        onModificationChange(svgId, pathToSave, false, highlightedPaths)
          .catch(error => console.error('Erreur sauvegarde prosthodontie:', error));
      }
    }
  }, [targetPath, onModificationChange, highlightedPaths]);

  // Fonction utilitaire pour obtenir le path ID d'un traitement
  const getPathForTreatment = (treatment: string): string | null => {
    const pathMap: Record<string, string> = {
      'veneer': '37',
      'onlay': '39',
      'crown': '41',
      'extraction': '53',
      'bonding': '22',
      'bridge': '52',
      'denture': '48',
      'implant': '56'
    };
    return pathMap[treatment] || targetPath;
  };

  // Gestionnaire de changement de bouton
  const handleButtonClick = useCallback((buttonId: string) => {
    setActiveButton(buttonId);
    console.log(`🔧 Bouton prosthodontie activé: ${buttonId}`);
  }, []);

  // Gestionnaire de changement de path cible
  const handleTargetPathChange = useCallback((pathId: string) => {
    setTargetPath(pathId);
    console.log(`🎯 Path cible prosthodontie: ${pathId}`);
  }, []);

  return (
    <>
      {/* Composant de sauvegarde invisible */}
      {SaveManagerComponent}

      {/* Dialog de conflit intelligent */}
      {conflictDialog}

      <div className="my-4">
        {/* Contrôles du Mode Intelligent */}
        <div className="mb-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200 mx-4">
          <Group justify="space-between" align="center">
            <div className="flex items-center space-x-3">
              <IconBrain className="text-purple-600" size={20} />
              <div>
                <Text size="sm" fw={600} className="text-purple-800">
                  Mode Intelligent Prosthodontie
                </Text>
                <Text size="xs" className="text-purple-600">
                  Gestion automatique des conflits de traitements prothétiques
                </Text>
              </div>
            </div>
            <Group>
              <Switch
                checked={isSmartModeEnabled}
                onChange={(event) => setIsSmartModeEnabled(event.currentTarget.checked)}
                label={isSmartModeEnabled ? "Activé" : "Désactivé"}
                color="purple"
                size="sm"
              />
              <Button
                variant="light"
                color="purple"
                size="xs"
                leftSection={<IconRefresh size={14} />}
                onClick={() => {
                  // Reset toutes les dents à l'état de base
                  for (let i = 1; i <= 32; i++) {
                    resetToothToBase(i.toString());
                  }
                }}
              >
                Reset
              </Button>
              <Badge color={isSmartModeEnabled ? "green" : "gray"} variant="light" size="sm">
                {isSmartModeEnabled ? "🧠 IA" : "🔧 Classique"}
              </Badge>
            </Group>
          </Group>
        </div>
        <Container>
          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px' }} align="center">
            <div style={{ position: 'absolute', right: 16 }}>
              <Menu withinPortal position="bottom-end" shadow="sm">
                <Menu.Target>
                  <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                    Procédures Prothétiques
                  </Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item>Couronne</Menu.Item>
                  <Menu.Item>Bridge</Menu.Item>
                  <Menu.Item>Prothèse partielle</Menu.Item>
                  <Menu.Item>Prothèse complète</Menu.Item>
                  <Menu.Item>Implant</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </div>

            {/* Contrôles spécifiques à la prosthodontie */}
            <ProsthodonticControls
              activeButton={activeButton}
              onButtonClick={handleButtonClick}
              onTargetPathChange={handleTargetPathChange}
            />
          </Flex>

          {/* SVG Dentaire - Toutes les 32 dents */}
          <div className="dental-svg-container">
            <div className="max-w-[920px] mt-2 mx-auto">
              {/* Mâchoire supérieure (dents 1-16) */}
              <div className="flex h-full px-0 max-h-[160px] mb-2">
                {Dantal.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                  />
                ))}
              </div>
              {/* Mâchoire inférieure (dents 17-32) */}
              <div className="flex h-full px-0 max-h-[160px]">
                {DantalB.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                  />
                ))}
              </div>
            </div>
          </div>
        </Container>

        {/* Onglets de procédures */}
        <div className="border-base-200 mx-4 border-t mt-4">
          <Tabs variant="unstyled" defaultValue="All Procedures">
            <Tabs.List grow className="space-x-0 gap-0 mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={<IconTool style={{ width: rem(16), height: rem(16) }} />}
              >
                Toutes les Procédures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
              >
                Planifiées
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={<IconPhoto style={{ width: rem(16), height: rem(16) }} />}
              >
                Terminées
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="All Procedures">
              <ProsthodonticProcedures
                type="all"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Planned">
              <ProsthodonticProcedures
                type="planned"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Completed">
              <ProsthodonticProcedures
                type="completed"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>
          </Tabs>
        </div>

        {/* Dialog de sauvegarde */}
        <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
            <Text size="sm" mb="xs" fw={500}>
              Sauvegarder les Modifications Prothétiques
            </Text>
          </Group>
          <Group align="flex-end">
            <Button
              w="100%"
              onClick={async () => {
                await save();
                close();
              }}
              className="hover:bg-[#3799CE]/90"
            >
              Sauvegarder
            </Button>
          </Group>
        </Dialog>
      </div>
    </>
  );
});

ProsthodonticsTab.displayName = 'ProsthodonticsTab';
