// frontend/dental_medicine/src/components/content/dental/specialties/Orthodontics/OrthodonticProcedures.tsx

import React, { useState, useMemo } from 'react';
import { Table, Checkbox, Text, Badge, Button, Group, TextInput, Select } from '@mantine/core';
import { IconSearch, IconFilter } from '@tabler/icons-react';
import { OrthodonticProcedure, ModificationState, SVGPathStyle } from '../../shared/types';

interface OrthodonticProceduresProps {
  type: 'all' | 'planned' | 'completed';
  modificationState: ModificationState;
  onModificationChange?: (
    svgId: string,
    pathId: string,
    isVisible: boolean,
        highlightedPaths?: Record<string, SVGPathStyle>
  ) => Promise<void>;
}

export const OrthodonticProcedures: React.FC<OrthodonticProceduresProps> = ({
  type,

  onModificationChange
}) => {

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Données des procédures orthodontiques
  const allOrthodonticProcedures: OrthodonticProcedure[] = [
    {
      position: 1,
      mass: 1.008,
      symbol: 'BRA',
      name: 'Appareils Orthodontiques Fixes',
      type: 'braces',
      pathId: '66',
      cost: 3500,
      duration: 120,
      category: 'fixe',
      duration_months: 24,
      adjustment_frequency: 6
    },
    {
      position: 2,
      mass: 4.003,
      symbol: 'INV',
      name: 'Gouttières Invisalign',
      type: 'aligners',
      pathId: '67',
      cost: 4500,
      duration: 90,
      category: 'amovible',
      duration_months: 18,
      adjustment_frequency: 2
    },
    {
      position: 3,
      mass: 6.941,
      symbol: 'RET',
      name: 'Appareil de Contention',
      type: 'retainer',
      pathId: '70',
      cost: 400,
      duration: 45,
      category: 'contention',
      duration_months: 12,
      adjustment_frequency: 6
    },
    {
      position: 4,
      mass: 9.012,
      symbol: 'EXP',
      name: 'Expanseur Palatin',
      type: 'expander',
      pathId: '72',
      cost: 800,
      duration: 60,
      category: 'expansion',
      duration_months: 6,
      adjustment_frequency: 4
    },
    {
      position: 5,
      mass: 10.811,
      symbol: 'LIN',
      name: 'Appareils Linguaux',
      type: 'braces',
      pathId: '68',
      cost: 5500,
      duration: 150,
      category: 'fixe',
      duration_months: 30,
      adjustment_frequency: 8
    },
    {
      position: 6,
      mass: 12.011,
      symbol: 'CER',
      name: 'Brackets Céramique',
      type: 'braces',
      pathId: '69',
      cost: 4200,
      duration: 120,
      category: 'fixe',
      duration_months: 24,
      adjustment_frequency: 6
    },
    {
      position: 7,
      mass: 14.007,
      symbol: 'ADJ',
      name: 'Ajustement Orthodontique',
      type: 'braces',
      pathId: '74',
      cost: 80,
      duration: 30,
      category: 'maintenance',
      duration_months: 0,
      adjustment_frequency: 1
    },
    {
      position: 8,
      mass: 15.999,
      symbol: 'ELA',
      name: 'Élastiques Orthodontiques',
      type: 'braces',
      pathId: '76',
      cost: 25,
      duration: 15,
      category: 'accessoire',
      duration_months: 0,
      adjustment_frequency: 1
    },
    {
      position: 9,
      mass: 18.998,
      symbol: 'HEA',
      name: 'Casque Orthodontique',
      type: 'expander',
      pathId: '77',
      cost: 600,
      duration: 45,
      category: 'externe',
      duration_months: 12,
      adjustment_frequency: 8
    },
    {
      position: 10,
      mass: 20.180,
      symbol: 'SPL',
      name: 'Gouttière de Contention',
      type: 'retainer',
      pathId: '78',
      cost: 300,
      duration: 30,
      category: 'contention',
      duration_months: 24,
      adjustment_frequency: 12
    }
  ];

  // Filtrer les procédures selon le type
  const filteredProcedures = useMemo(() => {
    let procedures = allOrthodonticProcedures;

    // Filtrer par type (all, planned, completed)
    if (type === 'planned') {
      procedures = procedures.filter(proc => proc.position <= 5);
    } else if (type === 'completed') {
      procedures = procedures.filter(proc => proc.position > 5);
    }

    // Filtrer par terme de recherche
    if (searchTerm) {
      procedures = procedures.filter(proc =>
        proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (filterCategory !== 'all') {
      procedures = procedures.filter(proc => proc.category === filterCategory);
    }

    return procedures;
  }, [type, searchTerm, filterCategory]);

  // Gestionnaire de sélection de ligne
  const handleRowSelection = (position: number) => {
    setSelectedRows(prev =>
      prev.includes(position)
        ? prev.filter(p => p !== position)
        : [...prev, position]
    );
  };

  // Gestionnaire d'application de procédure
  const handleApplyProcedure = async (procedure: OrthodonticProcedure) => {
    if (onModificationChange && procedure.pathId) {
      try {
        await onModificationChange('1', procedure.pathId, true, {
          [`1-${procedure.pathId}`]: {
            fill: getColorForProcedure(procedure.type),
            stroke: '#6f42c1',
            strokeWidth: 1
          }
        });
        console.log(`✅ Procédure orthodontique appliquée: ${procedure.name}`);
      } catch (error) {
        console.error(`❌ Erreur application procédure: ${procedure.name}`, error);
      }
    }
  };

  // Obtenir la couleur selon le type de procédure
  const getColorForProcedure = (type: OrthodonticProcedure['type']): string => {
    switch (type) {
      case 'braces': return '#e6e6fa';
      case 'aligners': return '#f0e6ff';
      case 'retainer': return '#e6f3ff';
      case 'expander': return '#ffe6f3';
      default: return '#f8f9fa';
    }
  };

  // Obtenir le badge de statut
  const getStatusBadge = () => {
    if (type === 'completed') {
      return <Badge color="green" size="sm">Terminé</Badge>;
    } else if (type === 'planned') {
      return <Badge color="blue" size="sm">Planifié</Badge>;
    } else {
      return <Badge color="gray" size="sm">Disponible</Badge>;
    }
  };

  // Obtenir le badge de catégorie
  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      'fixe': 'purple',
      'amovible': 'blue',
      'contention': 'green',
      'expansion': 'orange',
      'maintenance': 'gray',
      'accessoire': 'yellow',
      'externe': 'red'
    };
    return <Badge color={colors[category] || 'gray'} variant="light" size="xs">{category}</Badge>;
  };

  const rows = filteredProcedures.map((procedure) => (
    <Table.Tr
      key={procedure.position}
      bg={selectedRows.includes(procedure.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(procedure.position)}
          onChange={() => handleRowSelection(procedure.position)}
        />
      </Table.Td>
      <Table.Td>
        <Text fw={500} size="sm">{procedure.symbol}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.name}</Text>
      </Table.Td>
      <Table.Td>
        {procedure.category && getCategoryBadge(procedure.category)}
      </Table.Td>
      <Table.Td>
        {getStatusBadge()}
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.cost}€</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.duration}min</Text>
      </Table.Td>
      <Table.Td>
        <Text size="xs" c="dimmed">
          {procedure.duration_months ? `${procedure.duration_months} mois` : 'Ponctuel'}
        </Text>
      </Table.Td>
      <Table.Td>
        <Text size="xs" c="dimmed">
          {procedure.adjustment_frequency ? `${procedure.adjustment_frequency} sem.` : 'N/A'}
        </Text>
      </Table.Td>
      <Table.Td>
        <Button
          size="xs"
          variant="light"
          color="violet"
          onClick={() => handleApplyProcedure(procedure)}
        >
          Appliquer
        </Button>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <div className="p-4">
      {/* Filtres et recherche */}
      <Group mb="md">
        <TextInput
          placeholder="Rechercher une procédure..."
          leftSection={<IconSearch size={16} />}
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.currentTarget.value)}
          style={{ flex: 1 }}
        />
        <Select
          placeholder="Catégorie"
          leftSection={<IconFilter size={16} />}
          data={[
            { value: 'all', label: 'Toutes' },
            { value: 'fixe', label: 'Appareils fixes' },
            { value: 'amovible', label: 'Appareils amovibles' },
            { value: 'contention', label: 'Contention' },
            { value: 'expansion', label: 'Expansion' },
            { value: 'maintenance', label: 'Maintenance' },
            { value: 'accessoire', label: 'Accessoires' },
            { value: 'externe', label: 'Appareils externes' }
          ]}
          value={filterCategory}
          onChange={(value) => setFilterCategory(value || 'all')}
          w={180}
        />
      </Group>

      {/* Statistiques */}
      <Group mb="md">
        <Text size="sm" c="dimmed">
          {filteredProcedures.length} procédure(s) • {selectedRows.length} sélectionnée(s)
        </Text>
        {selectedRows.length > 0 && (
          <Button size="xs" variant="light" color="violet">
            Appliquer la sélection
          </Button>
        )}
      </Group>

      {/* Tableau des procédures */}
      <Table striped highlightOnHover>
        <Table.Thead>
          <Table.Tr>
            <Table.Th />
            <Table.Th>Code</Table.Th>
            <Table.Th>Procédure</Table.Th>
            <Table.Th>Catégorie</Table.Th>
            <Table.Th>Statut</Table.Th>
            <Table.Th>Coût</Table.Th>
            <Table.Th>Durée</Table.Th>
            <Table.Th>Traitement</Table.Th>
            <Table.Th>Fréquence</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>

      {filteredProcedures.length === 0 && (
        <Text ta="center" c="dimmed" mt="xl">
          Aucune procédure trouvée
        </Text>
      )}
    </div>
  );
};

export default OrthodonticProcedures;
