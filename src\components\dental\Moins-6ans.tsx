"use client";

import { EstimatesTabs} from './EstimatesTabsMoins-6ans';
import { EstimatesTabsRef } from './EstimatesTabs';
import { useEstimateReactive } from '../../../hooks/useEstimateReactive';
import React, { useState, useEffect, useCallback,  useRef } from 'react';
import {
  Card,
  Title,
  Text,
  Group,
  Stack,
  Grid,
  Button,
  Loader,
  Select,
  Switch
} from '@mantine/core';
import {
  IconDental,
  IconRefresh,
  IconDatabase,
  IconStethoscope,
  IconScissors,
  IconBone,
  IconPalette
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { TOOTH_NAMES, getQuadrant, getToothType, isPermanentTooth } from '@/data/ToothMapping';

// Types pour les dents adultes (11-48)
interface AdultTooth {
  id?: string;
  tooth_number: number;
  name: string;
  tooth_type: 'incisor' | 'canine' | 'premolar' | 'molar' | 'wisdom';
  quadrant: 'upper_right' | 'upper_left' | 'lower_left' | 'lower_right';
  position: number;
  is_permanent: boolean;
  status: 'healthy' | 'treated' | 'needs_treatment' | 'missing';
  color?: string;
  is_visible: boolean;
  treatments: string[];
  notes?: string;
  priority: number;
  created_at?: string;
  updated_at?: string;
}

interface ToothTreatment {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  specialty: string;
}

// Traitements disponibles pour adultes
const ADULT_TREATMENTS: ToothTreatment[] = [
  { id: 'cleaning', name: 'Nettoyage', icon: <IconStethoscope size={16} />, color: 'blue', specialty: 'preventive' },
  { id: 'filling', name: 'Obturation', icon: <IconDental size={16} />, color: 'green', specialty: 'restorative' },
  { id: 'crown', name: 'Couronne', icon: <IconDental size={16} />, color: 'yellow', specialty: 'prosthetic' },
  { id: 'root_canal', name: 'Endodontie', icon: <IconStethoscope size={16} />, color: 'red', specialty: 'endodontic' },
  { id: 'extraction', name: 'Extraction', icon: <IconScissors size={16} />, color: 'red', specialty: 'surgery' },
  { id: 'implant', name: 'Implant', icon: <IconBone size={16} />, color: 'purple', specialty: 'surgery' },
  { id: 'whitening', name: 'Blanchiment', icon: <IconPalette size={16} />, color: 'cyan', specialty: 'esthetic' },
  { id: 'veneer', name: 'Facette', icon: <IconDental size={16} />, color: 'pink', specialty: 'esthetic' }
];

const Moins6ans = () => {
  const [teeth, setTeeth] = useState<AdultTooth[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTooth, setSelectedTooth] = useState<number | null>(null);

  const [filterQuadrant, setFilterQuadrant] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [showOnlyProblematic, setShowOnlyProblematic] = useState(false);

  // Générer les dents adultes (11-48)
  const generateAdultTeeth = (): AdultTooth[] => {
    const adultTeeth: AdultTooth[] = [];

    // Dents permanentes 11-48
    for (let i = 11; i <= 48; i++) {
      // Exclure les numéros non valides (19, 29, 39, 49)
      if (i % 10 === 9) continue;

      const toothName = TOOTH_NAMES[i] || `Dent ${i}`;
      const quadrant = getQuadrant(i);
      const toothType = getToothType(i);
      const position = i % 10;

      adultTeeth.push({
        tooth_number: i,
        name: toothName,
        tooth_type: toothType as 'incisor' | 'canine' | 'premolar' | 'molar' | 'wisdom',
        quadrant: quadrant,
        position: position,
        is_permanent: isPermanentTooth(i),
        status: 'healthy',
        color: '#FFFFFF',
        is_visible: true,
        treatments: [],
        priority: 1,
        notes: ''
      });
    }

    return adultTeeth;
  };

  // Charger les données des dents
  const loadTeethData = useCallback(async () => {
    setLoading(true);
    try {
      // Simuler un appel API - remplacer par un vrai appel quand le backend sera prêt
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Pour l'instant, générer les données localement
      const adultTeeth = generateAdultTeeth();
      setTeeth(adultTeeth);
    } catch (error) {
      console.error('Erreur lors du chargement des dents:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de charger les données des dents',
        color: 'red'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadTeethData();
  }, [loadTeethData]);

  // Filtrer les dents selon les critères
  const filteredTeeth = teeth.filter(tooth => {
    if (filterQuadrant !== 'all' && tooth.quadrant !== filterQuadrant) return false;
    if (filterType !== 'all' && tooth.tooth_type !== filterType) return false;
    if (showOnlyProblematic && tooth.status === 'healthy') return false;
    return true;
  });

// Référence pour déclencher la sauvegarde des onglets
  const estimatesTabsRef = useRef<EstimatesTabsRef | null>(null);
 const {
    session,
    isLoading,

    saveModification,


  } = useEstimateReactive({
    patientId: 'patient-123', // TODO: Récupérer l'ID du patient actuel
    sessionName: 'Session Devis',
    autoSave: true,
    autoSaveDelay: 2000,
    autoInitialize: false, // NOUVEAU: Désactiver l'initialisation automatique
  });


  // Mettre à jour une dent
  const updateTooth = (toothNumber: number, updates: Partial<AdultTooth>) => {
    setTeeth(prev => prev.map(tooth =>
      tooth.tooth_number === toothNumber
        ? { ...tooth, ...updates, updated_at: new Date().toISOString() }
        : tooth
    ));
  };

  // Ajouter un traitement
  const addTreatment = (toothNumber: number, treatmentId: string) => {
    const tooth = teeth.find(t => t.tooth_number === toothNumber);
    if (tooth && !tooth.treatments.includes(treatmentId)) {
      updateTooth(toothNumber, {
        treatments: [...tooth.treatments, treatmentId],
        status: 'treated'
      });

      notifications.show({
        title: 'Traitement ajouté',
        message: `Traitement ajouté à la dent ${toothNumber}`,
        color: 'green'
      });
    }
  };

  // Supprimer un traitement
  const removeTreatment = (toothNumber: number, treatmentId: string) => {
    const tooth = teeth.find(t => t.tooth_number === toothNumber);
    if (tooth) {
      const newTreatments = tooth.treatments.filter(t => t !== treatmentId);
      updateTooth(toothNumber, {
        treatments: newTreatments,
        status: newTreatments.length > 0 ? 'treated' : 'healthy'
      });
    }
  };

  if (loading) {
    return (
      <Card withBorder p="xl">
        <Group justify="center">
          <Loader size="lg" />
          <Text>Chargement des dents adultes...</Text>
        </Group>
      </Card>
    );
  }

  return (
    <Stack gap="lg">
      {/* En-tête */}
      <Card withBorder p="lg">
        <Group justify="space-between" align="center">
          <Group gap="sm">
            <IconDental size={32} color="blue" />
            <div>
              <Title order={2}>Dents Adultes (11-48)</Title>
              <Text c="dimmed">Gestion des dents permanentes adultes</Text>
            </div>
          </Group>

          <Group gap="sm">
            <Button
              variant="light"
              leftSection={<IconRefresh size={16} />}
              onClick={loadTeethData}
              loading={loading}
            >
              Actualiser
            </Button>
            <Button
              variant="light"
              leftSection={<IconDatabase size={16} />}
              onClick={() => window.open('http://127.0.0.1:8000/admin/dentistry/tooth/', '_blank')}
            >
              Admin Backend
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Statistiques */}
      <Grid>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md" ta="center">
            <Text size="xl" fw={700} c="blue">
              {teeth.length}
            </Text>
            <Text size="sm" c="dimmed">Total Dents</Text>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md" ta="center">
            <Text size="xl" fw={700} c="green">
              {teeth.filter(t => t.status === 'healthy').length}
            </Text>
            <Text size="sm" c="dimmed">Saines</Text>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md" ta="center">
            <Text size="xl" fw={700} c="blue">
              {teeth.filter(t => t.status === 'treated').length}
            </Text>
            <Text size="sm" c="dimmed">Traitées</Text>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md" ta="center">
            <Text size="xl" fw={700} c="orange">
              {teeth.filter(t => t.status === 'needs_treatment').length}
            </Text>
            <Text size="sm" c="dimmed">À Traiter</Text>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Filtres */}
      <Card withBorder p="md">
        <Group justify="space-between" align="center" mb="md">
          <Title order={4}>Filtres</Title>
          <Switch
            label="Problématiques uniquement"
            checked={showOnlyProblematic}
            onChange={(event) => setShowOnlyProblematic(event.currentTarget.checked)}
          />
        </Group>

        <Group gap="md">
          <Select
            label="Quadrant"
            value={filterQuadrant}
            onChange={(value) => setFilterQuadrant(value || 'all')}
            data={[
              { value: 'all', label: 'Tous les quadrants' },
              { value: 'upper_right', label: 'Supérieur droit' },
              { value: 'upper_left', label: 'Supérieur gauche' },
              { value: 'lower_left', label: 'Inférieur gauche' },
              { value: 'lower_right', label: 'Inférieur droit' }
            ]}
            w={200}
          />

          <Select
            label="Type de dent"
            value={filterType}
            onChange={(value) => setFilterType(value || 'all')}
            data={[
              { value: 'all', label: 'Tous les types' },
              { value: 'incisor', label: 'Incisives' },
              { value: 'canine', label: 'Canines' },
              { value: 'premolar', label: 'Prémolaires' },
              { value: 'molar', label: 'Molaires' },
              { value: 'wisdom', label: 'Sagesse' }
            ]}
            w={200}
          />
        </Group>
      </Card>

      {/* Graphique dentaire SVG */}
      <Card withBorder p="lg">
        <Title order={3} mb="md">Graphique Dentaire Adulte</Title>
        <EstimatesTabs
          ref={estimatesTabsRef}
          onModificationChange={saveModification}
          session={session}
          isLoading={isLoading}
        />
        <DentalChartSVG
          teeth={filteredTeeth}
          selectedTooth={selectedTooth}
          onToothSelect={setSelectedTooth}
          onToothUpdate={updateTooth}
          onTreatmentAdd={addTreatment}
          onTreatmentRemove={removeTreatment}
          treatments={ADULT_TREATMENTS}
        />

      </Card>
    </Stack>
  );
};

// Composant pour le graphique dentaire SVG
interface DentalChartSVGProps {
  teeth: AdultTooth[];
  selectedTooth: number | null;
  onToothSelect: (toothNumber: number) => void;
  onToothUpdate: (toothNumber: number, updates: Partial<AdultTooth>) => void;
  onTreatmentAdd: (toothNumber: number, treatmentId: string) => void;
  onTreatmentRemove: (toothNumber: number, treatmentId: string) => void;
  treatments: ToothTreatment[];
}

const DentalChartSVG: React.FC<DentalChartSVGProps> = ({
  teeth,
  selectedTooth,
  onToothSelect,

  onTreatmentAdd,

  treatments
}) => {
  // Organiser les dents par quadrant
  const upperRight = teeth.filter(t => t.quadrant === 'upper_right').sort((a, b) => a.tooth_number - b.tooth_number);
  const upperLeft = teeth.filter(t => t.quadrant === 'upper_left').sort((a, b) => a.tooth_number - b.tooth_number);
  const lowerLeft = teeth.filter(t => t.quadrant === 'lower_left').sort((a, b) => a.tooth_number - b.tooth_number);
  const lowerRight = teeth.filter(t => t.quadrant === 'lower_right').sort((a, b) => a.tooth_number - b.tooth_number);

  // Fonction pour obtenir la position d'une dent dans le graphique
  const getToothPosition = (tooth: AdultTooth, index: number, quadrant: string) => {
    const toothSpacing = 60;
    const centerX = 400;
    const upperY = 120;
    const lowerY = 280;

    switch (quadrant) {
      case 'upper_right':
        // Dents 11-18 (de droite à gauche depuis le centre)
        return { x: centerX + 10 + (index * toothSpacing), y: upperY };
      case 'upper_left':
        // Dents 21-28 (de gauche à droite depuis le centre)
        return { x: centerX - 10 - ((index + 1) * toothSpacing), y: upperY };
      case 'lower_left':
        // Dents 31-38 (de gauche à droite depuis le centre)
        return { x: centerX - 10 - ((index + 1) * toothSpacing), y: lowerY };
      case 'lower_right':
        // Dents 41-48 (de droite à gauche depuis le centre)
        return { x: centerX + 10 + (index * toothSpacing), y: lowerY };
      default:
        return { x: centerX, y: upperY };
    }
  };

  // Fonction pour obtenir la couleur d'une dent selon son statut
  const getToothColor = (tooth: AdultTooth) => {
    if (tooth.color && tooth.color !== '#FFFFFF') return tooth.color;

    switch (tooth.status) {
      case 'healthy': return '#FFFFFF';
      case 'treated': return '#E3F2FD';
      case 'needs_treatment': return '#FFF3E0';
      case 'missing': return '#FFEBEE';
      default: return '#FFFFFF';
    }
  };

  return (
    <div style={{ width: '100%', height: '400px', overflow: 'auto' }}>
      <svg width="800" height="400" viewBox="0 0 800 400">
        {/* Ligne centrale verticale */}
        <line x1="400" y1="50" x2="400" y2="350" stroke="#E0E0E0" strokeWidth="1" strokeDasharray="3,3" />

        {/* Ligne horizontale séparant mâchoires */}
        <line x1="50" y1="200" x2="750" y2="200" stroke="#E0E0E0" strokeWidth="1" strokeDasharray="3,3" />

        {/* Dents du quadrant supérieur droit */}
        {upperRight.map((tooth, index) => {
          const pos = getToothPosition(tooth, index, 'upper_right');
          const isSelected = selectedTooth === tooth.tooth_number;

          return (
            <g key={tooth.tooth_number}>
              <rect
                x={pos.x - 25}
                y={pos.y - 30}
                width="50"
                height="60"
                fill={getToothColor(tooth)}
                stroke={isSelected ? "#2196F3" : "#000"}
                strokeWidth={isSelected ? "2" : "1"}
                rx="2"
                style={{ cursor: 'pointer' }}
                onClick={() => onToothSelect(tooth.tooth_number)}
              />
              <text
                x={pos.x}
                y={pos.y}
                textAnchor="middle"
                fontSize="12"
                fontWeight="bold"
                fill="#333"
                dominantBaseline="middle"
              >
                {tooth.tooth_number}
              </text>
              {/* Indicateur de traitements */}
              {tooth.treatments.length > 0 && (
                <circle
                  cx={pos.x + 20}
                  cy={pos.y - 25}
                  r="6"
                  fill="#2196F3"
                />
              )}
              {tooth.treatments.length > 0 && (
                <text
                  x={pos.x + 20}
                  y={pos.y - 25}
                  textAnchor="middle"
                  fontSize="8"
                  fill="white"
                  fontWeight="bold"
                  dominantBaseline="middle"
                >
                  {tooth.treatments.length}
                </text>
              )}
            </g>
          );
        })}

        {/* Dents du quadrant supérieur gauche */}
        {upperLeft.map((tooth, index) => {
          const pos = getToothPosition(tooth, index, 'upper_left');
          const isSelected = selectedTooth === tooth.tooth_number;

          return (
            <g key={tooth.tooth_number}>
              <rect
                x={pos.x - 25}
                y={pos.y - 30}
                width="50"
                height="60"
                fill={getToothColor(tooth)}
                stroke={isSelected ? "#2196F3" : "#000"}
                strokeWidth={isSelected ? "2" : "1"}
                rx="2"
                style={{ cursor: 'pointer' }}
                onClick={() => onToothSelect(tooth.tooth_number)}
              />
              <text
                x={pos.x}
                y={pos.y}
                textAnchor="middle"
                fontSize="12"
                fontWeight="bold"
                fill="#333"
                dominantBaseline="middle"
              >
                {tooth.tooth_number}
              </text>
              {tooth.treatments.length > 0 && (
                <circle cx={pos.x + 20} cy={pos.y - 25} r="6" fill="#2196F3" />
              )}
              {tooth.treatments.length > 0 && (
                <text x={pos.x + 20} y={pos.y - 25} textAnchor="middle" fontSize="8" fill="white" fontWeight="bold" dominantBaseline="middle">
                  {tooth.treatments.length}
                </text>
              )}
            </g>
          );
        })}

        {/* Dents du quadrant inférieur gauche */}
        {lowerLeft.map((tooth, index) => {
          const pos = getToothPosition(tooth, index, 'lower_left');
          const isSelected = selectedTooth === tooth.tooth_number;

          return (
            <g key={tooth.tooth_number}>
              <rect
                x={pos.x - 25}
                y={pos.y - 30}
                width="50"
                height="60"
                fill={getToothColor(tooth)}
                stroke={isSelected ? "#2196F3" : "#000"}
                strokeWidth={isSelected ? "2" : "1"}
                rx="2"
                style={{ cursor: 'pointer' }}
                onClick={() => onToothSelect(tooth.tooth_number)}
              />
              <text
                x={pos.x}
                y={pos.y}
                textAnchor="middle"
                fontSize="12"
                fontWeight="bold"
                fill="#333"
                dominantBaseline="middle"
              >
                {tooth.tooth_number}
              </text>
              {tooth.treatments.length > 0 && (
                <circle cx={pos.x + 20} cy={pos.y - 25} r="6" fill="#2196F3" />
              )}
              {tooth.treatments.length > 0 && (
                <text x={pos.x + 20} y={pos.y - 25} textAnchor="middle" fontSize="8" fill="white" fontWeight="bold" dominantBaseline="middle">
                  {tooth.treatments.length}
                </text>
              )}
            </g>
          );
        })}

        {/* Dents du quadrant inférieur droit */}
        {lowerRight.map((tooth, index) => {
          const pos = getToothPosition(tooth, index, 'lower_right');
          const isSelected = selectedTooth === tooth.tooth_number;

          return (
            <g key={tooth.tooth_number}>
              <rect
                x={pos.x - 25}
                y={pos.y - 30}
                width="50"
                height="60"
                fill={getToothColor(tooth)}
                stroke={isSelected ? "#2196F3" : "#000"}
                strokeWidth={isSelected ? "2" : "1"}
                rx="2"
                style={{ cursor: 'pointer' }}
                onClick={() => onToothSelect(tooth.tooth_number)}
              />
              <text
                x={pos.x}
                y={pos.y}
                textAnchor="middle"
                fontSize="12"
                fontWeight="bold"
                fill="#333"
                dominantBaseline="middle"
              >
                {tooth.tooth_number}
              </text>
              {tooth.treatments.length > 0 && (
                <circle cx={pos.x + 20} cy={pos.y - 25} r="6" fill="#2196F3" />
              )}
              {tooth.treatments.length > 0 && (
                <text x={pos.x + 20} y={pos.y - 25} textAnchor="middle" fontSize="8" fill="white" fontWeight="bold" dominantBaseline="middle">
                  {tooth.treatments.length}
                </text>
              )}
            </g>
          );
        })}
      </svg>

      {/* Panneau de contrôle pour la dent sélectionnée */}
      {selectedTooth && (
        <Card withBorder mt="md" p="md">
          <Group justify="space-between" mb="md">
            <Title order={4}>Dent {selectedTooth}</Title>
            <Button variant="light" size="xs" onClick={() => onToothSelect(-1)}>
              Fermer
            </Button>
          </Group>

          <Group gap="sm">
            {treatments.slice(0, 6).map((treatment) => (
              <Button
                key={treatment.id}
                variant="light"
                size="xs"
                color={treatment.color}
                onClick={() => onTreatmentAdd(selectedTooth, treatment.id)}
              >
                {treatment.name}
              </Button>
            ))}
          </Group>
        </Card>
      )}
    </div>
  );
};



export default Moins6ans;