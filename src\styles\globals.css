@import "tailwindcss";
:root {
  color-scheme: var(--mantine-color-scheme);         
  --mantine-color-dark-6: #252b32;
  --leftbar-width: 240px;
  --mantine-color-blue-0: #e7f5ff;
  
}
:root[data-mantine-color-scheme='light'] {
  --mantine-color-scheme: light;
  /* --mantine-color-body: #F2F7FC ; #f2f5f8  */
  --mantine-color-body: var(--mantine-color-white);
  --bg-body:#EFF2F4 ;/*#f2f5f8*/
  --mantine-color-text:#000;
  --mantine-colorgray: #f2f5f8;/* #F8F9FA */
  --border-color: #E5E7EB;
  --mantine-color-dark-4: #E5E7EB;
  --bg-SwitchColor: #e6e9ec;
  --mantine-color-indigo-6: #F2F5F8;/* border click color */
  --mantine-bg-hover: #E9ECEF;/* bg hover  Vous avez déjà un compte rgb(209 211 213)?*/
  --mantine-color-indigo-light-hover: #3799CE;/**/
  --border-base:var(--mantine-color-gray-4);
  
  /* --mantine-color-dark-0: #3799CE; */
  --mantine-color-dark-0: #1d232a;
  --bg-toggleMenu: #f2f5f8;
  --tooltip-bg: #E9ECEF;
  --tooltip-text: #170000;
  --mantine-color-dimmed: var(--mantine-color-gray-6);
  --bg-toggleMenu: #f2f5f8;
  --content-background: #ffffff;
  --mantine-color-bg: #f2f5f8;
  --bg-Button: "#E6E9EC";
  --bg-Button-hover: "#BEC2C5";
  --text-color-Button: "#191E23";
  --header-nav-base: #e6e9ec;
  --text-nav-base: #797c7f;
  --mantine-datatable-row: #e6e9ec;
  --bg-white: #fff;
  --text-daisy: #1d232a;
  --mantine-Button-label-MB: #ffffff;
  --header-nav-base: #e6e9ec;
  --text-nav-base: #797c7f;
  --mantine-datatable-row: #e6e9ec;
  --header-nav-base: #e6e9ec;
  --bg-Table-tr: #ffffff;
  --mantine-bg-color: #e6e9ec;
  ---mantinelight-hover:#E6E9EC;
  --logo:url("/logo-light.svg");
  --premium-bg: #e6e9ec;
  --blue-color: #3799ce ;
  --text-daisy-white:#ffffff;
  --rbc-border: #f2f4f5;
  --color-gray-tab: #e6e9ec;
  --text-tab: #797c7f;
  --bg-nav-hover:#EFF2F4 ;/*#f2f5f8*/
}

:root[data-mantine-color-scheme='dark'] {
  --mantine-color-scheme: dark;
  /* --mantine-color-body: #14181c; */
  --mantine-color-body: #191e23;
  --bg-body: #14181c;
  --mantine-color-text:#DCEBFA;
  --mantine-colorgray:#14181C;
  --border-color: #252b32;
  /* --border-color: #25292d; */
  --mantine-color-dark-4: #252b32;
  --bg-SwitchColor: #252b32;
  --mantine-color-dark-0: #dcebfa;
  --mantine-color-indigo-8: #14181C;/* border click color */
  --mantine-bg-hover: #252B32;/* border click color */
  --mantine-color-indigo-light-hover: #4a515a;
  --mantine-color-indigo-light-color: #dcebfa;
  --bg-toggleMenu: #191e23;
  --tooltip-bg: #4a515a;
  --tooltip-text: #ffffff;
  --mantine-color-gray-3: #3799CE; /*icon inbut color*/
  --mantine-color-dark-Menu-item-hover:#14181C; 
  --mantine-color-dimmed:#8ea9c2;
  --bg-toggleMenu: #191e23;
  --content-background: #191e23;
  --mantine-color-bg: #14181c;
  --bg-Button-hover: "#4A515A";
  --bg-Button: "#252B32";
  --text-color-Button: "#DCEBFA";
  --header-nav-base: #14181c;
  --text-nav-base: #dcebfa;
  --mantine-datatable-row: #252b32;
  --bg-white: #09090b;
  --text-daisy: #dcebfa;
  --mantine-Button-label-MB: #dcebfa;
  --header-nav-base: #14181c;
  --text-nav-base: #dcebfa;
  --mantine-datatable-row: #252b32;
  --header-nav-base: #14181c;
  --bg-Table-tr: #191e23;
  --mantine-bg-color: #252b32;
  ---mantinelight-hover:#4A515A;
  --logo:url("/logo-dark.svg");
  --premium-bg: #252b32;
  --blue-color:#3799ce ;
  --text-daisy-white:#dcebfa;
  --rbc-border: #252b32;
  --color-gray-tab: #252b32;
  --text-tab: #dcebfa;
  --border-base:var(--mantine-color-gray-8);
  --bg-nav-hover:#EFF2F4 ;/*#f2f5f8*/
}

/* Start-scrollbar */
.simplebar-scrollbar:before {
  position: absolute;
  content: "";
  background: #3799ce !important;
  border-radius: 7px;
  left: 2px;
  right: 2px;
  opacity: 0;
  transition: opacity 0.2s 0.5s linear;
}
.simplebar-scrollbar.simplebar-visible:before {
  opacity: 1 !important;
  transition-delay: 0s;
  transition-duration: 0s;
}

/* End-scrollbar */

html {
  -webkit-tap-highlight-color: transparent;
}

body {
  overflow: hidden;
  /* background-color: var(--mantine-color-body); */
  background-color: var(--bg-body);
  color:var(--mantine-color-text);
}
.leftmenu-wrapper {
  width: var(--leftbar-width);
  transition: all 0.3s;
  position: sticky;
  bottom: 0;
  top: 0;
  /* height: 100vh; */
  height: max-content;
  min-width: 15rem;
  --tw-border-opacity: 1;
  background-color: var(--content-background);
}
.leftmenu-wrapper.hide {
  margin-inline-start: calc(var(--leftbar-width) * -1);
}

.main-wrapper {
  width: 100%;
  max-width: 100vw;
  /* height: 100vh; */
}

.content-wrapper {
  flex-grow: 1;
  background-color: var(--mantine-color-bg);
   padding: 0rem 0.4rem; 
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}

/* Start-ScrollTop */
.go-to {
  bottom: 25px;
  right: 25px;
  position: fixed;
  cursor: pointer;
  border: none;

  display: inline-flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  background-color: rgba(113, 134, 157, 0.1);
  color: #677788;
  font-size: 1rem;
  opacity: 1;
  border-radius: 50%;
  transition: 0.3s ease-out;
  -moz-border-radius: 55px;
  -ms-border-radius: 55px;
  -o-border-radius: 55px;
  width: 40px;
  height: 40px;

  border-radius: 50%;
  transition: 0.3s ease-out;
}

.go-to:focus:hover,
.go-to:hover {
  color: #fff;
  background-color: #03a684;
  opacity: 1;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}
.fadeInUp {
  animation-name: fadeInUp;
}
.bgTab {
  background-color: var(--bgTab);
  padding: 8px;
  border-radius: var(--rounded-box, 1rem);
}
/* End-ScrollTop */
.LeftMenu_linksInner__eUr0V {
  padding-top: 0px !important;
  padding-bottom: var(--mantine-spacing-xl);
}
.logo {
  background-image: var(--logo);
  background-repeat: no-repeat;
}

.logo-success {
  opacity: 1;
  color: var(--green);
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
}
.logo-primary {
  opacity: 1;
  color: var(--blue);
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
}

@media (min-width: 768px) {
  .visibleui {
    display: none;
  }
}
/* #D9DCDE */
.border-b {
  border-color: var(--border-color);
  border-bottom-width: 1px;
}
.border-r {
  border-color: var(--border-color);
  border-bottom-width: 1px;
}
.border-base-200 {
  border-color: var(--border-color);
  border-bottom-width: 1px;
}
.mantine-focus-always:focus {
  outline: none;
}
.bg-Button {
  background-color: var(--bg-Button);
}
.bg-Button-hover {
  background-color: var(--bg-Button-hover);
}
.text-color-Button {
  color: var(--text-color-Button);
}
.header-nav-base {
  background-color: var(--header-nav-base);
  height: 40px;
  padding: 4px;
  color: var(--text-nav-base);
  border: 1px solid var(--border-color);
  border-radius: 5px;
}
table :where(thead, tfoot) {
  white-space: nowrap;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 700;
  color: var(--fallback-bc, oklch(var(--bc) / 0.6));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  color: #797c7f;
}
table :where(thead tr, tbody tr, footer tr) {
  font-size: 0.75rem;
  line-height: 1rem;
}
.mantine-datatable-row:hover {
  background-color: var(--mantine-datatable-row);
}
table
  :where(thead tr, tbody tr:not(:last-child), tbody tr:first-child:last-child) {
  font-size: 0.75rem;
  line-height: 1rem;
}
.m_4e7aa4ef,
.m_4e7aa4f3 {
  padding: 5px 2px;
  text-align: center;
}
table tr .m_4081bf90 {
  display: flex;
  flex-direction: row;
  flex-wrap: var(--group-wrap, wrap);
  justify-content: var(--group-justify, flex-start);
  align-items: var(--group-align, center);
  gap: 0.225rem;
}
.disk-orange {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(241, 113, 5);
}
.disk-purple {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(102, 101, 221);
}
.disk-teal {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(52, 209, 191);
}
.disk-azure {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(4, 150, 255);
}
.disk-red {
  background-color: var(--bg-white);
  border: 3.5px solid rgb(237, 4, 35);
}
.cls-2,
.cls-3,
.cls-4,
.cls-5,
.cls-7 {
  stroke: #c2c2c2;
  stroke-miterlimit: 10;
}
.cls-5 {
  fill: none;
}
.cls-1 {
  fill: #e8e8e8;
}
.cls-1,
.cls-2,
.cls-3 {
  stroke: #c2c2c2;
  stroke-miterlimit: 10;
}
.text-daisy {
  color: var(--text-daisy);
}
.mantine-Button-label-M {
  font-size: 13px !important;
  line-height: 19.5px !important;
  color: var(--text-daisy);
  letter-spacing: 0.05em;
  font-weight: 500 !important;
}
.mantine-Button-label-MB {
  font-size: 14px !important;
  line-height: 14px !important;
  color: var(--mantine-Button-label-MB);
  letter-spacing: 0.05em;
}

.tabs-boxed {
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: var(--header-nav-base);
  padding: 0.25rem;
}

.tab-active {
  background-color: #3799ce;
  color: #fff;
}
.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}
.mb-0 {
  margin-bottom: 0px !important;
}
.mantine-Table-td {
  text-align: left;
  padding-left: 16px;
}
.mantine-Table-tr:hover {
  background-color: var(--bg-Table-tr);
  color: #2486c8;
}
.mantine-Table-th {
  background-color: var(--bg-Table-tr);
  color: #2486c8;
}
.mantine-Table-th:hover {
  background-color: var(--bg-Table-tr);
  color: #2486c8;
}
.mantine-Select-input {
  line-height: 1.25rem !important;
}
.hiddens {
  display: none !important; /* Use !important to override other styles */
}
/* This modification is for the Treatments page and changes the background text at the hover div parent. */
.parent-div:hover .child-div {
  background-color: #15AABF ;/*#3799CE*/
  color: var(--mantine-color-blue-0);
}

:where([data-mantine-color-scheme='light']) .m_99ac2aa1:where([data-hovered]):where(:not(:disabled, [data-disabled])) {
  background-color: var(--menu-item-hover, var(--mantine-color-gray-1));
}

:where([data-mantine-color-scheme='dark']) .m_99ac2aa1:where([data-hovered]):where(:not(:disabled, [data-disabled])) {
  background-color: var(--menu-item-hover, var(--mantine-color-dark-Menu-item-hover));
}

.borderleft {
  border-left: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)) !important;
}
.active-class{
  background-color: #3799CE;
  color: var(--text-daisy-white) !important ;
}
.active-class:hover{
  background-color: #3799CE;
  color: var(--text-daisy-white) !important ;
}
/* NavLinkfontSize */
  .m_e17b862f {
    padding-inline-start: 0px;
  }
 
  @media (max-width: 1023px) { /* sm and md */
    .hide-sm-md {
      display: none;
    }
  }

  
  .visibleFrom_md {
    display: none;
  }
  
  @media (min-width: 768px) and (max-width: 1023px) { /* md range */
    .visibleFrom_md {
      display: flex; /* or block, depending on your layout */
    }
  }

  @media (max-width: 768px)  {
    .hidden_sm {
      display: none;
    }
  } 
  .mantine-TextInput-label{
    margin-bottom: 4px;
  }
  @media (max-width: 768px)  {
    .m_4081bf90 {
      background-color:var(--content-background);
      border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)); 
    }
  } 
  .rbc-time-slot .rbc-label {
    display: flex;
    flex-direction: column;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 0.625rem;
    font-family: Rubik, sans-serif;
    height: 20px;
    width: 47px;
    margin: 0px auto;
    border-radius: 8px;
    position: relative;
    /* top: -10px; */
    top: 2.5px !important; 
    background-color: var(--content-background);
  }
  

 
.rbc-day-slot .rbc-events-container {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  margin-right: 0px !important;
  top: 0;


}
.rbc-event-label{
  display: none  !important;
}
.rbc-event-content{
  text-transform: uppercase !important;
}

.rbc-event{
   border-right: 4px solid #37BFCE !important; 
  border-radius: 0 !important;
  border-top-right-radius: 2px; /* 0.125rem (2px) */
   border-bottom-right-radius: 2px;
   /* text-align: center !important;
   align-items: 'center' !important; */
}

.disabled {
  color: var(--mantine-color-gray-5);
  background: var(--mantine-color-gray-1);
}
.disabled:hover {
  color: var(--mantine-color-gray-5);
  background: var(--mantine-color-gray-1);
  
}
.css-19h8l3b-control {
  -webkit-tap-highlight-color: transparent;
  appearance: none;
  resize: var(--input-resize, none);
  display: block;
  width: 100%;
  transition: border-color 100msease;
  text-align: var(--input-text-align);
  color: var(--input-color);
  border: calc(0.0625rem* var(--mantine-scale)) solid var(--input-bd);
  background-color: var(--input-bg);
  font-family: var(--input-font-family, var(--mantine-font-family));
  height: var(--input-size);
  min-height: var(--input-height);
  line-height: var(--input-line-height);
  font-size: var(--input-fz, var(--input-fz, var(--mantine-font-size-sm)));
  border-radius: var(--input-radius);
  padding-inline-start: var(--input-padding-inline-start);
  padding-inline-end: var(--input-padding-inline-end);
  padding-top: var(--input-padding-y, 0rem);
  padding-bottom: var(--input-padding-y, 0rem);
  cursor: var(--input-cursor);
  overflow: var(--input-overflow);
}
.css-19h8l3b-control:hover{border-color:#9BCCE7 !important; }
.css-5vgdwv-control:focus {
  background-color: lightblue;
}
.mantine-datatable-table thead {
  border-bottom: 3px solid #3799CE !important;
}
.mantine-datatable-table[data-striped] tbody tr:nth-of-type(2n+1) {
  background: var(--mantine-datatable-striped-color);
}

/* --------------------------- */
.m_b59ab47c:where([data-active]) {
  border-top-color: var(--tab-border-top-color);
  border-bottom-color: var(--tab-border-bottom-color);
  border-inline-start-color: var(--tab-border-inline-start-color);
  border-inline-end-color: var(--tab-border-inline-end-color);
  --tab-border-top-color: var(--tab-border-color);
  --tab-border-inline-start-color: var(--tab-border-color);
  --tab-border-inline-end-color: var(--tab-border-color);
  --tab-border-bottom-color: var(--mantine-color-body);
  background-color: white;
}
.Tabs-Panel{
  background-color: white;
  border-top-color: white;
  border-bottom-color: #dee2e6;
  border-left-color: #dee2e6;
  border-right-color: #dee2e6;
  border-width: 1px;
  height: auto;
  padding: 10px;
  
  /* margin-left: 2px; */
}
.navBarButtonicon:hover {
  background-color: var(--bg-body);
  color: var(--text-daisy-white); 
 
}
.ButtonHover{
  background-color:#3799ce !important
}
.ButtonHover:hover{
  background-color:#15aabf !important
}
.HoverButton{
  background-color:#15aabf !important
}
.HoverButton:hover{
  background-color: #3799ce !important
}
.border-base-300{
border:calc(0.0625rem * var(--mantine-scale)) solid var(--border-base);
}
/*Modal.CloseButton for Add Patient Modal*/
.m_606cb269:hover{color: var(--mantine-color-gray-7) !important;}

.m_99ac2aa1{padding: 4px;}
.btn-Enr{
  background-color: #03A684;
  color: var(--mantine-Button-label-MB);
}
.btn-Enr:hover{
  background-color: #03A684 ;
   opacity: 0.9;
}
.btn-Supp{
  background-color: #F3124E;
  color: var(--mantine-Button-label-MB);
}
.btn-Supp:hover{
  background-color: #F3124E ;
   opacity: 0.9;
}
.btn-Ann{
  background-color: #F5A524;
  color: var(--mantine-Button-label-MB);
}
.btn-Ann:hover{
  background-color: #F5A524;
  opacity: 0.9;
  
}
.Icon_Delete{

  color: rgb(55, 153, 206)

}
.Icon_Delete:hover{
 color: #ED0423;
}

.NoHoverBtn:hover{
 background-color: #F8F9FA;

}