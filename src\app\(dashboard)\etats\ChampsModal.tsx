'use client';
import React, { useState } from 'react';
import {
  Modal,
  Title,
  Group,
  Button,
  Text,
  Card,
  Box,
  Stack,
  ScrollArea,
  ActionIcon,
  Divider,
} from '@mantine/core';
import {
  IconPlus,
  IconGripVertical,
  IconX,
} from '@tabler/icons-react';

interface ChampsModalProps {
  opened: boolean;
  onClose: () => void;
}

interface ChampItem {
  id: string;
  nom: string;
  type: 'dimension' | 'mesure';
}

const ChampsModal: React.FC<ChampsModalProps> = ({ opened, onClose }) => {
  // États pour les différentes sections
  const [tousLesChamps] = useState<ChampItem[]>([
    { id: '1', nom: 'Date de création', type: 'dimension' },
    { id: '2', nom: 'Nom du patient', type: 'dimension' },
    { id: '3', nom: 'Montant total', type: 'mesure' },
    { id: '4', nom: 'Statut', type: 'dimension' },
    { id: '5', nom: 'Médecin', type: 'dimension' },
    { id: '6', nom: 'Type de traitement', type: 'dimension' },
  ]);

  const [filtresRapport, setFiltresRapport] = useState<ChampItem[]>([]);
  const [colonnes, setColonnes] = useState<ChampItem[]>([]);
  const [lignes, setLignes] = useState<ChampItem[]>([]);
  const [valeurs, setValeurs] = useState<ChampItem[]>([]);

  const handleDragStart = (e: React.DragEvent, champ: ChampItem) => {
    e.dataTransfer.setData('application/json', JSON.stringify(champ));
  };

  const handleDrop = (e: React.DragEvent, section: string) => {
    e.preventDefault();
    const champData = JSON.parse(e.dataTransfer.getData('application/json'));
    
    switch (section) {
      case 'filtres':
        setFiltresRapport(prev => [...prev, champData]);
        break;
      case 'colonnes':
        setColonnes(prev => [...prev, champData]);
        break;
      case 'lignes':
        setLignes(prev => [...prev, champData]);
        break;
      case 'valeurs':
        setValeurs(prev => [...prev, champData]);
        break;
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const removeChamp = (id: string, section: string) => {
    switch (section) {
      case 'filtres':
        setFiltresRapport(prev => prev.filter(item => item.id !== id));
        break;
      case 'colonnes':
        setColonnes(prev => prev.filter(item => item.id !== id));
        break;
      case 'lignes':
        setLignes(prev => prev.filter(item => item.id !== id));
        break;
      case 'valeurs':
        setValeurs(prev => prev.filter(item => item.id !== id));
        break;
    }
  };

  const ChampCard = ({ champ, section, showRemove = false }: { 
    champ: ChampItem; 
    section?: string; 
    showRemove?: boolean;
  }) => (
    <Card
      p="xs"
      withBorder
      className="cursor-move bg-white hover:bg-gray-50 border-gray-300"
      draggable
      onDragStart={(e) => handleDragStart(e, champ)}
    >
      <Group justify="space-between" align="center" gap="xs">
        <Group align="center" gap="xs">
          <IconGripVertical size={14} className="text-gray-400" />
          <Text size="sm" className="text-gray-700">
            {champ.nom}
          </Text>
        </Group>
        {showRemove && section && (
          <ActionIcon
            size="xs"
            variant="subtle"
            color="red"
            onClick={() => removeChamp(champ.id, section)}
          >
            <IconX size={12} />
          </ActionIcon>
        )}
      </Group>
    </Card>
  );

  const DropZone = ({ 
    title, 
    section, 
    items, 
    placeholder 
  }: { 
    title: string; 
    section: string; 
    items: ChampItem[]; 
    placeholder: string;
  }) => (
    <Card
      p="md"
      withBorder
      className="h-48 bg-gray-50 border-2 border-dashed border-gray-300"
      onDrop={(e) => handleDrop(e, section)}
      onDragOver={handleDragOver}
    >
      <Text size="sm" fw={500} className="text-gray-700 mb-2">
        {title}
      </Text>
      <ScrollArea h={160}>
        <Stack gap="xs">
          {items.length > 0 ? (
            items.map((item) => (
              <ChampCard 
                key={item.id} 
                champ={item} 
                section={section}
                showRemove={true}
              />
            ))
          ) : (
            <Text size="sm" className="text-gray-400 text-center py-8">
              {placeholder}
            </Text>
          )}
        </Stack>
      </ScrollArea>
    </Card>
  );

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group align="center" gap="sm">
          <Title order={4} className="text-gray-800">
            Champs
          </Title>
        </Group>
      }
      size="xl"
      centered
      padding="lg"
    >
      <Box>
        {/* Instruction */}
        <Text size="sm" className="text-gray-600 mb-4">
          Faites glisser et déposez des champs d'organisation
        </Text>

        {/* Boutons d'action */}
        <Group justify="space-between" align="center" mb="lg">
          <Button
            leftSection={<IconPlus size={16} />}
            variant="outline"
            size="sm"
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Ajouter une valeur calculée
          </Button>
          
          <Group gap="sm">
            <Button
              variant="filled"
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              Appliquer
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
              onClick={onClose}
            >
              Annuler
            </Button>
          </Group>
        </Group>

        <div className="grid grid-cols-3 gap-4">
          {/* Colonne gauche - Tous Les Champs */}
          <div>
            <Card p="md" withBorder className="h-96 bg-white">
              <Text size="sm" fw={500} className="text-gray-700 mb-3">
                Tous Les Champs
              </Text>
              <ScrollArea h={320}>
                <Stack gap="xs">
                  {tousLesChamps.map((champ) => (
                    <ChampCard key={champ.id} champ={champ} />
                  ))}
                </Stack>
              </ScrollArea>
            </Card>
          </div>

          {/* Colonne centrale - Des Filtres De Rapport */}
          <div>
            <DropZone
              title="Des Filtres De Rapport"
              section="filtres"
              items={filtresRapport}
              placeholder="Glissez des champs ici"
            />
          </div>

          {/* Colonne droite - Les colonnes et Valeurs */}
          <div className="space-y-4">
            <DropZone
              title="Les colonnes"
              section="colonnes"
              items={colonnes}
              placeholder="Glissez des champs ici"
            />
            
            <Card
              p="md"
              withBorder
              className="bg-gray-50 border-2 border-dashed border-gray-300"
            >
              <Text size="sm" fw={500} className="text-gray-700 mb-2">
                Valeurs
              </Text>
              <Text size="sm" className="text-gray-400 text-center py-4">
                Glissez des mesures ici
              </Text>
            </Card>
          </div>
        </div>

        {/* Section Les lignes en bas */}
        <div className="mt-4">
          <DropZone
            title="Les lignes"
            section="lignes"
            items={lignes}
            placeholder="Glissez des champs ici"
          />
        </div>
      </Box>
    </Modal>
  );
};

export default ChampsModal;
