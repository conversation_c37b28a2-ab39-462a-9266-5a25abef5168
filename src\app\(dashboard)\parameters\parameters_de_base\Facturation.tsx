import React, { useState } from 'react';
import {
  Stack,
  Title,
  Select,
  Text,
  Paper,
  Table,
 
  Checkbox,
  Group,
   ColorInput,
  ColorPicker,
  ActionIcon,
  Modal
} from '@mantine/core';
import { IconPalette } from '@tabler/icons-react';
const Facturation = () => {
  // State for form values
  const [ongletDefaut, setOngletDefaut] = useState('');
  const [affichageDefaut, setAffichageDefaut] = useState('');
  const [genererBonConsignation, setGenererBonConsignation] = useState(false);

  // Data options for selects
  const ongletOptions = [
    { value: 'facturation', label: 'Facturation' },
    { value: 'devis', label: 'Devi<PERSON>' },
    { value: 'commandes', label: 'Commandes' },
    { value: 'reglements', label: 'Règlements' },
  ];

  const affichageOptions = [
    { value: 'liste', label: 'Liste' },
    { value: 'grille', label: 'Grille' },
    { value: 'tableau', label: 'Tableau' },
  ];

  // Contract states data
  const contractStates = [
    {
      etat: 'Future',
      couleur: '#e935d1',
      motif: 'Future',
      colorCode: '#e935d1'
    },
    {
      etat: 'Résilié',
      couleur: '#3fb2d',
      motif: 'Résilié',
      colorCode: '#3fb2d1'
    },
    {
      etat: 'Désactivé pendant des cycles',
      couleur: '#ab2d5c',
      motif: 'Désactivé pendant des ...',
      colorCode: '#ab2d5c'
    },
    {
      etat: 'Désactivé',
      couleur: '#4527a0',
      motif: 'Désactivé',
      colorCode: '#4527a0'
    },
    {
      etat: 'Expiré',
      couleur: '#124882',
      motif: 'Non autorisé',
      colorCode: '#124882'
    },
    {
      etat: 'Activé',
      couleur: '#3fb2d1',
      motif: 'Autorisé',
      colorCode: '#3fb2d1'
    },
    {
      etat: 'Brouillon',
      couleur: '#4527a0',
      motif: 'Brouillon',
      colorCode: '#4527a0'
    },
    {
      etat: 'Paiement partiel',
      couleur: '#2489a0',
      motif: 'Paiement partiel',
      colorCode: '#2489a0'
    },
    {
      etat: 'Retard de paiement',
      couleur: '#ebc264',
      motif: 'Retard de paiement',
      colorCode: '#ebc264'
    },
  ];
const [isColorPickerModalOpen, setIsColorPickerModalOpen] = useState(false);
  return (
    <Stack gap="lg" className="w-full">
      {/* Configuration Section */}
      <Paper p="md" withBorder>
        <Stack gap="md">
          {/* Onglet par défaut */}
          <div>
            <Text size="sm" fw={500} mb="xs" className="text-gray-700">
              Onglet par défaut
            </Text>
            <Select
              value={ongletDefaut}
              onChange={(value) => setOngletDefaut(value || '')}
              data={ongletOptions}
              size="sm"
              className="w-64"
              placeholder="Sélectionner un onglet"
            />
          </div>

          {/* Checkbox for generating consignment voucher */}
          <div>
            <Checkbox
              checked={genererBonConsignation}
              onChange={(event) => setGenererBonConsignation(event.currentTarget.checked)}
              label="Générer un bon de consignation à partir du contrat"
              size="sm"
              className="text-gray-700"
            />
          </div>

          {/* Affichage par défaut */}
          <div>
            <Text size="sm" fw={500} mb="xs" className="text-gray-700">
              Affichage par défaut
            </Text>
            <Select
              value={affichageDefaut}
              onChange={(value) => setAffichageDefaut(value || '')}
              data={affichageOptions}
              size="sm"
              className="w-64"
              placeholder="Sélectionner un affichage"
            />
          </div>
        </Stack>
      </Paper>

      {/* États des contrats Section */}
      <Paper p="md" withBorder>
        <Title order={4} className="text-gray-700 mb-md">
          États des contrats
        </Title>

        <Table
          striped
          highlightOnHover
          withTableBorder
          withColumnBorders
          className="w-full"
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm" style={{ paddingLeft:'20px',textAlign: 'left',width:'40%' }}>
                État
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-32 text-center" style={{ paddingLeft:'20px',textAlign: 'left',width:'20%' }}> 
                Couleur
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm" style={{ paddingLeft:'40px',textAlign: 'left',width:'40%' }}>
                Motif
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {contractStates.map((state, index) => (
              <Table.Tr key={index} className="hover:bg-gray-50">
                {/* État column */}
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-700">
                    {state.etat}
                  </Text>
                </Table.Td>

                {/* Couleur column */}
                <Table.Td className="border-r border-gray-300 text-center">
                 
                   <div className="flex justify-center">
                                    <Group>
                                      <ColorInput
                                          value={state.colorCode}
                                         w={"84%"}
                                        
                                      />    
                                      <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                                          onClick={() => setIsColorPickerModalOpen(true)}>
                                          <IconPalette stroke={2} />
                                      </ActionIcon>
                                     
                                  </Group>
                                      </div>
                </Table.Td>

                {/* Motif column */}
                <Table.Td>
                  <Text size="sm" className="text-blue-600">
                    {state.motif}
                  </Text>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>
        <Modal  
                        opened={isColorPickerModalOpen}
                        onClose={() => setIsColorPickerModalOpen(false)} 
                        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                        withCloseButton={false}
                        centered
                    >
                        <ColorPicker 
                            format="hex" 
                            
                            swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} 
                        />
                  </Modal>
    </Stack>
  );
};

export default Facturation;
