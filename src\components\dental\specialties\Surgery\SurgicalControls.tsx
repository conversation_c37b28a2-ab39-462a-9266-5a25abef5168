// frontend/dental_medicine/src/components/content/dental/specialties/Surgery/SurgicalControls.tsx

import React from 'react';
import { Button, Tooltip,  } from '@mantine/core';
import {  Menu,rem,Group} from "@mantine/core";
import { DentalControlsProps } from '../../shared/types';
import {   IconEye, IconFileZip, IconTrash ,IconSquareRoundedPlusFilled} from '@tabler/icons-react';
// Interface pour les contrôles chirurgicaux (hérite de DentalControlsProps)
type SurgicalControlsProps = DentalControlsProps;

export const SurgicalControls: React.FC<SurgicalControlsProps> = ({
  activeButton,
  onButtonClick,
  onTargetPathChange
}) => {

  const Controls = [
    {
      id: 'extraction',
      label: 'Extraction',
      pathId: '53',
      tooltip: 'Extraction dentaire',
      shortCode: 'Ex',
      icon: (
          <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" ><g>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}} d="M89.64,45.95L53.67,32.46l-5.33,2.06c0,0,0.51,2.91-2.59,7.18v3.01l41.02,19.11c2.46,1.14,5.27-0.65,5.27-3.36V49.42   C92.04,47.87,91.08,46.49,89.64,45.95z"/>
              <circle cx="33.04" cy="32.77" r="4.42"/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}} d="M88.94,2.16L32.71,19.58c-3.36,1.04-6.36,3.08-8.41,5.94c-1.92,2.68-3.31,6.39-2.14,11.08   c2.65,10.58,20.48,7.63,20.48,7.63v-3.82c0,0,3.18-4.41,2.81-8.23l45.5-13.07c1.51-0.43,2.55-1.82,2.55-3.39V5.53   C93.51,3.15,91.21,1.46,88.94,2.16z M33.04,38.94c-3.4,0-6.16-2.77-6.16-6.16c0-3.4,2.77-6.16,6.16-6.16c3.4,0,6.16,2.77,6.16,6.16   S36.44,38.94,33.04,38.94z"/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M32.52,47.24c0,0,4.9,4.27,5.24,7.8c0.35,3.53-1.42,9.99-4.06,13.23c-2.65,3.23-4.7,5.59-1.76,6.47   c0.22,0.07,0.52,0.1,0.89,0.1c4.55,0,19.44-5.32,20.57-23.29l-8.53-4.72L32.52,47.24z"/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M16.12,53.11c0.9,0,1.81,0.08,2.71,0.21c-0.01-0.78-0.03-1.66-0.08-2.61c-0.18-3.53,10.85-4.18,10.85-4.18   s-7.28-1.91-9.78-8.46c-2.78-7.25,2.99-14.22,2.99-14.22C9.65,26.77,11.12,47.63,12.15,53.74C13.34,53.32,14.65,53.11,16.12,53.11z   "/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M30.78,77.07c-1.37-0.41-2.23-1.16-2.56-2.22c-0.62-2.03,0.98-3.99,3.2-6.69l0.27-0.33c2.07-2.53,3.53-7.36,3.69-10.57   c-0.22-0.1-0.46-0.21-0.7-0.31c0,0-0.25-0.32-1.73-0.32c-0.74,0-1.8,0.08-3.28,0.32C29.35,57,29,57.03,28.64,57.03   c-3.35,0-8.15-2.08-12.53-2.08c-1.62,0-3.19,0.29-4.6,1.07C5.9,59.14,5.17,67.16,8.45,72.99c3.28,5.83,3.23,22.71,7.99,24.78   c0.37,0.16,0.71,0.23,1.03,0.23c3.8,0,4.32-10.46,7.01-10.8c0.05-0.01,0.09-0.01,0.14-0.01c2.74,0,2.49,10.81,6.66,10.81   c0.04,0,0.09,0,0.13,0c3.64-0.19,4.41-13.86,5.87-21.8c-1.98,0.7-3.77,1.05-5.1,1.05C31.62,77.25,31.17,77.19,30.78,77.07z"/></g>
            </svg>
      )
    },
    {
      id: 'implant_surgery',
      label: 'Implant',
      pathId: '56',
      tooltip: 'Chirurgie implantaire',
      shortCode: 'Im',
      icon: (
         <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 16.6 17"  >
          <path style={{display:"inline",fill:"#eaecf1",stroke:"#FFFFFF",strokeMiterlimit:"10"}} d="M13,7.9L13,7.9c0.3-0.8,0.5-1.7,0.5-2.5V4.9c0-0.8-0.2-1.6-0.7-2.3c0,0,0,0,0,0C12.1,1.6,11,1,9.8,1   C9.7,1,9.5,1,9.3,1c-0.3,0-0.7,0-1,0.1c-0.1,0-0.1,0-0.2,0l-0.2,0c-0.2,0-0.6,0-0.8-0.1C6.9,1,6.6,1,6.4,1l-0.7,0   c-1.1,0-2.2,0.4-2.9,1.3C2.4,2.7,2,3.4,2,4.3c0,0,0,0.1,0,0.3c0,1.2,0.2,2.4,0.6,3.6l0,0L13,8.2L13,7.9z"/>
          <path style={{display:"inline",fill:"#eaecf1",stroke:"#FFFFFF",strokeMiterlimit:"10"}} d="M5.8,2.3c0,0-2.5,0-2.5,3.2"/>
          <path style={{display:"inline",fill:"#eaecf1",stroke:"#FFFFFF",strokeMiterlimit:"10"}} d="M4.9,9.4v5c0,0.1,0,0.3,0.1,0.4l0.7,1c0.1,0.2,0.3,0.3,0.6,0.3l2.8,0.1c0.3,0,0.5-0.1,0.6-0.3l0.7-1.1   c0.1-0.1,0.1-0.3,0.1-0.4V9.5"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="10.4" y1="9.2" x2="4.1" y2="10.5"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="11.3" y1="10.6" x2="4.1" y2="11.8"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="11.3" y1="12" x2="4.2" y2="13.3"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="11.4" y1="13.3" x2="4.2" y2="14.8"/>
          <rect  x="3.3" y="8.2"  width="8.9" height="1.2"/>
          </svg>
      )
    },
     {
      id: 'Bone',
      label: 'Bone',
      pathId: '66',
      tooltip: 'Bone',
      shortCode: 'Bo',
      icon: (
          <svg xmlns="http://www.w3.org/2000/svg"   version="1.1"
          viewBox="0 0 1777.78 2222.225" x="0px" y="0px" fillRule="evenodd" clipRule="evenodd"><defs>
          </defs><g>
          <path  style={{ fill: "#f5f5f5", stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10 }} d="M431.91 361.84c-2.91,-99.39 -3.21,-158.24 1.4,-200.51 11.91,-109.19 56.6,-148.61 173.68,-159.47 89.92,-1.14 192.84,32.69 283.6,31.61 96.31,-1.14 180.48,-37.06 275.63,-33.18 118.19,4.82 169.38,38.86 179.75,196.87 2.41,36.7 2.61,79.83 0.95,164.39 -0.31,16.11 -13.87,25.63 -29.61,29.04 -281.37,61 -565.54,78.24 -855.79,0 -15.46,-4.17 -29.14,-12.76 -29.61,-28.75zm-209.04 -361.84l-7.53 548.22 139.69 -10.11 -181.51 286.45 -173.52 -272.21 122.62 -4.69 7.5 -545.72 92.76 -1.93zm1422.74 0l-7.53 548.22 139.69 -10.11 -181.51 286.45 -173.52 -272.21 122.62 -4.69 7.5 -545.72 92.76 -1.93zm-871.92 1443.06c6.45,120.73 19.6,135.83 -13.57,132.83 -163.08,-43.72 -178.71,-236.19 -184.76,-279.79 -11.04,-79.58 -74.22,-126.3 -78.48,-194.39 -6.55,-104.5 -10.26,-280.19 -4.19,-335.81 10.35,-94.95 49.22,-129.24 151.04,-138.69 46.88,-0.05 78.28,12.67 108.74,28.42 70.66,36.52 102.79,77.71 186.04,72.99 48.34,-2.74 51.92,-23.42 25.5,-25.96 -42.5,-4.09 -78.56,-13.57 -103.98,-26.83 -19.09,-9.96 -8.04,-28.52 6.08,-28.54 90.05,3.27 160.44,-19.83 217.26,-21.1 143.42,-3.2 192.23,6.93 203,170.87 3.39,51.72 1.76,217.96 -3.67,304.65 -4.27,68.09 -67.44,114.81 -78.48,194.39 -6.05,43.6 -21.68,236.07 -184.76,279.79 -33.17,3 -24.6,-12.42 -13.57,-132.83 21.19,-231.19 -243.81,-217.22 -232.21,0zm-767.17 -244.27c213.95,27.05 315.31,-150.36 405.76,-103.35l0.7 11.52c1.06,16.9 4.27,33.49 9.32,49.64 10.83,34.68 28.65,62.7 46.48,93.74 10.14,17.65 20.48,36.88 23.32,57.31 8,57.67 16.21,106.55 39.37,161.13 39.32,92.65 107.91,161.76 206.89,188.3l6.98 1.87 7.2 0.65c28.73,2.6 55.56,-2.55 78.39,-21.63 33.54,-28.04 36.85,-65.15 33.89,-105.04 -2.35,-31.54 -5.5,-62.7 -7.19,-94.36 -1.19,-22.35 1.72,-51.9 17.85,-69.18 5.72,-6.13 14.32,-11.43 23.06,-10.33 25.94,3.28 25.13,60.14 23.65,76.32 -2.97,32.43 -7.24,65.22 -9.45,97.63 -2.76,40.49 2.32,78.76 36.75,106.11 22.83,18.14 49.33,23.04 77.57,20.48l7.2 -0.65 6.99 -1.87c98.97,-26.53 167.56,-95.64 206.88,-188.3 23.16,-54.59 31.37,-103.46 39.37,-161.13 2.83,-20.43 13.19,-39.67 23.32,-57.31 17.83,-31.05 35.64,-59.07 46.48,-93.76 5.05,-16.15 8.26,-32.72 9.31,-49.62 0.26,-4.13 0.5,-8.27 0.74,-12.42 89.97,-43.63 191.41,131.11 403.89,104.25 2.19,-0.28 4.37,-0.48 6.54,-0.64l0 579.63 -1777.78 0 0 -579.63c2.17,0.16 4.35,0.37 6.54,0.64z"/></g>
          </svg>
      )
    },
    {
      id: 'Resection',
      label: 'Resection',
      pathId: '67',
      tooltip: 'Resection',
      shortCode: 'Re',
      icon: (
          <svg xmlns="http://www.w3.org/2000/svg" data-name="11 Tooth extraction" viewBox="0 0 64 80" x="0px" y="0px">
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M38.93,32.043c-2.163-.198-4.192,.303-5.904,1.292-.639,.369-1.412,.369-2.051,0-1.712-.99-3.741-1.49-5.904-1.292-4.572,.418-8.375,4.037-8.979,8.588-.492,3.7,1.043,7.069,3.648,9.163,1.422,1.143,2.26,2.856,2.26,4.681v.895c0,3.017,1.141,5.772,3.172,7.802,.53,.53,1.25,.828,2,.828,1.562,0,2.828-1.266,2.828-2.828v-5.172c0-1.105,.895-2,2-2s2,.895,2,2v5.172c0,1.562,1.266,2.828,2.828,2.828,.75,0,1.47-.298,2-.828,2.031-2.031,3.172-4.785,3.172-7.657v-1.041c0-1.825,.838-3.538,2.26-4.681,2.605-2.093,4.14-5.463,3.648-9.163-.605-4.551-4.407-8.17-8.979-8.588Z"/>
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M42.971,21.56l-11.469-3.728C25.16,15.771,20,7.771,20,0H12c0,11.348,7.321,22.285,17.029,25.44l11.469,3.728c6.859,2.229,11.502,8.621,11.502,15.833v9c4.418,0,8-3.582,8-8v-.999c0-10.677-6.875-20.141-17.029-23.441Z"/>
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M43.874,19.763c4.94-5.012,8.126-12.29,8.126-19.763h-8c0,6.806-3.958,13.787-9.202,16.801l8.791,2.857c.097,.032,.188,.073,.285,.105Z"/>
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M23.502,29.168l5.263-1.711-.354-.115c-3.219-1.046-6.19-2.883-8.77-5.269-9.384,3.683-15.641,12.746-15.641,22.927v1c0,4.418,3.582,8,8,8v-9c0-7.212,4.643-13.603,11.502-15.833Z"/>
          </svg>
      )
    },
    {
      id: 'bone_graft',
      label: 'Greffe',
      pathId: '59',
      tooltip: 'Greffe osseuse',
      shortCode: 'Gr',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M4 12C4 8 8 4 12 4C16 4 20 8 20 12C20 16 16 20 12 20C8 20 4 16 4 12Z"
            fill="#f5f5f5"
            stroke="#dc3545"
            strokeWidth="0.5"
          />
          <rect x="8" y="8" width="8" height="8" rx="1" fill="#dc3545" opacity="0.3"/>
          <circle cx="10" cy="10" r="1" fill="#dc3545"/>
          <circle cx="14" cy="10" r="1" fill="#dc3545"/>
          <circle cx="10" cy="14" r="1" fill="#dc3545"/>
          <circle cx="14" cy="14" r="1" fill="#dc3545"/>
          <circle cx="12" cy="12" r="1" fill="#dc3545"/>
        </svg>
      )
    },
    {
      id: 'sinus_lift',
      label: 'Sinus',
      pathId: '62',
      tooltip: 'Sinus lift',
      shortCode: 'Si',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M4 16C4 12 8 8 12 8C16 8 20 12 20 16V20H4V16Z"
            fill="#f5f5f5"
            stroke="#dc3545"
            strokeWidth="0.5"
          />
          <path
            d="M8 12L12 8L16 12"
            stroke="#dc3545"
            strokeWidth="2"
            fill="none"
          />
          <circle cx="12" cy="14" r="2" fill="#dc3545" opacity="0.6"/>
          <path d="M6 18L18 18" stroke="#dc3545" strokeWidth="2"/>
        </svg>
      )
    },
    {
      id: 'periodontal_surgery',
      label: 'Parodontal',
      pathId: '64',
      tooltip: 'Chirurgie parodontale',
      shortCode: 'Pa',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M3 12C3 7 7 3 12 3C17 3 21 7 21 12"
            stroke="#dc3545"
            strokeWidth="2"
            fill="none"
          />
          <path
            d="M5 15C5 13 7 11 12 11C17 11 19 13 19 15"
            stroke="#dc3545"
            strokeWidth="1.5"
            fill="none"
          />
          <path
            d="M7 18C7 17 9 16 12 16C15 16 17 17 17 18"
            stroke="#dc3545"
            strokeWidth="1"
            fill="none"
          />
          <circle cx="12" cy="20" r="1" fill="#dc3545"/>
          <path d="M10 4L12 2L14 4" stroke="#dc3545" strokeWidth="1" fill="none"/>
        </svg>
      )
    },
    {
      id: 'apicoectomy',
      label: 'Apicectomie',
      pathId: '65',
      tooltip: 'Apicectomie',
      shortCode: 'Ap',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 2C8 2 5 5 5 9V15C5 19 8 22 12 22C16 22 19 19 19 15V9C19 5 16 2 12 2Z"
            fill="#f5f5f5"
            stroke="#dc3545"
            strokeWidth="0.5"
          />
          <line x1="12" y1="6" x2="12" y2="16" stroke="#dc3545" strokeWidth="2"/>
          <circle cx="12" cy="18" r="2" fill="#dc3545" opacity="0.7"/>
          <path d="M10 18L12 20L14 18" stroke="#ffffff" strokeWidth="1" fill="none"/>
        </svg>
      )
    }
  ];

  return (
        <Group justify="space-between" mb={4} gap="xl"  w={"70%"}>
      {/* Menu "Tous les Traitements" */}
     <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                Tous les Traitements
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Télécharger zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Aperçu de tous
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Supprimer tous
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>

      {/* Boutons de traitement */}
      <div style={{ margin: '0 auto' }} className="mb-2 flex flex-end p-2 sm:justify-start space-x-2">
     {Controls.map((control) => (
           <Tooltip
                 key={control.id}
                 label={control.tooltip}
                 withArrow
                 className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                   >
             <Button
               styles={{
                 root: {
                   position: 'relative',
                   color: 'white',
                   height: '35px', // Adjust button height
                   width: '35px',  // Adjust button width
                   padding: 0,
                   borderRadius: '0.5rem'
                 },
               }}
             >
               {/* SVG in the middle */}
               <div
           style={{
             display: 'flex',
             justifyContent: 'center',
             alignItems: 'center',
             height: '100%',
             width: '100%',
           }}
         >
             <span  className={
             activeButton === control.id
               ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
               : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
           }
         onClick={() => {
                         onButtonClick(control.id);
                         onTargetPathChange(control.pathId);
                       }}
               >
     {control.icon}
                     </span>
               </div>
               {/* "CL" in the bottom-right corner */}
               <span
                 style={{
                   position: 'absolute',
                   bottom: '0px',
                   right: '0px',
                   fontSize: '8px',
                   fontWeight: '800',
                   backgroundColor: 'white',
                   // borderRadius:'0.125rem' ,
                   borderTopLeftRadius: '0.5rem' ,
                   borderBottomRightRadius: '0.5rem' ,
                   color:'#3799CE',
                   padding:'3px  0px 1px 2px' ,
                 }}
                 className="h-[14px] w-[14px] "
               >
                      {control.shortCode}
               </span>
             </Button>
           </Tooltip>
            ))}
      </div>
    </Group>
  );
};

export default SurgicalControls;
