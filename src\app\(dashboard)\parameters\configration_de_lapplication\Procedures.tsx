import React, { useState } from 'react';
import {
  Button,
  Group,
  Table,
  Text,
  TextInput,
  ActionIcon,
  Tooltip,
  Modal,
  NumberInput,
  Select,
  Checkbox,
  Grid,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconDots,
  IconLock,
} from '@tabler/icons-react';

// Types pour les données
interface Procedure {
  id: number;
  code: string;
  name: string;
  fee: number;
  reimbursement?: string;
  isActive: boolean;
}

const Procedures = () => {
  // États pour la gestion des données
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProcedure, setEditingProcedure] = useState<Procedure | null>(null);

  // États pour le formulaire
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    fee: 0,
    reimbursement: '',
    isActive: true,
    servicesDesignes: '',
    codeNGAP: '',
    codeCCAM: '',
    tnr: '',
    modalite: '',
    remboursable: false,
  });

  // Données de test pour les procédures
  const [procedures, setProcedures] = useState<Procedure[]>([
    { id: 1, code: 'RTR', name: 'Rétroalvéolaire', fee: 300.00, isActive: true },
    { id: 2, code: 'RXP', name: 'Rx Panoramique', fee: 300.00, isActive: true },
    { id: 3, code: 'CBM', name: 'Cone Beam', fee: 1000.00, isActive: true },
    { id: 4, code: 'CIB', name: 'Camera Intrabuccale', fee: 300.00, isActive: true },
    { id: 5, code: 'HBD', name: 'Motivation à l\'HBD', fee: 200.00, isActive: true },
    { id: 6, code: 'EME', name: 'Empreinte D\'étude', fee: 200.00, isActive: true },
    { id: 7, code: 'ANL', name: 'Analyse Biologique', fee: 0.00, isActive: true },
    { id: 8, code: 'SPR', name: 'Sondage Parodontal', fee: 200.00, isActive: true },
    { id: 9, code: 'DET', name: 'Détartrage', fee: 500.00, isActive: true },
    { id: 10, code: 'AEP', name: 'Aéropolissage', fee: 500.00, isActive: true },
    { id: 11, code: 'AFS', name: 'Ablation de fil de suture', fee: 200.00, isActive: true },
    { id: 12, code: 'COT', name: 'Contention', fee: 1000.00, isActive: true },
    { id: 13, code: 'CLT', name: 'Consultation', fee: 200.00, isActive: true },
    { id: 14, code: 'ORD', name: 'Ordonnance', fee: 200.00, isActive: true },
    { id: 15, code: 'CRI', name: 'Curetage interdentaire', fee: 600.00, isActive: true },
    { id: 16, code: 'SGL', name: 'Soulagement endodontique', fee: 1000.00, isActive: true },
    { id: 17, code: 'ABC', name: 'Drainage D\'abcès', fee: 1000.00, isActive: true },
    { id: 18, code: 'COF', name: 'Collage Facette', fee: 500.00, isActive: true },
    { id: 19, code: 'CLC', name: 'Collage Couronne', fee: 500.00, isActive: true },
    { id: 20, code: 'CFD', name: 'Collage Fragment Dentaire', fee: 500.00, isActive: true },
  ]);

  // Filtrage des procédures
  const filteredProcedures = procedures.filter(procedure =>
    procedure.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    procedure.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Ouverture du modal pour nouveau/édition
  const openModal = (procedure?: Procedure) => {
    if (procedure) {
      setEditingProcedure(procedure);
      setFormData({
        code: procedure.code,
        name: procedure.name,
        fee: procedure.fee,
        reimbursement: procedure.reimbursement || '',
        isActive: procedure.isActive,
        servicesDesignes: '',
        codeNGAP: '',
        codeCCAM: '',
        tnr: '',
        modalite: '',
        remboursable: false,
      });
    } else {
      setEditingProcedure(null);
      setFormData({
        code: '',
        name: '',
        fee: 0,
        reimbursement: '',
        isActive: true,
        servicesDesignes: '',
        codeNGAP: '',
        codeCCAM: '',
        tnr: '',
        modalite: '',
        remboursable: false,
      });
    }
    setIsModalOpen(true);
  };

  // Fermeture du modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingProcedure(null);
    setFormData({
      code: '',
      name: '',
      fee: 0,
      reimbursement: '',
      isActive: true,
      servicesDesignes: '',
      codeNGAP: '',
      codeCCAM: '',
      tnr: '',
      modalite: '',
      remboursable: false,
    });
  };

  // Sauvegarde d'une procédure
  const saveProcedure = () => {
    if (!formData.code.trim() || !formData.name.trim()) {
      return;
    }

    if (editingProcedure) {
      // Modification
      setProcedures(prev =>
        prev.map(procedure =>
          procedure.id === editingProcedure.id
            ? { ...procedure, ...formData }
            : procedure
        )
      );
    } else {
      // Création
      const newProcedure: Procedure = {
        id: Date.now(),
        ...formData,
      };
      setProcedures(prev => [...prev, newProcedure]);
    }
    closeModal();
  };

  // Suppression d'une procédure
  const deleteProcedure = (id: number) => {
    setProcedures(prev => prev.filter(procedure => procedure.id !== id));
  };

  // Toggle du statut actif
  const toggleActiveStatus = (id: number) => {
    setProcedures(prev =>
      prev.map(procedure =>
        procedure.id === id
          ? { ...procedure, isActive: !procedure.isActive }
          : procedure
      )
    );
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header avec bouton Nouveau */}
      <div className="bg-blue-500 text-white p-4 flex justify-between items-center">
        <Text size="xl" fw={600}>
          Procédures
        </Text>
        <Button
          variant="subtle"
          color="white"
          leftSection={<IconPlus size={16} />}
          onClick={() => openModal()}
          className="text-white hover:bg-blue-600"
        >
          Nouveau
        </Button>
      </div>

      {/* Barre de recherche */}
      <div className="p-4 border-b border-gray-200">
        <TextInput
          placeholder="Rechercher une procédure..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          leftSection={<IconSearch size={16} />}
          size="md"
          className="max-w-md"
        />
      </div>

      {/* Tableau des procédures */}
      <div className="flex-1 overflow-auto">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Code
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Nom
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Honoraire
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Remboursement
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm text-center">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredProcedures.map((procedure) => (
              <Table.Tr key={procedure.id} className="hover:bg-gray-50">
                <Table.Td className="border-r border-gray-200 text-sm font-medium">
                  {procedure.code}
                </Table.Td>
                <Table.Td className="border-r border-gray-200 text-sm">
                  {procedure.name}
                </Table.Td>
                <Table.Td className="border-r border-gray-200 text-sm">
                  {procedure.fee.toFixed(2)} €
                </Table.Td>
                <Table.Td className="border-r border-gray-200 text-sm">
                  {procedure.reimbursement || '-'}
                </Table.Td>
                <Table.Td className="text-center">
                  <Group gap="xs" justify="center">
                    {/* Status toggle */}
                    <Tooltip label={procedure.isActive ? 'Actif' : 'Inactif'}>
                      <ActionIcon
                        variant="subtle"
                        color={procedure.isActive ? 'green' : 'gray'}
                        size="sm"
                        onClick={() => toggleActiveStatus(procedure.id)}
                      >
                        <div
                          className={`w-3 h-3 rounded-full ${
                            procedure.isActive ? 'bg-green-500' : 'bg-gray-400'
                          }`}
                        />
                      </ActionIcon>
                    </Tooltip>

                    {/* Edit button */}
                    <Tooltip label="Modifier">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        size="sm"
                        onClick={() => openModal(procedure)}
                      >
                        <IconEdit size={14} />
                      </ActionIcon>
                    </Tooltip>

                    {/* Menu button */}
                    <Tooltip label="Plus d'options">
                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="sm"
                      >
                        <IconDots size={14} />
                      </ActionIcon>
                    </Tooltip>

                    {/* Delete button */}
                    <Tooltip label="Supprimer">
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        size="sm"
                        onClick={() => deleteProcedure(procedure.id)}
                      >
                        <IconTrash size={14} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {filteredProcedures.length === 0 && (
          <div className="flex items-center justify-center h-32 text-gray-500">
            <Text size="sm">Aucune procédure trouvée</Text>
          </div>
        )}
      </div>

      {/* Modal pour ajouter/modifier une procédure */}
      <Modal
        opened={isModalOpen}
        onClose={closeModal}
        title={
          <div className="flex items-center gap-2 text-white">
            <IconLock size={16} />
            <Text fw={600}>Procédure</Text>
          </div>
        }
        size="lg"
        centered
        styles={{
          header: {
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '12px 16px',
          },
          title: {
            color: 'white',
            fontWeight: 600,
          },
          close: {
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          },
        }}
      >
        <div className="p-4">
          <Grid gutter="md">
            {/* Première ligne: Code et Nom */}
            <Grid.Col span={6}>
              <TextInput
                label={
                  <Text size="sm" fw={500} c="red">
                    Code *
                  </Text>
                }
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                required
                className="border-b-2 border-blue-500"
                styles={{
                  input: {
                    borderBottom: '2px solid #3b82f6',
                    borderRadius: 0,
                    borderTop: 'none',
                    borderLeft: 'none',
                    borderRight: 'none',
                    paddingLeft: 0,
                  },
                }}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={
                  <Text size="sm" fw={500}>
                    Nom *
                  </Text>
                }
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid.Col>

            {/* Deuxième ligne: Honoraire et Services désignés */}
            <Grid.Col span={6}>
              <NumberInput
                label={
                  <Text size="sm" fw={500} c="red">
                    Honoraire *
                  </Text>
                }
                value={formData.fee}
                onChange={(value) => setFormData({ ...formData, fee: Number(value) || 0 })}
                min={0}
                step={0.01}
                decimalScale={2}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={
                  <Text size="sm" fw={500}>
                    Services désignés
                  </Text>
                }
                placeholder="Sélectionner..."
                value={formData.servicesDesignes}
                onChange={(value) => setFormData({ ...formData, servicesDesignes: value || '' })}
                data={[
                  { value: 'consultation', label: 'Consultation' },
                  { value: 'chirurgie', label: 'Chirurgie' },
                  { value: 'radiologie', label: 'Radiologie' },
                ]}
                rightSection={<IconPlus size={14} />}
              />
            </Grid.Col>

            {/* Troisième ligne: Code NGAP et Code CCAM */}
            <Grid.Col span={6}>
              <TextInput
                label={
                  <Text size="sm" fw={500}>
                    Code NGAP
                  </Text>
                }
                value={formData.codeNGAP}
                onChange={(e) => setFormData({ ...formData, codeNGAP: e.target.value })}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={
                  <Text size="sm" fw={500}>
                    Code CCAM
                  </Text>
                }
                value={formData.codeCCAM}
                onChange={(e) => setFormData({ ...formData, codeCCAM: e.target.value })}
              />
            </Grid.Col>

            {/* Quatrième ligne: TNR et Modalité */}
            <Grid.Col span={6}>
              <TextInput
                label={
                  <Text size="sm" fw={500}>
                    TNR
                  </Text>
                }
                value={formData.tnr}
                onChange={(e) => setFormData({ ...formData, tnr: e.target.value })}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={
                  <Text size="sm" fw={500}>
                    Modalité
                  </Text>
                }
                placeholder="Sélectionner..."
                value={formData.modalite}
                onChange={(value) => setFormData({ ...formData, modalite: value || '' })}
                data={[
                  { value: 'standard', label: 'Standard' },
                  { value: 'urgence', label: 'Urgence' },
                  { value: 'specialise', label: 'Spécialisé' },
                ]}
                rightSection={<IconPlus size={14} />}
              />
            </Grid.Col>

            {/* Checkbox Remboursable */}
            <Grid.Col span={12}>
              <Checkbox
                label={
                  <Text size="sm" fw={500}>
                    Remboursable
                  </Text>
                }
                checked={formData.remboursable}
                onChange={(e) => setFormData({ ...formData, remboursable: e.currentTarget.checked })}
                icon={IconLock}
              />
            </Grid.Col>
          </Grid>

          {/* Boutons d'action */}
          <Group justify="flex-end" gap="sm" mt="xl">
            <Button variant="outline" color="gray" onClick={closeModal}>
              Enregistrer
            </Button>
            <Button
              color="red"
              onClick={closeModal}
            >
              Annuler
            </Button>
          </Group>
        </div>
      </Modal>
    </div>
  );
};

export default Procedures;
