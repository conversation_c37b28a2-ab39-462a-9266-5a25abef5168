'use client';

import React, { useState } from 'react';
import {
  Title,
  Button,
  Group,
  Text,
  Modal,
  Table,
  ActionIcon,
  Tabs,
  Select
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconServerCog,
  IconPlus,
  IconDownload,
  IconTrash,
  IconAlertCircle
} from '@tabler/icons-react';

// Types
interface Backup {
  id: number;
  createdBy: string;
  generatedAt: string;
  status: 'generated' | 'waiting' | 'error' | 'missing';
  type: 'NO_FILES' | 'ONLY_FILES' | 'FULL';
  size: string;
  isGenerated: boolean;
  hasFailed: boolean;
  downloaded: boolean;
}

interface AutoBackup {
  id: number;
  createdAt: string;
  size: number;
  filename: string;
  hasError?: boolean;
}

const BackupsDeDonnees: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('manual');
  const [backupType, setBackupType] = useState<string>('');
  const [showTypeOptions, setShowTypeOptions] = useState(false);

  // Mock data
  const [manualBackups] = useState<Backup[]>([
    {
      id: 1,
      createdBy: '25/04/2025 15:15',
      generatedAt: '25/04/2025 15:15',
      status: 'generated',
      type: 'NO_FILES',
      size: '2.89 MB',
      isGenerated: true,
      hasFailed: false,
      downloaded: false
    }
  ]);

  const [autoBackups] = useState<AutoBackup[]>([
    { id: 1, createdAt: '31/05/2025 00:00', size: 50405284, filename: 'pratisoft-week-20250531.txz' },
    { id: 2, createdAt: '07/06/2025 00:00', size: 50719448, filename: 'pratisoft-week-20250607.txz' },
    { id: 3, createdAt: '08/06/2025 00:00', size: 50719524, filename: 'pratisoft-day-20250608.txz' },
    { id: 4, createdAt: '24/05/2025 00:00', size: 50405196, filename: 'pratisoft-week-20250524.txz', hasError: true },
    { id: 5, createdAt: '17/05/2025 00:00', size: 50405300, filename: 'pratisoft-week-20250517.txz' }
  ]);

  // Modals
  const [newBackupModalOpened, { open: openNewBackupModal, close: closeNewBackupModal }] = useDisclosure(false);

  const handleCreateBackup = () => {
    if (!backupType) {
      notifications.show({
        title: 'Erreur',
        message: 'Veuillez sélectionner un type de backup',
        color: 'red'
      });
      return;
    }

    notifications.show({
      title: 'Succès',
      message: 'Sauvegarde créée avec succès',
      color: 'green'
    });
    closeNewBackupModal();
    setBackupType('');
    setShowTypeOptions(false);
  };

  const handleDownload = (filename: string, isAuto = false) => {
    const extension = isAuto ? '.txz' : '.backup';
    notifications.show({
      title: 'Téléchargement',
      message: `Téléchargement de ${filename}${extension} en cours...`,
      color: 'blue'
    });
  };

  const handleDelete = (id: number) => {
    notifications.show({
      title: 'Suppression',
      message: 'Backup supprimé avec succès',
      color: 'green'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      generated: { color: 'green', label: 'Prêt à télécharger' },
      waiting: { color: 'yellow', label: 'En cours' },
      error: { color: 'red', label: 'Erreur' },
      missing: { color: 'gray', label: 'Fichier manquant' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <div className={`w-3 h-3 rounded-full bg-${config.color}-500`} title={config.label} />;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      NO_FILES: { color: 'blue', label: 'Données seulement' },
      ONLY_FILES: { color: 'orange', label: 'Pièces-jointes seulement' },
      FULL: { color: 'purple', label: 'Données et pièces-jointes' }
    };

    const config = typeConfig[type as keyof typeof typeConfig];
    return <div className={`w-3 h-3 rounded-full bg-${config.color}-500`} title={config.label} />;
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-blue-600 text-white px-6 py-4 flex items-center gap-3">
        <IconServerCog size={24} />
        <Title order={2} className="text-white font-medium">
          Backups de données
        </Title>
        <div className="flex-1" />
        {activeTab === 'manual' && (
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="blue"
            onClick={openNewBackupModal}
            className="bg-blue-500 hover:bg-blue-400"
          >
            Nouvelle sauvegarde (Backup)
          </Button>
        )}
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'manual')} className="flex-1 flex flex-col">
        <Tabs.List className="border-b border-gray-200 px-6">
          <Tabs.Tab value="manual">Backups Manuels</Tabs.Tab>
          <Tabs.Tab value="auto">backups Int.-0</Tabs.Tab>
        </Tabs.List>

        {/* Manual Backups Tab */}
        <Tabs.Panel value="manual" className="flex-1 p-6">
          <div className="flex flex-col h-full">
            <Table className="flex-1">
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Créé par</Table.Th>
                  <Table.Th>Généré le</Table.Th>
                  <Table.Th>Statut</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Taille</Table.Th>
                  <Table.Th></Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {manualBackups.map((backup) => (
                  <Table.Tr key={backup.id}>
                    <Table.Td>{backup.createdBy}</Table.Td>
                    <Table.Td>{backup.generatedAt}</Table.Td>
                    <Table.Td>{getStatusBadge(backup.status)}</Table.Td>
                    <Table.Td>{getTypeBadge(backup.type)}</Table.Td>
                    <Table.Td>{backup.size}</Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        {backup.isGenerated && (
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleDownload(`backup_${backup.id}`)}
                          >
                            <IconDownload size={16} />
                          </ActionIcon>
                        )}
                        {(backup.isGenerated || backup.hasFailed) && (
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            onClick={() => handleDelete(backup.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        )}
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>

            {/* Legend */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="flex justify-between">
                <div className="flex gap-6">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-500" />
                    <Text size="sm">Prêt à télécharger</Text>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-yellow-500" />
                    <Text size="sm">En cours</Text>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500" />
                    <Text size="sm">Erreur</Text>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-gray-500" />
                    <Text size="sm">Fichier manquant</Text>
                  </div>
                </div>
                <div className="flex gap-6">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500" />
                    <Text size="sm">Données seulement</Text>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-orange-500" />
                    <Text size="sm">Pièces-jointes seulement</Text>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-purple-500" />
                    <Text size="sm">Données et pièces-jointes</Text>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Tabs.Panel>

        {/* Auto Backups Tab */}
        <Tabs.Panel value="auto" className="flex-1 p-6">
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Créé par</Table.Th>
                <Table.Th>Taille</Table.Th>
                <Table.Th>Nom du fichier</Table.Th>
                <Table.Th></Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {autoBackups.map((backup) => (
                <Table.Tr key={backup.id}>
                  <Table.Td className="text-blue-600">{backup.createdAt}</Table.Td>
                  <Table.Td className="text-blue-600">{formatFileSize(backup.size)}</Table.Td>
                  <Table.Td className="text-blue-600">{backup.filename}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      {backup.hasError && (
                        <IconAlertCircle size={16} className="text-red-500" />
                      )}
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={() => handleDownload(backup.filename, true)}
                      >
                        <IconDownload size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Tabs.Panel>
      </Tabs>

      {/* New Backup Modal */}
      <Modal
        opened={newBackupModalOpened}
        onClose={closeNewBackupModal}
        title="Création d'une sauvegarde(backup) de vos données."
        size="md"
        centered
      >
        <div className="space-y-4">
          <div>
            <Text size="sm" fw={500} mb="xs" className="text-red-500">
              Type de backup *
            </Text>
            {!showTypeOptions ? (
              <Select
                placeholder="Pièces-jointes seulement"
                value={backupType}
                onChange={(value) => {
                  setBackupType(value || '');
                  setShowTypeOptions(true);
                }}
                data={[
                  { value: 'data_only', label: 'Données seulement' },
                  { value: 'files_only', label: 'Pièces-jointes seulement' },
                  { value: 'full', label: 'Données et pièces-jointes' }
                ]}
              />
            ) : (
              <div className="space-y-2">
                <div
                  className={`p-3 border rounded cursor-pointer ${backupType === 'data_only' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setBackupType('data_only')}
                >
                  <Text size="sm" className="text-blue-600">Données seulement</Text>
                </div>
                <div
                  className={`p-3 border rounded cursor-pointer ${backupType === 'files_only' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setBackupType('files_only')}
                >
                  <Text size="sm" className="text-blue-600">Pièces-jointes seulement</Text>
                </div>
                <div
                  className={`p-3 border rounded cursor-pointer ${backupType === 'full' ? 'bg-blue-50 border-blue-500' : 'border-gray-200'}`}
                  onClick={() => setBackupType('full')}
                >
                  <Text size="sm" className="text-blue-600">Données et pièces-jointes</Text>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="default"
              onClick={closeNewBackupModal}
              className="text-gray-600"
            >
              Annuler
            </Button>
            <Button
              color="blue"
              onClick={handleCreateBackup}
            >
              Enregistrer
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default BackupsDeDonnees;
