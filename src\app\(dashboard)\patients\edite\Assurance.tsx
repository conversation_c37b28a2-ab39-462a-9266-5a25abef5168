
'use cliente';
import { useEffect, useState } from 'react';
import { IconPlus } from '@tabler/icons-react'; // Ou `@mdi/react` si tu préfères
import { DateInput } from '@mantine/dates';
import { Group, Title, Button, Text, Tooltip, Menu, Divider,ActionIcon,Textarea } from '@mantine/core';
import { Select, TextInput, Radio,  Stack } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import Icon from '@mdi/react';
import { IconCalendar } from '@tabler/icons-react'; // ou mdi-calendar si tu utilises @mdi/react
import { useForm } from '@mantine/form';
import {InsuranceTable} from './AssurancesPatient'
import {
  mdiArrowLeft,
  mdiCardAccountDetails,
mdiPlaylistEdit,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,
  mdiFormatListBulleted,
   mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiCalendarPlus,
} from '@mdi/js';
type Location = { id: number; full_name: string };
type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
   patient: any;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
  selectedInsurance: string;
  setSelectedInsurance: (value: string) => void;
  affiliateNumber: string;
  setAffiliateNumber: (value: string) => void;
  affiliateType: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT';
  setAffiliateType: (value: 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT') => void;
  organizationOptions: { value: string; label: string }[];
   value: string | null;
  onChang: (val: string | null) => void;
  onAdd?: () => void;
  locked?: boolean;
   countryId: number | null;
  provinceId: number | null;
  values: Location | null;
  onChange: (city: Location | null) => void;
  disabled?: boolean;
  
};
const Assurance =({
  patient,
  onGoBack,
  onGoToContract,
   patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  selectedInsurance,
  setSelectedInsurance,
  affiliateNumber,
  setAffiliateNumber,
  affiliateType,
  setAffiliateType,
  organizationOptions,
  value,
  onChange,
  onAdd,
  locked = false,
  countryId,
  provinceId,

}: PatientActionsProps) => {
     const disabled = isFormInvalid || isDraft;
 
  const organizations = [
  { value: 'AMO', label: 'AMO' },
  { value: 'ATLANTA', label: 'ATLANTA' },
  { value: 'AXA', label: 'AXA' },
  { value: 'CNOPS', label: 'CNOPS' },
  { value: 'Aucune', label: 'Aucune' },
  // ... à compléter dynamiquement depuis API
];
const defaultTitles = [
  { value: 'Mr', label: 'Mr' },
  { value: 'Mlle', label: 'Mlle' },
  { value: 'Mme', label: 'Mme' },
  { value: 'Dr.', label: 'Dr.' },
  { value: 'Pr.', label: 'Pr.' },
  { value: 'Garçon', label: 'Garçon' },
  { value: 'Fille', label: 'Fille' },
  { value: 'Bébé', label: 'Bébé' },
];
    const [titles, setTitles] = useState(defaultTitles);
const handleAdds = () => {
    if (onAdd) return onAdd();

    // Exemple d’ajout d’un titre fictif
    const newTitle = prompt('Nouveau titre :');
    if (newTitle) {
      setTitles((prev) => [...prev, { value: newTitle, label: newTitle }]);
    }
  };
  const capitalize = (text: string) =>
    text.replace(/\b\w/g, (c) => c.toUpperCase());
  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      name: '',
      email: '',
      national_id: '',
      
    },
  });
  const [cities, setCities] = useState<Location[]>([]);
  const [search, setSearch] = useState('');

  useEffect(() => {
    if (!countryId || !provinceId) return;

    const fetchCities = async () => {
      // Remplace par ton appel API réel
      const response = await fetch(`/api/cities?country=${countryId}&province=${provinceId}&q=${search}`);
      const data = await response.json();
      setCities(data);
    };

    fetchCities();
  }, [countryId, provinceId, search]);
   const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
  const toggleAssurancesPatient = () => {
        setIsSidebarVisible(!isSidebarVisible);
      };


 interface Insurance {
  uid: string;
  organization: { name: string };
  affiliate: boolean;
  affiliate_fullname?: string;
  affiliate_number?: string;
  is_default: boolean;
}     
const insurances: Insurance[] = [
  {
    uid: '1',
    organization: { name: 'CNOPS' },
    affiliate: true,
    affiliate_fullname: '',
    affiliate_number: '',
    is_default: true,
  },
];
      const patientFullName = 'Dupont Jean';

  const handleAdd = () => {
    console.log('Ajouter assurance');
  };

  const handleEdit = (index: number) => {
    console.log('Modifier assurance à l’index', index);
  };

  const handleRemove = (index: number) => {
    console.log('Supprimer assurance à l’index', index);
  };

  const handleSetDefault = (index: number) => {
    console.log('Définir comme par défaut l’assurance à l’index', index);
  };
  return (
    <>
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
       <Group justify="space-between" align="center">
         <Group>
           {patient ? (
             <Icon path={mdiCardAccountDetails} size={1} />
           ) : (
             <Button variant="subtle" onClick={onGoBack}>
               <Icon path={mdiArrowLeft} size={1} color={"white"}/>
             </Button>
           )}
           <Title order={2}>Fiche patient</Title>
           <DatePickerInput placeholder="Date de création" />
           23/06/2025
         </Group>
   
         {patient && (
           <Group>
             <Text>{patient.full_name}</Text>
             <Text>{patient.gender}</Text>
             <Text>{patient.age}</Text>
             <Text>{patient.default_insurance}</Text>
             <Text>{patient.file_number}</Text>
             <Text>{patient.last_visit}</Text>
           </Group>
         )}
   
         <Group>
            <Group>
                 <Button leftSection={<Icon path={mdiPlaylistEdit} size={1.5}   color={"white"} />} variant="subtle" color='white'
                  onClick={(event) => {
                event.preventDefault();
                toggleAssurancesPatient(); // Toggle sidebar visibility
              }}
                 >
       Assurances patient
      </Button>   
            </Group>
           <Divider size="sm" orientation="vertical" />
           <Menu shadow="md" width={220}>
             <Menu.Target>
               <Button variant="subtle">
                 <Icon path={mdiApps} size={1} color={"white"}/>
               </Button>
             </Menu.Target>
             <Menu.Dropdown>
               <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />}>Alerts</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}>Relations Patient</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
             </Menu.Dropdown>
           </Menu>
   
           <Tooltip label="Contrat">
             <Button variant="subtle" onClick={onGoToContract}>
               <Icon path={mdiCertificate} size={1} color={"white"}/>
             </Button>
           </Tooltip>
   
           <Tooltip label="Liste patients">
             <Button component="a" href="/pratisoft/patient" variant="subtle">
               <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
             </Button>
           </Tooltip>
         </Group>
       </Group>
       
       </div>

    {/* --------------------------Start Content ------------------------------*/}
<div className={isSidebarVisible ?  "hidden": " "}>
 <Stack gap="sm" >
    <Group w={"100%"}>
      <Select
        label="Organisme"
        placeholder="Sélectionner un organisme"
        data={organizationOptions}
        value={selectedInsurance}
        onChange={setSelectedInsurance}
        searchable
        clearable
        nothingFoundMessage="Aucun résultat"
      />

      <TextInput
        label="N° Affiliation"
        value={affiliateNumber}
        onChange={(e) => setAffiliateNumber(e.currentTarget.value)}
      />

      <Radio.Group
        value={affiliateType}
        onChange={(val) => setAffiliateType(val as 'PATIENT' | 'PARTNER' | 'CHILD' | 'PARENT')}
        label="Bénéficiaire (Patient - Assuré)"
      >
        <Group>
          <Radio value="PATIENT" label="Lui même" />
          <Radio value="PARTNER" label="Conjoint" />
          <Radio value="CHILD" label="Enfant" />
          <Radio value="PARENT" label="Parent" />
        </Group>
      </Radio.Group>
      </Group>
    </Stack>
     <Stack gap="sm" >
    <Group w={"100%"}>
 <Group align="end" gap="sm">
      <Select
        label="Titre"
        placeholder="Sélectionner un titre"
        data={titles}
        value={value}
        onChange={onChange}
        searchable
        clearable
        nothingFoundMessage="Aucun résultat"
        w={200}
      />
      {!locked && (
        <Button
          variant="default"
          onClick={handleAdds}
          aria-label="Ajouter un titre"
          size="sm"
        >
          <IconPlus size={16} />
        </Button>
      )}
    </Group>
     <TextInput
      label="Nom"
      value={value}
      onChange={(event) => onChange(capitalize(event.currentTarget.value))}
      placeholder="Entrer le nom"
      withAsterisk
      autoComplete="off"
    />
    <TextInput
      label="Prénom"
      value={value}
      onChange={(event) => onChange(capitalize(event.currentTarget.value))}
      placeholder="Entrer le prénom"
      withAsterisk
      autoComplete="off"
    />
    <DateInput
      label="Date de naissance"
      placeholder="JJ/MM/AAAA"
      value={value}
      onChange={onChange}
      valueFormat="DD/MM/YYYY"
      icon={<IconCalendar size={18} />}
      clearable
      withAsterisk
    />
    <TextInput
  label="CNIE"
  placeholder="Entrez le numéro de CNIE"
  autoComplete="off"
  withAsterisk
  {...form.getInputProps('national_id')}
  onChange={(event) =>
    form.setFieldValue('national_id', event.currentTarget.value.toUpperCase()) // majuscules
  }
/>
 <Group align="end" gap="sm">
 <Select
      label="Ville"
      placeholder="Rechercher"
      searchable
      clearable
      value={value?.id.toString() || null}
      onSearchChange={setSearch}
      onChange={(val) => {
        const selected = cities.find((city) => city.id.toString() === val);
        onChange(selected || null);
      }}
      data={cities.map((c) => ({ value: c.id.toString(), label: c.full_name }))}
      rightSectionWidth={60}
      disabled={disabled || cities.length === 0}
      withAsterisk
    />
     {!locked && (
        <Button
          variant="default"
          onClick={handleAdd}
          aria-label="Ajouter un Ville"
          size="sm"
        >
          <IconPlus size={16} />
        </Button>
      )}
    </Group>

    </Group>
      <Textarea
      label="Adresse"
      placeholder="Saisir l’adresse"
      value={value}
      onChange={(event) => onChange(event.currentTarget.value)}
      autosize
      minRows={1}
      maxRows={4}
      withAsterisk
    />
     </Stack>
     </div>
      {isSidebarVisible && ( 
         <Stack gap="sm" >
     <InsuranceTable 
      insurances={insurances}
      patientFullName={patientFullName}
      onAdd={handleAdd}
      onEdit={handleEdit}
      onRemove={handleRemove}
      onSetDefault={handleSetDefault}
    />
    </Stack>
      )}
{/* -------------------------end  Content---------------------------------*/}
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={onSubmit}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    </>
  )
}

export default Assurance