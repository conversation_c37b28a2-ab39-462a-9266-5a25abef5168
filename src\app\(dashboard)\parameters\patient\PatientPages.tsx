import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto,  } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import Module_patient from './Module_patient'



// Mapping des sous-onglets pour Vente
const subtabMapping: { [key: string]: string } = {
  'Module_patient': 'Module_patient',
'Patient_en_cours': 'Patient_en_cours',
'Questionnaires_patient': 'Questionnaires_patient',
};

const PatientPages = () => {
  const [activeTab, setActiveTab] = useState('Module_patient');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'Module_patient')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="Module_patient" leftSection={<IconPhoto size={12} />}>
             Module patient
           </Tabs.Tab>
           
          
         </Tabs.List>
   
         <Tabs.Panel value="Module_patient" ml={20}>
           <Module_patient/>
         </Tabs.Panel>
   
         
        
       </Tabs>
  )
};



export default PatientPages
