// frontend/dental_medicine/src/components/content/dental/shared/useSmartTreatment.ts

import { useCallback } from 'react';
import { useTreatmentManager } from './useTreatmentManager';

interface UseSmartTreatmentProps {
  onModificationChange?: (svgId: string, pathId: string, isHidden: boolean, highlightedPaths: Record<string, boolean>) => Promise<void>;
  highlightedPaths: Record<string, boolean>;
}

interface UseSmartTreatmentReturn {
  handleSmartTreatment: (treatmentId: string, svgId: string) => Promise<void>;
  conflictDialog: React.ReactNode;
}

/**
 * Hook simplifié pour intégrer le système de traitements intelligents
 * dans les onglets existants sans refactoring majeur
 */
export const useSmartTreatment = ({
  onModificationChange,
  highlightedPaths
}: UseSmartTreatmentProps): UseSmartTreatmentReturn => {
  
  const {
    applyTreatment,
    conflicts,
    isConflictDialogOpen,
    pendingTreatment,
    resolveConflict,
    closeConflictDialog
  } = useTreatmentManager();

  /**
   * Applique un traitement de manière intelligente avec gestion des conflits
   */
  const handleSmartTreatment = useCallback(async (treatmentId: string, svgId: string) => {
    console.log(`🧠 Traitement intelligent: ${treatmentId} sur dent ${svgId}`);
    
    const result = await applyTreatment(treatmentId, svgId);
    
    if (result.success) {
      console.log(`✅ Traitement appliqué avec succès`);
      
      // Appliquer les changements via l'ancien système
      if (onModificationChange) {
        try {
          // Cacher tous les paths qui doivent être cachés
          for (const pathId of result.pathsToHide) {
            await onModificationChange(svgId, pathId, true, highlightedPaths);
          }
          
          // Afficher tous les paths qui doivent être visibles
          for (const pathId of result.pathsToShow) {
            await onModificationChange(svgId, pathId, false, highlightedPaths);
          }
          
          console.log(`💾 Modifications sauvegardées pour la dent ${svgId}`);
        } catch (error) {
          console.error('Erreur lors de la sauvegarde:', error);
        }
      }
    } else if (result.conflicts && result.conflicts.length > 0) {
      console.log(`⚠️ Conflits détectés, dialog affiché`);
      // Le dialog sera affiché automatiquement par useTreatmentManager
    }
  }, [applyTreatment, onModificationChange, highlightedPaths]);

  // Créer le composant de dialog de conflit
  const conflictDialog = React.createElement(
    require('./TreatmentConflictDialog').default,
    {
      isOpen: isConflictDialogOpen,
      conflicts,
      treatmentName: pendingTreatment ? 
        require('./treatmentCompatibility').TREATMENTS[pendingTreatment.treatmentId]?.name || pendingTreatment.treatmentId : '',
      toothNumber: pendingTreatment?.svgId || '',
      onResolve: async (action: 'force' | 'cancel' | 'modify') => {
        const result = await resolveConflict(action);
        
        if (result?.success && onModificationChange) {
          try {
            // Appliquer les changements forcés
            for (const pathId of result.pathsToHide) {
              await onModificationChange(pendingTreatment!.svgId, pathId, true, highlightedPaths);
            }
            
            for (const pathId of result.pathsToShow) {
              await onModificationChange(pendingTreatment!.svgId, pathId, false, highlightedPaths);
            }
            
            console.log(`🔨 Traitement forcé appliqué avec succès`);
          } catch (error) {
            console.error('Erreur lors de l\'application forcée:', error);
          }
        }
      },
      onClose: closeConflictDialog
    }
  );

  return {
    handleSmartTreatment,
    conflictDialog
  };
};

// Version React pour l'import
import React from 'react';
