"use client";
import React, { useState } from 'react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import moment from 'moment';
import AjouterUnRendezVous from './AjouterUnRendezVous';

// Example usage component showing how to use AjouterUnRendezVous with Edit Modal
const ExampleUsageAjouterUnRendezVous = () => {
  // Modal states
  const [opened, setOpened] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);

  // Patient form fields
  const [eventTitle, setEventTitle] = useState('');
  const [patientName, setPatientName] = useState('');
  const [patientlastName, setPatientlastName] = useState('');
  const [eventDateDeNaissance, setEventDateDeNaissance] = useState('');
  const [eventAge, setEventAge] = useState<number | null>(null);
  const [genderOption, setGenderOption] = useState('Homme');
  const [eventEtatCivil, setEventEtatCivil] = useState('');
  const [eventCin, setEventCin] = useState('');
  const [address, setAddress] = useState('');
  const [eventTelephone, setEventTelephone] = useState('');
  const [email, setEmail] = useState('');
  const [patientdoctor, setPatientDocteur] = useState('');
  const [patientsocialSecurity, setSocialSecurity] = useState('');
  const [patienttypeConsultation, setPatientTypeConsultation] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [dureeDeLexamen, setDureeDeLexamen] = useState('15 min');
  const [eventAganda, setEventAganda] = useState('');
  const [eventDate, setEventDate] = useState('');
  const [eventTime, setEventTime] = useState('');
  const [eventConsultation] = useState('15');
  const [patientcomment, setPatientcomment] = useState('');
  const [patientnotes, setPatientNotes] = useState('');
  const [patientcommentairelistedattente, setPatientCommentairelistedattente] = useState('');
  const [eventResourceId, setEventResourceId] = useState<number>(1);
  const [eventType, setEventType] = useState('visit');

  // Options and types
  const [titleOptions, setTitleOptions] = useState([
    { value: "Mr", label: "Mr" },
    { value: "Mlle", label: "Mlle" },
    { value: "Mme", label: "Mme" },
  ]);
  const [newTitle, setNewTitle] = useState("");
  const [consultationTypes, setConsultationTypes] = useState([
    { value: 'Consultation', label: 'Consultation', duration: '15min' },
    { value: 'Contrôle', label: 'Contrôle', duration: '15min' },
    { value: 'Urgence', label: 'Urgence', duration: '30min' },
  ]);
  const [newConsultationType, setNewConsultationType] = useState("");
  const [newConsultationColor, setNewConsultationColor] = useState("#3799CE");
  const [ColorPickeropened, setColorPickeropened] = useState(false);
  const [changeEndValue, setChangeEndValue] = useState('#FFFFFF');
  const [agendaTypes, setAgendaTypes] = useState([
    { value: "Cabinet", label: "Cabinet" },
    { value: "Center", label: "Center" },
  ]);
  const [newAgendaType, setNewAgendaType] = useState("");

  // Switches
  const [checkedAppelvideo, setCheckedAppelvideo] = useState(false);
  const [checkedRappelSms, setCheckedRappelSms] = useState(false);
  const [checkedRappelEmail, setCheckedRappelEmail] = useState(false);

  // Lists and modals
  const [ListRendezVousOpened, setListRendezVousOpened] = useState(false);
  const [currentPatient] = useState(null);
  const [waitingList, setWaitingList] = useState([]);

  // Initial consultation types from CetteJournee.tsx
  const initialConsultationTypes = [
    { value: '1er Consultation', label: '1er Consultation', duration: '15min' },
    { value: "Changement D'élastique'", label: "Changement D'élastique'", duration: '15min' },
    { value: 'Chirurgie/Paro', label: 'Chirurgie/Paro', duration: '30min' },
    { value: 'Collage', label: 'Collage', duration: '30min' },
    { value: 'Composite', label: 'Composite', duration: '15min' },
    { value: 'Consultation', label: 'Consultation', duration: '15min' },
    { value: 'Contention', label: 'Contention', duration: '30min' },
    { value: 'Contrôle', label: 'Contrôle', duration: '15min' },
  ];

  // Forms
  const appointmentForm = useForm({
    initialValues: {
      patientId: '',
      notes: '',
      date: new Date(),
      duration: 15,
      type: "visit",
      resourceId: Number(eventResourceId),
      addToWaitingList: false,
      removeFromCalendar: false,
      rescheduleDateTime: '',
    },
  });

  // Event handlers
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const birthDate = e.target.value;
    setEventDateDeNaissance(birthDate);
    
    if (birthDate) {
      const today = moment();
      const birth = moment(birthDate);
      const age = today.diff(birth, 'years');
      setEventAge(age);
    } else {
      setEventAge(null);
    }
  };

  const handleOptionChange = (value: string) => {
    setGenderOption(value);
  };

  const handleAppelvideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCheckedAppelvideo(e.currentTarget.checked);
  };

  const handleRappelSmsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCheckedRappelSms(e.currentTarget.checked);
  };

  const handleRappelEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCheckedRappelEmail(e.currentTarget.checked);
  };

  const getEventTypeColor = (type: string) => {
    const colors = {
      'visit': "#34D1BF",
      'visitor-counter': "#F17105", 
      'completed': "#3799CE",
      'diagnosis': "#F3124E",
    };
    return colors[type as keyof typeof colors] || "#3799CE";
  };

  const resetForm = () => {
    setPatientName('');
    setPatientlastName('');
    setEventType("visit");
    setEventEtatCivil('');
    setPatientDocteur('');
    setEventTitle('');
    setEventAganda('');
    setPatientTypeConsultation('');
    setSearchValue('');
    setPatientCommentairelistedattente('');
    setPatientcomment('');
    setEventTelephone('');
    setSocialSecurity('');
    setPatientNotes('');
    setEmail('');
    setAddress('');
    setEventConsultation('15');
    setEventDateDeNaissance('');
    setEventAge(null);
    setGenderOption('Homme');
    setEventCin('');
    setDureeDeLexamen('15 min');
    setEventDate(moment().format('YYYY-MM-DD'));
    setEventTime(moment().format('HH:mm'));
  };

  const handleSubmit = (values: any) => {
    console.log('Form submitted:', values);
    notifications.show({
      title: 'Rendez-vous ajouté',
      message: 'Le rendez-vous a été ajouté avec succès',
      color: 'green',
      autoClose: 2000
    });
    setOpened(false);
    resetForm();
  };

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Edit form submitted');
    notifications.show({
      title: 'Rendez-vous modifié',
      message: 'Le rendez-vous a été modifié avec succès',
      color: 'blue',
      autoClose: 2000
    });
    setShowEditModal(false);
    resetForm();
  };

  const closeRendezVous = () => {
    setShowEditModal(false);
    resetForm();
  };

  const openListDesPatient = () => {
    console.log('Opening patient list...');
  };

  const openListRendezVous = () => {
    setListRendezVousOpened(true);
  };

  const closeListRendezVous = () => {
    setListRendezVousOpened(false);
  };

  const openedColorPicker = () => {
    setColorPickeropened(true);
  };

  const closeColorPicker = () => {
    setColorPickeropened(false);
  };

  const setPatientModalOpen = (open: boolean) => {
    console.log('Patient modal open:', open);
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Example: AjouterUnRendezVous with Edit Modal</h1>
      
      <div className="space-y-4">
        <button
          onClick={() => setOpened(true)}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Ouvrir Modal Ajouter Rendez-vous
        </button>
        
        <button
          onClick={() => {
            // Simulate selecting an event for editing
            setSelectedEvent({
              id: '1',
              first_name: 'John',
              last_name: 'Doe',
              phone_numbers: '**********',
              email: '<EMAIL>',
              // Add other event properties as needed
            });
            setShowEditModal(true);
          }}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Ouvrir Modal Modifier Rendez-vous
        </button>
      </div>

      <AjouterUnRendezVous
        opened={opened}
        onClose={() => setOpened(false)}
        appointmentForm={appointmentForm}
        handleSubmit={handleSubmit}
        eventTitle={eventTitle}
        setEventTitle={setEventTitle}
        titleOptions={titleOptions}
        setTitleOptions={setTitleOptions}
        newTitle={newTitle}
        setNewTitle={setNewTitle}
        patientName={patientName}
        setPatientName={setPatientName}
        patientlastName={patientlastName}
        setPatientlastName={setPatientlastName}
        openListDesPatient={openListDesPatient}
        eventDateDeNaissance={eventDateDeNaissance}
        handleDateChange={handleDateChange}
        eventAge={eventAge}
        genderOption={genderOption}
        handleOptionChange={handleOptionChange}
        eventEtatCivil={eventEtatCivil}
        setEventEtatCivil={setEventEtatCivil}
        eventCin={eventCin}
        setEventCin={setEventCin}
        address={address}
        setAddress={setAddress}
        eventTelephone={eventTelephone}
        setEventTelephone={setEventTelephone}
        email={email}
        setEmail={setEmail}
        patientdoctor={patientdoctor}
        setPatientDocteur={setPatientDocteur}
        patientsocialSecurity={patientsocialSecurity}
        setSocialSecurity={setSocialSecurity}
        consultationTypes={consultationTypes}
        setConsultationTypes={setConsultationTypes}
        patienttypeConsultation={patienttypeConsultation}
        setPatientTypeConsultation={setPatientTypeConsultation}
        setEventType={setEventType}
        searchValue={searchValue}
        setSearchValue={setSearchValue}
        dureeDeLexamen={dureeDeLexamen}
        getEventTypeColor={getEventTypeColor}
        newConsultationType={newConsultationType}
        setNewConsultationType={setNewConsultationType}
        newConsultationColor={newConsultationColor}
        setNewConsultationColor={setNewConsultationColor}
        ColorPickeropened={ColorPickeropened}
        openedColorPicker={openedColorPicker}
        closeColorPicker={closeColorPicker}
        changeEndValue={changeEndValue}
        setChangeEndValue={setChangeEndValue}
        setDureeDeLexamen={setDureeDeLexamen}
        eventAganda={eventAganda}
        setEventAganda={setEventAganda}
        agendaTypes={agendaTypes}
        setAgendaTypes={setAgendaTypes}
        newAgendaType={newAgendaType}
        setNewAgendaType={setNewAgendaType}
        isWaitingList={false}
        eventDate={eventDate}
        setEventDate={setEventDate}
        eventTime={eventTime}
        setEventTime={setEventTime}
        eventConsultation={eventConsultation}
        openListRendezVous={openListRendezVous}
        ListRendezVousOpened={ListRendezVousOpened}
        closeListRendezVous={closeListRendezVous}
        patientcomment={patientcomment}
        setPatientcomment={setPatientcomment}
        patientnotes={patientnotes}
        setPatientNotes={setPatientNotes}
        patientcommentairelistedattente={patientcommentairelistedattente}
        setPatientCommentairelistedattente={setPatientCommentairelistedattente}
        eventResourceId={eventResourceId}
        setEventResourceId={setEventResourceId}
        eventType={eventType}
        checkedAppelvideo={checkedAppelvideo}
        handleAppelvideoChange={handleAppelvideoChange}
        checkedRappelSms={checkedRappelSms}
        handleRappelSmsChange={handleRappelSmsChange}
        checkedRappelEmail={checkedRappelEmail}
        handleRappelEmailChange={handleRappelEmailChange}
        currentPatient={currentPatient}
        waitingList={waitingList}
        setWaitingList={setWaitingList}
        setPatientModalOpen={setPatientModalOpen}
        notifications={notifications}
        // New props for Edit Modal
        showEditModal={showEditModal}
        setShowEditModal={setShowEditModal}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        resetForm={resetForm}
        handleEditSubmit={handleEditSubmit}
        closeRendezVous={closeRendezVous}
        initialConsultationTypes={initialConsultationTypes}
      />
    </div>
  );
};

export default ExampleUsageAjouterUnRendezVous;
