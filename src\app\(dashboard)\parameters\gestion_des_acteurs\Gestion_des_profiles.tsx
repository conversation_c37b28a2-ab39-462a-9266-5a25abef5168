import React, { useState, useEffect } from 'react';
import {
  Stack,
  Title,
  TextInput,
  Button,
  Table,
  Group,
  ActionIcon,
  Paper,
  Tooltip,
  Modal,
  Checkbox,
  Text,
  Badge,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconUsers,
  IconCheck,
  IconX,
} from '@tabler/icons-react';

// Types
interface ProfilePermissions {
  consultation: boolean;
  prescription: boolean;
  facturation: boolean;
  pharmacie: boolean;
  laboratoire: boolean;
  imagerie: boolean;
  hospitalisation: boolean;
  administration: boolean;
}

interface Profile {
  id: string;
  name: string;
  description: string;
  permissions: ProfilePermissions;
  userCount: number;
  isActive: boolean;
}

// Mock data for profiles
const mockProfiles: Profile[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Accès complet à toutes les fonctionnalités',
    permissions: {
      consultation: true,
      prescription: true,
      facturation: true,
      pharmacie: true,
      laboratoire: true,
      imagerie: true,
      hospitalisation: true,
      administration: true,
    },
    userCount: 3,
    isActive: true,
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Accès aux consultations et prescriptions',
    permissions: {
      consultation: true,
      prescription: true,
      facturation: true,
      pharmacie: false,
      laboratoire: true,
      imagerie: true,
      hospitalisation: false,
      administration: false,
    },
    userCount: 8,
    isActive: true,
  },
  {
    id: '3',
    name: 'Infirmier',
    description: 'Accès limité aux soins infirmiers',
    permissions: {
      consultation: true,
      prescription: false,
      facturation: false,
      pharmacie: true,
      laboratoire: false,
      imagerie: false,
      hospitalisation: true,
      administration: false,
    },
    userCount: 12,
    isActive: true,
  },
  {
    id: '4',
    name: 'Pharmacien',
    description: 'Gestion de la pharmacie et des médicaments',
    permissions: {
      consultation: false,
      prescription: false,
      facturation: true,
      pharmacie: true,
      laboratoire: false,
      imagerie: false,
      hospitalisation: false,
      administration: false,
    },
    userCount: 2,
    isActive: true,
  },
  {
    id: '5',
    name: 'Secrétaire Médicale',
    description: 'Gestion administrative et facturation',
    permissions: {
      consultation: true,
      prescription: false,
      facturation: true,
      pharmacie: false,
      laboratoire: false,
      imagerie: false,
      hospitalisation: false,
      administration: true,
    },
    userCount: 5,
    isActive: true,
  },
  {
    id: '6',
    name: 'Technicien Laboratoire',
    description: 'Gestion des analyses de laboratoire',
    permissions: {
      consultation: false,
      prescription: false,
      facturation: false,
      pharmacie: false,
      laboratoire: true,
      imagerie: false,
      hospitalisation: false,
      administration: false,
    },
    userCount: 3,
    isActive: false,
  },
];

const Gestion_des_profiles = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [modalOpened, setModalOpened] = useState(false);
  const [editingProfile, setEditingProfile] = useState<Profile | null>(null);
  const [newProfile, setNewProfile] = useState({
    name: '',
    description: '',
    permissions: {
      consultation: false,
      prescription: false,
      facturation: false,
      pharmacie: false,
      laboratoire: false,
      imagerie: false,
      hospitalisation: false,
      administration: false,
    }
  });

  useEffect(() => {
    setProfiles(mockProfiles);
  }, []);

  // Filter profiles based on search query
  const filteredProfiles = profiles.filter((profile: Profile) =>
    profile.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    profile.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Render permission icon
  const renderPermissionIcon = (hasPermission: boolean) => (
    hasPermission ? (
      <IconCheck size={16} className="text-green-600" />
    ) : (
      <IconX size={16} className="text-red-500" />
    )
  );

  // Handle edit profile
  const handleEditProfile = (profile: Profile) => {
    setEditingProfile(profile);
    setNewProfile({
      name: profile.name,
      description: profile.description,
      permissions: { ...profile.permissions }
    });
    setModalOpened(true);
  };

  // Reset form
  const resetForm = () => {
    setNewProfile({
      name: '',
      description: '',
      permissions: {
        consultation: false,
        prescription: false,
        facturation: false,
        pharmacie: false,
        laboratoire: false,
        imagerie: false,
        hospitalisation: false,
        administration: false,
      }
    });
    setEditingProfile(null);
  };

  return (
    <Stack gap="lg" className="w-full">
      {/* Header */}
      <Paper p="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUsers size={24} className="text-blue-600" />
            <Title order={3} className="text-gray-700">
              Gestion des profils
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="blue"
            size="sm"
            onClick={() => {
              resetForm();
              setModalOpened(true);
            }}
          >
            Nouveau profil
          </Button>
        </Group>
      </Paper>

      {/* Search */}
      <Paper p="md" withBorder>
        <TextInput
          placeholder="Rechercher un profil..."
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(event) => setSearchQuery(event.currentTarget.value)}
          className="w-full max-w-md"
        />
      </Paper>

      {/* Table */}
      <Paper withBorder>
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr className="bg-gray-50">
              <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                Nom du profil
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                Description
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Consultation
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Prescription
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Facturation
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Pharmacie
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Laboratoire
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Imagerie
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Hospitalisation
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Administration
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Utilisateurs
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Statut
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredProfiles.map((profile) => (
              <Table.Tr key={profile.id} className="hover:bg-gray-50">
                <Table.Td className="font-medium border-r border-gray-200">
                  {profile.name}
                </Table.Td>
                <Table.Td className="border-r border-gray-200 max-w-xs">
                  <Text size="sm" className="truncate">
                    {profile.description}
                  </Text>
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.consultation)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.prescription)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.facturation)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.pharmacie)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.laboratoire)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.imagerie)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.hospitalisation)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionIcon(profile.permissions.administration)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  <Badge variant="light" color="blue" size="sm">
                    {profile.userCount}
                  </Badge>
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  <Badge
                    variant="light"
                    color={profile.isActive ? "green" : "red"}
                    size="sm"
                  >
                    {profile.isActive ? "Actif" : "Inactif"}
                  </Badge>
                </Table.Td>
                <Table.Td className="text-center">
                  <Group gap="xs" justify="center">
                    <Tooltip label="Modifier">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        size="sm"
                        onClick={() => handleEditProfile(profile)}
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
                    </Tooltip>
                    <Tooltip label="Supprimer">
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        size="sm"
                        onClick={() => {
                          // Handle delete action
                          console.log('Delete profile:', profile.id);
                        }}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>

      {/* Modal for adding/editing profile */}
      <Modal
        opened={modalOpened}
        onClose={() => {
          setModalOpened(false);
          resetForm();
        }}
        title={editingProfile ? "Modifier le profil" : "Nouveau profil"}
        size="lg"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Nom du profil"
            placeholder="Ex: Médecin Chef, Infirmier..."
            value={newProfile.name}
            onChange={(event) =>
              setNewProfile({ ...newProfile, name: event.currentTarget.value })
            }
            required
          />

          <TextInput
            label="Description"
            placeholder="Description du rôle et des responsabilités..."
            value={newProfile.description}
            onChange={(event) =>
              setNewProfile({ ...newProfile, description: event.currentTarget.value })
            }
            required
          />

          <Text size="sm" fw={500} mt="md">
            Permissions :
          </Text>

          <Group gap="md">
            <Checkbox
              label="Consultation"
              checked={newProfile.permissions.consultation}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, consultation: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Prescription"
              checked={newProfile.permissions.prescription}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, prescription: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Facturation"
              checked={newProfile.permissions.facturation}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, facturation: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Pharmacie"
              checked={newProfile.permissions.pharmacie}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, pharmacie: event.currentTarget.checked }
                })
              }
            />
          </Group>

          <Group gap="md">
            <Checkbox
              label="Laboratoire"
              checked={newProfile.permissions.laboratoire}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, laboratoire: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Imagerie"
              checked={newProfile.permissions.imagerie}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, imagerie: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Hospitalisation"
              checked={newProfile.permissions.hospitalisation}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, hospitalisation: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Administration"
              checked={newProfile.permissions.administration}
              onChange={(event) =>
                setNewProfile({
                  ...newProfile,
                  permissions: { ...newProfile.permissions, administration: event.currentTarget.checked }
                })
              }
            />
          </Group>

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => {
                setModalOpened(false);
                resetForm();
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                if (editingProfile) {
                  // Update existing profile
                  const updatedProfiles = profiles.map(p =>
                    p.id === editingProfile.id
                      ? { ...p, name: newProfile.name, description: newProfile.description, permissions: newProfile.permissions }
                      : p
                  );
                  setProfiles(updatedProfiles);
                } else {
                  // Add new profile
                  const newId = (profiles.length + 1).toString();
                  const profile: Profile = {
                    id: newId,
                    name: newProfile.name,
                    description: newProfile.description,
                    permissions: newProfile.permissions,
                    userCount: 0,
                    isActive: true
                  };
                  setProfiles([...profiles, profile]);
                }

                // Reset form and close modal
                resetForm();
                setModalOpened(false);
              }}
              disabled={!newProfile.name || !newProfile.description}
            >
              {editingProfile ? "Modifier" : "Ajouter"}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default Gestion_des_profiles;
