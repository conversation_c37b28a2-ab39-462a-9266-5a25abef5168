// frontend/dental_medicine/src/components/content/dental/shared/SaveManager.tsx

import React, { forwardRef, useImperativeHandle, useCallback } from 'react';
import { notifications } from '@mantine/notifications';
import { 
  SaveManagerRef, 
  SaveResult, 
  SaveOptions, 
  NotificationConfig,
  ModificationState 
} from './types';

interface SaveManagerProps {
  modificationState: ModificationState;
  onModificationChange?: (
    svgId: string, 
    pathId: string, 
    isVisible: boolean, 
    highlightedPaths?: Record<string, any>
  ) => Promise<void>;
  specialty: string;
  onSaveComplete?: (result: SaveResult) => void;
}

export const SaveManager = forwardRef<SaveManagerRef, SaveManagerProps>(({
  modificationState,
  onModificationChange,
  specialty,
  onSaveComplete
}, ref) => {

  // Vérifier s'il y a des modifications non sauvegardées
  const hasUnsavedChanges = useCallback((): boolean => {
    const { hiddenPaths, highlightedPaths } = modificationState;
    return Object.keys(hiddenPaths).length > 0 || Object.keys(highlightedPaths).length > 0;
  }, [modificationState]);

  // Fonction principale de sauvegarde
  const triggerSave = useCallback(async (options: SaveOptions = {}): Promise<SaveResult> => {
    const {
      showNotification = true,
      closeDialog = true,
      validateData = true
    } = options;

    try {
      console.log(`🔄 [${specialty}] Début de la sauvegarde...`);

      const { hiddenPaths, highlightedPaths } = modificationState;

      // Validation des données si demandée
      if (validateData) {
        if (!hasUnsavedChanges()) {
          const result: SaveResult = {
            success: true,
            message: 'Aucune modification à sauvegarder',
            savedCount: 0
          };

          if (showNotification) {
            showNotificationMessage({
              title: '💡 Information',
              message: 'Aucune modification à sauvegarder',
              type: 'info'
            });
          }

          return result;
        }
      }

      // Compter les modifications à sauvegarder
      const modificationsToSave = new Set([
        ...Object.keys(hiddenPaths),
        ...Object.keys(highlightedPaths)
      ]);

      let savedCount = 0;
      const errors: string[] = [];

      // Sauvegarder via le système réactif unifié
      if (onModificationChange) {
        console.log(`📝 [${specialty}] Sauvegarde via le système réactif...`);

        for (const key of modificationsToSave) {
          try {
            const [svgId, pathId] = key.split('-');
            const isVisible = !hiddenPaths[key];
            const pathStyle = highlightedPaths[key] || {};

            console.log(`  📌 Sauvegarde: SVG ${svgId}, Path ${pathId}, Visible: ${isVisible}`);
            
            await onModificationChange(svgId, pathId, isVisible, { [key]: pathStyle });
            savedCount++;
            
          } catch (error) {
            const errorMsg = `Erreur sur ${key}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`;
            console.error(`❌ [${specialty}] ${errorMsg}`);
            errors.push(errorMsg);
          }
        }
      } else {
        // Fallback: sauvegarde directe via API
        console.warn(`⚠️ [${specialty}] onModificationChange non disponible, sauvegarde directe`);
        
        const payload = Array.from(modificationsToSave).map(key => {
          const [svgId, pathId] = key.split('-');
          const pathStyle = highlightedPaths[key] || {};
          
          return {
            svg_id: svgId,
            path_id: pathId,
            fill_color: pathStyle.fill || null,
            stroke_color: pathStyle.stroke || null,
            is_visible: !hiddenPaths[key],
            specialty: specialty
          };
        });

        try {
          const response = await fetch('http://localhost:8000/api/tooth-modifications/', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          savedCount = payload.length;
          console.log(`✅ [${specialty}] Sauvegarde directe réussie: ${savedCount} modifications`);
          
        } catch (error) {
          const errorMsg = `Erreur de sauvegarde directe: ${error instanceof Error ? error.message : 'Erreur inconnue'}`;
          console.error(`❌ [${specialty}] ${errorMsg}`);
          errors.push(errorMsg);
        }
      }

      // Préparer le résultat
      const result: SaveResult = {
        success: errors.length === 0,
        message: errors.length === 0 
          ? `${savedCount} modifications sauvegardées avec succès`
          : `${savedCount} modifications sauvegardées, ${errors.length} erreurs`,
        savedCount,
        errors: errors.length > 0 ? errors : undefined
      };

      // Afficher la notification si demandée
      if (showNotification) {
        showNotificationMessage({
          title: result.success ? '✅ Sauvegarde réussie' : '⚠️ Sauvegarde partielle',
          message: result.message,
          type: result.success ? 'success' : 'warning'
        });
      }

      // Callback de fin de sauvegarde
      if (onSaveComplete) {
        onSaveComplete(result);
      }

      console.log(`🎉 [${specialty}] Sauvegarde terminée:`, result);
      return result;

    } catch (error) {
      const errorMsg = `Erreur critique lors de la sauvegarde: ${error instanceof Error ? error.message : 'Erreur inconnue'}`;
      console.error(`💥 [${specialty}] ${errorMsg}`);

      const result: SaveResult = {
        success: false,
        message: errorMsg,
        savedCount: 0,
        errors: [errorMsg]
      };

      if (showNotification) {
        showNotificationMessage({
          title: '❌ Erreur de sauvegarde',
          message: errorMsg,
          type: 'error'
        });
      }

      if (onSaveComplete) {
        onSaveComplete(result);
      }

      return result;
    }
  }, [modificationState, onModificationChange, specialty, onSaveComplete, hasUnsavedChanges]);

  // Fonction utilitaire pour afficher les notifications
  const showNotificationMessage = (config: NotificationConfig) => {
    notifications.show({
      title: config.title,
      message: config.message,
      color: config.type === 'success' ? 'green' : 
             config.type === 'error' ? 'red' : 
             config.type === 'warning' ? 'yellow' : 'blue',
      autoClose: config.autoClose || (config.type === 'error' ? 7000 : 4000),
      position: config.position || 'top-right'
    });
  };

  // Exposer les méthodes via ref
  useImperativeHandle(ref, () => ({
    triggerSave: () => triggerSave(),
    hasUnsavedChanges
  }), [triggerSave, hasUnsavedChanges]);

  // Ce composant ne rend rien visuellement
  return null;
});

SaveManager.displayName = 'SaveManager';

// Hook personnalisé pour utiliser le SaveManager
export const useSaveManager = (
  modificationState: ModificationState,
  onModificationChange?: SaveManagerProps['onModificationChange'],
  specialty: string = 'unknown'
) => {
  const saveManagerRef = React.useRef<SaveManagerRef>(null);

  const save = useCallback(async (options?: SaveOptions): Promise<SaveResult> => {
    if (saveManagerRef.current) {
      return await saveManagerRef.current.triggerSave();
    }
    
    return {
      success: false,
      message: 'SaveManager non initialisé',
      savedCount: 0,
      errors: ['SaveManager non initialisé']
    };
  }, []);

  const hasChanges = useCallback((): boolean => {
    return saveManagerRef.current?.hasUnsavedChanges() || false;
  }, []);

  return {
    saveManagerRef,
    save,
    hasChanges,
    SaveManagerComponent: (
      <SaveManager
        ref={saveManagerRef}
        modificationState={modificationState}
        onModificationChange={onModificationChange}
        specialty={specialty}
      />
    )
  };
};

export default SaveManager;
