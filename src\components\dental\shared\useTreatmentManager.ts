// frontend/dental_medicine/src/components/content/dental/shared/useTreatmentManager.ts

import { useState, useCallback } from 'react';
import { treatmentManager, TreatmentConflict, TreatmentApplication } from './treatmentManager';

interface UseTreatmentManagerReturn {
  applyTreatment: (treatmentId: string, svgId: string) => Promise<TreatmentApplication>;
  removeTreatment: (treatmentId: string, svgId: string) => TreatmentApplication;
  getCurrentTreatments: (svgId: string) => string[];
  conflicts: TreatmentConflict[];
  isConflictDialogOpen: boolean;
  pendingTreatment: { treatmentId: string; svgId: string } | null;
  resolveConflict: (action: 'force' | 'cancel' | 'modify') => Promise<TreatmentApplication | null>;
  closeConflictDialog: () => void;
}

export const useTreatmentManager = (): UseTreatmentManagerReturn => {
  const [conflicts, setConflicts] = useState<TreatmentConflict[]>([]);
  const [isConflictDialogOpen, setIsConflictDialogOpen] = useState(false);
  const [pendingTreatment, setPendingTreatment] = useState<{ treatmentId: string; svgId: string } | null>(null);

  const applyTreatment = useCallback(async (treatmentId: string, svgId: string): Promise<TreatmentApplication> => {
    console.log(`🦷 Tentative d'application du traitement ${treatmentId} sur la dent ${svgId}`);
    
    // Vérifier les conflits
    const detectedConflicts = treatmentManager.canApplyTreatment(treatmentId, svgId);
    
    if (detectedConflicts.length > 0) {
      console.log(`⚠️ Conflits détectés pour ${treatmentId}:`, detectedConflicts);
      
      // Afficher le dialog de conflit
      setConflicts(detectedConflicts);
      setPendingTreatment({ treatmentId, svgId });
      setIsConflictDialogOpen(true);
      
      // Retourner un résultat en attente
      return {
        success: false,
        conflicts: detectedConflicts,
        pathsToShow: [],
        pathsToHide: []
      };
    }
    
    // Pas de conflits, appliquer directement
    const result = treatmentManager.applyTreatment(treatmentId, svgId);
    console.log(`✅ Traitement ${treatmentId} appliqué avec succès sur la dent ${svgId}`);
    
    return result;
  }, []);

  const removeTreatment = useCallback((treatmentId: string, svgId: string): TreatmentApplication => {
    console.log(`🗑️ Suppression du traitement ${treatmentId} de la dent ${svgId}`);
    return treatmentManager.removeTreatment(treatmentId, svgId);
  }, []);

  const getCurrentTreatments = useCallback((svgId: string): string[] => {
    return treatmentManager.getCurrentTreatments(svgId);
  }, []);

  const resolveConflict = useCallback(async (action: 'force' | 'cancel' | 'modify'): Promise<TreatmentApplication | null> => {
    if (!pendingTreatment) return null;

    const { treatmentId, svgId } = pendingTreatment;

    switch (action) {
      case 'force':
        console.log(`🔨 Forçage du traitement ${treatmentId} sur la dent ${svgId}`);
        const result = treatmentManager.applyTreatment(treatmentId, svgId, true);
        setIsConflictDialogOpen(false);
        setPendingTreatment(null);
        setConflicts([]);
        return result;

      case 'cancel':
        console.log(`❌ Annulation du traitement ${treatmentId} sur la dent ${svgId}`);
        setIsConflictDialogOpen(false);
        setPendingTreatment(null);
        setConflicts([]);
        return {
          success: false,
          pathsToShow: [],
          pathsToHide: []
        };

      case 'modify':
        console.log(`🔄 Modification de la séquence pour ${treatmentId}`);
        // Pour l'instant, on ferme juste le dialog
        // Dans une version future, on pourrait ouvrir un assistant de séquence
        setIsConflictDialogOpen(false);
        setPendingTreatment(null);
        setConflicts([]);
        return {
          success: false,
          pathsToShow: [],
          pathsToHide: []
        };

      default:
        return null;
    }
  }, [pendingTreatment]);

  const closeConflictDialog = useCallback(() => {
    setIsConflictDialogOpen(false);
    setPendingTreatment(null);
    setConflicts([]);
  }, []);

  return {
    applyTreatment,
    removeTreatment,
    getCurrentTreatments,
    conflicts,
    isConflictDialogOpen,
    pendingTreatment,
    resolveConflict,
    closeConflictDialog
  };
};
