'use client';

import React, { useState } from 'react';
import {
  Title,
  Text,
  Paper,
  Tabs,
  Button,
  Group,
  Stack,
  FileInput,
  Alert,
  List,

  Progress,
  Code,
  Accordion,
 
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconUpload,
  IconFileImport,
  IconUsers,
  IconMapPin,
  IconAlertCircle,
  IconCheck,
  
  IconInfoCircle,
} from '@tabler/icons-react';
import dataMigrationService from '@/services/dataMigrationService';



const DataMigrationPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string | null>('patients');
  const [patientFile, setPatientFile] = useState<File | null>(null);
  const [locationFile, setLocationFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    message: string;
    details?: string;
    stats?: {
      total: number;
      imported: number;
      skipped: number;
      errors: number;
    };
  } | null>(null);

  const handlePatientUpload = async () => {
    if (!patientFile) {
      notifications.show({
        title: 'Error',
        message: 'Please select a file to upload',
        color: 'red',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadResult(null);

    try {
      // Import the patient data using the data migration service
      const result = await dataMigrationService.importPatientData(
        patientFile,
        (progress) => setUploadProgress(progress)
      );

      setUploadResult({
        success: result.success,
        message: result.message,
        stats: result.stats,
        details: result.details
      });
    } catch (error) {
      setUploadProgress(0);
      setUploadResult({
        success: false,
        message: 'Error uploading file',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleLocationUpload = async () => {
    if (!locationFile) {
      notifications.show({
        title: 'Error',
        message: 'Please select a file to upload',
        color: 'red',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadResult(null);

    try {
      // Import the location data using the data migration service
      const result = await dataMigrationService.importLocationData(
        locationFile,
        (progress) => setUploadProgress(progress)
      );

      setUploadResult({
        success: result.success,
        message: result.message,
        stats: result.stats,
        details: result.details
      });
    } catch (error) {
      setUploadProgress(0);
      setUploadResult({
        success: false,
        message: 'Error uploading file',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Paper withBorder p="xl" radius="md">
      <Title order={3} mb="md">Data Migration</Title>
      <Text c="dimmed" mb="xl">
        Import data from other systems or update location data
      </Text>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="patients" leftSection={<IconUsers size={16} />}>
            Patient Import
          </Tabs.Tab>
          <Tabs.Tab value="locations" leftSection={<IconMapPin size={16} />}>
            Location Data
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="patients" pt="md">
          <Stack>
            <Text>
              Import patient data from App X or other systems. Supported formats: JSON, Excel (.xlsx), and CSV.
            </Text>

            <Alert icon={<IconInfoCircle size={16} />} title="Before you begin" color="blue" mb="md">
              <List size="sm">
                <List.Item>Export your patient data from App X in JSON, Excel, or CSV format</List.Item>
                <List.Item>Make sure the file includes patient names, contact information, and medical history</List.Item>
                <List.Item>Patient records with the same email address will be updated rather than duplicated</List.Item>
                <List.Item>Sensitive data should be handled according to privacy regulations</List.Item>
              </List>
            </Alert>

            <Accordion>
              <Accordion.Item value="format">
                <Accordion.Control>Expected File Format</Accordion.Control>
                <Accordion.Panel>
                  <Text size="sm" mb="xs">Your file should contain the following fields:</Text>
                  <Code block>
{`{
  "patients": [
    {
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone_number": "************",
      "date_of_birth": "1980-01-01",
      "gender": "male",
      "address": "123 Main St, Anytown, USA",
      "medical_history": "No significant medical history"
    },
    ...
  ]
}`}
                  </Code>
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>

            <FileInput
              label="Upload Patient Data"
              placeholder="Click to select file or drag and drop"
              accept=".json,.xlsx,.csv"
              leftSection={<IconFileImport size={16} />}
              value={patientFile}
              onChange={setPatientFile}
              clearable
            />

            {isUploading && (
              <Stack gap="xs">
                <Text size="sm">Uploading and processing file...</Text>
                <Progress value={uploadProgress} size="sm" striped animated />
              </Stack>
            )}

            {uploadResult && activeTab === 'patients' && (
              <Alert
                icon={uploadResult.success ? <IconCheck size={16} /> : <IconAlertCircle size={16} />}
                title={uploadResult.message}
                color={uploadResult.success ? 'green' : 'red'}
              >
                {uploadResult.details && <Text size="sm">{uploadResult.details}</Text>}

                {uploadResult.stats && (
                  <Group mt="md">
                    <Stack gap={5}>
                      <Text size="sm" fw={500}>Total records:</Text>
                      <Text size="sm" fw={500}>Imported:</Text>
                      <Text size="sm" fw={500}>Skipped:</Text>
                      <Text size="sm" fw={500}>Errors:</Text>
                    </Stack>
                    <Stack gap={5}>
                      <Text size="sm">{uploadResult.stats.total}</Text>
                      <Text size="sm" c="green">{uploadResult.stats.imported}</Text>
                      <Text size="sm" c="yellow">{uploadResult.stats.skipped}</Text>
                      <Text size="sm" c="red">{uploadResult.stats.errors}</Text>
                    </Stack>
                  </Group>
                )}
              </Alert>
            )}

            <Group justify="flex-end">
              <Button
                leftSection={<IconUpload size={16} />}
                onClick={handlePatientUpload}
                loading={isUploading}
                disabled={!patientFile}
              >
                Upload and Import
              </Button>
            </Group>
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="locations" pt="md">
          <Stack>
            <Text>
              Update the system&apos;s location data (countries, regions, and cities) by uploading a JSON file.
            </Text>

            <Alert icon={<IconInfoCircle size={16} />} title="Location Data Structure" color="blue" mb="md">
              <List size="sm">
                <List.Item>Upload a JSON file containing countries, regions, and cities</List.Item>
                <List.Item>Each country can have multiple regions, and each region can have multiple cities</List.Item>
                <List.Item>Existing locations with the same name will be updated rather than duplicated</List.Item>
              </List>
            </Alert>

            <Accordion>
              <Accordion.Item value="format">
                <Accordion.Control>Expected JSON Format</Accordion.Control>
                <Accordion.Panel>
                  <Code block>
{`{
  "countries": [
    {
      "name": "United States",
      "code": "US",
      "phone_code": "+1",
      "regions": [
        {
          "name": "California",
          "code": "CA",
          "cities": [
            {
              "name": "Los Angeles",
              "latitude": 34.0522,
              "longitude": -118.2437
            },
            {
              "name": "San Francisco",
              "latitude": 37.7749,
              "longitude": -122.4194
            }
          ]
        }
      ]
    }
  ]
}`}
                  </Code>
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>

            <FileInput
              label="Upload Location Data"
              placeholder="Click to select JSON file or drag and drop"
              accept=".json"
              leftSection={<IconFileImport size={16} />}
              value={locationFile}
              onChange={setLocationFile}
              clearable
            />

            {isUploading && (
              <Stack gap="xs">
                <Text size="sm">Uploading and processing file...</Text>
                <Progress value={uploadProgress} size="sm" striped animated />
              </Stack>
            )}

            {uploadResult && activeTab === 'locations' && (
              <Alert
                icon={uploadResult.success ? <IconCheck size={16} /> : <IconAlertCircle size={16} />}
                title={uploadResult.message}
                color={uploadResult.success ? 'green' : 'red'}
              >
                {uploadResult.details && <Text size="sm">{uploadResult.details}</Text>}
              </Alert>
            )}

            <Group justify="flex-end">
              <Button
                leftSection={<IconUpload size={16} />}
                onClick={handleLocationUpload}
                loading={isUploading}
                disabled={!locationFile}
              >
                Upload and Import
              </Button>
            </Group>
          </Stack>
        </Tabs.Panel>
      </Tabs>
    </Paper>
  );
};

export default DataMigrationPanel;
