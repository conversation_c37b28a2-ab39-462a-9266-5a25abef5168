'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Table,
  Text,
  Select,
  Pagination,
  ScrollArea,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconSearch,
  IconCalculator,
  IconRefresh,
} from '@tabler/icons-react';

// TypeScript interfaces
interface StockMovement {
  id: string;
  date: string;
  numeroDocument: string;
  source: string;
  depot: string;
  depotDestination: string;
  qte: number;
  prix: number;
}

interface DepotQuantity {
  id: string;
  depot: string;
  qte: number;
  dpQte: number;
  cnQte: number;
}

const RecalculeDeStock = () => {
  const [searchArticle, setSearchArticle] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [movements] = useState<StockMovement[]>([]);
  const [depotQuantities] = useState<DepotQuantity[]>([]);

  const handleUpdateStock = () => {
    notifications.show({
      title: 'Stock mis à jour',
      message: 'Le recalcul du stock a été effectué avec succès',
      color: 'green',
    });
  };

  return (
    <div className="p-4">
      {/* Header */}
      <Paper p="md" mb="md" withBorder className="bg-slate-600">
        <Group align="center">
          <IconCalculator size={24} className="text-white" />
          <Title order={3} className="text-white">
            Recalcule de stock
          </Title>
        </Group>
      </Paper>

      {/* Article Search */}
      <Paper p="md" mb="md" withBorder>
        <TextInput
          label="Article"
          placeholder="Rechercher un article..."
          value={searchArticle}
          onChange={(event) => setSearchArticle(event.currentTarget.value)}
          leftSection={<IconSearch size={16} />}
          className="max-w-md"
        />
      </Paper>

      {/* Main Movements Table */}
      <Paper p="md" mb="md" withBorder>
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Date</Table.Th>
                <Table.Th>N°. Document</Table.Th>
                <Table.Th>Source</Table.Th>
                <Table.Th>Dépôt</Table.Th>
                <Table.Th>Dépôt de destination</Table.Th>
                <Table.Th>Qté</Table.Th>
                <Table.Th>Prix</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {movements.length === 0 ? (
                <Table.Tr>
                  <Table.Td colSpan={7} className="text-center text-gray-500 py-8">
                    Aucun élément trouvé
                  </Table.Td>
                </Table.Tr>
              ) : (
                movements.map((movement) => (
                  <Table.Tr key={movement.id}>
                    <Table.Td>{movement.date}</Table.Td>
                    <Table.Td>{movement.numeroDocument}</Table.Td>
                    <Table.Td>{movement.source}</Table.Td>
                    <Table.Td>{movement.depot}</Table.Td>
                    <Table.Td>{movement.depotDestination}</Table.Td>
                    <Table.Td>{movement.qte}</Table.Td>
                    <Table.Td>{movement.prix.toFixed(2)}</Table.Td>
                  </Table.Tr>
                ))
              )}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {/* Pagination for Main Table */}
        <Group justify="space-between" mt="md">
          <Group>
            <Text size="sm">Page</Text>
            <Select
              size="sm"
              w={60}
              data={['1']}
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(parseInt(value || '1'))}
            />
            <Text size="sm">Lignes par Page</Text>
            <Select
              size="sm"
              w={80}
              data={['10', '25', '50', '100']}
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(parseInt(value || '10'))}
            />
            <Text size="sm">0 - 0 de 0</Text>
          </Group>
          <Pagination total={1} value={currentPage} onChange={setCurrentPage} size="sm" />
        </Group>
      </Paper>

      {/* Depot Quantities Table */}
      <Paper p="md" mb="md" withBorder>
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Dépôt</Table.Th>
                <Table.Th>Qté</Table.Th>
                <Table.Th>DP.Qté</Table.Th>
                <Table.Th>CN.Qté</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {depotQuantities.length === 0 ? (
                <Table.Tr>
                  <Table.Td colSpan={4} className="text-center text-gray-500 py-8">
                    Aucun élément trouvé
                  </Table.Td>
                </Table.Tr>
              ) : (
                depotQuantities.map((depot) => (
                  <Table.Tr key={depot.id}>
                    <Table.Td>{depot.depot}</Table.Td>
                    <Table.Td>{depot.qte}</Table.Td>
                    <Table.Td>{depot.dpQte}</Table.Td>
                    <Table.Td>{depot.cnQte}</Table.Td>
                  </Table.Tr>
                ))
              )}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {/* Pagination for Depot Table */}
        <Group justify="space-between" mt="md">
          <Group>
            <Text size="sm">Page</Text>
            <Select
              size="sm"
              w={60}
              data={['1']}
              value="1"
            />
            <Text size="sm">Lignes par Page</Text>
            <Select
              size="sm"
              w={80}
              data={['10', '25', '50', '100']}
              value="10"
            />
            <Text size="sm">0 - 0 de 0</Text>
          </Group>
          <Pagination total={1} value={1} size="sm" />
        </Group>
      </Paper>

      {/* Update Stock Button */}
      <Group justify="flex-end" mt="md">
        <Button
          variant="filled"
          className="bg-gray-500 hover:bg-gray-600"
          onClick={handleUpdateStock}
          leftSection={<IconRefresh size={16} />}
        >
          Mettre à jour le stock
        </Button>
      </Group>
    </div>
  );
};

export default RecalculeDeStock;
