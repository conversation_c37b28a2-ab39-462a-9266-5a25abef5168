
"use client";
import React, { useState, useCallback, forwardRef, useImperativeHandle, useMemo, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, rem, Button, Group, Text, Dialog, Menu,  Flex, Badge ,Divider,Tooltip} from '@mantine/core';
import { IconMessageCircle, IconSettings, IconPhoto, IconSquareRoundedPlusFilled ,IconDeviceFloppy,IconRefresh} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import Image from "next/image";
import {  SaveManagerRef, ModificationState, } from '../../shared/types';
import { useSaveManager } from '../../shared/SaveManager';
import { EstheticControls } from './EstheticControls';
import EstheticProcedures from './EstheticProcedures';
import { DentalSvg } from '@/components/TDentalSvgMin';
import { <PERSON><PERSON>, <PERSON>talB } from '@/data/Moins-6ans';
import { isAllowedPosition,isOnlyallowed} from '@/utils/dentalUtils';
import dentaltop from "../../../../../public/dentaltop.jpg";
import dentalButtom from "../../../../../public/dentalButtom.jpg";
import { ColorTarget } from '@/types/dental';
import { SVGPathStyle } from '../../shared/types';
import { EnhancedDentalSvg, ToothSelection } from '@/components/dental/EnhancedDentalSvg';
//import { EstimatesTabsRef } from '../../EstimatesTabs';
import { notifications } from '@mantine/notifications';
import { useEstimateReactive } from '@/hooks/useEstimateReactive';

// Types pour les props
interface EstimatesTabsProps {
  onModificationChange?: (svgId: string, pathId: string, isVisible: boolean, highlightedPaths?: Record<string, SVGPathStyle>) => Promise<void>;
  session?:  unknown;
  isLoading?: boolean;
}

// Interface pour les méthodes exposées via ref
export interface EstimatesTabsRef {
  triggerSave: () => Promise<void>;
}
export const EstheticDentistryTabNew = forwardRef<SaveManagerRef, EstimatesTabsProps>(({
  onModificationChange,

}, ref) => {
const {
        session,
        isSaving,
        forceSave,
        resetSession,
        modifications,
        initializeSession,

      } = useEstimateReactive({
        patientId: 'patient-123', // TODO: Récupérer l'ID du patient actuel
        sessionName: 'Session Devis',
        autoSave: true,
        autoSaveDelay: 2000,
        autoInitialize: false, // NOUVEAU: Désactiver l'initialisation automatique
      });

      // Initialiser la session manuellement au montage du composant
      useEffect(() => {
        if (!session) {
          console.log('🔄 [EstheticDentistry] Initialisation de la session...');
          initializeSession('patient-123', 'Session Esthétique')
            .then(() => console.log('✅ [EstheticDentistry] Session initialisée avec succès'))
            .catch(error => console.error('❌ [EstheticDentistry] Erreur initialisation session:', error));
        } else {
          console.log('✅ [EstheticDentistry] Session déjà active:', session);
        }
      }, [session, initializeSession]);
      // Référence pour déclencher la sauvegarde des onglets
      const estimatesTabsRef = useRef<EstimatesTabsRef | null>(null);
  // Fonction pour sauvegarder avec message
    const handleSaveWithMessage = async () => {
      try {
        // Sauvegarder les modifications du système réactif principal
        await forceSave();

        // Déclencher la sauvegarde des onglets spécialisés si disponible
        if (estimatesTabsRef.current?.triggerSave) {
          await estimatesTabsRef.current.triggerSave();
        }

        notifications.show({
          title: '✅ Sauvegarde réussie !',
          message: `${modifications.length} modifications ont été sauvegardées avec succès.`,
          color: 'green',
          autoClose: 3000,
        });
      } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        notifications.show({
          title: '❌ Erreur de sauvegarde',
          message: 'Impossible de sauvegarder les modifications. Veuillez réessayer.',
          color: 'red',
          autoClose: 5000,
        });
      }
    };
  // États locaux pour cette spécialité

   const [highlightedPaths, setHighlightedPaths] = useState<Record<string, SVGPathStyle>>({});

  const [activeButton, setActiveButton] = useState<string>('');
  const [targetPath, setTargetPath] = useState<string>('');
  const [opened, {  close }] = useDisclosure(false);

  // État pour la sélection multiple de dents
  const [selectedTeeth, setSelectedTeeth] = useState<string[]>([]);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState<boolean>(false);
const [isHidingMode] = useState(false);
  const [currentColor] = useState<string>("");
  const [pendingPath19Toggle, setPendingPath19Toggle] = useState(false);

  // État pour contrôler le mode Enhanced (cases à cocher)
  const [isEnhancedMode, setIsEnhancedMode] = useState<boolean>(false);

 const [currentColorTarget] = useState<ColorTarget>("fill");
 const [isPathSelectionActive] = useState(false);
 const [clickedIds, setClickedIds] = useState<string[]>([]);
const [brokenRedStrokeSvgs] = useState<Set<string>>(new Set());
 const [gradientEffectSvgs] = useState<Set<string>>(new Set()); // For Dental
  const [gradientBottomEffectSvgs, setGradientBottomEffectSvgs] = useState<Set<string>>(new Set()); // For DentalB
  const generatePathsToShowByDefault = () => {
    const svgIds = Array.from({ length: 32 }, (_, i) => `${i + 1}`);
    const pathIds = Array.from({ length: 16 }, (_, i) => `${i + 1}`);
    return svgIds.flatMap((svgId) => pathIds.map((pathId) => ({ svg_id: svgId, path_id: pathId })));
  };
  const pathsToShowByDefault = generatePathsToShowByDefault();

  // Fonction pour convertir svg_id vers numéro de dent dentaire standard
  const getToothNumber = useCallback((svgId: string) => {
    const id = parseInt(svgId);

    // Mapping selon la numérotation dentaire standard
    if (id >= 1 && id <= 8) {
      // upperRight (dents 1-8) → numéros dentaires 18-11 (de droite à gauche depuis le centre)
      return 18 - (id - 1);
    }
    if (id >= 9 && id <= 16) {
      // upperLeft (dents 9-16) → numéros dentaires 21-28
      return 20 + (id - 8);
    }
    if (id >= 17 && id <= 24) {
      // lowerLeft (dents 17-24) → numéros dentaires 38-31 (de gauche à droite depuis le centre)
      return 38 - (id - 17);
    }
    if (id >= 25 && id <= 32) {
      // lowerRight (dents 25-32) → numéros dentaires 41-48
      return 40 + (id - 24);
    }

    return id; // Fallback
  }, []);

  // Organiser les dents selon l'ordre d'Adulte.tsx : upperRight → upperLeft → lowerLeft → lowerRight
  const organizedTeeth = useMemo(() => {
    const allTeeth = [...Dantal, ...DantalB];

    // Fonction pour déterminer le quadrant d'une dent selon son svg_id
    const getQuadrant = (svgId: string) => {
      const id = parseInt(svgId);
      if (id >= 1 && id <= 8) return 'upper_right';      // Dents 1-8 (supérieur droit)
      if (id >= 9 && id <= 16) return 'upper_left';      // Dents 9-16 (supérieur gauche)
      if (id >= 17 && id <= 24) return 'lower_left';     // Dents 17-24 (inférieur gauche)
      if (id >= 25 && id <= 32) return 'lower_right';    // Dents 25-32 (inférieur droit)
      return 'unknown';
    };

    // Organiser par quadrant selon l'ordre d'Adulte.tsx
    const upperRight = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'upper_right').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));
    const upperLeft = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'upper_left').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));
    const lowerLeft = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'lower_left').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));
    const lowerRight = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'lower_right').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));

    // Retourner dans l'ordre : upperRight → upperLeft → lowerLeft → lowerRight
    return [...upperRight, ...upperLeft, ...lowerLeft, ...lowerRight];
  }, []);

    const initialHiddenPaths = useMemo(() => {
    return organizedTeeth.reduce((acc, svgData) => {
      svgData.paths.forEach((path) => {
        const key = `${svgData.svg_id}-${path.id}`;
        const isVisibleByDefault = pathsToShowByDefault.some(
          (visiblePath) => visiblePath.svg_id === svgData.svg_id && visiblePath.path_id === path.id
        );
        // Hide path 20 by default
        if (path.id === "20") {
          acc[key] = true;
        } else {
          acc[key] = !isVisibleByDefault;
        }
      });
      return acc;
    }, {} as Record<string, boolean>);
  }, [pathsToShowByDefault, organizedTeeth]);
const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>(initialHiddenPaths);
  // État de modification pour cette spécialité
  const modificationState: ModificationState = {
    hiddenPaths,
    highlightedPaths,
    clickedIds,
    activeButton,
    targetPath
  };

  // Gestionnaire de sauvegarde
  const { save, hasChanges, SaveManagerComponent } = useSaveManager(
    modificationState,
    onModificationChange,
    'esthetic'
  );

  // Exposer les méthodes via ref
  useImperativeHandle(ref, () => ({
     triggerSave: async () => {
       await save();
     },
     hasUnsavedChanges: () => hasChanges()
   }), [save, hasChanges]);

  // Gestionnaires pour le nouveau composant Enhanced
  const handleToothSelectionChange = useCallback((selections: ToothSelection[]) => {
    console.log('Tooth selections changed (Esthetic):', selections);
    // Ici on peut synchroniser avec l'état global ou sauvegarder
  }, []);

  const handlePathSelectionChange = useCallback((toothNumber: number, pathId: string, isSelected: boolean) => {
    console.log(`Path ${pathId} of tooth ${toothNumber} ${isSelected ? 'selected' : 'deselected'} (Esthetic)`);
    // Synchroniser avec les états existants si nécessaire
  }, []);

  const handleTreatmentApply = useCallback((toothNumber: number, pathIds: string[], treatment: string, color: string) => {
    console.log(`Applying esthetic treatment ${treatment} with color ${color} to tooth ${toothNumber}, paths:`, pathIds);

    // Appliquer le traitement via l'API existante
    pathIds.forEach(pathId => {
      const svgId = toothNumber.toString();
      const key = `${svgId}-${pathId}`;

      setHighlightedPaths(prev => ({
        ...prev,
        [key]: {
          fill: color // Pour les traitements esthétiques, on utilise principalement fill
        }
      }));
    });

    // Sauvegarder via onModificationChange si disponible
    if (onModificationChange) {
      pathIds.forEach(pathId => {
        onModificationChange(toothNumber.toString(), pathId, false, highlightedPaths)
          .catch(error => console.error('Erreur sauvegarde traitement esthétique:', error));
      });
    }
  }, [highlightedPaths, onModificationChange]);

  // Gestionnaire de changement de path cible
  const handleTargetPathChange = useCallback((pathId: string) => {
    setTargetPath(pathId);
    console.log(`🎯 Path cible esthétique: ${pathId}`);
  }, []);

  // Fonctions de gestion de sélection multiple
  const handleSelectAll = useCallback(() => {
    const allTeethIds = [...Dantal, ...DantalB].map(tooth => tooth.svg_id);
    setSelectedTeeth(allTeethIds);
  }, []);

  const handleDeselectAll = useCallback(() => {
    setSelectedTeeth([]);
  }, []);

  const handleApplyToSelected = useCallback(() => {
    if (selectedTeeth.length === 0) return;

    selectedTeeth.forEach(svgId => {
      // Appliquer l'action selon le bouton actif
      switch (activeButton) {
        case 'whitening':
          [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach(pathId => {
            const key = `${svgId}-${pathId}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          });
          break;
        case 'veneer':
          [20, 21, 22, 23].forEach(pathId => {
            const key = `${svgId}-${pathId}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          });
          break;
        case 'bonding':
          [24, 25, 26].forEach(pathId => {
            const key = `${svgId}-${pathId}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          });
          break;
        default:
          if (targetPath) {
            const key = `${svgId}-${targetPath}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          }
          break;
      }
    });

    // Désélectionner après application
    setSelectedTeeth([]);
    setIsMultiSelectMode(false);
  }, [selectedTeeth, activeButton, targetPath]);
const [hiddenSvgIds] = useState<string[]>([]);
// --------------------------------------------------------------






  const handleButtonClick = (buttonId: string) => {
    // Set the active button
    setActiveButton((prev) => (prev === buttonId ? '' : buttonId));
    // Set the target path based on the button clicked
    if (buttonId === "viewCleaning") {
      setTargetPath("17");
    } else if (buttonId === "viewFluoride") {
      setTargetPath("18");
    } else if (buttonId === "viewModeSealant") {
      setTargetPath("19");
    } else if (buttonId === "viewModeWhitening") {
      setActiveButton(""); // No target path for Whitening mode
    }
    if (buttonId === "RestoratinPermanent") {
      setTargetPath("RestoratinPermanent");
    }
    if (buttonId === "RestoratinTemporary") {
      setTargetPath("25");
    }
    if (buttonId === "RestoratinAmalgam") {
      setTargetPath("26");
    }
    if (buttonId === "RestoratinGlassIonomer") {
      setTargetPath("27");
    }
    if (buttonId === "PostCare") {
      setTargetPath("36");
    }
    if (buttonId === "Veneer") {
      setTargetPath("37");
    }
    if (buttonId === "Onlay") {
      setTargetPath("39");
    }
    if (buttonId === "CrownPermanent") {
      setTargetPath("41");
    }
    if (buttonId === "CrownTemporary" ) {
      setTargetPath("42");
    }
    if (buttonId === "CrownGold" ) {
      setTargetPath("44");
    }
    if (buttonId === "CrownZirconia") {
      setTargetPath("47");
    }
    if (buttonId === "Extraction") {
      setTargetPath("53");
    }
  };
  const onPathClick = (pathId: string, svgId: string) => {
     const positionKey = `${svgId}(${pathId})`;
           const key = `${svgId}-${pathId}`;
           setSelectedTeeth((prev: string[]) => {
            const isAlreadySelected = prev.includes(positionKey);
            return isAlreadySelected ? prev.filter((tooth) => tooth !== positionKey) : [...prev, positionKey];
          });
          if (currentColor === "#FF4444" && !isAllowedPosition(positionKey)) {
            return;
          }
          if (currentColor === "#8E1616" && !isOnlyallowed(positionKey)) {
           return;
         }
           if (pendingPath19Toggle && pathId === "19" && svgId === "1") {
             setHiddenPaths((prev) => ({
               ...prev,
               [`${svgId}-${pathId}`]: !prev[`${svgId}-${pathId}`], // Toggle hidden state
             }));
             setPendingPath19Toggle(false); // Reset pending state after applying
           }
     setHighlightedPaths((prev) => {
       const newHighlightedPaths = { ...prev };
       if (!newHighlightedPaths[key]) {
         newHighlightedPaths[key] = {};
       }
       if (currentColorTarget === "both") {
         if (newHighlightedPaths[key].fill && newHighlightedPaths[key].stroke) {
           delete newHighlightedPaths[key].fill;
           delete newHighlightedPaths[key].stroke;
         } else {
           newHighlightedPaths[key] = {
             fill: currentColor,
             stroke: "#2563EB"
           };
         }
       } else {
         if (newHighlightedPaths[key][currentColorTarget]) {
           delete newHighlightedPaths[key][currentColorTarget];
         } else {
           newHighlightedPaths[key][currentColorTarget] = currentColor;
         }
       }
       if (Object.keys(newHighlightedPaths[key]).length === 0) {
         delete newHighlightedPaths[key];
       }
       return newHighlightedPaths;
     });
     if (isPathSelectionActive) {
       setClickedIds((prevIds) => [...prevIds, pathId]);
     }
      // Toggle visibility of Path19
   };
  const handleSvgClick = useCallback(
     (svgId: string) => {
       const togglePathVisibility = (pathId: string, visible: boolean) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => {
           const newHiddenPaths = { ...prev, [key]: visible };

           // Sauvegarder la modification de manière réactive
           if (onModificationChange) {
             onModificationChange(svgId, pathId, !visible, highlightedPaths)
               .catch(error => console.error('Erreur sauvegarde:', error));
           }

           return newHiddenPaths;
         });
       };

       // Fonction pour appliquer les modifications à toutes les dents sélectionnées

       const handleWhiteningMode = () => {
         // Reset all paths to hidden first, then show only whitening paths
         const newHiddenPaths: Record<string, boolean> = {};

         // Hide all paths for this tooth (1-100)
         for (let i = 1; i <= 100; i++) {
           newHiddenPaths[`${svgId}-${i}`] = true;
         }

         // Show base tooth paths (8-16)
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           newHiddenPaths[`${svgId}-${pathId}`] = false;
         });

         // Show whitening paths (20-23)
         newHiddenPaths[`${svgId}-20`] = false;
         newHiddenPaths[`${svgId}-21`] = false;
         newHiddenPaths[`${svgId}-22`] = false;
         newHiddenPaths[`${svgId}-23`] = false;

         setHiddenPaths((prev) => ({ ...prev, ...newHiddenPaths }));

       };
       const handleCleaningMode = () => {
         // Reset all paths to hidden first, then show only cleaning paths
         const newHiddenPaths: Record<string, boolean> = {};

         // Hide all paths for this tooth (1-100)
         for (let i = 1; i <= 100; i++) {
           newHiddenPaths[`${svgId}-${i}`] = true;
         }

         // Show base tooth paths (8-16)
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           newHiddenPaths[`${svgId}-${pathId}`] = false;
         });

         // Show cleaning path (17)
         newHiddenPaths[`${svgId}-17`] = false;

         setHiddenPaths((prev) => ({ ...prev, ...newHiddenPaths }));

         // Sauvegarder la modification réactive pour le path 17 (cleaning)
         if (onModificationChange) {
           onModificationChange(svgId, '17', false, highlightedPaths)
             .catch(error => console.error('Erreur sauvegarde cleaning:', error));
         }
       };
       const handleFluorideMode = () => {
         if (DantalB.some((svgData) => svgData.svg_id === svgId)) {
           setGradientBottomEffectSvgs((prev) => {
             const newSet = new Set(prev);
             if (newSet.has(svgId)) {
               newSet.delete(svgId);
             } else {
               newSet.add(svgId);
             }
             return newSet;
           });
         }
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-17`]: true,
             [`${svgId}-18`]: !prev[`${svgId}-18`],
             [`${svgId}-19`]: true,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-24`]: true,
             [`${svgId}-25`]: true,
             [`${svgId}-26`]: true,
             [`${svgId}-27`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-29`]: true,
             [`${svgId}-30`]: true,
             [`${svgId}-31`]: true,
             [`${svgId}-32`]: true,
             [`${svgId}-33`]: true,
             [`${svgId}-34`]: true,
             [`${svgId}-35`]: true,
             [`${svgId}-36`]: true,
             [`${svgId}-37`]: true,
             [`${svgId}-38`]: true,
             [`${svgId}-39`]: true,
             [`${svgId}-40`]: true,
             [`${svgId}-41`]: true,
             [`${svgId}-42`]: true,
             [`${svgId}-43`]: true,
             [`${svgId}-44`]: true,
             [`${svgId}-45`]: true,
             [`${svgId}-46`]: true,
             [`${svgId}-47`]: true,
             [`${svgId}-48`]: true,
             [`${svgId}-49`]: true,
             [`${svgId}-50`]: true,
             [`${svgId}-51`]: true,
             [`${svgId}-52`]: true,
             [`${svgId}-53`]: true,
             [`${svgId}-54`]: true,
             [`${svgId}-55`]: true,
             [`${svgId}-56`]: true,
             [`${svgId}-57`]: true,
             [`${svgId}-58`]: true,
             [`${svgId}-59`]: true,
             [`${svgId}-60`]: true,
             [`${svgId}-61`]: true,
             [`${svgId}-62`]: true,
             [`${svgId}-63`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-65`]: true,
             [`${svgId}-67`]: true,
             [`${svgId}-68`]: true,
             [`${svgId}-69`]: true,

           }));
         });

       };
       const handleSealantMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-17`]: true,
             [`${svgId}-18`]: true,
             [`${svgId}-19`]: !prev[`${svgId}-19`],
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-24`]: true,
             [`${svgId}-25`]: true,
             [`${svgId}-26`]: true,
             [`${svgId}-27`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-29`]: true,
             [`${svgId}-30`]: true,
             [`${svgId}-31`]: true,
             [`${svgId}-32`]: true,
             [`${svgId}-33`]: true,
             [`${svgId}-34`]: true,
             [`${svgId}-35`]: true,
             [`${svgId}-36`]: true,
             [`${svgId}-37`]: true,
             [`${svgId}-38`]: true,
             [`${svgId}-39`]: true,
             [`${svgId}-40`]: true,
             [`${svgId}-41`]: true,
             [`${svgId}-42`]: true,
             [`${svgId}-43`]: true,
             [`${svgId}-44`]: true,
             [`${svgId}-45`]: true,
             [`${svgId}-46`]: true,
             [`${svgId}-47`]: true,
             [`${svgId}-48`]: true,
             [`${svgId}-49`]: true,
             [`${svgId}-50`]: true,
             [`${svgId}-51`]: true,
             [`${svgId}-52`]: true,
             [`${svgId}-53`]: true,
             [`${svgId}-54`]: true,
             [`${svgId}-55`]: true,
             [`${svgId}-56`]: true,
             [`${svgId}-57`]: true,
             [`${svgId}-58`]: true,
             [`${svgId}-59`]: true,
             [`${svgId}-60`]: true,
             [`${svgId}-61`]: true,
             [`${svgId}-62`]: true,
             [`${svgId}-63`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-65`]: true,
             [`${svgId}-67`]: true,
             [`${svgId}-68`]: true,
             [`${svgId}-69`]: true,
           }));
         });

       };
       const handleTargetPath = () => {
         if (targetPath) {
           togglePathVisibility(targetPath, !hiddenPaths[`${svgId}-${targetPath}`]);
         }
       };
       const handleRestoratinPermanentMode = () => {
         // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
    [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths((prev) => ({ ...prev,
        [key]: false,
        [`${svgId}-20`]: true,
        [`${svgId}-21`]: true,
        [`${svgId}-22`]: true,
        [`${svgId}-23`]: true,
        [`${svgId}-17`]: true,
        [`${svgId}-18`]: true,
        [`${svgId}-19`]: true,
        [`${svgId}-24`]: true,
        [`${svgId}-25`]: true,
        [`${svgId}-26`]: true,
        [`${svgId}-27`]: true,
        [`${svgId}-28`]: true,
        [`${svgId}-28`]: true,
        [`${svgId}-30`]: true,
        [`${svgId}-31`]: true,
        [`${svgId}-32`]: true,
        [`${svgId}-33`]: true,
        [`${svgId}-34`]: true,
        [`${svgId}-35`]: true,
        [`${svgId}-36`]: true,
        [`${svgId}-37`]: true,
        [`${svgId}-41`]: true,
        [`${svgId}-42`]: true,
        [`${svgId}-43`]: true,
        [`${svgId}-44`]: true,
        [`${svgId}-49`]: true,
        [`${svgId}-50`]: true,
        [`${svgId}-51`]: true,
        [`${svgId}-52`]: true,
        [`${svgId}-53`]: true,
        [`${svgId}-54`]: true,
        [`${svgId}-55`]: true,
        [`${svgId}-56`]: true,
        [`${svgId}-57`]: true,
        [`${svgId}-58`]: true,
        [`${svgId}-59`]: true,
        [`${svgId}-60`]: true,
        [`${svgId}-61`]: true,
        [`${svgId}-62`]: true,
        [`${svgId}-63`]: true,
        [`${svgId}-64`]: true,
        [`${svgId}-64`]: true,
        [`${svgId}-65`]: true,
        [`${svgId}-67`]: true,
        [`${svgId}-68`]: true,
        [`${svgId}-69`]: true,
      }));
    });
       };
       const handleTemporaryMode = () => {
            // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
       [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: !prev[`${svgId}-24`],
           [`${svgId}-25`]: !prev[`${svgId}-25`],
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
         }));
       });
       };
       const handleAmalgamMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
            [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: !prev[`${svgId}-26`],
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           }));
         });
       };
       const handleGlassIonomerMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
            [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: !prev[`${svgId}-27`],
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });
       };
       const handleRootPermanentMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
               [key]: false,
               [`${svgId}-20`]: true,
               [`${svgId}-21`]: true,
               [`${svgId}-22`]: true,
               [`${svgId}-23`]: true,
               [`${svgId}-17`]: true,
               [`${svgId}-18`]: true,
               [`${svgId}-19`]: true,
               [`${svgId}-24`]: true,
               [`${svgId}-25`]: true,
               [`${svgId}-26`]: true,
               [`${svgId}-27`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-29`]: true,
               [`${svgId}-30`]: true,
               [`${svgId}-31`]: true,
               [`${svgId}-32`]: true,
               [`${svgId}-33`]: true,
               [`${svgId}-34`]: true,
               [`${svgId}-35`]: true,
               [`${svgId}-36`]: true,
               [`${svgId}-37`]: true,
               [`${svgId}-38`]: true,
               [`${svgId}-39`]: true,
               [`${svgId}-40`]: true,
               [`${svgId}-41`]: true,
               [`${svgId}-42`]: true,
               [`${svgId}-43`]: true,
               [`${svgId}-44`]: true,
               [`${svgId}-45`]: true,
               [`${svgId}-46`]: true,
               [`${svgId}-47`]: true,
               [`${svgId}-48`]: true,
               [`${svgId}-49`]: true,
               [`${svgId}-50`]: true,
               [`${svgId}-51`]: true,
               [`${svgId}-52`]: true,
               [`${svgId}-53`]: true,
               [`${svgId}-54`]: true,
               [`${svgId}-55`]: true,
               [`${svgId}-56`]: true,
               [`${svgId}-57`]: true,
               [`${svgId}-58`]: true,
               [`${svgId}-59`]: true,
               [`${svgId}-60`]: true,
               [`${svgId}-61`]: true,
               [`${svgId}-62`]: true,
               [`${svgId}-63`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-65`]: true,
               [`${svgId}-67`]: true,
               [`${svgId}-68`]: true,
               [`${svgId}-69`]: true,
           }));
         });
       };
       const handleRootTemporaryMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: !prev[`${svgId}-28`],
           [`${svgId}-29`]: !prev[`${svgId}-29`],
           [`${svgId}-30`]: true,

           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

           }));
         });

       };
       const handleRootCalciumMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: !prev[`${svgId}-30`],
           [`${svgId}-31`]: !prev[`${svgId}-31`],
           [`${svgId}-32`]: !prev[`${svgId}-32`],
           [`${svgId}-33`]: !prev[`${svgId}-33`],
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleRootGuttaPerchaMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: !prev[`${svgId}-34`],
           [`${svgId}-35`]: !prev[`${svgId}-35`],
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

           }));
         });

       };
       const handlePostCareMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
               [`${svgId}-21`]: true,
               [`${svgId}-22`]: true,
               [`${svgId}-23`]: true,
               [`${svgId}-17`]: true,
               [`${svgId}-18`]: true,
               [`${svgId}-19`]: true,
               [`${svgId}-24`]: true,
               [`${svgId}-25`]: true,
               [`${svgId}-26`]: true,
               [`${svgId}-27`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-30`]: true,
               [`${svgId}-31`]: true,
               [`${svgId}-32`]: true,
               [`${svgId}-33`]: true,
               [`${svgId}-34`]: true,
               [`${svgId}-35`]: true,
               [`${svgId}-36`]: !prev[`${svgId}-36`],
               [`${svgId}-37`]: true,
               [`${svgId}-38`]: true,
               [`${svgId}-39`]: true,
               [`${svgId}-40`]: true,
               [`${svgId}-41`]: true,
               [`${svgId}-42`]: true,
               [`${svgId}-43`]: true,
               [`${svgId}-44`]: true,
               [`${svgId}-45`]: true,
               [`${svgId}-46`]: true,
               [`${svgId}-47`]: true,
               [`${svgId}-48`]: true,
               [`${svgId}-49`]: true,
               [`${svgId}-50`]: true,
               [`${svgId}-51`]: true,
               [`${svgId}-52`]: true,
               [`${svgId}-53`]: true,
               [`${svgId}-54`]: true,
               [`${svgId}-55`]: true,
               [`${svgId}-56`]: true,
               [`${svgId}-57`]: true,
               [`${svgId}-58`]: true,
               [`${svgId}-59`]: true,
               [`${svgId}-60`]: true,
               [`${svgId}-61`]: true,
               [`${svgId}-62`]: true,
               [`${svgId}-63`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-65`]: true,
               [`${svgId}-67`]: true,
               [`${svgId}-68`]: true,
               [`${svgId}-69`]: true,
           }));
         });

       };
       const handleVeneerMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: !prev[`${svgId}-37`],
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleCrownPermanentMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: true,
           [key]: !prev[key] ,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: !prev[`${svgId}-41`],
           [`${svgId}-41`]: !prev[`${svgId}-41`],

           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleCrownTemporaryMode = () => {
       // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
       [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: !prev[`${svgId}-24`],
           [`${svgId}-25`]: !prev[`${svgId}-25`],
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

         }));
       });

       };
       const handleCrownGoldMode = () => {
         setHiddenPaths((prev) => ({
           ...prev,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: !prev[`${svgId}-43`],
           [`${svgId}-44`]: !prev[`${svgId}-44`],
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

         }));
       };
       const handleOnlayMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = false;
           updatedPaths[`${svgId}-2`] = false;
           updatedPaths[`${svgId}-3`] = false;
           updatedPaths[`${svgId}-4`] = false;
           updatedPaths[`${svgId}-5`] = false;
           updatedPaths[`${svgId}-6`] = false;
           updatedPaths[`${svgId}-7`] = false;
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  !prev[`${svgId}-38`];
           updatedPaths[`${svgId}-39`] =  !prev[`${svgId}-39`];
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleCrownZirconiaMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] = !prev[`${svgId}-45`];
           updatedPaths[`${svgId}-46`]= !prev[`${svgId}-46`];
           updatedPaths[`${svgId}-47`]= !prev[`${svgId}-47`];
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });
       };
       const handleDentureMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = false;
           updatedPaths[`${svgId}-2`] = false;
           updatedPaths[`${svgId}-3`] = false;
           updatedPaths[`${svgId}-4`] = false;
           updatedPaths[`${svgId}-5`] = false;
           updatedPaths[`${svgId}-6`] = false;
           updatedPaths[`${svgId}-7`] = false;
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  !prev[`${svgId}-48`];
           updatedPaths[`${svgId}-49`] =  !prev[`${svgId}-49`];
           updatedPaths[`${svgId}-50`] =  !prev[`${svgId}-50`];
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleBridgeMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: !prev[`${svgId}-51`],
           [`${svgId}-52`]: !prev[`${svgId}-52`],

           }));
         });

       };
       const handleExtractionMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel

           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] = true;
           updatedPaths[`${svgId}-46`]= true;
           updatedPaths[`${svgId}-47`]= true;
           updatedPaths[`${svgId}-48`]= true;
           updatedPaths[`${svgId}-49`]= true;
           updatedPaths[`${svgId}-50`]= true;
           updatedPaths[`${svgId}-51`]= true;
           updatedPaths[`${svgId}-52`]= true;
           updatedPaths[`${svgId}-53`]= true;
           updatedPaths[`${svgId}-54`]= true;
           updatedPaths[`${svgId}-55`]= true;
           updatedPaths[`${svgId}-56`]= true;
           updatedPaths[`${svgId}-57`]= true;
           updatedPaths[`${svgId}-58`]= true;
           updatedPaths[`${svgId}-59`]= true;
           updatedPaths[`${svgId}-60`]= true;
           updatedPaths[`${svgId}-61`]= true;
           updatedPaths[`${svgId}-62`]= true;
           updatedPaths[`${svgId}-63`]= true;
           updatedPaths[`${svgId}-64`]= true;
           updatedPaths[`${svgId}-65`]= true;
           updatedPaths[`${svgId}-66`]= true;
           updatedPaths[`${svgId}-67`]= true;
           updatedPaths[`${svgId}-68`]= true;
           updatedPaths[`${svgId}-69`]= true;
           return  updatedPaths
         });
       };
       const handleImplantMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  !prev[`${svgId}-54`];
           updatedPaths[`${svgId}-55`] =  !prev[`${svgId}-55`];
           updatedPaths[`${svgId}-56`] =  !prev[`${svgId}-56`];
           updatedPaths[`${svgId}-57`] =  !prev[`${svgId}-57`];
           updatedPaths[`${svgId}-58`] =  !prev[`${svgId}-58`];
           updatedPaths[`${svgId}-59`] =  !prev[`${svgId}-59`];
           updatedPaths[`${svgId}-60`] =  !prev[`${svgId}-60`];
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleBoneMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  !prev[`${svgId}-61`];
           updatedPaths[`${svgId}-62`] =  !prev[`${svgId}-62`];
           updatedPaths[`${svgId}-63`] =  !prev[`${svgId}-63`];
           updatedPaths[`${svgId}-64`] =  !prev[`${svgId}-64`];
           updatedPaths[`${svgId}-65`] =  !prev[`${svgId}-65`];
           updatedPaths[`${svgId}-66`] =  !prev[`${svgId}-66`];
           updatedPaths[`${svgId}-67`] =  !prev[`${svgId}-67`];
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });
       };
       const handleResectionMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths

           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  !prev[`${svgId}-68`];
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleTeethCrownMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  !prev[`${svgId}-69`];
           return  updatedPaths
         });

       };
       const handleImplantTeethCrownMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  !prev[`${svgId}-54`];
           updatedPaths[`${svgId}-55`] =  !prev[`${svgId}-55`];
           updatedPaths[`${svgId}-56`] =  !prev[`${svgId}-56`];
           updatedPaths[`${svgId}-57`] =  !prev[`${svgId}-57`];
           updatedPaths[`${svgId}-58`] =  !prev[`${svgId}-58`];
           updatedPaths[`${svgId}-59`] =  !prev[`${svgId}-59`];
           updatedPaths[`${svgId}-60`] =  !prev[`${svgId}-60`];
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  !prev[`${svgId}-69`];
           return  updatedPaths
         });

       };

       switch (activeButton) {
         case "viewModeWhitening":
           handleWhiteningMode();
           break;
         case "viewCleaning":
           handleCleaningMode();
           break;
         case "viewFluoride":
           handleFluorideMode();
           break;
         case "viewModeSealant":
           handleSealantMode();
           break;
           case "RestoratinPermanent":
             handleRestoratinPermanentMode();
             break;
           case "RestoratinTemporary":
           handleTemporaryMode();
           break;
           case "RestoratinAmalgam":
           handleAmalgamMode();
           break;
           case "RestoratinGlassIonomer":
           handleGlassIonomerMode();
           break;
           case "RootPermanent":
           handleRootPermanentMode();
           break;
           case "RootTemporary":
           handleRootTemporaryMode();
           break;
           case "RootCalcium":
           handleRootCalciumMode();
           break;
           case "RootGuttaPercha":
           handleRootGuttaPerchaMode();
           break;
           case "PostCare":
             handlePostCareMode();
           break;
           case "Veneer":
             handleVeneerMode();
           break;
           case "Onlay":
             handleOnlayMode();
           break;
           case "CrownPermanent":
             handleCrownPermanentMode();
           break;
           case "CrownTemporary":
             handleCrownTemporaryMode();
           break;
           case "CrownGold":
             handleCrownGoldMode();
           break;
           case "CrownZirconia":
             handleCrownZirconiaMode();
           break;
           case "Denture":
             handleDentureMode();
           break;
           case "Bridge":
             handleBridgeMode();
           break;
           case "Extraction":
             handleExtractionMode();
           break;
           case "Implant":
             handleImplantMode();
           break;
           case "Bone":
             handleBoneMode();
           break;
           case "Resection":
             handleResectionMode();
           break;
           case "TeethCrown":
             handleTeethCrownMode();
           break;
           case "ImplantTeethCrown":
             handleImplantTeethCrownMode();
           break;
         default:
           handleTargetPath();
           break;
       }
     },
     [targetPath, activeButton, hiddenPaths, highlightedPaths, onModificationChange]
   );





  return (
    <>
      {/* Composant de sauvegarde invisible */}
      {SaveManagerComponent}

      <div className="my-4">
        <>
          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px' }} align="center">
            <div style={{ position: 'absolute', right: 16 }}>
              <Group gap="md">
                {/* Bouton pour basculer le mode Enhanced */}
                <Button
                  variant={isEnhancedMode ? "filled" : "outline"}
                  color={isEnhancedMode ? "blue" : "gray"}
                  size="sm"
                  onClick={() => setIsEnhancedMode(!isEnhancedMode)}
                  leftSection={<IconSettings size={14} />}
                >
                  {isEnhancedMode ? "Mode Simple" : "Mode Avancé"}
                </Button>

                <Menu withinPortal position="bottom-end" shadow="sm">
                  <Menu.Target>
                    <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                      Procédures Esthétiques
                    </Button>
                  </Menu.Target>
                  <Menu.Dropdown>
                    <Menu.Item>Blanchiment</Menu.Item>
                    <Menu.Item>Facettes</Menu.Item>
                    <Menu.Item>Collage composite</Menu.Item>
                    <Menu.Item>Design du sourire</Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              </Group>
            </div>

            {/* Contrôles spécifiques à l'esthétique */}
            <EstheticControls
              activeButton={activeButton}
              onButtonClick={handleButtonClick}
              onTargetPathChange={handleTargetPathChange}
            />
          </Flex>

          {/* Contrôles de sélection multiple */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Button
                  size="xs"
                  variant={isMultiSelectMode ? "filled" : "outline"}
                  onClick={() => setIsMultiSelectMode(!isMultiSelectMode)}
                  color="blue"
                >
                  {isMultiSelectMode ? "Mode Normal" : "Sélection Multiple"}
                </Button>
                {selectedTeeth.length > 0 && (
                  <Badge color="blue" variant="light">
                    {selectedTeeth.length} dent{selectedTeeth.length > 1 ? 's' : ''} sélectionnée{selectedTeeth.length > 1 ? 's' : ''}
                  </Badge>
                )}
              </div>

              {isMultiSelectMode && (
                <div className="flex gap-2">
                  <Button size="xs" variant="outline" onClick={handleSelectAll}>
                    Tout sélectionner
                  </Button>
                  <Button size="xs" variant="outline" onClick={handleDeselectAll}>
                    Tout désélectionner
                  </Button>
                  {selectedTeeth.length > 0 && activeButton && (
                    <Button size="xs" variant="filled" color="green" onClick={handleApplyToSelected}>
                      Appliquer à la sélection
                    </Button>
                  )}
                </div>
              )}
            </div>

            {isMultiSelectMode && (
              <Text size="xs" c="dimmed">
                Cliquez sur les dents pour les sélectionner, puis choisissez un traitement et cliquez sur &quot;Appliquer à la sélection&quot;
              </Text>
            )}
          </div>

          {/* SVG Dentaire - Toutes les 32 dents */}
          {isEnhancedMode ? (
            /* Mode Enhanced avec cases à cocher */
            <div className="enhanced-dental-container p-4 border rounded-lg bg-gray-50">
              <EnhancedDentalSvg
                patientId="current-patient"
                specialty="esthetic"
                onToothSelectionChange={handleToothSelectionChange}
                onPathSelectionChange={handlePathSelectionChange}
                onTreatmentApply={handleTreatmentApply}
                activeButton={activeButton}
                currentColor={currentColor}
                currentColorTarget={currentColorTarget}
              />
            </div>
          ) : (
            /* Mode normal existant */
            <>
            <div className=" h-full px-0   border-t-[3px] my-2">
                <Flex style={{ position: 'relative', width: '100%', }} align="center">
                  <div  style={{ position: 'absolute', left: 16 ,top:220}}>
                  <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
                    </div>
                </Flex>
              <div className=" max-w-[920px] mt-2 mx-auto">
              <div className=" flex h-full px-0  max-h-[320px] ">
                                  {Dantal.map((svgData) => (
                        <div key={svgData.svg_id} className="h-[230px] cursor-pointer hover:bg-[#F2F5F8]" onClick={() => handleSvgClick(svgData.svg_id)}>
                          <div className="mt-0.5 flex">
                            <Text ta="center" h={30} className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE]" style={{ width: svgData.width }}>
                              {getToothNumber(svgData.svg_id)}
                            </Text>
                          </div>
                          {!hiddenSvgIds.includes(svgData.svg_id) ? (
                            <DentalSvg
                              svgData={svgData}
                              isHidingMode={isHidingMode}
                              highlightedPaths={highlightedPaths}
                              onPathClick={onPathClick}
                              isPathSelectionActive={isPathSelectionActive}
                              hiddenPaths={hiddenPaths}
                              isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                              isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                              isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                            />
                          ) : (
                            <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
                          )}

                        </div>
                      ))}
                   </div>
                  </div>
                  </div>
                  <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
                  <div className=" h-full px-0   border-b-[3px]">
                     <Flex style={{ position: 'relative',  width: '100%', }} align="center">
                  <div  style={{ position: 'absolute', left: 16 ,top:14}}>
                  <Image
                        src={dentalButtom}
                        alt={"dentalButtom"}
                        width={32}
                        height={32}
                      />
                    </div>
                </Flex>
              <div className=" max-w-[920px]  mx-auto">
                  <div className=" flex h-full px-0  max-h-[320px] ">
                        {DantalB.map((svgData) => (
                        <div key={svgData.svg_id} className="h-[230px] cursor-pointer hover:bg-[#F2F5F8]" onClick={() => handleSvgClick(svgData.svg_id)}>
                          {!hiddenSvgIds.includes(svgData.svg_id) ? (
                            <DentalSvg
                              svgData={svgData}
                              isHidingMode={isHidingMode}
                              highlightedPaths={highlightedPaths}
                              onPathClick={onPathClick}
                              isPathSelectionActive={isPathSelectionActive}
                              hiddenPaths={hiddenPaths}
                              isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                              isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                              isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                            />
                          ) : (
                            <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
                          )}
                          <div className="mt-0.5 flex">
                            <Text ta="center" h={30} className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE]" style={{ width: svgData.width }}>
                              {getToothNumber(svgData.svg_id)}
                            </Text>
                                        </div>
                                      </div>
                                    ))}
                  </div>
                  </div>
                  </div>
            </>
          )}
                {/* <Divider  my="8px" variant="dashed" /> */}
              <div className="mx-auto w-[880px] max-w-[880px] mb-4 ">
              <br/>
              <Tooltip label="Sauvegarder maintenant">
                         <Button
                           variant="light"
                           color="blue"
                           leftSection={<IconDeviceFloppy size={14} />}
                           onClick={handleSaveWithMessage}
                           loading={isSaving}
                           disabled={isSaving}
                         >
                           Sauvegarder
                         </Button>
                       </Tooltip>

                       <Tooltip label="Réinitialiser la session">
                         <Button
                           variant="light"
                           color="red"
                           leftSection={<IconRefresh size={14} />}
                           onClick={resetSession}
                           disabled={isSaving}
                         >
                           Reset
                         </Button>
                       </Tooltip>
              </div>

        </>

        {/* Onglets de procédures */}
        <div className="border-base-200 mx-4 border-t mt-4">
          <Tabs variant="unstyled" defaultValue="All Procedures">
            <Tabs.List grow className="space-x-0 gap-0 mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={<IconMessageCircle style={{ width: rem(16), height: rem(16) }} />}
              >
                Toutes les Procédures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
              >
                Planifiées
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={<IconPhoto style={{ width: rem(16), height: rem(16) }} />}
              >
                Terminées
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="All Procedures">
              <EstheticProcedures
                type="all"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Planned">
              <EstheticProcedures
                type="planned"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Completed">
              <EstheticProcedures
                type="completed"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>
          </Tabs>
        </div>

        {/* Dialog de sauvegarde */}
        <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
            <Text size="sm" mb="xs" fw={500}>
              Sauvegarder les Modifications Esthétiques
            </Text>
          </Group>
          <Group align="flex-end">
            <Button
              w="100%"
              onClick={async () => {
                await save();
                close();
              }}
              className="hover:bg-[#3799CE]/90"
            >
              Sauvegarder
            </Button>
          </Group>
        </Dialog>
      </div>
    </>
  );
});

EstheticDentistryTabNew.displayName = 'EstheticDentistryTabNew';
