/**
 * Service centralisé pour les interactions dentaires entre frontend et backend
 * Gère les boutons, les modifications, et la synchronisation des données
 */

import { dentalModificationService, dentalSvgService, CreateDentalSvgData } from './dentalSvgService';
import { notifications } from '@mantine/notifications';

// Types pour les interactions dentaires
export interface DentalButtonConfig {
  id: string;
  label: string;
  pathId: string;
  specialty: 'therapeutic' | 'esthetic' | 'prosthetic' | 'orthodontic' | 'surgery';
  modificationType: 'color' | 'replacement' | 'addition' | 'removal' | 'restoration';
  defaultColor?: string;
  targetPath?: string;
}

export interface DentalInteractionResult {
  success: boolean;
  modificationsCreated: number;
  errors: string[];
  message: string;
}

export interface SelectedTeethData {
  [toothNumber: number]: string[]; // tooth number -> selected path IDs
}

/**
 * Configuration des boutons par spécialité
 */
const BUTTON_CONFIGS: Record<string, DentalButtonConfig> = {
  // Prosthodontics
  'Veneer': {
    id: 'Veneer',
    label: 'Facette',
    pathId: '37',
    specialty: 'prosthetic',
    modificationType: 'addition',
    defaultColor: '#3799CE'
  },
  'Onlay': {
    id: 'Onlay',
    label: 'Onlay',
    pathId: '39',
    specialty: 'prosthetic',
    modificationType: 'addition',
    defaultColor: '#3799CE'
  },
  'CrownPermanent': {
    id: 'CrownPermanent',
    label: 'Couronne Permanente',
    pathId: '41',
    specialty: 'prosthetic',
    modificationType: 'replacement',
    defaultColor: '#3799CE'
  },
  'CrownTemporary': {
    id: 'CrownTemporary',
    label: 'Couronne Temporaire',
    pathId: '42',
    specialty: 'prosthetic',
    modificationType: 'replacement',
    defaultColor: '#FFA500'
  },
  'CrownGold': {
    id: 'CrownGold',
    label: 'Couronne Or',
    pathId: '44',
    specialty: 'prosthetic',
    modificationType: 'replacement',
    defaultColor: '#FFD700'
  },
  'CrownZirconia': {
    id: 'CrownZirconia',
    label: 'Couronne Zircone',
    pathId: '47',
    specialty: 'prosthetic',
    modificationType: 'replacement',
    defaultColor: '#E6E6FA'
  },
  'Bridge': {
    id: 'Bridge',
    label: 'Bridge',
    pathId: '52',
    specialty: 'prosthetic',
    modificationType: 'addition',
    defaultColor: '#3799CE'
  },
  'Denture': {
    id: 'Denture',
    label: 'Prothèse',
    pathId: '48',
    specialty: 'prosthetic',
    modificationType: 'replacement',
    defaultColor: '#FFB6C1'
  },

  // Therapeutic
  'viewCleaning': {
    id: 'viewCleaning',
    label: 'Nettoyage',
    pathId: '17',
    specialty: 'therapeutic',
    modificationType: 'color',
    defaultColor: '#00FF00'
  },
  'viewFluoride': {
    id: 'viewFluoride',
    label: 'Fluor',
    pathId: '18',
    specialty: 'therapeutic',
    modificationType: 'color',
    defaultColor: '#00FFFF'
  },
  'viewModeSealant': {
    id: 'viewModeSealant',
    label: 'Scellant',
    pathId: '19',
    specialty: 'therapeutic',
    modificationType: 'addition',
    defaultColor: '#FFFFFF'
  },
  'RestoratinPermanent': {
    id: 'RestoratinPermanent',
    label: 'Restauration Permanente',
    pathId: 'RestoratinPermanent',
    specialty: 'therapeutic',
    modificationType: 'restoration',
    defaultColor: '#C0C0C0'
  }
};

/**
 * Service principal pour les interactions dentaires
 */
class DentalInteractionService {
  private selectedTeeth: Set<number> = new Set();
  private selectedPaths: SelectedTeethData = {};
  private currentPatientId: string | null = null;

  /**
   * Initialise le service avec un patient
   */
  initialize(patientId: string) {
    this.currentPatientId = patientId;
    this.selectedTeeth.clear();
    this.selectedPaths = {};
  }

  /**
   * Obtient la configuration d'un bouton
   */
  getButtonConfig(buttonId: string): DentalButtonConfig | undefined {
    return BUTTON_CONFIGS[buttonId];
  }

  /**
   * Sélectionne/désélectionne une dent
   */
  toggleToothSelection(toothNumber: number): boolean {
    if (this.selectedTeeth.has(toothNumber)) {
      this.selectedTeeth.delete(toothNumber);
      delete this.selectedPaths[toothNumber];
      return false;
    } else {
      this.selectedTeeth.add(toothNumber);
      this.selectedPaths[toothNumber] = ['default'];
      return true;
    }
  }

  /**
   * Sélectionne/désélectionne un path pour une dent
   */
  togglePathSelection(toothNumber: number, pathId: string): boolean {
    if (!this.selectedPaths[toothNumber]) {
      this.selectedPaths[toothNumber] = [];
    }

    const paths = this.selectedPaths[toothNumber];
    const index = paths.indexOf(pathId);

    if (index > -1) {
      paths.splice(index, 1);
      return false;
    } else {
      paths.push(pathId);
      return true;
    }
  }

  /**
   * Obtient les dents sélectionnées
   */
  getSelectedTeeth(): number[] {
    return Array.from(this.selectedTeeth);
  }

  /**
   * Obtient les paths sélectionnés pour une dent
   */
  getSelectedPaths(toothNumber: number): string[] {
    return this.selectedPaths[toothNumber] || [];
  }

  /**
   * Applique un traitement aux dents sélectionnées
   */
  async applyTreatmentToSelectedTeeth(
    buttonId: string,
    customColor?: string
  ): Promise<DentalInteractionResult> {
    if (!this.currentPatientId) {
      throw new Error('Patient ID non défini');
    }

    const buttonConfig = this.getButtonConfig(buttonId);
    if (!buttonConfig) {
      throw new Error(`Configuration du bouton ${buttonId} non trouvée`);
    }

    const selectedTeeth = this.getSelectedTeeth();
    if (selectedTeeth.length === 0) {
      throw new Error('Aucune dent sélectionnée');
    }

    const result: DentalInteractionResult = {
      success: true,
      modificationsCreated: 0,
      errors: [],
      message: ''
    };

    try {
      // Créer les modifications pour chaque dent sélectionnée
      for (const toothNumber of selectedTeeth) {
        const pathsForTooth = this.getSelectedPaths(toothNumber);
        const paths = pathsForTooth.length > 0 ? pathsForTooth : ['default'];

        for (const pathId of paths) {
          try {
            await dentalModificationService.createModification({
              patient_id: this.currentPatientId,
              tooth_number: toothNumber,
              path_id: buttonConfig.targetPath || pathId,
              modification_type: buttonConfig.modificationType,
              value: customColor || buttonConfig.defaultColor || '#000000',
              specialty: buttonConfig.specialty,
              applied_by_button: buttonId
            });

            result.modificationsCreated++;
          } catch (error) {
            console.error(`Erreur pour la dent ${toothNumber}:`, error);
            result.errors.push(`Erreur pour la dent ${toothNumber}: ${error}`);
          }
        }
      }

      result.message = `${result.modificationsCreated} modifications créées avec succès`;

      // Afficher une notification de succès
      notifications.show({
        title: 'Traitement appliqué',
        message: result.message,
        color: 'green',
        autoClose: 3000
      });

    } catch (error) {
      result.success = false;
      result.message = `Erreur lors de l'application: ${error}`;

      notifications.show({
        title: 'Erreur',
        message: result.message,
        color: 'red',
        autoClose: 5000
      });
    }

    return result;
  }

  /**
   * Charge les modifications existantes pour un patient
   */
  async loadPatientModifications(patientId: string) {
    try {
      const modifications = await dentalModificationService.getPatientModifications(patientId);
      console.log(`${modifications.length} modifications chargées pour le patient ${patientId}`);
      return modifications;
    } catch (error) {
      console.error('Erreur lors du chargement des modifications:', error);
      throw error;
    }
  }

  /**
   * Sauvegarde les données SVG du patient
   */
  async saveDentalSvgData(patientId: string, svgData: Partial<CreateDentalSvgData>) {
    try {
      const result = await dentalSvgService.updateDentalData(patientId, svgData);
      console.log('Données SVG sauvegardées avec succès');
      return result;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde SVG:', error);
      throw error;
    }
  }
}

// Instance singleton du service
export const dentalInteractionService = new DentalInteractionService();

// Export des types et du service
export default dentalInteractionService;
