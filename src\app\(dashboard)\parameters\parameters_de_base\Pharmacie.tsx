import React, { useState } from 'react';
import {
  Stack,
  Title,
  Select,
  Radio,
  Text,
  Paper,
  Checkbox,
  Table,

  Pagination,
  Grid,
  
  ColorInput,
  ColorPicker,
  Group,
  ActionIcon,
  Modal
} from '@mantine/core';
import { IconPalette } from '@tabler/icons-react';
const Pharmacie = () => {
  // State for form values
  const [tvaVente, setTvaVente] = useState('20');
  const [tvaAchat, setTvaAchat] = useState('20');
  const [libelleFamille, setLibelleFamille] = useState('Médicaments');
const [isColorPickerModalOpen, setIsColorPickerModalOpen] = useState(false);
  // Radio button states
  const [typeDefautMouvement, setTypeDefautMouvement] = useState('Sortie');
  const [typeBonsRetours, setTypeBonsRetours] = useState('Prix');
  const [valorisationStock, setValorisationStock] = useState('LIFO');
  const [modePrix, setModePrix] = useState('HT');

  // Checkbox state
  const [activerConfirmation, setActiverConfirmation] = useState(false);

  // Data options for selects
  const tvaOptions = [
    { value: '20', label: '20' },
    { value: '10', label: '10' },
    { value: '5.5', label: '5.5' },
    { value: '2.1', label: '2.1' },
    { value: '0', label: '0' },
  ];

  const libelleFamilleOptions = [
    { value: 'Médicaments', label: 'Médicaments' },
    { value: 'Parapharmacie', label: 'Parapharmacie' },
    { value: 'Dispositifs médicaux', label: 'Dispositifs médicaux' },
    { value: 'Compléments alimentaires', label: 'Compléments alimentaires' },
  ];

  // Alert management data
  const alertData = [
    {
      document: 'Bon de commande',
      states: [
        { state: 'Clôturer', notifier: '', color: '#5a35e9' },
        { state: 'Annulée (-)', notifier: '', color: '#bbbbbc' },
        { state: 'Retard de livraison', notifier: '0', color: '#e41519' },
        { state: 'Soldé', notifier: '0', color: '#2e9a4a' },
        { state: 'Partiellement reçu', notifier: '0', color: '#d33ae0' },
        { state: 'En cours', notifier: '', color: '#15cb15' },
        { state: 'Brouillon', notifier: '', color: '#f7f68f' },
      ]
    },
    {
      document: 'Mouvement de Stock',
      states: [
        { state: 'Brouillon', notifier: '', color: '#124882' },
        { state: 'Validé', notifier: '', color: '#fb2d31' },
      ]
    }
  ];

  return (
    <Stack gap="lg" className="w-full">
      {/* Valeurs par défaut Section */}
      <Paper p="md" withBorder>
        <Title order={4} className="text-gray-700 mb-md">
          Valeurs par défaut
        </Title>

        <Grid>
          {/* First row - Tax settings */}
          <Grid.Col span={6}>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                Tva vente
              </Text>
              <Select
                value={tvaVente}
                onChange={(value) => setTvaVente(value || '')}
                data={tvaOptions}
                size="sm"
                className="w-full"
              />
            </div>
          </Grid.Col>

          <Grid.Col span={6}>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                Libellé famille médicament
              </Text>
              <Select
                value={libelleFamille}
                onChange={(value) => setLibelleFamille(value || '')}
                data={libelleFamilleOptions}
                size="sm"
                className="w-full"
              />
            </div>
          </Grid.Col>

          {/* Second row - Purchase tax */}
          <Grid.Col span={6}>
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                Tva achat
              </Text>
              <Select
                value={tvaAchat}
                onChange={(value) => setTvaAchat(value || '')}
                data={tvaOptions}
                size="sm"
                className="w-full"
              />
            </div>
          </Grid.Col>
        </Grid>

        {/* Radio button sections */}
        <div className="mt-lg">
          <Grid>
            {/* Type par défaut du mouvement de stock */}
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Type par défaut du mouvement de stock
                </Text>
                <Stack gap="xs">
                  <Radio
                    value="Entrée"
                    checked={typeDefautMouvement === 'Entrée'}
                    onChange={(event) => setTypeDefautMouvement(event.currentTarget.value)}
                    label="Entrée"
                    size="sm"
                  />
                  <Radio
                    value="Sortie"
                    checked={typeDefautMouvement === 'Sortie'}
                    onChange={(event) => setTypeDefautMouvement(event.currentTarget.value)}
                    label="Sortie"
                    size="sm"
                  />
                </Stack>
              </div>
            </Grid.Col>

            {/* Type des bons de retours */}
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Type des bons de retours
                </Text>
                <Stack gap="xs">
                  <Radio
                    value="Prix"
                    checked={typeBonsRetours === 'Prix'}
                    onChange={(event) => setTypeBonsRetours(event.currentTarget.value)}
                    label="Prix"
                    size="sm"
                  />
                  <Radio
                    value="Stock"
                    checked={typeBonsRetours === 'Stock'}
                    onChange={(event) => setTypeBonsRetours(event.currentTarget.value)}
                    label="Stock"
                    size="sm"
                  />
                </Stack>
              </div>
            </Grid.Col>

            {/* Valorisation du stock */}
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Valorisation du stock:
                </Text>
                <Stack gap="xs">
                  <Radio
                    value="FIFO"
                    checked={valorisationStock === 'FIFO'}
                    onChange={(event) => setValorisationStock(event.currentTarget.value)}
                    label="FIFO"
                    size="sm"
                  />
                  <Radio
                    value="LIFO"
                    checked={valorisationStock === 'LIFO'}
                    onChange={(event) => setValorisationStock(event.currentTarget.value)}
                    label="LIFO"
                    size="sm"
                  />
                  <Radio
                    value="FEFO"
                    checked={valorisationStock === 'FEFO'}
                    onChange={(event) => setValorisationStock(event.currentTarget.value)}
                    label="FEFO"
                    size="sm"
                  />
                  <Radio
                    value="PMP"
                    checked={valorisationStock === 'PMP'}
                    onChange={(event) => setValorisationStock(event.currentTarget.value)}
                    label="PMP"
                    size="sm"
                  />
                </Stack>
              </div>
            </Grid.Col>

            {/* Mode de prix */}
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Mode de prix
                </Text>
                <Stack gap="xs">
                  <Radio
                    value="HT"
                    checked={modePrix === 'HT'}
                    onChange={(event) => setModePrix(event.currentTarget.value)}
                    label="HT"
                    size="sm"
                  />
                  <Radio
                    value="TTC"
                    checked={modePrix === 'TTC'}
                    onChange={(event) => setModePrix(event.currentTarget.value)}
                    label="TTC"
                    size="sm"
                  />
                </Stack>
              </div>
            </Grid.Col>
          </Grid>
        </div>

        {/* Checkbox for document confirmation */}
        <div className="mt-md">
          <Checkbox
            checked={activerConfirmation}
            onChange={(event) => setActiverConfirmation(event.currentTarget.checked)}
            label="Activer la confirmation des documents"
            size="sm"
          />
        </div>
      </Paper>

      {/* Gestion des alertes Section */}
      <Paper p="md" withBorder>
        <Title order={4} className="text-gray-700 mb-md">
          Gestion des alertes
        </Title>

        <Table
          striped
          highlightOnHover
          withTableBorder
          withColumnBorders
          className="w-full"
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-48" style={{ paddingLeft:'20px',textAlign: 'left',width:'20%' }}>
                Document
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm" style={{ paddingLeft:'20px',textAlign: 'left',width:'60%' }}>
                État
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-24 text-center"style={{ paddingLeft:'20px',textAlign: 'left',width:'5%' }}>
                Notifier
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-24 text-center"style={{ paddingLeft:'20px',textAlign: 'left',width:'15%' }}>
                Couleur
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {alertData.map((docGroup, docIndex) => (
              docGroup.states.map((stateItem, stateIndex) => (
                <Table.Tr key={`${docIndex}-${stateIndex}`} className="hover:bg-gray-50">
                  {/* Document column - only show for first state of each document */}
                  <Table.Td className="border-r border-gray-300">
                    {stateIndex === 0 ? (
                      <Text size="sm" fw={500} className="text-gray-700">
                        {docGroup.document}
                      </Text>
                    ) : null}
                  </Table.Td>

                  {/* État column */}
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-700">
                      {stateItem.state}
                    </Text>
                  </Table.Td>

                  {/* Notifier column */}
                  <Table.Td className="border-r border-gray-300 text-center">
                    <Text size="sm" className="text-gray-700">
                      {stateItem.notifier}
                    </Text>
                  </Table.Td>

                  {/* Couleur column */}
                  <Table.Td className="text-center">
                    <div className="flex justify-center">
                  <Group>
                    <ColorInput
                        value={stateItem.color}
                       w={"80%"}
                      
                    />    
                    <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                        onClick={() => setIsColorPickerModalOpen(true)}>
                        <IconPalette stroke={2} />
                    </ActionIcon>
                   
                </Group>
                    </div>
                  </Table.Td>
                </Table.Tr>
              ))
            ))}
          </Table.Tbody>
        </Table>

        {/* Pagination */}
        <div className="flex justify-center mt-md">
          <Pagination total={1} value={1} size="sm" />
        </div>
      </Paper>
      <Modal  
                  opened={isColorPickerModalOpen}
                  onClose={() => setIsColorPickerModalOpen(false)} 
                  transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                  withCloseButton={false}
                  centered
              >
                  <ColorPicker 
                      format="hex" 
                      
                      swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} 
                  />
            </Modal>
    </Stack>
  );
};

export default Pharmacie;
