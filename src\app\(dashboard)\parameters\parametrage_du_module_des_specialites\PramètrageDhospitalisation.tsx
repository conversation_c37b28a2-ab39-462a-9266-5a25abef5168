"use client";

import React, { useState } from 'react';
import {
  Button,
  TextInput,
  Group,
  ActionIcon,
  Modal,
  Stack,
  Text,
  Card,
  Textarea,
  Grid,
  Select,
  Tabs,
  ColorPicker,
  NumberInput,
  Checkbox
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconHospital,
  IconStethoscope,
  IconColorPicker
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { DataTable } from 'mantine-datatable';

// Types
interface HospitalReason {
  id: string;
  code: string;
  description: string;
  duree: number;
  couleur: string;
}

interface InterventionReason {
  id: string;
  code: string;
  description: string;
  duree: number;
  couleur: string;
}

interface Procedure {
  id: string;
  code: string;
  nom: string;
  honoraire: number;
  remise: number;
  total: number;
  commentaire: string;
}

interface Act {
  id: string;
  code: string;
  nom: string;
  honoraire: number;
  remise: number;
}

interface Etage {
  id: string;
  nom: string;
  description: string;
  couleur: string;
}

interface Chambre {
  id: string;
  nom: string;
  description: string;
  couleur: string;
  etage: string;
}

interface Lit {
  id: string;
  nom: string;
  description: string;
  chambre: string;
}

interface HospitalReasonFormData {
  code: string;
  description: string;
  duree: number;
  couleur: string;
}

interface InterventionFormData {
  code: string;
  description: string;
  duree: number;
  couleur: string;
}

interface ProcedureFormData {
  code: string;
  nom: string;
  honoraire: number;
  servicesDesignes: string;
  codeNGAP: string;
  codeCCAM: string;
  tnr: string;
  modalite: string;
  remboursable: boolean;
}

interface EtageFormData {
  nom: string;
  description: string;
  couleur: string;
}

interface ChambreFormData {
  nom: string;
  description: string;
  couleur: string;
  etage: string;
}

interface LitFormData {
  nom: string;
  description: string;
  chambre: string;
}

const PramètrageDhospitalisation = () => {
  // États pour les onglets et modales
  const [activeTab, setActiveTab] = useState('motifs-hospitalisation');
  const [hospitalModalOpened, { open: openHospitalModal, close: closeHospitalModal }] = useDisclosure(false);
  const [interventionModalOpened, { open: openInterventionModal, close: closeInterventionModal }] = useDisclosure(false);
  const [procedureModalOpened, { open: openProcedureModal, close: closeProcedureModal }] = useDisclosure(false);
  const [etageModalOpened, { open: openEtageModal, close: closeEtageModal }] = useDisclosure(false);
  const [chambreModalOpened, { open: openChambreModal, close: closeChambreModal }] = useDisclosure(false);
  const [litModalOpened, { open: openLitModal, close: closeLitModal }] = useDisclosure(false);
  const [modalActiveTab, setModalActiveTab] = useState('general');

  // États pour les données
  const [searchQuery, setSearchQuery] = useState('');
  const [hospitalReasons, setHospitalReasons] = useState<HospitalReason[]>([]);
  const [interventionReasons, setInterventionReasons] = useState<InterventionReason[]>([]);
  const [etages, setEtages] = useState<Etage[]>([]);
  const [chambres, setChambres] = useState<Chambre[]>([]);
  const [lits, setLits] = useState<Lit[]>([]);

  // Formulaires
  const hospitalForm = useForm<HospitalReasonFormData>({
    initialValues: {
      code: '',
      description: '',
      duree: 1,
      couleur: '#3b82f6'
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      description: (value) => (!value ? 'La description est requise' : null),
      duree: (value) => (value < 1 ? 'La durée doit être supérieure à 0' : null)
    }
  });

  const interventionForm = useForm<InterventionFormData>({
    initialValues: {
      code: '',
      description: '',
      duree: 1,
      couleur: '#3b82f6'
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      description: (value) => (!value ? 'La description est requise' : null),
      duree: (value) => (value < 1 ? 'La durée doit être supérieure à 0' : null)
    }
  });

  const procedureForm = useForm<ProcedureFormData>({
    initialValues: {
      code: '',
      nom: '',
      honoraire: 0,
      servicesDesignes: '',
      codeNGAP: '',
      codeCCAM: '',
      tnr: '',
      modalite: '',
      remboursable: false
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      nom: (value) => (!value ? 'Le nom est requis' : null)
    }
  });

  const etageForm = useForm<EtageFormData>({
    initialValues: {
      nom: '',
      description: '',
      couleur: '#3b82f6'
    },
    validate: {
      nom: (value) => (!value ? 'Le nom est requis' : null)
    }
  });

  const chambreForm = useForm<ChambreFormData>({
    initialValues: {
      nom: '',
      description: '',
      couleur: '#3b82f6',
      etage: ''
    },
    validate: {
      nom: (value) => (!value ? 'Le nom est requis' : null),
      etage: (value) => (!value ? 'L\'étage est requis' : null)
    }
  });

  const litForm = useForm<LitFormData>({
    initialValues: {
      nom: '',
      description: '',
      chambre: ''
    },
    validate: {
      nom: (value) => (!value ? 'Le nom est requis' : null),
      chambre: (value) => (!value ? 'La chambre est requise' : null)
    }
  });

  // Gestionnaires pour les onglets
  const handleTabChange = (value: string | null) => {
    if (value) setActiveTab(value);
  };

  // Gestionnaires pour les modales
  const handleHospitalModalOpen = () => {
    setModalActiveTab('general');
    openHospitalModal();
  };

  const handleInterventionModalOpen = () => {
    openInterventionModal();
  };

  const handleProcedureModalOpen = () => {
    openProcedureModal();
  };

  // Gestionnaires de soumission
  const handleHospitalSubmit = (values: HospitalReasonFormData) => {
    const newReason: HospitalReason = {
      id: Date.now().toString(),
      ...values
    };
    setHospitalReasons(prev => [...prev, newReason]);
    closeHospitalModal();
    hospitalForm.reset();
  };

  const handleInterventionSubmit = (values: InterventionFormData) => {
    const newReason: InterventionReason = {
      id: Date.now().toString(),
      ...values
    };
    setInterventionReasons(prev => [...prev, newReason]);
    closeInterventionModal();
    interventionForm.reset();
  };

  // Colonnes pour les tableaux
  const hospitalColumns = [
    { accessor: 'code', title: 'Code', width: 120 },
    { accessor: 'description', title: 'Motif', width: 300 },
    { accessor: 'duree', title: 'Durée (min)', width: 120 },
    { accessor: 'couleur', title: 'Couleur', width: 100 }
  ];

  const interventionColumns = [
    { accessor: 'code', title: 'Code', width: 120 },
    { accessor: 'description', title: 'Motif', width: 300 },
    { accessor: 'duree', title: 'Durée (min)', width: 120 },
    { accessor: 'couleur', title: 'Couleur', width: 100 }
  ];

  const etageColumns = [
    { accessor: 'nom', title: 'Nom', width: 200 },
    { accessor: 'description', title: 'Description', width: 300 },
    { accessor: 'couleur', title: 'Couleur', width: 100 }
  ];

  const chambreColumns = [
    { accessor: 'nom', title: 'Nom', width: 200 },
    { accessor: 'description', title: 'Description', width: 250 },
    { accessor: 'couleur', title: 'Couleur', width: 100 },
    { accessor: 'etage', title: 'Étage', width: 150 }
  ];

  const litColumns = [
    { accessor: 'nom', title: 'Nom', width: 200 },
    { accessor: 'description', title: 'Description', width: 300 },
    { accessor: 'chambre', title: 'Chambre', width: 200 }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <IconHospital size={32} className="text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-800">Paramétrage d&apos;hospitalisation</h1>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          onClick={() => {
            switch (activeTab) {
              case 'motifs-intervention':
                handleInterventionModalOpen();
                break;
              case 'actes':
                handleProcedureModalOpen();
                break;
              case 'etages':
                openEtageModal();
                break;
              case 'chambres':
                openChambreModal();
                break;
              case 'lits':
                openLitModal();
                break;
              default:
                handleHospitalModalOpen();
            }
          }}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {activeTab === 'motifs-intervention' ? 'Ajouter motifs d&apos;intervention' :
           activeTab === 'actes' ? 'Nouveau' :
           activeTab === 'etages' ? 'Ajouter Étage' :
           activeTab === 'chambres' ? 'Ajouter Chambre' :
           activeTab === 'lits' ? 'Ajouter Lit' :
           'Ajouter des motifs d&apos;hôpital'}
        </Button>
      </div>

      {/* Onglets principaux */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tabs.List>
            <Tabs.Tab value="motifs-hospitalisation">Motifs d&apos;hospitalisation</Tabs.Tab>
            <Tabs.Tab value="motifs-intervention">Motifs d&apos;intervention</Tabs.Tab>
            <Tabs.Tab value="actes">Actes</Tabs.Tab>
            <Tabs.Tab value="etages">Étages</Tabs.Tab>
            <Tabs.Tab value="chambres">Chambres</Tabs.Tab>
            <Tabs.Tab value="lits">Lits</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="motifs-hospitalisation" pt="md">
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>
            <DataTable
              withTableBorder
              borderRadius="sm"
              withColumnBorders
              striped
              highlightOnHover
              records={hospitalReasons}
              columns={hospitalColumns}
              minHeight={400}
              noRecordsText="Aucun élément trouvé."
            />
          </Tabs.Panel>

          <Tabs.Panel value="motifs-intervention" pt="md">
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>
            <DataTable
              withTableBorder
              borderRadius="sm"
              withColumnBorders
              striped
              highlightOnHover
              records={interventionReasons}
              columns={interventionColumns}
              minHeight={400}
              noRecordsText="Aucun élément trouvé."
            />
          </Tabs.Panel>

          <Tabs.Panel value="actes" pt="md">
            <div className="mb-4">
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={handleProcedureModalOpen}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Nouveau
              </Button>
            </div>
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                className="max-w-md"
              />
            </div>
            <DataTable
              withTableBorder
              borderRadius="sm"
              withColumnBorders
              striped
              highlightOnHover
              records={[]}
              columns={[
                { accessor: 'code', title: 'Code', width: 120 },
                { accessor: 'nom', title: 'Nom', width: 300 },
                { accessor: 'honoraire', title: 'Honoraire', width: 120 },
                { accessor: 'remise', title: 'Re...', width: 100 }
              ]}
              minHeight={400}
              noRecordsText="Aucun élément trouvé."
            />
          </Tabs.Panel>

          <Tabs.Panel value="etages" pt="md">
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>
            <DataTable
              withTableBorder
              borderRadius="sm"
              withColumnBorders
              striped
              highlightOnHover
              records={etages}
              columns={etageColumns}
              minHeight={400}
              noRecordsText="Aucun élément trouvé."
            />
          </Tabs.Panel>

          <Tabs.Panel value="chambres" pt="md">
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>
            <DataTable
              withTableBorder
              borderRadius="sm"
              withColumnBorders
              striped
              highlightOnHover
              records={chambres}
              columns={chambreColumns}
              minHeight={400}
              noRecordsText="Aucun élément trouvé."
            />
          </Tabs.Panel>

          <Tabs.Panel value="lits" pt="md">
            <div className="mb-4">
              <TextInput
                placeholder="Rechercher..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                className="max-w-md"
              />
            </div>
            <DataTable
              withTableBorder
              borderRadius="sm"
              withColumnBorders
              striped
              highlightOnHover
              records={lits}
              columns={litColumns}
              minHeight={400}
              noRecordsText="Aucun élément trouvé."
            />
          </Tabs.Panel>
        </Tabs>
      </Card>

      {/* Modal Motifs d'hospitalisation */}
      <Modal
        opened={hospitalModalOpened}
        onClose={closeHospitalModal}
        title={
          <div className="flex items-center gap-2">
            <IconHospital size={20} className="text-blue-500" />
            <Text fw={600} className="text-blue-500">Motifs d&apos;hospitalisation</Text>
          </div>
        }
        size="xl"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <Tabs value={modalActiveTab} onChange={setModalActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="general">Général</Tabs.Tab>
            <Tabs.Tab value="procedures">Procédures</Tabs.Tab>
            <Tabs.Tab value="interventions">Interventions</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="general" pt="md">
            <form onSubmit={hospitalForm.onSubmit(handleHospitalSubmit)}>
              <Stack gap="md">
                <Grid>
                  <Grid.Col span={6}>
                    <TextInput
                      label="Code"
                      placeholder=""
                      required
                      styles={{ label: { color: 'red' } }}
                      {...hospitalForm.getInputProps('code')}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Textarea
                      label="Description"
                      placeholder=""
                      required
                      styles={{ label: { color: 'red' } }}
                      {...hospitalForm.getInputProps('description')}
                    />
                  </Grid.Col>
                </Grid>

                <Grid>
                  <Grid.Col span={6}>
                    <NumberInput
                      label="Durée (jours)"
                      placeholder=""
                      min={1}
                      {...hospitalForm.getInputProps('duree')}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <div>
                      <Text size="sm" fw={500} mb={5}>Couleur</Text>
                      <div className="flex items-center gap-2">
                        <IconColorPicker size={16} />
                        <ColorPicker
                          format="hex"
                          {...hospitalForm.getInputProps('couleur')}
                        />
                      </div>
                    </div>
                  </Grid.Col>
                </Grid>

                <Group justify="flex-end" mt="md">
                  <Button variant="outline" color="gray" onClick={closeHospitalModal}>
                    Annuler
                  </Button>
                  <Button type="submit" color="red">
                    Enregistrer
                  </Button>
                </Group>
              </Stack>
            </form>
          </Tabs.Panel>

          <Tabs.Panel value="procedures" pt="md">
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-4">
                <IconStethoscope size={20} className="text-blue-500" />
                <Text fw={600} className="text-blue-500">Procédures</Text>
                <ActionIcon variant="subtle" size="sm">
                  <IconSearch size={16} />
                </ActionIcon>
              </div>
              <div className="bg-yellow-100 p-3 rounded mb-4">
                <Text size="sm" className="text-yellow-800">⚠️ Aucun élément trouvé.</Text>
              </div>
              <DataTable
                withTableBorder
                borderRadius="sm"
                withColumnBorders
                striped
                highlightOnHover
                records={[]}
                columns={[
                  { accessor: 'code', title: 'Code', width: 120 },
                  { accessor: 'actes', title: 'Actes', width: 120 },
                  { accessor: 'qte', title: 'Qté', width: 80 },
                  { accessor: 'honoraire', title: 'Honoraire', width: 120 },
                  { accessor: 'remise', title: 'Remise/Unité', width: 120 },
                  { accessor: 'total', title: 'Total', width: 120 },
                  { accessor: 'commentaire', title: 'Commentaire', width: 150 }
                ]}
                minHeight={200}
                noRecordsText="Aucun élément trouvé."
              />
              <div className="mt-4 flex justify-between items-center">
                <Text fw={600}>Total Net: 0,00 DH</Text>
                <Group>
                  <Button variant="outline" color="gray" onClick={closeHospitalModal}>
                    Annuler
                  </Button>
                  <Button color="red">
                    Enregistrer
                  </Button>
                </Group>
              </div>
            </div>
          </Tabs.Panel>

          <Tabs.Panel value="interventions" pt="md">
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-4">
                <IconStethoscope size={20} className="text-blue-500" />
                <Text fw={600} className="text-blue-500">Procédures</Text>
                <ActionIcon variant="subtle" size="sm">
                  <IconSearch size={16} />
                </ActionIcon>
              </div>
              <div className="bg-yellow-100 p-3 rounded mb-4">
                <Text size="sm" className="text-yellow-800">⚠️ Aucun élément trouvé.</Text>
              </div>
              <DataTable
                withTableBorder
                borderRadius="sm"
                withColumnBorders
                striped
                highlightOnHover
                records={[]}
                columns={[
                  { accessor: 'qte', title: 'Qté', width: 80 },
                  { accessor: 'honoraire', title: 'Honoraire', width: 120 },
                  { accessor: 'remise', title: 'Remise/Unité', width: 120 },
                  { accessor: 'total', title: 'Total', width: 120 },
                  { accessor: 'commentaire', title: 'Commentaire', width: 150 }
                ]}
                minHeight={200}
                noRecordsText="Aucun élément trouvé."
              />
              <div className="mt-4">
                <Button
                  leftSection={<IconPlus size={16} />}
                  variant="outline"
                  size="sm"
                >
                  Ajouter intervention
                </Button>
              </div>
              <div className="mt-4 flex justify-between items-center">
                <Text fw={600}>Total Net: 0,00 DH</Text>
                <Group>
                  <Button variant="outline" color="gray" onClick={closeHospitalModal}>
                    Annuler
                  </Button>
                  <Button color="red">
                    Enregistrer
                  </Button>
                </Group>
              </div>
            </div>
          </Tabs.Panel>
        </Tabs>
      </Modal>

      {/* Modal Motifs d'intervention */}
      <Modal
        opened={interventionModalOpened}
        onClose={closeInterventionModal}
        title={
          <div className="flex items-center gap-2">
            <IconStethoscope size={20} className="text-blue-500" />
            <Text fw={600} className="text-blue-500">Motifs d&apos;intervention</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={interventionForm.onSubmit(handleInterventionSubmit)}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...interventionForm.getInputProps('code')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Textarea
                  label="Description"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...interventionForm.getInputProps('description')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Durée (min)"
                  placeholder=""
                  min={1}
                  {...interventionForm.getInputProps('duree')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <div>
                  <Text size="sm" fw={500} mb={5}>Couleur</Text>
                  <div className="flex items-center gap-2">
                    <IconColorPicker size={16} />
                    <ColorPicker
                      format="hex"
                      {...interventionForm.getInputProps('couleur')}
                    />
                  </div>
                </div>
              </Grid.Col>
            </Grid>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeInterventionModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Procédure */}
      <Modal
        opened={procedureModalOpened}
        onClose={closeProcedureModal}
        title={
          <div className="flex items-center gap-2">
            <IconStethoscope size={20} className="text-blue-500" />
            <Text fw={600} className="text-blue-500">Procédure</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={procedureForm.onSubmit(() => {})}>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...procedureForm.getInputProps('code')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Nom"
                  placeholder=""
                  required
                  styles={{ label: { color: 'red' } }}
                  {...procedureForm.getInputProps('nom')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Honoraire"
                  placeholder=""
                  min={0}
                  {...procedureForm.getInputProps('honoraire')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Services désignés"
                  placeholder=""
                  data={[]}
                  {...procedureForm.getInputProps('servicesDesignes')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Code NGAP"
                  placeholder=""
                  {...procedureForm.getInputProps('codeNGAP')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Code CCAM"
                  placeholder=""
                  {...procedureForm.getInputProps('codeCCAM')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="TNR"
                  placeholder=""
                  {...procedureForm.getInputProps('tnr')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Modalité"
                  placeholder=""
                  data={[]}
                  rightSection={<IconPlus size={16} />}
                  {...procedureForm.getInputProps('modalite')}
                />
              </Grid.Col>
            </Grid>

            <Checkbox
              label="Remboursable"
              {...procedureForm.getInputProps('remboursable', { type: 'checkbox' })}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeProcedureModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Étage */}
      <Modal
        opened={etageModalOpened}
        onClose={closeEtageModal}
        title={
          <div className="flex items-center gap-2">
            <IconPlus size={20} className="text-white" />
            <Text fw={600} className="text-white">Ajouter Étage</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#0ea5e9', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={etageForm.onSubmit(() => {})}>
          <Stack gap="md">
            <div className="flex items-center gap-4">
              <TextInput
                label="Nom"
                placeholder=""
                required
                styles={{ label: { color: 'red' } }}
                className="flex-1"
                {...etageForm.getInputProps('nom')}
              />
              <div className="flex items-center gap-2 mt-6">
                <IconColorPicker size={16} className="text-gray-400" />
                <Text size="sm" className="text-gray-600">Couleur</Text>
              </div>
            </div>

            <Textarea
              label="Description"
              placeholder=""
              rows={3}
              {...etageForm.getInputProps('description')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeEtageModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Chambre */}
      <Modal
        opened={chambreModalOpened}
        onClose={closeChambreModal}
        title={
          <div className="flex items-center gap-2">
            <IconPlus size={20} className="text-white" />
            <Text fw={600} className="text-white">Ajouter Chambre</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#0ea5e9', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={chambreForm.onSubmit(() => {})}>
          <Stack gap="md">
            <div className="flex items-center gap-4">
              <TextInput
                label="Nom"
                placeholder=""
                required
                styles={{ label: { color: 'red' } }}
                className="flex-1"
                {...chambreForm.getInputProps('nom')}
              />
              <div className="flex items-center gap-2 mt-6">
                <IconColorPicker size={16} className="text-gray-400" />
                <Text size="sm" className="text-gray-600">Couleur</Text>
              </div>
            </div>

            <Textarea
              label="Description"
              placeholder=""
              rows={3}
              {...chambreForm.getInputProps('description')}
            />

            <Select
              label="Étage"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              data={[]}
              rightSection={<IconPlus size={16} />}
              {...chambreForm.getInputProps('etage')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeChambreModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Lit */}
      <Modal
        opened={litModalOpened}
        onClose={closeLitModal}
        title={
          <div className="flex items-center gap-2">
            <IconPlus size={20} className="text-white" />
            <Text fw={600} className="text-white">Ajouter Lit</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#0ea5e9', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={litForm.onSubmit(() => {})}>
          <Stack gap="md">
            <TextInput
              label="Nom"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...litForm.getInputProps('nom')}
            />

            <Textarea
              label="Description"
              placeholder=""
              rows={3}
              {...litForm.getInputProps('description')}
            />

            <Select
              label="Chambre"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              data={[]}
              rightSection={<IconPlus size={16} />}
              {...litForm.getInputProps('chambre')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeLitModal}>
                Annuler
              </Button>
              <Button type="submit" color="red">
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default PramètrageDhospitalisation;
