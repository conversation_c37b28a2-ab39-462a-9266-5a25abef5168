'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
// Icons
import {
   IconAlertCircle,
  
} from '@tabler/icons-react';
// Services
import authService from '@/services/authService';
import userManagementService, { SubscriptionPackage } from '@/services/userManagementService';
import settingsService from '@/services/settingsService';
// Components
import SubscriptionCard from '@/components/settings/SubscriptionCard';
// Using the UserSettings interface from the settingsService
interface UserSettings {
  theme: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  appointmentReminders: boolean;
  marketingEmails: boolean;
  language: string;
  timeFormat: string;
  dateFormat: string;
  calendarView: string;
  calendarStartHour: number;
  calendarEndHour: number;
  workingHoursSettings: {
    morningStart: string;
    morningEnd: string;
    afternoonStart: string;
    afternoonEnd: string;
    breakEnabled: boolean;
    breakDuration: number;
  };
  workDaysSettings: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturdayFirstHalf: boolean;
    vacationDays: number;
    holidays: number;
    emergencyDays: number;
    vacationPeriods: {
      startDate: Date | null;
      endDate: Date | null;
      description: string;
    }[];
    holidayDates: {
      date: Date | null;
      description: string;
    }[];
    emergencyDates: {
      date: Date | null;
      reason: string;
    }[];
  };
  privacySettings: {
    showProfileToOtherDoctors: boolean;
    shareAnonymizedDataForResearch: boolean;
    allowPatientFeedback: boolean;
  };
  accessibilitySettings: {
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
    fontSize: number;
  };
}

export default function Subscription() {
  const [loading, setLoading] = useState(true);

  const [, setSettings] = useState<UserSettings | null>(null);
  const [subscription, setSubscription] = useState<SubscriptionPackage | null>(null);
  const [userCounts, setUserCounts] = useState({ assistants: 0, patients: 0 });
  const [settingsLoaded, setSettingsLoaded] = useState(false);
  const router = useRouter();
  
  const form = useForm({
    initialValues: {
      theme: 'light',
      emailNotifications: true,
      smsNotifications: true,
      appointmentReminders: true,
      marketingEmails: false,
      language: 'en',
      timeFormat: '12h',
      dateFormat: 'MM/DD/YYYY',
      calendarView: 'month',
      calendarStartHour: 8,
      calendarEndHour: 18,
      workingHoursSettings: {
        morningStart: '09:00',
        morningEnd: '12:00',
        afternoonStart: '13:00',
        afternoonEnd: '17:00',
        breakEnabled: true,
        breakDuration: 60,
      },
      workDaysSettings: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturdayFirstHalf: true,
        vacationDays: 20,
        holidays: 10,
        emergencyDays: 5,
        vacationPeriods: [] as Array<{
          startDate: Date | null;
          endDate: Date | null;
          description: string;
        }>,
        holidayDates: [] as Array<{
          date: Date | null;
          description: string;
        }>,
        emergencyDates: [] as Array<{
          date: Date | null;
          reason: string;
        }>,
      },
      privacySettings: {
        showProfileToOtherDoctors: true,
        shareAnonymizedDataForResearch: true,
        allowPatientFeedback: true,
      },
      accessibilitySettings: {
        highContrast: false,
        largeText: false,
        screenReader: false,
        fontSize: 16,
      },
    },
  });

  // Effect to update document font size when it changes in the form
  useEffect(() => {
    if (!loading && form.values.accessibilitySettings) {
      document.documentElement.style.fontSize = `${form.values.accessibilitySettings.fontSize}px`;
    }
  }, [loading, form.values.accessibilitySettings]);

  useEffect(() => {
    // Avoid loading settings if already loaded
    if (settingsLoaded) {
      return;
    }

    const fetchSettings = async () => {
      try {
        setLoading(true);

        // Check if user is authenticated
        const isAuth = await authService.validateAuthentication();
        if (!isAuth) {
          router.push('/login');
          return;
        }

        // Fetch user settings from the settings service
        const userSettings = await settingsService.getUserSettings();

        // Update the theme to match the current color scheme
       

        setSettings(userSettings);

        // Update the appointment system with the current settings without showing notification
        // This ensures the web-frontend has the latest settings
        await settingsService.updateAppointmentSystem(userSettings, false);
        console.log('Settings updated in localStorage for web-frontend without notification');

        // Fetch subscription data
        const currentSubscription = await userManagementService.getCurrentSubscription();
        setSubscription(currentSubscription);

        // Fetch user counts
        const counts = await userManagementService.getUserCounts();
        setUserCounts(counts);

        // Set form values
        form.setValues({
          theme: userSettings.theme,
          emailNotifications: userSettings.emailNotifications,
          smsNotifications: userSettings.smsNotifications,
          appointmentReminders: userSettings.appointmentReminders,
          marketingEmails: userSettings.marketingEmails,
          language: userSettings.language,
          timeFormat: userSettings.timeFormat,
          dateFormat: userSettings.dateFormat,
          calendarView: userSettings.calendarView,
          calendarStartHour: userSettings.calendarStartHour,
          calendarEndHour: userSettings.calendarEndHour,
          workingHoursSettings: {
            morningStart: userSettings.workingHoursSettings.morningStart,
            morningEnd: userSettings.workingHoursSettings.morningEnd,
            afternoonStart: userSettings.workingHoursSettings.afternoonStart,
            afternoonEnd: userSettings.workingHoursSettings.afternoonEnd,
            breakEnabled: userSettings.workingHoursSettings.breakEnabled,
            breakDuration: userSettings.workingHoursSettings.breakDuration,
          },
          workDaysSettings: {
            monday: userSettings.workDaysSettings.monday,
            tuesday: userSettings.workDaysSettings.tuesday,
            wednesday: userSettings.workDaysSettings.wednesday,
            thursday: userSettings.workDaysSettings.thursday,
            friday: userSettings.workDaysSettings.friday,
            saturdayFirstHalf: userSettings.workDaysSettings.saturdayFirstHalf,
            vacationDays: userSettings.workDaysSettings.vacationDays,
            holidays: userSettings.workDaysSettings.holidays,
            emergencyDays: userSettings.workDaysSettings.emergencyDays,
            vacationPeriods: userSettings.workDaysSettings.vacationPeriods,
            holidayDates: userSettings.workDaysSettings.holidayDates,
            emergencyDates: userSettings.workDaysSettings.emergencyDates,
          },
          privacySettings: {
            showProfileToOtherDoctors: userSettings.privacySettings.showProfileToOtherDoctors,
            shareAnonymizedDataForResearch: userSettings.privacySettings.shareAnonymizedDataForResearch,
            allowPatientFeedback: userSettings.privacySettings.allowPatientFeedback,
          },
          accessibilitySettings: {
            highContrast: userSettings.accessibilitySettings.highContrast,
            largeText: userSettings.accessibilitySettings.largeText,
            screenReader: userSettings.accessibilitySettings.screenReader,
            fontSize: userSettings.accessibilitySettings.fontSize || 16,
          },
        });

        // Mark settings as loaded
        setSettingsLoaded(true);
      } catch (error) {
        console.error('Error fetching settings:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to load settings. Please try again later.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [, router, settingsLoaded]);

  

  return (
<>
      {/* <Group mb="xl">
        <Button
          leftSection={<IconArrowLeft size={16} />}
          variant="subtle"
          onClick={() => router.push('/dashboard')}
        >
          Back to Dashboard
        </Button>
        <Title>Doctor Settings</Title>
      </Group> */}

      {/* Subscription Management Card */}
      <SubscriptionCard
        subscription={subscription}
        userCounts={userCounts}
      />
    </>
  );
}





