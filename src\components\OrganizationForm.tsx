
import {
  Box,
  Button,
  Group,
  Stack,
  Switch,
  TextInput,
  Textarea,
  Title,
Modal,
  MultiSelect,
  Divider,
} from '@mantine/core';
import {ListeDesConventions } from '@/components/ListeDesConventions'
import { Icon } from '@mdi/react';
import {
  mdiAccountPlus,
  mdiClose,
  mdiTextBoxSearch,
} from '@mdi/js';
import { useState } from 'react';

type OrganizationFormProps = {
  onCancel: () => void;
  onSubmit: (data: any) => void;
};

export function OrganizationForm({ onCancel, onSubmit }: OrganizationFormProps) {
  const [name, setName] = useState('');
  const [contactFullName, setContactFullName] = useState('');
  const [hasRegNum, setHasRegNum] = useState(false);
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [address, setAddress] = useState('');
  const [conventions, setConventions] = useState<string[]>([]);
    const [isListeDesConventionsModalOpen, setIsListeDesConventionsModalOpen] = useState(false);
  const availableConventions = [
    { value: 'mut1', label: 'Mutuelle 1' },
    { value: 'mut2', label: 'Mutuelle 2' },
    { value: 'mut3', label: 'Mutuelle 3' },
  ];

  const isValid = name.trim() !== '';

  const handleSubmit = () => {
    onSubmit({
      name,
      contactFullName,
      hasRegNum,
      phone,
      email,
      address,
      conventions,
    });
  };

  return (
    <>
    <Box>
      {/* Toolbar */}
      <Group justify="space-between" px="md" py="xs" bg="var(--mantine-color-blue-light)">
        <Group>
          <Icon path={mdiAccountPlus} size={1} />
          <Title order={3}>Organisme</Title>
        </Group>
        <Button
          variant="subtle"
          color="gray"
          onClick={onCancel}
          leftSection={<Icon path={mdiClose} size={0.9} />}
        />
      </Group>

      {/* Content */}
      <Stack p="md" gap="sm">
        <Group grow>
          <TextInput
            label="Nom"
            required
            value={name}
            onChange={(e) => setName(e.currentTarget.value)}
          />
          <TextInput
            label="Nom complet du contact"
            value={contactFullName}
            onChange={(e) => setContactFullName(e.currentTarget.value)}
          />
        </Group>

        <Switch
          label="Numéro d'immatriculation obligatoire"
          checked={hasRegNum}
          onChange={(e) => setHasRegNum(e.currentTarget.checked)}
        />

        <Group grow>
          <TextInput
            label="Téléphone"
            value={phone}
            onChange={(e) => setPhone(e.currentTarget.value)}
          />
          <TextInput
            label="Email"
            value={email}
            onChange={(e) => setEmail(e.currentTarget.value)}
          />
        </Group>

        <Textarea
          label="Adresse"
          minRows={2}
          value={address}
          onChange={(e) => setAddress(e.currentTarget.value)}
        />

        {/* Conventions */}
        <Group justify="space-between">
          <label style={{ fontWeight: 500 }}>Convention</label>
          <Button
            size="xs"
            leftSection={<Icon path={mdiTextBoxSearch} size={0.8} />}
            variant="light"
            onClick={() => setIsListeDesConventionsModalOpen(true)}
          >
            Sélectionner
          </Button>
        </Group>
        <MultiSelect
          data={availableConventions}
          placeholder="Ajouter"
          value={conventions}
          onChange={setConventions}
          searchable
        />
      </Stack>

      <Divider my="sm" />

      {/* Actions */}
      <Group justify="flex-end" px="md" pb="md">
        <Button
          variant="filled"
          color="blue"
          onClick={handleSubmit}
          disabled={!isValid}
        >
          Enregistrer
        </Button>
        <Button variant="outline" color="red" onClick={onCancel}>
          Annuler
        </Button>
      </Group>
    </Box>
       <Modal opened={isListeDesConventionsModalOpen} size={"90%"}
                   onClose={() => setIsListeDesConventionsModalOpen(false)} withCloseButton={false}transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }} centered
                   >
               <ListeDesConventions
                  onCancel={() => setIsListeDesConventionsModalOpen(false)}
                  onSubmit={(data) => console.log('Données soumises:', data)}
                />
                 </Modal>
                 </>
  );
}
