'use client';
import { useState, useEffect } from 'react';
// import Image from 'next/legacy/image'
import { useSearchParams,  } from 'next/navigation';
import Languages from "~/layout/navBarButton/Iconbar/Languagespage";
import Link from 'next/link';
import {
  TextInput,
  PasswordInput,
  Paper,
  Title,
  Group,
  Button,
  Text,
  Anchor,
  Divider,
  Stack,
  Stepper,
  Radio,
  Card,
  Badge,
  Center,
  Alert,
  Box,
  Table,

} from '@mantine/core';
import { IconInfoTriangle } from '@tabler/icons-react';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconCreditCard, IconMessage, IconAlertCircle } from '@tabler/icons-react';
import RegistrationLicenseActivation from '~/components/license/RegistrationLicenseActivation';
import PaymentErrorAlert from '~/components/payment/PaymentErrorAlert';
import paymentConfigService, { PaymentConfiguration } from '~/services/paymentConfigService';
import { useTranslation } from 'react-i18next';
import dynamic from "next/dynamic";
const SwitchColorMode = dynamic(() => import("~/components/SwitchColorMode"), { ssr: false });
export default function RegisterPage() {
  const searchParams = useSearchParams();
  const { t, } = useTranslation('auth');
  const [loading, setLoading] = useState(false);
  const [active, setActive] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'message'>('card');
  const [subscriptionPeriod, setSubscriptionPeriod] = useState<'6months' | '1year'>('6months');
  const [, setLicenseNumber] = useState('');
  const [, setLicenseActivated] = useState(false);
  const [transactionId, setTransactionId] = useState('');
    interface PaymentErrorDetails {
    [key: string]: string | string[] | Record<string, unknown>;
  }
  const [paymentError, setPaymentError] = useState<{
    message: string;
    details?: PaymentErrorDetails;
    code?: string;
  } | null>(null);

  // Package information
  const [packageId, setPackageId] = useState<string | null>(null);
  const [packageName, setPackageName] = useState<string | null>(null);
  const [packagePrice, setPackagePrice] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<string | null>(null);

  // Package features (these would typically come from an API)
  const [maxUsers, setMaxUsers] = useState(3);
  const [maxAssistants, setMaxAssistants] = useState(2);
  const [expirationDate, setExpirationDate] = useState('');
  const [showDoctorNote, setShowDoctorNote] = useState(false);
  const [, setPaymentConfig] = useState<PaymentConfiguration | null>(null);
  const [, setLoadingPaymentConfig] = useState(false);

  // Initialize form first
  const form = useForm({
    initialValues: {
      email: '',
      password: '',
      password2: '',
      first_name: '',
      last_name: '',
      specialization: 'Dentaire',
      user_type: 'doctor',
    },
    validate: {
      email: (value) => (/^\S+@\S+\.[a-zA-Z]{2,}$/.test(value) ? null : 'Invalid email address'),
      password: (value) => (value.length < 8 ? 'Password must be at least 8 characters' : null),
      password2: (value, values) => (value !== values.password ? 'Passwords do not match' : null),
      first_name: (value) => (value.length < 1 ? 'First name is required' : null),
      last_name: (value) => (value.length < 1 ? 'Last name is required' : null),
    },
  });

  // Read package information from URL parameters and restore registration data if needed
  useEffect(() => {
    // Add null check for searchParams
    if (!searchParams) {
      return;
    }

    const pkgId = searchParams.get('packageId');
    const pkgName = searchParams.get('packageName');
    const pkgPrice = searchParams.get('packagePrice');
    const billing = searchParams.get('billingCycle');
    const resumeRegistration = searchParams.get('resumeRegistration');

    // Handle package information
    if (pkgId) {
      setPackageId(pkgId);
      console.log('Package ID from URL:', pkgId);
    }

    if (pkgName) {
      setPackageName(decodeURIComponent(pkgName));
      console.log('Package Name from URL:', decodeURIComponent(pkgName));
    }

    if (pkgPrice) {
      // Convert the price to the correct format based on the package name and billing cycle
      let formattedPrice = pkgPrice;

      if (pkgName) {
        const decodedName = decodeURIComponent(pkgName);
        // Use the billing variable from above instead of getting it again from searchParams
        // This avoids the TypeScript error since we've already checked searchParams for null

        // Try to get prices from payment configuration
        const paymentConfigStr = localStorage.getItem('paymentConfig');
        let config = null;

        if (paymentConfigStr) {
          try {
            config = JSON.parse(paymentConfigStr);
            console.log('Using payment configuration from localStorage:', config);
          } catch (error) {
            console.error('Error parsing payment config from localStorage:', error);
          }
        }

        if (billing === 'annual') {
          if (decodedName.toLowerCase().includes('premium')) {
            formattedPrice = config ? config.premium_price_annual.toString() : '499';
          } else if (decodedName.toLowerCase().includes('standard')) {
            formattedPrice = config ? config.standard_price_annual.toString() : '399';
          } else {
            formattedPrice = config ? config.basic_price_annual.toString() : '299';
          }
        } else {
          if (decodedName.toLowerCase().includes('premium')) {
            formattedPrice = config ? config.premium_price_6months.toString() : '299';
          } else if (decodedName.toLowerCase().includes('standard')) {
            formattedPrice = config ? config.standard_price_6months.toString() : '199';
          } else {
            formattedPrice = config ? config.basic_price_6months.toString() : '99';
          }
        }
      }

      setPackagePrice(formattedPrice);
      console.log('Package Price from URL:', pkgPrice);
      console.log('Formatted Package Price:', formattedPrice);
    }

    // Always update billing cycle and subscription period from URL parameters
    // This ensures that when user returns from plans page, the selection is properly applied
    if (billing) {
      setBillingCycle(billing);
      console.log('Billing cycle from URL:', billing);

      // Set subscription period based on billing cycle
      if (billing === 'annual') {
        setSubscriptionPeriod('1year');
        console.log('Setting subscription period to 1year for annual billing');

        // Calculate expiration date (1 year from now)
        const oneYearFromNow = new Date();
        oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
        setExpirationDate(oneYearFromNow.toLocaleDateString());

        // Set package features based on package name
        if (pkgName) {
          const decodedName = decodeURIComponent(pkgName);
          if (decodedName.toLowerCase().includes('premium')) {
            setMaxUsers(10);
            setMaxAssistants(5);
          } else if (decodedName.toLowerCase().includes('standard')) {
            setMaxUsers(5);
            setMaxAssistants(3);
          } else {
            setMaxUsers(3);
            setMaxAssistants(2);
          }
        }
      } else {
        setSubscriptionPeriod('6months');
        console.log('Setting subscription period to 6months for monthly billing');

        // Calculate expiration date (6 months from now)
        const sixMonthsFromNow = new Date();
        sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);
        setExpirationDate(sixMonthsFromNow.toLocaleDateString());

        // Set package features based on package name
        if (pkgName) {
          const decodedName = decodeURIComponent(pkgName);
          if (decodedName.toLowerCase().includes('premium')) {
            setMaxUsers(8);
            setMaxAssistants(4);
          } else if (decodedName.toLowerCase().includes('standard')) {
            setMaxUsers(4);
            setMaxAssistants(2);
          } else {
            setMaxUsers(2);
            setMaxAssistants(1);
          }
        }
      }
    }

    // If resuming registration, restore saved data
    if (resumeRegistration === 'true') {
      const savedData = localStorage.getItem('pendingRegistration');
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);

          // Restore form values
          form.setValues({
            email: parsedData.email || '',
            first_name: parsedData.first_name || '',
            last_name: parsedData.last_name || '',
            specialization: parsedData.specialization || '',
            password: '',
            password2: '',
          });

          // Show doctor note if specialization was selected
          if (parsedData.specialization) {
            setShowDoctorNote(true);
          }

          // Restore other state
          if (parsedData.active !== undefined && parsedData.active > 0) {
            setActive(parsedData.active);
          }

          // Only restore subscription period if not provided in URL
          // This allows URL parameters to take precedence when returning from plans page
          if (parsedData.subscriptionPeriod && !billing) {
            setSubscriptionPeriod(parsedData.subscriptionPeriod);
            console.log('Restored subscription period from saved data:', parsedData.subscriptionPeriod);
          }

          if (parsedData.paymentMethod) {
            setPaymentMethod(parsedData.paymentMethod);
          }

          console.log('Restored registration data:', parsedData);
        } catch (error) {
          console.error('Error parsing saved registration data:', error);
        }
      }
    }
  }, [searchParams]);

  const nextStep = async () => {
    if (active === 0) {
      // Validate first step form fields
      const errors = form.validate();
      if (errors.hasErrors) {
        return;
      }

      // Check for duplicate email before proceeding
      try {
        setLoading(true);

        // Import axios directly
        const axios = (await import('axios')).default;

        console.log('Checking email:', form.values.email.trim());
        interface EmailCheckResult {
          exists: boolean;
          is_activated?: boolean;
          [key: string]: unknown;
        }
        // Create a function to handle the email check result
        const handleEmailCheckResult = (emailCheckResult: EmailCheckResult) => {
          if (emailCheckResult.exists) {
            // Email exists, check if it's activated
            if (emailCheckResult.is_activated) {
              // Account is already activated, show error
              notifications.show({
                title: 'Email Already Registered',
                message: 'This email is already registered and activated. Please login instead.',
                color: 'red',
              });
              return false; // Don't proceed
            } else {
              // Account exists but not activated, redirect to license activation
              notifications.show({
                title: 'Account Pending Activation',
                message: 'This email is registered but not activated. Redirecting to license activation page.',
                color: 'yellow',
              });

              // Redirect to license activation page
              window.location.href = 'http://localhost:3001/license?message=pending_activation';
              return false; // Don't proceed
            }
          }
          return true; // Proceed with registration
        };

        // Try using the API Gateway first (if available)
        try {
          console.log('Trying API Gateway for email check');
          const gatewayResponse = await axios.get(`http://localhost:8080/api/auth/check-email/?email=${encodeURIComponent(form.values.email.trim())}`, {
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 3000 // 3 second timeout
          });

          console.log('API Gateway response:', gatewayResponse.data);
          if (!handleEmailCheckResult(gatewayResponse.data)) {
            return; // Stop if email exists
          }
        } catch (gatewayError) {
          console.log('API Gateway not available or error, trying direct service:', gatewayError);

          // Try GET method directly to auth service
          try {
            console.log('Trying direct GET to auth service');
            const checkEmailResponse = await axios.get(`http://localhost:8000/api/auth/check-email/?email=${encodeURIComponent(form.values.email.trim())}`, {
              headers: {
                'Content-Type': 'application/json'
              },
              timeout: 3000 // 3 second timeout
            });

            const emailCheckResult = checkEmailResponse.data;
            console.log('Email check result (GET):', emailCheckResult);

            if (!handleEmailCheckResult(emailCheckResult)) {
              return; // Stop if email exists
            }
          } catch (getError) {
            console.error('Error with GET method, trying POST:', getError);

            // Fallback to POST method if GET fails
            try {
              console.log('Trying POST method');
              const checkEmailResponse = await axios.post('http://localhost:8000/api/auth/check-email/', {
                email: form.values.email.trim()
              }, {
                headers: {
                  'Content-Type': 'application/json'
                },
                timeout: 3000 // 3 second timeout
              });

              const emailCheckResult = checkEmailResponse.data;
              console.log('Email check result (POST):', emailCheckResult);

              if (!handleEmailCheckResult(emailCheckResult)) {
                return; // Stop if email exists
              }
            } catch (postError) {
              console.error('All email check methods failed:', postError);

              // If all methods fail, show a detailed error
              notifications.show({
                title: 'Connection Error',
                message: 'Unable to connect to the server. Please check your internet connection and try again.',
                color: 'red',
                autoClose: false,
              });
              return;
            }
          }
        }

        // If we get here, the email doesn't exist, proceed with registration
        // Store form data in localStorage when moving from step 1 to step 2
        // This ensures we have the password data for final registration
        const formData = {
          email: form.values.email,
          first_name: form.values.first_name,
          last_name: form.values.last_name,
          specialization: form.values.specialization,
          password: form.values.password,
          password2: form.values.password2,
          active: active + 1, // Set to the next step
          subscriptionPeriod: subscriptionPeriod,
          paymentMethod: paymentMethod,
        };

        localStorage.setItem('pendingRegistration', JSON.stringify(formData));
        console.log('Saved form data with passwords to localStorage');

        // Proceed to next step
        setActive((current) => (current < 2 ? current + 1 : current));
      } catch (error) {
        console.error('Error in nextStep function:', error);
        notifications.show({
          title: 'Error',
          message: 'An unexpected error occurred. Please try again.',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    } else {
      // For other steps, just proceed
      setActive((current) => (current < 2 ? current + 1 : current));
    }
  };

  const prevStep = () => setActive((current) => (current > 0 ? current - 1 : current));

  // Import the alternative registration function


  const handlePayment = async () => {
    setLoading(true);
    // Clear any previous payment errors
    setPaymentError(null);

    try {
      // Validate transaction ID if payment method is "Send Instructions"
      if (paymentMethod === 'message') {
        if (!transactionId) {
          notifications.show({
            title: 'Transaction ID Required',
            message: 'Please enter the transaction ID from your payment.',
            color: 'red',
          });
          setLoading(false);
          return;
        }
      }

      // Get saved form data from localStorage to ensure we have passwords
      let password = form.values.password;
      let password2 = form.values.password2;

      // If passwords are empty, try to get them from localStorage
      if (!password || !password2) {
        console.log('Passwords are empty in form, trying to retrieve from localStorage');
        const savedData = localStorage.getItem('pendingRegistration');
        if (savedData) {
          try {
            const parsedData = JSON.parse(savedData);
            if (parsedData.password) {
              password = parsedData.password;
              console.log('Retrieved password from localStorage');
            }
            if (parsedData.password2) {
              password2 = parsedData.password2;
              console.log('Retrieved password2 from localStorage');
            }
          } catch (error) {
            console.error('Error parsing saved registration data:', error);
          }
        }
      }

      // For the "Send Instructions" option, we need to capture the transaction ID
      let transaction_id = '';
      let payment_method = '';
      let payment_status = '';
      let payment_reference = '';
      let payment_bank = '';
      let payment_account = '';
      let payment_amount = '';

      if (paymentMethod === 'message') {
        // Validate transaction ID format
        if (!transactionId || transactionId.trim() === '') {
          notifications.show({
            title: 'Transaction ID Required',
            message: 'Please enter the transaction ID from your payment.',
            color: 'red',
          });
          setLoading(false);
          return;
        }

        // Format the transaction ID if it doesn't already have the TXN- prefix
        if (!transactionId.startsWith('TXN-')) {
          const today = new Date();
          const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
          transaction_id = `TXN-${dateStr}-${transactionId.toUpperCase()}`;
        } else {
          transaction_id = transactionId.toUpperCase();
        }

        // Store transaction ID in localStorage for recovery if needed
        try {
          localStorage.setItem('lastTransactionId', transaction_id);
        } catch (e) {
          console.error('Failed to store transaction ID in localStorage:', e);
        }

        // Get payment configuration from localStorage if available
        let bankName = 'Medical Bank';
        let accountNumber = '1234-5678-9012-3456';

        try {
          const paymentConfigStr = localStorage.getItem('paymentConfig');
          if (paymentConfigStr) {
            const paymentConfig = JSON.parse(paymentConfigStr);
            bankName = paymentConfig.bank_name || bankName;
            accountNumber = paymentConfig.account_number || accountNumber;
          }
        } catch (error) {
          console.error('Error parsing payment config from localStorage:', error);
        }

        payment_method = 'bank_transfer';
        payment_status = 'pending_verification';
        payment_reference = form.values.email.trim();
        payment_bank = bankName;
        payment_account = accountNumber;
        // Normalize payment amount to a valid number string
        payment_amount = packagePrice ? parseFloat(packagePrice).toString() : '299';

        console.log('Using provided transaction ID:', transaction_id);
      } else {
        // For credit card payments, generate a mock transaction ID with date format
        const today = new Date();
        const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
        const randomPart = Math.random().toString(36).substring(2, 10).toUpperCase();
        transaction_id = `TXN-${dateStr}-${randomPart}`;
        console.log('Generated transaction ID:', transaction_id);
        payment_method = 'credit_card';
        payment_status = 'completed';
        // Normalize payment amount to a valid number string
        payment_amount = packagePrice ? parseFloat(packagePrice).toString() : '299';
      }

      // Prepare registration data with all required fields
      const registrationData = {
        email: form.values.email.trim(),
        password: password,
        password2: password2,
        first_name: form.values.first_name.trim(),
        last_name: form.values.last_name.trim(),
        user_type: 'doctor',
        // Additional doctor-specific fields
        specialization: form.values.specialization || '',
        // Don't generate a random license number - the backend will generate a secure one
        license_number: '',
        // Include subscription information
        subscription_period: subscriptionPeriod,
        // Include package information
        package_id: packageId ? packageId.toString() : '',
        package_name: packageName || '',
        package_price: packagePrice ? parseFloat(packagePrice).toString() : '',
        billing_cycle: billingCycle || '',
        // Include payment information
        transaction_id: transaction_id,
        payment_method: payment_method,
        payment_status: payment_status,
        payment_reference: payment_reference,
        payment_bank: payment_bank,
        payment_account: payment_account,
        payment_amount: payment_amount,
        // Add a flag to prevent duplicate license creation
        skip_license_creation: false,
        create_single_license: true
      };

      // Create a simplified version of the registration data with only the essential fields
      // This might help if the server is having issues with some of the fields
      const simplifiedRegistrationData = {
        email: form.values.email.trim(),
        password: password,
        password2: password2,
        first_name: form.values.first_name.trim(),
        last_name: form.values.last_name.trim(),
        user_type: 'doctor',
        specialization: form.values.specialization || '',
        transaction_id: transaction_id,
        // Add a flag to prevent duplicate license creation
        skip_license_creation: false,
        create_single_license: true
      };

      console.log('Prepared registration data:', registrationData);
      console.log('Detailed registration data:', JSON.stringify(registrationData, null, 2));

      // Log specific fields that might cause issues
      console.log('package_id type:', typeof registrationData.package_id, 'value:', registrationData.package_id);
      console.log('package_price type:', typeof registrationData.package_price, 'value:', registrationData.package_price);
      console.log('payment_amount type:', typeof registrationData.payment_amount, 'value:', registrationData.payment_amount);

      // Import axios directly
      const axios = (await import('axios')).default;

      // Send registration data directly to the backend
      console.log('Sending request to http://localhost:8000/api/auth/register/');

      // First try with simplified data
      try {
        console.log('Trying with simplified registration data...');
        const response = await axios.post('http://localhost:8000/api/auth/register/', simplifiedRegistrationData, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 seconds timeout
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        const registeredUser = response.data;

        console.log('Registration successful:', registeredUser);

        // Continue with the rest of the success flow
        // Extract the license number from the response
        const generatedLicenseNumber = registeredUser.license_number || 'DOC-' + Math.random().toString(36).substring(2, 10).toUpperCase();
        console.log('License number received:', generatedLicenseNumber);
        setLicenseNumber(generatedLicenseNumber);

        // Store the license number in sessionStorage for the activation component to use
        try {
          sessionStorage.setItem('licenseNumber', generatedLicenseNumber);
          // Also store the full user data in localStorage for reference
          localStorage.setItem('registeredUser', JSON.stringify(registeredUser));
        } catch (e) {
          console.error('Error storing license data in storage:', e);
        }

        // Move to the license activation step
        setActive(2);
        console.log('Moving to step 2 (license activation)');

        notifications.show({
          title: 'Registration Successful',
          message: 'Your account has been created. Please check your email for the activation code.',
          color: 'green',
        });

        // Clear the stored registration data
        localStorage.removeItem('pendingRegistration');

        return; // Exit the function after successful registration
      } catch (directError) {
        console.error('Direct API call failed, trying API Gateway:', directError);

        // Try using the API Gateway as a fallback with simplified data
        try {
          console.log('Trying API Gateway with simplified registration data...');
          const gatewayResponse = await axios.post('http://localhost:8080/api/auth/register/', simplifiedRegistrationData, {
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 10000 // 10 seconds timeout
          });

          console.log('Gateway Response status:', gatewayResponse.status);
          console.log('Gateway Response headers:', gatewayResponse.headers);
          const registeredUser = gatewayResponse.data;

          console.log('Registration via gateway successful:', registeredUser);

          // Continue with the rest of the success flow
          // Extract the license number from the response
          const generatedLicenseNumber = registeredUser.license_number || 'DOC-' + Math.random().toString(36).substring(2, 10).toUpperCase();
          console.log('License number received:', generatedLicenseNumber);
          setLicenseNumber(generatedLicenseNumber);

          // Store the license number in sessionStorage for the activation component to use
          try {
            sessionStorage.setItem('licenseNumber', generatedLicenseNumber);
            // Also store the full user data in localStorage for reference
            localStorage.setItem('registeredUser', JSON.stringify(registeredUser));
          } catch (e) {
            console.error('Error storing license data in storage:', e);
          }

          // Move to the license activation step
          setActive(2);
          console.log('Moving to step 2 (license activation)');

          notifications.show({
            title: 'Registration Successful',
            message: 'Your account has been created. Please check your email for the activation code.',
            color: 'green',
          });

          // Clear the stored registration data
          localStorage.removeItem('pendingRegistration');

          return; // Exit the function after successful registration
        } catch (gatewayError) {
          console.error('Simplified data attempts failed, trying with full data as last resort:', gatewayError);

          // Last attempt with full data but using a different endpoint
          try {
            console.log('Last attempt with full registration data using alternative endpoint...');
            // Try using the register-alt endpoint which might have different logic
            const fullDataResponse = await axios.post('http://localhost:8000/api/auth/register/?no_duplicate_license=true', registrationData, {
              headers: {
                'Content-Type': 'application/json'
              },
              timeout: 15000 // 15 seconds timeout
            });

            console.log('Full data response status:', fullDataResponse.status);
            console.log('Full data response headers:', fullDataResponse.headers);
            const registeredUser = fullDataResponse.data;

            console.log('Registration with full data successful:', registeredUser);

            // Continue with the rest of the success flow
            // Extract the license number from the response
            const generatedLicenseNumber = registeredUser.license_number || 'DOC-' + Math.random().toString(36).substring(2, 10).toUpperCase();
            console.log('License number received:', generatedLicenseNumber);
            setLicenseNumber(generatedLicenseNumber);

            // Store the license number in sessionStorage for the activation component to use
            try {
              sessionStorage.setItem('licenseNumber', generatedLicenseNumber);
              // Also store the full user data in localStorage for reference
              localStorage.setItem('registeredUser', JSON.stringify(registeredUser));
            } catch (e) {
              console.error('Error storing license data in storage:', e);
            }

            // Move to the license activation step
            setActive(2);
            console.log('Moving to step 2 (license activation)');

            notifications.show({
              title: 'Registration Successful',
              message: 'Your account has been created. Please check your email for the activation code.',
              color: 'green',
            });

            // Clear the stored registration data
            localStorage.removeItem('pendingRegistration');

            return; // Exit the function after successful registration
          } catch (finalError) {
            console.error('Alternative endpoint attempt failed, trying one last approach:', finalError);

            // Final attempt with a two-step approach
            try {
              console.log('Final attempt: Creating user first without license...');
              // Create a version without license-related fields
              const userOnlyData = {
                email: form.values.email.trim(),
                password: password,
                password2: password2,
                first_name: form.values.first_name.trim(),
                last_name: form.values.last_name.trim(),
                user_type: 'doctor',
                specialization: form.values.specialization || '',
                skip_license_creation: true // Explicitly skip license creation
              };

              const userResponse = await axios.post('http://localhost:8000/api/auth/register/', userOnlyData, {
                headers: {
                  'Content-Type': 'application/json'
                },
                timeout: 10000
              });

              console.log('User created successfully:', userResponse.data);
              const registeredUser = userResponse.data;

              // Continue with the rest of the success flow
              const generatedLicenseNumber = registeredUser.license_number || 'DOC-' + Math.random().toString(36).substring(2, 10).toUpperCase();
              console.log('License number received or generated:', generatedLicenseNumber);
              setLicenseNumber(generatedLicenseNumber);

              try {
                sessionStorage.setItem('licenseNumber', generatedLicenseNumber);
                localStorage.setItem('registeredUser', JSON.stringify(registeredUser));
              } catch (e) {
                console.error('Error storing license data in storage:', e);
              }

              setActive(2);
              console.log('Moving to step 2 (license activation)');

              notifications.show({
                title: 'Registration Successful',
                message: 'Your account has been created. Please check your email for the activation code.',
                color: 'green',
              });

              localStorage.removeItem('pendingRegistration');

              return; // Exit the function after successful registration
            } catch (lastError) {
              console.error('All registration attempts failed:', lastError);
              throw lastError; // Re-throw the error to be caught by the outer catch block
            }
          }
        }
      }
    } catch (error) {
      console.error('Payment/Registration error:', error);

      // Log more detailed error information
      interface AxiosError {
        response?: {
          data?: unknown;
          status?: number;
          headers?: unknown;
        };
        request?: unknown;
        message?: string;
        config?: unknown;
      }
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
        console.error('Error response headers:', axiosError.response.headers);
      } else if (axiosError.request) {
        console.error('Error request:', axiosError.request);
      } else {
        console.error('Error message:', axiosError.message);
      }
      console.error('Error config:', axiosError.config);

        // Define a type for API errors
      interface ApiError {
        response?: {
          data?: unknown;
          status?: number;
        };
        request?: unknown;
        message?: string;
      }

      const apiError = error as ApiError;

      // Parse the error
      let errorMessage = 'An error occurred during the payment process. Please try again.';
      let errorDetails = null;
      let errorCode = 'PAYMENT_ERROR';

      // Extract error details from the response
      if (apiError.response && apiError.response.data) {
        if (typeof apiError.response.data === 'object' && apiError.response.data !== null) {
          // Format field errors into a readable message
          const responseData = apiError.response.data as Record<string, unknown>;
          errorMessage = Object.entries(responseData)
            .map(([field, errors]) => {
              if (Array.isArray(errors)) {
                return `${field}: ${errors.join(', ')}`;
              }
              return `${field}: ${errors}`;
            })
            .join('\n');

          errorDetails = responseData;
        } else if (
          typeof apiError.response.data === 'object' &&
          apiError.response.data !== null &&
          'detail' in (apiError.response.data as Record<string, unknown>) &&
          typeof (apiError.response.data as Record<string, unknown>).detail === 'string'
        ) {
          errorMessage = (apiError.response.data as Record<string, string>).detail;
        }

        if (
          typeof apiError.response.data === 'object' &&
          apiError.response.data !== null &&
          'code' in (apiError.response.data as Record<string, unknown>) &&
          typeof (apiError.response.data as Record<string, unknown>).code === 'string'
        ) {
          errorCode = (apiError.response.data as Record<string, string>).code;
        } else if (apiError.response.status) {
          errorCode = `HTTP_${apiError.response.status}`;
        }
      } else if (apiError.request) {
        errorMessage = 'Network error. Please check your internet connection.';
        errorCode = 'NETWORK_ERROR';
      } else if (apiError.message) {
        errorMessage = apiError.message;
      }

       // Check if this is a transaction ID validation error
      const isTransactionIdError =
        apiError.response?.data &&
        typeof apiError.response.data === 'object' &&
        (
          'transaction_id' in (apiError.response.data as Record<string, unknown>) ||
          (
            'detail' in (apiError.response.data as Record<string, unknown>) &&
            typeof (apiError.response.data as Record<string, unknown>).detail === 'string' &&
            ((apiError.response.data as Record<string, unknown>).detail as string).toLowerCase().includes('transaction')
          )
        );

      // For transaction ID errors, we want to show an inline error and let the user try again
      if (isTransactionIdError) {
        console.log('Transaction ID validation error detected');

        // Show a specific error message for transaction ID issues
        notifications.show({
          title: 'Transaction ID Error',
          message: 'The transaction ID you entered could not be verified. Please check and try again.',
          color: 'red',
          autoClose: 8000,
        });

        // Set the payment error state to display the error component
        setPaymentError({
          message: 'The transaction ID you entered could not be verified. Please check and try again.',
          details: errorDetails as PaymentErrorDetails | undefined,
          code: 'TRANSACTION_ID_ERROR'
        });

        // Don't redirect for transaction ID errors
        return;
      }
      // For severe errors, redirect to the error page
       // For server errors, show a more helpful message
      if (apiError.response && apiError.response.status && apiError.response.status >= 500) {
        // Show a more helpful error message for server errors
        const serverErrorMessage = "The server encountered an error processing your registration. This might be due to temporary maintenance or high traffic. Please try again in a few minutes.";

        notifications.show({
          title: 'Server Error',
          message: serverErrorMessage,
          color: 'red',
          autoClose: false, // Keep it visible until user dismisses it
        });

        // Set the payment error state to display the error component with a helpful message
        setPaymentError({
          message: serverErrorMessage,
          details: {
            suggestion: "You can try the following:",
            steps: [
              "Wait a few minutes and try again",
              "Check if your email is already registered",
              "Try a different browser or device",
              "Contact support if the problem persists"
            ]
          } as PaymentErrorDetails,
          code: 'SERVER_ERROR'
        });
      } else {
        // For less severe errors, show an inline error alert
        notifications.show({
          title: 'Registration Failed',
          message: errorMessage,
          color: 'red',
          autoClose: 8000, // Give more time to read the error
        });

        // Set the payment error state to display the error component
        setPaymentError({
          message: errorMessage,
          details: errorDetails as PaymentErrorDetails | undefined,
          code: errorCode
        });
      }
    } finally {
      setLoading(false);
    }
  };





  // State for specializations


  // Fetch payment configuration from the API
  useEffect(() => {
    const fetchPaymentConfig = async () => {
      try {
        setLoadingPaymentConfig(true);
        const config = await paymentConfigService.getActiveConfig();
        setPaymentConfig(config);

        // Store in localStorage for use in registration
        localStorage.setItem('paymentConfig', JSON.stringify(config));

        console.log('Payment configuration loaded:', config);
      } catch (error) {
        console.error('Error fetching payment configuration:', error);
      } finally {
        setLoadingPaymentConfig(false);
      }
    };

    fetchPaymentConfig();
  }, []);

  // Fetch specializations from the API


  return (
    <>
     <>
      <title>{t('page-title')} | Doctor Portal </title>
      <meta
        name="description"
        content={t('description')}
      />
      </>
      <Stack  mt={180}>
      <Center mb={10} >
          <Box >
            <Center >
          {/* <Image
              src="/logo.svg"
               alt="Doctor Portal logo"
              width={96}
              height={96}

            /> */}
            </Center>
            <Title order={1} ta="center" style={{ color: '#1c7ed6' }}>
              Doctor Portal
            </Title>
            <Text ta="center" c="dimmed">
              Manage your practice efficiently
            </Text>
          </Box>
        </Center>
      <Paper radius="md" p="xl" withBorder style={{
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.3)',
          width: '65vw'
        }} mb={40}>
        <Title order={2} ta="center" mt="md" mb="md">
          Create a Doctor Account
        </Title>

        <Text c="dimmed" size="sm" ta="center" mb="xl">
          Register to access the Doctor Portal
        </Text>

        <Stepper active={active} onStepClick={setActive}  allowNextStepsSelect={false}>
          <Stepper.Step label="Account Information" description="Personal details">
            <Box mt="xl">
              <Stack>
                <Group grow>
                  <TextInput
                    label="First Name"
                    placeholder="John"
                    required
                    {...form.getInputProps('first_name')}
                  />

                  <TextInput
                    label="Last Name"
                    placeholder="Smith"
                    required
                    {...form.getInputProps('last_name')}
                  />
                </Group>

                <TextInput
                  label="Email"
                  placeholder="<EMAIL>"
                  required
                  {...form.getInputProps('email')}
                />

     <TextInput
                  label="Specialization"
                  placeholder="Dentaire"
                   value={'specialization'}
                  required
                  {...form.getInputProps('specialization')}
                  rightSection={<IconInfoTriangle stroke={1.75} onClick={()=>setShowDoctorNote(true)}/>}
                   disabled
                />


                {showDoctorNote && (
                  <Alert
                    icon={<IconAlertCircle size={16} />}
                    color="blue"
                    title="Note for Doctors"
                    mt="md"
                  >
                    After registration, you&apos;ll be redirected to the doctor application. There, you can subscribe to a plan and create assistant and user accounts based on your subscription package.
                  </Alert>
                )}

                <Divider label="Security" labelPosition="center" my="lg" />

                <PasswordInput
                  label="Password"
                  placeholder="Create a strong password"
                  required
                  {...form.getInputProps('password')}
                />

                <PasswordInput
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  required
                  {...form.getInputProps('password2')}
                />
              </Stack>
            </Box>
          </Stepper.Step>

          <Stepper.Step label="Subscription" description="Choose your plan">
            <Box mt="xl">
              {packageName && (
                <Card withBorder shadow="sm" radius="md" mb="xl">
                  <Title order={3} ta="center" mb="md" c="blue">
                    {packageName} Package
                  </Title>

                  <Group justify="space-between" mb="xs">
                    <Text fw={500}>Price:</Text>
                    <Text>
                      ${packagePrice} {billingCycle === 'annual' ? '/ year' : '/ 6 months'}
                    </Text>
                  </Group>

                  <Group justify="space-between" mb="xs">
                    <Text fw={500}>Max Users:</Text>
                    <Text>{maxUsers}</Text>
                  </Group>

                  <Group justify="space-between" mb="xs">
                    <Text fw={500}>Max Assistants:</Text>
                    <Text>{maxAssistants}</Text>
                  </Group>

                  <Group justify="space-between" mb="xs">
                    <Text fw={500}>Expiration:</Text>
                    <Text>{expirationDate}</Text>
                  </Group>

                  <Button
                    variant="light"
                    color="blue"
                    fullWidth
                    mt="md"
                    onClick={() => {
                      // Save current registration data to localStorage
                      const registrationData = {
                        email: form.values.email,
                        first_name: form.values.first_name,
                        last_name: form.values.last_name,
                        specialization: form.values.specialization,
                        // Include password fields to ensure they're available for final registration
                        password: form.values.password,
                        password2: form.values.password2,
                        active: active,
                        subscriptionPeriod: subscriptionPeriod,
                        paymentMethod: paymentMethod,
                      };

                      localStorage.setItem('pendingRegistration', JSON.stringify(registrationData));

                      // Use absolute URL to ensure we stay in the doctor portal
                      window.location.href = 'http://localhost:3001/subscription/plans?changingPackage=true';
                    }}
                  >
                    Change Package
                  </Button>
                </Card>
              )}

              <Title order={3} ta="center" mb="md">Select Subscription Period</Title>

              {/* Always show the Modify Package button */}
              {!packageName && (
                <Card withBorder shadow="sm" radius="md" mb="xl">
                  <Alert
                    icon={<IconAlertCircle size={16} />}
                    color="yellow"
                    title="No Package Selected"
                    mb="md"
                  >
                    Please select a subscription package to continue.
                  </Alert>

                  <Button
                    variant="light"
                    color="blue"
                    fullWidth
                    onClick={() => {
                      // Save current registration data to localStorage
                      const registrationData = {
                        email: form.values.email,
                        first_name: form.values.first_name,
                        last_name: form.values.last_name,
                        specialization: form.values.specialization,
                        // Include password fields to ensure they're available for final registration
                        password: form.values.password,
                        password2: form.values.password2,
                        active: active,
                        subscriptionPeriod: subscriptionPeriod,
                        paymentMethod: paymentMethod,
                      };

                      localStorage.setItem('pendingRegistration', JSON.stringify(registrationData));

                      // Use absolute URL to ensure we stay in the doctor portal
                      window.location.href = 'http://localhost:3001/subscription/plans?changingPackage=true';
                    }}
                  >
                    Select Package
                  </Button>
                </Card>
              )}

              {/* Only show subscription period selection if a package is selected */}
              {packageName && (
                <Group justify="center" mb="xl">
                  <Radio.Group
                    value={subscriptionPeriod}
                    onChange={(value) => {
                      console.log('Subscription period changed to:', value);
                      setSubscriptionPeriod(value as '6months' | '1year');

                      // Update expiration date based on new subscription period
                      if (value === '1year') {
                        console.log('Setting 1 year subscription');
                        const oneYearFromNow = new Date();
                        oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
                        setExpirationDate(oneYearFromNow.toLocaleDateString());

                        // Update package price for annual billing
                        if (packageName) {
                          console.log('Package name:', packageName);

                          // Try to get prices from payment configuration
                          const paymentConfigStr = localStorage.getItem('paymentConfig');
                          let config = null;

                          if (paymentConfigStr) {
                            try {
                              config = JSON.parse(paymentConfigStr);
                              console.log('Using payment configuration from localStorage:', config);
                            } catch (error) {
                              console.error('Error parsing payment config from localStorage:', error);
                            }
                          }

                          if (packageName.toLowerCase().includes('premium')) {
                            console.log('Setting Premium annual price');
                            setPackagePrice(config ? config.premium_price_annual.toString() : '499');
                            setMaxUsers(config ? config.premium_max_users : 10);
                            setMaxAssistants(config ? config.premium_max_assistants : 5);
                          } else if (packageName.toLowerCase().includes('standard')) {
                            console.log('Setting Standard annual price');
                            setPackagePrice(config ? config.standard_price_annual.toString() : '399');
                            setMaxUsers(config ? config.standard_max_users : 5);
                            setMaxAssistants(config ? config.standard_max_assistants : 3);
                          } else {
                            console.log('Setting Basic annual price');
                            setPackagePrice(config ? config.basic_price_annual.toString() : '299');
                            setMaxUsers(config ? config.basic_max_users : 3);
                            setMaxAssistants(config ? config.basic_max_assistants : 2);
                          }
                        } else {
                          console.log('No package name, setting default annual price');
                          setPackagePrice('499');
                        }

                        // Update billing cycle - this is important for when the form is submitted
                        setBillingCycle('annual');
                        console.log('Updated to annual billing cycle');
                      } else {
                        console.log('Setting 6 months subscription');
                        const sixMonthsFromNow = new Date();
                        sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);
                        setExpirationDate(sixMonthsFromNow.toLocaleDateString());

                        // Update package price for monthly billing
                        if (packageName) {
                          console.log('Package name:', packageName);

                          // Try to get prices from payment configuration
                          const paymentConfigStr = localStorage.getItem('paymentConfig');
                          let config = null;

                          if (paymentConfigStr) {
                            try {
                              config = JSON.parse(paymentConfigStr);
                              console.log('Using payment configuration from localStorage:', config);
                            } catch (error) {
                              console.error('Error parsing payment config from localStorage:', error);
                            }
                          }

                          if (packageName.toLowerCase().includes('premium')) {
                            console.log('Setting Premium 6-month price');
                            setPackagePrice(config ? config.premium_price_6months.toString() : '299');
                            setMaxUsers(config ? Math.floor(config.premium_max_users * 0.8) : 8);
                            setMaxAssistants(config ? Math.floor(config.premium_max_assistants * 0.8) : 4);
                          } else if (packageName.toLowerCase().includes('standard')) {
                            console.log('Setting Standard 6-month price');
                            setPackagePrice(config ? config.standard_price_6months.toString() : '199');
                            setMaxUsers(config ? Math.floor(config.standard_max_users * 0.8) : 4);
                            setMaxAssistants(config ? Math.floor(config.standard_max_assistants * 0.8) : 2);
                          } else {
                            console.log('Setting Basic 6-month price');
                            setPackagePrice(config ? config.basic_price_6months.toString() : '99');
                            setMaxUsers(config ? Math.floor(config.basic_max_users * 0.8) : 2);
                            setMaxAssistants(config ? Math.floor(config.basic_max_assistants * 0.8) : 1);
                          }
                        } else {
                          console.log('No package name, setting default 6-month price');
                          setPackagePrice('299');
                        }

                        // Update billing cycle - this is important for when the form is submitted
                        setBillingCycle('monthly');
                        console.log('Updated to monthly billing cycle');
                      }

                      // Save the updated subscription period to localStorage
                      try {
                        const savedData = localStorage.getItem('pendingRegistration');
                        if (savedData) {
                          const parsedData = JSON.parse(savedData);
                          parsedData.subscriptionPeriod = value;
                          localStorage.setItem('pendingRegistration', JSON.stringify(parsedData));
                          console.log('Updated subscription period in localStorage');
                        }
                      } catch (error) {
                        console.error('Error updating subscription period in localStorage:', error);
                      }
                    }}
                  >
                    <Group mt="xs" justify="center">
                      <Radio
                        value="6months"
                        label={`6 Months - $${packageName?.toLowerCase().includes('premium') ? '299' : packageName?.toLowerCase().includes('standard') ? '199' : '99'}`}
                      />
                      <Radio
                        value="1year"
                        label={`1 Year - $${packageName?.toLowerCase().includes('premium') ? '499' : packageName?.toLowerCase().includes('standard') ? '399' : '299'} (Save $99)`}
                      />
                    </Group>
                  </Radio.Group>
                </Group>
              )}

              {/* Only show the invoice if a package is selected */}
              {packageName && (
                <Paper withBorder p="md" radius="md" mb="xl" style={{ backgroundColor: '#f9f9f9' }}>
                  <Title order={4} mb="md" ta="center">Invoice</Title>

                  <Table>
                    <thead>
                      <tr>
                        <th>Description</th>
                        <th style={{ textAlign: 'right' }}>Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>
                          <Text fw={500}>
                            {packageName} - {subscriptionPeriod === '6months' ? '6 Months' : '1 Year'}
                          </Text>
                          <Text size="sm" c="dimmed">
                            Includes {maxUsers} users and {maxAssistants} assistants
                          </Text>
                        </td>
                        <td style={{ textAlign: 'right' }}>
                          ${packagePrice}
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <Text>Processing Fee</Text>
                        </td>
                        <td style={{ textAlign: 'right' }}>$0.00</td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr>
                        <th>Total</th>
                        <th style={{ textAlign: 'right' }}>
                          ${packagePrice}
                        </th>
                      </tr>
                    </tfoot>
                  </Table>

                  <Divider my="md" />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Subscription Details:</Text>
                      <Text size="sm">Start Date: {new Date().toLocaleDateString()}</Text>
                      <Text size="sm">Expiration Date: {expirationDate}</Text>
                    </div>
                    <div>
                      <Text fw={500}>Package Features:</Text>
                      <Text size="sm">Users: {maxUsers}</Text>
                      <Text size="sm">Assistants: {maxAssistants}</Text>
                    </div>
                  </Group>
                </Paper>
              )}

              {/* Show payment error if there is one */}
              {paymentError && (
                <Box mb="xl">
                  <PaymentErrorAlert
                    message={paymentError.message}
                    errorDetails={paymentError.details}
                    onRetry={() => {
                      setPaymentError(null);
                      handlePayment();
                    }}
                    onBack={() => {
                      setPaymentError(null);
                      prevStep();
                    }}
                    onContactSupport={() => {
                      window.location.href = 'mailto:<EMAIL>?subject=Payment%20Issue';
                    }}
                  />
                </Box>
              )}

              {/* Only show payment method section if a package is selected */}
              {packageName && (
                <>
                  <Divider my="xl" label="Payment Method" labelPosition="center" />

                  <Group justify="center" gap="xl">
                    <Card
                      shadow="sm"
                      padding="lg"
                      radius="md"
                      withBorder
                      style={{
                        width: 240,
                        cursor: 'pointer',
                        border: paymentMethod === 'card' ? '2px solid #228be6' : undefined
                      }}
                      onClick={() => setPaymentMethod('card')}
                    >
                      <Card.Section>
                        <Center p="md">
                          <IconCreditCard size={48} color="#228be6" />
                        </Center>
                      </Card.Section>

                      <Group justify="space-between" mt="md" mb="xs">
                        <Text fw={500}>Credit Card</Text>
                        <Badge color="blue" variant="light">
                          Recommended
                        </Badge>
                      </Group>

                      <Text size="sm" c="dimmed">
                        Pay securely with your credit or debit card
                      </Text>
                    </Card>

                    <Card
                      shadow="sm"
                      padding="lg"
                      radius="md"
                      withBorder
                      style={{
                        width: 240,
                        cursor: 'pointer',
                        border: paymentMethod === 'message' ? '2px solid #228be6' : undefined
                      }}
                      onClick={() => setPaymentMethod('message')}
                    >
                      <Card.Section>
                        <Center p="md">
                          <IconMessage size={48} color="#228be6" />
                        </Center>
                      </Card.Section>

                      <Group justify="space-between" mt="md" mb="xs">
                        <Text fw={500}>Send Message</Text>
                      </Group>

                      <Text size="sm" c="dimmed">
                        Request payment instructions via email
                      </Text>
                    </Card>
                  </Group>

                  {paymentMethod === 'card' && (
                    <Box mt="xl">
                      <Alert icon={<IconAlertCircle size={16} />} color="blue">
                        This is a demo. In a real application, a secure payment form would be displayed here.
                      </Alert>
                    </Box>
                  )}

                  {paymentMethod === 'message' && (
                    <Box mt="xl">
                      <Alert icon={<IconAlertCircle size={16} />} color="blue">
                        <Text>Please send the payment to the following account:</Text>
                        <Text fw={500} mt="xs">Bank: Medical Bank</Text>
                        <Text fw={500}>Account: 1234-5678-9012-3456</Text>
                        <Text fw={500}>Amount: ${packagePrice}</Text>
                        <Text fw={500}>Reference: Your email address</Text>
                      </Alert>

                      <TextInput
                        label="Transaction ID"
                        placeholder="Enter the transaction ID from your payment"
                        mt="md"
                        required
                        value={transactionId}
                        onChange={(e) => setTransactionId(e.currentTarget.value)}
                      />

                      <Alert color="yellow" icon={<IconAlertCircle size={16} />} mt="md">
                        <Text>After payment verification, an activation code will be sent to your email address.</Text>
                      </Alert>
                    </Box>
                  )}
                </>
              )}
            </Box>
          </Stepper.Step>

          <Stepper.Step label="Activation" description="Activate your license">
            <Box mt="xl">
              <Center>
                <Stack align="center" gap="md" style={{ maxWidth: '500px', width: '100%' }}>
                  {/* Use the existing LicenseActivation component */}
                  <Box style={{ width: '100%', maxWidth: '500px' }}>
                    <RegistrationLicenseActivation
                      onActivationSuccess={() => {
                        setLicenseActivated(true);
                        notifications.show({
                          title: 'License Activated',
                          message: 'Your license has been successfully activated. You will be redirected to the login page.',
                          color: 'green',
                          styles: { root: { borderRadius: '6px' } }
                        });
                      }}
                    />
                  </Box>
                </Stack>
              </Center>
            </Box>
          </Stepper.Step>
        </Stepper>

        <Group justify="flex-end" mt="xl">
          {active > 0 && (
            <Button variant="default" onClick={prevStep}>
              Back
            </Button>
          )}

          {active === 0 && (
            <Button onClick={nextStep}>
              Next Step
            </Button>
          )}

          {active === 1 && (
            <Button
              onClick={handlePayment}
              loading={loading}
              disabled={!packageName}
              title={!packageName ? "Please select a package first" : ""}
            >
              {!packageName
                ? "Select Package First"
                : paymentMethod === 'card'
                  ? 'Process Payment'
                  : 'Send Instructions'
              }
            </Button>
          )}

          {/* No button needed for step 3 as it has its own activation button */}
        </Group>

        <Text ta="center" mt="xl">
          Already have an account?{' '}
          <Anchor component={Link} href="/login">
            Login
          </Anchor>
        </Text>
      </Paper>
    </Stack>
        {/* <div className="fixed right-10 top-[calc(50%-12px)] "> */}
        {/* <div className='absolute md:right-10 md:top-[calc(50%-0px)] top-[calc(6%-0px)] right-6 '> */}
        <div className='absolute md:right-10 md:top-[calc(50%-0px)] top-[calc(6%-0px)] right-6 '>
        <SwitchColorMode />
        <Languages />
       </div>
    </>
  );
}
