// frontend/dental_medicine/src/components/content/dental/shared/DentalSvgWrapper.tsx
"use client";
import React, { useState,  useMemo } from "react";
import { DentalSvg } from '@/components/TDentalSvgMin';
import { Dantal, DantalB } from '@/utils/Tdantal';
import { SVGPathStyle } from './types';
import { ColorTarget, HighlightedPathStyles, } from '@/types/dental';
   import { isAllowedPosition,isOnlyallowed} from '@/utils/dentalUtils';
interface DentalSvgWrapperProps {
  svgId: string;
  hiddenPaths: Record<string, boolean>;
  highlightedPaths: Record<string, SVGPathStyle>;
  onSvgClick: (svgId: string) => void;
  isSelected?: boolean;
  isMultiSelectMode?: boolean;
}

export const DentalSvgWrapper: React.FC<DentalSvgWrapperProps> = ({
  svgId,
  hiddenPaths,
  highlightedPaths,
  onSvgClick,
  isSelected = false,
  isMultiSelectMode = false
}) => {
//------------------------------------
  const [isHidingMode] = useState(false);
   const [brokenRedStrokeSvgs] = useState<Set<string>>(new Set());
    const [gradientEffectSvgs] = useState<Set<string>>(new Set()); // For Dental
    const [gradientBottomEffectSvgs, ] = useState<Set<string>>(new Set()); // For DentalB
// Using isHidingMode as a read-only state since its setter is not used
    const [currentColor, ] = useState<string>("");
    const [, setHighlightedPaths] = useState<Record<string, HighlightedPathStyles>>({});
    const [isPathSelectionActive, ] = useState(false);
    // Using hiddenSvgIds as a read-only state since its setter is not used
 
   

   
    const [currentColorTarget, ] = useState<ColorTarget>("fill");
    // Using selectedColor as a read-only state since its setter is not used
   
    const [, setSelectedTeeth] = useState<string[]>([]);
    const [pendingPath19Toggle, setPendingPath19Toggle] = useState(false);
    const [, setClickedIds] = useState<string[]>([]);
    // Using effectSvgs as a read-only state since its setter is not used
 
   
    // const [viewModeLine, setViewModeLine] = useState(false);
  

 const generatePathsToShowByDefault = () => {
    const svgIds = Array.from({ length: 32 }, (_, i) => `${i + 1}`);
    const pathIds = Array.from({ length: 16 }, (_, i) => `${i + 1}`);
    return svgIds.flatMap((svgId) => pathIds.map((pathId) => ({ svg_id: svgId, path_id: pathId })));
  };
  const pathsToShowByDefault = generatePathsToShowByDefault();
  const initialHiddenPaths = useMemo(() => {
    return Dantal.concat(DantalB).reduce((acc, svgData) => {
      svgData.paths.forEach((path) => {
        const key = `${svgData.svg_id}-${path.id}`;
        const isVisibleByDefault = pathsToShowByDefault.some(
          (visiblePath) => visiblePath.svg_id === svgData.svg_id && visiblePath.path_id === path.id
        );
        // Hide path 20 by default
        if (path.id === "20") {
          acc[key] = true;
        } else {
          acc[key] = !isVisibleByDefault;
        }
      });
      return acc;
    }, {} as Record<string, boolean>);
  }, [pathsToShowByDefault]);
  const [, setHiddenPaths] = useState<Record<string, boolean>>(initialHiddenPaths);
  
  const onPathClick = (pathId: string, svgId: string) => {
     const positionKey = `${svgId}(${pathId})`;
           const key = `${svgId}-${pathId}`;
           setSelectedTeeth((prev: string[]) => {
            const isAlreadySelected = prev.includes(positionKey);
            return isAlreadySelected ? prev.filter((tooth) => tooth !== positionKey) : [...prev, positionKey];
          });
          if (currentColor === "#FF4444" && !isAllowedPosition(positionKey)) {
            return;
          }
          if (currentColor === "#8E1616" && !isOnlyallowed(positionKey)) {
           return;
         }
           if (pendingPath19Toggle && pathId === "19" && svgId === "1") {
             setHiddenPaths((prev) => ({
               ...prev,
               [`${svgId}-${pathId}`]: !prev[`${svgId}-${pathId}`], // Toggle hidden state
             }));
             setPendingPath19Toggle(false); // Reset pending state after applying
           }
     setHighlightedPaths((prev) => {
       const newHighlightedPaths = { ...prev };
       if (!newHighlightedPaths[key]) {
         newHighlightedPaths[key] = {};
       }
       if (currentColorTarget === "both") {
         if (newHighlightedPaths[key].fill && newHighlightedPaths[key].stroke) {
           delete newHighlightedPaths[key].fill;
           delete newHighlightedPaths[key].stroke;
         } else {
           newHighlightedPaths[key] = {
             fill: currentColor,
             stroke: "#2563EB"
           };
         }
       } else {
         if (newHighlightedPaths[key][currentColorTarget]) {
           delete newHighlightedPaths[key][currentColorTarget];
         } else {
           newHighlightedPaths[key][currentColorTarget] = currentColor;
         }
       }
       if (Object.keys(newHighlightedPaths[key]).length === 0) {
         delete newHighlightedPaths[key];
       }
       return newHighlightedPaths;
     });
     if (isPathSelectionActive) {
       setClickedIds((prevIds) => [...prevIds, pathId]);
     }
      // Toggle visibility of Path19
   };
 
//-----------------------------------
  // Trouver les données SVG correspondantes à l'ID
  const svgData = useMemo(() => {
    const allSvgData = [...Dantal, ...DantalB];
    return allSvgData.find(svg => svg.svg_id === svgId);
  }, [svgId]);

  // Gestionnaire de clic sur path
 

  // Si aucune donnée SVG trouvée, afficher un placeholder
  if (!svgData) {
    console.warn(`⚠️ Aucune donnée SVG trouvée pour l'ID: ${svgId}`);
    return (
      <div
        className="h-[200px] w-[100px] bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer"
        onClick={() => onSvgClick(svgId)}
      >
        <div className="text-center">
          <div className="text-gray-400 text-sm">SVG {svgId}</div>
          <div className="text-gray-400 text-xs">Non trouvé</div>
        </div>
      </div>
    );
  }

  // Classes CSS dynamiques pour la sélection
  const containerClasses = [
    "cursor-pointer",
    "transition-all duration-200",
    isSelected
      ? "bg-blue-100 border-2 border-blue-500 rounded-lg shadow-md"
      : "hover:bg-[#F2F5F8]",
    isMultiSelectMode && !isSelected
      ? "hover:border-2 hover:border-blue-300 hover:rounded-lg"
      : ""
  ].filter(Boolean).join(" ");

  return (
    <div
      className={containerClasses}
      onClick={() => onSvgClick(svgId)}
      title={isMultiSelectMode ? `Dent ${svgId} - Cliquez pour ${isSelected ? 'désélectionner' : 'sélectionner'}` : `Dent ${svgId}`}
    >
      <DentalSvg
        svgData={svgData}
        //isHidingMode={false}
        isHidingMode={isHidingMode}
        highlightedPaths={highlightedPaths}
        //onPathClick={handlePathClick}
        onPathClick={onPathClick}
        //isPathSelectionActive={true}
        isPathSelectionActive={isPathSelectionActive}
        hiddenPaths={hiddenPaths}
        //isBrokenRedStrokeActive={false}
        isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
        //isGradientEffectActive={false}
        isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
        //isGradientBottomEffectActive={false}
          isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
      />
    </div>
  );
};

export default DentalSvgWrapper;
