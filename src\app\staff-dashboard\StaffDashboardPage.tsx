'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Container,
  Paper,
  Title,
  Text,
  Button,
  Group,
  Loader,
  Stack,
  Card,
  SimpleGrid,
  Badge,
  List,
  ThemeIcon,
} from '@mantine/core';
import {
  IconCalendar,
  IconUsers,
  IconBuildingHospital,
  IconReportMedical,
  IconNotes,
  IconFileInvoice,
  IconMedicalCross,
  IconCheck,
  IconClock
} from '@tabler/icons-react';
import authService from '~/services/authService';

// Define interface for User
interface User {
  first_name?: string;
  last_name?: string;
  email?: string;
  user_type?: string;
  assigned_doctor_name?: string;
  id?: string;
}

export default function StaffDashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [tasks, setTasks] = useState([
    { id: 1, title: 'Update patient records', status: 'completed' },
    { id: 2, title: 'Schedule follow-up appointments', status: 'pending' },
    { id: 3, title: 'Process insurance claims', status: 'pending' },
    { id: 4, title: 'Order medical supplies', status: 'pending' },
    { id: 5, title: 'Prepare monthly reports', status: 'pending' },
  ]);

  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      try {
        // Check if token exists in localStorage
        const token = localStorage.getItem('token');
        console.log('Current auth token:', token ? 'exists' : 'missing');

        if (!token) {
          console.log('No token found, redirecting to login');
          router.push('/login');
          return;
        }

        // Log all authentication data for debugging
        console.log('Authentication data:');
        console.log('- Token:', token ? `${token.substring(0, 15)}...` : 'missing');
        console.log('- User Type:', localStorage.getItem('userType') || 'not set');
        console.log('- Cookies:', document.cookie);

        // Check if user is staff
        const userType = localStorage.getItem('userType');
        if (userType !== 'staff') {
          console.log('User is not staff, redirecting to appropriate dashboard');
          if (userType === 'doctor') {
            router.push('/dashboard');
          } else if (userType === 'assistant') {
            router.push('/dashboard-simple');
          } else {
            router.push('/login');
          }
          return;
        }

        // Set a default user if we can't fetch the profile
        setUser({
          first_name: 'Staff',
          last_name: 'Member',
          email: '<EMAIL>',
          user_type: 'staff'
        });

        // Try to fetch the actual user profile, but don't block on it
        try {
          const userProfile = await authService.getProfile();
          setUser(userProfile);
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
          // Continue with the default user
        }
      } catch (error) {
        console.error('Authentication error:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  const toggleTaskStatus = (taskId: number) => {
    setTasks(tasks.map(task =>
      task.id === taskId
        ? { ...task, status: task.status === 'completed' ? 'pending' : 'completed' }
        : task
    ));
  };

  if (loading) {
    return (
      <Container size="xl" py="xl">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <Loader size="xl" />
        </div>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Paper p="xl" radius="md" withBorder mb="xl" bg="blue.0">
        <Group justify="space-between" mb="md">
          <div>
            <Title order={2}>Welcome, {user?.first_name || 'Staff Member'}</Title>
            <Text c="dimmed">Staff Administration Dashboard</Text>
            {user?.assigned_doctor_name && (
              <Text size="sm" c="dimmed">Associated Doctor: Dr. {user.assigned_doctor_name}</Text>
            )}
          </div>
          <Badge size="xl" color="blue">Staff Portal</Badge>
        </Group>
      </Paper>

      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg">
        <Paper p="xl" radius="md" withBorder>
          <Title order={3} mb="md">Tasks</Title>
          <List spacing="md">
            {tasks.map(task => (
              <List.Item
                key={task.id}
                icon={
                  <ThemeIcon color={task.status === 'completed' ? 'green' : 'blue'} size={24} radius="xl">
                    {task.status === 'completed' ? <IconCheck size={16} /> : <IconClock size={16} />}
                  </ThemeIcon>
                }
                onClick={() => toggleTaskStatus(task.id)}
                style={{ cursor: 'pointer' }}
              >
                <Text style={{ textDecoration: task.status === 'completed' ? 'line-through' : 'none' }}>
                  {task.title}
                </Text>
              </List.Item>
            ))}
          </List>
        </Paper>

        <Paper p="xl" radius="md" withBorder>
          <Title order={3} mb="md">Quick Actions</Title>
          <SimpleGrid cols={{ base: 1 }} spacing="md">
            <Button
              fullWidth
              leftSection={<IconCalendar size={16} />}
              onClick={() => router.push('/appointments')}
            >
              Manage Appointments
            </Button>
            <Button
              fullWidth
              leftSection={<IconUsers size={16} />}
              onClick={() => router.push('/patients')}
            >
              Patient Records
            </Button>
            <Button
              fullWidth
              leftSection={<IconFileInvoice size={16} />}
              onClick={() => router.push('/billing')}
            >
              Billing & Invoices
            </Button>
            <Button
              fullWidth
              leftSection={<IconMedicalCross size={16} />}
              onClick={() => router.push('/pharmacy')}
            >
              Pharmacy Orders
            </Button>
          </SimpleGrid>
        </Paper>
      </SimpleGrid>

      <Title order={3} mt="xl" mb="md">Administrative Tools</Title>
      <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconBuildingHospital size={48} color="#228be6" />
            <Title order={3}>Facility Management</Title>
            <Text ta="center">Manage rooms, equipment and resources</Text>
            <Button fullWidth onClick={() => router.push('/facility')}>
              Access
            </Button>
          </Stack>
        </Card>

        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconReportMedical size={48} color="#40c057" />
            <Title order={3}>Reports</Title>
            <Text ta="center">Generate and view administrative reports</Text>
            <Button fullWidth onClick={() => router.push('/reports')}>
              View Reports
            </Button>
          </Stack>
        </Card>

        <Card withBorder p="xl" radius="md">
          <Stack align="center" gap="md">
            <IconNotes size={48} color="#7950f2" />
            <Title order={3}>Documents</Title>
            <Text ta="center">Manage medical and administrative documents</Text>
            <Button fullWidth onClick={() => router.push('/documents')}>
              Access Documents
            </Button>
          </Stack>
        </Card>
      </SimpleGrid>

      <Group justify="flex-start" mt="xl">
        <Button variant="subtle" onClick={() => authService.logout().then(() => router.push('/login'))}>
          Logout
        </Button>
      </Group>
    </Container>
  );
}
