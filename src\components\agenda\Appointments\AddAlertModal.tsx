import { useState } from 'react';
import { 
  Modal, 
  TextInput, 
  Button, 
  Group, 
  Text,
  Select,
  Checkbox
} from '@mantine/core';
import { IconAlertCircle, IconPlus } from '@tabler/icons-react';

interface AddAlertModalProps {
  opened: boolean;
  onClose: () => void;
  onAddAlert: (alert: string) => void;
  existingAlerts: string[];
}

export function AddAlertModal({ 
  opened, 
  onClose, 
  onAddAlert,
  existingAlerts 
}: AddAlertModalProps) {
  const [newAlert, setNewAlert] = useState('');
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  
  const presetAlerts = [
    { value: 'allaitante', label: 'Allaitante depuis:' },
    { value: 'aspirine', label: 'Allergique à l\'Aspirine' },
    { value: 'penicilline', label: 'Allergique à la Pénicilline' },
    { value: 'arthrose', label: 'Arthrose' },
    { value: 'anticoagulant', label: 'Cardiaque Anticoagulant' },
    { value: 'prothese', label: 'Cardiaque prothèse valvulaire' },
    { value: 'trouble-rythme', label: 'Cardiaque trouble du rythme' },
    { value: 'diabetique-id', label: 'Diabétique ID' },
    { value: 'diabetique-nid', label: 'Diabétique NID' },
    { value: 'enceinte', label: 'Enceinte depuis:' },
    { value: 'gastralgie', label: 'Gastralgie : ulcère anti-inflamm...' },
    { value: 'hypertension', label: 'Hypertension' },
    { value: 'hypotension', label: 'Hypotension' },
    { value: 'thyroide', label: 'Thyroïde' },
  ];

  const handleAddAlert = () => {
    if (newAlert.trim()) {
      onAddAlert(newAlert);
      setNewAlert('');
      onClose();
    } else if (selectedPreset) {
      const selectedAlert = presetAlerts.find(alert => alert.value === selectedPreset);
      if (selectedAlert) {
        onAddAlert(selectedAlert.label);
        setSelectedPreset(null);
        onClose();
      }
    }
  };

  return (
    <Modal.Root opened={opened} onClose={onClose} size="md">
      <Modal.Overlay />
      <Modal.Content>
        <Modal.Header style={{ background: "#3799CE", padding: "11px" }}>
          <Modal.Title>
            <Text fw={600} c="var(--mantine-color-white)" className="flex gap-2 text-lg">
              <IconAlertCircle size={24} />
              Ajouter une alerte médicale
            </Text>
          </Modal.Title>
          <Modal.CloseButton className="text-white hover:bg-[#2d89bd]" />
        </Modal.Header>
        <Modal.Body p="md">
          <div className="space-y-4">
            <Select
              label="Sélectionner une alerte prédéfinie"
              placeholder="Choisir une alerte"
              data={presetAlerts}
              value={selectedPreset}
              onChange={setSelectedPreset}
              clearable
              searchable
            />
            
            <Text className="text-center font-medium">- OU -</Text>
            
            <TextInput
              label="Créer une nouvelle alerte"
              placeholder="Entrez le nom de l'alerte"
              value={newAlert}
              onChange={(e) => setNewAlert(e.currentTarget.value)}
            />
            
            <Group justify="flex-end" mt="md">
              <Button variant="default" onClick={onClose}>Annuler</Button>
              <Button 
                color="#3799CE" 
                leftSection={<IconPlus size={16} />}
                onClick={handleAddAlert}
                disabled={!newAlert.trim() && !selectedPreset}
              >
                Ajouter
              </Button>
            </Group>
          </div>
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>
  );
}