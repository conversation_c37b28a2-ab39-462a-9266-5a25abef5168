import React, { useState } from 'react';
import { IconPalette } from '@tabler/icons-react';
import {
  Stack,
  Title,
  Paper,
  Tabs,
  Group,
  Text,
  Switch,
  SimpleGrid,
  Box,
  Table,
  Select,
  ActionIcon,
  Button,
  Modal ,
  ColorInput,
  ColorPicker,
} from '@mantine/core';
import {
  IconUser,
  IconX,
  IconMenu2,
} from '@tabler/icons-react';

// Types
interface ActiveField {
  id: string;
  label: string;
  isActive: boolean;
}

interface SyntheseBlock {
  id: string;
  sourceDisponible: string;
  listeDesBlocs: string;
  champsDuBloc: string[];
  couleur: string;
}

const Patient_en_cours = () => {
  const [activeTab, setActiveTab] = useState('general');
const [isColorPickerModalOpen, setIsColorPickerModalOpen] = useState(false);
  const [syntheseBlocks, setSyntheseBlocks] = useState<SyntheseBlock[]>([
    {
      id: '1',
      sourceDisponible: 'Informations générales',
      listeDesBlocs: 'Bloc démographique',
      champsDuBloc: ['Nom', 'Prénom', 'Âge', 'Sexe'],
      couleur: '#3b82f6'
    },
    {
      id: '2',
      sourceDisponible: 'Contact',
      listeDesBlocs: 'Bloc contact',
      champsDuBloc: ['Téléphone', 'Email', 'Adresse'],
      couleur: '#10b981'
    },
    {
      id: '3',
      sourceDisponible: 'Médical',
      listeDesBlocs: 'Bloc médical',
      champsDuBloc: ['Médecin traitant', 'Assurance'],
      couleur: '#f59e0b'
    }
  ]);

  const [activeFields, setActiveFields] = useState<ActiveField[]>([
    { id: 'sexe', label: 'Sexe', isActive: true },
    { id: 'age', label: 'Âge', isActive: true },
    { id: 'dateNaissance', label: 'Date de naissance', isActive: false },
    { id: 'cnie', label: 'CNIE', isActive: true },
    { id: 'dateCreation', label: 'Date de création', isActive: false },
    { id: 'telephone', label: 'Téléphone', isActive: false },
    { id: 'email', label: 'Email', isActive: false },
    { id: 'etatCivil', label: 'État civil', isActive: true },
    { id: 'profession', label: 'Profession', isActive: true },
    { id: 'ville', label: 'Ville', isActive: true },
    { id: 'assurance', label: 'Assurance', isActive: true },
    { id: 'commentaire', label: 'Commentaire', isActive: true },
    { id: 'province', label: 'Province', isActive: false },
    { id: 'adressePar', label: 'Adressé par', isActive: true },
    { id: 'medecinTraitant', label: 'Médecin traitant', isActive: true },
  ]);

  const handleFieldToggle = (fieldId: string) => {
    setActiveFields(prev =>
      prev.map(field =>
        field.id === fieldId
          ? { ...field, isActive: !field.isActive }
          : field
      )
    );
  };

  return (
    <Stack gap="lg" className="p-6">
      {/* Header */}
      <Paper className="bg-blue-600 text-white p-4 rounded-lg">
        <Group gap="sm" align="center">
          <IconUser size={24} />
          <Title order={3} className="text-white">
            Patient en cours
          </Title>
        </Group>
      </Paper>

      {/* Main Content */}
      <Paper shadow="sm" p="md" radius="md" withBorder>
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'general')}>
          <Tabs.List>
            <Tabs.Tab value="general">
              Général
            </Tabs.Tab>
            <Tabs.Tab value="fiche-synthese">
              Fiche de synthèse
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="general" pt="md">
            <Stack gap="lg">
              {/* Section Title */}
              <Title order={4} className="text-gray-700 mb-4">
                Champs actifs du patient en cours
              </Title>

              {/* Active Fields Grid */}
              <SimpleGrid cols={{ base: 2, sm: 3, md: 4, lg: 5 }} spacing="md">
                {activeFields.map((field) => (
                  <Box key={field.id} className="flex items-center gap-2">
                    <Switch
                      checked={field.isActive}
                      onChange={() => handleFieldToggle(field.id)}
                      size="sm"
                      color="blue"
                      styles={{
                        track: {
                          backgroundColor: field.isActive ? '#3b82f6' : '#e5e7eb',
                        },
                      }}
                    />
                    <Text
                      size="sm"
                      className={`${
                        field.isActive ? 'text-gray-700' : 'text-gray-400'
                      } select-none cursor-pointer`}
                      onClick={() => handleFieldToggle(field.id)}
                    >
                      {field.label}
                    </Text>
                  </Box>
                ))}
              </SimpleGrid>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="fiche-synthese" pt="md">
            <Stack gap="md">
              <Title order={4} className="text-gray-700">
                Fiche de synthèse
              </Title>

              {/* Synthese Blocks Table */}
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr className="bg-gray-50">
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Source disponible
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Liste des blocs
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                      Champs du bloc
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                      Couleur
                    </Table.Th>
                    <Table.Th className="font-semibold text-gray-700 text-center">
                      Actions
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {syntheseBlocks.map((block) => (
                    <Table.Tr key={block.id} className="hover:bg-gray-50">
                      <Table.Td className="border-r border-gray-200">
                        <Select
                          value={block.sourceDisponible}
                          data={[
                            'Informations générales',
                            'Contact',
                            'Médical',
                            'Administratif',
                            'Historique'
                          ]}
                          onChange={(value) => {
                            setSyntheseBlocks(prev =>
                              prev.map(b =>
                                b.id === block.id
                                  ? { ...b, sourceDisponible: value || '' }
                                  : b
                              )
                            );
                          }}
                          size="sm"
                          className="w-full"
                        />
                      </Table.Td>
                      <Table.Td className="border-r border-gray-200">
                        <Select
                          value={block.listeDesBlocs}
                          data={[
                            'Bloc démographique',
                            'Bloc contact',
                            'Bloc médical',
                            'Bloc administratif',
                            'Bloc historique'
                          ]}
                          onChange={(value) => {
                            setSyntheseBlocks(prev =>
                              prev.map(b =>
                                b.id === block.id
                                  ? { ...b, listeDesBlocs: value || '' }
                                  : b
                              )
                            );
                          }}
                          size="sm"
                          className="w-full"
                        />
                      </Table.Td>
                      <Table.Td className="border-r border-gray-200">
                        <Group gap="xs" wrap="wrap">
                          {block.champsDuBloc.map((champ, index) => (
                            <Box
                              key={index}
                              className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs flex items-center gap-1"
                            >
                              <Text size="xs">{champ}</Text>
                              <ActionIcon
                                size="xs"
                                variant="transparent"
                                color="blue"
                                onClick={() => {
                                  setSyntheseBlocks(prev =>
                                    prev.map(b =>
                                      b.id === block.id
                                        ? {
                                            ...b,
                                            champsDuBloc: b.champsDuBloc.filter((_, i) => i !== index)
                                          }
                                        : b
                                    )
                                  );
                                }}
                              >
                                <IconX size={10} />
                              </ActionIcon>
                            </Box>
                          ))}
                          <Button
                            size="xs"
                            variant="light"
                            onClick={() => {
                              const newChamp = prompt('Nom du nouveau champ:');
                              if (newChamp) {
                                setSyntheseBlocks(prev =>
                                  prev.map(b =>
                                    b.id === block.id
                                      ? { ...b, champsDuBloc: [...b.champsDuBloc, newChamp] }
                                      : b
                                  )
                                );
                              }
                            }}
                          >
                            + Ajouter
                          </Button>
                        </Group>
                      </Table.Td>
                      <Table.Td className="text-center border-r border-gray-200">
                        {/* <ColorSwatch
                          color={block.couleur}
                          size={24}
                          className="cursor-pointer mx-auto"
                          onClick={() => {
                            const newColor = prompt('Nouvelle couleur (hex):', block.couleur);
                            if (newColor) {
                              setSyntheseBlocks(prev =>
                                prev.map(b =>
                                  b.id === block.id
                                    ? { ...b, couleur: newColor }
                                    : b
                                )
                              );
                            }
                          }}
                        /> */}
                        <div className="flex justify-center">
                                          <Group>
                                            <ColorInput
                                                value={block.couleur}
                                               w={"80%"}
                                              
                                            />    
                                            <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                                                onClick={() => setIsColorPickerModalOpen(true)}>
                                                <IconPalette stroke={2} />
                                            </ActionIcon>
                                           
                                        </Group>
                                            </div>
                      </Table.Td>
                      <Table.Td className="text-center">
                        <Group gap="xs" justify="center">
                          <ActionIcon
                            variant="subtle"
                            color="gray"
                            size="sm"
                          >
                            <IconMenu2 size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => {
                              setSyntheseBlocks(prev => prev.filter(b => b.id !== block.id));
                            }}
                          >
                            <IconX size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Add Block Button */}
              <Group justify="flex-start">
                <Button
                  variant="light"
                  onClick={() => {
                    const newId = (syntheseBlocks.length + 1).toString();
                    setSyntheseBlocks(prev => [
                      ...prev,
                      {
                        id: newId,
                        sourceDisponible: 'Informations générales',
                        listeDesBlocs: 'Nouveau bloc',
                        champsDuBloc: [],
                        couleur: '#6b7280'
                      }
                    ]);
                  }}
                >
                  + Ajouter un bloc
                </Button>
              </Group>
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Paper>
       <Modal  
                        opened={isColorPickerModalOpen}
                        onClose={() => setIsColorPickerModalOpen(false)} 
                        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                        withCloseButton={false}
                        centered
                    >
                        <ColorPicker 
                            format="hex" 
                            
                            swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} 
                        />
                  </Modal>
    </Stack>
  );
};

export default Patient_en_cours;
