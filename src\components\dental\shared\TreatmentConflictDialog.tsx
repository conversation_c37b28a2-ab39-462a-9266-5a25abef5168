// frontend/dental_medicine/src/components/content/dental/shared/TreatmentConflictDialog.tsx

import React from 'react';
import { TreatmentConflict } from './treatmentManager';
import { TREATMENTS } from './treatmentCompatibility';

interface TreatmentConflictDialogProps {
  isOpen: boolean;
  conflicts: TreatmentConflict[];
  treatmentName: string;
  toothNumber: string;
  onResolve: (action: 'force' | 'cancel' | 'modify') => void;
  onClose: () => void;
}

export const TreatmentConflictDialog: React.FC<TreatmentConflictDialogProps> = ({
  isOpen,
  conflicts,
  treatmentName,
  toothNumber,
  onResolve,
  onClose
}) => {
  if (!isOpen) return null;

  const getConflictIcon = (type: string) => {
    switch (type) {
      case 'exclusive':
        return '⚠️';
      case 'prerequisite':
        return '📋';
      case 'sequence':
        return '🔄';
      default:
        return '❓';
    }
  };

  const getConflictColor = (type: string) => {
    switch (type) {
      case 'exclusive':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'prerequisite':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'sequence':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Conflit de Traitement
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">
              Tentative d'application de <span className="font-semibold text-blue-600">{treatmentName}</span> sur la dent <span className="font-semibold">{toothNumber}</span>
            </p>
          </div>

          {/* Conflicts List */}
          <div className="space-y-3 mb-6">
            {conflicts.map((conflict, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${getConflictColor(conflict.type)}`}
              >
                <div className="flex items-start space-x-2">
                  <span className="text-lg">{getConflictIcon(conflict.type)}</span>
                  <div className="flex-1">
                    <p className="text-sm font-medium mb-1">
                      {conflict.message}
                    </p>
                    {conflict.conflictingTreatments.length > 0 && (
                      <div className="text-xs opacity-75 mb-2">
                        Traitements concernés: {conflict.conflictingTreatments.map(t => TREATMENTS[t]?.name || t).join(', ')}
                      </div>
                    )}
                    {conflict.suggestedActions.length > 0 && (
                      <div className="text-xs">
                        <span className="font-medium">Actions suggérées:</span>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          {conflict.suggestedActions.map((action, actionIndex) => (
                            <li key={actionIndex}>{action}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
          <div className="flex space-x-3">
            <button
              onClick={() => onResolve('cancel')}
              className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={() => onResolve('modify')}
              className="flex-1 px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-300 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Modifier
            </button>
            <button
              onClick={() => onResolve('force')}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              Forcer
            </button>
          </div>
          
          <div className="mt-3 text-xs text-gray-500 text-center">
            <p><strong>Forcer</strong> : Applique le traitement en résolvant automatiquement les conflits</p>
            <p><strong>Modifier</strong> : Permet de choisir une séquence alternative</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreatmentConflictDialog;
