'use client';

import { useState } from 'react';
import { TextInput, Button, Group, Text, Alert, Paper } from '@mantine/core';
import { useForm } from '@mantine/form';
import { IconCheck, IconX } from '@tabler/icons-react';
import { AxiosError } from 'axios';
import subscriptionService, { Coupon } from '~/services/subscriptionService';

interface CouponFormProps {
  packageId?: number;
  onApplyCoupon: (coupon: Coupon) => void;
}

export default function CouponForm({ packageId, onApplyCoupon }: CouponFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);

  const form = useForm({
    initialValues: {
      code: '',
    },
    validate: {
      code: (value) => (value.trim().length === 0 ? 'Coupon code is required' : null),
    },
  });

  const handleSubmit = async (values: { code: string }) => {
    try {
      setLoading(true);
      setError(null);

      const result = await subscriptionService.validateCoupon(values.code, packageId);

      if (result.valid) {
        setAppliedCoupon(result.coupon);
        onApplyCoupon(result.coupon);
        form.reset();
      } else {
        setError('Invalid coupon code');
      }
    } catch (error) {
      console.error('Error validating coupon:', error);

      // Type assertion for Axios error
      const err = error as AxiosError<{ detail?: string }>;
      setError(err.response?.data?.detail || 'Failed to validate coupon. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    onApplyCoupon(null as unknown as Coupon);
  };

  return (
    <Paper p="md" withBorder>
      {appliedCoupon ? (
        <div>
          <Alert icon={<IconCheck size="1rem" />} title="Coupon Applied" color="green" mb="md">
            <Text size="sm">
              Coupon <strong>{appliedCoupon.code}</strong> has been applied to your order.
            </Text>
            <Text size="sm" mt="xs">
              {appliedCoupon.discount_type === 'percentage'
                ? `${appliedCoupon.discount_value}% discount`
                : `$${appliedCoupon.discount_value} discount`}
            </Text>
          </Alert>
          <Button variant="outline" color="red" onClick={removeCoupon} fullWidth>
            Remove Coupon
          </Button>
        </div>
      ) : (
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Text fw={500} mb="md">Have a coupon code?</Text>

          {error && (
            <Alert icon={<IconX size="1rem" />} title="Error" color="red" mb="md">
              {error}
            </Alert>
          )}

          <Group align="flex-end">
            <TextInput
              style={{ flex: 1 }}
              label="Coupon Code"
              placeholder="Enter your coupon code"
              {...form.getInputProps('code')}
            />
            <Button type="submit" loading={loading}>
              Apply
            </Button>
          </Group>
        </form>
      )}
    </Paper>
  );
}
