'use client';

import React, { useState } from 'react';
import {
  Title,
  Button,
  TextInput,
  Group,
  Text,
  Modal,
  Table,
  ActionIcon,
  Pagination,
  Select,
  Checkbox
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconFileText,
  IconPlus,
  IconSearch,
  IconFilter,
  IconFileSpreadsheet
} from '@tabler/icons-react';
import { RichTextEditor } from '@mantine/tiptap';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import '@mantine/tiptap/styles.css';

// Types
interface TextEditorModel {
  id?: number;
  title: string;
  description: string;
  content: string;
  category?: string;
}



const GestionDesModelsDediteurTexte: React.FC = () => {
  const [models, setModels] = useState<TextEditorModel[]>([
    { id: 1, title: 'Rechercher', description: 'Rechercher', content: '' }
  ]);
  const [selectedModel, setSelectedModel] = useState<TextEditorModel>({
    title: '',
    description: '',
    content: '',
    category: ''
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState<string[]>(['title', 'description']);
  // Modals and Drawers
  const [modelModalOpened, { open: openModelModal, close: closeModelModal }] = useDisclosure(false);

  // TipTap Editor
  const editor = useEditor({
    extensions: [StarterKit],
    content: selectedModel.content,
  });

  const handleSaveModel = () => {
    if (!selectedModel.title.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le titre est obligatoire',
        color: 'red'
      });
      return;
    }

    const updatedModel = {
      ...selectedModel,
      content: editor?.getHTML() || ''
    };

    if (selectedModel.id) {
      setModels(prev => prev.map(m => m.id === selectedModel.id ? updatedModel : m));
    } else {
      setModels(prev => [...prev, { ...updatedModel, id: Date.now() }]);
    }

    notifications.show({
      title: 'Succès',
      message: 'Modèle enregistré avec succès',
      color: 'green'
    });
    closeModelModal();
    setSelectedModel({ title: '', description: '', content: '', category: '' });
  };

  const handleExportExcel = () => {
    notifications.show({
      title: 'Export',
      message: 'Export Excel en cours...',
      color: 'blue'
    });
  };



  const filteredModels = models.filter(model =>
    model.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const paginatedModels = filteredModels.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const totalPages = Math.ceil(filteredModels.length / pageSize);

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-blue-600 text-white px-6 py-4 flex items-center gap-3">
        <IconFileText size={24} />
        <Title order={2} className="text-white font-medium">
          Gestion des modèls d&apos;editeur texte
        </Title>
        <div className="flex-1" />
        <Button
          leftSection={<IconPlus size={16} />}
          variant="filled"
          color="blue"
          onClick={openModelModal}
          className="bg-blue-500 hover:bg-blue-400"
        >
          Ajouter un modèl
        </Button>
      </div>

      {/* Search and Actions */}
      <div className="p-4 border-b border-gray-200">
        <Group justify="space-between">
          <Group>
            <ActionIcon variant="subtle" size="lg">
              <IconFilter size={20} />
            </ActionIcon>
            <TextInput
              placeholder="Rechercher"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftSection={<IconSearch size={16} />}
              className="w-80"
            />
          </Group>
          <Group>
            <ActionIcon
              variant="subtle"
              size="lg"
              onClick={() => setShowFilters(!showFilters)}
            >
              <IconFilter size={20} />
            </ActionIcon>
            <ActionIcon variant="subtle" size="lg" onClick={handleExportExcel}>
              <IconFileSpreadsheet size={20} />
            </ActionIcon>
          </Group>
        </Group>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <Text size="sm" fw={500} mb="sm">Filtres avancés</Text>
          <Group>
            <Select
              placeholder="Aucun filtre Enregistré"
              data={[]}
              className="w-48"
            />
            <Group gap="xs">
              <Checkbox
                label="Titre"
                checked={selectedColumns.includes('title')}
                onChange={(e) => {
                  if (e.currentTarget.checked) {
                    setSelectedColumns(prev => [...prev, 'title']);
                  } else {
                    setSelectedColumns(prev => prev.filter(col => col !== 'title'));
                  }
                }}
              />
              <Checkbox
                label="Description"
                checked={selectedColumns.includes('description')}
                onChange={(e) => {
                  if (e.currentTarget.checked) {
                    setSelectedColumns(prev => [...prev, 'description']);
                  } else {
                    setSelectedColumns(prev => prev.filter(col => col !== 'description'));
                  }
                }}
              />
            </Group>
          </Group>
        </div>
      )}

      {/* Table */}
      <div className="flex-1 p-4">
        <Table>
          <Table.Thead>
            <Table.Tr>
              {selectedColumns.includes('title') && <Table.Th>Titre</Table.Th>}
              {selectedColumns.includes('description') && <Table.Th>Description</Table.Th>}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {paginatedModels.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={selectedColumns.length} className="text-center py-8">
                  <Text c="dimmed">Aucun élément trouvé.</Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              paginatedModels.map((model) => (
                <Table.Tr
                  key={model.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setSelectedModel(model);
                    openModelModal();
                  }}
                >
                  {selectedColumns.includes('title') && (
                    <Table.Td className="text-blue-600">{model.title}</Table.Td>
                  )}
                  {selectedColumns.includes('description') && (
                    <Table.Td className="text-blue-600">{model.description}</Table.Td>
                  )}
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="p-4 border-t border-gray-200 flex items-center justify-between">
        <Group>
          <Text size="sm" c="dimmed">
            Page {currentPage} - Lignes par Page {pageSize} - {filteredModels.length} de {models.length}
          </Text>
        </Group>
        <Group>
          <Select
            value={pageSize.toString()}
            onChange={(value) => setPageSize(Number(value))}
            data={['10', '20', '50', '100']}
            size="sm"
            w={80}
          />
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </div>

      {/* Floating Add Button */}
      <ActionIcon
        size="xl"
        radius="xl"
        variant="filled"
        color="blue"
        className="fixed bottom-6 right-6 shadow-lg"
        onClick={() => {
          setSelectedModel({ title: '', description: '', content: '', category: '' });
          openModelModal();
        }}
      >
        <IconPlus size={24} />
      </ActionIcon>

      {/* Model Modal */}
      <Modal
        opened={modelModalOpened}
        onClose={closeModelModal}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconFileText size={20} />
            <Text fw={500}>Gestion des modèls d&apos;editeur texte</Text>
          </div>
        }
        size="xl"
        padding={0}
      >
        <div className="p-6">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-red-500">
                Titre *
              </Text>
              <TextInput
                value={selectedModel.title}
                onChange={(e) => setSelectedModel(prev => ({ ...prev, title: e.target.value }))}
                className="border-b-2 border-red-500"
              />
            </div>
            <div>
              <Text size="sm" fw={500} mb="xs">
                Context/Category
              </Text>
              <Select
                value={selectedModel.category}
                onChange={(value) => setSelectedModel(prev => ({ ...prev, category: value || '' }))}
                data={[
                  { value: 'patient', label: 'Données patient' },
                  { value: 'biometry', label: 'Biométrie' },
                  { value: 'doctor', label: 'Docteur' },
                  { value: 'plus', label: 'Plus' }
                ]}
                placeholder="Sélectionner une catégorie"
              />
            </div>
          </div>

          <div className="mb-4">
            <RichTextEditor editor={editor}>
              <RichTextEditor.Toolbar sticky stickyOffset={60}>
                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Bold />
                  <RichTextEditor.Italic />
                  <RichTextEditor.Underline />
                  <RichTextEditor.Strikethrough />
                  <RichTextEditor.ClearFormatting />
                  <RichTextEditor.Highlight />
                  <RichTextEditor.Code />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.H1 />
                  <RichTextEditor.H2 />
                  <RichTextEditor.H3 />
                  <RichTextEditor.H4 />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Blockquote />
                  <RichTextEditor.Hr />
                  <RichTextEditor.BulletList />
                  <RichTextEditor.OrderedList />
                  <RichTextEditor.Subscript />
                  <RichTextEditor.Superscript />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.Link />
                  <RichTextEditor.Unlink />
                </RichTextEditor.ControlsGroup>

                <RichTextEditor.ControlsGroup>
                  <RichTextEditor.AlignLeft />
                  <RichTextEditor.AlignCenter />
                  <RichTextEditor.AlignJustify />
                  <RichTextEditor.AlignRight />
                </RichTextEditor.ControlsGroup>
              </RichTextEditor.Toolbar>

              <RichTextEditor.Content />
            </RichTextEditor>
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2 text-gray-500">
              <IconFileText size={16} />
              <Text size="sm">Favoris</Text>
            </div>
            <Group>
              <Button variant="default" onClick={closeModelModal}>
                Annuler
              </Button>
              <Button color="red" onClick={handleSaveModel}>
                Envoyer
              </Button>
            </Group>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default GestionDesModelsDediteurTexte;
