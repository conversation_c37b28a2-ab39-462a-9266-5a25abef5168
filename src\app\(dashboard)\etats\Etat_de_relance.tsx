'use client';
import React from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
} from '@mantine/core';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconBell,
} from '@tabler/icons-react';

const EtatDeRelance = () => {
  // Interface pour les données de relance
  interface RelanceData {
    numeroFacture: string;
    moins15: number;
    moins30: number;
    moins60: number;
    moins120: number;
  }

  // Données d'exemple correspondant à l'image
  const donneesRelance: RelanceData[] = [
    // Pas de données dans l'exemple de l'image, juste les en-têtes et la ligne Total
  ];

  // Calcul des totaux pour la ligne Total
  const totaux = {
    moins15: 0,
    moins30: 0,
    moins60: 0,
    moins120: 0,
  };

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconBell size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              État de relance
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Zone principale du tableau */}
      <div className="bg-white h-[calc(100vh-80px)]">
        <Table
          striped={false}
          highlightOnHover={false}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                N°. Facture
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                -15
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                -30
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                -60
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                -120
              </Table.Th>
              {/* Colonnes vides supplémentaires pour correspondre à l'image */}
              {Array.from({ length: 6 }, (_, index) => (
                <Table.Th
                  key={index}
                  className="border-r border-gray-300 bg-gray-100 w-20"
                />
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {/* Données de relance */}
            {donneesRelance.map((item, index) => (
              <Table.Tr key={index} className="hover:bg-gray-50">
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-800">
                    {item.numeroFacture}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-center">
                  <Text size="sm" className="text-gray-800">
                    {item.moins15}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-center">
                  <Text size="sm" className="text-gray-800">
                    {item.moins30}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-center">
                  <Text size="sm" className="text-gray-800">
                    {item.moins60}
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-center">
                  <Text size="sm" className="text-gray-800">
                    {item.moins120}
                  </Text>
                </Table.Td>
                {/* Cellules vides supplémentaires */}
                {Array.from({ length: 6 }, (_, cellIndex) => (
                  <Table.Td
                    key={cellIndex}
                    className="border-r border-gray-300"
                  />
                ))}
              </Table.Tr>
            ))}

            {/* Ligne Total avec fond vert - correspondant à l'image */}
            <Table.Tr className="bg-green-500">
              <Table.Td className="border-r border-gray-300 bg-green-500">
                <Text size="sm" fw={500} className="text-white">
                  Total
                </Text>
              </Table.Td>
              <Table.Td className="border-r border-gray-300 bg-green-500">
                <div className="h-6 bg-green-600 rounded flex items-center justify-center">
                  <Text size="sm" className="text-white font-medium">
                    {totaux.moins15}
                  </Text>
                </div>
              </Table.Td>
              <Table.Td className="border-r border-gray-300 bg-green-500">
                <div className="h-6 bg-green-600 rounded flex items-center justify-center">
                  <Text size="sm" className="text-white font-medium">
                    {totaux.moins30}
                  </Text>
                </div>
              </Table.Td>
              <Table.Td className="border-r border-gray-300 bg-green-500">
                <div className="h-6 bg-green-600 rounded flex items-center justify-center">
                  <Text size="sm" className="text-white font-medium">
                    {totaux.moins60}
                  </Text>
                </div>
              </Table.Td>
              <Table.Td className="border-r border-gray-300 bg-green-500">
                <div className="h-6 bg-green-600 rounded flex items-center justify-center">
                  <Text size="sm" className="text-white font-medium">
                    {totaux.moins120}
                  </Text>
                </div>
              </Table.Td>
              {/* Cellules vides supplémentaires pour la ligne Total */}
              {Array.from({ length: 6 }, (_, cellIndex) => (
                <Table.Td
                  key={cellIndex}
                  className="border-r border-gray-300 bg-green-500"
                />
              ))}
            </Table.Tr>

            {/* Lignes vides pour remplir l'espace */}
            {Array.from({ length: 15 }, (_, index) => (
              <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                {Array.from({ length: 11 }, (_, cellIndex) => (
                  <Table.Td
                    key={cellIndex}
                    className="border-r border-gray-300 h-8"
                  />
                ))}
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </div>
    </Box>
  );
};

export default EtatDeRelance;
