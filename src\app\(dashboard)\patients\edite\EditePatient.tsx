
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AvatarWithUpload from '@/components/common/AvatarWithUpload';
import Link from 'next/link';
import dayjs from 'dayjs';
import { extractErrorMessage, logError } from '@/utils/errorUtils';
import Icon from '@mdi/react';
import { mdiPlaylistPlus,mdiPlus,mdiRefresh,mdiShare} from '@mdi/js';
import { mdiTooth ,mdiCalendarPlus } from '@mdi/js';
import { useAuth } from '@/hooks/useAuth';
import {PatientActions} from './PatientActions'
import PatientHeader from './PatientHeader'
import {
  Text,
  Container,
  Title,
  ActionIcon,
  Button,
  Group,
  Card,
 Modal,
  Tabs,
  TextInput,
  Textarea,
  Select,
  Grid,
  Paper,
  Stack,
  Divider,
  Badge,
  Loader,
  Alert,
 Switch,

} from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconUser,
  IconAddressBook,
  IconPhone,
  IconMedicalCross,

  IconCalendar,
  IconCheck,
  IconAlertCircle,
  // IconArrowLeft,
  IconEdit,
} from '@tabler/icons-react';
//import { Margarine } from 'next/font/google';
// import authService from '@/services/authService';


interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  date_of_birth: string | null;
  gender: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  medical_conditions: string;
  allergies: string;
  medications: string;
  blood_type: string;
  height: string;
  weight: string;
  profile_image?: string;
  created_at: string;
  updated_at: string;

  file_number?: number,
  category?: string,
  pricing?: number,
  is_bookmarked?: boolean,
  insured?:boolean,
  description?:string,
  titre?: string;
  nationality: string;
    spoken_languages: string;
    cine: string;
    profession: string;
    attending_physician: string;
}


const patient = {
  id: 64,
  full_name: 'ABDESSALMAD AGADIR',
  gender: 'M',
  age: 35,
  default_insurance: 'CNOPS',
  file_number: 'PT-102',
  last_visit: '2025-06-01',
  file_date: new Date(),
};
export default function EditePatient() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>('personal');
  const [is_bookmarked, setis_bookmarked] = useState(false);
  const [insured, setInsured] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { refreshUser } = useAuth();
  const [isCategorieModalOpen, setIsCategorieModalOpen] = useState(false);
const [isNationalityModalOpen, setIsNationalityModalOpen] = useState(false);
const [isLanguagesSpokenModalOpen, setLanguagesSpokenModalOpen] = useState(false);
const [isProfessionModalOpen, setProfessionModalOpen] = useState(false);

    const FicheForm = useForm({
    initialValues: {
          file_number:1,
          category: '',
          pricing: 0,
          is_bookmarked: false,
          insured: false,
         description:'',
         titre: '',
    },
    validate: {
      pricing: (value) => (value < 0 ? 'Last name must be at least 2 characters' : null),
    },
  });
  const personalForm = useForm({
    initialValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone_number: '',
      date_of_birth: null as Date | null,
      gender: '',
      nationality: '',
    spoken_languages: '',
    cine: '',
    profession: '',
    attending_physician: '',
    },
    validate: {
      first_name: (value) => (value.trim().length < 2 ? 'First name must be at least 2 characters' : null),
      last_name: (value) => (value.trim().length < 2 ? 'Last name must be at least 2 characters' : null),
      email: (value) => (/^\S+@\S+\.\S+$/.test(value) ? null : 'Invalid email address'),
      phone_number: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid phone number'),
    },
  });

  const addressForm = useForm({
    initialValues: {
      address: '',
      city: '',
      state: '',
      zip_code: '',
      country: '',
    },
  });

  const emergencyForm = useForm({
    initialValues: {
      emergency_contact_name: '',
      emergency_contact_phone: '',
      emergency_contact_relationship: '',
    },
    validate: {
      emergency_contact_phone: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid phone number'),
    },
  });

  const medicalForm = useForm({
    initialValues: {
      medical_conditions: '',
      allergies: '',
      medications: '',
      blood_type: '',
      height: '',
      weight: '',
    },
  });

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        setError(null);

        // In a real app, you would fetch the patient profile from an API
        // For now, we'll use mock data
        const mockProfile: UserProfile = {
          id: 'patient-123',
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          phone_number: '(*************',
          date_of_birth: '1985-05-15',
          gender: 'Male',
          address: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          zip_code: '12345',
          country: 'United States',
          emergency_contact_name: 'Jane Doe',
          emergency_contact_phone: '(*************',
          emergency_contact_relationship: 'Spouse',
          medical_conditions: 'Hypertension, Asthma',
          allergies: 'Penicillin, Peanuts',
          medications: 'Lisinopril 10mg daily, Albuterol as needed',
          blood_type: 'O+',
          height: '180 cm',
          weight: '75 kg',
          created_at: '2022-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          file_number: 2,
          category: 'visite',
          pricing: 1522,
          is_bookmarked: true,
          insured:true,
          nationality: "marocien",
          spoken_languages: "arabe",
          cine: "wa12458",
          profession: "employ",
          attending_physician: "ahmeb",
        };

        setProfile(mockProfile);

        // Set form values
         FicheForm.setValues({
          file_number: mockProfile.file_number,
          category: mockProfile.category,
          pricing: mockProfile.pricing,
          is_bookmarked: mockProfile.is_bookmarked,
          insured: mockProfile.insured,
        });
        personalForm.setValues({
          first_name: mockProfile.first_name,
          last_name: mockProfile.last_name,
          email: mockProfile.email,
          phone_number: mockProfile.phone_number,
          date_of_birth: mockProfile.date_of_birth ? new Date(mockProfile.date_of_birth) : null,
          gender: mockProfile.gender,
          nationality: mockProfile.nationality,
          spoken_languages: mockProfile.spoken_languages,
          cine: mockProfile.cine,
          profession: mockProfile.profession,
          attending_physician: mockProfile.attending_physician,
        });

        addressForm.setValues({
          address: mockProfile.address,
          city: mockProfile.city,
          state: mockProfile.state,
          zip_code: mockProfile.zip_code,
          country: mockProfile.country,
        });

        emergencyForm.setValues({
          emergency_contact_name: mockProfile.emergency_contact_name,
          emergency_contact_phone: mockProfile.emergency_contact_phone,
          emergency_contact_relationship: mockProfile.emergency_contact_relationship,
        });

        medicalForm.setValues({
          medical_conditions: mockProfile.medical_conditions,
          allergies: mockProfile.allergies,
          medications: mockProfile.medications,
          blood_type: mockProfile.blood_type,
          height: mockProfile.height,
          weight: mockProfile.weight,
        });
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Failed to load profile. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

 const handleUpdateFiche = async (values: typeof FicheForm.values) => {
    try {
      setSubmitting(true);
      
      // In a real app, you would call an API to update the profile
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          file_number: values.file_number,
          category: values.category,
          pricing: values.pricing,
          is_bookmarked: values.is_bookmarked,
          insured: values.insured,
           description: values.description,
         titer: values.titre,
         
           
        };
      });
      
      notifications.show({
        title: 'Success',
        message: 'Personal information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating personal info:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update personal information. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };
  const handleUpdatePersonalInfo = async (values: typeof personalForm.values) => {
    try {
      setSubmitting(true);
      
      // In a real app, you would call an API to update the profile
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          first_name: values.first_name,
          last_name: values.last_name,
          email: values.email,
          phone_number: values.phone_number,
          date_of_birth: values.date_of_birth ? values.date_of_birth.toISOString().split('T')[0] : null,
          gender: values.gender,
          nationality: values.nationality,
          spoken_languages: values.spoken_languages,
          cine: values.cine,
          profession: values.profession,
          attending_physician: values.attending_physician,
        };
      });
      
      notifications.show({
        title: 'Success',
        message: 'Personal information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating personal info:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update personal information. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateAddress = async (values: typeof addressForm.values) => {
    try {
      setSubmitting(true);
      
      // In a real app, you would call an API to update the address
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          address: values.address,
          city: values.city,
          state: values.state,
          zip_code: values.zip_code,
          country: values.country,
        };
      });
      
      notifications.show({
        title: 'Success',
        message: 'Address updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating address:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update address. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateEmergencyContact = async (values: typeof emergencyForm.values) => {
    try {
      setSubmitting(true);
      
      // In a real app, you would call an API to update the emergency contact
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          emergency_contact_name: values.emergency_contact_name,
          emergency_contact_phone: values.emergency_contact_phone,
          emergency_contact_relationship: values.emergency_contact_relationship,
        };
      });
      
      notifications.show({
        title: 'Success',
        message: 'Emergency contact updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating emergency contact:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update emergency contact. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateMedicalInfo = async (values: typeof medicalForm.values) => {
    try {
      setSubmitting(true);
      
      // In a real app, you would call an API to update the medical info
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update the local state
      setProfile(prev => {
        if (!prev) return null;
        return {
          ...prev,
          medical_conditions: values.medical_conditions,
          allergies: values.allergies,
          medications: values.medications,
          blood_type: values.blood_type,
          height: values.height,
          weight: values.weight,
        };
      });
      
      notifications.show({
        title: 'Success',
        message: 'Medical information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (err) {
      console.error('Error updating medical info:', err);
      notifications.show({
        title: 'Error',
        message: 'Failed to update medical information. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Container size="lg" py="xl">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <Loader size="xl" />
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="lg" py="xl" >
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
        <Button mt="md" onClick={() => router.push('/dashboard')}>
          Back to Dashboard
        </Button>
      </Container>
    );
  }
 return (
    <>
            <PatientHeader
      patient={patient}
      onGoBack={() => console.log('Retour')}
      onAddMeasurement={() => console.log('Ajouter biométrie')}
      onShowChart={() => console.log('Afficher graphique')}
      onShowInsurances={() => console.log('Afficher assurances')}
      onOpenMenu={() => console.log('Menu ouvert')}
      onGoToContract={() => console.log('Contrat')}
    />

      <Grid gutter="md" w={"100%"} p={'10px'}>
       
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder p="lg">
      
            <Stack align="center" gap="md">
              {/* <Avatar size={120} radius={120} color="blue">
                {profile?.first_name?.[0]}{profile?.last_name?.[0]}
              </Avatar> */}
               <AvatarWithUpload
                  size={120}
                  radius={120}
                  color="blue"
                  src={profile?.profile_image
                   
                      ? `${profile.profile_image}?t=${Date.now()}`
                      : undefined}
                  initials={`${profile?.first_name?.charAt(0) || ''}${profile?.last_name?.charAt(0) || ''}`}
                  onImageUpload={async (file) => {
                    try {
                      const formData = new FormData();
                      formData.append('profile_image', file);

                      // Show loading notification
                      notifications.show({
                        id: 'uploading-image',
                        title: 'Uploading image',
                        message: 'Please wait while your image is being uploaded...',
                        loading: true,
                        autoClose: false,
                      });

                     

                      // Refresh user data in the AuthContext to update all instances of the avatar
                      await refreshUser();

                      // Show success notification
                      notifications.update({
                        id: 'uploading-image',
                        title: 'Success',
                        message: 'Profile image updated successfully',
                        color: 'green',
                        icon: <IconCheck size={16} />,
                        autoClose: 3000,
                      });

                      
                    } catch (error: any) {
                      // Log the error with detailed information
                      logError('uploading profile image', error);

                      // Extract the error message
                      const errorMessage = extractErrorMessage(error, 'Failed to upload profile image. Please try again.');

                      // Update notification to show error
                      notifications.update({
                        id: 'uploading-image',
                        title: 'Error',
                        message: errorMessage,
                        color: 'red',
                        icon: <IconAlertCircle size={16} />,
                        autoClose: 3000,
                      });
                    }
                  }}
                />
              <Title order={3}>{profile?.first_name} {profile?.last_name}</Title>
              <Badge size="lg">{profile?.gender || 'Not specified'}</Badge>
              
              <Divider w="100%" />
              
              <Stack w="100%" gap="xs">
                <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          
                        <TextInput
                          label="N° de dossier papier"
                          placeholder="N° de dossier papier"
                          required
                          {...FicheForm.getInputProps('file_number')}
                          mb={10}
                        />
                     <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Catégorie"
                        {...FicheForm.getInputProps('category')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setIsCategorieModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group >
                      <Select
                          w={"100%"}
                          label="pricing"
                        {...FicheForm.getInputProps('pricing')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        
                        />
                        <Group justify="center" grow mb={10} mt={26} ml={40}>
                         <Switch
                        defaultChecked
                        label="Favoris"
                        size="xs"
                        {...FicheForm.getInputProps('is_bookmarked')}
                       
                        />
                          <Switch
                        defaultChecked
                        label="Assuré"
                        size="xs"
                        {...FicheForm.getInputProps(' insured ')}
                        />
                     </Group>
                    
                </form>
              
              </Stack>
         
              <Button 
                leftSection={<IconEdit size={16} />} 
                variant="outline" 
                fullWidth
                onClick={() => setActiveTab('personal')}
              >
                Edit Profile
              </Button>
            </Stack>
           
          </Card>
           
        </Grid.Col>
        
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Paper withBorder p="md" radius="md">
            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List mb="md">
                <Tabs.Tab value="personal" leftSection={<IconUser size={14} />}>
                  Personal Information
                </Tabs.Tab>
                <Tabs.Tab value="address" leftSection={<IconAddressBook size={14} />}>
                  Address
                </Tabs.Tab>
                <Tabs.Tab value="emergency" leftSection={<IconPhone size={14} />}>
                  Emergency Contact
                </Tabs.Tab>
                <Tabs.Tab value="medical" leftSection={<IconMedicalCross size={14} />}>
                  Medical Information
                </Tabs.Tab>
                  <Tabs.Tab value="medical" leftSection={<Icon path={mdiShare} size={1} color={'#3799ce'}/>} ml="auto" 
                                 >  
                  <Button variant="subtle" component={Link} href={`/patients`} className='NoHoverBtn'> Liste des patients</Button>
                </Tabs.Tab>
                              
              </Tabs.List>

              <Tabs.Panel value="personal">
                <form onSubmit={personalForm.onSubmit(handleUpdatePersonalInfo)}>
                  <Stack gap="md">
                    <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                          label="First Name"
                          placeholder="Enter first name"
                          required
                          {...personalForm.getInputProps('first_name')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                          label="Last Name"
                          placeholder="Enter last name"
                          required
                          {...personalForm.getInputProps('last_name')}
                        />
                      </Grid.Col>
                    </Grid>

                    <TextInput
                      label="Email"
                      placeholder="Enter email address"
                      required
                      {...personalForm.getInputProps('email')}
                    />
<Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Nationalité"
                        {...personalForm.getInputProps('nationality')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setIsNationalityModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 6 }}>
                       <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Langues parlées"
                        {...personalForm.getInputProps('languages_Spoken')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setLanguagesSpokenModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      </Grid.Col>
                    </Grid>
                    <Grid>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                          label="CNIE"
                          placeholder="CNIE"
                          required
                          {...personalForm.getInputProps('cnie')}
                        />
                      </Grid.Col>
                        <Grid.Col span={{ base: 12, md: 6 }}>
                       <Group mb={10}>
                        <Select
                          w={"68%"}
                          label="Profession"
                        {...personalForm.getInputProps('Profession')}
                          searchable
                          maxDropdownHeight={280}
                          clearable
                          placeholder="Rechercher..."
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setProfessionModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      </Grid.Col>
</Grid>
<Grid>
 <Grid.Col span={{ base: 12, md: 6 }}>
   <Select
                          label="Médecin traitant"
                      placeholder="Médecin traitant"
                          data={[
                            { value: 'ahmed', label: 'ahmed' },
                            { value: 'Stif', label: 'Stif' },
                            { value: 'Other', label: 'Other' },
                           
                          ]}
                          clearable
                          {...personalForm.getInputProps('attending_physician')}
                        />
                    
                    </Grid.Col>
                     <Grid.Col span={{ base: 12, md: 6 }}>
                      <TextInput
                      label="N° Passport"
                      placeholder="N° Passport"
                      {...personalForm.getInputProps('passport_number')}
                    />
                    </Grid.Col>
                    </Grid>
<Grid>
                       <Grid.Col span={{ base: 12, md: 6 }}>
                      <TextInput
                      label={
                        <>
                          Phone Number<sup>1</sup>
                        </>
                      }
                      placeholder="Téléphone"
                      {...personalForm.getInputProps('phone_number')}
                    />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label={
                        <>
                          Phone Number<sup>2</sup>
                        </>
                      }
                      placeholder="Enter phone number"
                      {...personalForm.getInputProps('phone_number')}
                    />
                    </Grid.Col>
</Grid>
                    <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <DatePickerInput
                          label="Date of Birth"
                          placeholder="Select date of birth"
                          leftSection={<IconCalendar size={16} />}
                          clearable
                          {...personalForm.getInputProps('date_of_birth')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <Select
                          label="Gender"
                          placeholder="Select gender"
                          data={[
                            { value: 'Male', label: 'Male' },
                            { value: 'Female', label: 'Female' },
                            { value: 'Other', label: 'Other' },
                            { value: 'Prefer not to say', label: 'Prefer not to say' },
                          ]}
                          clearable
                          {...personalForm.getInputProps('gender')}
                        />
                      </Grid.Col>
                    </Grid>

                    <Group justify="flex-end" mt="md">
                           <Group>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                      </Group>
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="address">
                <form onSubmit={addressForm.onSubmit(handleUpdateAddress)}>
                  <Stack gap="md">
                    <TextInput
                      label="Adressé par"
                      placeholder="Adressé par"
                      {...addressForm.getInputProps('address')}
                    />

                    <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                          <Group mb={10}>
                        <Select
                          w={"68%"}
                         label="Ville"
                          placeholder="Entrez le Ville"
                          data={[
                            { value: 'AGADIR', label: 'AGADIR' },
                            { value: 'AIT BAHA', label: 'AIT BAHA' },
                            { value: 'AZILAL', label: 'AZILAL' },
                            { value: 'BERRECHID', label: 'BERRECHID' },
                         
                          ]}
                          clearable
                          {...addressForm.getInputProps('city')}
                          searchable
                          maxDropdownHeight={280}
                        
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setProfessionModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                       
                       
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                              <Group mb={10}>
                        <Select
                          w={"68%"}
                         label="Pays"
                          placeholder="Entrez le pays"
                          {...addressForm.getInputProps('country')}
                          data={[
                            { value: 'Maroc', label: 'Maroc' },
                            { value: 'France', label: 'France' },
                            { value: 'usa', label: 'usa' },
                            
                         
                          ]}
                          clearable
                          {...addressForm.getInputProps('city')}
                          searchable
                          maxDropdownHeight={280}
                        
                        />
                      <div style={{marginTop:"26px"}}>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      mr={8}
                      onClick={() => setProfessionModalOpen(true)}
                      >
                      <Icon path={mdiPlus} size={1} />
                      </ActionIcon>
                      <ActionIcon  size="input-sm" variant="default" 
                      aria-label="ActionIcon the same size as inputs"
                      //onClick={() => ()}
                      >
                      <Icon path={ mdiRefresh} size={1} />
                      </ActionIcon>
                      </div>
                      </Group>
                      
                      </Grid.Col>
                    </Grid>

                    <Grid>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                          label="Code postal"
                          placeholder="Entrez le code postal"
                          {...addressForm.getInputProps('zip_code')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 6 }}>
                     
                          <TextInput
                          label="État/Province"
                          placeholder="État/Province"
                          {...addressForm.getInputProps('state')}
                        />
                      </Grid.Col>
                    </Grid>
                    <Grid>
                          {/* if wwitsh show hide  texteara */}
                    <Grid.Col span={{ base: 12, md: 2 }} p={0} mt={12}>
                      <div style={{marginTop:'20px',padding:'0'}}>
<Switch
                        defaultChecked
                        label="A complèter"
                        size="xs"
                        {...FicheForm.getInputProps('is_complete')}
                      
                        />
                        </div>
                        </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 10 }}>
                         <Textarea
                        placeholder="Raison"
                         {...FicheForm.getInputProps('Reason')}
                      />
    </Grid.Col>
      </Grid>   
                   <Grid>
                    <Grid.Col span={{ base: 12, md: 2 }}mt={4}>
                      <div style={{marginTop:'20px',padding:'0'}}>
                        {/* if wwitsh show hide datetim piker and texteara */}
 <Switch
                        defaultChecked
                        label="Décès"
                        size="xs"
                        {...FicheForm.getInputProps('Death')}
                        />
                        </div>
                       
                        </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 4 }} mt={12}>
                        {/* <DateTimePicker label="Date de décès" placeholder="Date de décès" /> */}
              <DateTimePicker
      // label="Pick date and time"
                  placeholder="Pick date and time"
                  presets={[
                    { value: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'), label: 'Yesterday' },
                    { value: dayjs().format('YYYY-MM-DD HH:mm:ss'), label: 'Today' },
                    { value: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm:ss'), label: 'Tomorrow' },
                    { value: dayjs().add(1, 'month').format('YYYY-MM-DD HH:mm:ss'), label: 'Next month' },
                    { value: dayjs().add(1, 'year').format('YYYY-MM-DD HH:mm:ss'), label: 'Next year' },
                    {
                      value: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
                      label: 'Last month',
                    },
                    { value: dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'), label: 'Last year' },
                  ]}
                />
                        </Grid.Col>
                         <Grid.Col span={{ base: 12, md: 6 }}>
                         <Textarea
                        placeholder="Raison"
                         {...FicheForm.getInputProps('Raison_de_décès')}
                      />
    </Grid.Col>
      </Grid>       
                    
                    <Group justify="flex-end" mt="md">
                        <Group>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                      </Group>
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="emergency">
                <form onSubmit={emergencyForm.onSubmit(handleUpdateEmergencyContact)}>
                  <Stack gap="md">
                    <TextInput
                      label="Emergency Contact Name"
                      placeholder="Enter emergency contact name"
                      {...emergencyForm.getInputProps('emergency_contact_name')}
                    />

                    <TextInput
                      label="Emergency Contact Phone"
                      placeholder="Enter emergency contact phone"
                      {...emergencyForm.getInputProps('emergency_contact_phone')}
                    />

                    <TextInput
                      label="Relationship to Patient"
                      placeholder="Enter relationship to patient"
                      {...emergencyForm.getInputProps('emergency_contact_relationship')}
                    />

                    <Group justify="flex-end" mt="md">
                        <Group>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                      </Group>
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="medical">
                <form onSubmit={medicalForm.onSubmit(handleUpdateMedicalInfo)}>
                  <Stack gap="md">
                    <Textarea
                      label="Medical Conditions"
                      placeholder="Enter any medical conditions"
                      minRows={3}
                      {...medicalForm.getInputProps('medical_conditions')}
                    />

                    <Textarea
                      label="Allergies"
                      placeholder="Enter any allergies"
                      minRows={3}
                      {...medicalForm.getInputProps('allergies')}
                    />

                    <Textarea
                      label="Current Medications"
                      placeholder="Enter current medications"
                      minRows={3}
                      {...medicalForm.getInputProps('medications')}
                    />

                    <Grid>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <Select
                          label="Blood Type"
                          placeholder="Select blood type"
                          data={[
                            { value: 'A+', label: 'A+' },
                            { value: 'A-', label: 'A-' },
                            { value: 'B+', label: 'B+' },
                            { value: 'B-', label: 'B-' },
                            { value: 'AB+', label: 'AB+' },
                            { value: 'AB-', label: 'AB-' },
                            { value: 'O+', label: 'O+' },
                            { value: 'O-', label: 'O-' },
                          ]}
                          clearable
                          {...medicalForm.getInputProps('blood_type')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <TextInput
                          label="Height"
                          placeholder="e.g., 180 cm"
                          {...medicalForm.getInputProps('height')}
                        />
                      </Grid.Col>
                      <Grid.Col span={{ base: 12, md: 4 }}>
                        <TextInput
                          label="Weight"
                          placeholder="e.g., 75 kg"
                          {...medicalForm.getInputProps('weight')}
                        />
                      </Grid.Col>
                    </Grid>

                    <Group justify="flex-end" mt="md">
                        <Group>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                        <ActionIcon variant="filled" aria-label="Settings">
                        <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
                      </ActionIcon>
                      </Group>
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>
            </Tabs>
          </Paper>
        </Grid.Col>
      </Grid>
      <PatientActions
        patientId={String(patient.id)} // ✅ Convertit le `number` en `string`
        //isFormInvalid={!form?.isValid?.()} // ✅ Vérifie que `form` est bien défini
        //isDraft={!!patient.isDraft} // ✅ Ajoute une valeur par défaut (faux si manquant)
        onPrint={() => console.log('Impression...')}
        onPrevious={() => console.log('Précédent')}
        onNext={() => console.log('Suivant')}
        onStartVisit={() => console.log('Commencer visite')}
        onAppointment={() => console.log('Ajouter RDV')}
        onCancel={() => router.back()}
        onSaveQuitNew={() => console.log('Save & new')}
        onSaveQuit={() => console.log('Save & quit')}
        //onSubmit={() => form?.submit?.()} // ✅ Vérifie que `form` et `submit` existent
      />
       <Modal
        opened={isCategorieModalOpen}
        onClose={() => setIsCategorieModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsCategorieModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
       <Modal
        opened={isNationalityModalOpen}
        onClose={() => setIsNationalityModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsNationalityModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
         <Modal
        opened={isLanguagesSpokenModalOpen}
        onClose={() => setLanguagesSpokenModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setLanguagesSpokenModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
          <Modal
        opened={isProfessionModalOpen}
        onClose={() => setProfessionModalOpen(false)}
        transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
        centered
        title={
          <Group>
            <Icon path={mdiPlaylistPlus} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      > 
     
        <form onSubmit={FicheForm.onSubmit(handleUpdateFiche)}>
          <Stack  gap="md">
            <TextInput
              required
              label="Titre"
               {...FicheForm.getInputProps('titre')}
            />
           <Group>
           
        </Group>
      
            <TextInput
              label="Description"
             {...FicheForm.getInputProps('description')}
            />
           
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setProfessionModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </>
  );
}
