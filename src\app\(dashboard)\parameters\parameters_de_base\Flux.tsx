import React, { useState } from 'react';
import {
  Table,
  Button,
  ActionIcon,
  Group,
  Text,
  Stack,
  Title,
  Paper,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconSettings,
  IconArrowRight,
  IconArrowLeft,
} from '@tabler/icons-react';

interface FluxItem {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  order: number;
}

const Flux = () => {
  // State for flux items
  const [fluxItems, setFluxItems] = useState<FluxItem[]>([
    { id: '1', name: 'Accueil patient', description: 'Réception et enregistrement du patient', status: 'active', order: 1 },
    { id: '2', name: 'Consultation médicale', description: 'Examen médical et diagnostic', status: 'active', order: 2 },
    { id: '3', name: 'Prescription', description: 'Rédaction de l\'ordonnance', status: 'active', order: 3 },
    { id: '4', name: 'Paiement', description: 'Règlement des honoraires', status: 'active', order: 4 },
    { id: '5', name: 'Suivi', description: 'Planification du suivi médical', status: 'inactive', order: 5 },
  ]);

  // Add new flux item
  const handleAddFlux = () => {
    const newFlux: FluxItem = {
      id: Date.now().toString(),
      name: 'Nouvelle étape',
      description: 'Description de la nouvelle étape',
      status: 'active',
      order: fluxItems.length + 1,
    };
    setFluxItems([...fluxItems, newFlux]);
  };

  // Delete flux item
  const handleDeleteFlux = (id: string) => {
    setFluxItems(fluxItems.filter(item => item.id !== id));
  };

  // Move item up
  const moveUp = (index: number) => {
    if (index > 0) {
      const newItems = [...fluxItems];
      [newItems[index], newItems[index - 1]] = [newItems[index - 1], newItems[index]];
      // Update order numbers
      newItems.forEach((item, idx) => {
        item.order = idx + 1;
      });
      setFluxItems(newItems);
    }
  };

  // Move item down
  const moveDown = (index: number) => {
    if (index < fluxItems.length - 1) {
      const newItems = [...fluxItems];
      [newItems[index], newItems[index + 1]] = [newItems[index + 1], newItems[index]];
      // Update order numbers
      newItems.forEach((item, idx) => {
        item.order = idx + 1;
      });
      setFluxItems(newItems);
    }
  };

  // Toggle status
  const toggleStatus = (id: string) => {
    setFluxItems(items =>
      items.map(item =>
        item.id === id
          ? { ...item, status: item.status === 'active' ? 'inactive' : 'active' }
          : item
      )
    );
  };

  return (
    <Stack gap="md" className="w-full">
      {/* Header */}
      <Paper p="md" withBorder>
        <Group justify="space-between" align="center">
          <div>
            <Title order={4} className="text-gray-700">
              Gestion des flux de travail
            </Title>
            <Text size="sm" c="dimmed">
              Configurez l'ordre et les étapes du processus de travail
            </Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleAddFlux}
            variant="filled"
            color="blue"
          >
            Ajouter une étape
          </Button>
        </Group>
      </Paper>

      {/* Table */}
      <Paper p="md" withBorder>
        <Table
          striped
          highlightOnHover
          withTableBorder
          withColumnBorders
          className="w-full"
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-16">
                Ordre
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm">
                Nom de l'étape
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm">
                Description
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-24 text-center">
                Statut
              </Table.Th>
              <Table.Th className="bg-gray-50 text-gray-700 font-medium text-sm w-32 text-center">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {fluxItems.map((item, index) => (
              <Table.Tr key={item.id} className="hover:bg-gray-50">
                {/* Order column */}
                <Table.Td className="border-r border-gray-300 text-center">
                  <Group gap="xs" justify="center">
                    <Text size="sm" fw={500}>
                      {item.order}
                    </Text>
                    <div className="flex flex-col gap-1">
                      <ActionIcon
                        variant="subtle"
                        size="xs"
                        onClick={() => moveUp(index)}
                        disabled={index === 0}
                        className="text-gray-400 hover:text-blue-500"
                      >
                        <IconArrowLeft size={12} style={{ transform: 'rotate(90deg)' }} />
                      </ActionIcon>
                      <ActionIcon
                        variant="subtle"
                        size="xs"
                        onClick={() => moveDown(index)}
                        disabled={index === fluxItems.length - 1}
                        className="text-gray-400 hover:text-blue-500"
                      >
                        <IconArrowRight size={12} style={{ transform: 'rotate(90deg)' }} />
                      </ActionIcon>
                    </div>
                  </Group>
                </Table.Td>

                {/* Name column */}
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" fw={500} className="text-gray-700">
                    {item.name}
                  </Text>
                </Table.Td>

                {/* Description column */}
                <Table.Td className="border-r border-gray-300">
                  <Text size="sm" className="text-gray-600">
                    {item.description}
                  </Text>
                </Table.Td>

                {/* Status column */}
                <Table.Td className="border-r border-gray-300 text-center">
                  <Button
                    size="xs"
                    variant={item.status === 'active' ? 'filled' : 'outline'}
                    color={item.status === 'active' ? 'green' : 'gray'}
                    onClick={() => toggleStatus(item.id)}
                  >
                    {item.status === 'active' ? 'Actif' : 'Inactif'}
                  </Button>
                </Table.Td>

                {/* Actions column */}
                <Table.Td className="text-center">
                  <Group gap="xs" justify="center">
                    <ActionIcon
                      variant="subtle"
                      color="blue"
                      size="sm"
                      title="Modifier"
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                      title="Paramètres"
                    >
                      <IconSettings size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="subtle"
                      color="red"
                      size="sm"
                      title="Supprimer"
                      onClick={() => handleDeleteFlux(item.id)}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>
    </Stack>
  );
};

export default Flux;
