


import React, {  useRef } from "react";
import { EstimatesTabsRef } from './EstimatesTabs';
import Dental13ansetdemi from './Dental13_ans_et_demi'
import  DentalAns12 from './Dental_12_ans '
import Dental_Ans_7 from './Dental_Ans_7'
import Dental_Ans_6 from './Dental_Ans_6'
import DentalMoins6ans from './Dental_Moins-6ans'
import { DentalTabs} from './DentalTabs';
import { useEstimateReactive } from '@/hooks/useEstimateReactive';
import Icon from '@mdi/react';
import { mdiToothOutline } from '@mdi/js';
import { Tabs ,Text} from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';

const Dental = () => {
  const {
    session,
    isLoading,
    saveModification,
  } = useEstimateReactive({
    patientId: 'patient-123', // TODO: Récupérer l'ID du patient actuel
    sessionName: 'Session Devis',
    autoSave: true,
    autoSaveDelay: 2000,
    autoInitialize: false, // NOUVEAU: Désactiver l'initialisation automatique
  });
  const estimatesTabsRef = useRef<EstimatesTabsRef | null>(null);
  return (
    <>
   
       <Tabs defaultValue="Adulte">
      <Tabs.List justify="flex-end" w={"auto"} mb={12}>
        <Tabs.Tab value="Adulte" mr="auto" leftSection={<Icon path={mdiToothOutline} size={1} />}>
         <Text fw={700}>Charte dentaire</Text>
        </Tabs.Tab>
        <Tabs.Tab value="Adulte" leftSection={<IconPhoto size={12} />}>
          Adulte
        </Tabs.Tab>
        <Tabs.Tab value="13ansetdemi" leftSection={<IconMessageCircle size={12} />}>
          13 ans et demi
        </Tabs.Tab>
        <Tabs.Tab value="12ans" leftSection={<IconSettings size={12} />}>
          12 ans
        </Tabs.Tab>

         <Tabs.Tab value="7ansetdemi" leftSection={<IconSettings size={12} />}>
          7 ans et demi
        </Tabs.Tab>

         <Tabs.Tab value="6ans" leftSection={<IconSettings size={12} />}>
          6 ans
        </Tabs.Tab>
         <Tabs.Tab value="Moinsde6ans" leftSection={<IconSettings size={12} />}>
          Moins de 6 ans
        </Tabs.Tab>

      </Tabs.List>

       <Tabs.Panel value="Adulte">
         <DentalTabs
              ref={estimatesTabsRef}
              onModificationChange={saveModification}
              session={session}
              isLoading={isLoading}
            />
      </Tabs.Panel>

      <Tabs.Panel value="13ansetdemi">
       <Dental13ansetdemi/>
      </Tabs.Panel>

      <Tabs.Panel value="12ans">
       <DentalAns12/>
      </Tabs.Panel> 
       <Tabs.Panel value="7ansetdemi">
        <Dental_Ans_7/>
      </Tabs.Panel> 
       <Tabs.Panel value="6ans">
        <Dental_Ans_6/>
      </Tabs.Panel> 
       <Tabs.Panel value="Moinsde6ans">
        <DentalMoins6ans/>
      </Tabs.Panel> 
       
    </Tabs>

   
           
        </>
  );
}
export default Dental;


