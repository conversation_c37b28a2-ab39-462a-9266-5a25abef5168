'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Stack,
  Card,
  Box,
} from '@mantine/core';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconLock,
} from '@tabler/icons-react';

// Interface pour les pathologies
interface Pathologie {
  id: string;
  nom: string;
  nomPatient: string;
  total: number;
  couleur: string;
}

const Pathologies = () => {
  // Données d'exemple des pathologies
  const [pathologies] = useState<Pathologie[]>([
    {
      id: '1',
      nom: 'Pathologie',
      nomPatient: 'Nom du patient',
      total: 1,
      couleur: '#4CAF50', // Vert
    },
    // Vous pouvez ajouter plus de pathologies ici
  ]);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconLock size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Pathologies
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-64 bg-white border-r border-gray-200"
        >
          <Stack gap="xs">
            {/* Filtre Pathologie */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Pathologie
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>

            {/* Filtre Nom du patient */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Nom du patient
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>

            {/* Section Total */}
            <div className="mt-4">
              <div className="bg-green-500 text-white px-2 py-1 rounded-t">
                <Text size="sm" fw={500}>
                  Total
                </Text>
              </div>
              <div className="bg-green-100 px-2 py-1 rounded-b border border-green-300 border-t-0">
                <Text size="sm" className="text-green-800">
                  {pathologies.length}
                </Text>
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du tableau */}
        <div className="flex-1 bg-white">
          <Table
            striped={false}
            highlightOnHover={false}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Pathologie
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nom du patient
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Total
                </Table.Th>
                {/* Colonnes vides pour correspondre à l'image */}
                {Array.from({ length: 8 }, (_, index) => (
                  <Table.Th
                    key={index}
                    className="border-r border-gray-300 bg-gray-100 w-20"
                  />
                ))}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {pathologies.map((pathologie) => (
                <Table.Tr key={pathologie.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300 text-sm">
                    {pathologie.nom}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-sm">
                    {pathologie.nomPatient}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <div
                      className="h-6 rounded text-white text-xs flex items-center justify-center font-medium"
                      style={{ backgroundColor: pathologie.couleur }}
                    >
                      {pathologie.total}
                    </div>
                  </Table.Td>
                  {/* Cellules vides pour correspondre à l'image */}
                  {Array.from({ length: 8 }, (_, index) => (
                    <Table.Td
                      key={index}
                      className="border-r border-gray-300"
                    />
                  ))}
                </Table.Tr>
              ))}

              {/* Lignes vides pour remplir l'espace */}
              {Array.from({ length: 20 }, (_, index) => (
                <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                  {Array.from({ length: 11 }, (_, cellIndex) => (
                    <Table.Td
                      key={cellIndex}
                      className="border-r border-gray-300 h-8"
                    />
                  ))}
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
      </div>
    </Box>
  );
};

export default Pathologies;
