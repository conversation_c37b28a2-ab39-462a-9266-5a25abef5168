'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Stack,
  Card,
  Box,
  Radio,
  Button,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconUsers,
  IconChevronDown,
  IconChevronRight,
} from '@tabler/icons-react';

// Interface pour les données de visite
interface VisiteData {
  id: string;
  organisme: string;
  dateVisite: string;
  montantDu: number;
  montantRegle: number;
  montantRemise: number;
  montantReste: number;
  expanded?: boolean;
  children?: VisiteData[];
}

const PatientVisitsAssures = () => {
  // États pour les filtres
  const [dateDebut, setDateDebut] = useState<string>('01/09/2022');
  const [dateFin, setDateFin] = useState<string>('30/09/2022');
  const [sourceDonnees, setSourceDonnees] = useState('tous');
  const [patientsAssures, setPatientsAssures] = useState(true);

  // États pour l'expansion des lignes
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set(['cnops']));

  // Données d'exemple
  const [visites] = useState<VisiteData[]>([
    {
      id: 'root',
      organisme: '',
      dateVisite: '',
      montantDu: 7300.00,
      montantRegle: 0.00,
      montantRemise: 0.00,
      montantReste: 7300.00,
      children: [
        {
          id: 'cnops',
          organisme: 'CNOPS',
          dateVisite: '',
          montantDu: 7300.00,
          montantRegle: 0.00,
          montantRemise: 0.00,
          montantReste: 7300.00,
          children: [
            {
              id: 'cnops-date',
              organisme: '15/09/2022',
              dateVisite: '15/09/2022',
              montantDu: 7300.00,
              montantRegle: 0.00,
              montantRemise: 0.00,
              montantReste: 7300.00,
            }
          ]
        }
      ]
    }
  ]);

  const toggleRow = (id: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedRows(newExpanded);
  };

  const formatMontant = (montant: number) => {
    return `${montant.toFixed(3)}DH`;
  };

  const renderRow = (item: VisiteData, level: number = 0): React.ReactNode[] => {
    const rows: React.ReactNode[] = [];
    const isExpanded = expandedRows.has(item.id);
    const hasChildren = item.children && item.children.length > 0;

    // Déterminer le style de la ligne
    let rowClass = '';
    let bgColor = '';

    if (item.id === 'root') {
      // Ligne racine (cachée)
      return item.children ? item.children.flatMap(child => renderRow(child, level)) : [];
    } else if (item.id === 'total') {
      // Ligne total
      bgColor = 'bg-green-500';
      rowClass = 'text-white font-bold';
    } else if (level === 0) {
      // Ligne principale (CNOPS)
      bgColor = 'bg-green-100';
    } else {
      // Ligne enfant
      bgColor = 'bg-white';
    }

    rows.push(
      <Table.Tr key={item.id} className={`${rowClass} hover:bg-gray-50`}>
        <Table.Td
          className={`border-r border-gray-300 ${bgColor} ${level > 0 ? 'pl-8' : ''}`}
          style={{ paddingLeft: level > 0 ? `${level * 20 + 8}px` : '8px' }}
        >
          <Group gap="xs" align="center">
            {hasChildren && (
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={() => toggleRow(item.id)}
                className="text-gray-600"
              >
                {isExpanded ? <IconChevronDown size={14} /> : <IconChevronRight size={14} />}
              </ActionIcon>
            )}
            <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-gray-800'}>
              {item.organisme}
            </Text>
          </Group>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-gray-800'}>
            {item.organisme}
          </Text>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-gray-800'}>
            {item.dateVisite}
          </Text>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-gray-800'}>
            {item.organisme}
          </Text>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <div className={`text-center px-2 py-1 rounded ${item.id === 'total' ? 'bg-green-600' : 'bg-green-200'}`}>
            <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-green-800 font-medium'}>
              {formatMontant(item.montantDu)}
            </Text>
          </div>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <div className={`text-center px-2 py-1 rounded ${item.id === 'total' ? 'bg-green-600' : 'bg-green-200'}`}>
            <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-green-800 font-medium'}>
              {formatMontant(item.montantRegle)}
            </Text>
          </div>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <div className={`text-center px-2 py-1 rounded ${item.id === 'total' ? 'bg-green-600' : 'bg-green-200'}`}>
            <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-green-800 font-medium'}>
              {formatMontant(item.montantRemise)}
            </Text>
          </div>
        </Table.Td>
        <Table.Td className={`border-r border-gray-300 ${bgColor}`}>
          <div className={`text-center px-2 py-1 rounded ${item.id === 'total' ? 'bg-green-600' : 'bg-green-200'}`}>
            <Text size="sm" className={item.id === 'total' ? 'text-white font-bold' : 'text-green-800 font-medium'}>
              {formatMontant(item.montantReste)}
            </Text>
          </div>
        </Table.Td>
        {/* Colonnes vides supplémentaires */}
        {Array.from({ length: 3 }, (_, index) => (
          <Table.Td key={index} className={`border-r border-gray-300 ${bgColor}`} />
        ))}
      </Table.Tr>
    );

    // Ajouter les enfants si la ligne est étendue
    if (isExpanded && hasChildren) {
      item.children!.forEach(child => {
        rows.push(...renderRow(child, level + 1));
      });
    }

    return rows;
  };

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUsers size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Patients/Visits assurés
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Section des filtres */}
      <Card shadow="none" padding="md" radius={0} className="bg-white border-b">
        <Group align="center" gap="lg">
          {/* Filtres de date */}
          <Group align="center" gap="sm">
            <Text size="sm" fw={500}>Du</Text>
            <DatePickerInput
              value={new Date(dateDebut)}
              onChange={(value: Date | null) => {
                if (value) {
                  setDateDebut(value.toLocaleDateString('fr-FR'));
                }
              }}
              placeholder="01/09/2022"
              size="sm"
              className="w-32"
            />
            <Text size="sm" fw={500}>Au</Text>
            <DatePickerInput
              value={new Date(dateFin)}
              onChange={(value: Date | null) => {
                if (value) {
                  setDateFin(value.toLocaleDateString('fr-FR'));
                }
              }}
              placeholder="30/09/2022"
              size="sm"
              className="w-32"
            />
          </Group>

          {/* Source de données */}
          <Group align="center" gap="sm">
            <Text size="sm" fw={500}>Source de données</Text>
            <Radio.Group value={sourceDonnees} onChange={setSourceDonnees}>
              <Group gap="md">
                <Radio value="tous" label="Tous les objets payables" size="sm" />
                <Radio value="mutuelles" label="Seulement déclarer en mutuelles" size="sm" />
              </Group>
            </Radio.Group>
          </Group>

          {/* Toggle patients assurés */}
          <Group align="center" gap="sm" className="ml-auto">
            <Text size="sm" fw={500} className="text-gray-600">
              Patients assurée seulement
            </Text>
            <Button
              variant={patientsAssures ? "filled" : "outline"}
              size="xs"
              onClick={() => setPatientsAssures(!patientsAssures)}
              className={patientsAssures ? "bg-blue-500" : ""}
            >
              {patientsAssures ? "ON" : "OFF"}
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[calc(100vh-160px)]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-64 bg-white border-r border-gray-200"
        >
          <Stack gap="xs">
            {/* Filtre Médecin */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Médecin
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>

            {/* Filtre Organisme */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Organisme
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>

            {/* Filtre Date de paiement */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Date de paiement
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>

            {/* Filtre Nom du patient */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Nom du patient
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>

            {/* Filtre Identifiant Unique */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Identifiant Unique
                </Text>
              </div>
              <div className="p-2">
                <Text size="sm" className="text-gray-600">
                  Total
                </Text>
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du tableau */}
        <div className="flex-1 bg-white">
          <Table
            striped={false}
            highlightOnHover={false}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Médecin
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Organisme
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Date de paiement
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nom du patient
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Identifiant Unique
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Somme de Montant dû
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Somme de Montant réglé
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Somme de Remise
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Somme de Reste à régler
                </Table.Th>
                {/* Colonnes vides supplémentaires */}
                {Array.from({ length: 2 }, (_, index) => (
                  <Table.Th
                    key={index}
                    className="border-r border-gray-300 bg-gray-100 w-20"
                  />
                ))}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {/* Rendu des données hiérarchiques */}
              {visites.flatMap(item => renderRow(item))}

              {/* Ligne Total */}
              <Table.Tr className="bg-green-500 text-white font-bold">
                <Table.Td className="border-r border-gray-300 bg-green-500">
                  <Text size="sm" className="text-white font-bold">
                    Total
                  </Text>
                </Table.Td>
                {Array.from({ length: 3 }, (_, index) => (
                  <Table.Td key={index} className="border-r border-gray-300 bg-green-500" />
                ))}
                <Table.Td className="border-r border-gray-300 bg-green-500">
                  <div className="text-center px-2 py-1 rounded bg-green-600">
                    <Text size="sm" className="text-white font-bold">
                      7 300.000DH
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-500">
                  <div className="text-center px-2 py-1 rounded bg-green-600">
                    <Text size="sm" className="text-white font-bold">
                      0.000DH
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-500">
                  <div className="text-center px-2 py-1 rounded bg-green-600">
                    <Text size="sm" className="text-white font-bold">
                      0.000DH
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-500">
                  <div className="text-center px-2 py-1 rounded bg-green-600">
                    <Text size="sm" className="text-white font-bold">
                      7 300.000DH
                    </Text>
                  </div>
                </Table.Td>
                {/* Colonnes vides supplémentaires */}
                {Array.from({ length: 2 }, (_, index) => (
                  <Table.Td key={index} className="border-r border-gray-300 bg-green-500" />
                ))}
              </Table.Tr>

              {/* Lignes vides pour remplir l'espace */}
              {Array.from({ length: 15 }, (_, index) => (
                <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                  {Array.from({ length: 11 }, (_, cellIndex) => (
                    <Table.Td
                      key={cellIndex}
                      className="border-r border-gray-300 h-8"
                    />
                  ))}
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
      </div>
    </Box>
  );
};

export default PatientVisitsAssures;
