import { Select } from '@mantine/core';

interface PaymentMode {
  id: number;
  value: string;
}

interface PaymentModeSelectProps {
  value: number | null;
  onChange: (id: number | null) => void;
  items: PaymentMode[];
  readOnly?: boolean;
  onAdd?: () => void;
  onClear?: () => void;
  showAddButton?: boolean;
  showClearButton?: boolean;
}

export function PaymentModeSelect({
  value,
  onChange,
  items,
  readOnly = false,
  onAdd,
  onClear,
  showAddButton = true,
  showClearButton = true,
}: PaymentModeSelectProps) {
  return (
    <div className="flex items-center gap-2 w-full">
      <Select
        label="Mode"
        placeholder="Choisir un mode de paiement"
        data={items.map((item) => ({
          value: item.id.toString(),
          label: item.value,
        }))}
        value={value?.toString() ?? null}
        onChange={(val) => onChange(val ? parseInt(val) : null)}
        searchable
        nothingFoundMessage="Aucun mode trouvé"
        disabled={readOnly}
        className="flex-1"
      />
      {showAddButton && !readOnly && (
        <button onClick={onAdd} aria-label="Ajouter un mode">
          <i className="mdi mdi-plus" />
        </button>
      )}
      {showClearButton && !readOnly && value !== null && (
        <button onClick={onClear} aria-label="Annuler la sélection">
          <i className="mdi mdi-close" />
        </button>
      )}
    </div>
  );
}
