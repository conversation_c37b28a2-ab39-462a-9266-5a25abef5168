import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';


//general_service poret:8000
console.log('Doctor Portal API URL:', API_URL);

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Include cookies in requests
});

// Add a request interceptor to include the auth token in all requests
api.interceptors.request.use(
  (config) => {
    // Log request details for debugging
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);

    // Don't log FormData objects directly as they can't be easily serialized
    if (config.data && !(config.data instanceof FormData)) {
      console.log('Request payload:', config.data);
    } else if (config.data instanceof FormData) {
      console.log('Request payload: [FormData]');

      // Log FormData entries for debugging
      if (process.env.NODE_ENV === 'development') {
        try {
          // Define a more specific type for FormData entries
          type FormDataEntryValue = string | Blob | File;
          const formDataEntries: Record<string, string | FormDataEntryValue> = {};
          config.data.forEach((value: FormDataEntryValue, key: string) => {
            if (value instanceof File) {
              formDataEntries[key] = `File: ${value.name} (${value.size} bytes)`;
            } else {
              formDataEntries[key] = value;
            }
          });
          console.log('FormData entries:', formDataEntries);
        } catch (error) {
          console.log('Could not log FormData entries:', error);
        }
      }
    }

    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // For FormData requests, let the browser set the Content-Type to include the boundary
    if (config.data instanceof FormData) {
      // Remove Content-Type header to let the browser set it with the correct boundary
      delete config.headers['Content-Type'];
    }

    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => {
    // We're disabling response logging to reduce console noise
    // Only log responses in development mode if needed
    // const isDev = process.env.NODE_ENV === 'development';
    // if (isDev) {
    //   console.log(`API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
    // }
    return response;
  },
  async (error) => {
    // List of endpoints that we want to suppress error logging for
    const suppressErrorLoggingFor = [
      '/api/auth/user/',
      '/api/auth/profile/',
      '/api/auth/users/',
      '/api/auth/assistants/'
    ];

    // Check if this is an error we want to suppress logging for
    const shouldSuppressLogging = error?.config?.url &&
      suppressErrorLoggingFor.some(endpoint => error.config.url.includes(endpoint)) &&
      error?.response?.status === 404;

    // Only log errors that aren't suppressed
    if (!shouldSuppressLogging) {
      try {
        // Instead of using console.error which can get concatenated,
        // use a custom approach to ensure clean logging
        const errorDetails = {
          endpoint: error?.config?.url || 'unknown',
          method: error?.config?.method?.toUpperCase() || 'unknown',
          status: error?.response?.status || 'unknown',
          message: error?.message || 'Unknown error'
        };

        // Use a single line for logging to prevent concatenation
        console.log(`%c API Error: ${JSON.stringify(errorDetails)}`, 'color: red; font-weight: bold;');
      } catch {
        // Fallback if JSON stringification fails
        console.log('API Error occurred (details could not be formatted)');
      }
    }

    const originalRequest = error.config;

    // If the error is 401 and we haven't already tried to refresh the token
    if (error && error.response && error.response.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Attempt to refresh the token
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await axios.post(`${API_URL}/api/auth/token/refresh/`, {
          refresh: refreshToken,
        });

        const { access } = response.data;
        localStorage.setItem('token', access);

        // Retry the original request with the new token
        originalRequest.headers.Authorization = `Bearer ${access}`;
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Always return a rejected promise with the original error
    // This ensures consistent behavior even if there are issues in our error handling
    return Promise.reject(error || new Error('Unknown API error'));
  }
);

export default api;
