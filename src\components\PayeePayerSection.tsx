// components/PayeePayerSection.tsx
import {
  Box,
  Radio,
  Group,
  Stack,
  TextInput,
  Button,
  Divider,
} from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiMagnify } from '@mdi/js';
import { useState } from 'react';

type Props = {
  readOnly: boolean;
  payeeReadOnly: boolean;
};

export const PayeePayerSection = ({ readOnly, payeeReadOnly }: Props) => {
  const [payeeType, setPayeeType] = useState<'P' | 'T'>('P');
  const [payerType, setPayerType] = useState<'P' | 'T' | 'O'>('P');
  const [selectedPatient, setSelectedPatient] = useState('');

  const handleClear = () => {
    setSelectedPatient('');
  };

  const handleShowModal = () => {
    alert('Modal de recherche patient...');
  };

  return (
    <Box>
      {/* Bénéficiaire */}
      <Group align="flex-start" wrap="nowrap">
        <Stack gap="xs" style={{ minHeight: 0 }}>
          <label>Bénéficiaire</label>
          <Radio.Group
            value={payeeType}
            onChange={(value: typeof payeeType) => setPayeeType(value)}
            orientation="horizontal"
            disabled={readOnly || payeeReadOnly}
          >
            <Radio
              value="P"
              label="Patient"
              onClick={handleClear}
            />
            <Radio
              value="T"
              label="Tiers payant"
              onClick={handleClear}
            />
          </Radio.Group>
        </Stack>

        {/* Champ Choisir un patient */}
        {payeeType === 'P' && (
          <Box ml="lg" style={{ flex: 1 }}>
            <TextInput
              readOnly
              label="Choisir un patient"
              placeholder="Nom du patient"
              value={selectedPatient}
              onClick={handleShowModal}
              rightSection={
                <Button
                  size="xs"
                  variant="subtle"
                  onClick={handleShowModal}
                  disabled={readOnly || payeeReadOnly}
                >
                  <Icon path={mdiMagnify} size={0.8} />
                </Button>
              }
            />
          </Box>
        )}
      </Group>

      <Divider my="md" />

      {/* Payeur */}
      <Group align="flex-start" wrap="nowrap">
        <Stack gap="xs">
          <label>Payeur</label>
          <Radio.Group
            value={payerType}
            onChange={(value: typeof payerType) => setPayerType(value)}
            orientation="horizontal"
            disabled={readOnly || payeeReadOnly}
          >
            <Radio
              value="P"
              label="Patient"
              disabled={payerType === 'T'}
            />
            <Radio
              value="T"
              label="Tiers payant"
              disabled={payerType !== 'T'}
            />
            <Radio
              value="O"
              label="Autre"
              disabled={payerType === 'T'}
            />
          </Radio.Group>
        </Stack>
      </Group>
    </Box>
  );
};
