/**
 * Format a date string to a human-readable format
 * @param dateString - ISO date string
 * @returns Formatted date string (e.g., "Monday, January 1, 2023")
 */
export function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date: ${dateString}`);
      return 'N/A';
    }

    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch (error) {
    console.error(`Error formatting date: ${dateString}`, error);
    return 'N/A';
  }
}

/**
 * Format a date string to a short date format
 * @param dateString - ISO date string
 * @returns Formatted date string (e.g., "Jan 1, 2023")
 */
export function formatShortDate(dateString: string): string {
  try {
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date: ${dateString}`);
      return 'N/A';
    }

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  } catch (error) {
    console.error(`Error formatting date: ${dateString}`, error);
    return 'N/A';
  }
}

/**
 * Format a date string to a simple date format for tables
 * Handles invalid dates gracefully by returning a fallback value
 *
 * @param dateString - ISO date string
 * @param fallback - The fallback value to return if the date is invalid (default: 'N/A')
 * @returns Formatted date string or the fallback value
 */
export function formatTableDate(dateString: string | null | undefined, fallback: string = 'N/A'): string {
  if (!dateString) return fallback;

  try {
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date for table: ${dateString}`);
      return fallback;
    }

    return date.toLocaleDateString();
  } catch (error) {
    console.error(`Error formatting table date: ${dateString}`, error);
    return fallback;
  }
}

/**
 * Format a time string to a human-readable format
 * @param dateString - ISO date string
 * @returns Formatted time string (e.g., "2:30 PM")
 */
export function formatTime(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

/**
 * Format a date range for display
 * @param startDate - ISO date string for start date
 * @param endDate - ISO date string for end date
 * @returns Formatted date range string
 */
export function formatDateRange(startDate: string, endDate: string): string {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // If same day, just show one date with time range
  if (start.toDateString() === end.toDateString()) {
    return `${formatShortDate(startDate)}, ${formatTime(startDate)} - ${formatTime(endDate)}`;
  }

  // Otherwise show full date range
  return `${formatShortDate(startDate)} - ${formatShortDate(endDate)}`;
}

/**
 * Get relative time description (e.g., "2 days ago", "in 3 hours")
 * @param dateString - ISO date string
 * @returns Relative time description
 */
export function getRelativeTimeDescription(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);

  if (diffDay > 0) {
    return diffDay === 1 ? 'tomorrow' : `in ${diffDay} days`;
  } else if (diffDay < 0) {
    return diffDay === -1 ? 'yesterday' : `${Math.abs(diffDay)} days ago`;
  } else if (diffHour > 0) {
    return diffHour === 1 ? 'in 1 hour' : `in ${diffHour} hours`;
  } else if (diffHour < 0) {
    return diffHour === -1 ? '1 hour ago' : `${Math.abs(diffHour)} hours ago`;
  } else if (diffMin > 0) {
    return diffMin === 1 ? 'in 1 minute' : `in ${diffMin} minutes`;
  } else if (diffMin < 0) {
    return diffMin === -1 ? '1 minute ago' : `${Math.abs(diffMin)} minutes ago`;
  } else {
    return diffSec >= 0 ? 'just now' : 'just now';
  }
}

/**
 * Check if a date is in the past
 * @param dateString - ISO date string
 * @returns Boolean indicating if date is in the past
 */
export function isPastDate(dateString: string): boolean {
  const date = new Date(dateString);
  const now = new Date();
  return date < now;
}

/**
 * Check if a date is today
 * @param dateString - ISO date string
 * @returns Boolean indicating if date is today
 */
export function isToday(dateString: string): boolean {
  const date = new Date(dateString);
  const now = new Date();
  return date.toDateString() === now.toDateString();
}

/**
 * Format a date for API requests (YYYY-MM-DD)
 * @param date - Date object
 * @returns Formatted date string (e.g., "2023-01-01")
 */
export function formatDateForAPI(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Get day of week name
 * @param dayIndex - Day index (0-6, where 0 is Sunday)
 * @returns Day name
 */
export function getDayName(dayIndex: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayIndex];
}

/**
 * Format time in 24-hour format to 12-hour format
 * @param time - Time string in 24-hour format (HH:MM)
 * @returns Time string in 12-hour format (e.g., "2:30 PM")
 */
export function formatTimeFrom24Hour(time: string): string {
  const [hours, minutes] = time.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12;
  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
}
