import React, { useState } from 'react';
import {
  Stack,
  Title,
  Select,
  Checkbox,
  Group,
  Text,
  Paper,
  Collapse,
  ActionIcon,
  Grid,
} from '@mantine/core';
import {
  IconPlus,
  IconX,
  IconChevronDown,
  IconChevronUp,
} from '@tabler/icons-react';

const Prescriptions = () => {
  // State for form values
  const [formeGalenique, setFormeGalenique] = useState('Comprimé');
  const [baseMedicament, setBaseMedicament] = useState(false);
  const [utilisationAvancee, setUtilisationAvancee] = useState(true);
  const [utilisationProduits, setUtilisationProduits] = useState(false);
  const [posologieArabe, setPosologieArabe] = useState(false);

  // State for generators
  const [generateur1, setGenerateur1] = useState('[qte_unit], [frequencies], [period]');
  const [generateur2, setGenerateur2] = useState('prescription_period_prefix');
  const [generateur3, setGenerateur3] = useState('prescription_next');

  // State for additional generators
  const [generateurFormu, setGenerateurFormu] = useState('[qte_unit], [frequencies], [period]');
  const [generateurArabe1, setGenerateurArabe1] = useState('');
  const [generateurArabe2, setGenerateurArabe2] = useState('');

  // State for dictionaries
  const [dicChampsSalutations, setDicChampsSalutations] = useState('Salutations');
  const [dictObservation, setDictObservation] = useState('');
  const [dictCommentaire, setDictCommentaire] = useState('');

  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState({
    prescriptions: true,
    generators: true,
    ophtalmique: true,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Data options for selects
  const formeGaleniqueOptions = [
    { value: 'Comprimé', label: 'Comprimé' },
    { value: 'Gélule', label: 'Gélule' },
    { value: 'Sirop', label: 'Sirop' },
    { value: 'Injection', label: 'Injection' },
    { value: 'Pommade', label: 'Pommade' },
  ];

  const generatorOptions = [
    { value: '[qte_unit], [frequencies], [period]', label: '[qte_unit], [frequencies], [period]' },
    { value: 'prescription_period_prefix', label: 'prescription_period_prefix' },
    { value: 'prescription_next', label: 'prescription_next' },
    { value: 'Utilisation de la posologie en Arabe', label: 'Utilisation de la posologie en Arabe' },
  ];

  const salutationsOptions = [
    { value: 'Salutations', label: 'Salutations' },
    { value: 'Cordialement', label: 'Cordialement' },
    { value: 'Respectueusement', label: 'Respectueusement' },
  ];

  const observationOptions = [
    { value: 'observation1', label: 'Observation 1' },
    { value: 'observation2', label: 'Observation 2' },
    { value: 'observation3', label: 'Observation 3' },
  ];

  const commentaireOptions = [
    { value: 'commentaire1', label: 'Commentaire 1' },
    { value: 'commentaire2', label: 'Commentaire 2' },
    { value: 'commentaire3', label: 'Commentaire 3' },
  ];

  return (
    <Stack gap="lg" className="w-full">
      {/* Prescriptions Section */}
      <Paper p="md" withBorder>
        <Group
          justify="space-between"
          className="cursor-pointer mb-md"
          onClick={() => toggleSection('prescriptions')}
        >
          <Title order={4} className="text-gray-700">
            Prescriptions
          </Title>
          <ActionIcon variant="subtle" size="sm">
            {expandedSections.prescriptions ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
          </ActionIcon>
        </Group>

        <Collapse in={expandedSections.prescriptions}>
          <Stack gap="md">
            {/* Forme galénique - Unité */}
            <div>
              <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                Forme galénique - Unité
              </Text>
              <Group gap="xs">
                <Select
                  value={formeGalenique}
                  onChange={(value) => setFormeGalenique(value || '')}
                  data={formeGaleniqueOptions}
                  className="flex-1"
                  size="sm"
                />
                <ActionIcon variant="subtle" color="blue" size="sm">
                  <IconPlus size={14} />
                </ActionIcon>
                <ActionIcon variant="subtle" color="gray" size="sm">
                  <IconX size={14} />
                </ActionIcon>
              </Group>
            </div>

            {/* Checkboxes */}
            <Stack gap="xs">
              <Checkbox
                checked={baseMedicament}
                onChange={(event) => setBaseMedicament(event.currentTarget.checked)}
                label="Base Médicament par défaut, Favoris"
                size="sm"
              />
              <Checkbox
                checked={utilisationAvancee}
                onChange={(event) => setUtilisationAvancee(event.currentTarget.checked)}
                label="Utilisation Avancée (affichage des prix, presentation...)"
                size="sm"
              />
              <Checkbox
                checked={utilisationProduits}
                onChange={(event) => setUtilisationProduits(event.currentTarget.checked)}
                label="Utilisation des produits Paramédicaux & Parapharm..."
                size="sm"
              />
              <Checkbox
                checked={posologieArabe}
                onChange={(event) => setPosologieArabe(event.currentTarget.checked)}
                label="Posologie en Arabe par défaut"
                size="sm"
              />
            </Stack>
          </Stack>
        </Collapse>
      </Paper>

      {/* Generators Section */}
      <Paper p="md" withBorder>
        <Group
          justify="space-between"
          className="cursor-pointer mb-md"
          onClick={() => toggleSection('generators')}
        >
          <Title order={4} className="text-gray-700">
            Générateurs de posologie
          </Title>
          <ActionIcon variant="subtle" size="sm">
            {expandedSections.generators ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
          </ActionIcon>
        </Group>

        <Collapse in={expandedSections.generators}>
          <Grid>
            {/* First row of generators */}
            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Générateur de posologie Arabe(...)
                </Text>
                <Group gap="xs">
                  <Select
                    value={generateur1}
                    onChange={(value) => setGenerateur1(value || '')}
                    data={generatorOptions}
                    className="flex-1"
                    size="sm"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Générateur de posologie (préfix ...)
                </Text>
                <Group gap="xs">
                  <Select
                    value={generateur2}
                    onChange={(value) => setGenerateur2(value || '')}
                    data={generatorOptions}
                    className="flex-1"
                    size="sm"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Générateur de posologie (début ...)
                </Text>
                <Group gap="xs">
                  <Select
                    value={generateur3}
                    onChange={(value) => setGenerateur3(value || '')}
                    data={generatorOptions}
                    className="flex-1"
                    size="sm"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            {/* Second row of generators */}
            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Générateur de posologie (formu...)
                </Text>
                <Group gap="xs">
                  <Select
                    value={generateurFormu}
                    onChange={(value) => setGenerateurFormu(value || '')}
                    data={generatorOptions}
                    className="flex-1"
                    size="sm"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Générateur de posologie Arabe(...)
                </Text>
                <Group gap="xs">
                  <Select
                    value={generateurArabe1}
                    onChange={(value) => setGenerateurArabe1(value || '')}
                    data={generatorOptions}
                    className="flex-1"
                    size="sm"
                    placeholder="Sélectionner"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Générateur de posologie Arabe(...)
                </Text>
                <Group gap="xs">
                  <Select
                    value={generateurArabe2}
                    onChange={(value) => setGenerateurArabe2(value || '')}
                    data={generatorOptions}
                    className="flex-1"
                    size="sm"
                    placeholder="Sélectionner"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            {/* Dictionary section */}
            <Grid.Col span={6}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Dictionnaire du champs conseils
                </Text>
                <Group gap="xs">
                  <Select
                    value={dicChampsSalutations}
                    onChange={(value) => setDicChampsSalutations(value || '')}
                    data={salutationsOptions}
                    className="flex-1"
                    size="sm"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
                <Text size="xs" className="text-gray-500 mt-1">
                  conseils
                </Text>
              </div>
            </Grid.Col>

            <Grid.Col span={6}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Dictionnaire du champs salutati...
                </Text>
                <Group gap="xs">
                  <Select
                    value={dicChampsSalutations}
                    onChange={(value) => setDicChampsSalutations(value || '')}
                    data={salutationsOptions}
                    className="flex-1"
                    size="sm"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>
          </Grid>
        </Collapse>
      </Paper>

      {/* Prescription Ophtalmique Section */}
      <Paper p="md" withBorder>
        <Group
          justify="space-between"
          className="cursor-pointer mb-md"
          onClick={() => toggleSection('ophtalmique')}
        >
          <Title order={4} className="text-gray-700">
            Prescription Ophtalmique
          </Title>
          <ActionIcon variant="subtle" size="sm">
            {expandedSections.ophtalmique ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
          </ActionIcon>
        </Group>

        <Collapse in={expandedSections.ophtalmique}>
          <Grid>
            <Grid.Col span={6}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Dictionnaire d'Observation
                </Text>
                <Group gap="xs">
                  <Select
                    value={dictObservation}
                    onChange={(value) => setDictObservation(value || '')}
                    data={observationOptions}
                    className="flex-1"
                    size="sm"
                    placeholder="Sélectionner"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>

            <Grid.Col span={6}>
              <div>
                <Text size="sm" fw={500} mb="xs" className="text-gray-700">
                  Dictionnaire du Commentaire
                </Text>
                <Group gap="xs">
                  <Select
                    value={dictCommentaire}
                    onChange={(value) => setDictCommentaire(value || '')}
                    data={commentaireOptions}
                    className="flex-1"
                    size="sm"
                    placeholder="Sélectionner"
                  />
                  <ActionIcon variant="subtle" color="blue" size="sm">
                    <IconPlus size={14} />
                  </ActionIcon>
                  <ActionIcon variant="subtle" color="gray" size="sm">
                    <IconX size={14} />
                  </ActionIcon>
                </Group>
              </div>
            </Grid.Col>
          </Grid>
        </Collapse>
      </Paper>
    </Stack>
  );
};

export default Prescriptions;
