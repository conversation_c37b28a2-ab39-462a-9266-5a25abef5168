import api from '../lib/api';

// Types for pharmacy entities
export interface Supplier {
  id?: string;
  raison_sociale: string;
  email: string;
  ville: string;
  tel_gestionnaire: string;
  tel_fixe: string;
  fax: string;
  adresse: string;
  commentaire: string;
  mode_paiement: string;
  condition_paiement: string;
  ice: string;
  rc: string;
  directeur_commercial: string;
  gestionnaire_vente: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Depot {
  id?: string;
  name: string;
  code: string;
  address: string;
  manager: string;
  phone: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Product {
  id?: string;
  code: string;
  designation: string;
  category?: string;
  category_name?: string;
  unit: string;
  price: number;
  barcode: string;
  description: string;
  min_stock: number;
  max_stock: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface PurchaseRequestItem {
  id?: string;
  product: string;
  product_code?: string;
  product_designation?: string;
  depot: string;
  depot_name?: string;
  quantity: number;
  unit_price?: number;
  total_price?: number;
}

export interface PurchaseRequest {
  id?: string;
  numero: string;
  date: string;
  date_echeance?: string;
  urgent: boolean;
  supplier: string;
  supplier_name?: string;
  commentaire: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'completed';
  items?: PurchaseRequestItem[];
  created_at?: string;
  updated_at?: string;
}

export interface Inventory {
  id?: string;
  product: string;
  product_code?: string;
  product_designation?: string;
  depot: string;
  depot_name?: string;
  quantity: number;
  reserved_quantity: number;
  available_quantity?: number;
  last_movement_date?: string;
}

// API endpoints
const ENDPOINTS = {
  suppliers: '/api/pharmacy/suppliers/',
  depots: '/api/pharmacy/depots/',
  products: '/api/pharmacy/products/',
  purchaseRequests: '/api/pharmacy/purchase-requests/',
  inventory: '/api/pharmacy/inventory/',
  stockMovements: '/api/pharmacy/stock-movements/',
};

// Helper function to make API calls
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: any,
  params?: any
) => {
  try {
    const config = { params };
    if (method === 'get') {
      const response = await api.get(endpoint, config);
      return response.data;
    } else {
      const response = await api[method](endpoint, data, config);
      return response.data;
    }
  } catch (error) {
    console.error(`API call failed for ${method.toUpperCase()} ${endpoint}:`, error);
    throw error;
  }
};

// Supplier Service
export const supplierService = {
  // Get all suppliers
  getAll: async (params?: any): Promise<Supplier[]> => {
    return makeApiCall(ENDPOINTS.suppliers, 'get', null, params);
  },

  // Get supplier by ID
  getById: async (id: string): Promise<Supplier> => {
    return makeApiCall(`${ENDPOINTS.suppliers}${id}/`);
  },

  // Create new supplier
  create: async (supplier: Omit<Supplier, 'id' | 'created_at' | 'updated_at'>): Promise<Supplier> => {
    return makeApiCall(ENDPOINTS.suppliers, 'post', supplier);
  },

  // Update supplier
  update: async (id: string, supplier: Partial<Supplier>): Promise<Supplier> => {
    return makeApiCall(`${ENDPOINTS.suppliers}${id}/`, 'put', supplier);
  },

  // Delete supplier
  delete: async (id: string): Promise<void> => {
    return makeApiCall(`${ENDPOINTS.suppliers}${id}/`, 'delete');
  },

  // Get simple list for dropdowns
  getSimpleList: async (): Promise<{ id: string; raison_sociale: string }[]> => {
    return makeApiCall(`${ENDPOINTS.suppliers}simple_list/`);
  },
};

// Depot Service
export const depotService = {
  // Get all depots
  getAll: async (params?: any): Promise<Depot[]> => {
    return makeApiCall(ENDPOINTS.depots, 'get', null, params);
  },

  // Get depot by ID
  getById: async (id: string): Promise<Depot> => {
    return makeApiCall(`${ENDPOINTS.depots}${id}/`);
  },

  // Create new depot
  create: async (depot: Omit<Depot, 'id' | 'created_at' | 'updated_at'>): Promise<Depot> => {
    return makeApiCall(ENDPOINTS.depots, 'post', depot);
  },

  // Update depot
  update: async (id: string, depot: Partial<Depot>): Promise<Depot> => {
    return makeApiCall(`${ENDPOINTS.depots}${id}/`, 'put', depot);
  },

  // Delete depot
  delete: async (id: string): Promise<void> => {
    return makeApiCall(`${ENDPOINTS.depots}${id}/`, 'delete');
  },

  // Get simple list for dropdowns
  getSimpleList: async (): Promise<{ id: string; name: string; code: string }[]> => {
    return makeApiCall(`${ENDPOINTS.depots}simple_list/`);
  },
};

// Product Service
export const productService = {
  // Get all products
  getAll: async (params?: any): Promise<Product[]> => {
    return makeApiCall(ENDPOINTS.products, 'get', null, params);
  },

  // Get product by ID
  getById: async (id: string): Promise<Product> => {
    return makeApiCall(`${ENDPOINTS.products}${id}/`);
  },

  // Create new product
  create: async (product: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> => {
    return makeApiCall(ENDPOINTS.products, 'post', product);
  },

  // Update product
  update: async (id: string, product: Partial<Product>): Promise<Product> => {
    return makeApiCall(`${ENDPOINTS.products}${id}/`, 'put', product);
  },

  // Delete product
  delete: async (id: string): Promise<void> => {
    return makeApiCall(`${ENDPOINTS.products}${id}/`, 'delete');
  },

  // Get simple list for dropdowns
  getSimpleList: async (): Promise<{ id: string; code: string; designation: string; unit: string; price: number }[]> => {
    return makeApiCall(`${ENDPOINTS.products}simple_list/`);
  },

  // Get inventory for a product
  getInventory: async (id: string): Promise<Inventory[]> => {
    return makeApiCall(`${ENDPOINTS.products}${id}/inventory/`);
  },
};

// Purchase Request Service
export const purchaseRequestService = {
  // Get all purchase requests
  getAll: async (params?: any): Promise<PurchaseRequest[]> => {
    return makeApiCall(ENDPOINTS.purchaseRequests, 'get', null, params);
  },

  // Get purchase request by ID
  getById: async (id: string): Promise<PurchaseRequest> => {
    return makeApiCall(`${ENDPOINTS.purchaseRequests}${id}/`);
  },

  // Create new purchase request
  create: async (purchaseRequest: Omit<PurchaseRequest, 'id' | 'created_at' | 'updated_at'>): Promise<PurchaseRequest> => {
    return makeApiCall(ENDPOINTS.purchaseRequests, 'post', purchaseRequest);
  },

  // Update purchase request
  update: async (id: string, purchaseRequest: Partial<PurchaseRequest>): Promise<PurchaseRequest> => {
    return makeApiCall(`${ENDPOINTS.purchaseRequests}${id}/`, 'put', purchaseRequest);
  },

  // Delete purchase request
  delete: async (id: string): Promise<void> => {
    return makeApiCall(`${ENDPOINTS.purchaseRequests}${id}/`, 'delete');
  },

  // Approve purchase request
  approve: async (id: string): Promise<{ status: string }> => {
    return makeApiCall(`${ENDPOINTS.purchaseRequests}${id}/approve/`, 'post');
  },

  // Reject purchase request
  reject: async (id: string): Promise<{ status: string }> => {
    return makeApiCall(`${ENDPOINTS.purchaseRequests}${id}/reject/`, 'post');
  },

  // Submit purchase request
  submit: async (id: string): Promise<{ status: string }> => {
    return makeApiCall(`${ENDPOINTS.purchaseRequests}${id}/submit/`, 'post');
  },
};

// Inventory Service
export const inventoryService = {
  // Get all inventory items
  getAll: async (params?: any): Promise<Inventory[]> => {
    return makeApiCall(ENDPOINTS.inventory, 'get', null, params);
  },

  // Get inventory by depot
  getByDepot: async (depotId: string): Promise<Inventory[]> => {
    return makeApiCall(`${ENDPOINTS.inventory}by_depot/`, 'get', null, { depot_id: depotId });
  },

  // Get low stock items
  getLowStock: async (): Promise<Inventory[]> => {
    return makeApiCall(`${ENDPOINTS.inventory}low_stock/`);
  },

  // Update inventory
  update: async (id: string, inventory: Partial<Inventory>): Promise<Inventory> => {
    return makeApiCall(`${ENDPOINTS.inventory}${id}/`, 'put', inventory);
  },
};

export default {
  supplier: supplierService,
  depot: depotService,
  product: productService,
  purchaseRequest: purchaseRequestService,
  inventory: inventoryService,
};
