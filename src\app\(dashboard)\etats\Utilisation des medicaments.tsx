'use client';
import React, { useState, useEffect } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  Select,
  Table,
  ActionIcon,
  Tooltip,
  Card,
  Text,
  LoadingOverlay,
  Alert,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconPill,
  IconPrinter,
  IconFileExport,
  IconTable,
  IconSettings,
  IconChartBar,
  IconRefresh,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useMedicaments } from '../../../hooks/useMedicaments';
import { MedicamentFilters } from '../../../services/medicamentService';

const UtilisationDesMedicaments = () => {
  // États pour la gestion des filtres
  const [dateDebut, setDateDebut] = useState<Date | null>(new Date('2022-06-01'));
  const [dateFin, setDateFin] = useState<Date | null>(new Date('2022-09-30'));
  const [sourceDonnees, setSourceDonnees] = useState<string>('dernieres_prescription');

  // Hook personnalisé pour les médicaments
  const {
    medicaments,
    stats,
    loading,
    error,
    fetchMedicaments,
    exportData,
    refreshData,
  } = useMedicaments();
    {
      id: '1',
      nom: 'Actav 1 g / 125 mg | sachet',
      nombreUtilisation: 3,
      couleur: '#E8F5E8',
    },
    {
      id: '2',
      nom: 'Augmentin 1g /125 mg | sachet',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
    },
    {
      id: '3',
      nom: 'Bi-rodogyl 1,5 ml / 250 mg | comprimé',
      nombreUtilisation: 4,
      couleur: '#E8F5E8',
    },
    {
      id: '4',
      nom: 'Brufen 400 mg | Comprimé',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
    },
    {
      id: '5',
      nom: 'Cataflan 25 mg | Dragée',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
    },
    {
      id: '6',
      nom: 'Di-indo 25 mg | Comprimé',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
    },
    {
      id: '7',
      nom: 'Doliprane 500 mg | sachet',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
    },
    {
      id: '8',
      nom: 'Ery 250 mg | sachet',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
    },
    {
      id: '9',
      nom: 'Solumedrol 120mg / 2ml | injection',
      nombreUtilisation: 1,
      couleur: '#E8F5E8',
    },
    {
      id: '10',
      nom: 'Spectrum 500 mg | Comprimé',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
    },
    {
      id: '11',
      nom: 'Surgam 200 mg | Comprimé',
      nombreUtilisation: 2,
      couleur: '#E8F5E8',
    },
  ]);

  // Calcul du total
  const totalUtilisations = medicaments.reduce((sum, med) => sum + med.nombreUtilisation, 0);

  // Options pour la source de données
  const sourceOptions = [
    { value: 'dernieres_prescription', label: 'Dernières prescription' },
    { value: 'toutes_prescriptions', label: 'Toutes les prescriptions' },
    { value: 'prescriptions_validees', label: 'Prescriptions validées' },
  ];

  return (
    <Paper p="xl" radius="md" withBorder w="100%">
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group align="center">
          <IconPill size={24} color="#4A90E2" />
          <Title order={3} c="#4A90E2" className="text-lg font-semibold">
            Utilisation des médicaments
          </Title>
        </Group>
      </Group>

      {/* Filters Section */}
      <Card withBorder mb="md" className="bg-gray-50">
        <Grid align="end">
          <Grid.Col span={3}>
            <Text size="sm" fw={500} mb="xs" className="text-gray-700">
              Du
            </Text>
            <DatePickerInput
              value={dateDebut}
              onChange={setDateDebut}
              placeholder="01/06/2022"
              size="sm"
              className="w-full"
            />
          </Grid.Col>
          <Grid.Col span={3}>
            <Text size="sm" fw={500} mb="xs" className="text-gray-700">
              Au
            </Text>
            <DatePickerInput
              value={dateFin}
              onChange={setDateFin}
              placeholder="30/09/2022"
              size="sm"
              className="w-full"
            />
          </Grid.Col>
          <Grid.Col span={4}>
            <Text size="sm" fw={500} mb="xs" className="text-gray-700">
              Source de données
            </Text>
            <Select
              value={sourceDonnees}
              onChange={(value) => setSourceDonnees(value || '')}
              data={sourceOptions}
              size="sm"
              className="w-full"
            />
          </Grid.Col>
          <Grid.Col span={2}>
            <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
              Filtrer
            </Button>
          </Grid.Col>
        </Grid>
      </Card>

      {/* Table Section */}
      <Card withBorder mb="md">
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th className="text-gray-700 font-semibold">Médicament</Table.Th>
              <Table.Th className="text-gray-700 font-semibold text-center">Nombre d&apos;utilisation</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {medicaments.map((medicament) => (
              <Table.Tr key={medicament.id} style={{ backgroundColor: medicament.couleur }}>
                <Table.Td className="py-3">
                  <Text size="sm" className="text-gray-800">
                    {medicament.nom}
                  </Text>
                </Table.Td>
                <Table.Td className="text-center py-3">
                  <Text size="sm" fw={500} className="text-gray-800">
                    {medicament.nombreUtilisation}
                  </Text>
                </Table.Td>
              </Table.Tr>
            ))}
            {/* Total Row */}
            <Table.Tr style={{ backgroundColor: '#F0F8FF', borderTop: '2px solid #4A90E2' }}>
              <Table.Td className="py-3">
                <Text size="sm" fw={700} className="text-blue-600">
                  Total
                </Text>
              </Table.Td>
              <Table.Td className="text-center py-3">
                <Text size="sm" fw={700} className="text-blue-600">
                  {totalUtilisations}
                </Text>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>
      </Card>

      {/* Action Buttons */}
      <Group justify="center" mt="xl">
        <Tooltip label="Imprimer le rapport">
          <ActionIcon size="lg" variant="outline" color="blue">
            <IconPrinter size={20} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Exporter en Excel">
          <ActionIcon size="lg" variant="outline" color="green">
            <IconFileExport size={20} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Affichage tableau">
          <ActionIcon size="lg" variant="outline" color="gray">
            <IconTable size={20} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Affichage graphique">
          <ActionIcon size="lg" variant="outline" color="orange">
            <IconChartBar size={20} />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Paramètres">
          <ActionIcon size="lg" variant="outline" color="purple">
            <IconSettings size={20} />
          </ActionIcon>
        </Tooltip>
      </Group>
    </Paper>
  );
};

export default UtilisationDesMedicaments;
