"use client";
import { useState } from "react";
import { SegmentedControl } from "@mantine/core";
import React from "react";
import { <PERSON><PERSON>, rem, <PERSON><PERSON> } from "@mantine/core";
import { IconExternalLink } from "@tabler/icons-react";
import { SearchForm } from "@/layout/SearchForm";
import Icon from '@mdi/react';
import { mdiFileDownloadOutline ,mdiPlusCircle,mdiFilterVariant} from '@mdi/js';
//import { File, ListFilter, PlusCircle } from "lucide-react";
// import CetteJournee from "./overview/CetteJournee";
import CetteJournee from "./overview/CetteJournee";
import LaSemaine from "./overview/LaSemaine";
import LeMois from "./overview/LeMois";
import "@/styles/tab.css";
import { Center } from "@mantine/core";
import {
  IconCalendar,
  IconCalendarWeek,
  IconCalendarMonth,
} from "@tabler/icons-react";
type SectionType = "jour" | "semaine" | "mois";
const tabs = {
  jour: <CetteJournee />,
  semaine: <LaSemaine />,
  mois: <LeMois />,
};
const CalendarPatient = () => {
  const [section, setSection] = useState<SectionType>("jour");
  const handleSectionChange = (value: string) => {
    if (value === "jour" || value === "semaine" || value === "mois") {
      setSection(value);
    }
  };
  return (
    <>
      <div className="w-full">
        <div className="flex items-center rounded-sm bg-[var(--content-background)] p-1 pt-0">
          <div style={{width: "460px"}}>
          <SegmentedControl
            color="#3799CE"
            // className="w-[50%]"
            value={section}
            onChange={handleSectionChange}
            transitionTimingFunction="ease"
            fullWidth
            data={[
              {
                value: "jour",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendar style={{ width: rem(16), height: rem(16) }} />
                    <span>Jour</span>
                  </Center>
                ),
              },
              {
                value: "semaine",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendarWeek
                      style={{ width: rem(16), height: rem(16) }}
                    />
                    <span>Semaine</span>
                  </Center>
                ),
              },
              {
                value: "mois",
                label: (
                  <Center style={{ gap: 10 }}>
                    <IconCalendarMonth
                      style={{ width: rem(16), height: rem(16) }}
                    />
                    <span>Mois</span>
                  </Center>
                ),
              },
            ]}
          />
          </div>
          <div className="ml-auto flex items-center gap-2 pb-1">
            <SearchForm />
          </div>
          <div className="ml-auto flex items-center gap-2">
            <Menu
              width={200}
              shadow="md"
              transitionProps={{ transition: "rotate-right", duration: 150 }}
            >
              <Menu.Target>
                <button
                  className="ring-offset-background focus-visible:ring-ring bg-background hover:text-accent-foreground border-base-300 hover:bg-accent inline-flex h-8 items-center justify-center gap-1 whitespace-nowrap rounded-md border px-3 text-sm font-medium uppercase tracking-wider transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                  tabIndex={0}
                  role="button"
                >
                  <Icon path={mdiFilterVariant} size={1} className="h-3.5 w-3.5" />
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                    Filtre
                  </span>
                </button>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Item
                  leftSection={
                    <IconExternalLink
                      style={{ width: rem(14), height: rem(14) }}
                    />
                  }
                  component="a"
                  href="https://mantine.dev"
                  target="_blank"
                >
                  Les trois derniers jours
                </Menu.Item>
                <Menu.Item
                  leftSection={
                    <IconExternalLink
                      style={{ width: rem(14), height: rem(14) }}
                    />
                  }
                  component="a"
                  href="https://mantine.dev"
                  target="_blank"
                >
                  La semaine dernière
                </Menu.Item>
                <Menu.Item
                  leftSection={
                    <IconExternalLink
                      style={{ width: rem(14), height: rem(14) }}
                    />
                  }
                  component="a"
                  href="https://mantine.dev"
                  target="_blank"
                >
                  Archived
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>

            <button className="ring-offset-background focus-visible:ring-ring bg-background hover:text-accent-foreground border-base-300 hover:bg-accent inline-flex h-8 items-center justify-center gap-1 whitespace-nowrap rounded-md border px-3 text-sm font-medium uppercase tracking-wider transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
             <Icon path={mdiFileDownloadOutline} size={1} className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                Exporter
              </span>
            </button>
            <Button
             leftSection={<Icon path={mdiPlusCircle} size={1}  className="h-3.5 w-3.5" />}
              className="ButtonHover" 
            >
              Ajouter des patients
            </Button>
          </div>
        </div>
        <div className="overflow-hidden">
          {tabs[section] || <div>No component found</div>}
        </div>
      </div>
    </>
  );
};

export default CalendarPatient;
