// frontend/dental_medicine/src/services/completePatientService.ts

import api from '../lib/api';
import { notifications } from '@mantine/notifications';

// Types pour la gestion d'erreurs
export interface ApiError {
  response?: {
    status: number;
    data: {
      detail?: string;
      message?: string;
      [key: string]: unknown;
    };
  };
  message: string;
  name: string;
}

// Types pour les rendez-vous
export interface AppointmentData {
  appointment_date: string; // ISO date string
  appointment_time: string; // HH:MM format
  duration_minutes?: number;
  appointment_type: string;
  reason?: string;
  notes?: string;
  doctor_id?: string;
}

// Types pour le graphique dentaire
export interface ToothData {
  status: 'normal' | 'cavity' | 'filling' | 'crown' | 'missing' | 'root_canal' | 'implant' | 'bridge' | 'extraction' | 'other';
  procedures: ToothProcedure[];
  notes: string;
}

export interface ToothProcedure {
  date: string; // ISO date string
  type: string;
  description: string;
  performed_by: string;
}

export interface TeethChart {
  adult: Record<string, ToothData>; // Keys are tooth numbers 1-32
  child?: Record<string, ToothData>; // Keys are tooth numbers 1-20 (optional)
  last_updated: string; // ISO date string
  extended_info?: Record<string, unknown>; // For additional custom data
}

// Utility functions for TeethChart
export class TeethChartUtils {
  /**
   * Initialize a new teeth chart
   */
  static initializeTeethChart(): TeethChart {
    const adult: Record<string, ToothData> = {};

    // Initialize adult teeth (1-32)
    for (let i = 1; i <= 32; i++) {
      adult[i.toString()] = {
        status: 'normal',
        procedures: [],
        notes: ''
      };
    }

    return {
      adult,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Update a specific tooth status
   */
  static updateToothStatus(
    chart: TeethChart,
    toothNumber: number,
    status: ToothData['status'],
    notes?: string
  ): TeethChart {
    const updatedChart = { ...chart };
    const toothKey = toothNumber.toString();

    if (updatedChart.adult[toothKey]) {
      updatedChart.adult[toothKey] = {
        ...updatedChart.adult[toothKey],
        status,
        notes: notes || updatedChart.adult[toothKey].notes
      };
      updatedChart.last_updated = new Date().toISOString();
    }

    return updatedChart;
  }

  /**
   * Add a procedure to a tooth
   */
  static addToothProcedure(
    chart: TeethChart,
    toothNumber: number,
    procedure: Omit<ToothProcedure, 'date'>
  ): TeethChart {
    const updatedChart = { ...chart };
    const toothKey = toothNumber.toString();

    if (updatedChart.adult[toothKey]) {
      const newProcedure: ToothProcedure = {
        ...procedure,
        date: new Date().toISOString()
      };

      updatedChart.adult[toothKey] = {
        ...updatedChart.adult[toothKey],
        procedures: [...updatedChart.adult[toothKey].procedures, newProcedure]
      };
      updatedChart.last_updated = new Date().toISOString();
    }

    return updatedChart;
  }

  /**
   * Get teeth by status
   */
  static getTeethByStatus(chart: TeethChart, status: ToothData['status']): number[] {
    const result: number[] = [];

    Object.entries(chart.adult).forEach(([toothNumber, toothData]) => {
      if (toothData.status === status) {
        result.push(parseInt(toothNumber));
      }
    });

    return result.sort((a, b) => a - b);
  }

  /**
   * Get teeth that need attention (not normal)
   */
  static getTeethNeedingAttention(chart: TeethChart): Record<ToothData['status'], number[]> {
    const result: Record<string, number[]> = {};

    Object.entries(chart.adult).forEach(([toothNumber, toothData]) => {
      if (toothData.status !== 'normal') {
        if (!result[toothData.status]) {
          result[toothData.status] = [];
        }
        result[toothData.status].push(parseInt(toothNumber));
      }
    });

    // Sort each array
    Object.keys(result).forEach(status => {
      result[status].sort((a, b) => a - b);
    });

    return result as Record<ToothData['status'], number[]>;
  }
}

// Interface pour les données complètes du patient
export interface CompletePatientData {
  // Informations personnelles
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: Date | null;
  age: string;
  gender: string;
  nationalIdNumber: string;
  passportNumber: string;
  maritalStatus: string;
  
  // Adresse
  address: string;
  city: string;
  state: string;
  zipCode: string;
  
  // Informations médicales
  medicalHistory: string;
  allergies: string;
  
  // Assurance
  insuranceCompany: string;
  insurancePolicyNumber: string;
  
  // Contact d'urgence
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  
  // Informations de visite
  doctorAssigned: string;
  visitType: string;
  visitDuration: string;
  agenda: string;
  comments: string;
  diagnosticRoom: string;
  additionalNotes: string;
  
  // Rendez-vous
  appointment_date: Date;
  duration: number;
  resourceId: number;
  consultation_type: string;
}

// Interface pour la réponse du backend
export interface PatientCreationResponse {
  patient: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    date_of_birth: string;
    gender: string;
    address: string;
    city: string;
    postal_code: string;
    created_at: string;
    updated_at: string;
  };
  dentistry_profile: {
    id: string;
    dental_history: string;
    dental_insurance_provider: string;
    dental_insurance_policy_number: string;
    teeth_chart: TeethChart;
    created_at: string;
    updated_at: string;
  };
  appointment?: {
    id: string;
    start_time: string;
    end_time: string;
    consultation_type: string;
    status: string;
    resource_id: number;
    created_at: string;
  };
}

// Interface pour les médecins
export interface Doctor {
  id: string;
  first_name: string;
  last_name: string;
  specialization: string;
  is_assistant: boolean;
}

/**
 * Service pour la gestion des patients complets avec intégration backend
 */
class CompletePatientService {
  private readonly baseURL = '/api/dentistry';

  /**
   * Créer un patient complet avec toutes les informations
   */
  async createCompletePatient(patientData: CompletePatientData): Promise<PatientCreationResponse> {
    try {
      console.log('🚀 Création du patient complet:', patientData);

      // Préparer les données pour le backend
      const backendData = this.transformToBackendFormat(patientData);

      // Appel API pour créer le patient
      const response = await api.post(`${this.baseURL}/patients/complete/`, backendData);

      console.log('✅ Patient créé avec succès:', response.data);
      
      // Notification de succès
      notifications.show({
        title: 'Patient créé',
        message: `Patient ${patientData.firstName} ${patientData.lastName} créé avec succès`,
        color: 'green',
        autoClose: 3000,
      });

      return response.data;
    } catch (error: unknown) {
      const apiError = error as ApiError;
      console.error('❌ Erreur lors de la création du patient:', apiError);

      // Gestion des erreurs spécifiques
      if (apiError.response?.status === 400) {
        const errorMessage = this.extractErrorMessage(apiError.response.data);
        notifications.show({
          title: 'Erreur de validation',
          message: errorMessage,
          color: 'red',
          autoClose: 5000,
        });
      } else if (apiError.response?.status === 409) {
        notifications.show({
          title: 'Patient existant',
          message: 'Un patient avec cet email existe déjà',
          color: 'orange',
          autoClose: 5000,
        });
      } else {
        notifications.show({
          title: 'Erreur système',
          message: 'Impossible de créer le patient. Veuillez réessayer.',
          color: 'red',
          autoClose: 5000,
        });
      }

      throw error;
    }
  }

  /**
   * Récupérer la liste des médecins disponibles
   */
  async getDoctors(): Promise<Doctor[]> {
    try {
      const response = await api.get(`${this.baseURL}/doctors/`);
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des médecins:', error);
      
      // Retourner des données mock en cas d'erreur
      return this.getMockDoctors();
    }
  }

  /**
   * Valider les données du patient avant soumission
   */
  validatePatientData(patientData: CompletePatientData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validation des champs obligatoires
    if (!patientData.firstName.trim()) {
      errors.push('Le prénom est obligatoire');
    }
    if (!patientData.lastName.trim()) {
      errors.push('Le nom est obligatoire');
    }
    if (!patientData.email.trim()) {
      errors.push('L\'email est obligatoire');
    }
    if (!patientData.phone.trim()) {
      errors.push('Le téléphone est obligatoire');
    }
    if (!patientData.dateOfBirth) {
      errors.push('La date de naissance est obligatoire');
    }
    if (!patientData.gender) {
      errors.push('Le genre est obligatoire');
    }
    if (!patientData.visitType) {
      errors.push('Le type de visite est obligatoire');
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (patientData.email && !emailRegex.test(patientData.email)) {
      errors.push('Format d\'email invalide');
    }

    // Validation du téléphone
    if (patientData.phone && patientData.phone.length < 10) {
      errors.push('Numéro de téléphone invalide');
    }

    // Validation de l'âge
    if (patientData.dateOfBirth) {
      const age = this.calculateAge(patientData.dateOfBirth);
      if (age < 0 || age > 150) {
        errors.push('Date de naissance invalide');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Transformer les données du frontend vers le format backend
   */
  private transformToBackendFormat(patientData: CompletePatientData) {
    return {
      // Données utilisateur de base
      user_data: {
        first_name: patientData.firstName,
        last_name: patientData.lastName,
        email: patientData.email,
        phone: patientData.phone,
        date_of_birth: patientData.dateOfBirth ? 
          patientData.dateOfBirth.toISOString().split('T')[0] : null,
        gender: patientData.gender,
        address: patientData.address,
        city: patientData.city,
        state: patientData.state,
        postal_code: patientData.zipCode,
        user_type: 'patient'
      },

      // Profil dentaire spécialisé
      dentistry_profile: {
        dental_history: patientData.medicalHistory,
        dental_insurance_provider: patientData.insuranceCompany,
        dental_insurance_policy_number: patientData.insurancePolicyNumber,
        allergies: patientData.allergies,
        emergency_contact_name: patientData.emergencyContactName,
        emergency_contact_phone: patientData.emergencyContactPhone,
        emergency_contact_relationship: patientData.emergencyContactRelationship,
        national_id_number: patientData.nationalIdNumber,
        passport_number: patientData.passportNumber,
        marital_status: patientData.maritalStatus,
        additional_notes: patientData.additionalNotes
      },

      // Informations de visite
      visit_info: {
        doctor_assigned: patientData.doctorAssigned,
        visit_type: patientData.visitType,
        visit_duration: patientData.visitDuration,
        agenda: patientData.agenda,
        comments: patientData.comments,
        diagnostic_room: patientData.diagnosticRoom
      },

      // Rendez-vous
      appointment_data: {
        start_time: patientData.appointment_date.toISOString(),
        end_time: new Date(
          patientData.appointment_date.getTime() + (patientData.duration * 60000)
        ).toISOString(),
        consultation_type: patientData.consultation_type,
        resource_id: patientData.resourceId,
        status: 'scheduled',
        notes: patientData.comments
      }
    };
  }

  /**
   * Extraire le message d'erreur du backend
   */
  private extractErrorMessage(errorData: unknown): string {
    if (typeof errorData === 'string') {
      return errorData;
    }

    // Type guard pour vérifier si errorData est un objet
    if (errorData && typeof errorData === 'object') {
      const errorObj = errorData as Record<string, unknown>;

      if (errorObj.detail && typeof errorObj.detail === 'string') {
        return errorObj.detail;
      }

      if (errorObj.message && typeof errorObj.message === 'string') {
        return errorObj.message;
      }

      // Extraire les erreurs de validation
      const errors: string[] = [];
      Object.keys(errorObj).forEach(field => {
        const fieldValue = errorObj[field];
        if (Array.isArray(fieldValue)) {
          errors.push(`${field}: ${fieldValue.join(', ')}`);
        } else if (typeof fieldValue === 'string') {
          errors.push(`${field}: ${fieldValue}`);
        } else if (fieldValue) {
          errors.push(`${field}: ${String(fieldValue)}`);
        }
      });

      return errors.length > 0 ? errors.join('; ') : 'Erreur de validation';
    }

    return 'Erreur de validation';
  }

  /**
   * Calculer l'âge basé sur la date de naissance
   */
  private calculateAge(dateOfBirth: Date): number {
    const today = new Date();
    let age = today.getFullYear() - dateOfBirth.getFullYear();
    const monthDiff = today.getMonth() - dateOfBirth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Données mock pour les médecins en cas d'erreur API
   */
  private getMockDoctors(): Doctor[] {
    return [
      {
        id: 'doctor-1',
        first_name: 'Dr. Jean',
        last_name: 'Dupont',
        specialization: 'Dentiste généraliste',
        is_assistant: false
      },
      {
        id: 'assistant-1',
        first_name: 'Dr. Marie',
        last_name: 'Martin',
        specialization: 'Assistant dentaire',
        is_assistant: true
      },
      {
        id: 'assistant-2',
        first_name: 'Dr. Pierre',
        last_name: 'Durand',
        specialization: 'Assistant dentaire',
        is_assistant: true
      }
    ];
  }

  /**
   * Créer un rendez-vous séparé (si nécessaire)
   */
  async createAppointment(patientId: string, appointmentData: AppointmentData) {
    try {
      const response = await api.post(`${this.baseURL}/appointments/`, {
        patient_id: patientId,
        ...appointmentData
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du rendez-vous:', error);
      throw error;
    }
  }

  /**
   * Mettre à jour les informations d'un patient
   */
  async updatePatient(patientId: string, updateData: Partial<CompletePatientData>) {
    try {
      const backendData = this.transformToBackendFormat(updateData as CompletePatientData);
      const response = await api.patch(`${this.baseURL}/patients/${patientId}/`, backendData);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du patient:', error);
      throw error;
    }
  }
}

// Export de l'instance du service
export const completePatientService = new CompletePatientService();
export default completePatientService;
