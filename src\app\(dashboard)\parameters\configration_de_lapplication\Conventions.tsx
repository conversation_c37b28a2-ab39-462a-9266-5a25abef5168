import React, { useState } from 'react';
import {
  Button,
  Card,
  Group,
  Text,
  TextInput,
  Select,
  Checkbox,
  NumberInput,
  Tabs,
  Divider,
} from '@mantine/core';
import {
  IconPlus,
  IconFileText,
  IconList,
} from '@tabler/icons-react';

// Types pour les données des conventions

interface ConventionFormData {
  dateDebut: string;
  dateFin: string;
  titre: string;
  conventionCadre: string;
  tmPourcentage: number | '';
  tpPourcentage: number | '';
  plafondRemboursement: number | '';
  conditionPaiement: string;
  activer: boolean;
}

const Conventions = () => {
  // États pour la gestion des données
  const [activeTab, setActiveTab] = useState<string | null>('liste');
  const [formData, setFormData] = useState<ConventionFormData>({
    dateDebut: '',
    dateFin: '',
    titre: '',
    conventionCadre: '',
    tmPourcentage: '',
    tpPourcentage: '',
    plafondRemboursement: '',
    conditionPaiement: '',
    activer: false,
  });

  // Données d'exemple pour les dropdowns
  const conventionsCadre = [
    { value: 'convention1', label: 'Convention Standard' },
    { value: 'convention2', label: 'Convention Spéciale' },
    { value: 'convention3', label: 'Convention Premium' },
  ];

  const conditionsPaiement = [
    { value: 'immediat', label: 'Paiement immédiat' },
    { value: '30jours', label: '30 jours' },
    { value: '60jours', label: '60 jours' },
    { value: '90jours', label: '90 jours' },
  ];

  // Fonction pour vérifier si le formulaire est valide
  const isFormValid = () => {
    return (
      formData.dateDebut.trim() !== '' &&
      formData.titre.trim() !== '' &&
      formData.tmPourcentage !== '' &&
      formData.tpPourcentage !== ''
    );
  };

  // Fonction de sauvegarde
  const handleSave = () => {
    if (isFormValid()) {
      console.log('Sauvegarde des données:', formData);
      // Logique de sauvegarde ici
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <IconFileText size={24} className="text-blue-600" />
            </div>
            <div>
              <Text size="xl" fw={600} className="text-gray-900">
                Gestion des conventions
              </Text>
            </div>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            size="sm"
          >
            Ajouter
          </Button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 p-6">
        <Card shadow="sm" padding="lg" radius="md" className="bg-white">
          {/* Onglets */}
          <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
            <Tabs.List>
              <Tabs.Tab
                value="liste"
                leftSection={<IconList size={16} />}
                className="text-blue-600 border-b-2 border-blue-600"
              >
                Liste des conventions
              </Tabs.Tab>
            </Tabs.List>
          </Tabs>

          {/* Formulaire */}
          <div className="space-y-6">
            {/* Première ligne - Dates et Activer */}
            <div className="flex items-start justify-between">
              <Group grow className="flex-1 mr-8">
                <TextInput
                  label={
                    <span>
                      Date début <span className="text-red-500">*</span>
                    </span>
                  }
                  placeholder=""
                  type="date"
                  value={formData.dateDebut}
                  onChange={(e) => setFormData({ ...formData, dateDebut: e.target.value })}
                  required
                  size="sm"
                  styles={{
                    label: {
                      color: '#dc2626',
                      fontWeight: 500,
                    }
                  }}
                />
                <TextInput
                  label="Date Fin"
                  placeholder=""
                  type="date"
                  value={formData.dateFin}
                  onChange={(e) => setFormData({ ...formData, dateFin: e.target.value })}
                  size="sm"
                />
              </Group>
              <Checkbox
                label="Activer"
                checked={formData.activer}
                onChange={(event) => setFormData({ ...formData, activer: event.currentTarget.checked })}
                size="sm"
                className="mt-6"
              />
            </div>

            {/* Deuxième ligne - Titre et Convention cadre */}
            <Group grow>
              <TextInput
                label={
                  <span>
                    Titre <span className="text-red-500">*</span>
                  </span>
                }
                placeholder=""
                value={formData.titre}
                onChange={(e) => setFormData({ ...formData, titre: e.target.value })}
                required
                size="sm"
              />
              <Select
                label="Convention cadre"
                placeholder="Sélectionner une convention"
                value={formData.conventionCadre}
                onChange={(value) => setFormData({ ...formData, conventionCadre: value || '' })}
                data={conventionsCadre}
                size="sm"
              />
            </Group>

            {/* Troisième ligne - TM % et TP % */}
            <Group grow>
              <NumberInput
                label={
                  <span>
                    TM % <span className="text-red-500">*</span>
                  </span>
                }
                placeholder=""
                value={formData.tmPourcentage}
                onChange={(value) => setFormData({ ...formData, tmPourcentage: value || '' })}
                min={0}
                max={100}
                suffix="%"
                required
                size="sm"
              />
              <NumberInput
                label={
                  <span>
                    TP % <span className="text-red-500">*</span>
                  </span>
                }
                placeholder=""
                value={formData.tpPourcentage}
                onChange={(value) => setFormData({ ...formData, tpPourcentage: value || '' })}
                min={0}
                max={100}
                suffix="%"
                required
                size="sm"
              />
            </Group>

            {/* Quatrième ligne - Plafond et Condition de paiement */}
            <Group grow>
              <NumberInput
                label="Plafond de remboursement(Montant)"
                placeholder=""
                value={formData.plafondRemboursement}
                onChange={(value) => setFormData({ ...formData, plafondRemboursement: value || '' })}
                min={0}
                size="sm"
              />
              <Select
                label="Condition de paiement"
                placeholder="Sélectionner une condition"
                value={formData.conditionPaiement}
                onChange={(value) => setFormData({ ...formData, conditionPaiement: value || '' })}
                data={conditionsPaiement}
                size="sm"
              />
            </Group>

            {/* Divider */}
            <Divider className="my-6" />

            {/* Section Liste des exceptions */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <Text size="lg" fw={500} className="text-gray-900">
                  Liste des exceptions
                </Text>
                <Button
                  leftSection={<IconPlus size={16} />}
                  variant="outline"
                  size="sm"
                  className="border-blue-500 text-blue-500 hover:bg-blue-50"
                >
                  Exception
                </Button>
              </div>

              {/* Zone vide pour les exceptions */}
              <div className="border border-gray-200 rounded-lg p-8 text-center bg-gray-50">
                <Text size="sm" className="text-gray-500">
                  Aucune exception ajoutée
                </Text>
              </div>
            </div>

            {/* Bouton Enregistrer */}
            <div className="flex justify-end pt-6">
              <Button
                onClick={handleSave}
                disabled={!isFormValid()}
                size="sm"
                className={`${
                  isFormValid()
                    ? 'bg-blue-500 hover:bg-blue-600 text-white'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Enregistrer
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Conventions;
