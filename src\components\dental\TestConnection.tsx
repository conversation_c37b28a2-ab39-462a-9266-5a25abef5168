'use client';

import React, { useState } from 'react';
import {
  Card,
  Text,
  Button,
  Group,
  Stack,
  Badge,
  Alert,
  Code,
  Title,
} from '@mantine/core';
import {
  IconPlugConnected,
  IconExternalLink,
  IconCheck,
  IconX,
  IconLoader,
} from '@tabler/icons-react';
import { testBackendConnection } from '../../../utils/testBackendConnection';
import { toothService } from '../../../services/toothService';

const TestConnection: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleTest = async () => {
    setTesting(true);
    setResult(null);
    
    try {
      const testResult = await testBackendConnection();
      setResult(testResult);
    } catch (error) {
      setResult({
        success: false,
        message: 'Test failed',
        error: error
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <Stack gap="md">
      <Card withBorder p="md">
        <Title order={4} mb="md">Test de Connexion Backend</Title>
        
        <Group justify="space-between" mb="md">
          <div>
            <Text size="sm" c="dimmed">
              Testez la connexion entre le frontend et le backend Django
            </Text>
            <Code size="xs">http://127.0.0.1:8000/admin/dentistry/tooth/</Code>
          </div>
          <Group>
            <Button
              leftSection={testing ? <IconLoader size={16} /> : <IconPlugConnected size={16} />}
              onClick={handleTest}
              loading={testing}
              variant="filled"
            >
              {testing ? 'Test en cours...' : 'Tester Connexion'}
            </Button>
            <Button
              leftSection={<IconExternalLink size={16} />}
              onClick={toothService.openAdminPanel}
              variant="light"
            >
              Ouvrir Admin
            </Button>
          </Group>
        </Group>

        {result && (
          <Alert
            color={result.success ? 'green' : 'red'}
            title={result.success ? 'Connexion Réussie' : 'Connexion Échouée'}
            icon={result.success ? <IconCheck size={16} /> : <IconX size={16} />}
          >
            <Stack gap="xs">
              <Text size="sm">{result.message}</Text>
              
              {result.success && result.data && (
                <div>
                  <Text size="xs" c="dimmed">
                    Données reçues: {result.data.count || result.data.results?.length || 0} enregistrements
                  </Text>
                  {result.data.results && result.data.results.length > 0 && (
                    <Code block size="xs" mt="xs">
                      {JSON.stringify(result.data.results[0], null, 2)}
                    </Code>
                  )}
                </div>
              )}
              
              {!result.success && result.error && (
                <Code block size="xs" c="red">
                  {result.error.toString()}
                </Code>
              )}
            </Stack>
          </Alert>
        )}
      </Card>

      <Card withBorder p="md">
        <Title order={5} mb="md">Informations de Connexion</Title>
        <Stack gap="xs">
          <Group justify="space-between">
            <Text size="sm">Frontend URL:</Text>
            <Badge variant="light">http://localhost:3001/dental</Badge>
          </Group>
          <Group justify="space-between">
            <Text size="sm">Backend API:</Text>
            <Badge variant="light">http://127.0.0.1:8000/api/teeth/</Badge>
          </Group>
          <Group justify="space-between">
            <Text size="sm">Admin Panel:</Text>
            <Badge variant="light">http://127.0.0.1:8000/admin/dentistry/tooth/</Badge>
          </Group>
        </Stack>
      </Card>
    </Stack>
  );
};

export default TestConnection;
