'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Paper,
  Title,
  Text,
  Group,
  Avatar,
  Button,
  Loader,
  Stack,
  Tabs,
  TextInput,
  ScrollArea,

  ActionIcon,
  Badge,
 
  Card,
  Menu,
  Modal,
  Select
} from '@mantine/core';
import { IconSend, IconDotsVertical, IconPlus, IconSearch, IconCheck, } from '@tabler/icons-react';
import messagingService, { Conversation, Message } from '@/services/messagingService';
import { formatDistanceToNow } from 'date-fns';

interface SpecialtyMessagingProps {
  specialtyId?: string;
  currentDoctorId?: string;
  currentDoctorName?: string;
}

export default function SpecialtyMessaging({
  specialtyId,
  currentDoctorId = '1', // Default for mock data
  
}: SpecialtyMessagingProps) {
  const [loading, setLoading] = useState(true);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [newConversationModalOpen, setNewConversationModalOpen] = useState(false);
  const [availableDoctors, setAvailableDoctors] = useState<{ id: string; name: string }[]>([]);
  const [selectedDoctor, setSelectedDoctor] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversations on component mount
  useEffect(() => {
    const fetchConversations = async () => {
      try {
        setLoading(true);
        const data = await messagingService.getConversations(specialtyId);
        setConversations(data);
        
        // If there are conversations, set the first one as active
        if (data.length > 0) {
          setActiveConversation(data[0].id);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();
  }, [specialtyId]);

  // Fetch messages when active conversation changes
  useEffect(() => {
    const fetchMessages = async () => {
      if (activeConversation) {
        try {
          const data = await messagingService.getMessages(activeConversation);
          setMessages(data);
          
          // Mark messages as read
          await messagingService.markAsRead(activeConversation);
          
          // Update unread count in conversations
          setConversations(prev => 
            prev.map(conv => 
              conv.id === activeConversation 
                ? { ...conv, unread_count: 0 } 
                : conv
            )
          );
        } catch (error) {
          console.error('Error fetching messages:', error);
        }
      }
    };

    fetchMessages();
  }, [activeConversation]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a new message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !activeConversation) return;

    const conversation = conversations.find(c => c.id === activeConversation);
    if (!conversation) return;

    try {
      const sentMessage = await messagingService.sendMessage(
        conversation.participant_id, 
        newMessage,
        specialtyId
      );
      
      if (sentMessage) {
        setMessages(prev => [...prev, sentMessage]);
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Handle opening new conversation modal
  const handleNewConversation = async () => {
    try {
      if (specialtyId) {
        const doctors = await messagingService.getDoctorsInSpecialty(specialtyId);
        
        // Filter out doctors that already have conversations
        const existingDoctorIds = conversations.map(c => c.participant_id);
        const availableDocs = doctors.filter(d => !existingDoctorIds.includes(d.id));
        
        setAvailableDoctors(availableDocs);
        setNewConversationModalOpen(true);
      }
    } catch (error) {
      console.error('Error fetching available doctors:', error);
    }
  };

  // Handle creating a new conversation
  const handleCreateConversation = async () => {
    if (!selectedDoctor) return;
    
    try {
      // Send an initial message to create the conversation
      const doctor = availableDoctors.find(d => d.id === selectedDoctor);
      if (!doctor) return;
      
      const initialMessage = `Hello Dr. ${doctor.name.split(' ')[1]}, I'd like to connect with you.`;
      const sentMessage = await messagingService.sendMessage(selectedDoctor, initialMessage, specialtyId);
      
      if (sentMessage) {
        // Refresh conversations
        const updatedConversations = await messagingService.getConversations(specialtyId);
        setConversations(updatedConversations);
        
        // Find and set the new conversation as active
        const newConversation = updatedConversations.find(c => c.participant_id === selectedDoctor);
        if (newConversation) {
          setActiveConversation(newConversation.id);
        }
        
        // Close modal and reset selection
        setNewConversationModalOpen(false);
        setSelectedDoctor(null);
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  };

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(conv => 
    conv.participant_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <Paper p="md" withBorder>
        <Group justify="center" py="xl">
          <Loader size="md" />
          <Text>Loading messages...</Text>
        </Group>
      </Paper>
    );
  }

  return (
    <Paper p="md" withBorder>
      <Title order={4} mb="md">Specialty Messaging</Title>
      <Text size="sm" color="dimmed" mb="md">
        Communicate with other doctors in your specialty for collaboration and consultation.
      </Text>
      
      <Card withBorder p={0} style={{ overflow: 'hidden' }}>
        <Tabs defaultValue="messages">
          <Tabs.List>
            <Tabs.Tab value="messages">Messages</Tabs.Tab>
          </Tabs.List>
          
          <Tabs.Panel value="messages">
            <Group justify="space-between" p="md" style={{ borderBottom: '1px solid #eee' }}>
              <TextInput
                placeholder="Search conversations"
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.currentTarget.value)}
                style={{ flexGrow: 1 }}
              />
              <ActionIcon color="blue" onClick={handleNewConversation}>
                <IconPlus size={20} />
              </ActionIcon>
            </Group>
            
            <div style={{ display: 'flex', height: '500px' }}>
              {/* Conversations list */}
              <div style={{ width: '300px', borderRight: '1px solid #eee' }}>
                <ScrollArea h={500}>
                  {filteredConversations.length === 0 ? (
                    <Text ta="center" color="dimmed" py="xl">
                      No conversations found
                    </Text>
                  ) : (
                    filteredConversations.map(conv => (
                      <div 
                        key={conv.id}
                        style={{ 
                          padding: '10px', 
                          borderBottom: '1px solid #eee',
                          backgroundColor: activeConversation === conv.id ? '#f8f9fa' : 'transparent',
                          cursor: 'pointer'
                        }}
                        onClick={() => setActiveConversation(conv.id)}
                      >
                        <Group justify="space-between">
                          <Group>
                            <Avatar color="blue" radius="xl">
                              {conv.participant_name.charAt(0)}
                            </Avatar>
                            <div>
                              <Group gap={5}>
                                <Text size="sm" fw={500}>{conv.participant_name}</Text>
                                {conv.unread_count > 0 && (
                                  <Badge size="xs" color="red">{conv.unread_count}</Badge>
                                )}
                              </Group>
                              {conv.last_message && (
                                <Text size="xs" c="dimmed" lineClamp={1}>
                                  {conv.last_message}
                                </Text>
                              )}
                            </div>
                          </Group>
                          {conv.last_message_time && (
                            <Text size="xs" c="dimmed">
                              {formatDistanceToNow(new Date(conv.last_message_time), { addSuffix: true })}
                            </Text>
                          )}
                        </Group>
                      </div>
                    ))
                  )}
                </ScrollArea>
              </div>
              
              {/* Message area */}
              <div style={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                {activeConversation ? (
                  <>
                    {/* Conversation header */}
                    <div style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color="blue" radius="xl">
                            {conversations.find(c => c.id === activeConversation)?.participant_name.charAt(0)}
                          </Avatar>
                          <Text fw={500}>
                            {conversations.find(c => c.id === activeConversation)?.participant_name}
                          </Text>
                        </Group>
                        <Menu position="bottom-end">
                          <Menu.Target>
                            <ActionIcon>
                              <IconDotsVertical size={16} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item>View Profile</Menu.Item>
                            <Menu.Item color="red">Clear Conversation</Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                    </div>
                    
                    {/* Messages */}
                    <ScrollArea style={{ flexGrow: 1, height: '380px', padding: '10px' }}>
                      {messages.length === 0 ? (
                        <Text ta="center" color="dimmed" py="xl">
                          No messages yet. Start the conversation!
                        </Text>
                      ) : (
                        messages.map(msg => (
                          <div
                            key={msg.id}
                            style={{
                              marginBottom: '10px',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: msg.sender_id === currentDoctorId ? 'flex-end' : 'flex-start'
                            }}
                          >
                            <div
                              style={{
                                backgroundColor: msg.sender_id === currentDoctorId ? '#e3f2fd' : '#f5f5f5',
                                padding: '8px 12px',
                                borderRadius: '12px',
                                maxWidth: '70%'
                              }}
                            >
                              <Text size="sm">{msg.content}</Text>
                            </div>
                            <Text size="xs" color="dimmed" mt={4}>
                              {formatDistanceToNow(new Date(msg.created_at), { addSuffix: true })}
                              {msg.sender_id === currentDoctorId && (
                                <span style={{ marginLeft: '5px' }}>
                                  {msg.read ? (
                                    <IconCheck size={12} style={{ display: 'inline', verticalAlign: 'middle' }} />
                                  ) : (
                                    <span>Sent</span>
                                  )}
                                </span>
                              )}
                            </Text>
                          </div>
                        ))
                      )}
                      <div ref={messagesEndRef} />
                    </ScrollArea>
                    
                    {/* Message input */}
                    <div style={{ padding: '10px', borderTop: '1px solid #eee' }}>
                      <Group gap={8}>
                        <TextInput
                          placeholder="Type a message..."
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.currentTarget.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSendMessage();
                            }
                          }}
                          style={{ flexGrow: 1 }}
                        />
                        <ActionIcon color="blue" onClick={handleSendMessage} disabled={!newMessage.trim()}>
                          <IconSend size={16} />
                        </ActionIcon>
                      </Group>
                    </div>
                  </>
                ) : (
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <Stack align="center" gap="md">
                      <Text color="dimmed">Select a conversation or start a new one</Text>
                      <Button leftSection={<IconPlus size={16} />} onClick={handleNewConversation}>
                        New Conversation
                      </Button>
                    </Stack>
                  </div>
                )}
              </div>
            </div>
          </Tabs.Panel>
        </Tabs>
      </Card>
      
      {/* New conversation modal */}
      <Modal
        opened={newConversationModalOpen}
        onClose={() => setNewConversationModalOpen(false)}
        title="Start a New Conversation"
      >
        <Stack>
          <Text size="sm">
            Select a doctor from your specialty to start a conversation:
          </Text>
          
          {availableDoctors.length === 0 ? (
            <Text color="dimmed">
              No available doctors found. You already have conversations with all doctors in your specialty.
            </Text>
          ) : (
            <Select
              label="Select Doctor"
              placeholder="Choose a doctor"
              data={availableDoctors.map(doc => ({ value: doc.id, label: doc.name }))}
              value={selectedDoctor}
              onChange={setSelectedDoctor}
            />
          )}
          
          <Group justify="space-between" mt="md">
            <Button variant="outline" onClick={() => setNewConversationModalOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateConversation} 
              disabled={!selectedDoctor || availableDoctors.length === 0}
            >
              Start Conversation
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Paper>
  );
}
