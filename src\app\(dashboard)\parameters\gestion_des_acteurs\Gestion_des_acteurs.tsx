import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';

import Liste_des_specialites from './Liste_des_specialites';
import Gestion_des_profiles from './Gestion_des_profiles';
import Liste_des_uitilisateurs from './Liste_des_uitilisateurs';
import Liste_des_contacts from './Liste_des_contacts';
import List_des_techniciens from './List_des_techniciens';



// Mapping des sous-onglets pour Vente
const subtabMapping: { [key: string]: string } = {
  'Liste_des_specialites': 'Liste_des_specialites',
  'Gestion-des_profiles': 'Gestion-des_profiles',
  'Liste_des_uitilisateurs': 'Liste_des_uitilisateurs',
  'Liste_des_contacts': 'Liste_des_contacts',
  'List_des_techniciens': 'List_des_techniciens',

};

const Gestion_des_acteurs = () => {
  const [activeTab, setActiveTab] = useState('Liste_des_specialites');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'Liste_des_specialites')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="Liste_des_specialites" leftSection={<IconPhoto size={12} />}>
             Liste des specialites
           </Tabs.Tab>
           <Tabs.Tab value="Gestion-des_profiles" leftSection={<IconMessageCircle size={12} />}>
             Gestion des profiles
           </Tabs.Tab>
           <Tabs.Tab value="Liste_des_uitilisateurs" leftSection={<IconSettings size={12} />}>
             Liste des utilisateurs
           </Tabs.Tab>
           <Tabs.Tab value="Liste_des_contacts" leftSection={<IconSettings size={12} />}>
             Liste des contacts
           </Tabs.Tab>
           <Tabs.Tab value="List_des_techniciens" leftSection={<IconSettings size={12} />}>
             Liste des techniciens
           </Tabs.Tab>
          
         </Tabs.List>
   
         <Tabs.Panel value="Liste_des_specialites" ml={20}>
           <Liste_des_specialites/>
         </Tabs.Panel>
   
         <Tabs.Panel value="Gestion-des_profiles" ml={20}>
           <Gestion_des_profiles/>
         </Tabs.Panel>
   
         <Tabs.Panel value="Liste_des_uitilisateurs" ml={20}>
           <Liste_des_uitilisateurs/>
         </Tabs.Panel>

         <Tabs.Panel value="Liste_des_contacts" ml={20}>
           <Liste_des_contacts/>
         </Tabs.Panel>

         <Tabs.Panel value="List_des_techniciens" ml={20}>
           <List_des_techniciens/>
         </Tabs.Panel>
        
       </Tabs>
  )
};



export default Gestion_des_acteurs
