"use client";
import { rem, <PERSON>, Divider, Textarea } from "@mantine/core";
import {
  IconStarFilled,
  IconPlane,
  IconSquareRoundedPlusFilled,
} from "@tabler/icons-react";

import { Group, Button, Box, Drawer, Text } from "@mantine/core";
import { Avatar } from "@mantine/core";
import { Select } from "@mantine/core";
import { FileInput } from "@mantine/core";
import { IconCamera } from "@tabler/icons-react";
import { Accordion } from "@mantine/core";
import { IconXboxX } from "@tabler/icons-react";
import { useDisclosure } from "@mantine/hooks";

const placeholder =
  "It can’t help but hear a pin drop from over half a mile away, so it lives deep in the mountains where there aren’t many people or Pokémon.It was born from sludge on the ocean floor. In a sterile environment, the germs within its body can’t multiply, and it dies.It has no eyeballs, so it can’t see. It checks its surroundings via the ultrasonic waves it emits from its mouth.";

const Overviews = () => {
  const icon = (
    <IconCamera style={{ width: rem(18), height: rem(18) }} stroke={1.5} />
  );
  const [opened, { open, close }] = useDisclosure(false);
  return (
    <>
      <div className="m-1 mt-2 flex bg-[var(--mantine-color-bg)]">
        <div className="w-3/12 bg-white pr-2">
          <div className="bg-[#F2F5F8] p-2">
            <div>
              {" "}
              <Box style={{ overflow: "hidden" }} mb={8}>
                <Box
                  maw={500}
                  p="md"
                  mx="auto"
                  bg="var(--mantine-color-blue-light)"
                >
                  <div className="my-4 flex flex-row">
                    {/* <Avatar src="/avatar/user.png" alt="it's me" /> */}
                    <span className="basis-1/6">
                      <Avatar radius="xl" />
                    </span>

                    <span className="basis-2/4">
                      {" "}
                      <Select
                        // label="Your favorite library"
                        placeholder="Select Avatar"
                        data={["React", "Angular", "Vue", "Svelte"]}
                        searchable
                      />
                    </span>

                    <span className="basis-1/10">
                      {" "}
                      <FileInput
                        leftSection={icon}
                        // label="Attach your CV"
                        // placeholder="Your CV"
                        leftSectionPointerEvents="none"
                        className="pl-2"
                      />
                    </span>

                    {/* <FileInput
                          rightSection={icon}
                          label="Attach your CV"
                          placeholder="Your CV"
                          rightSectionPointerEvents="none"
                          mt="md"
                        /> */}
                  </div>
                  <Group grow preventGrowOverflow={false} wrap="nowrap" mb={8}>
                    <Card withBorder padding="sm" radius="md">
                      <Group>
                        <div>
                          <Text fw={500}>Balance</Text>
                          <Text fz="xs" c="dimmed">
                            $0.00
                          </Text>
                        </div>
                      </Group>
                    </Card>
                    <Card withBorder padding="sm" radius="md">
                      <Group>
                        <div>
                          <Text fw={500}>Total debts</Text>
                          <Text fz="xs" c="dimmed">
                            $0.00
                          </Text>
                        </div>
                      </Group>
                    </Card>
                  </Group>
                  <Group grow preventGrowOverflow={false} wrap="nowrap" mb={8}>
                    <Button
                      leftSection={<IconStarFilled size={14} />}
                      variant="default"
                    >
                      Vip
                    </Button>

                    <Button
                      leftSection={<IconPlane size={14} />}
                      variant="default"
                    >
                      Foreigners
                    </Button>
                  </Group>
                  <Button
                    leftSection={<IconPlane size={14} />}
                    variant="default"
                    w={"100%"}
                  >
                    + Group
                  </Button>
                </Box>
              </Box>
            </div>
            <div>
              <Accordion variant="separated">
                <Accordion.Item
                  className={
                    "border-base-200 text-base-content mb-[var(--mantine-spacing-lg)] rounded-[var(--mantine-radius-md)] border-solid border-[rem(1px)]"
                  }
                  value="general"
                >
                  <Accordion.Control>
                    <Text fw={700}>General</Text>
                    <Text size="sm"></Text>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <div className="flex justify-between">
                      <div>
                        <Text fw={700}>Full Name</Text>
                        <Text size="sm">Pateint</Text>
                      </div>
                      <div>
                        <Group>
                          <Button variant="filled" color="teal">
                            M
                          </Button>
                          {/* default */}
                          <Button variant=" default">F </Button>
                          <Button variant=" default">E </Button>
                        </Group>
                      </div>
                    </div>
                    <Divider my="md" />
                    <Text fw={700}>Birthday:</Text>
                    <Text size="sm">MM/DD/YY</Text>
                    <Divider my="md" />
                    <Text fw={700}>Code:</Text>
                    <Text size="sm">12</Text>
                    <Divider my="md" />
                    <Text fw={700}>Phone:</Text>
                    <Text size="sm">+(212)6 33 44 57</Text>
                    <Divider my="md" />
                    <Text fw={700}>Email:</Text>
                    <Text size="sm"><EMAIL></Text>
                  </Accordion.Panel>
                </Accordion.Item>

                <Accordion.Item
                  className={
                    "border-base-200 text-base-content mb-[var(--mantine-spacing-lg)] rounded-[var(--mantine-radius-md)] border-solid border-[rem(1px)]"
                  }
                  value="another-account"
                >
                  <Accordion.Control>
                    <Text fw={700}>Groups</Text>
                    <Text size="sm">Foreigner,VIP</Text>
                  </Accordion.Control>
                  <Accordion.Panel>{placeholder}</Accordion.Panel>
                </Accordion.Item>

                <Accordion.Item
                  className={
                    "border-base-200 text-base-content mb-[var(--mantine-spacing-lg)] rounded-[var(--mantine-radius-md)] border-solid border-[rem(1px)]"
                  }
                  value="newsletter"
                >
                  <Accordion.Control>
                    <Text fw={700}>Discounts</Text>
                    <Text size="sm">15.00%</Text>
                  </Accordion.Control>
                  <Accordion.Panel>{placeholder}</Accordion.Panel>
                </Accordion.Item>
                <Accordion.Item
                  className={
                    "border-base-200 text-base-content mb-[var(--mantine-spacing-lg)] rounded-[var(--mantine-radius-md)] border-solid border-[rem(1px)]"
                  }
                  value="newsletter"
                >
                  <Accordion.Control>
                    <Text fw={700}>family</Text>
                    <Text size="sm">balance:$0.00</Text>
                  </Accordion.Control>
                  <Accordion.Panel>{placeholder}</Accordion.Panel>
                </Accordion.Item>
                <Accordion.Item
                  className={
                    "border-base-200 text-base-content mb-[var(--mantine-spacing-lg)] rounded-[var(--mantine-radius-md)] border-solid border-[rem(1px)]"
                  }
                  value="credit-card"
                >
                  <Accordion.Control>
                    Do you store credit card information securely?
                  </Accordion.Control>
                  <Accordion.Panel>{placeholder}</Accordion.Panel>
                </Accordion.Item>
                <Accordion.Item
                  className={
                    "border-base-200 text-base-content mb-[var(--mantine-spacing-lg)] rounded-[var(--mantine-radius-md)] border-solid border-[rem(1px)]"
                  }
                  value="newsletter"
                >
                  <Accordion.Control>
                    <Text fw={700}>Insurance</Text>
                    <Text size="sm">No insurance</Text>
                  </Accordion.Control>
                  <Accordion.Panel>{placeholder}</Accordion.Panel>
                </Accordion.Item>
              </Accordion>
            </div>
          </div>
        </div>
        <div className="w-6/12 bg-white">
          <div className="bg-[#F2F5F8] p-2 px-1.5">
            <div className="flex justify-between border-2 p-4">
              <Text fw={700}>
                All{" "}
                <Button variant="filled" color="teal" px={4} h={20} ml={4}>
                  3
                </Button>
              </Text>
              <Text fw={700}>
                Appointments
                <Button variant="filled" color="teal" px={4} h={20} ml={4}>
                  2
                </Button>
              </Text>
              <Text fw={700}>
                Invoices
                <Button variant="filled" color="teal" px={4} h={20} ml={4}>
                  0
                </Button>
              </Text>
              <Text fw={700}>
                Notes
                <Button variant="filled" color="teal" px={4} h={20} ml={4}>
                  0
                </Button>
              </Text>
              <Text fw={700}>
                Tasks
                <Button variant="filled" color="teal" px={4} h={20} ml={4}>
                  0
                </Button>
              </Text>
              <Text fw={700}>
                Documents
                <Button variant="filled" color="teal" px={4} h={20} ml={4}>
                  1
                </Button>
              </Text>
            </div>
            <Card mt={8}>
              <Text size="sm">Nov 8,2021</Text>
              <Text fw={700}>Consent Agreement</Text>
            </Card>
            <Card my={8}>
              <div className="flex justify-between">
                <Group justify="center">
                  <Button variant="default">Gallery</Button>

                  <Divider orientation="vertical" />

                  <Text fw={700}>Singned On</Text>
                  <Text size="sm">N/A</Text>
                </Group>
                <div>
                  <Text size="sm">Nov 8,2021</Text>
                  <Text size="sm">Consent Agreement</Text>
                </div>
              </div>
            </Card>
            <Card my={8}>
              <Text size="sm">Nov 8,2021</Text>
              <Text size="sm">Appointment 03:00-05:15</Text>
            </Card>
            <Card my={8}>
              <div className="flex">
                <Group justify="center">
                  <Button variant="default">No status</Button>
                  <Divider orientation="vertical" />
                  <div>
                    <Text size="sm">Provider</Text>
                    <Text fw={700}><EMAIL></Text>
                  </div>
                  <Divider orientation="vertical" />
                  <div>
                    <Text size="sm">Assistence</Text>
                    <Text fw={700}>N/A</Text>
                  </div>
                </Group>
                <div>
                  <Text size="sm">Nov 8,2021</Text>
                  <Text size="sm">by <EMAIL></Text>
                </div>
              </div>
              <Divider my="sm" variant="dashed" />
              <div className="mt-4 flex">
                <Avatar name={"Tom Smith"} color="initials" mr={8} />
                <div>
                  <Text size="sm"><EMAIL></Text>
                  <Text size="sm">22</Text>
                </div>
              </div>
              <Divider my="sm" variant="dashed" />
              <Textarea
                mt={10}
                w={"100%"}
                // label="Input label"
                // description="Input description"
                placeholder="Input placeholder"
              />
            </Card>
            <Card my={8}>
              <div className="flex">
                <Group justify="center">
                  <Button variant="default">No status</Button>
                  <Divider orientation="vertical" />
                  <div>
                    <Text size="sm">Provider</Text>
                    <Text fw={700}><EMAIL></Text>
                  </div>
                  <Divider orientation="vertical" />
                  <div>
                    <Text size="sm">Assistence</Text>
                    <Text fw={700}>N/A</Text>
                  </div>
                </Group>
                <div>
                  <Text size="sm">Nov 8,2021</Text>
                  <Text size="sm">by <EMAIL></Text>
                </div>
              </div>
              <Divider my="sm" variant="dashed" />
              <div className="mt-4 flex">
                <Avatar name={"Tom Smith"} color="initials" mr={8} />
                <div>
                  <Text size="sm"><EMAIL></Text>
                  <Text size="sm">22</Text>
                </div>
              </div>
              <Divider my="sm" variant="dashed" />
              <Textarea
                mt={10}
                w={"100%"}
                // label="Input label"
                // description="Input description"
                placeholder="Input placeholder"
              />
            </Card>
          </div>
        </div>
        <div className="w-3/12 bg-white px-2">
          <div className="bg-[#F2F5F8] px-1.5 pb-8 pt-2">
            <Accordion
              chevronPosition="right"
              defaultValue="reset-password"
              variant="separated"
            >
              <Accordion.Item
                className={"text-[var(--mantine-font-size-sm)]"}
                value="reset-password"
              >
                <Accordion.Control>Allergies (0)</Accordion.Control>
                <Accordion.Panel>
                  <Button
                    leftSection={<IconSquareRoundedPlusFilled size={14} />}
                    variant="default"
                    w={"100%"}
                    bg={"#DCEAF7"}
                    onClick={open}
                  >
                    Add Item 1
                  </Button>
                </Accordion.Panel>
              </Accordion.Item>

              <Accordion.Item
                className={"text-[var(--mantine-font-size-sm)]"}
                value="another-account"
              >
                <Accordion.Control>Prescription (0)</Accordion.Control>
                <Accordion.Panel>
                  {" "}
                  <Button
                    leftSection={<IconSquareRoundedPlusFilled size={14} />}
                    variant="default"
                    w={"100%"}
                    bg={"#DCEAF7"}
                    onClick={open}
                  >
                    Add Item 2
                  </Button>
                </Accordion.Panel>
              </Accordion.Item>

              <Accordion.Item
                className={"text-[var(--mantine-font-size-sm)]"}
                value="newsletter"
              >
                <Accordion.Control>Existing Condition (0)</Accordion.Control>
                <Accordion.Panel>
                  {" "}
                  <Button
                    leftSection={<IconSquareRoundedPlusFilled size={14} />}
                    variant="default"
                    w={"100%"}
                    bg={"#DCEAF7"}
                    onClick={open}
                  >
                    Add Item 3
                  </Button>
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>
          </div>
        </div>
      </div>

      <Drawer
        opened={opened}
        onClose={close}
        title="Authentication"
        closeButtonProps={{
          icon: <IconXboxX size={20} stroke={1.5} />,
        }}
        transitionProps={{
          transition: "rotate-right",
          duration: 150,
          timingFunction: "linear",
        }}
        position="right"
      >
        {/* Drawer content */}
      </Drawer>
    </>
  );
};

export default Overviews;
