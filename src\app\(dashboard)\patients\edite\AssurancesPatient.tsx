
import { useState } from 'react';
import cx from 'clsx';
import Icon from '@mdi/react';
import { mdiStar ,mdiPlusCircle} from '@mdi/js';
import Link from 'next/link';
import classes from './TableScrollArea.module.css';
import {
  Table,
  ScrollArea,
  ActionIcon,
  Tooltip,
  Text,
  Group
} from '@mantine/core';
import {
  IconStar,
  IconStarOff,
  IconPencil,
  IconTrash,
  IconCircle
} from '@tabler/icons-react';

interface Insurance {
  uid: string;
  organization: { name: string };
  affiliate: boolean;
  affiliate_fullname?: string;
  affiliate_number?: string;
  is_default: boolean;
}

interface InsuranceTableProps {
  insurances: Insurance[];
  patientFullName: string;
  onAdd: () => void;
  onEdit: (index: number) => void;
  onRemove: (index: number) => void;
  onSetDefault: (index: number) => void;
}

export function InsuranceTable({
  insurances,
  patientFullName,
  onAdd,
  onEdit,
  onRemove,
  onSetDefault,
}: InsuranceTableProps) {

const [scrolled, setScrolled] = useState(false);

  return (
    <>
   
      
       <ScrollArea h={300} onScrollPositionChange={({ y }) => setScrolled(y !== 0)}>
     <Table striped highlightOnHover withTableBorder withColumnBorders miw={700}>
        <Table.Thead className={cx(classes.header, { [classes.scrolled]: scrolled })}>
          <Table.Tr>
            <Table.Th>Assurance</Table.Th>
            <Table.Th>Bénéficiaire (Patient - Assuré)</Table.Th>
            <Table.Th>Nom de l’assuré(e)</Table.Th>
             <Table.Th>N° Affiliation</Table.Th>
           
              <Table.Th className="text-center">
             
                <ActionIcon variant="light" onClick={onAdd}>
                 <Icon path={mdiStar} size={1} />
                </ActionIcon>
             
            </Table.Th>
            <Table.Th className="text-center">
              <Tooltip label="Ajouter assurance">
                <ActionIcon variant="light" onClick={onAdd}>
                 <Icon path={mdiPlusCircle} size={1} />
                </ActionIcon>
              </Tooltip>
            </Table.Th>
          </Table.Tr>
        </Table.Thead>
         {insurances.length === 0 ? (
            <Table.Th>
              <Table.Td colSpan={6}>
                <Text  ta="center" c="dimmed">
                  Aucune assurance renseignée.
                </Text>
              </Table.Td>
            </Table.Th>
          ) : (
            <Table.Tbody> 
            { insurances.map((insurance, index) => (
         
    <Table.Tr key={insurance.uid || index} style={{ background: insurance.uid ? undefined : '#fff3cd' }}>
      <Table.Td>{insurance.organization.name || 'Aucune'}</Table.Td>
                <Table.Td>{insurance.affiliate ? 'Lui même' : ''}</Table.Td>
                <Table.Td>{insurance.affiliate_fullname || patientFullName}</Table.Td>
                <Table.Td>{insurance.affiliate_number || ''}</Table.Td>
        <Table.Td>
                  <Group justify="center">
                    <ActionIcon
                      variant="transparent"
                      onClick={() => onSetDefault(index)}
                      aria-label="Définir comme par défaut"
                    >
                      {insurance.is_default ? (
                        <IconStar size={18} />
                      ) : (
                        <IconStarOff size={18} />
                      )}
                    </ActionIcon>
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Group justify="center" gap={4}>
                    <ActionIcon
                      variant="subtle"
                      onClick={() => onEdit(index)}
                      aria-label="Modifier"
                    >
                      <IconPencil size={16} />
                    </ActionIcon>
                    <ActionIcon
                      variant="subtle"
                      onClick={() => onRemove(index)}
                      color="red"
                      aria-label="Supprimer"
                    >
                      <IconTrash size={16} />
                    </ActionIcon>
                  </Group>
                </Table.Td>
    </Table.Tr>

          
        
         ))}
         </Table.Tbody>
        )}
      </Table>
    </ScrollArea>
   
    </>
  );
}
