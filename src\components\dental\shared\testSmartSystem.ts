// frontend/dental_medicine/src/components/content/dental/shared/testSmartSystem.ts

import { treatmentManager } from './treatmentManager';
import { TREATMENTS } from './treatmentCompatibility';

/**
 * Script de test pour vérifier le fonctionnement du système intelligent
 */
export const testSmartSystem = () => {
  console.log('🧪 === TEST DU SYSTÈME INTELLIGENT ===');

  // Test 1: Traitements compatibles
  console.log('\n📋 Test 1: Traitements compatibles');
  const result1 = treatmentManager.applyTreatment('cleaning', '1');
  console.log('Nettoyage appliqué:', result1.success);
  
  const result2 = treatmentManager.applyTreatment('fluoride', '1');
  console.log('Fluorure appliqué:', result2.success);
  
  const result3 = treatmentManager.applyTreatment('sealant', '1');
  console.log('Scellant appliqué:', result3.success);
  
  console.log('Traitements sur dent 1:', treatmentManager.getCurrentTreatments('1'));

  // Test 2: Conflit détecté
  console.log('\n⚠️ Test 2: Conflit détecté');
  const result4 = treatmentManager.applyTreatment('crown', '2');
  console.log('Couronne appliquée:', result4.success);
  
  const result5 = treatmentManager.applyTreatment('bridge', '2');
  console.log('Bridge tenté:', result5.success);
  console.log('Conflits détectés:', result5.conflicts?.length || 0);
  
  if (result5.conflicts && result5.conflicts.length > 0) {
    console.log('Premier conflit:', result5.conflicts[0].message);
  }

  // Test 3: Application forcée
  console.log('\n🔨 Test 3: Application forcée');
  const result6 = treatmentManager.applyTreatment('bridge', '2', true);
  console.log('Bridge forcé:', result6.success);
  console.log('Traitements sur dent 2:', treatmentManager.getCurrentTreatments('2'));

  // Test 4: Séquence logique
  console.log('\n🔄 Test 4: Séquence logique');
  const result7 = treatmentManager.applyTreatment('implant', '3');
  console.log('Implant tenté:', result7.success);
  console.log('Conflits (prérequis):', result7.conflicts?.length || 0);
  
  if (result7.conflicts && result7.conflicts.length > 0) {
    console.log('Prérequis manquant:', result7.conflicts[0].message);
  }

  // Test 5: Traitement destructif
  console.log('\n🚫 Test 5: Traitement destructif');
  const result8 = treatmentManager.applyTreatment('extraction', '4');
  console.log('Extraction appliquée:', result8.success);
  
  const result9 = treatmentManager.applyTreatment('crown', '4');
  console.log('Couronne tentée après extraction:', result9.success);
  console.log('Conflits (dent extraite):', result9.conflicts?.length || 0);

  // Test 6: Paths calculés
  console.log('\n🎨 Test 6: Calcul des paths');
  const result10 = treatmentManager.applyTreatment('veneer', '5');
  console.log('Facette appliquée:', result10.success);
  console.log('Paths à afficher:', result10.pathsToShow);
  console.log('Paths à cacher:', result10.pathsToHide);

  // Résumé des tests
  console.log('\n📊 === RÉSUMÉ DES TESTS ===');
  console.log('Traitements définis:', Object.keys(TREATMENTS).length);
  console.log('Dents testées: 1, 2, 3, 4, 5');
  
  for (let i = 1; i <= 5; i++) {
    const treatments = treatmentManager.getCurrentTreatments(i.toString());
    console.log(`Dent ${i}:`, treatments.length > 0 ? treatments.join(', ') : 'Aucun traitement');
  }

  console.log('\n✅ Tests terminés !');
};

/**
 * Test spécifique pour les paths SVG
 */
export const testPathCalculation = () => {
  console.log('🎨 === TEST CALCUL DES PATHS ===');

  // Test avec différents traitements
  const treatments = ['whitening', 'veneer', 'crown', 'extraction', 'implant'];
  
  treatments.forEach(treatmentId => {
    console.log(`\n🦷 Test ${treatmentId}:`);
    const result = treatmentManager.applyTreatment(treatmentId, '10');
    
    if (result.success) {
      console.log(`  ✅ Appliqué avec succès`);
      console.log(`  📍 Paths visibles: [${result.pathsToShow.join(', ')}]`);
      console.log(`  🚫 Paths cachés: ${result.pathsToHide.length} paths`);
    } else {
      console.log(`  ❌ Échec: ${result.conflicts?.[0]?.message || 'Erreur inconnue'}`);
    }
    
    // Reset pour le test suivant
    treatmentManager.removeTreatment(treatmentId, '10');
  });

  console.log('\n✅ Test des paths terminé !');
};

/**
 * Test de performance
 */
export const testPerformance = () => {
  console.log('⚡ === TEST DE PERFORMANCE ===');

  const startTime = performance.now();
  
  // Appliquer 100 traitements
  for (let i = 1; i <= 32; i++) {
    treatmentManager.applyTreatment('cleaning', i.toString());
    treatmentManager.applyTreatment('fluoride', i.toString());
    treatmentManager.applyTreatment('whitening', i.toString());
  }
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log(`⏱️ Temps d'exécution: ${duration.toFixed(2)}ms`);
  console.log(`📊 Traitements appliqués: ${32 * 3} traitements`);
  console.log(`🚀 Performance: ${((32 * 3) / duration * 1000).toFixed(0)} traitements/seconde`);
  
  console.log('\n✅ Test de performance terminé !');
};
