

import React from 'react'
import classes from '~/styles/layout.module.css';
interface FooterProps {
    sidebarVisible: boolean;  // Receive state from parent
  }
  function Footer({ sidebarVisible }: FooterProps) {
    
  return (
    <footer className={`${classes.footer} ${sidebarVisible ? classes.headerWithSidebar : classes.headerWithoutSidebar}`}>
    <div className={classes.footerInner}>
      <div className={classes.footerLinks}>
        <a href="#" className={classes.footerLink}>Conditions d&apos;utilisation</a>
        <a href="#" className={classes.footerLink}>Politique de confidentialité</a>
        <a href="#" className={classes.footerLink}>Contact</a>
      </div>
      <div className={classes.footerCopyright}>
        © {new Date().getFullYear()} Doctor Portal. Tous droits réservés.
      </div>
    </div>
  </footer>
  )
}

export default Footer