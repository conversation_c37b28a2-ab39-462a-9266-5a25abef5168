// frontend/dental_medicine/src/components/content/dental/shared/pathUtils.ts

/**
 * Utility functions to fix layer stacking issues in dental SVG paths
 * Based on the corrected logic from EstimatesTabs.tsx
 */

export interface TreatmentPathConfig {
  treatmentPaths: string[];
  basePaths?: string[];
  resetAllPaths?: boolean;
}

/**
 * Creates a proper path visibility configuration without layer stacking
 * This function replicates the corrected logic from EstimatesTabs.tsx
 */
export const createPathVisibilityConfig = (
  svgId: string,
  config: TreatmentPathConfig
): Record<string, boolean> => {
  const newHiddenPaths: Record<string, boolean> = {};

  // Step 1: Hide all paths for this tooth (1-100) if resetAllPaths is true
  if (config.resetAllPaths !== false) {
    for (let i = 1; i <= 100; i++) {
      newHiddenPaths[`${svgId}-${i}`] = true;
    }
  }

  // Step 2: Show base tooth paths (default: 8-16)
  const basePaths = config.basePaths || [8, 9, 10, 11, 12, 13, 14, 15, 16];
  basePaths.forEach((pathId) => {
    newHiddenPaths[`${svgId}-${pathId}`] = false;
  });

  // Step 3: Show only the specific treatment paths
  config.treatmentPaths.forEach((pathId) => {
    newHiddenPaths[`${svgId}-${pathId}`] = false;
  });

  return newHiddenPaths;
};

/**
 * Predefined treatment configurations based on EstimatesTabs.tsx patterns
 */
export const TREATMENT_CONFIGS: Record<string, TreatmentPathConfig> = {
  // Esthetic treatments
  whitening: {
    treatmentPaths: ['20', '21', '22', '23'],
    resetAllPaths: true
  },

  // Preventive treatments
  cleaning: {
    treatmentPaths: ['17'],
    resetAllPaths: true
  },

  fluoride: {
    treatmentPaths: ['18'],
    resetAllPaths: true
  },

  sealant: {
    treatmentPaths: ['19'],
    resetAllPaths: true
  },

  // Restorative treatments
  temporary_restoration: {
    treatmentPaths: ['24', '25'],
    resetAllPaths: true
  },

  amalgam: {
    treatmentPaths: ['26'],
    resetAllPaths: true
  },

  glass_ionomer: {
    treatmentPaths: ['27'],
    resetAllPaths: true
  },

  // Endodontic treatments
  root_canal_temporary: {
    treatmentPaths: ['28', '29'],
    resetAllPaths: true
  },

  root_canal_calcium: {
    treatmentPaths: ['30', '31', '32', '33'],
    resetAllPaths: true
  },

  // Prosthodontic treatments
  veneer: {
    treatmentPaths: ['37'],
    resetAllPaths: true
  },

  onlay: {
    treatmentPaths: ['38', '39'],
    resetAllPaths: true
  },

  crown: {
    treatmentPaths: ['40', '41'],
    resetAllPaths: true
  },

  bridge: {
    treatmentPaths: ['51', '52'],
    resetAllPaths: true
  },

  denture: {
    treatmentPaths: ['48', '49', '50'],
    resetAllPaths: true
  },

  // Surgical treatments
  extraction: {
    treatmentPaths: ['53'],
    resetAllPaths: true
  },

  implant: {
    treatmentPaths: ['54', '55', '56', '57', '58', '59', '60'],
    resetAllPaths: true
  },

  bone_graft: {
    treatmentPaths: ['61', '62', '63', '64', '65', '66'],
    resetAllPaths: true
  },

  // Orthodontic treatments
  braces: {
    treatmentPaths: ['66'],
    resetAllPaths: true
  },

  aligners: {
    treatmentPaths: ['67'],
    resetAllPaths: true
  }
};

/**
 * Apply a treatment to a tooth without layer stacking
 * This is the main function to use in components
 */
export const applyTreatmentWithoutStacking = (
  svgId: string,
  treatmentId: string,
  customConfig?: TreatmentPathConfig
): Record<string, boolean> => {
  const config = customConfig || TREATMENT_CONFIGS[treatmentId];

  if (!config) {
    console.warn(`Treatment configuration not found for: ${treatmentId}`);
    return {};
  }

  console.log(`🦷 Applying treatment ${treatmentId} to tooth ${svgId} without stacking`);
  console.log(`📍 Treatment paths: [${config.treatmentPaths.join(', ')}]`);

  return createPathVisibilityConfig(svgId, config);
};

/**
 * Reset a tooth to its base state (only base paths visible)
 */
export const resetToothToBase = (svgId: string): Record<string, boolean> => {
  return createPathVisibilityConfig(svgId, {
    treatmentPaths: [], // No treatment paths
    resetAllPaths: true
  });
};

/**
 * Apply multiple compatible treatments to a tooth
 * This function checks compatibility and applies treatments in the correct order
 */
export const applyCompatibleTreatments = (
  svgId: string,
  treatmentIds: string[]
): Record<string, boolean> => {
  const allTreatmentPaths: string[] = [];

  // Collect all treatment paths
  treatmentIds.forEach(treatmentId => {
    const config = TREATMENT_CONFIGS[treatmentId];
    if (config) {
      allTreatmentPaths.push(...config.treatmentPaths);
    }
  });

  // Remove duplicates
  const uniquePaths = [...new Set(allTreatmentPaths)];

  console.log(`🦷 Applying compatible treatments [${treatmentIds.join(', ')}] to tooth ${svgId}`);
  console.log(`📍 Combined paths: [${uniquePaths.join(', ')}]`);

  return createPathVisibilityConfig(svgId, {
    treatmentPaths: uniquePaths,
    resetAllPaths: true
  });
};

/**
 * Test function to demonstrate the fix for layer stacking
 */
export const testLayerStackingFix = () => {
  console.log('🧪 === TEST DE CORRECTION D\'EMPILEMENT ===');

  const svgId = '1';

  // Test 1: Application séquentielle de traitements (problème original)
  console.log('\n📋 Test 1: Application séquentielle sans reset');

  // Simuler l'ancien comportement (problématique)
  let oldPaths: Record<string, boolean> = {};

  // Blanchiment
  oldPaths[`${svgId}-20`] = false; // Visible
  console.log('Après blanchiment:', Object.keys(oldPaths).filter(k => !oldPaths[k]));

  // Facettes (s'ajoute au blanchiment)
  oldPaths[`${svgId}-37`] = false; // Visible
  console.log('Après facettes (PROBLÈME):', Object.keys(oldPaths).filter(k => !oldPaths[k]));

  // Test 2: Application avec la logique corrigée
  console.log('\n✅ Test 2: Application avec reset complet');

  // Blanchiment avec reset
  let newPaths1 = applyTreatmentWithoutStacking(svgId, 'whitening');
  console.log('Après blanchiment (corrigé):', Object.keys(newPaths1).filter(k => !newPaths1[k]));

  // Facettes avec reset (remplace le blanchiment)
  let newPaths2 = applyTreatmentWithoutStacking(svgId, 'veneer');
  console.log('Après facettes (corrigé):', Object.keys(newPaths2).filter(k => !newPaths2[k]));

  // Test 3: Comparaison des résultats
  console.log('\n📊 Comparaison:');
  const oldVisiblePaths = Object.keys(oldPaths).filter(k => !oldPaths[k]);
  const newVisiblePaths = Object.keys(newPaths2).filter(k => !newPaths2[k]);

  console.log(`❌ Ancien système: ${oldVisiblePaths.length} paths visibles (empilement)`);
  console.log(`✅ Nouveau système: ${newVisiblePaths.length} paths visibles (pas d'empilement)`);

  console.log('\n🎯 Résultat: Le problème d\'empilement est résolu !');
};
