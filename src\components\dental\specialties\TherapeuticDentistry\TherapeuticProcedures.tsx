// frontend/dental_medicine/src/components/content/dental/specialties/TherapeuticDentistry/TherapeuticProcedures.tsx

import React, { useState, useMemo } from 'react';
import { Table, Checkbox, Text, Badge, Button, Group, TextInput, Select } from '@mantine/core';
import { IconSearch, IconFilter } from '@tabler/icons-react';
import { TherapeuticProcedure, ModificationState , SVGPathStyle} from '../../shared/types';

interface TherapeuticProceduresProps {
  type: 'all' | 'planned' | 'completed';
  modificationState: ModificationState;
  onModificationChange?: (
    svgId: string,
    pathId: string,
    isVisible: boolean,
    highlightedPaths?: Record<string, SVGPathStyle>
  ) => Promise<void>;
}

export const TherapeuticProcedures: React.FC<TherapeuticProceduresProps> = ({
  type,

  onModificationChange
}) => {

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Données des procédures thérapeutiques
  const allTherapeuticProcedures: TherapeuticProcedure[] = [
    {
      position: 1,
      mass: 1.008,
      symbol: 'DET',
      name: 'Détartrage Complet',
      type: 'scaling',
      pathId: '17',
      cost: 80,
      duration: 45,
      category: 'preventif',
      materials: ['Ultrasons', 'Curettes'],
      sessions: 1
    },
    {
      position: 2,
      mass: 4.003,
      symbol: 'FLU',
      name: 'Application Fluorée',
      type: 'fluoride_treatment',
      pathId: '18',
      cost: 25,
      duration: 15,
      category: 'preventif',
      materials: ['Gel fluoré'],
      sessions: 1
    },
    {
      position: 3,
      mass: 6.941,
      symbol: 'OBT',
      name: 'Obturation Composite',
      type: 'filling',
      pathId: '26',
      cost: 120,
      duration: 60,
      category: 'restaurateur',
      materials: ['Composite', 'Adhésif'],
      sessions: 1
    },
    {
      position: 4,
      mass: 9.012,
      symbol: 'CAN',
      name: 'Traitement de Canal',
      type: 'root_canal',
      pathId: '34',
      cost: 350,
      duration: 90,
      category: 'endodontie',
      materials: ['Gutta-percha', 'Ciment'],
      sessions: 2
    },
    {
      position: 5,
      mass: 10.811,
      symbol: 'SCE',
      name: 'Scellement Sillons',
      type: 'sealant',
      pathId: '19',
      cost: 40,
      duration: 20,
      category: 'preventif',
      materials: ['Résine de scellement'],
      sessions: 1
    },
    {
      position: 6,
      mass: 12.011,
      symbol: 'PAR',
      name: 'Traitement Parodontal',
      type: 'periodontal_therapy',
      pathId: '20',
      cost: 200,
      duration: 75,
      category: 'parodontie',
      materials: ['Antibiotiques locaux'],
      sessions: 3
    },
    {
      position: 7,
      mass: 14.007,
      symbol: 'POL',
      name: 'Polissage Dentaire',
      type: 'cleaning',
      pathId: '21',
      cost: 50,
      duration: 30,
      category: 'preventif',
      materials: ['Pâte à polir'],
      sessions: 1
    },
    {
      position: 8,
      mass: 15.999,
      symbol: 'DES',
      name: 'Désensibilisation',
      type: 'fluoride_treatment',
      pathId: '22',
      cost: 60,
      duration: 25,
      category: 'therapeutique',
      materials: ['Vernis fluoré'],
      sessions: 2
    }
  ];

  // Filtrer les procédures selon le type
  const filteredProcedures = useMemo(() => {
    let procedures = allTherapeuticProcedures;

    // Filtrer par type (all, planned, completed)
    if (type === 'planned') {
      procedures = procedures.filter(proc => proc.position <= 4); // Exemple: les 4 premières sont planifiées
    } else if (type === 'completed') {
      procedures = procedures.filter(proc => proc.position > 4); // Exemple: les autres sont terminées
    }

    // Filtrer par terme de recherche
    if (searchTerm) {
      procedures = procedures.filter(proc =>
        proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (filterCategory !== 'all') {
      procedures = procedures.filter(proc => proc.category === filterCategory);
    }

    return procedures;
  }, [type, searchTerm, filterCategory]);

  // Gestionnaire de sélection de ligne
  const handleRowSelection = (position: number) => {
    setSelectedRows(prev =>
      prev.includes(position)
        ? prev.filter(p => p !== position)
        : [...prev, position]
    );
  };

  // Gestionnaire d'application de procédure
  const handleApplyProcedure = async (procedure: TherapeuticProcedure) => {
    if (onModificationChange && procedure.pathId) {
      try {
        await onModificationChange('1', procedure.pathId, true, {
          [`1-${procedure.pathId}`]: {
            fill: getColorForProcedure(procedure.type),
            stroke: '#28a745',
            strokeWidth: 1
          }
        });
        console.log(`✅ Procédure thérapeutique appliquée: ${procedure.name}`);
      } catch (error) {
        console.error(`❌ Erreur application procédure: ${procedure.name}`, error);
      }
    }
  };

  // Obtenir la couleur selon le type de procédure
  const getColorForProcedure = (type: TherapeuticProcedure['type']): string => {
    switch (type) {
      case 'cleaning': return '#e8f5e8';
      case 'scaling': return '#d4edda';
      case 'fluoride_treatment': return '#cce5ff';
      case 'filling': return '#fff3cd';
      case 'root_canal': return '#f8d7da';
      case 'sealant': return '#e2e3e5';
      case 'periodontal_therapy': return '#ffeaa7';
      default: return '#f8f9fa';
    }
  };

  // Obtenir le badge de statut
  const getStatusBadge = () => {
    if (type === 'completed') {
      return <Badge color="green" size="sm">Terminé</Badge>;
    } else if (type === 'planned') {
      return <Badge color="blue" size="sm">Planifié</Badge>;
    } else {
      return <Badge color="gray" size="sm">Disponible</Badge>;
    }
  };

  // Obtenir le badge de catégorie
  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      'preventif': 'green',
      'restaurateur': 'blue',
      'endodontie': 'red',
      'parodontie': 'orange',
      'therapeutique': 'purple'
    };
    return <Badge color={colors[category] || 'gray'} variant="light" size="xs">{category}</Badge>;
  };

  const rows = filteredProcedures.map((procedure) => (
    <Table.Tr
      key={procedure.position}
      bg={selectedRows.includes(procedure.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(procedure.position)}
          onChange={() => handleRowSelection(procedure.position)}
        />
      </Table.Td>
      <Table.Td>
        <Text fw={500} size="sm">{procedure.symbol}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.name}</Text>
      </Table.Td>
      <Table.Td>
        {getCategoryBadge(procedure.category || '')}
      </Table.Td>
      <Table.Td>
        {getStatusBadge()}
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.cost}€</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.duration}min</Text>
      </Table.Td>
      <Table.Td>
        <Text size="xs" c="dimmed">
          {procedure.sessions} séance{procedure.sessions && procedure.sessions > 1 ? 's' : ''}
        </Text>
      </Table.Td>
      <Table.Td>
        <Button
          size="xs"
          variant="light"
          color="green"
          onClick={() => handleApplyProcedure(procedure)}
        >
          Appliquer
        </Button>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <div className="p-4">
      {/* Filtres et recherche */}
      <Group mb="md">
        <TextInput
          placeholder="Rechercher une procédure..."
          leftSection={<IconSearch size={16} />}
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.currentTarget.value)}
          style={{ flex: 1 }}
        />
        <Select
          placeholder="Catégorie"
          leftSection={<IconFilter size={16} />}
          data={[
            { value: 'all', label: 'Toutes' },
            { value: 'preventif', label: 'Préventif' },
            { value: 'restaurateur', label: 'Restaurateur' },
            { value: 'endodontie', label: 'Endodontie' },
            { value: 'parodontie', label: 'Parodontie' },
            { value: 'therapeutique', label: 'Thérapeutique' }
          ]}
          value={filterCategory}
          onChange={(value) => setFilterCategory(value || 'all')}
          w={150}
        />
      </Group>

      {/* Statistiques */}
      <Group mb="md">
        <Text size="sm" c="dimmed">
          {filteredProcedures.length} procédure(s) • {selectedRows.length} sélectionnée(s)
        </Text>
        {selectedRows.length > 0 && (
          <Button size="xs" variant="light" color="green">
            Appliquer la sélection
          </Button>
        )}
      </Group>

      {/* Tableau des procédures */}
      <Table striped highlightOnHover>
        <Table.Thead>
          <Table.Tr>
            <Table.Th />
            <Table.Th>Code</Table.Th>
            <Table.Th>Procédure</Table.Th>
            <Table.Th>Catégorie</Table.Th>
            <Table.Th>Statut</Table.Th>
            <Table.Th>Coût</Table.Th>
            <Table.Th>Durée</Table.Th>
            <Table.Th>Séances</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>

      {filteredProcedures.length === 0 && (
        <Text ta="center" c="dimmed" mt="xl">
          Aucune procédure trouvée
        </Text>
      )}
    </div>
  );
};

export default TherapeuticProcedures;
