'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Text,
  Card,
  Box,
  TextInput,
  Select,
  Radio,
} from '@mantine/core';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconUser,
  IconSearch,
} from '@tabler/icons-react';
import ChampsModal from './ChampsModal';

const EtatsDuPatient = () => {
  // États pour les filtres
  const [patientRecherche, setPatientRecherche] = useState('');
  const [dateDebut, setDateDebut] = useState('');
  const [dateFin, setDateFin] = useState('');
  const [sourceDonnees, setSourceDonnees] = useState('etat-compte-general');

  // État pour la modale des champs
  const [champsModalOpened, setChampsModalOpened] = useState(false);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUser size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              États du patient
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
                onClick={() => setChampsModalOpened(true)}
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Section des filtres */}
      <Card shadow="none" padding="md" radius={0} className="bg-white border-b">
        <div className="space-y-4">
          {/* Première ligne de filtres */}
          <Group align="center" gap="lg" wrap="nowrap">
            {/* Champ de recherche patient */}
            <div className="flex-shrink-0">
              <TextInput
                placeholder="Choisir un patient"
                value={patientRecherche}
                onChange={(event) => setPatientRecherche(event.currentTarget.value)}
                leftSection={<IconSearch size={16} />}
                size="sm"
                className="w-48"
              />
            </div>

            {/* Filtres de date */}
            <Group align="center" gap="sm" wrap="nowrap">
              <Text size="sm" fw={500} className="text-gray-700">
                Du
              </Text>
              <Select
                placeholder="Sélectionner"
                value={dateDebut}
                onChange={(value) => setDateDebut(value || '')}
                data={[
                  { value: '2022-01-01', label: '01/01/2022' },
                  { value: '2022-02-01', label: '01/02/2022' },
                  { value: '2022-03-01', label: '01/03/2022' },
                ]}
                size="sm"
                className="w-32"
              />

              <Text size="sm" fw={500} className="text-gray-700">
                Au
              </Text>
              <Select
                placeholder="Sélectionner"
                value={dateFin}
                onChange={(value) => setDateFin(value || '')}
                data={[
                  { value: '2022-12-31', label: '31/12/2022' },
                  { value: '2022-11-30', label: '30/11/2022' },
                  { value: '2022-10-31', label: '31/10/2022' },
                ]}
                size="sm"
                className="w-32"
              />
            </Group>
          </Group>

          {/* Section Source de données */}
          <div>
            <Text size="sm" fw={500} className="text-gray-700 mb-2">
              Source de données
            </Text>
            <Radio.Group
              value={sourceDonnees}
              onChange={setSourceDonnees}
              size="sm"
            >
              <Group gap="xl">
                <Radio
                  value="etat-compte-general"
                  label="État du Compte général"
                  className="text-sm"
                />
                <Radio
                  value="actes-dentaires"
                  label="Les actes dentaires"
                  className="text-sm"
                />
                <Radio
                  value="encaissements"
                  label="Encaissements"
                  className="text-sm"
                />
                <Radio
                  value="paiements"
                  label="Paiements"
                  className="text-sm"
                />
              </Group>
            </Radio.Group>
          </div>
        </div>
      </Card>

      {/* Zone de contenu principal */}
      <div className="flex-1 bg-white p-8">
        <div className="text-center text-gray-500">
          <Text size="lg" className="mb-2">
            Sélectionnez un patient et configurez les filtres pour afficher les états
          </Text>
          <Text size="sm" className="text-gray-400">
            Utilisez les filtres ci-dessus pour générer un rapport détaillé
          </Text>
        </div>
      </div>

      {/* Modale des champs */}
      <ChampsModal
        opened={champsModalOpened}
        onClose={() => setChampsModalOpened(false)}
      />
    </Box>
  );
};

export default EtatsDuPatient;
