/**
 * Hook personnalisé pour gérer les interactions dentaires
 * Centralise la logique d'interaction entre les boutons et le backend
 */

import { useState, useCallback, useEffect } from 'react';
import { dentalInteractionService } from '../services/dentalInteractionService';
import type { DentalInteractionResult } from '../services/dentalInteractionService';
import { notifications } from '@mantine/notifications';

export interface UseDentalInteractionProps {
  patientId: string;
  specialty: 'therapeutic' | 'esthetic' | 'prosthetic' | 'orthodontic' | 'surgery';
  autoSave?: boolean;
  showNotifications?: boolean;
}

export interface UseDentalInteractionReturn {
  // État
  selectedTeeth: number[];
  activeButton: string | null;
  isLoading: boolean;

  // Actions sur les boutons
  handleButtonClick: (buttonId: string) => Promise<void>;
  setActiveButton: (buttonId: string | null) => void;
  getButtonInfo: (buttonId: string) => any;

  // Actions sur les dents
  toggleToothSelection: (toothNumber: number) => boolean;
  togglePathSelection: (toothNumber: number, pathId: string) => boolean;
  getSelectedPaths: (toothNumber: number) => string[];
  selectAllTeeth: () => void;
  deselectAllTeeth: () => void;

  // Actions de traitement
  applyTreatmentToSelectedTeeth: (buttonId?: string, customColor?: string) => Promise<DentalInteractionResult>;
  loadPatientData: () => Promise<void>;
  saveCurrentState: () => Promise<void>;

  // Utilitaires
  hasSelectedTeeth: boolean;
  canApplyTreatment: boolean;
}

/**
 * Hook principal pour les interactions dentaires
 */
export const useDentalInteraction = ({
  patientId,
  specialty,
  autoSave = false,
  showNotifications = true
}: UseDentalInteractionProps): UseDentalInteractionReturn => {

  // États locaux
  const [selectedTeeth, setSelectedTeeth] = useState<number[]>([]);
  const [activeButton, setActiveButton] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialisation du service
  useEffect(() => {
    dentalInteractionService.initialize(patientId);
    loadPatientData();
  }, [patientId]);

  // Chargement des données du patient
  const loadPatientData = useCallback(async () => {
    setIsLoading(true);
    try {
      await dentalInteractionService.loadPatientModifications(patientId);
    } catch (error) {
      console.error('Erreur lors du chargement:', error);

      // Si c'est un problème d'UUID, on continue sans charger les données
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('UUID') || errorMessage.includes('patient')) {
        console.warn('Patient de test non trouvé, fonctionnement en mode démo');
        if (showNotifications) {
          notifications.show({
            title: 'Mode démo',
            message: 'Fonctionnement en mode démo (patient de test)',
            color: 'yellow',
            autoClose: 2000
          });
        }
      } else {
        if (showNotifications) {
          notifications.show({
            title: 'Erreur de chargement',
            message: 'Impossible de charger les données du patient',
            color: 'red',
            autoClose: 3000
          });
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [patientId, showNotifications]);

  // Gestionnaire de clic sur un bouton
  const handleButtonClick = useCallback(async (buttonId: string) => {
    try {
      setActiveButton(prev => prev === buttonId ? null : buttonId);

      const buttonInfo = dentalInteractionService.getButtonConfig(buttonId);

      if (showNotifications && buttonInfo) {
        notifications.show({
          title: 'Traitement sélectionné',
          message: `${buttonInfo.label} activé`,
          color: 'blue',
          autoClose: 2000
        });
      }
    } catch (error) {
      console.error('Erreur lors de l\'activation du bouton:', error);

      if (showNotifications) {
        notifications.show({
          title: 'Erreur',
          message: 'Impossible d\'activer le traitement',
          color: 'red',
          autoClose: 3000
        });
      }
    }
  }, [showNotifications]);

  // Sélection/désélection de dent
  const toggleToothSelection = useCallback((toothNumber: number): boolean => {
    const isSelected = dentalInteractionService.toggleToothSelection(toothNumber);
    setSelectedTeeth(dentalInteractionService.getSelectedTeeth());

    if (autoSave) {
      saveCurrentState();
    }

    return isSelected;
  }, [autoSave]);

  // Sélection/désélection de path
  const togglePathSelection = useCallback((toothNumber: number, pathId: string): boolean => {
    const isSelected = dentalInteractionService.togglePathSelection(toothNumber, pathId);

    if (autoSave) {
      saveCurrentState();
    }

    return isSelected;
  }, [autoSave]);

  // Obtenir les paths sélectionnés pour une dent
  const getSelectedPaths = useCallback((toothNumber: number): string[] => {
    return dentalInteractionService.getSelectedPaths(toothNumber);
  }, []);

  // Sélectionner toutes les dents
  const selectAllTeeth = useCallback(() => {
    // Sélectionner les dents 1-32 (système standard)
    for (let i = 1; i <= 32; i++) {
      if (!dentalInteractionService.getSelectedTeeth().includes(i)) {
        dentalInteractionService.toggleToothSelection(i);
      }
    }
    setSelectedTeeth(dentalInteractionService.getSelectedTeeth());
  }, []);

  // Désélectionner toutes les dents
  const deselectAllTeeth = useCallback(() => {
    const currentSelected = dentalInteractionService.getSelectedTeeth();
    currentSelected.forEach(toothNumber => {
      dentalInteractionService.toggleToothSelection(toothNumber);
    });
    setSelectedTeeth([]);
  }, []);

  // Appliquer le traitement aux dents sélectionnées
  const applyTreatmentToSelectedTeeth = useCallback(async (
    buttonId?: string,
    customColor?: string
  ): Promise<DentalInteractionResult> => {
    const treatmentButtonId = buttonId || activeButton;

    if (!treatmentButtonId) {
      const error = 'Aucun traitement sélectionné';

      if (showNotifications) {
        notifications.show({
          title: 'Erreur',
          message: error,
          color: 'orange',
          autoClose: 3000
        });
      }

      throw new Error(error);
    }

    if (selectedTeeth.length === 0) {
      const error = 'Aucune dent sélectionnée';

      if (showNotifications) {
        notifications.show({
          title: 'Erreur',
          message: error,
          color: 'orange',
          autoClose: 3000
        });
      }

      throw new Error(error);
    }

    setIsLoading(true);

    try {
      const result = await dentalInteractionService.applyTreatmentToSelectedTeeth(
        treatmentButtonId,
        customColor
      );

      // Recharger les données après application (seulement si pas en mode démo)
      if (result.success && !patientId.includes('123e4567')) {
        await loadPatientData();
      }

      return result;
    } catch (error) {
      console.error('Erreur lors de l\'application du traitement:', error);

      // En mode démo, simuler un succès
      if (patientId.includes('123e4567')) {
        const demoResult: DentalInteractionResult = {
          success: true,
          modificationsCreated: selectedTeeth.length,
          errors: [],
          message: `Mode démo: ${selectedTeeth.length} modifications simulées`
        };

        if (showNotifications) {
          notifications.show({
            title: 'Mode démo',
            message: demoResult.message,
            color: 'blue',
            autoClose: 3000
          });
        }

        return demoResult;
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [activeButton, selectedTeeth.length, showNotifications, loadPatientData]);

  // Sauvegarder l'état actuel
  const saveCurrentState = useCallback(async () => {
    if (!autoSave) return;

    try {
      // Ici on pourrait sauvegarder l'état de sélection
      console.log('État sauvegardé automatiquement');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde automatique:', error);
    }
  }, [autoSave]);

  // Obtenir les informations d'un bouton
  const getButtonInfo = useCallback((buttonId: string) => {
    return dentalInteractionService.getButtonConfig(buttonId);
  }, []);

  // Propriétés calculées
  const hasSelectedTeeth = selectedTeeth.length > 0;
  const canApplyTreatment = hasSelectedTeeth && activeButton !== null;

  return {
    // État
    selectedTeeth,
    activeButton,
    isLoading,

    // Actions sur les boutons
    handleButtonClick,
    setActiveButton,
    getButtonInfo,

    // Actions sur les dents
    toggleToothSelection,
    togglePathSelection,
    getSelectedPaths,
    selectAllTeeth,
    deselectAllTeeth,

    // Actions de traitement
    applyTreatmentToSelectedTeeth,
    loadPatientData,
    saveCurrentState,

    // Utilitaires
    hasSelectedTeeth,
    canApplyTreatment
  };
};

export default useDentalInteraction;
