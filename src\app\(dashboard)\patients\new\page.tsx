'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AvatarWithUpload from '@/components/common/AvatarWithUpload';
import { IconUpload,  } from '@tabler/icons-react';
import {
  Container,
  TextInput,
  Group,
  Stack,
  Card,
  Textarea,
  Select,
  Divider,
  Text,
  Alert,
  Title,
  Button,
  FileInput,
 
  Box,
 
} from '@mantine/core';
import Icon from '@mdi/react';
import { mdiAccountClock } from '@mdi/js';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconArrowLeft, IconCheck, IconAlertCircle } from '@tabler/icons-react';
import Link from 'next/link';
import "~/styles/tab.css";
// import patientService from '@/services/patientService';
import { completePatientService, CompletePatientData } from '@/services/completePatientService';
//import userManagementService from '@/services/userManagementService';
interface ApiError {
  response?: {
    status?: number;
    data?: {
      detail?: string;
      message?: string;
    };
  };
  message: string;
}

export default function AddPatientPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [doctors, setDoctors] = useState<{ value: string; label: string }[]>([]);
  const [visitDurations, setVisitDurations] = useState<{ value: string; label: string }[]>([]);

  // Calculate age based on date of birth
  const calculateAge = (dateOfBirth: Date | string | null) => {
    if (!dateOfBirth) return '';

    // Ensure dateOfBirth is a Date object
    const birthDate = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;

    // Check if birthDate is a valid Date object
    if (!(birthDate instanceof Date) || isNaN(birthDate.getTime())) {
      console.error('Invalid date object:', dateOfBirth);
      return '';
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age.toString();
  };

  // Update visit durations based on visit type
  const updateVisitDurations = (visitType: string) => {
    switch (visitType) {
      case 'diagnosis':
        setVisitDurations([
          { value: '20', label: '20 minutes' },
          { value: '30', label: '30 minutes' },
        ]);
        break;
      case 'examination':
        setVisitDurations([
          { value: '15', label: '15 minutes' },
          { value: '20', label: '20 minutes' },
        ]);
        break;
      case 're-diagnosis':
        setVisitDurations([
          { value: '10', label: '10 minutes' },
          { value: '15', label: '15 minutes' },
        ]);
        break;
      default:
        setVisitDurations([]);
    }
  };

  // Custom handlers for form fields
  const handleDateOfBirthChange = (value: Date | null) => {
    console.log('Date of birth changed:', value);
    form.setFieldValue('birth_date', value);

    try {
      if (value) {
        // Ensure value is a valid Date object
        if (!(value instanceof Date) || isNaN(value.getTime())) {
          console.error('Invalid date object received:', value);
          form.setFieldValue('age', '');
          return;
        }

        const calculatedAge = calculateAge(value);
        form.setFieldValue('age', calculatedAge);
      } else {
        form.setFieldValue('age', '');
      }
    } catch (error) {
      console.error('Error calculating age:', error);
      form.setFieldValue('age', '');
    }
  };

  const handleGenderChange = (value: string | null) => {
    form.setFieldValue('gender', value || '');
    // Clear ID fields if gender is child
    if (value === 'child') {
      form.setFieldValue('cin', '');
      form.setFieldValue('passportNumber', '');
    }
  };

  const handleVisitTypeChange = (value: string | null) => {
    form.setFieldValue('typeConsultation', value || '');
    form.setFieldValue('consultationDuration', '');
    if (value) {
      updateVisitDurations(value);
    }
  };

  // Fetch doctors on component mount
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        console.log('🔄 Chargement des médecins depuis le backend...');
        const doctorsData = await completePatientService.getDoctors();

        // Transformer les données pour le Select
        const doctorOptions = doctorsData.map(doctor => ({
          value: doctor.id,
          label: `${doctor.first_name} ${doctor.last_name}${doctor.is_assistant ? ' (Assistant)' : ''}`
        }));

        setDoctors(doctorOptions);
        console.log('✅ Médecins chargés:', doctorOptions.length);
      } catch (error) {
        console.error('❌ Erreur lors du chargement des médecins:', error);

        // Fallback vers des données mock
        const mockDoctors = [
          { value: 'doctor-1', label: 'Dr. John Smith' },
          { value: 'assistant-1', label: 'Dr. Jane Doe (Assistant)' },
          { value: 'assistant-2', label: 'Dr. Robert Johnson (Assistant)' },
        ];
        setDoctors(mockDoctors);
      }
    };

    fetchDoctors();
  }, []);

  const form = useForm({
    initialValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone_numbers: '',
      birth_date: null as Date | null,
      age: '',
      gender: '',
      cin: '',
      passportNumber: '',
      etatCivil: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      medicalHistory: '',
      allergies: '',
      insuranceCompany: '',
      insurancePolicyNumber: '',
      emergencyContactName: '',
      emergencyContactPhone: '',
      emergencyContactRelationship: '',
      docteur: '',
      typeConsultation: '',
      consultationDuration: '',
      agenda: '',
      comment: '',
      diagnosticRoom: '',
      additionalNotes: '',
      profile_image: null as File | null,
    },

    // Add event handlers for form fields
    transformValues: (values) => {
      // If date of birth is set, calculate and set age
      if (values.birth_date && !values.age) {
        try {
          // Ensure birth_date is a valid Date object
          const dateOfBirth = values.birth_date;
          if (dateOfBirth instanceof Date && !isNaN(dateOfBirth.getTime())) {
            values.age = calculateAge(dateOfBirth);
          } else {
            console.error('Invalid date in transformValues:', dateOfBirth);
            values.age = '';
          }
        } catch (error) {
          console.error('Error calculating age in transformValues:', error);
          values.age = '';
        }
      }
      return values;
    },
    validate: {
      first_name: (value) => (value.length < 1 ? 'First name is required' : null),
      last_name: (value) => (value.length < 1 ? 'Last name is required' : null),
      email: (value) => (/^\S+@\S+\.\S+$/.test(value) ? null : 'Invalid email'),
      phone_numbers: (value) => (value.length < 10 ? 'Valid phone number is required' : null),
      birth_date: (value) => (!value ? 'Date of birth is required' : null),
      gender: (value) => (!value ? 'Gender is required' : null),
      typeConsultation: (value) => (!value ? 'Visit type is required' : null),
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    setLoading(true);

    try {
      console.log('🚀 Début de la création du patient depuis la page:', values);

      // Transformer les données vers le format CompletePatientData
      const completePatientData: CompletePatientData = {
        // Informations personnelles
        firstName: values.first_name,
        lastName: values.last_name,
        email: values.email,
        phone: values.phone_numbers,
        dateOfBirth: values.birth_date,
        age: values.age,
        gender: values.gender,
        nationalIdNumber: values.gender !== 'child' ? values.cin : '',
        passportNumber: values.gender !== 'child' ? values.passportNumber : '',
        maritalStatus: values.etatCivil,

        // Adresse
        address: values.address,
        city: values.city,
        state: values.state,
        zipCode: values.zipCode,

        // Informations médicales
        medicalHistory: values.medicalHistory,
        allergies: values.allergies,

        // Assurance
        insuranceCompany: values.insuranceCompany,
        insurancePolicyNumber: values.insurancePolicyNumber,

        // Contact d'urgence
        emergencyContactName: values.emergencyContactName,
        emergencyContactPhone: values.emergencyContactPhone,
        emergencyContactRelationship: values.emergencyContactRelationship,

        // Informations de visite
        doctorAssigned: values.docteur,
        visitType: values.typeConsultation,
        visitDuration: values.consultationDuration,
        agenda: values.agenda,
        comments: values.comment,
        diagnosticRoom: values.diagnosticRoom,
        additionalNotes: values.additionalNotes,

        // Rendez-vous (valeurs par défaut pour la page de création)
        appointment_date: new Date(), // Date actuelle par défaut
        duration: parseInt(values.consultationDuration) || 30,
        resourceId: 1, // Salle par défaut
        consultation_type: values.typeConsultation || 'consultation'
      };

      // Validation des données avant soumission
      const validation = completePatientService.validatePatientData(completePatientData);
      if (!validation.isValid) {
        console.error('❌ Validation échouée:', validation.errors);

        notifications.show({
          title: 'Erreur de validation',
          message: validation.errors.join(', '),
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      // Créer le patient via le service backend complet
      const result = await completePatientService.createCompletePatient(completePatientData);
      console.log('✅ Patient créé avec succès:', result);

      notifications.show({
        title: 'Succès',
        message: `Patient ${values.first_name} ${values.last_name} créé avec succès`,
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      // Redirection vers la liste des patients
      router.push('/patients');

    } catch (error: unknown) {
      console.error('❌ Erreur lors de la création du patient:', error);

      const apiError = error as ApiError;
      let errorMessage = 'Impossible de créer le patient. Veuillez réessayer.';

      // Gestion des erreurs spécifiques
      if (apiError?.response?.status === 400) {
        errorMessage = 'Données invalides. Vérifiez les champs requis.';
      } else if (apiError?.response?.status === 409) {
        errorMessage = 'Un patient avec cet email existe déjà.';
      } else if (apiError?.response?.data?.detail) {
        errorMessage = apiError.response.data.detail;
      } else if (apiError?.response?.data?.message) {
        errorMessage = apiError.response.data.message;
      } else if (apiError?.message) {
        errorMessage = apiError.message;
      }

      notifications.show({
        title: 'Erreur',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };
  const [toggleState, setToggleState] = useState(1);
 

const icons = [
  { icon: <Icon path={mdiAccountClock} size={1} key="DocumentsPage" />, label: "Documents" },
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return ( 
        <div className='w-full mb-30 flex'>
      <Container size="lg" py="xl" >
      <Group mb="lg">
        <Button
          variant="outline"
          leftSection={<IconArrowLeft size={16} />}
          component={Link}
          href="/patients"
        >
          Retour aux patients
        </Button>
      </Group>

      <Title order={1} mb="lg">Ajouter un nouveau patient</Title>

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Card withBorder p="xl" radius="md" style={{width:"100%"}}>
          <Stack>
            <Title order={3}>Informations personnelles</Title>

            <Group grow>
               <Box>
                 <AvatarWithUpload
                  size={120}
                  radius={120}
                  color="blue"
                  src={form.values?.profile_image
                   
                      ? `${form.values.profile_image}?t=${Date.now()}`
                      : undefined}
                  initials={`${form.values?.first_name?.charAt(0) || ''}${form.values?.last_name?.charAt(0) || ''}`}
                  onImageUpload={async () => {
                     <FileInput
                    placeholder="Upload image"
                    accept="image/png,image/jpeg,image/jpg"
                    leftSection={<IconUpload size={14} />}
                    {...form.getInputProps('profile_image')}
                  />
                  }}
                />
         
            </Box>
              <TextInput
                label="Prénom"
                placeholder="Jean"
                required
                {...form.getInputProps('first_name')}
              />

              <TextInput
                label="Nom"
                placeholder="Dupont"
                required
                {...form.getInputProps('last_name')}
              />
            </Group>

            <Group grow>
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                required
                {...form.getInputProps('email')}
              />

              <TextInput
                label="Phone"
                placeholder="(*************"
                required
                {...form.getInputProps('phone_numbers')}
              />
            </Group>

            <Group grow>
               <TextInput
                label="Date de Naissance"
                type="date"
                placeholder="Date de Naissance..."
                value={form.values.birth_date instanceof Date ? form.values.birth_date.toISOString().split('T')[0] : ''}
                onChange={(event) => {
                  const value = event.currentTarget.value ? new Date(event.currentTarget.value) : null;
                  handleDateOfBirthChange(value);
                }}
                required
                className="input input-bordered max-w-[278px] w-full"
                />
              <Select
                label="Âge"
                placeholder="Sélectionner l'âge"
                searchable
                clearable
                data={Array.from({ length: 120 }, (_, i) => ({ value: (i + 1).toString(), label: (i + 1).toString() + ' ans' }))}
                value={form.values.age}
                onChange={(value) => {
                  form.setFieldValue('age', value || '');
                  if (value) {
                    form.setFieldValue('birth_date', null);
                  }
                }}
              />
            </Group>

            <Group grow>
              <Select
                label="Genre"
                placeholder="Sélectionner le genre"
                required
                data={[
                  { value: 'male', label: 'Homme' },
                  { value: 'female', label: 'Femme' },
                  { value: 'child', label: 'Enfant' },
                  { value: 'other', label: 'Autre' },
                ]}
                {...form.getInputProps('gender', {
                  type: 'input',
                  onChange: handleGenderChange
                })}
              />

              <Select
                label="État civil"
                placeholder="Sélectionner l'état civil"
                data={[
                  { value: 'single', label: 'Célibataire' },
                  { value: 'married', label: 'Marié(e)' },
                ]}
                {...form.getInputProps('etatCivil')}
              />
            </Group>

            {form.values.gender !== 'child' && (
              <Group grow>
                <TextInput
                  label="Numéro d'identité nationale"
                  placeholder="Entrer le numéro d'identité"
                  {...form.getInputProps('cin')}
                />

                <TextInput
                  label="Numéro de passeport"
                  placeholder="Entrer le numéro de passeport"
                  {...form.getInputProps('passportNumber')}
                />
              </Group>
            )}

            <Divider my="md" />

            <Title order={3}>Address</Title>

            <TextInput
              label="Street Address"
              placeholder="123 Main St"
              {...form.getInputProps('address')}
            />

            <Group grow>
              <TextInput
                label="City"
                placeholder="New York"
                {...form.getInputProps('city')}
              />

              <TextInput
                label="State/Province"
                placeholder="NY"
                {...form.getInputProps('state')}
              />

              <TextInput
                label="Zip/Postal Code"
                placeholder="10001"
                {...form.getInputProps('zipCode')}
              />
            </Group>

            <Divider my="md" />

            <Title order={3}>Medical Information</Title>

            <Textarea
              label="Medical History"
              placeholder="Enter any relevant medical history..."
              minRows={3}
              {...form.getInputProps('medicalHistory')}
            />

            <Textarea
              label="Allergies"
              placeholder="Enter any allergies..."
              minRows={2}
              {...form.getInputProps('allergies')}
            />

            <Divider my="md" />

            <Title order={3}>Insurance Information</Title>

            <Group grow>
              <Select
                label="Insurance Company"
                placeholder="Select insurance company"
                data={[
                  { value: 'aetna', label: 'Aetna' },
                  { value: 'blue-cross', label: 'Blue Cross Blue Shield' },
                  { value: 'cigna', label: 'Cigna' },
                  { value: 'humana', label: 'Humana' },
                  { value: 'kaiser', label: 'Kaiser Permanente' },
                  { value: 'medicare', label: 'Medicare' },
                  { value: 'medicaid', label: 'Medicaid' },
                  { value: 'united', label: 'UnitedHealthcare' },
                  { value: 'other', label: 'Other' },
                  { value: 'none', label: 'No Insurance' },
                ]}
                {...form.getInputProps('insuranceCompany')}
              />

              <TextInput
                label="Policy Number"
                placeholder="Enter insurance policy number"
                {...form.getInputProps('insurancePolicyNumber')}
              />
            </Group>

            <Divider my="md" />

            <Title order={3}>Visit Information</Title>

            <Group grow>
              <Select
                label="Doctor Assigned"
                placeholder="Select doctor"
                data={doctors}
                {...form.getInputProps('docteur')}
              />

              <Select
                label="Visit Type"
                placeholder="Select visit type"
                required
                data={[
                  { value: 'diagnosis', label: 'Diagnosis' },
                  { value: 'examination', label: 'Examination' },
                  { value: 're-diagnosis', label: 'Re-diagnosis' },
                ]}
                {...form.getInputProps('typeConsultation', {
                  type: 'input',
                  onChange: handleVisitTypeChange
                })}
              />
            </Group>

            <Group grow>
              <Select
                label="Visit Duration"
                placeholder="Select duration"
                data={visitDurations}
                disabled={!form.values.typeConsultation}
                {...form.getInputProps('consultationDuration')}
              />

              <Select
                label="Diagnostic Room"
                placeholder="Select room"
                data={[
                  { value: 'room-a', label: 'Room A' },
                  { value: 'room-b', label: 'Room B' },
                ]}
                {...form.getInputProps('diagnosticRoom')}
              />
            </Group>

            <Group grow>
              <Select
                label="Agenda"
                placeholder="Select agenda"
                data={[
                  { value: 'regular', label: 'Regular Visit' },
                  { value: 'follow-up', label: 'Follow-up' },
                  { value: 'emergency', label: 'Emergency' },
                  { value: 'consultation', label: 'Consultation' },
                ]}
                {...form.getInputProps('agenda')}
              />
            </Group>

            <Textarea
              label="Comments"
              placeholder="Enter any additional comments..."
              minRows={2}
              {...form.getInputProps('comment')}
            />

            <Divider my="md" />

            <Title order={3}>Emergency Contact</Title>

            <TextInput
              label="Name"
              placeholder="Jane Doe"
              {...form.getInputProps('emergencyContactName')}
            />

            <Group grow>
              <TextInput
                label="Phone"
                placeholder="(*************"
                {...form.getInputProps('emergencyContactPhone')}
              />

              <TextInput
                label="Relationship"
                placeholder="Spouse"
                {...form.getInputProps('emergencyContactRelationship')}
              />
            </Group>

            <Divider my="md" />

            <Title order={3}>Additional Information</Title>

            <Textarea
              label="Additional Notes"
              placeholder="Enter any additional notes or information about the patient..."
              minRows={3}
              {...form.getInputProps('additionalNotes')}
            />

            <Alert color="blue" icon={<IconAlertCircle size={16} />} mt="md">
              <Text fw={500}>Privacy Notice</Text>
              <Text size="sm">
                Patient information is protected under HIPAA regulations. This data will be stored securely
                and only accessed by authorized personnel.
              </Text>
            </Alert>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" component={Link} href="/patients">
                Annuler
              </Button>
              <Button type="submit" loading={loading}>
                Ajouter le patient
              </Button>
            </Group>
          </Stack>
        </Card>
      </form>
    </Container> </div>)
    
    

    default:
      return null;
  }
};
  return (
    <>
        <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    
    </>
   
  );
}
