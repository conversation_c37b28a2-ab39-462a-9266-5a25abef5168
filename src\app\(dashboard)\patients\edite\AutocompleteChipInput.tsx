import { useState } from 'react';
import { Autocomplete, Chip, Group, Box, Text, ActionIcon } from '@mantine/core';
import { IconTrash } from '@tabler/icons-react';

interface AutocompleteChipInputProps {
  label: string;
  data: string[];
  value: string[];
  onChange: (value: string[]) => void;
  readOnly?: boolean;
  required?: boolean;
  placeholder?: string;
}

export function AutocompleteChipInput({
  label,
  data,
  value,
  onChange,
  readOnly = false,
  required = false,
  placeholder = 'Saisir',
}: AutocompleteChipInputProps) {
  const [searchValue, setSearchValue] = useState('');

  const handleAdd = (item: string) => {
    if (!item.trim()) return;
    if (!value.includes(item)) {
      onChange([...value, item]);
    }
    setSearchValue('');
  };

  const handleRemove = (item: string) => {
    onChange(value.filter((v) => v !== item));
  };

  const handleClear = () => {
    onChange([]);
  };

  return (
    <Box>
      <Group justify="space-between" mb="xs">
        <Text fw={500}>
          {label}
          {required && <span style={{ color: 'red' }}> *</span>}
        </Text>
        <ActionIcon
          onClick={handleClear}
          disabled={readOnly || value.length === 0}
          color="red"
          variant="light"
          aria-label="Vider le champ"
        >
          <IconTrash size={18} />
        </ActionIcon>
      </Group>

      <Autocomplete
        data={data}
        value={searchValue}
        onChange={setSearchValue}
        onKeyDown={(event) => {
          if (event.key === 'Enter') {
            event.preventDefault();
            handleAdd(searchValue);
          }
        }}
        placeholder={placeholder}
        disabled={readOnly}
      />

      <Group mt="xs" spacing="xs" wrap="wrap">
        {value.map((item) => (
          <Chip
            key={item}
            checked
            onChange={() => handleRemove(item)}
            disabled={readOnly}
            variant="filled"
          >
            {item}
          </Chip>
        ))}
      </Group>
    </Box>
  );
}
