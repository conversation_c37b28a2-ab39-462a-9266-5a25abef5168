import React from 'react'
import { Textarea,} from '@mantine/core';
export interface FreeDictionaryField {
  id: string;
  key: string;
  type: string;
  label: string;
  value: string;
  meta_data?: {
    small?: boolean;
  };
  dictUid?: string;
  blockUid?: string;
}

interface Props {
  fields: FreeDictionaryField[];
  values: Record<string, string>;
  onChange: (key: string, value: string) => void;
  readOnly?: boolean;
}

 export function FreeDictionaryInput({
  onChange,
  readOnly = false,
}: Props) {
  return (
    <Textarea
  label="Allergies"
  value={value}
  onChange={(e) => onChange(e.currentTarget.value)}
  placeholder="Ajouter"
  disabled={readOnly}
/>
  )
}
