// frontend/dental_medicine/src/components/content/dental/shared/types.ts

import React from 'react';

// Types pour les styles SVG
export interface SVGPathStyle {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  opacity?: number;
  [key: string]: string | number | undefined;
}

// Types pour les sessions utilisateur
export interface UserSession {
  userId: string;
  username: string;
  role: string;
  permissions: string[];
  [key: string]: unknown;
}

export interface DentalModificationProps {
  onModificationChange?: (
    svgId: string,
    pathId: string,
    isVisible: boolean,
    highlightedPaths?: Record<string, SVGPathStyle>
  ) => Promise<void>;
  session?: unknown;
  isLoading?: boolean;
}

export interface SaveManagerRef {
  triggerSave: () => Promise<void>;
  hasUnsavedChanges: () => boolean;
}

export interface DentalSpecialtyTabProps extends DentalModificationProps {
  ref?: React.Ref<SaveManagerRef>;
}

export interface EstimatesTabsRef {
  triggerSave: () => Promise<void>;
}

// Types pour les contrôles dentaires
export interface DentalControlsProps {
  activeButton: string;
  onButtonClick: (buttonId: string) => void;
  onTargetPathChange: (pathId: string) => void;
}

export interface ProcedureItem {
  position: number;
  mass: number;
  symbol: string;
  name: string;
  pathId?: string;
  cost?: number;
  duration?: number;
  category?: string;
}

export interface SVGManagerProps {
  svgId: string;
  hiddenPaths: Record<string, boolean>;
  highlightedPaths: Record<string, SVGPathStyle>;
  onPathClick: (svgId: string, pathId: string) => void;
  onPathHighlight: (key: string, style: SVGPathStyle) => void;
}

// Types pour les spécialités dentaires
export type DentalSpecialty =
  | 'esthetic'
  | 'therapeutic'
  | 'prosthodontic'
  | 'surgical'
  | 'orthodontic';

export interface SpecialtyConfig {
  id: DentalSpecialty;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
}

// Types pour les procédures par spécialité
export interface EstheticProcedure extends ProcedureItem {
  type: 'whitening' | 'veneer' | 'bonding' | 'crown_aesthetic' | 'smile_design';
  shadeFrom?: string;
  shadeTo?: string;
}

export interface TherapeuticProcedure extends ProcedureItem {
  type: 'cleaning' | 'scaling' | 'fluoride_treatment' | 'filling' | 'root_canal' | 'sealant' | 'periodontal_therapy';
  materials?: string[];
  sessions?: number;
}

export interface ProsthodonticProcedure extends ProcedureItem {
  type: 'crown' | 'bridge' | 'denture' | 'implant' | 'inlay_onlay';
  material?: string;
  warranty?: number;
}

export interface SurgicalProcedure extends ProcedureItem {
  type: 'extraction' | 'implant_surgery' | 'bone_graft' | 'sinus_lift' | 'wisdom_tooth';
  anesthesia?: string;
  complexity?: string;
}

export interface OrthodonticProcedure extends ProcedureItem {
  type: 'braces' | 'aligners' | 'retainer' | 'expander';
  duration_months?: number;
  adjustment_frequency?: number;
}

// États des modifications
export interface ModificationState {
  hiddenPaths: Record<string, boolean>;
  highlightedPaths: Record<string, SVGPathStyle>;
  clickedIds: string[];
  activeButton: string;
  targetPath: string;
}

// Configuration des boutons de contrôle
export interface ControlButton {
  id: string;
  label: string;
  tooltip: string;
  pathId: string;
  icon?: React.ReactNode;
  color?: string;
  specialty: DentalSpecialty;
}

// Types pour la sauvegarde
export interface SaveResult {
  success: boolean;
  message: string;
  savedCount?: number;
  errors?: string[];
}

export interface SaveOptions {
  showNotification?: boolean;
  closeDialog?: boolean;
  validateData?: boolean;
}

// Types pour les données de procédures
export interface ProcedureData {
  allProcedures: ProcedureItem[];
  planned: ProcedureItem[];
  completed: ProcedureItem[];
  inventory: ProcedureItem[];
}

// Types pour les effets visuels
export interface EffectState {
  isEffectArmed: boolean;
  isResetArmed: boolean;
  showTracks11to13: boolean;
  showTracksWhitening: boolean;
}

// Types pour les dialogues
export interface DialogState {
  opened: boolean;
  title: string;
  content?: React.ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
}

// Types pour les filtres et recherche
export interface FilterState {
  searchTerm: string;
  selectedCategory: string;
  priceRange: [number, number];
  sortBy: 'name' | 'price' | 'duration';
  sortOrder: 'asc' | 'desc';
}

// Types pour les statistiques
export interface SpecialtyStats {
  totalProcedures: number;
  completedProcedures: number;
  plannedProcedures: number;
  totalCost: number;
  averageDuration: number;
}

export interface DentalStats {
  [key: string]: SpecialtyStats;
}

// Types pour l'export/import
export interface ExportData {
  specialty: DentalSpecialty;
  procedures: ProcedureItem[];
  modifications: ModificationState;
  timestamp: string;
  version: string;
}

// Types pour la validation
export interface ValidationRule {
  field: string;
  required?: boolean;
  minValue?: number;
  maxValue?: number;
  pattern?: RegExp;
  customValidator?: (value: unknown) => boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Types pour les notifications
export interface NotificationConfig {
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  autoClose?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

// Types pour les hooks personnalisés
export interface UseDentalModificationReturn {
  modificationState: ModificationState;
  updateModification: (svgId: string, pathId: string, isVisible: boolean) => void;
  resetModifications: () => void;
  saveModifications: () => Promise<SaveResult>;
  hasUnsavedChanges: boolean;
}

export interface UseSpecialtyDataReturn {
  procedures: ProcedureData;
  stats: SpecialtyStats;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Types pour les préférences utilisateur
export interface UserPreferences {
  defaultSpecialty: DentalSpecialty;
  autoSave: boolean;
  autoSaveInterval: number;
  showTooltips: boolean;
  compactView: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// Types pour l'historique
export interface HistoryEntry {
  id: string;
  timestamp: string;
  action: 'create' | 'update' | 'delete' | 'save';
  specialty: DentalSpecialty;
  description: string;
  data?: Record<string, unknown>;
}

export interface HistoryManager {
  entries: HistoryEntry[];
  addEntry: (entry: Omit<HistoryEntry, 'id' | 'timestamp'>) => void;
  undo: () => boolean;
  redo: () => boolean;
  canUndo: boolean;
  canRedo: boolean;
  clear: () => void;
}
