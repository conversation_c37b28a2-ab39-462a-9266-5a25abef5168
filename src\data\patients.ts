export type Patient = {
    id: string;
    sex: string;
    firstName: string;
    lastName: string;
    email: string;
    dateNaissance: string;
    departmentId: string;
    cin: string;
    ville: string;
    assurance: string;
    telephone: string;
    dateCreation: string;
    dernierVisite: string;
  };
  
  export const Patients: Patient[] = [
    {
      id: '1',
      sex: 'M',
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      dateNaissance: '1980-01-01',
      departmentId: 'CARDIO',
      cin: 'A123456',
      ville: 'Paris',
      assurance: 'CNSS',
      telephone: '+33123456789',
      dateCreation: '2024-01-01',
      dernierVisite: '2024-02-15'
    },
    {
      id: '2',
      sex: 'F',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      dateNaissance: '1990-05-15',
      departmentId: 'PEDIA',
      cin: 'B789012',
      ville: 'Lyon',
      assurance: 'RAMED',
      telephone: '+33987654321',
      dateCreation: '2024-01-15',
      dernierVisite: '2024-02-20'
    },
    {
        id: '3',
        sex: 'M',
        firstName: '<PERSON>',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '4',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '5',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '6',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '7',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '8',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '9',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '10',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '11',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '12',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '13',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '14',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '15',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '16',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '17',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '18',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '19',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '20',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
      {
        id: '21',
        sex: 'M',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateNaissance: '1980-01-01',
        departmentId: 'CARDIO',
        cin: 'A123456',
        ville: 'Paris',
        assurance: 'CNSS',
        telephone: '+33123456789',
        dateCreation: '2024-01-01',
        dernierVisite: '2024-02-15'
      },
  ];