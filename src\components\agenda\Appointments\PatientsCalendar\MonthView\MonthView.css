/* MonthView.css */
.MonthView {
  background-color: var(--content-background) !important;
}
.rbc-month-view {
  border: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-button-link {
  color: var(--text-daisy) !important;
  padding: 25px;
}
.MonthView .rbc-header {
  border-bottom: 1px solid var(--rbc-border) !important;
  padding: 25px;
}
.MonthView .rbc-header + .rbc-header {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-rtl .rbc-header + .rbc-header {
  border-left-width: 0;
  border-right: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-button-link {
  color: var(--text-daisy) !important;
  padding: 25px;
}
.rbc-month-row + .rbc-month-row {
  border-top: 1px solid var(--rbc-border) !important;
}
.rbc-time-view .rbc-allday-cell + .rbc-allday-cell {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-time-content > * + * > * {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid var(--rbc-border) !important;
}
.MonthView .rbc-today {
  background-color: #3799ce !important;
}
.MonthView .rbc-today .rbc-button-link {
  color: #dcebfa !important;
}
.MonthView .rbc-off-range-bg {
  background-color: #06b1bd !important;
}
.rbc-day-bg.rbc-off-range-bg {
  background-image: none;
}
.rbc-day-bg {
  background-image: url("/add.svg");
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

/* .MonthView .rbc-month-row .rbc-day-bg {
  background-image: url(/add.svg) center center / 16px 16px no-repeat;
} */

/* }
.MonthView .rbc-off-range-bg {
  background: rgb(0, 0, 0, 0.125);
  word-wrap: break-word;
  flex: 1 1;
  height: 100%;
  line-height: 1;
  min-height: 1em;
  width: 100%;
  transition:
    background-color var(--transition),
    opacity var(--transition);
  position: relative;
  transform: scaleX(-1);
  background-size: 10px 10px;
  background-image: repeating-linear-gradient(
    45deg,
    rgb(220, 226, 232) 0px,
    rgb(220, 226, 232) 1px,
    transparent 0px,
    transparent 50%
  );
  pointer-events: none;
  width: 100% !important;
  height: 100% !important;
  border-radius: 0px !important;
} */
