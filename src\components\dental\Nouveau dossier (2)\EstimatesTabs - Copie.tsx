"use client";
import React, { useState, useC<PERSON>back, useMemo, forwardRef, useImperativeHandle } from "react";
import {  Group,Divider, Alert,Dialog, Tabs,Radio,  Avatar,Flex,Container} from "@mantine/core";
import { DentalSvg } from '@/components/TDentalSvgMin';
import { <PERSON><PERSON>,<PERSON>talB } from '@/data/Tdantal';
import { CheckIcon } from "@mantine/core";
import { ColorTarget, HighlightedPathStyles, ApiResponse  } from '@/types/dental';
import { isAllowedPosition,isOnlyallowed} from '@/utils/dentalUtils';
import { EstimateSession, PathHighlight } from '@/services/estimateService';
import "simplebar-react/dist/simplebar.min.css";
import {  Tooltip ,Button,Text} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconHeart } from "@tabler/icons-react";
import {   IconEye, IconFileZip, IconTrash ,IconSquareRoundedPlusFilled} from '@tabler/icons-react';
import { <PERSON>u, rem } from '@mantine/core';
import {  IconPhoto,  IconMessageCircle,  IconSettings,  IconDeviceFloppy, } from "@tabler/icons-react";
import { MdPrint } from "react-icons/md";
import classes from '@/style/EstimatesTabs.module.css';
import Image from "next/image";
import dentaltop from "../../../../public/dentaltop.jpg";
import dentalButtom from "../../../../public/dentalButtom.jpg";
import { Table, Checkbox } from '@mantine/core';

// Types pour les props
interface EstimatesTabsProps {
  onModificationChange?: (svgId: string, pathId: string, isVisible: boolean, highlightedPaths?: Record<string, any>) => Promise<void>;
  session?: any;
  isLoading?: boolean;
}

// Interface pour les méthodes exposées via ref
export interface EstimatesTabsRef {
  triggerSave: () => Promise<void>;
}

export const EstimatesTabs = forwardRef<EstimatesTabsRef, EstimatesTabsProps>(({
  onModificationChange,
  session,
  isLoading,
}, ref) => {
  const icon = <IconHeart />;
   // Using isHidingMode as a read-only state since its setter is not used
   const [isHidingMode] = useState(false);
    const [currentColor, setCurrentColor] = useState<string>("");
    const [highlightedPaths, setHighlightedPaths] = useState<Record<string, HighlightedPathStyles>>({});
    const [isPathSelectionActive, setIsPathSelectionActive] = useState(false);
    // Using hiddenSvgIds as a read-only state since its setter is not used
    const [hiddenSvgIds] = useState<string[]>([]);
    const [targetPath, setTargetPath] = useState<string | null>(null);
    const [activeButton, setActiveButton] = useState<string | null>(null);
    // Using brokenRedStrokeSvgs as a read-only state since its setter is not used
    const [brokenRedStrokeSvgs] = useState<Set<string>>(new Set());
    // Using gradientEffectSvgs as a read-only state since its setter is not used
    const [gradientEffectSvgs] = useState<Set<string>>(new Set()); // For Dental
    const [gradientBottomEffectSvgs, setGradientBottomEffectSvgs] = useState<Set<string>>(new Set()); // For DentalB
    const [checked, setChecked] = useState(false);
    const [currentColorTarget, setCurrentColorTarget] = useState<ColorTarget>("fill");
    // Using selectedColor as a read-only state since its setter is not used
    const [selectedColor] = useState<string | null>(null);
    const [selectedTeeth, setSelectedTeeth] = useState<string[]>([]);
    const [pendingPath19Toggle, setPendingPath19Toggle] = useState(false);
    const [clickedIds, setClickedIds] = useState<string[]>([]);
    // Using effectSvgs as a read-only state since its setter is not used
    const [effectSvgs] = useState<Set<string>>(new Set());
    const [isAlertVisible, setIsAlertVisible] = useState(false);
    // const [viewModeLine, setViewModeLine] = useState(false);
   const [opened, { toggle, close }] = useDisclosure(false);
    const [effectState, setEffectState] = useState({
      isEffectArmed: false,
      isResetArmed: false,
      showTracks11to13: false,
      showTracksWhitening: false,
    });
 const generatePathsToShowByDefault = () => {
    const svgIds = Array.from({ length: 32 }, (_, i) => `${i + 1}`);
    const pathIds = Array.from({ length: 16 }, (_, i) => `${i + 1}`);
    return svgIds.flatMap((svgId) => pathIds.map((pathId) => ({ svg_id: svgId, path_id: pathId })));
  };
  const pathsToShowByDefault = generatePathsToShowByDefault();
  const initialHiddenPaths = useMemo(() => {
    return Dantal.concat(DantalB).reduce((acc, svgData) => {
      svgData.paths.forEach((path) => {
        const key = `${svgData.svg_id}-${path.id}`;
        const isVisibleByDefault = pathsToShowByDefault.some(
          (visiblePath) => visiblePath.svg_id === svgData.svg_id && visiblePath.path_id === path.id
        );
        // Hide path 20 by default
        if (path.id === "20") {
          acc[key] = true;
        } else {
          acc[key] = !isVisibleByDefault;
        }
      });
      return acc;
    }, {} as Record<string, boolean>);
  }, [pathsToShowByDefault]);
  const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>(initialHiddenPaths);
  const handleButtonClick = (buttonId: string) => {
    // Reset all effects when a new button is clicked
    setEffectState({
      isEffectArmed: false,
      isResetArmed: false,
      showTracks11to13: false,
      showTracksWhitening: false,
    });

    // Set the active button
    setActiveButton((prev) => (prev === buttonId ? null : buttonId));
    // Set the target path based on the button clicked
    if (buttonId === "viewCleaning") {
      setTargetPath("17");
    } else if (buttonId === "viewFluoride") {
      setTargetPath("18");
    } else if (buttonId === "viewModeSealant") {
      setTargetPath("19");
    } else if (buttonId === "viewModeWhitening") {
      setActiveButton(null); // No target path for Whitening mode
    }
    if (buttonId === "RestoratinPermanent") {
      setTargetPath("RestoratinPermanent");
    }
    if (buttonId === "RestoratinTemporary") {
      setTargetPath("25");
    }
    if (buttonId === "RestoratinAmalgam") {
      setTargetPath("26");
    }
    if (buttonId === "RestoratinGlassIonomer") {
      setTargetPath("27");
    }
    if (buttonId === "PostCare") {
      setTargetPath("36");
    }
    if (buttonId === "Veneer") {
      setTargetPath("37");
    }
    if (buttonId === "Onlay") {
      setTargetPath("39");
    }
    if (buttonId === "CrownPermanent") {
      setTargetPath("41");
    }
    if (buttonId === "CrownTemporary" ) {
      setTargetPath("42");
    }
    if (buttonId === "CrownGold" ) {
      setTargetPath("44");
    }
    if (buttonId === "CrownZirconia") {
      setTargetPath("47");
    }
    if (buttonId === "Extraction") {
      setTargetPath("53");
    }
  };
  const onPathClick = (pathId: string, svgId: string) => {
     const positionKey = `${svgId}(${pathId})`;
           const key = `${svgId}-${pathId}`;
           setSelectedTeeth((prev: string[]) => {
            const isAlreadySelected = prev.includes(positionKey);
            return isAlreadySelected ? prev.filter((tooth) => tooth !== positionKey) : [...prev, positionKey];
          });
          if (currentColor === "#FF4444" && !isAllowedPosition(positionKey)) {
            return;
          }
          if (currentColor === "#8E1616" && !isOnlyallowed(positionKey)) {
           return;
         }
           if (pendingPath19Toggle && pathId === "19" && svgId === "1") {
             setHiddenPaths((prev) => ({
               ...prev,
               [`${svgId}-${pathId}`]: !prev[`${svgId}-${pathId}`], // Toggle hidden state
             }));
             setPendingPath19Toggle(false); // Reset pending state after applying
           }
     setHighlightedPaths((prev) => {
       const newHighlightedPaths = { ...prev };
       if (!newHighlightedPaths[key]) {
         newHighlightedPaths[key] = {};
       }
       if (currentColorTarget === "both") {
         if (newHighlightedPaths[key].fill && newHighlightedPaths[key].stroke) {
           delete newHighlightedPaths[key].fill;
           delete newHighlightedPaths[key].stroke;
         } else {
           newHighlightedPaths[key] = {
             fill: currentColor,
             stroke: "#2563EB"
           };
         }
       } else {
         if (newHighlightedPaths[key][currentColorTarget]) {
           delete newHighlightedPaths[key][currentColorTarget];
         } else {
           newHighlightedPaths[key][currentColorTarget] = currentColor;
         }
       }
       if (Object.keys(newHighlightedPaths[key]).length === 0) {
         delete newHighlightedPaths[key];
       }
       return newHighlightedPaths;
     });
     if (isPathSelectionActive) {
       setClickedIds((prevIds) => [...prevIds, pathId]);
     }
      // Toggle visibility of Path19
   };
  const handleSvgClick = useCallback(
     (svgId: string) => {
       const togglePathVisibility = (pathId: string, visible: boolean) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => {
           const newHiddenPaths = { ...prev, [key]: visible };

           // Sauvegarder la modification de manière réactive
           if (onModificationChange) {
             onModificationChange(svgId, pathId, !visible, highlightedPaths)
               .catch(error => console.error('Erreur sauvegarde:', error));
           }

           return newHiddenPaths;
         });
       };

       // Fonction pour appliquer les modifications à toutes les dents sélectionnées
       const applyToSelectedTeeth = (pathIds: number[], visible: boolean) => {
         selectedTeeth.forEach(selectedTooth => {
           // Extraire svgId de la sélection (format: "svgId(pathId)")
           const match = selectedTooth.match(/^(.+)\((.+)\)$/);
           if (match) {
             const [, selectedSvgId] = match;
             pathIds.forEach(pathId => {
               togglePathVisibility(pathId.toString(), visible);
             });
           }
         });
       };
       const handleWhiteningMode = () => {
         // Reset all paths to hidden first, then show only whitening paths
         const newHiddenPaths = {};

         // Hide all paths for this tooth (1-100)
         for (let i = 1; i <= 100; i++) {
           newHiddenPaths[`${svgId}-${i}`] = true;
         }

         // Show base tooth paths (8-16)
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           newHiddenPaths[`${svgId}-${pathId}`] = false;
         });

         // Show whitening paths (20-23)
         newHiddenPaths[`${svgId}-20`] = false;
         newHiddenPaths[`${svgId}-21`] = false;
         newHiddenPaths[`${svgId}-22`] = false;
         newHiddenPaths[`${svgId}-23`] = false;

         setHiddenPaths((prev) => ({ ...prev, ...newHiddenPaths }));

       };
       const handleCleaningMode = () => {
         // Reset all paths to hidden first, then show only cleaning paths
         const newHiddenPaths = {};

         // Hide all paths for this tooth (1-100)
         for (let i = 1; i <= 100; i++) {
           newHiddenPaths[`${svgId}-${i}`] = true;
         }

         // Show base tooth paths (8-16)
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           newHiddenPaths[`${svgId}-${pathId}`] = false;
         });

         // Show cleaning path (17)
         newHiddenPaths[`${svgId}-17`] = false;

         setHiddenPaths((prev) => ({ ...prev, ...newHiddenPaths }));

         // Sauvegarder la modification réactive pour le path 17 (cleaning)
         if (onModificationChange) {
           onModificationChange(svgId, '17', false, highlightedPaths)
             .catch(error => console.error('Erreur sauvegarde cleaning:', error));
         }
       };
       const handleFluorideMode = () => {
         if (DantalB.some((svgData) => svgData.svg_id === svgId)) {
           setGradientBottomEffectSvgs((prev) => {
             const newSet = new Set(prev);
             newSet.has(svgId) ? newSet.delete(svgId) : newSet.add(svgId);
             return newSet;
           });
         }
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-17`]: true,
             [`${svgId}-18`]: !prev[`${svgId}-18`],
             [`${svgId}-19`]: true,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-24`]: true,
             [`${svgId}-25`]: true,
             [`${svgId}-26`]: true,
             [`${svgId}-27`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-29`]: true,
             [`${svgId}-30`]: true,
             [`${svgId}-31`]: true,
             [`${svgId}-32`]: true,
             [`${svgId}-33`]: true,
             [`${svgId}-34`]: true,
             [`${svgId}-35`]: true,
             [`${svgId}-36`]: true,
             [`${svgId}-37`]: true,
             [`${svgId}-38`]: true,
             [`${svgId}-39`]: true,
             [`${svgId}-40`]: true,
             [`${svgId}-41`]: true,
             [`${svgId}-42`]: true,
             [`${svgId}-43`]: true,
             [`${svgId}-44`]: true,
             [`${svgId}-45`]: true,
             [`${svgId}-46`]: true,
             [`${svgId}-47`]: true,
             [`${svgId}-48`]: true,
             [`${svgId}-49`]: true,
             [`${svgId}-50`]: true,
             [`${svgId}-51`]: true,
             [`${svgId}-52`]: true,
             [`${svgId}-53`]: true,
             [`${svgId}-54`]: true,
             [`${svgId}-55`]: true,
             [`${svgId}-56`]: true,
             [`${svgId}-57`]: true,
             [`${svgId}-58`]: true,
             [`${svgId}-59`]: true,
             [`${svgId}-60`]: true,
             [`${svgId}-61`]: true,
             [`${svgId}-62`]: true,
             [`${svgId}-63`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-65`]: true,
             [`${svgId}-67`]: true,
             [`${svgId}-68`]: true,
             [`${svgId}-69`]: true,

           }));
         });

       };
       const handleSealantMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-17`]: true,
             [`${svgId}-18`]: true,
             [`${svgId}-19`]: !prev[`${svgId}-19`],
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-24`]: true,
             [`${svgId}-25`]: true,
             [`${svgId}-26`]: true,
             [`${svgId}-27`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-29`]: true,
             [`${svgId}-30`]: true,
             [`${svgId}-31`]: true,
             [`${svgId}-32`]: true,
             [`${svgId}-33`]: true,
             [`${svgId}-34`]: true,
             [`${svgId}-35`]: true,
             [`${svgId}-36`]: true,
             [`${svgId}-37`]: true,
             [`${svgId}-38`]: true,
             [`${svgId}-39`]: true,
             [`${svgId}-40`]: true,
             [`${svgId}-41`]: true,
             [`${svgId}-42`]: true,
             [`${svgId}-43`]: true,
             [`${svgId}-44`]: true,
             [`${svgId}-45`]: true,
             [`${svgId}-46`]: true,
             [`${svgId}-47`]: true,
             [`${svgId}-48`]: true,
             [`${svgId}-49`]: true,
             [`${svgId}-50`]: true,
             [`${svgId}-51`]: true,
             [`${svgId}-52`]: true,
             [`${svgId}-53`]: true,
             [`${svgId}-54`]: true,
             [`${svgId}-55`]: true,
             [`${svgId}-56`]: true,
             [`${svgId}-57`]: true,
             [`${svgId}-58`]: true,
             [`${svgId}-59`]: true,
             [`${svgId}-60`]: true,
             [`${svgId}-61`]: true,
             [`${svgId}-62`]: true,
             [`${svgId}-63`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-65`]: true,
             [`${svgId}-67`]: true,
             [`${svgId}-68`]: true,
             [`${svgId}-69`]: true,
           }));
         });

       };
       const handleTargetPath = () => {
         if (targetPath) {
           togglePathVisibility(targetPath, !hiddenPaths[`${svgId}-${targetPath}`]);
         }
       };
       const handleRestoratinPermanentMode = () => {
         // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
    [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths((prev) => ({ ...prev,
        [key]: false,
        [`${svgId}-20`]: true,
        [`${svgId}-21`]: true,
        [`${svgId}-22`]: true,
        [`${svgId}-23`]: true,
        [`${svgId}-17`]: true,
        [`${svgId}-18`]: true,
        [`${svgId}-19`]: true,
        [`${svgId}-24`]: true,
        [`${svgId}-25`]: true,
        [`${svgId}-26`]: true,
        [`${svgId}-27`]: true,
        [`${svgId}-28`]: true,
        [`${svgId}-28`]: true,
        [`${svgId}-30`]: true,
        [`${svgId}-31`]: true,
        [`${svgId}-32`]: true,
        [`${svgId}-33`]: true,
        [`${svgId}-34`]: true,
        [`${svgId}-35`]: true,
        [`${svgId}-36`]: true,
        [`${svgId}-37`]: true,
        [`${svgId}-41`]: true,
        [`${svgId}-42`]: true,
        [`${svgId}-43`]: true,
        [`${svgId}-44`]: true,
        [`${svgId}-49`]: true,
        [`${svgId}-50`]: true,
        [`${svgId}-51`]: true,
        [`${svgId}-52`]: true,
        [`${svgId}-53`]: true,
        [`${svgId}-54`]: true,
        [`${svgId}-55`]: true,
        [`${svgId}-56`]: true,
        [`${svgId}-57`]: true,
        [`${svgId}-58`]: true,
        [`${svgId}-59`]: true,
        [`${svgId}-60`]: true,
        [`${svgId}-61`]: true,
        [`${svgId}-62`]: true,
        [`${svgId}-63`]: true,
        [`${svgId}-64`]: true,
        [`${svgId}-64`]: true,
        [`${svgId}-65`]: true,
        [`${svgId}-67`]: true,
        [`${svgId}-68`]: true,
        [`${svgId}-69`]: true,
      }));
    });
       };
       const handleTemporaryMode = () => {
            // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
       [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: !prev[`${svgId}-24`],
           [`${svgId}-25`]: !prev[`${svgId}-25`],
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
         }));
       });
       };
       const handleAmalgamMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
            [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: !prev[`${svgId}-26`],
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           }));
         });
       };
       const handleGlassIonomerMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
            [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: !prev[`${svgId}-27`],
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });
       };
       const handleRootPermanentMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
               [key]: false,
               [`${svgId}-20`]: true,
               [`${svgId}-21`]: true,
               [`${svgId}-22`]: true,
               [`${svgId}-23`]: true,
               [`${svgId}-17`]: true,
               [`${svgId}-18`]: true,
               [`${svgId}-19`]: true,
               [`${svgId}-24`]: true,
               [`${svgId}-25`]: true,
               [`${svgId}-26`]: true,
               [`${svgId}-27`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-29`]: true,
               [`${svgId}-30`]: true,
               [`${svgId}-31`]: true,
               [`${svgId}-32`]: true,
               [`${svgId}-33`]: true,
               [`${svgId}-34`]: true,
               [`${svgId}-35`]: true,
               [`${svgId}-36`]: true,
               [`${svgId}-37`]: true,
               [`${svgId}-38`]: true,
               [`${svgId}-39`]: true,
               [`${svgId}-40`]: true,
               [`${svgId}-41`]: true,
               [`${svgId}-42`]: true,
               [`${svgId}-43`]: true,
               [`${svgId}-44`]: true,
               [`${svgId}-45`]: true,
               [`${svgId}-46`]: true,
               [`${svgId}-47`]: true,
               [`${svgId}-48`]: true,
               [`${svgId}-49`]: true,
               [`${svgId}-50`]: true,
               [`${svgId}-51`]: true,
               [`${svgId}-52`]: true,
               [`${svgId}-53`]: true,
               [`${svgId}-54`]: true,
               [`${svgId}-55`]: true,
               [`${svgId}-56`]: true,
               [`${svgId}-57`]: true,
               [`${svgId}-58`]: true,
               [`${svgId}-59`]: true,
               [`${svgId}-60`]: true,
               [`${svgId}-61`]: true,
               [`${svgId}-62`]: true,
               [`${svgId}-63`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-65`]: true,
               [`${svgId}-67`]: true,
               [`${svgId}-68`]: true,
               [`${svgId}-69`]: true,
           }));
         });
       };
       const handleRootTemporaryMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: !prev[`${svgId}-28`],
           [`${svgId}-29`]: !prev[`${svgId}-29`],
           [`${svgId}-30`]: true,

           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

           }));
         });

       };
       const handleRootCalciumMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: !prev[`${svgId}-30`],
           [`${svgId}-31`]: !prev[`${svgId}-31`],
           [`${svgId}-32`]: !prev[`${svgId}-32`],
           [`${svgId}-33`]: !prev[`${svgId}-33`],
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleRootGuttaPerchaMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: !prev[`${svgId}-34`],
           [`${svgId}-35`]: !prev[`${svgId}-35`],
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

           }));
         });

       };
       const handlePostCareMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
               [`${svgId}-21`]: true,
               [`${svgId}-22`]: true,
               [`${svgId}-23`]: true,
               [`${svgId}-17`]: true,
               [`${svgId}-18`]: true,
               [`${svgId}-19`]: true,
               [`${svgId}-24`]: true,
               [`${svgId}-25`]: true,
               [`${svgId}-26`]: true,
               [`${svgId}-27`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-30`]: true,
               [`${svgId}-31`]: true,
               [`${svgId}-32`]: true,
               [`${svgId}-33`]: true,
               [`${svgId}-34`]: true,
               [`${svgId}-35`]: true,
               [`${svgId}-36`]: !prev[`${svgId}-36`],
               [`${svgId}-37`]: true,
               [`${svgId}-38`]: true,
               [`${svgId}-39`]: true,
               [`${svgId}-40`]: true,
               [`${svgId}-41`]: true,
               [`${svgId}-42`]: true,
               [`${svgId}-43`]: true,
               [`${svgId}-44`]: true,
               [`${svgId}-45`]: true,
               [`${svgId}-46`]: true,
               [`${svgId}-47`]: true,
               [`${svgId}-48`]: true,
               [`${svgId}-49`]: true,
               [`${svgId}-50`]: true,
               [`${svgId}-51`]: true,
               [`${svgId}-52`]: true,
               [`${svgId}-53`]: true,
               [`${svgId}-54`]: true,
               [`${svgId}-55`]: true,
               [`${svgId}-56`]: true,
               [`${svgId}-57`]: true,
               [`${svgId}-58`]: true,
               [`${svgId}-59`]: true,
               [`${svgId}-60`]: true,
               [`${svgId}-61`]: true,
               [`${svgId}-62`]: true,
               [`${svgId}-63`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-65`]: true,
               [`${svgId}-67`]: true,
               [`${svgId}-68`]: true,
               [`${svgId}-69`]: true,
           }));
         });

       };
       const handleVeneerMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: !prev[`${svgId}-37`],
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleCrownPermanentMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: true,
           [key]: !prev[key] ,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: !prev[`${svgId}-41`],
           [`${svgId}-41`]: !prev[`${svgId}-41`],

           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleCrownTemporaryMode = () => {
       // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
       [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: !prev[`${svgId}-24`],
           [`${svgId}-25`]: !prev[`${svgId}-25`],
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

         }));
       });

       };
       const handleCrownGoldMode = () => {
         setHiddenPaths((prev) => ({
           ...prev,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: !prev[`${svgId}-43`],
           [`${svgId}-44`]: !prev[`${svgId}-44`],
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

         }));
       };
       const handleOnlayMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = false;
           updatedPaths[`${svgId}-2`] = false;
           updatedPaths[`${svgId}-3`] = false;
           updatedPaths[`${svgId}-4`] = false;
           updatedPaths[`${svgId}-5`] = false;
           updatedPaths[`${svgId}-6`] = false;
           updatedPaths[`${svgId}-7`] = false;
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  !prev[`${svgId}-38`];
           updatedPaths[`${svgId}-39`] =  !prev[`${svgId}-39`];
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleCrownZirconiaMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] = !prev[`${svgId}-45`];
           updatedPaths[`${svgId}-46`]= !prev[`${svgId}-46`];
           updatedPaths[`${svgId}-47`]= !prev[`${svgId}-47`];
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });
       };
       const handleDentureMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = false;
           updatedPaths[`${svgId}-2`] = false;
           updatedPaths[`${svgId}-3`] = false;
           updatedPaths[`${svgId}-4`] = false;
           updatedPaths[`${svgId}-5`] = false;
           updatedPaths[`${svgId}-6`] = false;
           updatedPaths[`${svgId}-7`] = false;
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  !prev[`${svgId}-48`];
           updatedPaths[`${svgId}-49`] =  !prev[`${svgId}-49`];
           updatedPaths[`${svgId}-50`] =  !prev[`${svgId}-50`];
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleBridgeMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: !prev[`${svgId}-51`],
           [`${svgId}-52`]: !prev[`${svgId}-52`],

           }));
         });

       };
       const handleExtractionMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel

           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] = true;
           updatedPaths[`${svgId}-46`]= true;
           updatedPaths[`${svgId}-47`]= true;
           updatedPaths[`${svgId}-48`]= true;
           updatedPaths[`${svgId}-49`]= true;
           updatedPaths[`${svgId}-50`]= true;
           updatedPaths[`${svgId}-51`]= true;
           updatedPaths[`${svgId}-52`]= true;
           updatedPaths[`${svgId}-53`]= true;
           updatedPaths[`${svgId}-54`]= true;
           updatedPaths[`${svgId}-55`]= true;
           updatedPaths[`${svgId}-56`]= true;
           updatedPaths[`${svgId}-57`]= true;
           updatedPaths[`${svgId}-58`]= true;
           updatedPaths[`${svgId}-59`]= true;
           updatedPaths[`${svgId}-60`]= true;
           updatedPaths[`${svgId}-61`]= true;
           updatedPaths[`${svgId}-62`]= true;
           updatedPaths[`${svgId}-63`]= true;
           updatedPaths[`${svgId}-64`]= true;
           updatedPaths[`${svgId}-65`]= true;
           updatedPaths[`${svgId}-66`]= true;
           updatedPaths[`${svgId}-67`]= true;
           updatedPaths[`${svgId}-68`]= true;
           updatedPaths[`${svgId}-69`]= true;
           return  updatedPaths
         });
       };
       const handleImplantMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  !prev[`${svgId}-54`];
           updatedPaths[`${svgId}-55`] =  !prev[`${svgId}-55`];
           updatedPaths[`${svgId}-56`] =  !prev[`${svgId}-56`];
           updatedPaths[`${svgId}-57`] =  !prev[`${svgId}-57`];
           updatedPaths[`${svgId}-58`] =  !prev[`${svgId}-58`];
           updatedPaths[`${svgId}-59`] =  !prev[`${svgId}-59`];
           updatedPaths[`${svgId}-60`] =  !prev[`${svgId}-60`];
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleBoneMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  !prev[`${svgId}-61`];
           updatedPaths[`${svgId}-62`] =  !prev[`${svgId}-62`];
           updatedPaths[`${svgId}-63`] =  !prev[`${svgId}-63`];
           updatedPaths[`${svgId}-64`] =  !prev[`${svgId}-64`];
           updatedPaths[`${svgId}-65`] =  !prev[`${svgId}-65`];
           updatedPaths[`${svgId}-66`] =  !prev[`${svgId}-66`];
           updatedPaths[`${svgId}-67`] =  !prev[`${svgId}-67`];
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });
       };
       const handleResectionMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths

           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  !prev[`${svgId}-68`];
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleTeethCrownMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  !prev[`${svgId}-69`];
           return  updatedPaths
         });

       };
       const handleImplantTeethCrownMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  !prev[`${svgId}-54`];
           updatedPaths[`${svgId}-55`] =  !prev[`${svgId}-55`];
           updatedPaths[`${svgId}-56`] =  !prev[`${svgId}-56`];
           updatedPaths[`${svgId}-57`] =  !prev[`${svgId}-57`];
           updatedPaths[`${svgId}-58`] =  !prev[`${svgId}-58`];
           updatedPaths[`${svgId}-59`] =  !prev[`${svgId}-59`];
           updatedPaths[`${svgId}-60`] =  !prev[`${svgId}-60`];
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  !prev[`${svgId}-69`];
           return  updatedPaths
         });

       };

       switch (activeButton) {
         case "viewModeWhitening":
           handleWhiteningMode();
           break;
         case "viewCleaning":
           handleCleaningMode();
           break;
         case "viewFluoride":
           handleFluorideMode();
           break;
         case "viewModeSealant":
           handleSealantMode();
           break;
           case "RestoratinPermanent":
             handleRestoratinPermanentMode();
             break;
           case "RestoratinTemporary":
           handleTemporaryMode();
           break;
           case "RestoratinAmalgam":
           handleAmalgamMode();
           break;
           case "RestoratinGlassIonomer":
           handleGlassIonomerMode();
           break;
           case "RootPermanent":
           handleRootPermanentMode();
           break;
           case "RootTemporary":
           handleRootTemporaryMode();
           break;
           case "RootCalcium":
           handleRootCalciumMode();
           break;
           case "RootGuttaPercha":
           handleRootGuttaPerchaMode();
           break;
           case "PostCare":
             handlePostCareMode();
           break;
           case "Veneer":
             handleVeneerMode();
           break;
           case "Onlay":
             handleOnlayMode();
           break;
           case "CrownPermanent":
             handleCrownPermanentMode();
           break;
           case "CrownTemporary":
             handleCrownTemporaryMode();
           break;
           case "CrownGold":
             handleCrownGoldMode();
           break;
           case "CrownZirconia":
             handleCrownZirconiaMode();
           break;
           case "Denture":
             handleDentureMode();
           break;
           case "Bridge":
             handleBridgeMode();
           break;
           case "Extraction":
             handleExtractionMode();
           break;
           case "Implant":
             handleImplantMode();
           break;
           case "Bone":
             handleBoneMode();
           break;
           case "Resection":
             handleResectionMode();
           break;
           case "TeethCrown":
             handleTeethCrownMode();
           break;
           case "ImplantTeethCrown":
             handleImplantTeethCrownMode();
           break;
         default:
           handleTargetPath();
           break;
       }
     },
     [effectState, targetPath, pathsToShowByDefault, activeButton, hiddenPaths, effectSvgs]
   );
  const handleToggleWhitening = useCallback(() => {
    setEffectState((prev) => ({
      ...prev,
      showTracksWhitening: !prev.showTracksWhitening,
      isEffectArmed: false, // Reset other effects
      showTracks11to13: false, // Reset other effects
    }));

    // Toggle the active button for Whitening mode
    setActiveButton((prev) => (prev === "viewModeWhitening" ? null : "viewModeWhitening"));
  }, []);
  const handleColorSelect = (color: string, target: "fill" | "stroke") => {
    setCurrentColor(color);
    setCurrentColorTarget(target);
    setIsPathSelectionActive(true);
    // Only disable hiding mode if active
  };

  const getFormattedOutput = () => {
    return Object.entries(highlightedPaths)
      .map(([key]) => {
        const [svgId, pathId] = key.split("-");
        return `${svgId}(${pathId})`;
      })
      .join(", ");
  };
  const sendPathsToApi = async () => {
    if (Object.keys(highlightedPaths).length === 0) {
      console.warn("No highlighted paths to send.");
      return;
    }

    // NOUVEAU: Utiliser le système réactif unifié
    if (onModificationChange) {
      try {
        console.log("🔄 Sauvegarde via le système réactif...");

        // Sauvegarder chaque modification via le système réactif
        for (const [key, pathStyle] of Object.entries(highlightedPaths)) {
          const [svgId, pathId] = key.split("-");
          const isVisible = !hiddenPaths[key]; // Déterminer la visibilité

          console.log(`📝 Sauvegarde: SVG ${svgId}, Path ${pathId}, Visible: ${isVisible}`);
          await onModificationChange(svgId, pathId, isVisible, { [key]: pathStyle });
        }

        console.log("✅ Toutes les modifications sauvegardées via le système réactif!");

        // Fermer le dialog après sauvegarde réussie
        close();

      } catch (error) {
        console.error("❌ Erreur lors de la sauvegarde réactive:", error);
      }
    } else {
      // FALLBACK: Ancien système direct (si onModificationChange n'est pas disponible)
      console.warn("⚠️ onModificationChange non disponible, utilisation du système direct");

      const payload = Object.entries(highlightedPaths).map(([key, { fill, stroke }]) => {
        const [svgId, pathId] = key.split("-");
        const validColor = (color: string | undefined): string | null => {
          if (color === undefined) return null;
          return /^#[0-9A-F]{6}$/i.test(color) ? color : null;
        };
        return {
          svg_id: svgId,
          path_id: pathId,
          fill_color: validColor(fill),
          stroke_color: validColor(stroke),
        };
      });

      try {
        const response = await fetch("http://localhost:8000/api/tooth-modifications/", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });

        if (!response.ok) throw new Error("Failed to send paths");

        console.log("✅ Modifications sauvegardées via l'API directe!");
        close();

      } catch (error) {
        console.error("❌ Erreur lors de la sauvegarde directe:", error);
      }
    }
  };
  // Utility function to split array into chunks
  const chunkArray = <T,>(array: T[], chunkSize: number): T[][] => {
    const result: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      result.push(array.slice(i, i + chunkSize));
    }
    return result;
  };
  const elementAllProcedures = [
    { position: 2, mass: 16.011, symbol: 'x1', name: '11.0 Prophy/Aesthetic-Cleaning' },
    { position: 7, mass: 14.007, symbol: 'N', name: '9.0 Prophy/Aesthetic-Bridge' },
    { position: 39, mass: 98.906, symbol: 'Y', name: 'Yttrium' },
    { position: 56, mass: 137.33, symbol: 'Ba', name: 'Barium' },
    { position: 58, mass: 140.12, symbol: 'Ce', name: 'Cerium' },
  ];
  const elementPlanned = [
    { position: 1, mass: 7.011, symbol: 'x1', name: '2.0 Prophy/Aesthetic-Denture' },
    { position: 7, mass: 12.007, symbol: 'N', name: '11.0 Prophy/Aesthetic-Cleaning' },
    { position: 39, mass: 88.906, symbol: 'Y', name: 'Yttrium' },
    { position: 56, mass: 637.33, symbol: 'Ba', name: 'Barium' },
    { position: 58, mass: 120.12, symbol: 'Ce', name: 'Cerium' },
  ];
  const elementCompleted = [
    { position: 2, mass: 12.011, symbol: 'x1', name: '11.0 Prophy/Aesthetic-Cleaning' },
    { position: 3, mass: 13.007, symbol: 'N', name: 'Nitrogen' },
    { position: 49, mass: 48.906, symbol: 'Y', name: 'Yttrium' },
    { position: 66, mass: 136.33, symbol: 'Ba', name: '9.0 Prophy/Aesthetic-Bridge' },
    { position: 58, mass: 100.12, symbol: 'Ce', name: '2.0 Prophy/Aesthetic-Denture' },
  ];
  const elementInventory = [
    { position: 12, mass: 12.011, symbol: 'x1', name: '11.0 Prophy/Aesthetic-Cleaning' },
    { position: 7, mass: 1466.007, symbol: 'N', name: 'Nitrogen' },
    { position: 139, mass: 88.906, symbol: 'Y', name: 'Yttrium' },
    { position: 56, mass: 1317.33, symbol: 'Ba', name: '2.0 Prophy/Aesthetic-Denture' },
    { position: 528, mass: 1040.12, symbol: 'Ce', name: 'Cerium' },
  ];
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  // Exposer la méthode triggerSave via ref
  useImperativeHandle(ref, () => ({
    triggerSave: async () => {
      console.log("🔄 Déclenchement de la sauvegarde depuis le bouton principal...");
      await sendPathsToApi();
    }
  }));

const rowsAllProcedures = elementAllProcedures.map((element) => (
    <Table.Tr
      key={element.name}
      bg={selectedRows.includes(element.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(element.position)}
          onChange={(event) =>
            setSelectedRows(
              event.currentTarget.checked
                ? [...selectedRows, element.position]
                : selectedRows.filter((position) => position !== element.position)
            )
          }
        />
      </Table.Td>
      <Table.Td>{element.position}</Table.Td>
      <Table.Td>{element.name}</Table.Td>
      <Table.Td>{element.symbol}</Table.Td>
      <Table.Td>$ {element.mass}</Table.Td>
    </Table.Tr>

  ));
  const rowsPlanned = elementPlanned.map((element) => (
    <Table.Tr
      key={element.name}
      bg={selectedRows.includes(element.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(element.position)}
          onChange={(event) =>
            setSelectedRows(
              event.currentTarget.checked
                ? [...selectedRows, element.position]
                : selectedRows.filter((position) => position !== element.position)
            )
          }
        />
      </Table.Td>
      <Table.Td>{element.position}</Table.Td>
      <Table.Td>{element.name}</Table.Td>
      <Table.Td>{element.symbol}</Table.Td>
      <Table.Td>$ {element.mass}</Table.Td>
    </Table.Tr>

  ));
  const rowsCompleted = elementCompleted.map((element) => (
    <Table.Tr
      key={element.name}
      bg={selectedRows.includes(element.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(element.position)}
          onChange={(event) =>
            setSelectedRows(
              event.currentTarget.checked
                ? [...selectedRows, element.position]
                : selectedRows.filter((position) => position !== element.position)
            )
          }
        />
      </Table.Td>
      <Table.Td>{element.position}</Table.Td>
      <Table.Td>{element.name}</Table.Td>
      <Table.Td>{element.symbol}</Table.Td>
      <Table.Td>$ {element.mass}</Table.Td>
    </Table.Tr>

  ));
  const rowsInventory = elementInventory.map((element) => (
    <Table.Tr
      key={element.name}
      bg={selectedRows.includes(element.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(element.position)}
          onChange={(event) =>
            setSelectedRows(
              event.currentTarget.checked
                ? [...selectedRows, element.position]
                : selectedRows.filter((position) => position !== element.position)
            )
          }
        />
      </Table.Td>
      <Table.Td>{element.position}</Table.Td>
      <Table.Td>{element.name}</Table.Td>
      <Table.Td>{element.symbol}</Table.Td>
      <Table.Td>$ {element.mass}</Table.Td>
    </Table.Tr>

  ));
  return (
    <>
      <Tabs
        variant="unstyled"
        defaultValue="Therapy"
        classNames={classes}
      >
       <Tabs.List grow className="space-x-0 gap-0 mx-4">
              <Tabs.Tab
                value="Prophy/Aesthetic"
                leftSection={
                  < svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 -30 104 130" x="0px" y="0px" style={{ width: rem(40), height: rem(32) }} >
                <path  d="M100.02,23c-0.49-1.27-1.61-2.06-2.96-2c-3.51,0.08-6.45-0.74-8.74-2.47c-10.53-7.91-18.07-8.33-31.01-1.73   c-3.33,1.7-7.3,1.7-10.63,0c-12.92-6.59-20.43-6.18-30.92,1.67c-2.33,1.74-5.32,2.6-8.86,2.53c-1.34,0.01-2.47,0.76-2.94,2.04   C3.5,24.28,3.66,26.28,5.45,28c-0.39,0.61-0.63,1.34-0.63,2.12v2.43c0,2.6,2.11,4.71,4.7,4.71h2.3c0.66,0,1.26-0.25,1.73-0.64   c1,2.42,3.39,4.13,6.17,4.13h1.42c1.51,0,2.84-0.72,3.7-1.82c0.99,2.44,3.39,4.17,6.19,4.17h0.22v6.56l-6.81,6.5   c-2.2,2.12-3.47,5.09-3.47,8.14v26.75c0,0.42,0.34,0.75,0.75,0.75h12.93c0.41,0,0.75-0.33,0.75-0.75V66.22   c0-0.76,0.19-1.51,0.56-2.18l3.66-6.61c0.18-0.32,0.34-0.66,0.51-0.99h2.28c2.59,0,4.69-2.11,4.69-4.7v-4.48h1.04   c1.66,0,3.12-0.87,3.95-2.17c0.84,1.3,2.29,2.17,3.96,2.17h4.48c3.29,0,6.03-2.39,6.58-5.52c0.85,0.85,2.01,1.37,3.3,1.37h2.75   c2.8,0,5.2-1.73,6.19-4.18c0.86,1.1,2.19,1.82,3.69,1.82h1.43c2.78,0,5.17-1.71,6.17-4.13c0.47,0.39,1.07,0.64,1.73,0.64h2.29   c2.6,0,4.71-2.11,4.71-4.71v-2.43c0-0.83-0.26-1.6-0.7-2.24C100.09,26.44,100.62,24.57,100.02,23z M59.35,23.73   c-2.26,1.16-4.8,1.76-7.35,1.76c-2.56,0-5.1-0.6-7.36-1.76c-5.57-2.84-9.65-4.1-13.23-4.1c-0.42,0-0.75-0.34-0.75-0.75   c0-0.42,0.33-0.75,0.75-0.75c3.88,0,8.04,1.27,13.91,4.27c4.1,2.09,9.25,2.09,13.35,0c5.78-2.95,10.08-4.27,13.91-4.27   c0.42,0,0.75,0.34,0.75,0.75s-0.33,0.75-0.75,0.75C69,19.63,64.91,20.9,59.35,23.73z M38.66,43.03c0.41,0,0.75,0.33,0.75,0.75   c0,0.41-0.34,0.75-0.75,0.75h-0.8v0.79c0,0.42-0.34,0.75-0.75,0.75c-0.42,0-0.75-0.33-0.75-0.75v-0.79h-0.8   c-0.41,0-0.75-0.34-0.75-0.75c0-0.42,0.34-0.75,0.75-0.75h0.8v-0.8c0-0.42,0.33-0.75,0.75-0.75c0.41,0,0.75,0.33,0.75,0.75v0.8   H38.66z M13.04,34.53c0,0.68-0.55,1.23-1.22,1.23h-2.3c-1.76,0-3.2-1.44-3.2-3.21v-2.43c0-1.36,1.11-2.47,2.47-2.47h1.76   c1.38,0,2.49,1.11,2.49,2.47V34.53z M24.35,36.05c0,1.77-1.44,3.2-3.21,3.2h-1.42c-2.85,0-5.18-2.32-5.18-5.18v-1.71   c0-2.04,1.66-3.7,3.7-3.7h2.41c2.04,0,3.7,1.66,3.7,3.7V36.05z M31.03,41.6c-2.85,0-5.18-2.31-5.18-5.16v-3.89   c0-2.04,1.66-3.7,3.7-3.7h3.73c2.04,0,3.7,1.66,3.7,3.7v4.76h-0.53c-2.57,0-4.69,1.86-5.11,4.29H31.03z M25.6,90.3h-3.13v-26   c0-2.65,1.1-5.22,3.01-7.06l6.01-5.73c0.3,1.11,0.88,2.12,1.7,2.94l-4.12,3.95c-2.21,2.11-3.47,5.08-3.47,8.13V90.3z M38.31,56.7   l-3.66,6.61c-0.49,0.89-0.75,1.9-0.75,2.91V90.3h-6.8V66.53c0-2.65,1.09-5.22,3.01-7.05L34.79,55c1.74-1.65,3.69-3.02,5.79-4.08   C40.09,52.94,39.32,54.88,38.31,56.7z M51.34,42.57c0,1.76-1.44,3.19-3.2,3.19H47.1v-3.27c0-2.86-2.32-5.18-5.18-5.18h-3.44v-3.99   c0-2.03,1.66-3.69,3.7-3.69h5.47c2.03,0,3.69,1.66,3.69,3.69V42.57z M65.71,40.59c0,2.85-2.32,5.17-5.18,5.17h-4.48   c-1.77,0-3.21-1.43-3.21-3.2v-9.24c0-2.03,1.66-3.69,3.7-3.69h5.48c2.03,0,3.69,1.66,3.69,3.69V40.59z M78.34,36.43   c0,2.86-2.32,5.18-5.18,5.18h-2.75c-1.76,0-3.2-1.44-3.2-3.2v-5.86c0-2.04,1.66-3.69,3.7-3.69h3.73c2.04,0,3.7,1.65,3.7,3.69V36.43   z M89.65,34.07c0,2.86-2.33,5.18-5.18,5.18h-1.43c-1.76,0-3.2-1.43-3.2-3.2v-3.69c0-2.04,1.66-3.7,3.7-3.7h2.41   c2.04,0,3.7,1.66,3.7,3.7V34.07z M97.87,32.55c0,1.77-1.44,3.21-3.21,3.21h-2.29c-0.67,0-1.22-0.55-1.22-1.23v-4.41   c0-1.36,1.11-2.47,2.49-2.47h1.76c1.36,0,2.47,1.11,2.47,2.47V32.55z"/><path d="M66.54,64.99l-0.8,0v-0.8c0-0.41-0.34-0.75-0.75-0.75s-0.75,0.34-0.75,0.75v0.8l-0.8,0c-0.41,0-0.75,0.34-0.75,0.75   c0,0.41,0.34,0.75,0.75,0.75l0.8,0v0.8c0,0.41,0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75v-0.8l0.8,0c0.41,0,0.75-0.34,0.75-0.75   C67.29,65.32,66.95,64.99,66.54,64.99z"/>
                <path  d="M12.31,56.83h-0.8v-0.8c0-0.41-0.34-0.75-0.75-0.75s-0.75,0.34-0.75,0.75v0.8h-0.8c-0.41,0-0.75,0.34-0.75,0.75   c0,0.41,0.34,0.75,0.75,0.75h0.8v0.8c0,0.41,0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75v-0.8h0.8c0.41,0,0.75-0.34,0.75-0.75   C13.06,57.17,12.72,56.83,12.31,56.83z"/>
                <path  d="M45.67,81.59h-0.8v-0.8c0-0.41-0.34-0.75-0.75-0.75s-0.75,0.34-0.75,0.75v0.8h-0.8c-0.41,0-0.75,0.34-0.75,0.75   c0,0.41,0.34,0.75,0.75,0.75h0.8v0.8c0,0.41,0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75v-0.8h0.8c0.41,0,0.75-0.34,0.75-0.75   C46.42,81.92,46.09,81.59,45.67,81.59z"/>
              </svg>
                }
              >
               {/* Prophy/Aesthetic */}
               {/* تجميل الأسنان - Cosmetic Dentistry */}
                Cosmetic Dentistry
              </Tabs.Tab>
              <Tabs.Tab
                value="Therapy"
                leftSection={
                  <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 64 80" style={{ width: rem(40), height: rem(32) }}>
                  <path d="M31.93,64c-0.88,0-1.72-0.36-2.34-1.01c-3.01-3.18-3.16-10.8-2.98-15.06c0.06-1.38-0.31-2.72-1.07-3.87
                  c-2.35-3.55-3.68-6.97-3.93-10.16c-0.04-0.53,0.36-1,0.89-1.04c0.55-0.03,1,0.36,1.04,0.89c0.23,2.86,1.44,5.97,3.61,9.25
                  c0.99,1.49,1.47,3.23,1.39,5.02c-0.28,6.73,0.61,11.7,2.45,13.64c0.28,0.29,0.68,0.44,1.07,0.4c0.22-0.02,0.62-0.12,0.89-0.55
                  c0.62-1.01,1.26-3.32,0.75-8.57l0-2.27c0-1.67,0.66-3.24,1.86-4.41c1.2-1.17,2.77-1.78,4.47-1.73c3.28,0.09,5.95,2.94,5.95,6.35
                  v1.96c-0.51,5.38,0.12,7.67,0.74,8.66c0.27,0.43,0.68,0.53,0.9,0.56c0.39,0.04,0.79-0.11,1.07-0.4
                  c1.84-1.95,2.74-6.92,2.46-13.64c-0.08-1.78,0.4-3.52,1.38-5.01c5.76-8.71,3.06-13.47,2.94-13.67
                   c-3.23-6.61-11.08-4.98-14.3-3.97c-0.87,0.27-1.78,0.27-2.66,0c-0.89-0.28-2.3-0.65-3.94-0.82c-0.53-0.06-0.92-0.54-0.86-1.07      c0.06-0.53,0.53-0.92,1.07-0.86c1.8,0.19,3.34,0.6,4.32,0.9c0.49,0.15,1.01,0.15,1.5,0c2.89-0.9,12.62-3.21,16.58,4.89      c0.1,0.15,3.47,5.83-3.02,15.66c-0.75,1.15-1.12,2.49-1.06,3.87c0.18,4.26,0.03,11.87-2.99,15.06c-0.7,0.73-1.66,1.1-2.67,0.99      c-0.97-0.1-1.83-0.63-2.35-1.45c-1.14-1.82-1.49-5.11-1.04-9.78l0-1.87c0-2.37-1.83-4.35-4.07-4.41      c-1.14-0.05-2.24,0.39-3.06,1.18c-0.82,0.8-1.28,1.87-1.28,3.01v2.17c0.45,4.55,0.1,7.84-1.04,9.68      c-0.52,0.83-1.37,1.36-2.34,1.46C32.15,63.99,32.04,64,31.93,64z"/>
                  <path d="M24.67,35.02c-6.06,0-10.99-4.93-10.99-10.99s4.93-10.99,10.99-10.99s10.99,4.93,10.99,10.99S30.73,35.02,24.67,35.02z
                    M24.67,14.97c-4.99,0-9.05,4.06-9.05,9.05c0,4.99,4.06,9.05,9.05,9.05c4.99,0,9.05-4.06,9.05-9.05
                    C33.73,19.03,29.66,14.97,24.67,14.97z"/>

                  <path d="M41.41,16.95c-0.4,0-0.76-0.22-0.95-0.58l-2.05-3.94L34.95,9.9C34.66,9.69,34.5,9.36,34.5,9c0-0.35,0.17-0.68,0.46-0.88
                    l3.49-2.5l2.02-3.79c0.38-0.7,1.5-0.7,1.88,0l2.02,3.79l3.49,2.51c0.28,0.19,0.45,0.53,0.45,0.88c0,0.35-0.16,0.69-0.45,0.89
                    l0,0l-3.46,2.53l-2.05,3.95C42.18,16.72,41.81,16.95,41.41,16.95z M40.64,15.47C40.64,15.47,40.64,15.48,40.64,15.47
                    L40.64,15.47z M42.18,15.46L42.18,15.46C42.18,15.47,42.18,15.47,42.18,15.46z M37.04,9.02l2.68,1.96
                    c0.13,0.1,0.27,0.26,0.34,0.41l1.35,2.6l1.37-2.64c0.07-0.14,0.2-0.29,0.33-0.38l2.67-1.95l-2.7-1.94
                    c-0.12-0.09-0.25-0.24-0.32-0.37l-1.34-2.52l-1.35,2.52c-0.07,0.13-0.2,0.29-0.32,0.37L37.04,9.02z M46.72,9.7
                    c0,0,0.01,0.01,0.01,0.01L46.72,9.7z M36.09,9.69L36.09,9.69C36.09,9.7,36.09,9.7,36.09,9.69z M47.29,9.11L47.29,9.11
                    L47.29,9.11z"/>

                    <path d="M11.16,44.04C11.16,44.04,11.16,44.04,11.16,44.04c-0.39,0-0.74-0.22-0.92-0.57l-1.47-2.82l-2.48-1.81
                       c-0.25-0.18-0.43-0.55-0.43-0.86c0-0.34,0.16-0.66,0.43-0.85l2.51-1.8l1.44-2.71c0.17-0.32,0.56-0.59,0.93-0.55
                        c0.39,0,0.74,0.22,0.92,0.57l1.44,2.7l2.49,1.79c0.25,0.18,0.44,0.55,0.44,0.86c0,0.34-0.16,0.66-0.43,0.86c0,0,0,0,0,0      l-2.47,1.81l-1.47,2.83C11.92,43.8,11.52,44.04,11.16,44.04z M8.42,38l1.66,1.21c0.13,0.09,0.25,0.24,0.32,0.38l0.76,1.46      l0.77-1.48c0.06-0.12,0.17-0.25,0.28-0.34L13.9,38l-1.68-1.2c-0.13-0.09-0.26-0.24-0.33-0.38l-0.74-1.38l-0.75,1.41      c-0.06,0.12-0.17,0.25-0.27,0.33L8.42,38z M15.46,38.07L15.46,38.07L15.46,38.07z M15.46,38.07L15.46,38.07L15.46,38.07z"/>

                    <path d="M11.92,13.61C11.91,13.61,11.91,13.61,11.92,13.61c-0.4,0-0.75-0.22-0.93-0.57L9.26,9.72L6.34,7.6
                         C6.09,7.41,5.9,7.04,5.9,6.73c0-0.34,0.16-0.67,0.44-0.87l2.95-2.12l1.7-3.19C11.16,0.24,11.56,0,11.92,0c0,0,0,0,0,0
                         c0.39,0,0.74,0.22,0.93,0.57l1.7,3.18l2.93,2.11c0.25,0.18,0.45,0.57,0.44,0.88c0,0.34-0.17,0.66-0.44,0.86l-2.91,2.12      l-1.73,3.32C12.68,13.36,12.28,13.61,11.92,13.61z M8.45,6.73l2.11,1.54c0.13,0.09,0.26,0.24,0.32,0.38l1.03,1.98l1.04-1.99      c0.07-0.13,0.19-0.27,0.31-0.36l2.12-1.55L13.25,5.2c-0.13-0.09-0.26-0.25-0.33-0.39l-1-1.88l-1.01,1.9      c-0.07,0.14-0.21,0.29-0.34,0.38L8.45,6.73z M7.48,6.03L7.48,6.03C7.49,6.03,7.48,6.03,7.48,6.03z"/>

                     <path  d="M22.92,28.45c-0.26,0-0.5-0.1-0.69-0.28l-3.41-3.41c-0.38-0.38-0.38-0.99,0-1.37c0.38-0.38,0.99-0.38,1.37,0l2.72,2.72      l6.23-6.23c0.38-0.38,0.99-0.38,1.37,0c0.38,0.38,0.38,0.99,0,1.37l-6.91,6.91C23.42,28.35,23.18,28.45,22.92,28.45z"/>
                     </svg>
                }
              >
                Therapy
              </Tabs.Tab>
              <Tabs.Tab
                value="Prosthodontics"
                leftSection={
                  <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 402.69 610.00125"  style={{ width: rem(40), height: rem(32) }}>
                      <path d="M322.956,357.198c-6.048,1.779-32.579,8.889-78.254,11.492c-0.077,0.005-0.154,0.007-0.231,0.007   c-2.104,0-3.868-1.645-3.989-3.772c-0.126-2.205,1.56-4.096,3.766-4.221c30.147-1.719,51.983-5.506,64.667-8.271   c-19.938-6.924-41.488-16.722-41.722-42.216V293.39H154.376v16.791c-0.234,25.527-21.783,35.328-41.722,42.253   c12.684,2.765,34.52,6.552,64.667,8.271c2.206,0.125,3.892,2.016,3.766,4.221c-0.121,2.128-1.885,3.772-3.989,3.772   c-0.077,0-0.154-0.002-0.231-0.007c-45.675-2.603-72.206-9.713-78.254-11.492c-8.754,3.076-16.338,6.325-19.88,10.882   c-12.409,15.962-4.057,63.677,11.007,86.318c12.123,18.224,26.963,31.646,36.927,33.401c2.921,0.515,5.319,0.043,7.332-1.439   c4.39-3.236,3.459-14.606,2.558-25.603c-1.214-14.818-2.589-31.613,5.855-43.429c13.349-18.678,35.714-27.758,68.373-27.758   c32.66,0,55.025,9.08,68.374,27.759c8.443,11.814,7.068,28.609,5.854,43.428c-0.901,10.996-1.832,22.366,2.558,25.603   c2.011,1.482,4.409,1.954,7.332,1.439c9.964-1.755,24.804-15.178,36.927-33.401c15.063-22.642,23.416-70.356,11.007-86.318   C339.293,363.523,331.71,360.274,322.956,357.198z"/>
                      <path d="M376.797,34.469c-14.703-16.611-34.464-26.93-51.572-26.93c-18.476,0-38.178,8.056-57.23,15.847   c-19.003,7.77-38.652,15.805-57.21,15.805c-8.212,0-15.967-1.495-23.41-3.884c9.488,16.549,24.755,27.248,46.436,32.547   c2.146,0.524,3.461,2.689,2.937,4.835c-0.446,1.827-2.082,3.051-3.883,3.051c-0.314,0-0.634-0.037-0.952-0.115   c-26.979-6.593-45.646-21.586-55.559-44.531c-7.788-3.415-15.29-7.534-22.686-11.598C136.227,9.911,118.191,0,97.269,0   C58.042,0,0,38.163,0,120.075c0,55.972,38.131,101.874,60.86,124.254c9.459,9.315,22.029,14.445,35.396,14.445l208.383-0.001   c15.407,0,29.753-6.854,39.356-18.804c19.033-23.68,51.838-70.567,57.707-118.531C407.205,76.467,388.46,47.646,376.797,34.469z"/>
                  </svg>
                }
              >
              Prosthodontics
              </Tabs.Tab>
              <Tabs.Tab
                value="Surgery"
                leftSection={
                  <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 128 160" style={{ width: rem(40), height: rem(32) }} >
                     <path
                    d="M112.03587,37.41332V19.39526a8.588,8.588,0,0,0-8.57783-8.57784H48.5599a5.14671,5.14671,0,0,0,0,10.29341h1.71556v1.71556a5.15233,5.15233,0,0,0,5.14671,5.1467H72.57783a5.15233,5.15233,0,0,0,5.14671-5.1467V21.11083h24.01793V39.42919a8.90556,8.90556,0,0,0-1.70541,2.63691L91.69692,62.32454a5.46153,5.46153,0,0,1-5.0645,3.391H45.12876V58.8533h12.009a8.58807,8.58807,0,0,0,8.57784-8.57784V48.5599H48.37A11.96752,11.96752,0,0,0,51.991,39.98206v-5.1467H24.542v5.1467A11.96792,11.96792,0,0,0,28.163,48.5599H21.11083V27.97309a3.43113,3.43113,0,0,1,6.86226,0v3.43114h3.43114V27.97309a6.86227,6.86227,0,0,0-13.72454,0V48.5599H10.81742v1.71556a8.588,8.588,0,0,0,8.57784,8.57784h12.009v13.389L13.21989,91.64a8.85112,8.85112,0,0,0-2.40247,6.07485v.29161a8.88271,8.88271,0,0,0,15.39152,6.0447L41.35512,87.74077A8.61755,8.61755,0,0,0,46.26434,89.699L42.13657,103.458H36.55093a8.58793,8.58793,0,0,0-8.57784,8.57783v5.14671h72.05382v-5.14671a8.588,8.588,0,0,0-8.57784-8.57783H85.86343L81.72864,89.6756a8.5707,8.5707,0,0,0,7.83146-6.80436h7.312a8.86118,8.86118,0,0,0,8.27125-5.60062l11.40259-28.503a8.87456,8.87456,0,0,0-4.51007-11.35432ZM74.2934,22.82639A1.71666,1.71666,0,0,1,72.57783,24.542H55.42217a1.71666,1.71666,0,0,1-1.71557-1.71557V21.11083H74.2934Zm-25.7335-5.1467a1.71557,1.71557,0,1,1,0-3.43113H103.458a5.15232,5.15232,0,0,1,5.1467,5.1467V36.58433c-.112-.00419-.21706-.0334-.33-.0334a8.89448,8.89448,0,0,0-3.10108.57706V21.11083a3.43568,3.43568,0,0,0-3.43114-3.43114ZM27.97309,39.98206V38.2665H48.5599v1.71556a8.58808,8.58808,0,0,1-8.57784,8.57784H36.55093A8.588,8.588,0,0,1,27.97309,39.98206Z"
                  />
                   </svg>
               }
              >
                Surgery
              </Tabs.Tab>
              <Tabs.Tab
                value="Ortho"
                leftSection={
                 <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" style={{ width: rem(40), height: rem(32) }}>
                  <path d="M87,39h-5.112c-1.602-6.041-5.718-12.975-11.068-12.975c-5.16,0-9.173,6.415-10.889,12.275   c-1.725-5.864-5.728-12.275-10.872-12.275c-5.16,0-9.173,6.414-10.888,12.274c-1.725-5.863-5.729-12.274-10.874-12.274   c-5.366,0-9.491,6.938-11.082,12.975H12v2h3.775c-0.179,1.025-0.275,1.995-0.275,2.861c0,3.043,1.2,5.152,3.567,6.166   C20.953,50.835,23.308,51,26.19,51h0.547h1.667c3.649,0,7.971-0.063,9.799-3.229c0.595,1,1.468,1.76,2.625,2.255   C42.714,50.835,45.069,51,47.952,51h0.547h1.667c3.647,0,7.97-0.063,9.797-3.229c0.596,1,1.47,1.76,2.626,2.255   C64.476,50.835,66.83,51,69.713,51h0.547h1.666c4.764,0,10.691-0.088,10.691-7.076c0-0.883-0.101-1.875-0.286-2.924H87V39z    M70.819,29.05c3.195,0,6.422,5.122,7.924,9.95H75v-2h-9v2h-3.125C64.369,34.176,67.61,29.05,70.819,29.05z M49.059,29.05   c3.195,0,6.421,5.122,7.923,9.95H53v-2h-9v2h-2.885C42.608,34.176,45.85,29.05,49.059,29.05z M27.297,29.05   c3.196,0,6.422,5.122,7.925,9.95H32v-2h-9v2h-3.646C20.847,34.176,24.088,29.05,27.297,29.05z M28.404,48h-1.107H26.19   c-2.446,0-4.582-0.135-5.941-0.718c-0.908-0.389-1.749-1.154-1.749-3.446c0-0.854,0.125-1.822,0.336-2.836H23v2h9v-2h3.745   c0.22,1.042,0.351,2.037,0.351,2.911C36.096,47.376,34.363,48,28.404,48z M50.166,48h-1.107h-1.107   c-2.447,0-4.583-0.135-5.942-0.718c-0.908-0.389-1.749-1.154-1.749-3.446c0-0.854,0.125-1.822,0.336-2.836H44v2h9v-2h4.505   c0.221,1.042,0.351,2.037,0.351,2.911C57.855,47.376,56.123,48,50.166,48z M79.617,43.911c0,3.465-1.732,4.089-7.691,4.089h-1.106   h-1.106c-2.447,0-4.582-0.135-5.942-0.718c-0.909-0.389-1.749-1.154-1.749-3.446c0-0.854,0.126-1.822,0.336-2.836H66v2h9v-2h4.267   C79.487,42.042,79.617,43.037,79.617,43.911z"/>
                  <path d="M77.458,58.919c0-6.044-5.265-5.919-9.108-5.919h-0.454h-1.382c-2.812,0-6.375-0.056-8.09,2.273   C56.708,52.944,53.144,53,50.332,53h-0.454h-1.382c-2.812,0-6.375-0.056-8.09,2.274C38.69,52.944,35.127,53,32.314,53H31.86h-1.382   c-3.844,0-9.107-0.125-9.107,5.919c0,0.638,0.063,1.341,0.18,2.081H18v2h3.987c1.353,5.046,4.85,10.799,9.409,10.799   c4.162,0,7.439-4.795,9.009-9.469c1.57,4.674,4.846,9.469,9.008,9.469s7.439-4.794,9.01-9.468c1.57,4.674,4.847,9.468,9.008,9.468   c4.56,0,8.058-5.753,9.41-10.799H80v-2h-2.722C77.395,60.26,77.458,59.557,77.458,58.919z M66.514,56h0.451h1.385   c5.081,0,6.108,0.419,6.108,2.95c0,0.624-0.082,1.32-0.22,2.05H71v-1h-7v1h-3.368c-0.142-0.741-0.226-1.449-0.226-2.081   C60.406,56.388,61.434,56,66.514,56z M48.496,56h0.451h1.385c5.082,0,6.109,0.419,6.109,2.95c0,0.624-0.082,1.32-0.22,2.05H54v-1   h-8v1h-3.386c-0.141-0.741-0.226-1.449-0.226-2.081C42.389,56.388,43.416,56,48.496,56z M24.371,58.919   c0-2.531,1.027-2.919,6.107-2.919h0.451h1.385c5.081,0,6.108,0.419,6.108,2.95c0,0.624-0.082,1.32-0.22,2.05H35v-1h-7v1h-3.403   C24.456,60.259,24.371,59.551,24.371,58.919z M31.396,70.861c-2.526,0-5.074-4.014-6.289-7.861H28v2h7v-2h2.695   C36.483,66.847,33.928,70.861,31.396,70.861z M49.414,70.861c-2.526,0-5.074-4.014-6.289-7.861H46v2h8v-2h1.713   C54.501,66.847,51.946,70.861,49.414,70.861z M67.432,70.861c-2.525,0-5.074-4.014-6.289-7.861H64v2h7v-2h2.729   C72.518,66.847,69.963,70.861,67.432,70.861z"/>
                 </svg>
                }
              >
                Ortho
              </Tabs.Tab>
      </Tabs.List>
        <Tabs.Panel value="Prophy/Aesthetic">
        <div className="my-4 ">
        <div>
      <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px'}} align="center">
        <div  style={{ position: 'absolute', right: 16 }}>
          <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>All Procedures</Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Download zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Preview all
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Delete all
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          </div>
        <div style={{ margin: '0 auto' }} className=" mb-2 flex flex-end p-2 sm:justify-start space-x-2 ">

    <Tooltip
        label="Cleaning"
        withArrow
        className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
        <Button
        styles={{
        root: {
        position: 'relative',
        color: 'white',
        height: '35px', // Adjust button height
        width: '35px',  // Adjust button width
        padding: 0,
        borderRadius: '0.5rem'
        },
      }}
    >
        <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      width: '100%',
    }}
    >
      <span  className={
          activeButton === 'viewCleaning'
        ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
        : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
    }
    onClick={() => {
      handleButtonClick("viewCleaning");
      // setIsBrokenRedStrokeActive((prev) => !prev);
      setTargetPath("17");
    }}
        >
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="-5.0 -10.0 110.0 135.0">
            <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="m87.484 32.52c-3.4727-1.6875-10.57-5.2656-13.992-6.9219v-0.003906c-0.94531-0.46484-2.0547-0.46484-3.0039 0l-13.977 6.9258c-1.2305 0.58984-1.9805 1.8672-1.8945 3.2266 0.35156 5.7656 2.6016 19.934 15.871 26.875 0.94531 0.49609 2.0742 0.49609 3.0195 0 13.27-6.9375 15.516-21.109 15.855-26.875 0.089844-1.3594-0.65234-2.6367-1.8789-3.2266zm-5.0586 6.8281-11.789 11.789h-0.003907c-0.29687 0.30859-0.70312 0.48047-1.1328 0.48047-0.42578 0-0.83594-0.17188-1.1328-0.48047l-6.793-6.7969c-0.60547-0.62891-0.59766-1.625 0.019531-2.2422 0.61719-0.61719 1.6172-0.625 2.2461-0.023437l5.6719 5.6523 10.648-10.648v0.003906c0.62891-0.60547 1.625-0.59766 2.2422 0.019531 0.61719 0.61719 0.625 1.6133 0.019531 2.2422z"/>
            <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="m68.996 65.48c-14.715-7.7109-17.188-23.227-17.574-29.539-0.16406-2.6523 1.2812-5.1406 3.6602-6.3164l13.992-6.9062c2.9531-1.6211 5.9883-0.046875 8.6406 1.3828-0.51953-5.3633-0.16797-16.254-7.8711-15.645-6.668 0.63281-14.691 5.1523-19.934 9.3008-2.7461 2.2344-6.582 2.5742-9.6797 0.85547-0.46094-0.28516-0.74219-0.79688-0.72656-1.3398-0.003906-0.52344 0.25781-1.0156 0.69141-1.3086 0.43359-0.29688 0.98828-0.35547 1.4727-0.16016 0.60156 0.26172 1.2227 0.47266 1.8594 0.62109 0.89844 0.14062 1.8164 0.042969 2.6641-0.28516 1.8438-0.85938 3.5547-1.9805 5.0781-3.3281 1.043-0.82812 2.1719-1.543 3.3633-2.1406l-3.1328-2.2969c-5.7891-4.0508-12.973-1.8945-17.43 2.9727h0.003907c-1.7852 1.7227-4.5586 1.8906-6.5391 0.40234-3.957-3.082-12.855-6.0664-15.066 0.59375-3.375 11.344-2.0859 23.566 3.582 33.957 1.7656 3.2109 2.4688 6.9023 2.0078 10.539-1.1094 8.4805-1.0938 24.496 11.406 36.305l-0.003906-0.003906c0.625 0.59375 1.5352 0.76172 2.3281 0.42969 0.79297-0.32812 1.3164-1.0977 1.3359-1.9531-0.33203-4.2578 0.83203-44.207 15.23-33.461 6.5547 7.0664 6.9375 27.387 6.9062 33.445 0.011719 0.85938 0.53125 1.6328 1.3281 1.9648 0.79297 0.33594 1.707 0.16797 2.332-0.42578 7.3164-7.1328 11.543-16.852 11.777-27.066-0.59375-0.125-1.1641-0.32422-1.7031-0.59375z"/>
            </svg>
      </span>
        </div>
        <span
          style={{
            position: 'absolute',
            bottom: '0px',
            right: '0px',
            fontSize: '8px',
            fontWeight: '800',
            backgroundColor: 'white',
            borderTopLeftRadius: '0.5rem' ,
            borderBottomRightRadius: '0.5rem' ,
            color:'#3799CE',
            padding:'3px  0px 1px 2px' ,
          }}
          className="h-[14px] w-[14px] "
        >
            Cl
        </span>
      </Button>
    </Tooltip>
          <Tooltip
            label="Fluoride"
            withArrow
            className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              >
        <Button
          styles={{
            root: {
              position: 'relative',
              color: 'white',
              height: '35px', // Adjust button height
              width: '35px',  // Adjust button width
              padding: 0,
              borderRadius: '0.5rem'
            },
          }}
        >
          <div
        style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        }}
        >
        <span  className={
        activeButton === 'viewFluoride'
          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
        }

        onClick={() => {
        handleButtonClick("viewFluoride");
        setTargetPath("18");
        }}
          >
          <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 16.6 17"
            >
               <style jsx>{`
                .st42{fill:#FAF8F8;stroke:#3799ce;stroke-width:0.75;stroke-miterlimit:10;}
                .st43 { fill:none; }
                .st44 { fill-rule:evenodd;clip-rule:evenodd;fill:#ffffff; }
                .st45 { fill:none;stroke:#3799ce;stroke-width:0.5;stroke-miterlimit:10; }
              `}</style>
               <path className="st42" d="M13.6,8.2l0.2-0.4c0.5-0.8,0.8-1.7,0.8-2.7V4.8c0-1.3-0.8-2.6-2.2-3.3c-0.1,0-0.1-0.1-0.2-0.1   c-0.2-0.1-0.5-0.2-0.8-0.2c-0.1,0-0.1,0-0.2,0c-0.5,0-1,0-1.5,0.1c-0.4,0-0.8,0.1-1.2,0.1c-0.4,0-0.8-0.1-1.1-0.1   C7,1.2,6.5,1.2,6,1.2H5.3c-0.3,0-0.5,0-0.8,0.1C4,1.5,3.1,2,2.4,2.9C2.1,3.4,2,3.9,2,4.5c0,0.2,0,0.4,0,0.6c0,0.9,0.2,1.8,0.6,2.6   L3.3,9c0.1,0.2,0.2,0.4,0.2,0.6l0,2.2c0,0.8,0.2,1.6,0.5,2.3c0.3,0.7,0.9,1.4,1.6,1.4c0,0,1.4,0.2,1.4-1.8c0,0,0.1-1.1,0.1-1.8   c0-0.4,0.2-0.9,0.6-1.2C8,10.5,8.5,10.4,9,11c0.2,0.3,0.4,0.6,0.4,1c0,0.3,0,0.8,0.1,1.2c0,0.3,0.1,0.6,0.1,1v0   c0.3,0.8,1.4,2.8,2.8-0.3c0,0,0.7-1.7,0.7-3.7C13.1,9.4,13.3,8.8,13.6,8.2z"/>
               <path className="st43" d="M15.9,9.2l0.2-0.4c0.1-0.1,0.2-0.3,0.3-0.5c0.2-0.3,0.3-0.7,0.5-1c0.3-0.7,0.4-1.5,0.4-2.3l-3.7-0.1         c0,0.3-0.1,0.7-0.2,1c-0.1,0.4-0.3,0.6-0.6,1.1L15.9,9.2z"/>
                    <path className="st44" d="M15.3,5.8l0.4,0.1c0-0.2,0.1-0.4,0.1-0.7l-0.4,0C15.3,5.4,15.3,5.6,15.3,5.8z"/>
                    <path className="st44" d="M14.9,6.9L15.3,7c0.1-0.2,0.2-0.4,0.2-0.6l-0.4-0.1C15.1,6.5,15,6.7,14.9,6.9z"/>
                    <path className="st44" d="M14.4,7.8L14.7,8c0.1-0.2,0.2-0.4,0.3-0.5l-0.3-0.2C14.6,7.5,14.5,7.7,14.4,7.8z"/>

                      <path className="st43" d="M17.4,5l0-0.5c0-0.1,0-0.4,0-0.6c0-0.4-0.1-0.9-0.3-1.3c-0.3-0.8-0.8-1.5-1.4-2.1l-2.6,2.7         c0.2,0.2,0.4,0.5,0.5,0.7c0,0.1,0.1,0.2,0.1,0.4c0,0.1,0,0.1,0,0.2l0,0.4L17.4,5z"/>

                    <path className="st44" d="M14.8,2.7l0.3-0.2c-0.1-0.2-0.3-0.4-0.4-0.5l-0.3,0.3C14.6,2.3,14.7,2.5,14.8,2.7z"/>
                    <path className="st44" d="M15.3,3.6l0.4-0.1c-0.1-0.2-0.1-0.4-0.2-0.6L15,3.1C15.1,3.3,15.2,3.5,15.3,3.6z"/>
                    <path className="st44" d="M15.4,4.7h0.4c0-0.2,0-0.4,0-0.7l-0.4,0C15.4,4.3,15.4,4.5,15.4,4.7z"/>

                      <path className="st43" d="M15.7,0.6c-0.6-0.6-1.3-1-2-1.3l-0.3-0.1c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.5-0.1-0.7-0.1         c-0.1,0-0.2,0-0.4,0l-0.2,0l-0.5,0l0,3.7c0.1,0,0.2,0,0.4,0l0.2,0l0.1,0c0,0,0.1,0,0.1,0l0.1,0l0.1,0.1         c0.3,0.1,0.6,0.3,0.8,0.5L15.7,0.6z"/>

                    <path className="st44" d="M12.2,0.9l0.1-0.4c-0.2,0-0.4,0-0.7,0l0,0.4C11.8,0.8,12,0.9,12.2,0.9z"/>
                    <path className="st44" d="M13.2,1.2l0.2-0.3c-0.2-0.1-0.4-0.2-0.6-0.3L12.6,1C12.8,1.1,13,1.1,13.2,1.2z"/>
                    <path className="st44" d="M14.1,1.8l0.2-0.3c-0.2-0.1-0.3-0.3-0.5-0.4l-0.2,0.3C13.8,1.6,13.9,1.7,14.1,1.8z"/>

                      <path className="st43" d="M11.3-1.1l-1,0l-0.9,0C9-1,8.5-1,8.1-1.1L7.7,2.7c0.7,0.1,1.4,0,2,0l0.5,0l0.3,0l0.7,0L11.3-1.1z"/>

                    <path className="st44" d="M8.8,1l0-0.4c-0.2,0-0.4,0-0.6,0l0,0.4C8.4,1,8.6,1,8.8,1z"/>
                    <polygon className="st44" points="9.9,0.9 9.9,0.5 9.3,0.6 9.3,1       "/>
                    <polygon className="st44" points="11,0.9 11,0.5 10.4,0.5 10.4,0.9       "/>

                      <path className="st43" d="M8.1-1.1L6.3-1.2c-0.3,0-0.7-0.1-0.9,0H4.9l-0.3,0c-0.1,0-0.2,0-0.4,0l0.4,3.7l0.1,0l0.1,0h0.4         c0.3,0,0.5,0,0.7,0c0.5,0,0.8,0.1,1.6,0.1L8.1-1.1z"/>

                    <polygon className="st44" points="5.4,0.8 5.4,0.4 4.8,0.4 4.8,0.8       "/>
                    <polygon className="st44" points="6.5,0.8 6.5,0.5 5.9,0.4 5.9,0.8       "/>
                    <polygon className="st44" points="7.6,0.9 7.7,0.5 7.1,0.5 7,0.9       "/>

                      <path className="st43" d="M4.3-1.2C3.9-1.2,3.3-1,3-0.9C2.6-0.7,2.3-0.5,2-0.3C1.3,0.1,0.7,0.6,0.2,1.2l2.9,2.3         C3.4,3.3,3.6,3,3.9,2.8c0.3-0.2,0.7-0.4,0.8-0.3L4.3-1.2z"/>

                    <path className="st44" d="M2.4,1.8L2.1,1.6C2,1.7,1.8,1.9,1.7,2l0.3,0.2C2.1,2.1,2.2,2,2.4,1.8z"/>
                    <path className="st44" d="M3.3,1.2L3.1,0.9C2.9,1,2.7,1.1,2.5,1.2l0.2,0.3C2.9,1.4,3.1,1.3,3.3,1.2z"/>
                    <path className="st44" d="M4.3,0.8L4.2,0.5C4,0.5,3.8,0.6,3.6,0.7L3.7,1C3.9,0.9,4.1,0.9,4.3,0.8z"/>

                      <path className="st43" d="M0.2,1.2C0,1.5-0.3,2-0.5,2.4c-0.2,0.5-0.3,0.9-0.3,1.4l0,0.9c0,0.4,0,0.8,0.1,1.1L3,5.4         C3,5.2,3,5,3,4.8L3,4c0-0.2,0-0.3,0.2-0.5L0.2,1.2z"/>

                    <path className="st44" d="M1.2,4.8l-0.4,0c0,0.2,0,0.4,0,0.7l0.4,0C1.2,5.2,1.2,5,1.2,4.8z"/>
                    <path className="st44" d="M1.3,3.6L0.9,3.6c0,0.2,0,0.5,0,0.7l0.4,0C1.2,4,1.2,3.8,1.3,3.6z"/>
                    <path className="st44" d="M1.6,2.7L1.3,2.5C1.2,2.6,1.1,2.8,1,3.1l0.4,0.1C1.4,3,1.5,2.8,1.6,2.7z"/>

                      <path className="st43" d="M-0.7,5.9C-0.6,6.7-0.4,7.4,0,8.1c0.2,0.4,0.3,0.6,0.4,0.8l0.4,0.7l3.3-1.8L3.3,6.4         C3.2,6.1,3,5.7,3,5.4L-0.7,5.9z"/>

                    <rect x="2" y="8" transform="matrix(0.8751 -0.484 0.484 0.8751 -3.7334 2.069)" className="st44" width="0.4" height="0.6"/>
                    <path className="st44" d="M1.6,6.9L1.3,7.1c0.1,0.2,0.2,0.4,0.3,0.6l0.3-0.2C1.8,7.3,1.7,7.1,1.6,6.9z"/>
                    <path className="st44" d="M1.3,5.9L0.9,5.9C1,6.1,1,6.4,1.1,6.6l0.4-0.1C1.4,6.3,1.4,6.1,1.3,5.9z"/>

                      <path className="st43" d="M0.8,9.6l0.1,0.2l0,0l0,0.7l0,1.6L4.7,12l0-1.5l0-0.9c0-0.5-0.2-1-0.4-1.5L4.1,7.8L0.8,9.6z"/>


                      <rect x="2.6" y="11.2" transform="matrix(0.9998 -1.784261e-02 1.784261e-02 0.9998 -0.2047 5.136523e-02)" className="st44" width="0.4" height="0.6"/>

                      <rect x="2.6" y="10.1" transform="matrix(0.9998 -1.784262e-02 1.784262e-02 0.9998 -0.1845 5.082545e-02)" className="st44" width="0.4" height="0.6"/>
                    <path className="st44" d="M2.7,8.9L2.4,9.1c0.1,0.2,0.2,0.3,0.2,0.5l0.4-0.1C2.9,9.3,2.8,9.1,2.7,8.9z"/>

                      <path className="st43" d="M1,12.1c0,0.7,0.1,1.5,0.4,2.2c0.3,1,1,2.1,1.7,2.7l1.9-3.8l0-0.1c-0.1-0.3-0.2-0.7-0.2-1.1L1,12.1z"/>

                    <path className="st44" d="M3.6,14.4l-0.3,0.3c0.1,0.2,0.2,0.4,0.4,0.6l0.2-0.3C3.8,14.7,3.7,14.6,3.6,14.4z"/>
                    <path className="st44" d="M3.2,13.4l-0.4,0.1c0.1,0.2,0.1,0.4,0.2,0.6L3.4,14C3.3,13.8,3.2,13.6,3.2,13.4z"/>
                    <path className="st44" d="M3,12.3l-0.4,0c0,0.2,0,0.4,0.1,0.7l0.4-0.1C3,12.7,3,12.5,3,12.3z"/>

                      <path className="st43" d="M3.1,17c0.7,0.5,1.4,0.8,2.3,0.8c1.1,0,2.1-0.6,2.7-1.5l-3.1-3.1L3.1,17z"/>

                    <path className="st44" d="M6.1,15.4l0.2,0.4c0.2-0.1,0.3-0.3,0.4-0.5L6.4,15C6.3,15.1,6.2,15.3,6.1,15.4z"/>
                    <path className="st44" d="M5.1,15.7l0,0.4l0.1,0c0.2,0,0.4,0,0.6-0.1l-0.1-0.4C5.5,15.7,5.3,15.7,5.1,15.7L5.1,15.7z"/>
                    <path className="st44" d="M4.2,15.2L4,15.6c0.2,0.2,0.4,0.3,0.6,0.4l0.1-0.4C4.5,15.5,4.3,15.4,4.2,15.2z"/>

                      <path className="st43" d="M8,16.2c0.3-0.4,0.4-0.9,0.5-1.4c0.1-0.3,0.1-0.5,0.1-0.8l0-0.8l0-0.9l0-0.2l0,0.1c0,0,0,0.1,0,0.1         l-3.4-1.6C5.2,11,5.1,11.3,5,11.6c0,0.2,0,0.3,0,0.5l0,0.2l0,0.8l0,0.1L8,16.2z"/>

                    <path className="st44" d="M6.7,12.4l0.4,0c0-0.2,0-0.4,0.1-0.6l-0.4-0.1C6.7,11.9,6.7,12.2,6.7,12.4z"/>
                    <polygon className="st44" points="6.7,13.5 7,13.5 7.1,12.9 6.7,12.9       "/>
                    <path className="st44" d="M6.6,14.5l0.3,0.3C7,14.6,7,14.3,7,14.1l-0.4-0.2C6.6,14.1,6.6,14.3,6.6,14.5z"/>

                      <path className="st43" d="M8.7,12.3c0,0.1-0.1,0.1-0.1,0.1c0,0,0,0-0.1,0.1c-0.1,0-0.3,0-0.4,0c-0.3-0.1-0.4-0.2-0.4-0.2         c0,0,0-0.1,0-0.1l3.6-0.9c-0.1-0.6-0.4-1.1-0.8-1.6c-0.3-0.4-0.9-0.9-1.8-1C7.7,8.7,6.9,9,6.5,9.3C6,9.6,5.6,10.1,5.3,10.7         L8.7,12.3z"/>

                    <path className="st44" d="M9.1,11l-0.3,0.3c0.1,0.1,0.2,0.3,0.3,0.4l0.3-0.1C9.4,11.3,9.3,11.1,9.1,11z"/>
                    <path className="st44" d="M8,10.5l0.1,0.4c0.2,0,0.3,0,0.5,0.1l0.2-0.3C8.5,10.5,8.2,10.5,8,10.5z"/>
                    <path className="st44" d="M7,11.2l0.3,0.2c0.1-0.1,0.2-0.3,0.4-0.4l-0.2-0.3C7.3,10.9,7.1,11,7,11.2z"/>

                      <path className="st43" d="M7.6,12.3L7.6,12.3l0,0.1l0,0.4c0,0.3,0,0.5,0.1,0.9l0.1,1.1l0,0.1l0,0l0,0L8,15.2l0,0l0,0l0.1,0.1         l0.1,0.3c0.1,0.2,0.2,0.4,0.3,0.5l3.1-2.1c0,0-0.1-0.1-0.1-0.2l-0.1-0.1l0-0.1l0,0l0,0l0,0c0.1,0.2-0.1-0.4,0.1,0.4l0,0l0,0         l0,0l0,0c0-0.2,0-0.4-0.1-0.6c0-0.2,0-0.6-0.1-0.8l0-0.4l0-0.3c0-0.1,0-0.3-0.1-0.4L7.6,12.3z"/>

                    <path className="st44" d="M9.8,14.3l-0.4,0.1c0.1,0.3,0.2,0.4,0.3,0.6l0.3-0.2C10,14.7,9.8,14.5,9.8,14.3z"/>
                    <path className="st44" d="M9.7,13.2l-0.4,0c0,0.2,0,0.4,0.1,0.6l0.4,0C9.7,13.6,9.7,13.4,9.7,13.2z"/>
                    <polygon className="st44" points="9.6,12.1 9.2,12.1 9.3,12.7 9.7,12.7       "/>


                      <path className="st43" d="M8.5,16.2c0.3,0.4,0.6,0.8,1.1,1.2c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.4,0.1,0.6,0.2c0.2,0,0.5,0,0.7,0         c0.2,0,0.4-0.1,0.6-0.2c0.7-0.3,1.1-0.7,1.4-1c0.3-0.3,0.5-0.7,0.7-1L11,13.7c-0.1,0.2-0.2,0.3-0.3,0.4         c-0.1,0.1-0.1,0.1,0.1,0c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.2,0.1c0.1,0,0.1,0,0.1,0.1c0.1,0.1,0,0,0-0.1         L8.5,16.2z"/>

                    <path className="st44" d="M12,15.2l0.3,0.3c0.2-0.2,0.3-0.3,0.4-0.5l-0.3-0.2C12.3,14.9,12.2,15.1,12,15.2z"/>
                    <path className="st44" d="M11.2,15.7l0,0.4c0.3,0,0.5-0.1,0.7-0.2l-0.2-0.3C11.5,15.7,11.3,15.7,11.2,15.7z"/>
                    <path className="st44" d="M10.3,15.2L10,15.5c0.1,0.2,0.3,0.3,0.5,0.5l0.2-0.3C10.6,15.5,10.5,15.4,10.3,15.2z"/>

                      <path className="st43" d="M14.2,15.6c0.2-0.3,0.3-0.6,0.5-1c0.1-0.3,0.2-0.6,0.3-1c0.2-0.6,0.3-1.3,0.4-1.9l-3.7-0.5         c-0.1,0.5-0.2,0.9-0.3,1.4c-0.1,0.2-0.1,0.4-0.2,0.6c0,0.1-0.2,0.3-0.3,0.5L14.2,15.6z"/>

                    <path className="st44" d="M13.4,12.2l0.4,0.1c0-0.2,0.1-0.4,0.1-0.6l-0.4-0.1C13.5,11.8,13.4,12,13.4,12.2z"/>
                    <path className="st44" d="M13.1,13.3l0.4,0.1c0.1-0.2,0.1-0.4,0.2-0.6l-0.4-0.1C13.2,12.9,13.1,13.1,13.1,13.3z"/>
                    <path className="st44" d="M12.6,14.3l0.3,0.2c0.1-0.2,0.2-0.4,0.3-0.6l-0.3-0.1C12.8,14,12.7,14.1,12.6,14.3z"/>

                      <path className="st43" d="M15.5,11.7c0-0.3,0.1-0.7,0.1-1l0-0.5c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.5,0.3-0.8l-3.1-2         c-0.4,0.7-0.8,1.5-0.9,2.4c0,0.2,0,0.5,0,0.6l0,0.3c0,0.2,0,0.4-0.1,0.7L15.5,11.7z"/>

                    <path className="st44" d="M13.8,8.9L14.2,9c0.1-0.2,0.2-0.4,0.2-0.5l-0.3-0.2C14,8.5,13.9,8.7,13.8,8.9z"/>
                    <path className="st44" d="M13.6,10l0.4,0c0-0.2,0-0.4,0.1-0.6l-0.4-0.1C13.6,9.6,13.6,9.8,13.6,10z"/>
                    <path className="st44" d="M13.6,11.1l0.4,0c0-0.2,0-0.4,0.1-0.6l-0.4,0C13.6,10.7,13.6,10.9,13.6,11.1z"/>

          <path className="st45" d="M6.5,2.8c0,0-1.1-1.1-2.7,1.1"/>
        </svg>
        </span>
          </div>
          <span
            style={{
              position: 'absolute',
              bottom: '0px',
              right: '0px',
              fontSize: '8px',
              fontWeight: '800',
              backgroundColor: 'white',
              // borderRadius:'0.125rem' ,
              borderTopLeftRadius: '0.5rem' ,
              borderBottomRightRadius: '0.5rem' ,
              color:'#3799CE',
              padding:'3px  0px 1px 2px' ,
            }}
            className="h-[14px] w-[14px] "
          >
             Fl
          </span>
        </Button>
          </Tooltip>
         <Tooltip
           label="Sealant"
           withArrow
           className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
             >
           <Button
             styles={{
               root: {
                 position: 'relative',
                 color: 'white',
                 height: '35px', // Adjust button height
                 width: '35px',  // Adjust button width
                 padding: 0,
                 borderRadius: '0.5rem'
               },
             }}
           >
             <div
         style={{
           display: 'flex',
           justifyContent: 'center',
           alignItems: 'center',
           height: '100%',
           width: '100%',
         }}
         >
           <span  className={
           activeButton === 'viewModeSealant'
             ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-0.5 hover:bg-[#3799CE]"
             : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-0.5 hover:bg-[#3799CE]"
         }
         onClick={() => {
           handleButtonClick("viewModeSealant");
           setTargetPath("19");
         }}
             >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 120" x="0px" y="0px">
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M75.49,77.49a7.21,7.21,0,0,1-4.16-13.11c-1.58-6,5.88-10.32,10.35-6a37.65,37.65,0,0,1,3.69-5.46c-.71-1.52-1.61-2.18-3.6-2.86a2.7,2.7,0,0,1,0-5.1c2.45-.85,3.25-1.65,4.09-4.1a2.71,2.71,0,0,1,5.1,0s0,.09.05.13A23.94,23.94,0,0,0,50,20.56a25,25,0,0,1-14.71,7.55h-.14a1,1,0,0,1-.13-2,23,23,0,0,0,13.53-6.94,19.87,19.87,0,0,0-1.9-1.53A24.42,24.42,0,0,1,37.89,23a1,1,0,0,1-.67-1.88,22.65,22.65,0,0,0,7.71-4.63A23.93,23.93,0,0,0,9.66,43.78a9.17,9.17,0,0,1,3.44-.2,20.81,20.81,0,0,1,1-15.7,1,1,0,0,1,1.34-.46,1,1,0,0,1,.45,1.34,18.8,18.8,0,0,0-.44,15.48,8.86,8.86,0,0,1,4,3.48,6.24,6.24,0,0,1,6.9,6.13,7.58,7.58,0,1,1-3.63,14.66A40,40,0,0,1,25,77.34l2,11.24a7.25,7.25,0,0,0,14.41-.39L43,72.86A7,7,0,0,1,45.49,68a13.51,13.51,0,0,0-8.1,6,1,1,0,0,1-1.73-1c6-10.41,23.61-10,29,.58a1,1,0,1,1-1.78.91A13.27,13.27,0,0,0,54.48,68,7,7,0,0,1,57,72.86L58.5,87.92a7.53,7.53,0,0,0,15,.4L75,81.24C75.47,77.29,75.59,77.49,75.49,77.49ZM43.56,52.92a5.83,5.83,0,0,1-4.05-1.64,6,6,0,1,1-1-11.78A6,6,0,1,1,49,44.89,5.87,5.87,0,0,1,43.56,52.92ZM63.4,17.48a16.32,16.32,0,0,1,17.36,4.63A1,1,0,0,1,80,23.77a1,1,0,0,1-.75-.34A14.34,14.34,0,0,0,64,19.38a1,1,0,0,1-.64-1.9Zm2.9,21.43c-.91-2.92-1.63-3.65-4.56-4.56a2.46,2.46,0,0,1,0-4.69c2.93-.92,3.65-1.64,4.56-4.56a2.46,2.46,0,0,1,4.7,0c.91,2.92,1.63,3.64,4.56,4.56a2.46,2.46,0,0,1,0,4.69c-2.93.91-3.65,1.64-4.56,4.56A2.46,2.46,0,0,1,66.3,38.91Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M69.09,38.31c1.1-3.53,2.33-4.76,5.87-5.87a.46.46,0,0,0,0-.87c-3.54-1.11-4.77-2.34-5.87-5.87a.46.46,0,0,0-.88,0c-1.1,3.53-2.33,4.76-5.87,5.87a.45.45,0,0,0,0,.87c3.54,1.11,4.77,2.34,5.87,5.87A.46.46,0,0,0,69.09,38.31Z"/>
                   <circle cx="5.23" cy="71.15" r="4.1" style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}}/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M20.93,7.28a3.09,3.09,0,1,0-3.09-3.09A3.09,3.09,0,0,0,20.93,7.28Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M44.11,37.54A3.93,3.93,0,0,0,40.2,42a3.95,3.95,0,1,0,0,6.91,3.87,3.87,0,0,0,7.25-1.87,3.82,3.82,0,0,0-.89-2.43A4,4,0,0,0,44.11,37.54Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M94.4,46.8c-3-1-4.29-2.29-5.33-5.33a.7.7,0,0,0-1.32,0c-1,3-2.29,4.29-5.33,5.33a.7.7,0,0,0,0,1.32c3,1,4.29,2.29,5.33,5.33a.7.7,0,0,0,1.32,0c1-3,2.29-4.29,5.33-5.33A.7.7,0,0,0,94.4,46.8Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M30.63,62.61a5.56,5.56,0,0,0-6.73-6.68,4.22,4.22,0,0,0-5.45-5.86,7,7,0,1,0-6.89,9.39,5.2,5.2,0,0,0,8.88,4.78A5.58,5.58,0,0,0,30.63,62.61Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M81.47,64a4.24,4.24,0,1,0-7.54,1.36A5.21,5.21,0,1,0,80,72.84,5.57,5.57,0,1,0,81.47,64Z"/>
                 </svg>
           </span>
             </div>
             <span
               style={{
                 position: 'absolute',
                 bottom: '0px',
                 right: '0px',
                 fontSize: '8px',
                 fontWeight: '800',
                 backgroundColor: 'white',
                 borderTopLeftRadius: '0.5rem' ,
                 borderBottomRightRadius: '0.5rem' ,
                 color:'#3799CE',
                 padding:'3px  0px 1px 2px' ,
               }}
               className="h-[14px] w-[14px] "
             >
                 Se
             </span>
           </Button>
           </Tooltip>
         <Tooltip
          label="Whitening"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Button
            styles={{
              root: {
                position: 'relative',
                color: 'white',
                height: '35px',
                width: '35px',
                padding: 0,
                borderRadius: '0.5rem',
              },
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
              }}
            >
              <span
                className={
                  activeButton === 'viewModeWhitening'
                    ? "block h-[35px] w-[35px] rounded-md bg-[#3799CE] hover:bg-[#3799CE]"
                    : "block h-[35px] w-[35px] rounded-md bg-[#5A5A5A] hover:bg-[#3799CE]"
                }
                onClick={() => {
                  handleButtonClick("viewModeWhitening");
                  handleToggleWhitening();
                }}
              >
          <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" enableBackground="new 0 0 100 100" >
                <g>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M48.146,22.038c-23.132-9.042-35.014,2.7-26.877,27.101c1.381,4.139,11.02,34.895,17.751,32.745   c6.173-1.973,7.039-14.389,10.76-17.195c5.452,0.963,5.986,20.604,13.325,17.041c8.523-4.139,15.563-30.213,17.313-38.475   c1.656-7.838,2.085-24.431-9.303-25.353C62.975,17.24,56.018,22.238,48.146,22.038z"/>
                  </g>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M82.689,5.845c0,9.515-9.79,9.789-9.79,9.789s9.79,0.274,9.79,9.789c0-9.515,9.789-9.789,9.789-9.789  S82.689,15.36,82.689,5.845z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M18.71,64.422c0,9.113-9.376,9.376-9.376,9.376s9.376,0.262,9.376,9.374c0-9.112,9.375-9.374,9.375-9.374  S18.71,73.535,18.71,64.422z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M18.71,7.758c0,7.655-7.876,7.875-7.876,7.875s7.876,0.22,7.876,7.875c0-7.654,7.875-7.875,7.875-7.875  S18.71,15.414,18.71,7.758z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M81.211,61.422c0,7.656-7.877,7.876-7.877,7.876s7.877,0.22,7.877,7.874c0-7.654,7.875-7.874,7.875-7.874  S81.211,69.078,81.211,61.422z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M57.148,4.84c0,6.32-6.502,6.501-6.502,6.501s6.502,0.181,6.502,6.499c0-6.318,6.5-6.499,6.5-6.499  S57.148,11.16,57.148,4.84z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M28.898,76.672c0,6.32-6.502,6.502-6.502,6.502s6.502,0.181,6.502,6.498c0-6.317,6.5-6.498,6.5-6.498  S28.898,82.992,28.898,76.672z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M85.712,51.5c0,6.32-6.502,6.502-6.502,6.502s6.502,0.181,6.502,6.498c0-6.317,6.5-6.498,6.5-6.498  S85.712,57.82,85.712,51.5z"/>
                  <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M13.462,22c0,4.862-5.002,5.001-5.002,5.001s5.002,0.14,5.002,4.999c0-4.859,5-4.999,5-4.999S13.462,26.862,13.462,22z"/>
                </svg>
              </span>
            </div>
            <span
              style={{
                position: 'absolute',
                bottom: '0px',
                right: '0px',
                fontSize: '8px',
                fontWeight: '800',
                backgroundColor: 'white',
                borderTopLeftRadius: '0.5rem',
                borderBottomRightRadius: '0.5rem',
                color: '#3799CE',
                padding: '3px 0px 1px 2px',
              }}
              className="h-[14px] w-[14px]"
            >
              Wh
            </span>
          </Button>
        </Tooltip>


        </div>
      </Flex>
     </div>
      {isAlertVisible && isPathSelectionActive && clickedIds.length > 0 &&(
      <div className="flex justify-between border-2 p-1 px-1">
        <Alert
          title={`SelectTeeth : ${getFormattedOutput() || ""}`}
          icon={icon}
          withCloseButton
          onClose={() => setIsAlertVisible(false)} // Close alert
          w={"100%"}
          bg={"cyan"}
          color="white"
        ></Alert>
      </div>
    )}
    <div className=" h-full px-0   border-t-[3px] my-2">
    <Flex style={{ position: 'relative', width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:220}}>
        <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
          </div>
      </Flex>
    <div className=" max-w-[920px] mt-2 mx-auto">
    <div className=" flex h-full px-0  max-h-[320px] ">
                        {Dantal.map((svgData) => (
              <div key={svgData.svg_id} className="h-[230px] cursor-pointer hover:bg-[#F2F5F8]" onClick={() => handleSvgClick(svgData.svg_id)}>
                {!hiddenSvgIds.includes(svgData.svg_id) ? (
                  <DentalSvg
                    svgData={svgData}
                    isHidingMode={isHidingMode}
                    highlightedPaths={highlightedPaths}
                    onPathClick={onPathClick}
                    isPathSelectionActive={isPathSelectionActive}
                    hiddenPaths={hiddenPaths}
                    isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                    isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                    isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                  />
                ) : (
                  <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
                )}
                <div className="mt-0.5 flex">
                  <Text ta="center" h={30} className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE]" style={{ width: svgData.width }}>
                    {svgData.svg_id}
                  </Text>
                </div>
              </div>
            ))}

         </div>
        </div>
        </div>
        <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
        <div className=" h-full px-0   border-b-[3px]">
           <Flex style={{ position: 'relative',  width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:14}}>
        <Image
              src={dentalButtom}
              alt={"dentalButtom"}
              width={32}
              height={32}
            />
          </div>
      </Flex>
    <div className=" max-w-[920px]  mx-auto">
        <div className=" flex h-full px-0  max-h-[320px] ">
              {DantalB.map((svgData) => (
              <div key={svgData.svg_id} className="h-[230px] cursor-pointer hover:bg-[#F2F5F8]" onClick={() => handleSvgClick(svgData.svg_id)}>
                {!hiddenSvgIds.includes(svgData.svg_id) ? (
                  <DentalSvg
                    svgData={svgData}
                    isHidingMode={isHidingMode}
                    highlightedPaths={highlightedPaths}
                    onPathClick={onPathClick}
                    isPathSelectionActive={isPathSelectionActive}
                    hiddenPaths={hiddenPaths}
                    isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                    isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                    isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                  />
                ) : (
                  <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
                )}
                <div className="mt-0.5 flex">
                  <Text ta="center" h={30} className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE]" style={{ width: svgData.width }}>
                    {svgData.svg_id}
                  </Text>
                              </div>
                            </div>
                          ))}
        </div>
        </div>
        </div>
        {/* <Divider  my="8px" variant="dashed" /> */}
      <div className="mx-auto w-[880px] max-w-[880px] mb-4 ">
      <br/>
      <Button onClick={toggle} className="ButtonHover  w-full">Register </Button>
      </div>

    <div className="border-base-200 mx-4 border-t mt-4">
      <Tabs
        variant="unstyled"
        defaultValue="Therapy"
        classNames={classes}
      >
            <Tabs.List grow className="space-x-0 gap-0   mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={
                  <IconMessageCircle
                    style={{ width: rem(16), height: rem(16) }}
                  />
                }
              >
               All Procedures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={
                  <IconSettings style={{ width: rem(16), height: rem(16) }} />
                }
              >
               Planned
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
              Completed
              </Tabs.Tab>
              <Tabs.Tab
                value="Inventory"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
                Inventory
              </Tabs.Tab>
            </Tabs.List>
             <Tabs.Panel value="All Procedures" className=" mt-2">
           <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsAllProcedures}</Table.Tbody>
              </Table>
              </Tabs.Panel>
            <Tabs.Panel value="Planned" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsPlanned}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Completed" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsCompleted}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Inventory" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsInventory}</Table.Tbody>
              </Table>
            </Tabs.Panel>
      </Tabs>
      <Button rightSection={<MdPrint size={14} />} className="ButtonHover  " mt={6}>Imprimer</Button>
      </div>

      <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
          < IconDeviceFloppy stroke={2} />
      <Text size="sm" mb="xs" fw={500}>
      Save Modification
      </Text>
      </Group>
      <Group align="flex-end">
        <Button w={"100%"} onClick={sendPathsToApi} className="hover:bg-[#3799CE]/90">save</Button>
      </Group>
    </Dialog>
      </div>
        </Tabs.Panel>
        <Tabs.Panel value="Therapy">
        <div className="my-4 ">
        <Container>
      <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px'}} align="center">
        <div  style={{ position: 'absolute', right: 0 }}>
          <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>All Procedures</Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Download zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Preview all
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Delete all
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          </div>
        <div style={{ margin: '0 auto' }} className=" mb-2 flex flex-end p-2 sm:justify-start space-x-2 ">

        <Menu shadow="md" width={210}  trigger="hover" openDelay={100} closeDelay={400}>
                   <Tooltip
                         label="Restoratin استعادة"
                         withArrow
                         className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       >
                   <Menu.Target>
                         <Button
                   styles={{
                     root: {
                       position: 'relative',
                       color: 'white',
                       height: '35px', // Adjust button height
                       width: '35px',  // Adjust button width
                       padding: 0,
                       borderRadius: '0.5rem'
                     },
                   }}
                 >
                   {/* SVG in the middle */}
                   <div
                   style={{
                     display: 'flex',
                     justifyContent: 'center',
                     alignItems: 'center',
                     height: '100%',
                     width: '100%',
                   }}
                 >

                 <span  className={
                   (  activeButton === "RestoratinPermanent" ||
                     activeButton === 'RestoratinTemporary' ||
                     activeButton === 'RestoratinAmalgam' ||
                     activeButton === 'RestoratinGlassIonomer'||
                       isHidingMode
                   )
                     ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                     : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
                 }
                 onClick={() =>
                   setChecked(!checked) // Toggle checked state
                   }>


                 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 45" x="0px" y="0px">
                   <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="M19.4,12a6.26611,6.26611,0,0,0-2.83.91986,7.50773,7.50773,0,0,1-3.57,1.08,7.50773,7.50773,0,0,1-3.57-1.08A6.26611,6.26611,0,0,0,6.6,12C3.25,12,2,16.28955,2,18.99933a19.1719,19.1719,0,0,0,1.73,8.48926L7.13,33.488a.9904.9904,0,0,0,.78.51,1.02437,1.02437,0,0,0,.86005-.36l3.62-4.34961a.8045.8045,0,0,1,1.22,0L17.23,33.638a1.00772,1.00772,0,0,0,.77.36h.09a.9904.9904,0,0,0,.78-.51l3.41-6.01947A19.177,19.177,0,0,0,24,18.99933C24,16.28955,22.75,12,19.4,12Z"/>
                   <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="M33,3H21a1.003,1.003,0,0,0-1,1H17.82A3.01033,3.01033,0,0,0,15,2H11A3.00879,3.00879,0,0,0,8,5V7A1.003,1.003,0,0,0,9,8h3v3a1,1,0,0,0,2,0V8h3a1.003,1.003,0,0,0,1-1V6h2a1.003,1.003,0,0,0,1,1h9V17a1.003,1.003,0,0,0,1,1h2a1.003,1.003,0,0,0,1-1V4A1.003,1.003,0,0,0,33,3Z"/>
                   </svg>

                         </span>
                   </div>
                   {/* "CL" in the bottom-right corner */}
                   <span
                      style={{
                       position: 'absolute',
                       bottom: '0px',
                       right: '0px',
                       fontSize: '8px',
                       fontWeight: '800',
                       backgroundColor: 'white',
                       // borderRadius:'0.125rem' ,
                       borderTopLeftRadius: '0.5rem' ,
                       borderBottomRightRadius: '0.5rem' ,
                       color:'#3799CE',
                        padding:'3px  0px 1px 2px' ,
                     }}
                     className="h-[14px] w-[14px] "
                   >
                       Re
                   </span>
                 </Button>
                   </Menu.Target>
                   </Tooltip>
                   <Menu.Dropdown>
                   <Menu.Label>Restoratin</Menu.Label>
                   <Menu.Item className={
                     activeButton === 'RestoratinPermanent'
                         ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                         : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                     }
                     onClick={() => {
                       handleButtonClick("RestoratinPermanent");
                       const isValidSelection = selectedTeeth.every(tooth => isOnlyallowed(tooth));
                       // Always set the selected color to ensure the radio reflects the change
                       if (selectedColor !== "#FF4444") {
                         handleColorSelect("#8E1616", "fill");
                       }
                       // Apply color only if the selection is valid
                       if (isValidSelection) {
                         handleColorSelect("#8E1616", "fill");
                       }
                     }}
                   >
                     <Group  className="h-[28px] w-[186.4px] " >
                     <Radio
                         checked={activeButton === 'RestoratinPermanent' }
                         onChange={(event) =>
                         setChecked(event.currentTarget.checked)
                         }
                         icon={CheckIcon}
                     />
                     <div className="flex">
                     <Avatar
                       color="blue"
                       radius="sm"
                       style={{ width: "28px", height: "28px" }}
                         px={0.5}
                     >
                         <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 640" x="0px" y="0px">
                         <path style={{fill:"#3799CE"}} d="M479.9,25.19,472,17.3a19.2,19.2,0,0,0-27.11,0L304.61,157.6a13.29,13.29,0,0,0-14.21,3l-95.3,95.31a13.27,13.27,0,0,0-3,14.21l-15.87,15.86c-9.78,9.79-29.57,32-25,47.51L95.65,389.06a9.3,9.3,0,0,1-2.91,2L38.43,414.27a19.68,19.68,0,0,0-9.6,27.39l27.79,56.95.06,0a2,2,0,0,0,.52.84,3,3,0,0,0,5-3.09c-1.19-3.86-16.28-58-17.65-65.12a2,2,0,0,1,.87-.7l54.31-23.24a26.76,26.76,0,0,0,8.45-5.72l55.61-55.61a17.16,17.16,0,0,0,4.87.68c15,0,33.84-16.89,42.64-25.68l15.86-15.87a13.22,13.22,0,0,0,14.21-3l95.31-95.3a13.29,13.29,0,0,0,3-14.21L479.89,52.31A19.2,19.2,0,0,0,479.9,25.19ZM312.47,163.88,442.34,34l20.85,20.85L333.32,184.73ZM103.9,397.31a20.78,20.78,0,0,1-6.57,4.44L43,425a8,8,0,0,0-4.42,4.74c-.42,1.23-.61,1.79,8.67,35.58.1.35.2.7.29,1.05L34.2,439l-.06-.11a13.68,13.68,0,0,1,6.65-19.08L95.1,396.54a14.86,14.86,0,0,0,4.79-3.24L154.23,339c.32.39.67.78,1,1.15l1.81,1.81c.37.38.76.72,1.15,1.05Zm100.29-83.45c-14.7,14.7-33.5,27.54-40,21l-1.81-1.81c-6.54-6.54,6.3-25.34,21-40l15.07-15.07,20.86,20.85ZM329.57,199.73,234.26,295a3.27,3.27,0,0,1-4.61,0l-27.48-27.48a3.27,3.27,0,0,1,0-4.61l95.3-95.3a3.26,3.26,0,0,1,4.61,0l27.49,27.49A3.27,3.27,0,0,1,329.57,199.73ZM472.82,45.23l-5.39,5.39L446.58,29.77,452,24.38a9.18,9.18,0,0,1,13,0l7.89,7.88A9.18,9.18,0,0,1,472.82,45.23Z"/>
                         <path style={{fill:"#3799CE"}} d="M252,230.31a5,5,0,1,0-3.54-1.47A5,5,0,0,0,252,230.31Z"/>
                         <path style={{fill:"#3799CE"}} d="M266.09,216.16a5,5,0,0,0,3.54-1.47,5,5,0,1,0-7.08,0A5,5,0,0,0,266.09,216.16Z"/>
                         <path style={{fill:"#3799CE"}} d="M237.81,244.44a5,5,0,1,0-3.54-1.46A5,5,0,0,0,237.81,244.44Z"/>
                         <path style={{fill:"#3799CE"}} d="M227.2,250.06a5,5,0,1,0,0,7.06A5,5,0,0,0,227.2,250.06Z"/>
                         <path style={{fill:"#3799CE"}} d="M280.23,202a5,5,0,1,0-3.53-1.47A5,5,0,0,0,280.23,202Z"/>
                         <path style={{fill:"#3799CE"}} d="M294.38,187.87a4.93,4.93,0,0,0,3.53-1.46,5,5,0,1,0-7.07,0A4.94,4.94,0,0,0,294.38,187.87Z"/>
                         <path style={{fill:"#3799CE"}} d="M237.17,260a5,5,0,1,0,0,7.07A5,5,0,0,0,237.17,260Z"/>
                         <path style={{fill:"#3799CE"}} d="M307.88,189.32a5,5,0,1,0,0,7.07A5,5,0,0,0,307.88,189.32Z"/>
                         <path style={{fill:"#3799CE"}} d="M279.6,217.6a5,5,0,1,0,0,7.08A5,5,0,0,0,279.6,217.6Z"/>
                         <path style={{fill:"#3799CE"}} d="M293.74,203.47a5,5,0,1,0,0,7.06A5,5,0,0,0,293.74,203.47Z"/>
                         <path style={{fill:"#3799CE"}} d="M265.46,231.74a5,5,0,1,0-7.07,7.08,5,5,0,0,0,7.07,0A5,5,0,0,0,265.46,231.74Z"/>
                         <path style={{fill:"#3799CE"}} d="M251.31,245.89a5,5,0,0,0-7.07,7.06,5,5,0,0,0,7.07-7.06Z"/>
                         <path style={{fill:"#3799CE"}} d="M296.64,213.44a5,5,0,1,0,7.08,0A5,5,0,0,0,296.64,213.44Z"/>
                         <path style={{fill:"#3799CE"}} d="M310.79,199.3a5,5,0,1,0,7.07,0A5,5,0,0,0,310.79,199.3Z"/>
                         <path style={{fill:"#3799CE"}} d="M240.08,270a5,5,0,1,0,7.07,0A5,5,0,0,0,240.08,270Z"/>
                         <path style={{fill:"#3799CE"}} d="M254.22,255.86a5,5,0,1,0,7.07,0A5,5,0,0,0,254.22,255.86Z"/>
                         <path style={{fill:"#3799CE"}} d="M268.36,241.72a5,5,0,0,0,3.54,8.54,4.92,4.92,0,0,0,3.53-1.48,5,5,0,0,0-7.07-7.06Z"/>
                         <path style={{fill:"#3799CE"}} d="M282.5,227.57a5,5,0,1,0,7.07,0A5,5,0,0,0,282.5,227.57Z"/>

                         </svg>
                       </Avatar>
                       <Text fw={500} ml={6}> Permanent</Text>
                         </div>
                   </Group>
                       </Menu.Item>
                   <Menu.Item className={
                     activeButton === 'RestoratinTemporary'
                           ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                           : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                       }onClick={() => {
                         handleButtonClick("RestoratinTemporary");
                         setTargetPath("25");
                       }}
                     >
                 <Group  className="h-[28px] w-[186.4px] " >
                 <Radio
                     checked={activeButton === 'RestoratinTemporary'}
                     onChange={(event) =>
                     setChecked(event.currentTarget.checked)
                     }
                     icon={CheckIcon}
                 />
                 <div className="flex">
                 <Avatar
                   color="blue"
                   radius="sm"
                   style={{ width: "28px", height: "28px" }}
                    px={0.5}
                 >
                 <svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 0 60 40" x="0px" y="0px">
                   <path style={{fill:"#3799CE"}} d="M9.27,41.15a4.47,4.47,0,0,0,7.61-2.75l.8-8.77a1.63,1.63,0,0,1,.52-1,1.6,1.6,0,0,1,2.68,1l.8,8.77A4.47,4.47,0,0,0,30.6,38v-.22a11.49,11.49,0,1,0,7.92-21.18,11.46,11.46,0,0,0-.15-1.74A11.34,11.34,0,0,0,19.28,8.77,11.26,11.26,0,0,0,9.51,5.68,11.31,11.31,0,0,0,4,25.49,10.83,10.83,0,0,1,8,33.69V38A4.42,4.42,0,0,0,9.27,41.15ZM46,27.9a9.5,9.5,0,1,1-9.5-9.5A9.51,9.51,0,0,1,46,27.9ZM5.33,24A9.32,9.32,0,1,1,18.51,10.89a1,1,0,0,0,.77.36h0a1,1,0,0,0,.77-.36,9.33,9.33,0,0,1,16.35,4.3,8.27,8.27,0,0,1,.12,1.21h0A11.49,11.49,0,0,0,28.6,36.26V38a2.46,2.46,0,0,1-4.13,1.82,2.45,2.45,0,0,1-.8-1.59l-.8-8.77a3.58,3.58,0,0,0-3.59-3.28,3.6,3.6,0,0,0-3.59,3.28l-.8,8.77a2.47,2.47,0,0,1-4.21,1.51A2.41,2.41,0,0,1,10,38v-4.3A12.8,12.8,0,0,0,5.33,24Z"/><path d="M27.29,12l-1.42-1.41a9.33,9.33,0,0,1-13.18,0L11.28,12a11.33,11.33,0,0,0,16,0Z"/>
                   <path style={{fill:"#3799CE"}} d="M35.48,19.4v8.5a1,1,0,0,0,1,1H43.1v-2H37.48V19.4Z"/>
                 </svg>
                  </Avatar>
                  <Text fw={500} ml={6}> Temporary</Text>
                     </div>
                 </Group>
                   </Menu.Item>
                   <Menu.Item className={
                  activeButton === 'RestoratinAmalgam'
                     ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                     : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                 }
                   onClick={() => {
                     handleButtonClick("RestoratinAmalgam");
                     setTargetPath("26");

                 }}
               >
                 <Group  className="h-[28px] w-[186.4px] " >
                 <Radio
                     checked={activeButton === 'RestoratinAmalgam'}
                     onChange={(event) =>
                     setChecked(event.currentTarget.checked)
                     }
                     icon={CheckIcon}
                 />
                 <div className="flex">
                 <Avatar
                   color="blue"
                   radius="sm"
                   style={{ width: "28px", height: "28px" }}
                    px={0.5}
                 >
                 <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="30 0 250 270" >
                 <path style={{fill:"#3799CE",stroke:"#3799CE"}} d="M203.5,76.9c-8.1-7.5-16.2-10.8-26.6-10.8c-8.3,0-12.5,1.7-17.8,3.8c-3.6,1.5-7.7,3.1-14.1,4.6c-8.9-3.3-18.1-6.4-27.7-5.5  c-11.8,1-23,8.5-28.5,19c-1.4,2.6-2.4,5.3-3.1,8.1c-0.2,0.4-0.3,0.8-0.3,1.2c-2.3,9.9-0.8,20.2,1.1,27.8c2.6,10.3,6.4,20.3,10.1,30  l1.8,4.6c0,0.1,4.3,12.4,5.4,32.3c0.4,8.1,3.1,18.4,6.7,26.1c4.8,10.3,11,15.7,17.8,15.7c0.2,0,0.4,0,0.6,0c2.6-0.1,4.8-1.2,6.5-3.3  c6-7.1,4.9-24.5,3.7-34.3c-0.4-3.1,0-6.1,1-8.6c1.6-3.9,4.1-6,7.7-6.4c0,0,1-0.1,2.5-0.1c1.5,0,2.7,0.2,2.8,0.2  c3.4,0.4,6,2.6,7.6,6.4c1.1,2.6,1.4,5.6,1,8.6c-1.2,9.7-2.3,27.1,3.7,34.3c1.7,2.1,3.9,3.2,6.5,3.3c3.7,0.1,7.4-1.3,10.7-4.4  c9.3-8.6,14.1-27.4,14.4-37.5c0.6-18.6,3.8-29.2,3.8-29.3c2.5-8.3,4.7-16.2,7-24.2c1.8-6.3,3.6-12.6,5.5-19.1  C218.4,102.8,214.8,87.4,203.5,76.9z M209.6,118.1c-1.9,6.5-3.7,12.9-5.5,19.1c-2.3,8-4.5,15.9-7,24.2c-0.1,0.5-3.3,11.2-4,30.4  c-0.3,9.7-5.1,27.1-13.1,34.6c-2.5,2.3-5.1,3.4-7.7,3.3c-1.4-0.1-2.5-0.6-3.5-1.8c-3.6-4.2-4.6-15.8-2.8-31c0.5-3.8,0-7.5-1.3-10.8  c-2.2-5.3-6-8.4-11-9c-0.1,0-1.5-0.2-3.3-0.2c-1.8,0-3,0.2-3,0.2c-5,0.6-8.8,3.7-11,9c-1.3,3.2-1.8,7-1.3,10.8  c1.9,15.2,0.8,26.8-2.8,31c-1,1.2-2.1,1.7-3.5,1.8c-0.1,0-0.2,0-0.4,0c-11.9,0-19.7-24.9-20.4-37.9c-1.1-20.5-5.5-33-5.7-33.5  l-1.8-4.7C97,144,93.2,134,90.7,124c-1.8-7-3.2-16.4-1.2-25.4c3.6-1.5,16-6.3,27.4-6c6.4,0.2,11.5,2.6,17,5.2  c6.2,2.9,12.7,5.9,21.3,5.9c8.4,0,14.8-3.4,20.5-6.4c5.5-2.9,10.3-5.4,16-4.7c9.9,1.2,16.8,4,19,4.9  C212.1,103.8,211.7,110.8,209.6,118.1z"/>
                 </svg>
                  </Avatar>
                  <Text fw={500} ml={6}> Amalgam</Text>
                     </div>
                  </Group>
                   </Menu.Item>
                   <Menu.Item className={
                      activeButton === 'RestoratinGlassIonomer'
                         ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                         : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                     }onClick={() => {
                       handleButtonClick("RestoratinGlassIonomer");
                       setTargetPath("27");
                     }}
                   >
                     <Group  className="h-[28px] w-[186.4px] " >
                     <Radio
                         checked={activeButton === 'RestoratinGlassIonomer'}
                         onChange={(event) =>
                         setChecked(event.currentTarget.checked)
                         }
                         icon={CheckIcon}
                     />
                     <div className="flex">
                     <Avatar
                       color="blue"
                       radius="sm"
                       style={{ width: "28px", height: "28px" }}
                       px={0.5}
                     >
                         <svg xmlns="http://www.w3.org/2000/svg"  version="1.0" x="0px" y="0px" viewBox="0 0 106 112.5"  >
                         <g>
                           <path style={{ fill:"#3799CE"}} d="M80.41,70.82H72.2c-0.07-0.68-0.14-1.37-0.18-2.05c-0.12-2.12-0.04-4.27,0.68-6.28c0.4-1.12,1-2.18,1.47-3.27   c0.49-1.15,0.95-2.31,1.31-3.51c0.83-2.75,1.47-5.59,1.77-8.45c0.25-2.35,0.3-4.9-0.5-7.16c-0.71-1.99-2.13-3.57-4.2-4.15   c-2.84-0.78-5.98,0.15-8.62,1.19c-1.01,0.4-2,0.84-2.98,1.32c-0.87,0.42-0.11,1.72,0.76,1.3c2.8-1.36,6.03-2.76,9.22-2.56   c1.88,0.12,3.33,1.05,4.13,2.71c0.9,1.85,0.92,4.15,0.79,6.18c-0.17,2.65-0.73,5.27-1.43,7.83c-0.56,2.05-1.28,4.03-2.19,5.95   c-0.44,0.94-0.91,1.86-1.2,2.86c-0.28,0.96-0.43,1.96-0.51,2.96c-0.14,1.72-0.02,3.42,0.15,5.13H68.2   c-0.91-11.26-5.95-16.57-15.68-16.57c-6.38,0-10.73,2.29-13.24,7.02c1.51-6.47,4.75-14.86,6.92-18.13   c0.67-1.01,1.53-2.02,2.45-3.09c1.15-1.34,2.34-2.73,3.14-4.15c1.17-2.07,1.26-5.86,1.24-7.45c1.28-0.23,2.26-1.35,2.26-2.7v-0.82   h1.29c0.96,0,1.75-0.79,1.75-1.75v-2.39h0.18c0.96,0,1.75-0.79,1.75-1.75V5c0-0.41-0.34-0.75-0.75-0.75H44.44   c-0.41,0-0.75,0.34-0.75,0.75v14.03c0,0.96,0.79,1.75,1.75,1.75h0.18v2.39c0,0.96,0.79,1.75,1.75,1.75h1.29v0.82   c0,1.52,1.23,2.75,2.75,2.75h0.11c0.01,1.49-0.07,4.93-1.05,6.67c-0.73,1.3-1.87,2.63-2.97,3.91c-0.91,1.06-1.85,2.16-2.56,3.24   c-3.15,4.78-8.47,19.75-7.96,25.52c0.01,0.14,0.07,0.26,0.15,0.37c-0.13,0.83-0.23,1.71-0.3,2.62h-1.16   c0.07-0.71,0.14-1.43,0.18-2.15c0.1-1.98,0.03-4.02-0.53-5.94c-0.3-1.04-0.79-1.99-1.25-2.97c-0.51-1.09-0.98-2.19-1.37-3.33   c-0.95-2.72-1.63-5.62-2.01-8.49c-0.29-2.23-0.43-4.62,0.16-6.81c0.46-1.69,1.44-3.13,3.13-3.67c2.56-0.82,5.49,0.13,7.88,1.05   c0.95,0.37,1.88,0.79,2.79,1.23c0.86,0.42,1.63-0.87,0.76-1.3c-0.39-0.19-0.82-0.39-1.27-0.59c1.57-1.27,3.36-2.2,5.38-2.49   c0.4-0.06,0.62-0.57,0.52-0.92c-0.12-0.43-0.52-0.58-0.92-0.52c-2.48,0.35-4.66,1.64-6.52,3.28c-4.28-1.71-9.8-2.9-12.37,1.5   c-1.14,1.96-1.34,4.38-1.26,6.59c0.09,2.78,0.62,5.54,1.31,8.23c0.51,1.98,1.14,3.91,1.96,5.8c0.47,1.09,1.07,2.13,1.46,3.25   c0.34,0.97,0.54,1.99,0.63,3.01c0.15,1.75,0.04,3.49-0.15,5.23h-8.59c-0.41,0-0.75,0.34-0.75,0.75V101c0,0.41,0.34,0.75,0.75,0.75   h54.81c0.41,0,0.75-0.34,0.75-0.75V71.57C81.16,71.15,80.82,70.82,80.41,70.82z M45.19,19.03V5.75h13.56v13.28   c0,0.14-0.11,0.25-0.25,0.25H45.44C45.3,19.28,45.19,19.16,45.19,19.03z M47.12,23.17v-2.39h9.7v2.39c0,0.14-0.11,0.25-0.25,0.25   h-2.04h-5.11h-2.04C47.23,23.42,47.12,23.31,47.12,23.17z M50.16,25.74v-0.82h3.61v0.82c0,0.69-0.56,1.25-1.25,1.25h-1.11   C50.72,26.99,50.16,26.43,50.16,25.74z M52.52,55.75c6.49,0,13.12,1.9,14.19,15.07h-2.97c-2.29-6.05-6.17-7.61-11.22-7.61   s-8.93,1.56-11.22,7.61h-2.97C39.4,57.65,46.02,55.75,52.52,55.75z M66.87,74.77c0,2.99-0.33,5.68-0.98,8.04   c-0.27-4.37-0.83-7.8-1.64-10.49h2.56C66.84,73.1,66.87,73.91,66.87,74.77z M62.13,70.82H42.9c2.26-5.45,5.82-6.11,9.62-6.11   S59.88,65.36,62.13,70.82z M38.23,72.32h2.56c-0.81,2.69-1.37,6.12-1.64,10.49c-0.65-2.36-0.98-5.04-0.98-8.04   C38.17,73.91,38.19,73.1,38.23,72.32z M36.72,72.32c-0.04,0.79-0.06,1.61-0.06,2.45c0,4.77,0.81,8.83,2.4,12.08   c0.15,0.31,0.49,0.48,0.83,0.4c0.34-0.07,0.58-0.37,0.59-0.71c0.16-6.45,0.83-11,1.87-14.22h20.32c1.03,3.22,1.71,7.77,1.87,14.22   c0.01,0.35,0.25,0.64,0.59,0.71c0.05,0.01,0.11,0.02,0.16,0.02c0.28,0,0.55-0.16,0.67-0.42c1.59-3.25,2.4-7.31,2.4-12.08   c0-0.84-0.02-1.66-0.06-2.45h2.55c0.22,1.85,0.46,3.71,0.5,5.57c0.06,3.01-0.68,6.1-1.49,8.98c-0.47,1.68-1.05,3.33-1.84,4.89   c-0.38,0.75-0.81,1.49-1.3,2.18c-0.33,0.47-0.96,1.54-1.61,1.51c-1.17-0.05-1.1-2.65-1.15-3.45c-0.1-1.56-0.11-3.13-0.19-4.69   c-0.17-3.16-0.58-6.53-2.61-9.09c-0.35-0.44-0.75-0.82-1.18-1.18c0.43-0.87,0.88-1.56,1.3-2.11c0.25-0.33,0.19-0.8-0.14-1.05   c-0.33-0.25-0.8-0.19-1.05,0.14c-1.57,2.04-3.5,5.92-3.81,12.81c-0.24,5.22-1.25,8.08-3.02,8.52c-0.39,0.11-0.8,0.02-1.23-0.28   c-1.2-0.82-2.77-3.48-3.28-8.32c-0.5-3.6-0.69-7.23-0.5-9.48c0.15-1.76-0.13-2.98-0.14-3.03c-0.09-0.4-0.49-0.65-0.9-0.56   c-0.4,0.09-0.65,0.49-0.56,0.9c0,0.01,0.19,0.85,0.13,2.12c-2.61,1.98-3.62,5.01-4.01,8.16c-0.38,3.04-0.08,6.16-0.61,9.18   c-0.09,0.53-0.26,1.48-1,1.42c-0.63-0.04-1.22-1.05-1.55-1.51c-1.01-1.41-1.76-3-2.35-4.62c-1.03-2.81-1.67-5.78-2.14-8.74   c-0.3-1.89-0.12-3.85,0.08-5.74c0.09-0.84,0.19-1.68,0.29-2.52H36.72z M79.66,100.25H26.34V72.32h7.67   c-0.24,2.04-0.51,4.09-0.51,6.14c0.01,1.54,0.27,3.05,0.56,4.56c0.31,1.62,0.68,3.23,1.15,4.82c0.54,1.79,1.2,3.57,2.1,5.22   c0.43,0.79,0.92,1.55,1.48,2.26c0.53,0.67,1.1,1.4,1.98,1.59c0.83,0.18,1.67-0.18,2.19-0.83c0.5-0.62,0.62-1.39,0.73-2.16   c0.22-1.58,0.25-3.19,0.31-4.78c0.1-3.19,0.17-6.77,2.01-9.53c0.2-0.3,0.43-0.58,0.68-0.85c-0.04,2.28,0.16,5.2,0.57,8.17   c0.47,4.52,1.94,8.02,3.92,9.38c0.57,0.39,1.17,0.59,1.76,0.59c0.23,0,0.47-0.03,0.7-0.09c3.34-0.82,3.96-6.05,4.14-9.9   c0.17-3.65,0.8-6.39,1.58-8.42c2.16,2.07,2.7,5.31,2.89,8.19c0.11,1.65,0.12,3.31,0.21,4.97c0.08,1.41,0.03,3.38,1,4.51   c0.53,0.62,1.4,0.94,2.2,0.74c0.86-0.22,1.44-0.97,1.96-1.64c1.13-1.45,1.96-3.1,2.63-4.8c1.18-2.98,1.84-6.13,2.38-9.28   c0.51-2.94,0.1-5.9-0.26-8.85h7.29V100.25z"/><path d="M50.1,41.36c1.41-0.62,2.9-1.15,4.46-1.15c2.14-0.01,4.1,0.93,5.85,2.09c0.81,0.54,1.56-0.76,0.76-1.3   c-1.96-1.31-4.2-2.31-6.6-2.3c-1.84,0.01-3.55,0.63-5.21,1.36C48.46,40.45,49.22,41.74,50.1,41.36z"/><path d="M42.8,41.85c-0.08-0.01-0.17-0.02-0.25-0.02c-0.02,0-0.01,0-0.02,0c-0.03,0-0.06-0.01-0.09-0.01   c-0.34-0.05-0.68-0.13-1.02-0.23c-0.81-0.24-1.39-0.51-2.15-0.93c-0.34-0.19-0.83-0.09-1.03,0.27c-0.19,0.35-0.1,0.82,0.27,1.03   c1.47,0.82,3.08,1.4,4.78,1.43c0.39,0.01,0.77-0.35,0.75-0.75c-0.02-0.41-0.33-0.74-0.75-0.75C43.12,41.88,42.96,41.87,42.8,41.85z   "/><path d="M57.69,37.34c0.35,0.17,0.83,0.11,1.03-0.27c0.18-0.34,0.11-0.84-0.27-1.03c-1.51-0.75-3.08-1.42-4.72-1.84   c-0.38-0.1-0.83,0.12-0.92,0.52c-0.09,0.39,0.12,0.82,0.52,0.92C54.89,36.05,56.27,36.63,57.69,37.34z"/><path d="M77.04,78.84c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S76.62,78.84,77.04,78.84   z"/><path d="M75.88,84.41c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S75.46,84.41,75.88,84.41   z"/>
                           <path style={{ fill:"#3799CE"}}d="M76.4,89.7h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S76.81,89.7,76.4,89.7z"/>
                           <path style={{ fill:"#3799CE"}}d="M72.73,92.69h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S73.14,92.69,72.73,92.69z"/>
                           <path style={{ fill:"#3799CE"}}d="M76.4,95.89h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S76.81,95.89,76.4,95.89z"/>
                           <path style={{ fill:"#3799CE"}}d="M69.4,94.76h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S69.81,94.76,69.4,94.76z"/>
                           <path style={{ fill:"#3799CE"}}d="M72.06,97.44h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S72.47,97.44,72.06,97.44z"/>
                           <path style={{ fill:"#3799CE"}}d="M65.49,97.57h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S65.91,97.57,65.49,97.57z"/>
                           <path style={{ fill:"#3799CE"}}d="M59.46,97.28h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S59.88,97.28,59.46,97.28z"/>
                           <path style={{ fill:"#3799CE"}}d="M55.17,96.82h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S55.58,96.82,55.17,96.82z"/>
                           <path style={{ fill:"#3799CE"}}d="M50.57,97.88h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S50.98,97.88,50.57,97.88z"/>
                           <path style={{ fill:"#3799CE"}}d="M46.24,96.49h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S46.65,96.49,46.24,96.49z"/>
                           <path style={{ fill:"#3799CE"}}d="M40.11,97.36H40.1c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S40.53,97.36,40.11,97.36z"/>
                           <path style={{ fill:"#3799CE"}}d="M34.75,92.32h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S35.16,92.32,34.75,92.32z"/>
                           <path style={{ fill:"#3799CE"}}d="M32.35,89.36c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S32.35,89.78,32.35,89.36z   "/>
                           <path style={{ fill:"#3799CE"}}d="M33.35,83.44h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S33.76,83.44,33.35,83.44z"/>
                           <path style={{ fill:"#3799CE"}}d="M32.87,95.16h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S33.28,95.16,32.87,95.16z"/>
                           <path style={{ fill:"#3799CE"}}d="M34.75,97.88h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S35.16,97.88,34.75,97.88z"/>
                           <path style={{ fill:"#3799CE"}}d="M29.73,91.72h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S30.14,91.72,29.73,91.72z"/>
                           <path style={{ fill:"#3799CE"}}d="M28.63,87.25c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S28.21,87.25,28.63,87.25   z"/>
                           <path style={{ fill:"#3799CE"}}d="M31.9,80.52c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S31.9,80.93,31.9,80.52z"/>
                           <path style={{ fill:"#3799CE"}}d="M31.06,76.72c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S30.64,76.72,31.06,76.72   z"/>
                           <path style={{ fill:"#3799CE"}}d="M27.99,96.12h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S28.41,96.12,27.99,96.12z"/>
                           <path style={{ fill:"#3799CE"}}d="M72.69,88.77c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S72.28,88.77,72.69,88.77   z"/>
                           <path style={{ fill:"#3799CE"}}d="M73.9,75.5c0.41,0,0.75-0.34,0.75-0.75S74.32,74,73.9,74h-0.01c-0.41,0-0.74,0.34-0.74,0.75S73.49,75.5,73.9,75.5z"/>
                           <path style={{ fill:"#3799CE"}}d="M46.17,93.07c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S46.17,93.48,46.17,93.07z   "/>
                           <path style={{ fill:"#3799CE"}}d="M46.06,88.27c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S45.65,88.27,46.06,88.27   z"/>
                           <path style={{ fill:"#3799CE"}}d="M59.74,93.82c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S59.74,94.24,59.74,93.82z   "/>
                           <path style={{ fill:"#3799CE"}}d="M59.46,87.37h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S59.88,87.37,59.46,87.37z"/>
                           <path style={{ fill:"#3799CE"}}d="M59.79,82.7h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S60.2,82.7,59.79,82.7z"/>
                           </g>
                         </svg>
                     </Avatar>
                     <Text fw={500} ml={6}>  Glass Ionomer</Text>
                         </div>
                 </Group>
                   </Menu.Item>
                   </Menu.Dropdown>
                 </Menu>
          <Menu shadow="md" width={210}  trigger="hover" openDelay={100} closeDelay={400}>
                          <Tooltip
                                label="Root Canal"
                                withArrow
                                className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                              >
                          <Menu.Target>
                                <Button
                          styles={{
                            root: {
                              position: 'relative',
                              color: 'white',
                              height: '35px',
                              width: '35px',
                              padding: 0,
                              borderRadius: '0.5rem'
                            },
                          }}
                        >
                          <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        width: '100%',
                      }}
                    >
                        <span  className={
                        (
                        // viewCleaning4 === true ||
                        activeButton  === "RootPermanent"||
                        activeButton  === "RootTemporary"||
                        activeButton  === "RootCalcium"  ||
                        activeButton  === "RootGuttaPercha" ||
                        isHidingMode
                        )
                          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
                      }
                      onClick={() =>
                        setChecked(!checked)
                      }
                          >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 60" x="0px" y="0px">
                        <path style={{ fill: "#ffffff",  stroke:"#fff" ,strokeWidth:0.25,strokeMiterlimit:10 }} d="M5,25.69a13.84,13.84,0,0,1,5,10.52v5.42a5.37,5.37,0,0,0,10.72.48l1-11a2.29,2.29,0,0,1,4.56,0l1,11A5.37,5.37,0,0,0,38,41.63V36.21a13.84,13.84,0,0,1,5-10.52A14,14,0,1,0,24,5.19,14,14,0,1,0,5,25.69Zm24.3,16.24-1-11a4.29,4.29,0,0,0-8.54,0l-1,11a3.35,3.35,0,0,1-1.52,2.52l1-16.2a4,4,0,0,1,3.51-3.8,19.66,19.66,0,0,1,4.6,0,4,4,0,0,1,3.51,3.8l1,16.2A3.35,3.35,0,0,1,29.27,41.93ZM2.19,12.87a12,12,0,0,1,21-5.55,1,1,0,0,0,1.54,0,12,12,0,1,1,17,16.85,15.8,15.8,0,0,0-5.74,12v5.42a3.37,3.37,0,0,1-1.38,2.7l.63-18.86A1.35,1.35,0,0,1,36,24.28a1.41,1.41,0,0,1,1.39,0l.19.13a2.45,2.45,0,0,0,3.76-1.69l.9-6a5.9,5.9,0,0,0-2-5.11,6.13,6.13,0,0,0-5.5-1.26,42.39,42.39,0,0,1-21.4,0,6.13,6.13,0,0,0-5.5,1.26,5.91,5.91,0,0,0-2,5.16l.89,5.92a2.43,2.43,0,0,0,1.44,1.88,2.5,2.5,0,0,0,2.37-.22l.15-.1a1.39,1.39,0,0,1,1.38,0,1.35,1.35,0,0,1,.76,1.19l.63,18.86A3.37,3.37,0,0,1,12,41.63V36.21a15.8,15.8,0,0,0-5.74-12A12,12,0,0,1,2.19,12.87Z"/>
                        </svg>
                                </span>
                          </div>
                          <span
                              style={{
                              position: 'absolute',
                              bottom: '0px',
                              right: '0px',
                              fontSize: '8px',
                              fontWeight: '800',
                              backgroundColor: 'white',
                              // borderRadius:'0.125rem' ,
                              borderTopLeftRadius: '0.5rem' ,
                              borderBottomRightRadius: '0.5rem' ,
                              color:'#3799CE',
                                padding:'3px  0px 1px 2px' ,
                            }}
                            className="h-[14px] w-[14px] "
                          >
                            Ro
                          </span>
                        </Button>
                          </Menu.Target>
                          </Tooltip>
                          <Menu.Dropdown>
                          <Menu.Label>Root Canal</Menu.Label>
                          <Menu.Item className={
                    activeButton  === "RootPermanent"
                        ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                        : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                  }
                  onClick={() => {
                    handleButtonClick("RootPermanent");
                    const isValidSelection = selectedTeeth.every(tooth => isAllowedPosition(tooth));
                    // Always set the selected color to ensure the radio reflects the change
                    if (selectedColor !== "#FF4444") {
                      handleColorSelect("#FF4444", "fill");
                    }
                    // Apply color only if the selection is valid
                    if (isValidSelection) {
                      handleColorSelect("#FF4444", "fill");
                    }
                  }}
                  >
                  <Group  className="h-[28px] w-[186.4px] " >
                    <Radio
                        checked={activeButton  === "RootPermanent"}
                        onChange={(event) =>
                        setChecked(event.currentTarget.checked)
                        }
                        icon={CheckIcon}
                        className="cursor-pointer"
                    />
                        <div className="flex">
                        <Avatar
                          color="blue"
                          radius="sm"
                          style={{ width: "28px", height: "28px" }}
                          px={0.5}
                        >
                      <svg xmlns="http://www.w3.org/2000/svg"
                          version="1.1" x="0px" y="0px" viewBox="0 0 400 420" >
                          <path style={{fill:"#3799CE"}} d="M200.185 128.344c4.119,-1.685 8.045,-3.332 11.635,-4.906 9.732,-4.265 16.997,-7.995 23.996,-10.952 -0.086,0.497 -0.132,1.012 -0.132,1.543 0,3.415 1.858,6.739 4.679,8.626 -5.152,2.105 -10.227,4.596 -15.265,6.958 -3.293,11.774 -5.356,23.053 -7.41,35.056 -0.311,1.814 -0.615,3.636 -0.913,5.465 3.68,5.454 7.171,10.804 9.816,15.683 1.723,3.18 3.082,6.146 4.38,9.769l-0.005 0.002c1.304,3.64 2.537,7.889 4.002,13.618 2.886,11.281 6.804,28.816 10.724,46.361 1.933,5.908 3.265,12.282 4.208,19.285 0.977,7.254 1.538,15.188 1.89,23.462 0.258,6.052 0.403,12.318 0.52,18.641 0.217,0.12 0.439,0.229 0.665,0.324 0.295,-6.993 0.882,-13.999 2.637,-21.053 1.172,-4.718 2.864,-9.438 4.88,-14.044 2.011,-4.593 4.341,-9.065 6.8,-13.301 1.45,-2.499 2.94,-4.911 4.428,-7.232 0.641,-3.794 1.286,-7.584 2.068,-11.183 0.817,-3.76 1.795,-7.38 3.095,-10.717 1.55,-3.979 3.52,-7.454 5.572,-11.074 1.362,-2.405 2.763,-4.877 4.058,-7.522 3.02,-6.171 5.683,-13.666 8.319,-21.259 1.383,-16.172 1.743,-32.218 0.784,-46.611 -1.038,-9.706 -2.494,-21.207 -9.586,-28.522 -0.76,-0.784 -1.563,-1.488 -2.401,-2.12 -0.468,-2.333 -1.763,-4.497 -3.579,-6.03 2.735,-1.138 4.725,-3.774 4.966,-7.049 2.598,1.359 5.029,3.122 7.217,5.378 6.632,6.84 11.026,18.198 12.892,35.649 1.865,17.45 1.202,40.994 -1.368,62.217 -2.57,21.222 -7.046,40.124 -12.518,56.787 -5.472,16.663 -11.937,31.087 -16.746,40.372 -4.808,9.285 -7.958,13.43 -11.357,15.586 -3.399,2.156 -7.047,2.321 -11.441,0.994 -4.393,-1.326 -9.534,-4.144 -12.932,-12.766 -3.399,-8.622 -5.057,-23.047 -5.886,-33.658 -0.829,-10.611 -0.829,-17.409 -4.477,-23.792 -3.648,-6.384 -10.943,-12.353 -18.321,-14.259 -7.378,-1.907 -14.839,0.249 -21.223,3.896 -6.383,3.648 -11.689,8.787 -14.341,18.818 -2.653,10.031 -2.653,24.954 -2.985,35.068 -0.331,10.113 -0.994,15.419 -2.404,19.564 -1.409,4.145 -3.565,7.13 -7.544,8.207 -3.979,1.078 -9.782,0.249 -17.824,-7.958 -8.041,-8.207 -18.321,-23.792 -26.114,-42.197 -6.25,-14.762 -10.9,-31.337 -13.737,-46.389l-0.141 -0.58c-5.106,-21.093 -8.173,-46.211 -9.376,-63.413 -1.202,-17.202 -0.538,-26.487 1.99,-35.233 2.528,-8.746 6.921,-16.954 14.258,-21.14 7.337,-4.187 17.617,-4.352 27.151,-2.238 9.534,2.113 18.321,6.507 27.824,10.512 4.832,2.037 9.849,3.973 14.595,5.549l-31.277 26.867 51.254 -23.059zm47.874 -56.344l23.387 0c1.342,0 2.496,0.504 3.408,1.488 0.912,0.984 1.327,2.172 1.226,3.51l-2.035 26.784 2.117 0.602c1.815,0.517 3.301,2.544 3.301,4.506l0 0.001c0,1.962 -1.486,3.145 -3.301,2.628l-2.647 -0.753 -0.66 8.686 2.09 0.595c1.814,0.517 3.299,2.544 3.299,4.506l0 0.001c0,1.962 -1.485,3.145 -3.299,2.628l-2.62 -0.746 -0.66 8.688 2.583 0.735c1.816,0.517 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.484,3.145 -3.3,2.628l-3.113 -0.886 -0.66 8.687 1.859 0.529c1.815,0.516 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.485,3.144 -3.3,2.628l-2.39 -0.68 -0.659 8.687 2.179 0.62c1.815,0.517 3.3,2.544 3.3,4.506l0 0.001c0,1.963 -1.485,3.145 -3.3,2.628l-2.71 -0.771 -0.66 8.687 1.977 0.563c1.815,0.516 3.3,2.543 3.3,4.506l0 0.001c0,1.962 -1.485,3.144 -3.3,2.628l-2.507 -0.714 -0.66 8.688 1.949 0.554c1.815,0.516 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.485,3.145 -3.3,2.628l-2.479 -0.706 -0.66 8.687 1.921 0.547c1.815,0.517 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.485,3.145 -3.3,2.628l-2.451 -0.698 -0.798 10.497c-0.183,2.429 -2.196,4.295 -4.632,4.295 -2.437,0.001 -4.451,-1.865 -4.635,-4.295l-1.037 -13.656 -2.211 -0.63c-1.816,-0.517 -3.3,-2.544 -3.3,-4.505l0 -0.002c0,-1.962 1.484,-3.144 3.3,-2.628l1.657 0.472 -0.689 -9.071 -2.187 -0.622c-1.816,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.962 1.484,-3.144 3.3,-2.627l1.633 0.465 -0.689 -9.072 -2.162 -0.615c-1.815,-0.516 -3.3,-2.544 -3.3,-4.506l0 -0.001c0,-1.962 1.485,-3.145 3.3,-2.628l1.608 0.458 -0.689 -9.071 -2.311 -0.658c-1.815,-0.516 -3.3,-2.544 -3.3,-4.506l0 -0.001c0,-1.962 1.485,-3.145 3.3,-2.628l1.757 0.5 -0.688 -9.07 -1.939 -0.552c-1.815,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.962 1.485,-3.144 3.3,-2.627l1.384 0.394 -0.689 -9.072 -2.61 -0.742c-1.815,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.961 1.485,-3.144 3.3,-2.627l2.057 0.585 -0.689 -9.071 -2.064 -0.587c-1.815,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.962 1.485,-3.144 3.3,-2.627l1.51 0.43 -0.689 -9.071 -2.039 -0.58c-1.815,-0.517 -3.3,-2.545 -3.3,-4.507l0 -0.001c0,-1.962 1.485,-3.144 3.3,-2.628l1.485 0.423 -1.403 -18.468c-0.101,-1.338 0.314,-2.526 1.226,-3.51 0.912,-0.984 2.066,-1.488 3.408,-1.488zm-31.974 102.476c-0.429,2.771 -0.836,5.556 -1.215,8.351 2.615,9.181 5.217,18.006 7.763,25.325 3.312,9.517 6.568,16.603 9.823,23.668 1.597,1.904 3.094,3.847 4.465,5.838 0.932,1.354 1.806,2.728 2.624,4.126 -2.742,-12.179 -5.35,-23.514 -7.48,-31.84 -1.461,-5.711 -2.667,-9.882 -3.91,-13.351l-0.006 0c-1.25,-3.488 -2.55,-6.327 -4.193,-9.359 -2.152,-3.972 -4.906,-8.302 -7.871,-12.758zm-2.159 15.887c-0.832,7.292 -1.424,14.626 -1.638,21.934 4.763,4.094 9.432,8.223 13.703,12.504 -2.04,-4.646 -4.093,-9.667 -6.18,-15.666 -1.947,-5.599 -3.913,-12.012 -5.885,-18.772zm-1.715 25.817c-0.059,5.601 0.12,11.177 0.597,16.699 9.057,1.664 18.015,8.558 22.541,16.478 4.305,7.534 4.281,16.331 4.938,24.741 0.474,6.073 1.501,24.841 6.224,35.414 0.76,1.705 1.677,3.358 2.76,4.731 -0.108,-5.337 -0.246,-10.632 -0.467,-15.812 -0.348,-8.182 -0.902,-16.024 -1.866,-23.182 -0.958,-7.109 -2.323,-13.543 -4.332,-19.481 -1.999,-5.904 -4.635,-11.316 -8.145,-16.412 -5.696,-8.272 -13.743,-15.797 -22.25,-23.176zm43.734 101.566c1.197,-0.108 2.49,-0.559 3.882,-1.442 0.609,-0.386 1.437,-1.217 2.407,-2.434 0.117,-6.885 0.458,-13.827 3.703,-21.501 3.48,-8.232 10.235,-17.089 16.989,-25.947l0.053 -0.064 0.002 -0.003 0.05 0.043c0.527,-1.558 0.928,-2.77 1.176,-3.525 1.893,-7.128 3.615,-14.789 5.126,-22.766 -1.516,2.551 -3.168,5.224 -5.073,8.126l-0.006 0c-1.182,1.8 -2.475,3.711 -3.829,5.711l0.004 0.003c-3.366,4.973 -7.109,10.503 -10.55,16.433 -2.412,4.156 -4.692,8.528 -6.648,12.996 -1.95,4.456 -3.585,9.017 -4.716,13.565 -1.721,6.919 -2.284,13.86 -2.57,20.805zm9.42 -8.438c1.428,-2.341 2.952,-5.177 4.426,-8.405 3.648,-7.988 7.338,-17.757 10.11,-25.509 -4.64,6.276 -8.801,12.43 -11.213,18.134 -2.323,5.496 -3.057,10.684 -3.323,15.78zm25.5 -77.827c1.05,-6.332 1.966,-12.807 2.726,-19.325 -1.397,3.654 -2.846,7.139 -4.397,10.308 -1.366,2.792 -2.772,5.273 -4.14,7.686 -2,3.528 -3.919,6.915 -5.385,10.677 -1.225,3.144 -2.162,6.624 -2.954,10.271 -0.306,1.41 -0.589,2.835 -0.858,4.27 0.709,-1.054 1.407,-2.086 2.09,-3.095l0.006 0c1.337,-1.976 2.617,-3.867 3.806,-5.677l-0.005 -0.003c3.778,-5.755 6.495,-10.483 9.111,-15.112z"/>
                      </svg>
                        </Avatar>
                        <Text fw={500} ml={6}> Permanent {/* الجدور */}</Text>
                            </div>
                    </Group>
                          </Menu.Item>
                           <Menu.Item className={
                                     activeButton  === "RootTemporary"
                                        ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                        : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                    }onClick={() => {
                                      handleButtonClick("RootTemporary");
                                      setTargetPath("28");
                                    }}
                                  >
                                    <Group  className="h-[28px] w-[186.4px] " >
                                    <Radio
                                        checked={activeButton  === "RootTemporary"}
                                        onChange={(event) =>
                                        setChecked(event.currentTarget.checked)
                                        }
                                        icon={CheckIcon}
                                    />
                                    <div className="flex">
                                    <Avatar
                                      color="blue"
                                      radius="sm"
                                      style={{ width: "28px", height: "28px" }}
                                       px={0.5}
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 90 85"  >
                                      <path style={{ fill: "#3799CE" }} d="M66.562,5c-7.158,0-13.376,4.1-16.43,10.074c-5.418-0.499-10.791,1.702-14.797,4.021c-4.119-2.39-8.075-3.745-11.773-4.029  c-5.846-0.449-10.899,1.894-14.612,6.776c-2.179,2.865-3.472,6.067-3.84,9.517c-0.312,2.917,0.037,6.027,1.039,9.245  c1.768,5.683,5.244,10.685,7.848,13.88c1.667,2.046,2.585,4.604,2.585,7.203V77.5c0,2.003,0.779,3.887,2.194,5.304  C20.192,84.22,22.076,85,24.08,85c3.555,0,6.647-2.529,7.354-6.014l1.975-9.774c0.296-1.462,1.55-1.576,1.926-1.576  c0.377,0,1.63,0.114,1.926,1.574l1.975,9.776c0.343,1.691,1.267,3.225,2.604,4.32c1.4,1.144,3.165,1.742,4.977,1.689  c4.01-0.12,7.27-3.539,7.27-7.621V61.676c0-2.596,0.914-5.152,2.573-7.196c2.464-3.035,5.615-7.579,7.451-12.768  c0.802,0.106,1.62,0.163,2.45,0.163C76.729,41.875,85,33.604,85,23.437S76.729,5,66.562,5L66.562,5z M53.022,51.526  c-2.335,2.878-3.621,6.482-3.621,10.151v15.698c0,1.574-1.221,2.891-2.725,2.936c-1.34,0.062-2.602-0.938-2.845-2.253l-1.976-9.775  c-0.646-3.189-3.266-5.332-6.52-5.332c-3.254,0-5.875,2.143-6.521,5.333l-1.975,9.774c-0.264,1.307-1.425,2.256-2.759,2.256  c-1.516,0.031-2.842-1.299-2.811-2.812V61.686c0-3.675-1.292-7.284-3.639-10.164c-2.343-2.875-5.461-7.345-7.006-12.313  c-1.733-5.57-1.041-10.458,2.056-14.53c2.78-3.656,6.221-5.271,10.523-4.941c2.781,0.214,5.543,1.23,7.873,2.388  c-1.872,1.618-3.798,3.716-5.177,6.3c-0.609,1.143-0.177,2.563,0.965,3.171c1.119,0.604,2.559,0.188,3.171-0.965  c1.763-3.304,4.861-5.693,6.57-6.833c5.103-3.189,9.057-4.101,11.902-4.111c-0.251,1.211-0.383,2.464-0.383,3.748  c0,7.692,4.736,14.299,11.445,17.058C57.939,44.916,55.189,48.857,53.022,51.526L53.022,51.526z M66.562,37.187  c-7.582,0-13.751-6.169-13.751-13.75s6.169-13.75,13.751-13.75c7.581,0,13.75,6.169,13.75,13.75S74.144,37.187,66.562,37.187  L66.562,37.187z M66.562,37.187"/>
                                      <path style={{fill:"#3799CE"}} d="M66.562,14.375c-1.295,0-2.344,1.049-2.344,2.344v4.375h-2.812c-1.295,0-2.344,1.049-2.344,2.344  c0,1.295,1.049,2.344,2.344,2.344h5.156c1.294,0,2.344-1.049,2.344-2.344v-6.719C68.906,15.424,67.856,14.375,66.562,14.375  L66.562,14.375z M66.562,14.375"/>
                                      </svg>
                                     </Avatar>
                                     <Text fw={500} ml={6}> Temporary</Text>
                                        </div>
                                    </Group>
                            </Menu.Item>
                               <Menu.Item className={
                                       activeButton  === "RootCalcium"
                                          ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                          : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                      }onClick={() => {
                                        handleButtonClick("RootCalcium");
                                        setTargetPath("30");
                                      }}
                                    >
                                      <Group  className="h-[28px] w-[186.4px] " >
                                      <Radio
                                          checked={activeButton  === "RootCalcium" }
                                          onChange={(event) =>
                                          setChecked(event.currentTarget.checked)
                                          }
                                          icon={CheckIcon}
                                      />
                                      <div className="flex">
                                      <Avatar
                                        color="blue"
                                        radius="sm"
                                        style={{ width: "28px", height: "28px" }}
                                         px={0.5}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg"    version="1.1"viewBox="0 0 846.66 880" x="0px" y="0px" >
                              <path style={{fill:"#3799CE"}} d="M423.33 148.5c-24.94,0.08 -45.13,-4.82 -64.69,-9.58 -28.86,-7.01 -56.4,-13.7 -95.77,-1.57 -14.95,4.6 -26.46,11.13 -36.08,18.8 -9.79,7.8 -17.84,16.95 -25.64,26.57 -18.68,23.03 -30.03,48.47 -34.4,76.54 -2.19,14.06 -2.65,28.83 -1.43,44.35 29.12,4.33 44.55,25.97 58.2,45.1 9.63,13.5 18.2,25.48 28.77,25.36 17.79,-0.22 30.06,15.02 42.1,30 11.46,14.26 22.72,28.25 35.35,18.95 38.92,-28.64 63.8,-10.87 92.32,9.53 7.25,5.18 14.79,10.58 22.56,15.06 18.17,10.47 30.91,-0.04 43.95,-10.81 12.21,-10.09 24.66,-20.37 42.45,-20.29 13.32,0.06 22.25,3.81 29.93,7.02 11.29,4.73 18.75,7.84 42.03,-21.7 4.93,-6.27 9.86,-16.59 15.04,-27.46l6.88 -14.08c12.88,-25.35 28.62,-47.79 54.67,-41.01 3.1,-21.32 3.27,-41.31 0.35,-60.02 -4.37,-28.07 -15.72,-53.51 -34.4,-76.54 -7.8,-9.62 -15.85,-18.77 -25.64,-26.57 -9.62,-7.67 -21.13,-14.2 -36.08,-18.8 -39.37,-12.13 -66.91,-5.44 -95.77,1.57 -19.57,4.75 -39.75,9.66 -64.7,9.58zm-255.93 172.81c1.6,10.34 3.86,20.99 6.81,31.96 8.53,31.72 22.47,56.75 36.45,81.87 12.76,22.91 25.56,45.92 34.76,74.19 8.66,26.65 13.1,57.97 17.4,88.44 7.97,56.49 15.53,109.99 47.97,117.52 20.24,4.7 30.71,-18.67 38.2,-50.91 3.77,-16.28 6.7,-34.32 9.63,-52.42 5.57,-34.45 11.21,-69.24 22.53,-92.78 4.65,-9.67 10.75,-17.35 17.62,-22.68 7.45,-5.8 15.89,-8.89 24.53,-8.92 8.68,0.03 17.11,3.12 24.57,8.9 6.89,5.34 13,13.02 17.65,22.67l0.21 0.47c11.18,23.53 16.79,58.1 22.32,92.34 2.93,18.1 5.86,36.14 9.63,52.38l0.08 0.37c7.48,32.07 17.94,55.26 38.12,50.58 32.44,-7.53 40,-61.04 47.97,-117.53 4.3,-30.47 8.73,-61.78 17.39,-88.43 9.2,-28.27 22.01,-51.29 34.77,-74.2 13.98,-25.1 27.92,-50.15 36.45,-81.86 1.55,-5.76 2.91,-11.42 4.08,-17.01 -15.97,-3.68 -27.08,13.05 -36.56,31.67l-6.68 13.72c-5.58,11.71 -10.88,22.82 -17.02,30.62 -31.25,39.65 -43.4,34.58 -61.81,26.86 -6.3,-2.63 -13.65,-5.71 -23.51,-5.76 -11.63,-0.05 -21.72,8.28 -31.61,16.45 -17.61,14.55 -34.8,28.74 -63.13,12.41 -8.69,-5.01 -16.46,-10.57 -23.94,-15.92 -23.22,-16.6 -43.48,-31.07 -72.56,-9.67 -25.64,18.87 -41.91,-1.37 -58.49,-21.99 -9.5,-11.82 -19.18,-23.83 -28.74,-23.72 -19.4,0.23 -30.38,-15.13 -42.73,-32.44 -10.76,-15.09 -22.83,-32.01 -42.36,-37.18zm195.2 -198.79c18.61,4.53 37.82,9.19 60.73,9.12 22.9,0.07 42.13,-4.6 60.74,-9.12 31.18,-7.58 60.92,-14.8 104.69,-1.31 17.23,5.3 30.51,12.85 41.64,21.72 10.97,8.74 19.75,18.71 28.22,29.15 20.6,25.39 33.12,53.51 37.96,84.6 3.66,23.44 2.93,48.48 -1.96,75.23l-0.03 0.2 -0.03 0.21 -0.02 0.08 -0.02 0.12 -0.04 0.21 -0.01 0.03c-1.5,8.13 -3.4,16.42 -5.67,24.87 -9.04,33.58 -23.52,59.61 -38.05,85.7 -12.31,22.11 -24.68,44.32 -33.43,71.22 -8.22,25.29 -12.54,55.82 -16.73,85.53 -8.8,62.4 -17.15,121.5 -60.95,131.67 -33.39,7.76 -48.63,-21.78 -58.32,-63.17l-0.1 -0.4c-3.79,-16.32 -6.81,-34.92 -9.83,-53.58 -5.33,-33.01 -10.74,-66.35 -20.9,-87.74l-0.19 -0.37c-3.5,-7.27 -7.91,-12.9 -12.75,-16.65 -4.51,-3.5 -9.42,-5.37 -14.22,-5.4 -4.78,0.03 -9.68,1.91 -14.19,5.41 -4.89,3.76 -9.3,9.38 -12.77,16.61 -10.28,21.38 -15.72,54.93 -21.09,88.14 -3.02,18.66 -6.04,37.26 -9.83,53.58 -9.69,41.6 -24.92,71.36 -58.42,63.57 -43.8,-10.17 -52.15,-69.26 -60.95,-131.66 -4.19,-29.71 -8.52,-60.25 -16.74,-85.54 -8.75,-26.89 -21.11,-49.1 -33.42,-71.21 -14.53,-26.1 -29.02,-52.14 -38.05,-85.71 -4.19,-15.59 -7.09,-30.63 -8.67,-45.15l-0.03 -0.21 -0.02 -0.21c-2.09,-19.38 -1.8,-37.83 0.94,-55.38 4.84,-31.09 17.36,-59.21 37.96,-84.6 8.47,-10.44 17.25,-20.41 28.22,-29.15 11.13,-8.87 24.41,-16.42 41.64,-21.72 43.77,-13.49 73.51,-6.27 104.69,1.31z"/>
                             </svg>

                                       </Avatar>
                                       <Text fw={500} ml={6}> Calcium</Text>
                                          </div>
                                   </Group>
                                        </Menu.Item>
                                          <Menu.Item className={
                                                      activeButton  === "RootGuttaPercha"
                                                          ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                                          : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                                      }onClick={() => {
                                                        handleButtonClick("RootGuttaPercha");
                                                        setTargetPath("34");
                                                      }}
                                                    >
                                                      <Group  className="h-[28px] w-[186.4px] " >
                                                      <Radio
                                                          checked={activeButton  === "RootGuttaPercha"}
                                                          onChange={(event) =>
                                                          setChecked(event.currentTarget.checked)
                                                          }
                                                          icon={CheckIcon}
                                                      />
                                                      <div className="flex">
                                                      <Avatar
                                                        color="blue"
                                                        radius="sm"
                                                        style={{ width: "28px", height: "28px" }}
                                                        px={0.5}
                                                      >

                                                           <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 104 80" >
                                                        <path style={{fill:"#3799CE"}} d="M81.23,21.11c-3.54-5.34-11.02-3.26-15.87-1.29c-1.13,0.46-2.23,0.96-3.33,1.49c-0.87,0.42-0.11,1.71,0.76,1.29
                                                         c3.35-1.61,7.03-3.18,10.79-3.37c2.17-0.12,4.31,0.33,5.76,1.89c1.33,1.42,1.91,3.55,2.1,5.55c0.25,2.66-0.02,5.36-0.47,7.99
                                                          c-0.51,3-1.28,5.95-2.19,8.86c-0.44,1.42-0.92,2.83-1.42,4.23c-0.46,1.26-1,2.48-1.56,3.7c-0.61,1.33-1.18,2.59-1.41,4.04
                                                          c-0.45,2.91-0.31,5.94-0.26,8.87c0.05,3.01,0.15,6.02,0.1,9.03c-0.08,5.26-0.65,10.6-2.64,15.51V65.46
                                                          c-0.01-6.52,1.25-12.89,3.76-18.95c0.28-0.69,0.48-1.16,0.54-1.28c0.01-0.03,0.03-0.07,0.04-0.1c0.87-2.49,2.8-10.21,0.3-12.94
                                                          c-3.34-3.65-11.01-0.3-13.28,0.81c-3.89,1.9-7.28,2.74-11.01,2.74c-0.42,0-0.75,0.34-0.75,0.75c0,0.42,0.33,0.75,0.75,0.75
                                                          c3.97,0,7.57-0.89,11.67-2.89c4-1.97,9.42-3.44,11.51-1.15c1.6,1.75,0.69,7.73-0.59,11.4c-0.09,0.18-0.25,0.55-0.57,1.34
                                                          c-0.38,0.92-0.72,1.85-1.05,2.79c-1.44-0.93-2.91-2.07-3.11-2.57c-0.02-0.05-0.2-0.56,0.16-2.57C70,43.4,70,43.22,70.02,43.03
                                                          c1.28-0.57,2.56-0.42,2.57-0.42c0.41,0.06,0.79-0.23,0.85-0.64c0.06-0.41-0.23-0.79-0.64-0.84c-0.07-0.01-1.31-0.17-2.75,0.29
                                                          c-0.07-0.79-0.26-1.55-0.58-2.24c-0.17-0.38-0.62-0.55-0.99-0.37c-0.38,0.17-0.54,0.62-0.37,0.99c0.47,1.03,0.6,2.25,0.38,3.52
                                                          c-0.19,1.08-0.26,1.81-0.25,2.33c-0.56,0.04-1.14,0.19-1.73,0.36c-0.25,0.06-0.5,0.13-0.76,0.2c-0.17-0.5-0.44-1.22-0.82-2
                                                          c0.11-0.36,0.43-1.08,1.23-1.56c0.36-0.21,0.48-0.67,0.27-1.03c-0.21-0.35-0.67-0.47-1.03-0.26c-0.63,0.37-1.07,0.85-1.37,1.3
                                                          c-0.39-0.58-0.85-1.14-1.37-1.6c-0.32-0.27-0.79-0.24-1.06,0.07c-0.27,0.32-0.24,0.79,0.07,1.06c1.33,1.17,2.19,3.17,2.58,4.27
                                                          c-1.27,0.1-2.71-0.16-4.47-1.36c-0.34-0.24-0.81-0.15-1.04,0.19c-0.23,0.35-0.15,0.81,0.19,1.05c0.51,0.34,0.99,0.61,1.45,0.84
                                                          c-0.51,0.52-1.12,1.39-1.3,2.71c-0.05,0.41,0.24,0.79,0.65,0.84c0.03,0.01,0.07,0.01,0.1,0.01c0.37,0,0.69-0.27,0.74-0.65
                                                          c0.21-1.58,1.27-2.2,1.33-2.24c0.04-0.02,0.06-0.06,0.1-0.09c0.63,0.16,1.22,0.23,1.78,0.23c1.21,0,2.24-0.29,3.13-0.54
                                                          c0.68-0.19,1.27-0.34,1.74-0.31c0.78,1.11,2.78,2.44,3.78,3.06c-1.23,3.88-1.97,7.87-2.23,11.93l-0.41,0.08
                                                          c-2.34,0.43-4.96,0.92-7.02,0.32c-1.01-1.66-2.1-3.08-3.24-4.24c0.45-0.16,1-0.39,1.59-0.68c0.35,0.12,1.08,0.43,1.56,1.24
                                                          c0.14,0.23,0.39,0.37,0.64,0.37c0.13,0,0.26-0.04,0.38-0.11c0.36-0.21,0.48-0.67,0.27-1.03c-0.38-0.63-0.85-1.06-1.3-1.36
                                                          c0.58-0.4,1.14-0.85,1.6-1.38c0.27-0.31,0.23-0.78-0.08-1.06c-0.31-0.27-0.78-0.24-1.06,0.08c-1.42,1.63-4.09,2.54-4.86,2.77   c-2.03-1.67-4.21-2.55-6.44-2.55c-1.46,0-2.9,0.38-4.29,1.11c-0.01,0-0.03,0-0.04,0.01c-2.51,1.4-5.86,0.01-8.82-1.23   c-2.44-1.01-4.94-2.05-6.54-1.05c-0.62-2.54-1.43-5.03-2.44-7.47c-0.32-0.79-0.47-1.16-0.56-1.35c-1.29-3.67-2.2-9.65-0.6-11.4   c1.45-1.58,4.33-1.27,6.5-0.73c0.5,0.12,1.02,0.28,1.55,0.46l-2.4,15.06c-0.07,0.41,0.21,0.8,0.62,0.86   c0.04,0.01,0.08,0.01,0.12,0.01c0.36,0,0.68-0.26,0.74-0.63l6.87-43.11l0.06,0.01c0.04,0.01,0.08,0.01,0.11,0.01   c0.37,0,0.69-0.27,0.74-0.64c0.06-0.41-0.22-0.79-0.63-0.85l-1.6-0.24c-0.42-0.05-0.79,0.23-0.85,0.64   C40.31,4.46,40.59,4.84,41,4.9l0.05,0.01l-2.54,15.97c-3.52-1.43-7.66-2.7-11.4-1.66c-2.73,0.76-4.55,2.9-5.4,5.54   c-1,3.12-0.84,6.59-0.43,9.8c0.52,4.11,1.47,8.28,2.9,12.18c0.53,1.43,1.14,2.84,1.79,4.22c0.56,1.19,1.13,2.34,1.47,3.62   c0.68,2.51,0.68,5.14,0.5,7.72c-0.35,5.03-1.64,10.19-0.8,15.24c0.63,3.73,1.4,7.47,2.68,11.04c0.76,2.12,1.69,4.22,2.93,6.11   c1.04,1.57,2.67,4.1,4.9,3.18c1.93-0.79,2.05-3.2,2.21-4.98c0.18-1.96,0.19-3.92,0.27-5.87c0.15-3.96,0.41-8.35,2.81-11.67   c0.32-0.46,0.71-0.86,1.11-1.25c-0.09,3.02,0.17,7.04,0.73,11.14c0.62,5.82,2.72,12.82,5.67,14.75c1.35,0.88,2.13,0.55,2.34,0.49   c3.97-0.98,4.97-9.99,5.21-15.27c0.25-5.41,1.28-9.28,2.48-12.01c1.04,2.08,1.65,4.29,2,6.63c0.45,2.97,0.49,5.99,0.57,8.98   c0.04,1.6,0.08,3.2,0.12,4.79c0.02,1.09-0.09,2.38,0.72,3.24c0.93,1,2.45,1.02,3.62,0.47c1.1-0.52,1.87-1.51,2.55-2.48   c6.98-9.83,5.51-22.34,5.55-33.69c0-1.59-0.01-3.2,0.17-4.78c0.17-1.51,0.69-2.79,1.32-4.15c0.93-2.03,1.74-4.09,2.45-6.2   c1.01-3,1.89-6.06,2.53-9.17c0.6-2.88,1.03-5.85,0.94-8.81C82.95,25.66,82.56,23.12,81.23,21.11z M32.07,90.23   c-1.55-3.57-2.45-7.42-3.15-11.24c-0.34-1.85-0.57-3.67-0.52-5.57c0.06-2.53,0.41-5.05,0.69-7.57c0.57-4.94,0.9-9.99-1.32-14.59   c-1.24-2.56-2.33-5.17-3.1-7.91c-0.52-1.81-0.98-3.65-1.34-5.5c-0.66-3.27-1.09-6.68-0.73-10.02c0.26-2.41,1.09-5.08,3.24-6.44   c2.73-1.72,6.36-1.01,9.24-0.13c1.08,0.32,2.14,0.72,3.19,1.14l-1.44,9.03c-0.49-0.16-0.96-0.3-1.43-0.42   c-3.67-0.91-6.43-0.5-7.97,1.18c-2.5,2.73-0.57,10.45,0.3,12.94c0.01,0.03,0.03,0.07,0.05,0.1c0.05,0.11,0.25,0.59,0.53,1.29   c2.5,6,3.76,12.34,3.76,18.85V90.23z M36.35,81.23l-0.03,15.21c-0.31-0.14-0.6-0.38-0.85-0.66c-0.73-0.81-1.36-1.7-1.92-2.64   c0.01-0.04,0.02-0.08,0.02-0.12V65.37c0-3.46-0.35-6.87-1.04-10.21c0.71-1.16,2.29-0.67,5.57,0.7c2.09,0.87,4.36,1.8,6.55,1.91   c-1,0.93-3.92,4.85-5,6.99C37.52,69.01,36.37,76.26,36.35,81.23z M70.09,65.46v26.51c-0.13,0.22-1.64,2.73-2.51,3.51   c-0.07,0.06-0.15,0.12-0.24,0.17l-0.03-14.42c-0.01-3.66-0.63-8.55-1.81-12.57c-0.22-1.13-0.83-2.5-1.49-3.91   c-0.09-0.17-0.18-0.33-0.27-0.5c0.4,0.04,0.81,0.07,1.22,0.07c1.7,0,5.17-0.65,5.17-0.65C70.11,64.27,70.09,64.86,70.09,65.46z"/><path d="M57.06,18.6c-0.17,0.05-0.36,0.18-0.45,0.34c-0.17,0.33-0.11,0.86,0.27,1.03c1.08,0.48,2.14,1.03,3.18,1.59   c0.34,0.19,0.83,0.1,1.03-0.27c0.18-0.35,0.1-0.83-0.27-1.03c-1.04-0.57-2.1-1.11-3.18-1.59C57.45,18.59,57.27,18.54,57.06,18.6z"/>
                                                          <path style={{fill:"#3799CE"}} d="M51.69,18.23c0.49,0.09,0.98,0.22,1.46,0.36l-5.64,16.77c-0.51-0.1-1.02-0.21-1.55-0.35c-0.4-0.11-0.81,0.14-0.91,0.54
                                                          c-0.1,0.4,0.14,0.81,0.54,0.92c0.49,0.13,0.97,0.23,1.44,0.33l-4.18,12.42c-0.13,0.39,0.08,0.82,0.47,0.95
                                                          c0.08,0.03,0.16,0.04,0.24,0.04c0.31,0,0.61-0.2,0.71-0.51L59.1,5.59l0.11,0.04c0.08,0.03,0.16,0.04,0.24,0.04
                                                          c0.31,0,0.61-0.2,0.71-0.51c0.13-0.39-0.08-0.82-0.47-0.95l-1.65-0.55c-0.39-0.13-0.82,0.08-0.95,0.47
                                                          c-0.13,0.39,0.08,0.82,0.47,0.95l0.11,0.04l-4.05,12.05c-0.51-0.15-1.02-0.28-1.54-0.38c-0.2-0.04-0.4-0.03-0.58,0.08
                                                          c-0.16,0.09-0.3,0.27-0.34,0.45C51.08,17.67,51.27,18.15,51.69,18.23z"/>
                                                          <path style={{fill:"#3799CE"}} d="M42.59,18.04c-0.31,0.23-0.51,0.66-0.27,1.03c0.2,0.31,0.69,0.52,1.03,0.27c0.17-0.13,0.35-0.25,0.54-0.35
                                                          c0.09-0.05,0.19-0.1,0.29-0.15c0.05-0.02,0.1-0.05,0.15-0.07c0.02-0.01,0.04-0.02,0.05-0.02c0.38-0.16,0.77-0.28,1.17-0.38
                                                          c0.27-0.07,0.54-0.11,0.82-0.16L42.28,33.7c-0.55-0.24-1.23-0.55-1.7-0.77c-0.35-0.16-0.62-0.29-0.71-0.32   c-0.38-0.16-0.82,0.02-0.98,0.4c-0.16,0.38,0.02,0.82,0.4,0.98c0.08,0.03,0.33,0.15,0.66,0.3c0.57,0.26,1.36,0.62,1.94,0.88   L37.3,52.61c-0.11,0.4,0.13,0.81,0.53,0.92c0.06,0.02,0.13,0.02,0.19,0.02c0.33,0,0.64-0.22,0.72-0.56L50.14,9.74l0.11,0.03   c0.06,0.02,0.13,0.02,0.19,0.02c0.33,0,0.64-0.22,0.73-0.56c0.1-0.4-0.14-0.81-0.54-0.92l-1.68-0.44c-0.4-0.1-0.81,0.14-0.91,0.54   c-0.1,0.4,0.14,0.81,0.54,0.92l0.11,0.03l-1.92,7.27C45.3,16.83,43.8,17.13,42.59,18.04z"/>
                                                          <circle cx="35.14" cy="91.73" r="0.75"/>
                                                          <circle cx="34.39" cy="88.37" r="0.75"/>
                                                          <circle cx="35.58" cy="85.52" r="0.75"/>
                                                          <circle cx="34.83" cy="81.91" r="0.75"/>
                                                          <circle cx="34.48" cy="78.64" r="0.75"/>
                                                          <circle cx="35.7" cy="75.65" r="0.75"/>
                                                          <circle cx="34.85" cy="72.35" r="0.75"/>
                                                          <circle cx="36.79" cy="68.77" r="0.75"/>
                                                          <circle cx="34.54" cy="65.88" r="0.75"/>
                                                          <circle cx="34.64" cy="61.87" r="0.75"/>
                                                          <circle cx="37.07" cy="59.51" r="0.75"/>
                                                          <circle cx="41.48" cy="58.31" r="0.75"/>
                                                          <circle cx="40.33" cy="61.87" r="0.75"/>
                                                          <circle cx="34.41" cy="56.36" r="0.75"/>
                                                          <circle cx="68.66" cy="91.46" r="0.75"/>
                                                          <circle cx="69.22" cy="87.99" r="0.75"/>
                                                          <circle cx="68.85" cy="84.97" r="0.75"/>
                                                          <circle cx="68.85" cy="80.64" r="0.75"/>
                                                          <circle cx="69.17" cy="77.98" r="0.75"/>
                                                          <circle cx="67.81" cy="74.13" r="0.75"/>
                                                          <circle cx="68.91" cy="71.6" r="0.75"/>
                                                          <circle cx="69.03" cy="68.39" r="0.75"/>
                                                          <circle cx="67.04" cy="65.36" r="0.75"/>
                                                          <circle cx="66.19" cy="67.64" r="0.75"/>
                                                          </svg>

                                                      </Avatar>
                                                      <Text fw={500} ml={6}>  Gutta-Percha</Text>
                                                          </div>
                                                      </Group>
                                                        </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
              <Tooltip
                                                   label="Post & Care"
                                                   withArrow
                                                   className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                                     >
                                               <Button
                                                 styles={{
                                                   root: {
                                                     position: 'relative',
                                                     color: 'white',
                                                     height: '35px', // Adjust button height
                                                     width: '35px',  // Adjust button width
                                                     padding: 0,
                                                     borderRadius: '0.5rem'
                                                   },
                                                 }}
                                               >
                                                 {/* SVG in the middle */}
                                                 <div
                                             style={{
                                               display: 'flex',
                                               justifyContent: 'center',
                                               alignItems: 'center',
                                               height: '100%',
                                               width: '100%',
                                             }}
                                           >
                                               <span  className={
                                               activeButton  === "PostCare"
                                                 ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
                                                 : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
                                             }
                                             onClick={() => {
                                              handleButtonClick("PostCare");
                                              setTargetPath("36");
                                            }}
                                                 >
                                     <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" enableBackground="new 0 0 100 100" >
                                     <path fillRule="evenodd" clipRule="evenodd" style={{ fill: "#ffffff",   stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10 }}
                                     d="M42.369,28.706c-0.77-0.299-1.154-1.164-0.855-1.936  c0.297-0.771,1.164-1.154,1.932-0.855c2.102,0.813,4.326,1.221,6.557,1.221c2.229,0,4.451-0.407,6.553-1.221  c0.77-0.299,1.635,0.085,1.934,0.855c0.297,0.771-0.086,1.637-0.857,1.936c-2.461,0.952-5.049,1.431-7.629,1.431  C47.42,30.137,44.83,29.658,42.369,28.706 M50.002,53.913c-4.559,0-8.25,3.691-8.25,8.248c0,0,0,4.629,0,13.888  c0.002,0.49-0.268,0.941-0.699,1.171c-0.432,0.231-0.959,0.2-1.365-0.078C31.09,71.171,27.705,62.62,29.541,51.485  c0.281-1.714-0.059-2.114-0.846-3.498c-3.738-6.565-4.188-13.826-2.213-20.802c3.131-8.3,9.139-9.625,18.02-3.975  c3.514,1.359,7.484,1.359,10.998,0c8.881-5.65,14.887-4.325,18.021,3.975c1.971,6.976,1.523,14.236-2.215,20.804  c-0.787,1.382-1.131,1.782-0.846,3.496c1.836,11.135-1.549,19.686-10.15,25.656c-0.404,0.278-0.928,0.31-1.363,0.078  c-0.432-0.229-0.701-0.681-0.699-1.171c0-9.259,0-13.888,0-13.888C58.248,57.604,54.555,53.913,50.002,53.913z M8.27,41.257  c-0.828,0-1.5-0.671-1.5-1.499c0-0.83,0.672-1.501,1.5-1.501h9.697c-0.646-6.949,0.725-13.581,3.779-18.395  c1.273-2.01,2.85-3.716,4.703-5.008c1.871-1.305,4.012-2.179,6.396-2.513c4.275-0.6,9.309,0.546,14.934,4.073  c0.717,0.21,1.465,0.318,2.223,0.318c0.756,0,1.504-0.108,2.221-0.318c5.623-3.527,10.656-4.673,14.934-4.073  c2.383,0.334,4.525,1.208,6.396,2.513c1.854,1.292,3.43,2.998,4.703,5.008c3.055,4.813,4.426,11.445,3.779,18.395h9.697  c0.828,0,1.498,0.671,1.498,1.501c0,0.828-0.67,1.499-1.498,1.499H80.355v-0.002c-0.07,0-0.137-0.005-0.205-0.017  c-0.82-0.111-1.393-0.868-1.277-1.686c0.959-6.864-0.217-13.463-3.146-18.086c-1.074-1.692-2.379-3.109-3.885-4.16  c-1.486-1.038-3.191-1.733-5.096-2.001c-3.641-0.509-8.037,0.545-13.078,3.74l-0.002-0.001c-0.105,0.065-0.221,0.12-0.342,0.16  c-1.09,0.352-2.211,0.528-3.322,0.528c-1.078,0-2.16-0.167-3.217-0.494c-0.154-0.038-0.309-0.1-0.453-0.193  c-5.037-3.195-9.436-4.249-13.078-3.74c-1.902,0.268-3.607,0.963-5.096,2.001c-1.506,1.05-2.809,2.468-3.881,4.16  c-2.922,4.603-4.098,11.16-3.162,17.988c0.021,0.098,0.031,0.197,0.031,0.303c0,0.828-0.67,1.499-1.498,1.499H8.27z M75.344,46.601  c0.389-0.075,0.787-0.117,1.195-0.117H95v34.198c0,3.927-3.203,7.128-7.127,7.128H12.129C8.203,87.81,5,84.608,5,80.682V46.483  h18.48c0.4,0,0.795,0.04,1.176,0.115c0.418,0.972,0.895,1.93,1.432,2.874c0.143,0.252,0.443,0.712,0.553,0.988  c0.006,0.153-0.037,0.401-0.061,0.537c-1.941,11.771,1.473,21.723,11.4,28.61c1.32,0.914,3.059,1.015,4.479,0.262  c1.412-0.752,2.293-2.222,2.293-3.821c-0.002-4.631,0-9.261,0-13.888c0-2.902,2.35-5.249,5.25-5.249  c2.898,0,5.248,2.347,5.248,5.249c0,4.627,0,9.257,0,13.888c-0.002,1.6,0.879,3.069,2.293,3.821c1.42,0.753,3.156,0.652,4.479-0.262  c9.926-6.888,13.34-16.839,11.4-28.61c-0.025-0.136-0.066-0.384-0.063-0.535c0.107-0.278,0.41-0.738,0.553-0.99  C74.449,48.53,74.924,47.574,75.344,46.601z"/>
                                     </svg>
                                                       </span>
                                                 </div>
                                                 {/* "CL" in the bottom-right corner */}
                                                 <span
                                                   style={{
                                                     position: 'absolute',
                                                     bottom: '0px',
                                                     right: '0px',
                                                     fontSize: '8px',
                                                     fontWeight: '800',
                                                     backgroundColor: 'white',
                                                     // borderRadius:'0.125rem' ,
                                                     borderTopLeftRadius: '0.5rem' ,
                                                     borderBottomRightRadius: '0.5rem' ,
                                                     color:'#3799CE',
                                                     padding:'3px  0px 1px 2px' ,
                                                   }}
                                                   className="h-[14px] w-[14px] "
                                                 >
                                                     Po
                                                 </span>
                                               </Button>
                                               </Tooltip>
        </div>
      </Flex>
     </Container>

      {isAlertVisible && isPathSelectionActive && clickedIds.length > 0 &&(
      <div className="flex justify-between border-2 p-1 px-1">
        <Alert
          title={`SelectTeeth : ${getFormattedOutput() || ""}`}
          icon={icon}
          withCloseButton
          onClose={() => setIsAlertVisible(false)} // Close alert
          w={"100%"}
          bg={"cyan"}
          color="white"
        ></Alert>
      </div>
    )}
       <div className=" h-full px-0   border-t-[3px] my-2">
      <Flex style={{ position: 'relative', width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:220}}>
        <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
          </div>
      </Flex>
       <div className=" max-w-[920px] mt-2 mx-auto">
       <div className=" flex h-full px-0  max-h-[320px] ">
                {Dantal.map((svgData) => (
                <div
                  key={svgData.svg_id}
                  className="h-[230px] cursor-pointer hover:bg-[#F2F5F8] parent-div"
                  onClick={() => handleSvgClick(svgData.svg_id)}

                >
             {!hiddenSvgIds.includes(svgData.svg_id) ? (
                  <DentalSvg
                    svgData={svgData}
                    isHidingMode={isHidingMode}
                    highlightedPaths={highlightedPaths}
                    onPathClick={onPathClick}
                    isPathSelectionActive={isPathSelectionActive}
                    hiddenPaths={hiddenPaths}
                    isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                    isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                    isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                  />
                ) : (
              <div
                style={{
                  height: '200px', // Match SVG height
                  width: svgData.width, // Match SVG width
                  alignItems: 'center',
                }}
              />
            )}
            <div className="mt-0.5 flex">
              <Text
                ta="center"
                h={30}
                key={svgData.svg_id}
                className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                style={{ width: svgData.width }}
              >
                {svgData.svg_id}
              </Text>
            </div>
          </div>
                ))}

         </div>
        </div>
        </div>
        <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
        <div className=" h-full px-0   border-b-[3px]">
           <Flex style={{ position: 'relative',  width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:14}}>
        <Image
              src={dentalButtom}
              alt={"dentalButtom"}
              width={32}
              height={32}
            />
          </div>
      </Flex>
    <div className=" max-w-[920px]  mx-auto">
        <div className=" flex h-full px-0  max-h-[320px] ">
        <div className="flex h-full px-0 max-h-[320px]">
        {DantalB.map((svgData) => (
       <div
                      key={svgData.svg_id}
                     className="h-[230px]  cursor-pointer hover:bg-[#F2F5F8] parent-div"
                      onClick={() => handleSvgClick(svgData.svg_id)}
                    >
                       <div className="mt-0.5 flex  ">
                   <Text
                       ta="center"
                       h={30}
                       key={svgData.svg_id}
                       className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                       style={{ width: svgData.width }}
                     >
                       {svgData.svg_id}
                     </Text>
                     </div>
         {!hiddenSvgIds.includes(svgData.svg_id) ? (
           <DentalSvg
                  svgData={svgData}
                  isHidingMode={isHidingMode}
                  highlightedPaths={highlightedPaths}
                  onPathClick={onPathClick}
                  isPathSelectionActive={isPathSelectionActive}
                  hiddenPaths={hiddenPaths}
                  isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                  isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                  isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                />

         ) : (
           <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
         )}

                     </div>
                   ))}
     </div>
        </div>
        </div>
        </div>
      <div className="mx-auto w-[880px] max-w-[880px] mb-4 ">
      <br/>
      <Button onClick={toggle} className="ButtonHover  w-full">Register </Button>
      </div>

    <div className="border-base-200 mx-4 border-t mt-4">
      <Tabs
        variant="unstyled"
        defaultValue="Therapy"
        classNames={classes}
      >
            <Tabs.List grow className="space-x-0 gap-0   mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={
                  <IconMessageCircle
                    style={{ width: rem(16), height: rem(16) }}
                  />
                }
              >
               All Procedures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={
                  <IconSettings style={{ width: rem(16), height: rem(16) }} />
                }
              >
               Planned
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
              Completed
              </Tabs.Tab>
              <Tabs.Tab
                value="Inventory"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
                Inventory
              </Tabs.Tab>
            </Tabs.List>
             <Tabs.Panel value="All Procedures" className=" mt-2">
           <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsAllProcedures}</Table.Tbody>
              </Table>
              </Tabs.Panel>
            <Tabs.Panel value="Planned" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsPlanned}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Completed" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsCompleted}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Inventory" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsInventory}</Table.Tbody>
              </Table>
            </Tabs.Panel>
      </Tabs>
      <Button rightSection={<MdPrint size={14} />} className="ButtonHover  " mt={6}>Imprimer</Button>
      </div>

      <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
          < IconDeviceFloppy stroke={2} />
      <Text size="sm" mb="xs" fw={500}>
      Save Modification
      </Text>
      </Group>
      <Group align="flex-end">
        <Button w={"100%"} onClick={sendPathsToApi} className="hover:bg-[#3799CE]/90">save</Button>
      </Group>
    </Dialog>


      </div>
        </Tabs.Panel>
        <Tabs.Panel value="Prosthodontics">
        <div className="my-4 ">
        <Container>
        <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px'}} align="center">
          <div  style={{ position: 'absolute', right: 0 }}>
            <Menu withinPortal position="bottom-end" shadow="sm">
              <Menu.Target>
                <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>All Procedures</Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                  Download zip
                </Menu.Item>
                <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                  Preview all
                </Menu.Item>
                <Menu.Item
                  leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                  color="red"
                >
                  Delete all
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
            </div>
          <div style={{ margin: '0 auto' }} className=" mb-2 flex flex-end p-2 sm:justify-start space-x-2 ">
          <Tooltip
                                     label="Veneer"
                                     withArrow
                                     className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                       >
                                 <Button
                                   styles={{
                                     root: {
                                       position: 'relative',
                                       color: 'white',
                                       height: '35px', // Adjust button height
                                       width: '35px',  // Adjust button width
                                       padding: 0,
                                       borderRadius: '0.5rem'
                                     },
                                   }}
                                 >
                                   <div
                               style={{
                                 display: 'flex',
                                 justifyContent: 'center',
                                 alignItems: 'center',
                                 height: '100%',
                                 width: '100%',
                               }}
                             >
                                 <span  className={
                                activeButton  === "Veneer"
                                   ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE] p-1"
                                   : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE] p-1"
                               }
                               onClick={() => {
                                 handleButtonClick("Veneer");
                                 setTargetPath("37");
                               }}
                                         >
                               <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 13.14 16.28"><defs>
                                 </defs>
                                   <path style={{ fill: "#ffffff",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10  }} d="M14.86,15.58a3.7,3.7,0,0,0-2.39-.94h-.35a.79.79,0,0,0-.7.4c-.44.82-1.07,1.37-1.56,1.37-.31,0-.68-.1-.88-.94A.64.64,0,0,0,8.35,15a.66.66,0,0,0-.64.47c-.2.83-.57.94-.87.94-.49,0-1.12-.55-1.57-1.37a.78.78,0,0,0-.69-.41H4.23a3.71,3.71,0,0,0-2.39.95.18.18,0,0,0,0,.27.23.23,0,0,0,.29,0A3.31,3.31,0,0,1,4.23,15h.35a.36.36,0,0,1,.33.19,2.57,2.57,0,0,0,1.93,1.58c.65,0,1.08-.42,1.28-1.24a.24.24,0,0,1,.46,0c.2.82.63,1.24,1.28,1.24a2.57,2.57,0,0,0,1.93-1.58.36.36,0,0,1,.33-.19h.35a3.26,3.26,0,0,1,2.1.83.23.23,0,0,0,.29,0A.18.18,0,0,0,14.86,15.58Z" transform="translate(-1.78 -0.51)"/>
                                   <polygon style={{ fill: "#3799CE"}} points="11.08 5.28 2.32 5.39 1.18 4.19 1.33 2.1 2.43 0.82 3.93 0.29 5.58 0.53 7.14 1.97 6.78 1.11 7.96 0.46 9.68 0.29 11.03 0.94 11.96 2.22 12.07 3.69 11.08 5.28"/>
                                   <polygon style={{ fill: "#ffffff" }} points="4.9 6.29 3.56 6.44 2.24 7.63 2.1 8.75 2.54 10.11 3.4 10.99 3.5 11.88 3.61 13.22 4.25 14.57 4.9 15.11 5.58 14.64 5.79 11.3 6.65 10.79 7.17 11.03 7.51 11.46 7.57 13 7.67 14.03 7.89 15.04 8.32 15.06 8.65 14.82 9.29 13.93 9.65 12.79 9.72 11.26 10.04 10.7 10.71 10.05 11.11 9.14 10.96 7.72 9.51 6.38 8.25 6.34 6.57 6.61 4.9 6.29"/>
                               </svg>
                                         </span>
                                   </div>
                                   {/* "CL" in the bottom-right corner */}
                                   <span
                                     style={{
                                       position: 'absolute',
                                       bottom: '0px',
                                       right: '0px',
                                       fontSize: '8px',
                                       fontWeight: '800',
                                       backgroundColor: 'white',
                                       // borderRadius:'0.125rem' ,
                                       borderTopLeftRadius: '0.5rem' ,
                                       borderBottomRightRadius: '0.5rem' ,
                                       color:'#3799CE',
                                       padding:'3px  0px 1px 2px' ,
                                     }}
                                     className="h-[14px] w-[14px] "
                                   >
                                       Ve
                                   </span>
                                 </Button>
                                 </Tooltip>
            <Tooltip
                                                    label="Onlay"
                                                    withArrow
                                                    className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                                      >
                                                <Button
                                                  styles={{
                                                    root: {
                                                      position: 'relative',
                                                      color: 'white',
                                                      height: '35px', // Adjust button height
                                                      width: '35px',  // Adjust button width
                                                      padding: 0,
                                                      borderRadius: '0.5rem'
                                                    },
                                                  }}
                                                >
                                                  <div
                                              style={{
                                                display: 'flex',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                height: '100%',
                                                width: '100%',
                                              }}
                                            >
                                                <span  className={
                                                   activeButton  === "Onlay"
                                                  ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE] p-1"
                                                  : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE] p-1"
                                                  }
                                                  onClick={() => {
                                                    handleButtonClick("Onlay");
                                                    setTargetPath("39");
                                                  }}     >
                                                <svg xmlns="http://www.w3.org/2000/svg" id="Calque_1" data-name="Calque 1" viewBox="0 0 14.67 15.82">
                                                <path style={{ fill: "#3799CE", }} d="M14.21,1.49a4.51,4.51,0,0,0-3-1,5.28,5.28,0,0,0-2,.36,10.44,10.44,0,0,1-1.58.43A8.1,8.1,0,0,0,4.58.75,4.13,4.13,0,0,0,1.4,2.54a2.89,2.89,0,0,0-.35.76.26.26,0,0,0,0,.11A4.89,4.89,0,0,0,1.14,6,23,23,0,0,0,2.27,8.86l.2.44a10.63,10.63,0,0,1,.6,3,6.37,6.37,0,0,0,.75,2.46,2.38,2.38,0,0,0,2,1.48h.07A1,1,0,0,0,6.6,16c.67-.67.55-2.31.42-3.23a1.37,1.37,0,0,1,.11-.81,1,1,0,0,1,.86-.6h.59a1,1,0,0,1,.85.61,1.39,1.39,0,0,1,.11.81c-.13.91-.26,2.55.41,3.23a1,1,0,0,0,.73.31,1.82,1.82,0,0,0,1.19-.41,5.31,5.31,0,0,0,1.61-3.54,10.32,10.32,0,0,1,.43-2.76c.28-.78.52-1.53.78-2.28l.61-1.8A3.36,3.36,0,0,0,14.21,1.49Zm.68,3.89c-.21.61-.41,1.21-.62,1.8-.25.75-.5,1.5-.78,2.28a10.71,10.71,0,0,0-.44,2.86,5.08,5.08,0,0,1-1.47,3.27,1.36,1.36,0,0,1-.86.31.58.58,0,0,1-.39-.17c-.4-.4-.51-1.49-.31-2.92a1.69,1.69,0,0,0-.15-1,1.47,1.47,0,0,0-1.22-.85s-.17,0-.37,0a2.66,2.66,0,0,0-.34,0,1.49,1.49,0,0,0-1.23.85,1.75,1.75,0,0,0-.14,1c.21,1.43.09,2.52-.31,2.92a.6.6,0,0,1-.39.17h0c-1.33,0-2.2-2.35-2.28-3.58a10.28,10.28,0,0,0-.63-3.15l-.2-.45a23.76,23.76,0,0,1-1.1-2.79,4.41,4.41,0,0,1-.13-2.39A9.51,9.51,0,0,1,4.54,3a5.49,5.49,0,0,1,1.9.49A6.12,6.12,0,0,0,8.81,4a5.24,5.24,0,0,0,2.29-.61A3.44,3.44,0,0,1,12.89,3,9.26,9.26,0,0,1,15,3.43,3.38,3.38,0,0,1,14.89,5.38Z" transform="translate(-0.89 -0.47)"/>
                                                <path style={{ fill: "#ffffff",     stroke: "#3799ce",strokeWidth: "0.5",     strokeMiterlimit: "10"}} d="M4.58.75,14.89,5.38c-.21.61-.41,1.21-.62,1.8-.25.75-.5,1.5-.78,2.28a10.71,10.71,0,0,0-.44,2.86,5.08,5.08,0,0,1-1.47,3.27,1.36,1.36,0,0,1-.86.31.58.58,0,0,1-.39-.17c-.4-.4-.51-1.49-.31-2.92a1.69,1.69,0,0,0-.15-1,1.47,1.47,0,0,0-1.22-.85s-.17,0-.37,0a2.66,2.66,0,0,0-.34,0,1.49,1.49,0,0,0-1.23.85,1.75,1.75,0,0,0-.14,1c.21,1.43.09,2.52-.31,2.92a.6.6,0,0,1-.39.17h0c-1.33,0-2.2-2.35-2.28-3.58a10.28,10.28,0,0,0-.63-3.15l-.2-.45a23.76,23.76,0,0,1-1.1-2.79,4.41,4.41,0,0,1-.13-2.39A9.51,9.51,0,0,1,4.54,3a5.49,5.49,0,0,1,1.9.49A6.12,6.12,0,0,0,8.81,4a5.24,5.24,0,0,0,2.29-.61A3.44,3.44,0,0,1,12.89,3,9.26,9.26,0,0,1,15,3.43,3.38,3.38,0,0,1,14.89,5.38Z" transform="translate(-0.89 -0.47)"/>
                                                </svg>
                                                        </span>
                                                  </div>
                                                  {/* "CL" in the bottom-right corner */}
                                                  <span
                                                    style={{
                                                      position: 'absolute',
                                                      bottom: '0px',
                                                      right: '0px',
                                                      fontSize: '8px',
                                                      fontWeight: '800',
                                                      backgroundColor: 'white',
                                                      // borderRadius:'0.125rem' ,
                                                      borderTopLeftRadius: '0.5rem' ,
                                                      borderBottomRightRadius: '0.5rem' ,
                                                      color:'#3799CE',
                                                      padding:'3px  0px 1px 2px' ,
                                                    }}
                                                    className="h-[14px] w-[14px] "
                                                  >
                                                      On
                                                  </span>
                                                </Button>
                                                </Tooltip>
           <Menu shadow="md" width={210} trigger="hover" openDelay={100} closeDelay={400}>
                                                             <Tooltip
                                                               label="Crown"
                                                               withArrow
                                                               className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                                               >
                                                               <Menu.Target>
                                                               <Button
                                                               styles={{
                                                                 root: {
                                                                   position: 'relative',
                                                                   color: 'white',
                                                                   height: '35px', // Adjust button height
                                                                   width: '35px',  // Adjust button width
                                                                   padding: 0,
                                                                   borderRadius: '0.5rem',
                                                                 },
                                                               }}
                                                             >
                                                           {/* SVG in the middle */}
                                                           <div
                                                             style={{
                                                               display: 'flex',
                                                               justifyContent: 'center',
                                                               alignItems: 'center',
                                                               height: '100%',
                                                               width: '100%',
                                                             }}
                                                           >
                                                               <span  className={
                                                             (activeButton  === "CrownPermanent" ||
                                                               activeButton  === "CrownTemporary" ||
                                                               activeButton  === "CrownGold" ||
                                                               activeButton  === "CrownZirconia"||
                                                             isHidingMode
                                                             )
                                                               ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                                                               : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
                                                           }
                                                           onClick={() =>
                                                             setChecked(!checked) // Toggle checked state
                                                           }
                                                               >
                                                           <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 16.6 17" >
                                                           <path  style={{ fill: "#ffffff", stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}} d="M2.4,3.9l11.5,0.1c0.2,0,0.4-0.2,0.3-0.4C13.6,2.4,12-0.6,8.9,1.2c-0.5,0.3-1,0.3-1.5,0     C6,0.6,3.4-0.2,2,3.5C2,3.7,2.1,3.9,2.4,3.9z"/>
                                                           <path  style={{ fill: "#3799CE", stroke:"#ffffff" ,strokeWidth:0.25,strokeMiterlimit:10     }} d="M11.4,5.7c-0.9-0.2-4.3-1-6.7,0C4.6,5.7,4.5,5.8,4.5,6c0,0.3,0,0.9,0,1.2c0,0.2,0.1,0.3,0.3,0.3l6.4,0.1     c0.2,0,0.3-0.1,0.3-0.3V6C11.6,5.8,11.5,5.7,11.4,5.7z"/>
                                                           <path  style={{ fill: "#ffffff", stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10 }} d="M13.4,6.9h-11C2.2,6.9,2,7.1,2,7.4v0.4C2,8,2,8.2,2.2,8.3l1.7,2c0.1,0.2,0.2,0.4,0.2,0.6l-0.3,4.6     c0,0.3,0.2,0.6,0.6,0.7c0.7,0.1,1.9,0,3.2-2c0.2-0.2,0.2-0.5,0.3-0.8l0-0.7c0-0.1,0.1-0.1,0.1-0.1l0.4,0c0.1,0,0.2,0.1,0.2,0.2     v0.7c0,0.4,0.1,0.8,0.3,1.1c0.4,0.7,1.2,1.8,3,1.7c0.3,0,0.6-0.3,0.6-0.5c0-1.2-0.1-4.3-0.1-5.2c0-0.2,0.1-0.4,0.2-0.5l1.2-1.2     c0.2-0.2,0.4-0.5,0.4-0.9V7.5C14.1,7.2,13.8,6.9,13.4,6.9z"/>
                                                            </svg>
                                                                 </span>
                                                           </div>
                                                           <span
                                                             style={{
                                                               position: 'absolute',
                                                               bottom: '0px',
                                                               right: '0px',
                                                               fontSize: '8px',
                                                               fontWeight: '800',
                                                               backgroundColor: 'white',
                                                               // borderRadius:'0.125rem' ,
                                                               borderTopLeftRadius: '0.5rem' ,
                                                               borderBottomRightRadius: '0.5rem' ,
                                                               color:'#3799CE',
                                                               padding:'3px  0px 1px 2px' ,
                                                             }}
                                                             className="h-[14px] w-[14px] "
                                                           >
                                                             Cr
                                                           </span>
                                                         </Button>
                                                           </Menu.Target>
                                                             </Tooltip>
                                                             <Menu.Dropdown >
                                                             <Menu.Label>Crown</Menu.Label>
                                                             <Menu.Item className={
                                                               activeButton  === "CrownPermanent"
                                                                   ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                                                   : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                                               }onClick={() => {
                                                                 handleButtonClick("CrownPermanent");
                                                                 setTargetPath("41");
                                                               }}
                                                             >

                                                           <Group  className="h-[28px] w-[186.4px] " >
                                                           <Radio
                                                               checked={activeButton  === "CrownPermanent"}
                                                               onChange={(event) =>
                                                               setChecked(event.currentTarget.checked)
                                                               }
                                                               icon={CheckIcon}
                                                           />
                                                               <div className="flex">
                                                               <Avatar
                                                             color="blue"
                                                             radius="sm"
                                                             style={{ width: "28px", height: "28px" }}
                                                             px={0.5}
                                                           >
                                                             <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 80" x="0px" y="0px">
                                                               <path style={{fill:"#3799CE"}}  d="M12.73,85.51H6.37a1,1,0,0,0,0,2h6.36a1,1,0,1,0,0-2Z"/>
                                                               <path style={{fill:"#3799CE"}}  d="M80.69,85.51H75.8a1,1,0,0,0,0,2h4.89a1,1,0,0,0,0-2Z"/>
                                                               <path style={{fill:"#3799CE"}}  d="M40.67,11a19.1,19.1,0,0,0,14.94-.23A1,1,0,1,0,54.82,9a17.2,17.2,0,0,1-13.37.21A1,1,0,0,0,40.67,11Z"/>
                                                               <path style={{fill:"#3799CE"}}  d="M95,55H90.35a14.32,14.32,0,0,1-10-3.84A19,19,0,0,0,69.9,46a2.73,2.73,0,0,0-2.23-2.28l-1.92-5.47a4.31,4.31,0,0,0-4-2.87H34.3a4.31,4.31,0,0,0-4,2.87l-1.9,5.41a2.74,2.74,0,0,0-2.63,2.4,19.06,19.06,0,0,0-10.07,5.06C11,55.56,5.62,55,1,55a1,1,0,0,0-1,1V92.51a1,1,0,0,0,1,1H95a1,1,0,0,0,1-1V56A1,1,0,0,0,95,55ZM32.14,38.91a2.3,2.3,0,0,1,2.16-1.53H61.7a2.3,2.3,0,0,1,2.16,1.53l1.67,4.73H30.47Zm-3.65,6.73H67.13A.83.83,0,0,1,67.8,47a10.2,10.2,0,0,0-1.14,7.34,24,24,0,0,1,.7,4.77A21,21,0,0,1,62.15,73a1.14,1.14,0,0,1-1.9-1.21,16.78,16.78,0,0,0,.85-5.32C61.1,61.08,56,55.6,51.64,52a5.67,5.67,0,0,0-7.48,0c-4.31,3.64-9.45,9.11-9.45,14.52,0,4.57,1.45,5.39.71,6.41a1.18,1.18,0,0,1-1.76.12,21,21,0,0,1-5.22-13.94,23.36,23.36,0,0,1,.67-4.6,10.18,10.18,0,0,0-1.32-7.56A.83.83,0,0,1,28.49,45.64ZM23.89,57a1,1,0,0,0,0-2H13.71A15.11,15.11,0,0,0,17,52.56,17.11,17.11,0,0,1,26.12,48a8.07,8.07,0,0,1,1,6,25.26,25.26,0,0,0-.71,5,23,23,0,0,0,5.72,15.26,3.09,3.09,0,0,0,2.34,1,3.14,3.14,0,0,0,3-4.14,14.81,14.81,0,0,1-.77-4.74c0-4.88,5.48-10.23,8.74-13a3.67,3.67,0,0,1,4.9,0c3.27,2.76,8.75,8.11,8.75,13a15.09,15.09,0,0,1-.74,4.69,3.14,3.14,0,0,0,5.29,3.16,23,23,0,0,0,5.71-15.26,25.49,25.49,0,0,0-.75-5.21A8.22,8.22,0,0,1,69.53,48,17.13,17.13,0,0,1,79,52.56,16.29,16.29,0,0,0,90.35,57H94V84.6c-3.79-2.62-6.58-5-11.87-5-7.63,0-7.43.36-15.16,4.37a13.86,13.86,0,0,1-6.4,1.57H36.43a22.23,22.23,0,0,1-9.32-2.07L19.68,80A24.24,24.24,0,0,0,9.52,77.74H2V57ZM94,91.51H2V79.74H9.52a22.23,22.23,0,0,1,9.32,2.07l7.43,3.45a24.24,24.24,0,0,0,10.16,2.25H60.57a15.93,15.93,0,0,0,7.32-1.79l5-2.59a14,14,0,0,1,6.4-1.56c3.35,0,6.43-.27,10.08,2.25L94,87Z"/>
                                                               <path style={{fill:"#3799CE"}}  d="M27.91,31h40A6.07,6.07,0,0,0,73,28.23C78.74,19.41,75.51,7,62.73,3.05a13.47,13.47,0,0,0-9.48.69A13.06,13.06,0,0,1,43,4,16.54,16.54,0,0,0,20.39,16.23,16.34,16.34,0,0,0,22.72,28.1,6.1,6.1,0,0,0,27.91,31ZM22.35,16.58A14.53,14.53,0,0,1,42.22,5.8,15.06,15.06,0,0,0,54,5.58,11.56,11.56,0,0,1,62.13,5c11.41,3.55,14.25,14.39,9.18,22.18A4.1,4.1,0,0,1,67.88,29h-40a4,4,0,0,1-3.48-2A14.46,14.46,0,0,1,22.35,16.58Z"/>
                                                             </svg>
                                                           </Avatar>
                                                                 <Text fw={500} ml={6}> Permanent</Text>
                                                                   </div>
                                                           </Group>
                                                             </Menu.Item>
                                                             <Menu.Item className={
                                                           activeButton  === ""
                                                               ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                                               : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                                           }onClick={() => {
                                                             handleButtonClick("CrownTemporary");
                                                             setTargetPath("42");
                                                           }}
                                                         >
                                                           <Group  className="h-[28px] w-[186.4px] " >
                                                           <Radio
                                                               checked={activeButton  === "CrownTemporary"}
                                                               onChange={(event) =>
                                                               setChecked(event.currentTarget.checked)
                                                               }
                                                               icon={CheckIcon}
                                                           />
                                                           <div className="flex">
                                                           <Avatar
                                                             color="blue"
                                                             radius="sm"
                                                             style={{ width: "28px", height: "28px" }}
                                                             px={0.5}
                                                           >
                                                                   <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 110 95" >
                                                               <path style={{fill:"#3799CE"}} d="M12.152,56.961c0.357,0.619,1.018,1,1.732,1s1.375-0.381,1.732-1L22.5,45.037c0.357-0.619,0.357-1.381,0-2   c-0.357-0.619-1.018-1-1.732-1h-3.947c3.633-15.418,17.318-26.379,33.404-26.379c12.986,0,24.717,7.193,30.616,18.773   c0.502,0.984,1.707,1.375,2.69,0.875c0.984-0.501,1.376-1.706,0.875-2.69c-6.585-12.927-19.682-20.958-34.18-20.958   c-18.28,0-33.791,12.676-37.509,30.379H7c-0.714,0-1.375,0.381-1.732,1c-0.357,0.619-0.357,1.381,0,2L12.152,56.961z    M17.305,46.037l-3.42,5.924l-3.42-5.924H17.305z"/>
                                                               <path style={{fill:"#3799CE"}} d="M77.5,54.961c-0.357,0.619-0.357,1.381,0,2c0.357,0.619,1.018,1,1.732,1h4.398c-3.628,15.42-17.313,26.382-33.404,26.382   c-12.872,0-24.556-7.101-30.493-18.531c-0.509-0.979-1.718-1.361-2.697-0.853c-0.98,0.509-1.362,1.716-0.853,2.697   c6.628,12.761,19.672,20.688,34.042,20.688c18.285,0,33.796-12.677,37.51-30.382H93c0.714,0,1.375-0.381,1.732-1   c0.357-0.619,0.357-1.381,0-2l-6.884-11.924c-0.357-0.619-1.018-1-1.732-1s-1.375,0.381-1.732,1L77.5,54.961z M86.116,48.037   l3.42,5.924h-6.841L86.116,48.037z"/>
                                                               <path style={{fill:"#3799CE"}} d="M68.96,29.967c-3.331-4.026-9.336-6.16-14.945-5.315c-1.363,0.202-2.709,0.589-4.015,1.153   c-1.305-0.564-2.651-0.951-4.01-1.152c-5.612-0.849-11.62,1.288-14.95,5.314c-5.96,7.205-2.444,18.268,4.577,24.079   c0.166,0.138,0.253,0.337,0.228,0.52c-0.994,7.101-0.698,11.846,3.677,18.078c0.521,0.74,1.183,1.415,2.061,2.105   c0.331,0.268,0.857,0.637,1.64,0.752c0.129,0.016,0.261,0.024,0.396,0.024c1.284,0,2.745-0.74,3.129-2.086   c0.13-0.435,0.155-0.887,0.128-1.342c0.015-0.187,0.013-0.376-0.028-0.569c-0.042-0.197-0.083-0.38-0.124-0.565   c-0.009-0.048-0.016-0.097-0.025-0.145l-0.007,0.001c0-0.002-0.001-0.003-0.001-0.005c-0.075-0.331-0.148-0.651-0.219-0.953   c-0.453-1.93-0.726-3.095-0.448-5.133c0.281-2.056,1.805-3.074,3.138-3.341c0.618-0.125,2.728-0.359,3.999,1.802   c1.293,2.199,0.632,5.323,0.048,8.079l-0.055,0.261c-0.04,0.191-0.042,0.379-0.028,0.563c-0.028,0.453-0.006,0.901,0.121,1.324   c0.432,1.509,2.173,2.256,3.571,2.079c0.744-0.109,1.27-0.479,1.58-0.729c0.899-0.708,1.561-1.382,2.085-2.126   c4.373-6.229,4.668-10.974,3.675-18.073c-0.025-0.184,0.062-0.383,0.225-0.519C71.404,48.235,74.92,37.172,68.96,29.967z    M61.828,50.969c-1.241,1.031-1.852,2.582-1.633,4.151c0.853,6.099,0.668,9.76-2.619,14.675c0.482-2.79,0.65-5.882-0.968-8.634   c-1.709-2.906-4.944-4.358-8.233-3.696c-3.371,0.676-5.85,3.315-6.314,6.722c-0.307,2.249-0.09,3.838,0.259,5.451   c-3.187-4.838-3.357-8.485-2.513-14.52c0.218-1.568-0.393-3.119-1.636-4.152c-4.806-3.978-8.886-12.602-4.048-18.45   c2.43-2.937,7.064-4.545,11.276-3.908c1.256,0.186,2.502,0.586,3.704,1.19c0.041,0.02,0.085,0.029,0.127,0.046   c0.043,0.027,0.08,0.06,0.126,0.084c1.547,0.82,2.946,1.965,4.043,3.311c0.396,0.485,0.971,0.736,1.551,0.736   c0.445,0,0.892-0.147,1.263-0.45c0.856-0.698,0.984-1.958,0.286-2.814c-0.61-0.748-1.303-1.435-2.039-2.075   c0.05-0.008,0.099-0.021,0.148-0.028c4.21-0.636,8.842,0.973,11.271,3.909C70.716,38.365,66.636,46.99,61.828,50.969z"/>

                                                             </svg>
                                                           </Avatar>
                                                           <Text fw={500} ml={6}> Temporary</Text>
                                                               </div>
                                                             </Group>
                                                             </Menu.Item>
                                                             <Menu.Item className={
                                                            activeButton  === "CrownGold"
                                                             ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                                             : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                                               }onClick={() => {
                                                                 handleButtonClick("CrownGold");
                                                                 setTargetPath("44");
                                                               }}
                                                             >
                                                             <Group  className="h-[28px] w-[186.4px] " >
                                                            <Radio
                                                               checked={activeButton  === "CrownGold"}
                                                               onChange={(event) =>
                                                               setChecked(event.currentTarget.checked)
                                                               }
                                                               icon={CheckIcon}
                                                           />
                                                               <div className="flex">
                                                               <Avatar
                                                                 color="blue"
                                                                 radius="sm"
                                                                 style={{ width: "28px", height: "28px" }}
                                                                 px={0.5}
                                                               >
                                                               <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 512 500">
                                                               <path style={{fill:"#3799CE"}} d="M303.752,451.781c-4.43,0-8.959-1.248-13.461-3.709c-1.263-0.689-1.997-2.061-1.873-3.492     c5.102-58.812-3.563-73.54-17.919-97.939c-4.336-7.365-9.145-15.54-14.22-25.963c-5.079,10.431-9.892,18.61-14.229,25.979     c-14.359,24.399-23.026,39.127-17.964,97.925c0.123,1.433-0.61,2.8-1.87,3.49c-4.497,2.462-9.024,3.71-13.456,3.71     c-9.279,0-17.923-5.327-25.692-15.836c-7.021-9.496-13.315-23.223-18.708-40.799c-5.233-17.054-9.53-37.41-12.773-60.508     c-3.179-22.639-5.27-47.391-6.219-73.593c-7.173-4.916-13.527-12.086-18.908-21.342c-5.436-9.351-9.629-20.375-12.466-32.768     c-2.809-12.271-4.114-25.122-3.879-38.197c0.243-13.572,2.142-26.627,5.645-38.803c2.802-9.741,6.6-18.733,11.287-26.726     c5.016-8.552,11.063-15.974,17.975-22.061c7.405-6.521,15.861-11.558,25.136-14.972c9.855-3.628,20.767-5.468,32.431-5.468     c10.237,0,20.31,1.643,29.939,4.882c8.451,2.843,16.429,6.868,23.751,11.979c7.309-5.11,15.28-9.134,23.73-11.978     c9.627-3.24,19.698-4.883,29.937-4.883c11.668,0,22.582,1.841,32.439,5.471c9.278,3.417,17.735,8.456,25.142,14.98     c6.912,6.089,12.962,13.515,17.977,22.071c4.688,7.996,8.486,16.992,11.286,26.736c3.499,12.175,5.396,25.229,5.636,38.797     c0.232,13.073-1.076,25.921-3.888,38.188c-2.839,12.39-7.038,23.412-12.477,32.76c-5.385,9.253-11.744,16.421-18.923,21.335     c-0.948,26.205-3.038,50.954-6.213,73.589c-3.24,23.099-7.534,43.455-12.762,60.504c-5.391,17.577-11.684,31.304-18.702,40.8     c-7.77,10.511-16.417,15.842-25.702,15.843C303.757,451.781,303.755,451.781,303.752,451.781z M295.848,442.74     c2.704,1.19,5.354,1.793,7.904,1.793c0.002,0,0.003,0,0.005,0c13.978-0.003,27.288-18.301,37.476-51.519     c10.271-33.496,16.924-81.116,18.729-134.093c0.039-1.204,0.677-2.309,1.696-2.948c13.512-8.467,24.378-26.925,29.813-50.642     c5.48-23.91,4.88-50.651-1.648-73.364c-6.871-23.907-26.385-64.012-79.878-64.012c-18.716,0-36.526,5.875-51.506,16.987     c-1.281,0.951-3.033,0.952-4.316,0.001c-15.009-11.114-32.829-16.989-51.535-16.989c-53.475,0-72.99,40.087-79.864,63.982     c-6.533,22.713-7.14,49.46-1.665,73.38c5.429,23.724,16.287,42.188,29.79,50.656c1.02,0.64,1.655,1.743,1.696,2.947     c1.804,52.965,8.46,100.589,18.744,134.1c10.194,33.22,23.502,51.514,37.471,51.512c2.551,0,5.2-0.603,7.899-1.793     c-2.22-27.303-1.553-46.482,2.135-61.951c3.568-14.967,9.797-25.552,17.01-37.808c5.175-8.794,11.04-18.759,17.173-32.326     c0.587-1.298,1.878-2.132,3.302-2.132c1.423,0,2.716,0.834,3.303,2.132c6.13,13.558,11.992,23.521,17.163,32.31     c7.214,12.26,13.442,22.847,17.005,37.816C297.431,396.248,298.086,415.434,295.848,442.74z"/>
                                                                 <path style={{fill:"#FFCF40"}} d="M300.248,118.865c-1.615,0-3.088-1.085-3.507-2.722c-3.695-14.372-11.552-23.861-23.354-28.206     c-8.851-3.257-16.572-2.32-16.649-2.31c-1.985,0.254-3.8-1.149-4.054-3.135c-0.254-1.985,1.149-3.8,3.135-4.054     c0.385-0.049,9.576-1.166,20.072,2.698c6.215,2.288,11.615,5.845,16.053,10.573c5.481,5.842,9.456,13.455,11.815,22.628     c0.498,1.938-0.669,3.914-2.607,4.413C300.849,118.828,300.545,118.865,300.248,118.865z"/>
                                                               <path style={{fill:"#FFCF40"}} d="M202.587,227.268c-0.376,0-0.759-0.059-1.136-0.184c-16.853-5.56-28.735-15.054-35.313-28.221     c-2.562-5.125-4.296-10.805-5.157-16.882c-0.682-4.814-0.819-9.886-0.409-15.077c0.7-8.831,2.744-14.968,2.831-15.225     c0.64-1.896,2.696-2.915,4.593-2.275c1.895,0.639,2.913,2.692,2.276,4.587l0,0c-0.019,0.056-1.885,5.709-2.491,13.709     c-0.549,7.239-0.151,17.969,4.867,27.978c5.691,11.348,16.145,19.598,31.074,24.522c1.9,0.627,2.933,2.676,2.306,4.577     C205.525,226.302,204.109,227.268,202.587,227.268z"/>
                                                               </svg>
                                                               </Avatar>
                                                               <Text fw={500} ml={6}> Gold</Text>
                                                                   </div>
                                                              </Group>
                                                             </Menu.Item>
                                                           <Menu.Item className={
                                                           activeButton  === "CrownZirconia"
                                                               ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                                               : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                                           }onClick={() => {
                                                             handleButtonClick("CrownZirconia");
                                                             setTargetPath("47");
                                                           }}
                                                         >
                                                           <Group  className="h-[28px] w-[186.4px] " >
                                                           <Radio
                                                               checked={activeButton  === "CrownZirconia"}
                                                               onChange={(event) =>
                                                               setChecked(event.currentTarget.checked)
                                                               }
                                                               icon={CheckIcon}
                                                           />
                                                           <div className="flex">
                                                           <Avatar
                                                             color="blue"
                                                             radius="sm"
                                                             style={{ width: "28px", height: "28px" }}
                                                             px={0.5}
                                                           >
                                                                   <svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="-5.0 -10.0 110.0 125.0">
                                                                 <path style={{fill:"#3799CE"}} d="m92.242 55.523h-0.023438l-4.0312 0.074218h-0.023438c-0.48047 0-0.9375-0.29688-1.1992-0.77344l-0.42969-0.79297c-0.69531-1.2852-2-2.0859-3.3984-2.0859h-6.3086c-1.3047 0-2.5195 0.6875-3.25 1.832l-0.67969 1.0703c-0.26953 0.42188-0.69531 0.67188-1.1406 0.67188l-4.4102 0.003907c-0.0625 0-0.12109 0.011718-0.18359 0.023437-0.015624 0.003906-0.03125-0.003906-0.050781 0-11.121 2.1016-23.227 2.1055-33.23 0.011719-0.0625-0.015625-0.125-0.023438-0.1875-0.027344-0.046874-0.003906-0.085937-0.003906-0.14062-0.003906l-4.0117 0.074218h-0.023438c-0.48047 0-0.9375-0.29687-1.1992-0.77734l-0.42969-0.78906c-0.69531-1.2852-2-2.0859-3.3984-2.0859h-6.3164c-1.3047 0-2.5195 0.68359-3.25 1.832l-0.67969 1.0703c-0.26562 0.42188-0.69141 0.67188-1.1367 0.67188h-4.4023c-0.41406 0-0.80078 0.20312-1.0312 0.54687-0.23438 0.34375-0.28125 0.77734-0.12891 1.1602 0.16406 0.42188 0.29688 0.82031 0.39844 1.2148 0.50781 1.9531 0.41016 3.0586 0.26563 4.7305-0.0625 0.72656-0.13281 1.5469-0.16406 2.543-0.09375 3.5 1.4922 7.9141 5.2891 9.9062 0.40234 0.19141 0.82812 0.28516 1.25 0.28516 0.42969 0 0.85937-0.10156 1.2539-0.30078 0.42188-0.21094 0.68359-0.64453 0.68359-1.1133v-10.641c0-1.3281 0.5-2.5625 1.4219-3.4844 0.82813-0.85156 1.9492-1.3477 3.1055-1.3945h0.14844c1.207 0.046876 2.3281 0.54297 3.168 1.4062 0.91016 0.91406 1.4102 2.1484 1.4102 3.4727v10.641c0 0.47266 0.26562 0.90234 0.68359 1.1133 0.78125 0.39453 1.6914 0.39844 2.543-0.007813 3.7578-1.9531 5.3438-6.3711 5.25-9.8867-0.027344-0.99219-0.097656-1.8164-0.16406-2.543-0.14844-1.6758-0.24219-2.7812 0.26172-4.7227 0.023438-0.078125 0.042969-0.16016 0.070313-0.24219 9.7734 1.8828 21.332 1.8594 32.074-0.035156 0.027344 0.085937 0.0625 0.17969 0.085937 0.26562 0.50781 1.9531 0.41016 3.0586 0.26562 4.7305-0.0625 0.72656-0.13281 1.5469-0.16406 2.543-0.09375 3.5 1.4922 7.9141 5.2891 9.9062 0.40234 0.19141 0.82812 0.28516 1.25 0.28516 0.42969 0 0.85938-0.10156 1.2539-0.29688 0.42188-0.21094 0.6875-0.64453 0.6875-1.1172v-10.633c0-1.3281 0.5-2.5625 1.418-3.4844 0.82812-0.85156 1.9492-1.3477 3.1094-1.3945h0.14844c1.207 0.046876 2.3281 0.54297 3.168 1.4062 0.90625 0.91406 1.4062 2.1445 1.4062 3.4727v10.641c0 0.47266 0.26563 0.90234 0.6875 1.1172 0.78125 0.39453 1.6914 0.39844 2.543-0.007812 3.7578-1.9531 5.3438-6.375 5.25-9.8867-0.027343-0.99609-0.097656-1.8164-0.16406-2.543-0.14844-1.6758-0.24219-2.7812 0.26172-4.7188 0.10547-0.39453 0.24219-0.79297 0.41016-1.2227 0.15625-0.39062 0.10156-0.82812-0.13281-1.1719-0.23438-0.33984-0.61719-0.53906-1.0273-0.53906zm-60.641 7.8711c0.0625 0.72266 0.12891 1.4727 0.15234 2.3906 0.070313 2.6055-0.99609 5.8203-3.4766 7.3633v-9.293c0-1.9922-0.75781-3.8516-2.1289-5.2266-1.2773-1.3125-3-2.0781-4.8984-2.1523h-0.25c-1.8516 0.074219-3.5703 0.83984-4.8398 2.1406-1.3789 1.3828-2.1367 3.2422-2.1367 5.2383v9.2852c-2.4766-1.582-3.543-4.8555-3.4766-7.3555 0.023437-0.91797 0.089844-1.668 0.15234-2.3945 0.14453-1.6719 0.27344-3.1211-0.28125-5.3711h2.7031c1.3047 0 2.5195-0.6875 3.25-1.832l0.67969-1.0703c0.26953-0.42188 0.69531-0.67188 1.1406-0.67188h6.3164c0.48828 0 0.9375 0.28906 1.1992 0.77734l0.42969 0.78906c0.71094 1.3125 2.0234 2.1016 3.4609 2.0859l2.2852-0.042968c-0.55078 2.2305-0.42578 3.6758-0.28125 5.3398zm58.641-0.003906c0.0625 0.72656 0.12891 1.4727 0.15234 2.3945 0.070313 2.6055-0.99219 5.8203-3.4766 7.3594v-9.293c0-1.9922-0.75781-3.8555-2.125-5.2266-1.2773-1.3125-3-2.0781-4.8984-2.1523h-0.25c-1.8516 0.074219-3.5742 0.83984-4.8398 2.1406-1.3789 1.3828-2.1367 3.2422-2.1367 5.2383v9.2852c-2.4766-1.582-3.5469-4.8555-3.4766-7.3555 0.023438-0.91797 0.089844-1.668 0.15234-2.3945 0.14453-1.6719 0.27344-3.1211-0.28125-5.3711h2.7031c1.3047 0 2.5195-0.68359 3.25-1.832l0.67969-1.0703c0.26563-0.42188 0.69141-0.67188 1.1367-0.67188h6.3164c0.48047 0 0.9375 0.29688 1.1992 0.77344l0.42969 0.79297c0.71094 1.3125 2.0273 2.1016 3.4648 2.0859l2.2852-0.042969c-0.55469 2.2344-0.42969 3.6797-0.28516 5.3398z"/>
                                                                 <path style={{fill:"#3799CE"}} d="m13.629 30.543h0.11328c0.68359 0 1.2422-0.54688 1.25-1.2305 0.007812-0.69141-0.54297-1.2578-1.2344-1.2695h-0.125c-3.8281 0-6.418 2.4961-7.0039 4.9805-0.45703 1.8867-0.31641 3.9492 0.40625 5.9531 0.18359 0.50781 0.66406 0.82422 1.1758 0.82422 0.14062 0 0.28516-0.023437 0.42578-0.074219 0.64844-0.23437 0.98438-0.95312 0.75-1.6016-0.55859-1.543-0.67188-3.1016-0.32812-4.5234 0.29297-1.2383 1.75-3.0586 4.5703-3.0586z"/>
                                                                 <path style={{fill:"#3799CE"}} d="m44.355 30.543h0.11328c0.68359 0 1.2422-0.54688 1.25-1.2305 0.007812-0.69141-0.54297-1.2578-1.2344-1.2695h-0.125c-3.8281 0-6.418 2.4961-7.0039 4.9805-0.45703 1.8906-0.31641 3.9492 0.40625 5.9531 0.18359 0.50781 0.66406 0.82422 1.1758 0.82422 0.14062 0 0.28516-0.023437 0.42578-0.074219 0.64844-0.23437 0.98438-0.95312 0.75-1.6016-0.55859-1.543-0.67188-3.1016-0.32812-4.5234 0.29297-1.2383 1.75-3.0586 4.5703-3.0586z"/>
                                                                 <path style={{fill:"#3799CE"}} d="m90.938 24.734c-2.0039-0.80469-4.1641-0.85938-6.0781-0.14844-0.78516 0.28906-1.8711 0.78125-2.3555 1.0352-0.60938 0.32422-0.83984 1.082-0.51562 1.6914s1.0781 0.83984 1.6914 0.51562c0.35547-0.19141 1.332-0.63281 2.0508-0.89844 1.3359-0.49219 2.8516-0.44922 4.3008 0.13281 1.1836 0.45312 2.207 1.2383 2.9648 2.2891 1.7461 2.3516 2.3086 5.5 1.4688 8.2109-0.4375 1.3984-1.2031 2.6953-2.0156 4.0703-0.55469 0.9375-1.1211 1.8984-1.5938 2.9219l-20.25-0.003907c-0.47656-1.0312-1.0469-2-1.6094-2.9453-0.80859-1.3672-1.5703-2.6562-2.0078-4.0391-0.83984-2.7188-0.27734-5.8672 1.4766-8.2305 0.75-1.0352 1.7695-1.8203 2.957-2.2734 1.0273-0.39453 2.082-0.52734 3.1953-0.38672 0.011718 0 1.2852 0.12891 2.9258 1.2148 0.44141 0.29688 0.85938 0.58984 1.2578 0.87109 1.1641 0.81641 2.2617 1.5859 3.4766 2.1211 1.2891 0.56641 3.2305 1.1055 5.1641 0.46094 0.65625-0.21875 1.0078-0.92578 0.78906-1.582s-0.92578-1.0078-1.5781-0.78906c-1.1758 0.39062-2.4844 0.007813-3.3711-0.37891-0.98438-0.43359-1.9414-1.1016-3.0469-1.8789-0.41406-0.28906-0.84375-0.58984-1.3086-0.90234-2.1953-1.4531-3.9297-1.6133-4.0547-1.6211-1.4727-0.19141-2.9375-0.007812-4.3516 0.53906-1.6406 0.625-3.0547 1.7148-4.0781 3.1328-0.42578 0.57422-0.77734 1.1953-1.0938 1.832-0.3125-0.63281-0.66016-1.25-1.0859-1.8203-1.0352-1.4297-2.4492-2.5156-4.0703-3.1367-2.0039-0.80469-4.1641-0.85938-6.0781-0.14844-0.78516 0.28906-1.8711 0.78125-2.3555 1.0352-0.60937 0.32422-0.83984 1.082-0.51562 1.6914 0.32422 0.60938 1.0781 0.83984 1.6914 0.51562 0.35547-0.19141 1.332-0.63281 2.0508-0.89844 1.332-0.49219 2.8555-0.44922 4.3008 0.13281 1.1836 0.45312 2.207 1.2383 2.9648 2.2891 1.7461 2.3516 2.3086 5.5 1.4688 8.2109-0.4375 1.3984-1.2031 2.6953-2.0156 4.0703-0.55469 0.9375-1.1211 1.8984-1.5938 2.9219l-20.23-0.007813c-0.47656-1.0312-1.0469-2-1.6094-2.9453-0.80859-1.3672-1.5703-2.6562-2.0078-4.0391-0.83984-2.7188-0.27734-5.8672 1.4766-8.2305 0.75-1.0352 1.7695-1.8203 2.957-2.2734 1.0273-0.39453 2.082-0.52734 3.1953-0.38672 0.011719 0 1.2852 0.12891 2.9258 1.2148 0.44141 0.29688 0.85938 0.58984 1.2578 0.87109 1.1641 0.81641 2.2617 1.5859 3.4766 2.1211 1.2891 0.56641 3.2344 1.1055 5.1641 0.46094 0.65625-0.21875 1.0078-0.92578 0.78906-1.582s-0.92578-1.0078-1.5781-0.78906c-1.1797 0.39062-2.4844 0.007813-3.3711-0.37891-0.98438-0.43359-1.9414-1.1016-3.0469-1.8789-0.41406-0.28906-0.84375-0.58984-1.3086-0.90234-2.1953-1.4531-3.9297-1.6133-4.0547-1.6211-1.4727-0.19141-2.9375-0.007812-4.3516 0.53906-1.6406 0.625-3.0547 1.7148-4.0781 3.1328-0.42578 0.57422-0.77734 1.1953-1.0938 1.832-0.3125-0.63281-0.66016-1.25-1.0859-1.8203-1.0352-1.4297-2.4453-2.5156-4.0664-3.1367-2.0039-0.80469-4.1641-0.85938-6.082-0.14844-0.78516 0.28906-1.8711 0.78125-2.3555 1.0352-0.60938 0.32422-0.83984 1.082-0.51562 1.6914s1.0781 0.83984 1.6914 0.51562c0.35547-0.19141 1.332-0.63281 2.0508-0.89844 1.332-0.49219 2.8555-0.44922 4.3008 0.13281 1.1836 0.45312 2.207 1.2383 2.9648 2.2891 1.7461 2.3516 2.3086 5.5 1.4688 8.2109-0.4375 1.3984-1.2031 2.6953-2.0156 4.0703-0.55469 0.9375-1.1211 1.8984-1.5938 2.9219l-20.23-0.007813c-0.47656-1.0312-1.0469-2-1.6094-2.9453-0.80859-1.3672-1.5703-2.6562-2.0078-4.0391-0.83984-2.7188-0.27734-5.8672 1.4766-8.2305 0.75-1.0352 1.7695-1.8203 2.957-2.2734 1.0273-0.39453 2.082-0.52734 3.1953-0.38672 0.011719 0 1.2852 0.12891 2.9258 1.2148 0.44531 0.29688 0.86328 0.58984 1.2656 0.875 1.1602 0.8125 2.2578 1.5859 3.4688 2.1172 1.293 0.56641 3.2344 1.1055 5.1641 0.46094 0.65625-0.21875 1.0078-0.92578 0.78906-1.582s-0.92578-1.0078-1.5781-0.78906c-1.1797 0.39062-2.4805 0.007813-3.3711-0.37891-0.98438-0.43359-1.9375-1.1016-3.0391-1.875-0.41406-0.29297-0.84766-0.59375-1.3125-0.90625-2.1992-1.4531-3.9336-1.6133-4.0547-1.6211-1.4766-0.19141-2.9375-0.007812-4.3516 0.53906-1.6406 0.625-3.0547 1.7148-4.0781 3.1328-2.2148 2.9766-2.9219 6.9805-1.8516 10.453 0.52344 1.6641 1.3984 3.1406 2.2422 4.5664 0.67969 1.1484 1.3203 2.2344 1.7734 3.3789 0.1875 0.47656 0.64844 0.79297 1.1641 0.79297l21.902-0.003907c0.51172 0 0.97266-0.3125 1.1641-0.78906 0.45312-1.1406 1.0859-2.2188 1.7617-3.3633 0.51562-0.87891 1.0352-1.7812 1.4883-2.7266 0.44922 0.9375 0.96875 1.8359 1.4844 2.707 0.67969 1.1484 1.3203 2.2344 1.7734 3.3789 0.1875 0.47656 0.64844 0.79297 1.1641 0.79297h21.895c0.51172 0 0.97266-0.3125 1.1641-0.78906 0.45312-1.1406 1.0859-2.2188 1.7617-3.3633 0.51562-0.87891 1.0352-1.7812 1.4883-2.7266 0.44922 0.9375 0.96875 1.8359 1.4844 2.707 0.67969 1.1484 1.3203 2.2344 1.7734 3.3789 0.1875 0.47656 0.64844 0.79297 1.1641 0.79297h21.887c0.51172 0 0.97266-0.3125 1.1641-0.78906 0.45312-1.1406 1.0859-2.2188 1.7617-3.3633 0.84375-1.4336 1.7188-2.918 2.2461-4.5938 1.0703-3.4648 0.36328-7.4688-1.8438-10.434-1.0312-1.4297-2.4453-2.5156-4.0664-3.1367z"/>
                                                                 <path style={{fill:"#3799CE"}} d="m75.082 30.543h0.09375c0.68359 0.050781 1.2578-0.54297 1.2656-1.2305 0.007813-0.69141-0.54297-1.2578-1.2344-1.2695h-0.125c-3.8281 0-6.418 2.4961-7.0039 4.9805-0.45703 1.8906-0.31641 3.9492 0.40625 5.9531 0.18359 0.50781 0.66406 0.82422 1.1758 0.82422 0.14062 0 0.28516-0.023437 0.42578-0.074219 0.64844-0.23437 0.98437-0.95312 0.75-1.6016-0.55859-1.543-0.67188-3.1016-0.32813-4.5234 0.29688-1.2383 1.7539-3.0586 4.5742-3.0586z"/>
                                                                 </svg>
                                                           </Avatar>
                                                           <Text fw={500} ml={6}> Zirconia</Text>
                                                               </div>
                                                           </Group>
                                                             </Menu.Item>
                                                             </Menu.Dropdown>
                                                           </Menu>
           <Tooltip
                 label="Denture"
                 withArrow
                 className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                   >
             <Button
               styles={{
                 root: {
                   position: 'relative',
                   color: 'white',
                   height: '35px', // Adjust button height
                   width: '35px',  // Adjust button width
                   padding: 0,
                   borderRadius: '0.5rem'
                 },
               }}
             >
               {/* SVG in the middle */}
               <div
           style={{
             display: 'flex',
             justifyContent: 'center',
             alignItems: 'center',
             height: '100%',
             width: '100%',
           }}
           >
             <span  className={
             activeButton  === "Denture"
               ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
               : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
           }
           onClick={() => {
             handleButtonClick("Denture");
             setTargetPath("48");
           }}
                     >
           <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" viewBox="0 0 200 250" x="0px" y="0px">
           <path style={{ fill: "#f5f5f5" }} d="M81.50276,99.34975a19.183,19.183,0,0,0,13.56049.01477,3.9079,3.9079,0,0,0,2.52856-3.03333,41.42989,41.42989,0,0,0,.21606-12.35046A12.464,12.464,0,0,0,94.22243,76.737c-3.28333-3.191-6.94879-3.19055-11.0387.1048a13.08632,13.08632,0,0,0-4.73358,8.96558A38.501,38.501,0,0,0,78.99074,96.4,3.90159,3.90159,0,0,0,81.50276,99.34975Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M74.933,74.69441a11.20236,11.20236,0,0,0-10.8407,1.18414,5.51714,5.51714,0,0,0-2.36273,3.24109,30.25884,30.25884,0,0,0-.29468,14.50983,5.61916,5.61916,0,0,0,3.99158,4.114,19.26465,19.26465,0,0,0,9.92346.139,7.76628,7.76628,0,0,1-.189-.78943,42.63142,42.63142,0,0,1-.58374-11.66351,17.28758,17.28758,0,0,1,3.17993-8.44464A6.44071,6.44071,0,0,0,74.933,74.69441Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M57.03749,72.75417a10.56981,10.56981,0,0,0-9.63989,1.33429,5.11786,5.11786,0,0,0-2.20709,2.99646,28.48221,28.48221,0,0,0-.30573,13.65369A5.26216,5.26216,0,0,0,48.60951,94.658a18.10067,18.10067,0,0,0,9.12842.21124c-.03333-.11676-.06671-.23907-.09448-.36133a34.2021,34.2021,0,0,1,.32245-16.37781,9.41705,9.41705,0,0,1,1.78455-3.519A6.41461,6.41461,0,0,0,57.03749,72.75417Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M104.93874,99.36452a19.18248,19.18248,0,0,0,13.5603-.01477A3.90178,3.90178,0,0,0,121.01112,96.4a38.50358,38.50358,0,0,0,.54065-10.59259,13.08639,13.08639,0,0,0-4.73364-8.96558c-4.08984-3.29535-7.75537-3.29578-11.0387-.1048a12.46387,12.46387,0,0,0-3.58533,7.24371,41.42975,41.42975,0,0,0,.21582,12.35046A3.90819,3.90819,0,0,0,104.93874,99.36452Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M124.65187,97.8824a19.26512,19.26512,0,0,0,9.92358-.139,5.6192,5.6192,0,0,0,3.99146-4.114,30.25732,30.25732,0,0,0-.29468-14.50983,5.5168,5.5168,0,0,0-2.36267-3.24109,11.20238,11.20238,0,0,0-10.8407-1.18414,6.44037,6.44037,0,0,0-2.8241,2.29041,17.28757,17.28757,0,0,1,3.17993,8.44464A42.629,42.629,0,0,1,124.841,97.093,7.75034,7.75034,0,0,1,124.65187,97.8824Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M142.26393,94.86922a18.10067,18.10067,0,0,0,9.12842-.21124,5.26206,5.26206,0,0,0,3.72473-3.91937,28.48148,28.48148,0,0,0-.30579-13.65369,5.117,5.117,0,0,0-2.207-2.99646,10.56978,10.56978,0,0,0-9.63989-1.33429,6.41366,6.41366,0,0,0-2.71289,1.85687,9.415,9.415,0,0,1,1.78442,3.519,34.20212,34.20212,0,0,1,.32251,16.37781C142.33058,94.63014,142.29726,94.75246,142.26393,94.86922Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M78.87874,124.187a5.05264,5.05264,0,0,0,2.87317,3.82385,14.58254,14.58254,0,0,0,13.0545.0506,4.977,4.977,0,0,0,2.75024-3.67187,55.60663,55.60663,0,0,0,.16064-15.33588,5.04,5.04,0,0,0-3.95459-4.26447,26.63105,26.63105,0,0,0-11.05811-.00006A5.04016,5.04016,0,0,0,78.75,109.05367,55.70243,55.70243,0,0,0,78.87874,124.187Z"/>
           <path  style={{ fill: "#f5f5f5" }} d="M76.73976,104.19222a5.06025,5.06025,0,0,0-1.05072-.34467,25.821,25.821,0,0,0-10.72394,0,4.878,4.878,0,0,0-3.83038,4.13055,53.824,53.824,0,0,0,.12225,14.67664,4.89758,4.89758,0,0,0,2.78528,3.70813,14.17873,14.17873,0,0,0,11.58563.52258A8.60762,8.60762,0,0,1,75.033,124.784a59.82378,59.82378,0,0,1-.139-16.25549A8.96981,8.96981,0,0,1,76.73976,104.19222Z"/>
           <path style={{ fill: "#f5f5f5" }}d="M59.41682,102.80238a4.37552,4.37552,0,0,0-1.5899-.67822,23.5916,23.5916,0,0,0-9.74554,0,4.44069,4.44069,0,0,0-3.4801,3.75812,49.13206,49.13206,0,0,0,.11115,13.33685,4.46085,4.46085,0,0,0,2.52954,3.369,12.9339,12.9339,0,0,0,10.16241.61145A57.9102,57.9102,0,0,1,57.2765,107.45,8.74689,8.74689,0,0,1,59.41682,102.80238Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M102.44533,124.38954a4.97673,4.97673,0,0,0,2.75,3.67188,14.58293,14.58293,0,0,0,13.05469-.0506,5.05276,5.05276,0,0,0,2.873-3.82385,55.7,55.7,0,0,0,.12891-15.1333,5.04037,5.04037,0,0,0-3.95471-4.26453,26.63047,26.63047,0,0,0-11.058.00006,5.0398,5.0398,0,0,0-3.95459,4.26447A55.58766,55.58766,0,0,0,102.44533,124.38954Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M124.374,126.88545a14.1786,14.1786,0,0,0,11.58557-.52258,4.89772,4.89772,0,0,0,2.78528-3.70813,53.82153,53.82153,0,0,0,.12231-14.67664,4.87826,4.87826,0,0,0-3.83044-4.13055,25.82128,25.82128,0,0,0-10.724,0,5.0609,5.0609,0,0,0-1.05066.34467,8.96924,8.96924,0,0,1,1.8457,4.3363,59.82133,59.82133,0,0,1-.139,16.25549A8.60472,8.60472,0,0,1,124.374,126.88545Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M152.7599,122.58809a4.46058,4.46058,0,0,0,2.52954-3.369,49.13941,49.13941,0,0,0,.11121-13.33685,4.4409,4.4409,0,0,0-3.48022-3.75812,23.5913,23.5913,0,0,0-9.74548,0,4.37716,4.37716,0,0,0-1.59.67822,8.74607,8.74607,0,0,1,2.14038,4.64758,57.90776,57.90776,0,0,1-.12793,15.74957A12.934,12.934,0,0,0,152.7599,122.58809Z"/>
           <path style={{ fill: "#3799CE" }} d="M162.42458,130.66841a41.01544,41.01544,0,0,0-4.4519-7.71344,8.31738,8.31738,0,0,1-3.58008,3.16321,17.96006,17.96006,0,0,1-7.533,1.74573,16.37692,16.37692,0,0,1-5.59827-1.00629l-.06677-.02783a8.78045,8.78045,0,0,1-3.60791,3.06879,19.37357,19.37357,0,0,1-8.1167,1.879,17.47232,17.47232,0,0,1-6.53223-1.27856l-.90613-.3559a9.0125,9.0125,0,0,1-2.15149,1.401,19.85284,19.85284,0,0,1-8.32788,1.94019,17.40325,17.40325,0,0,1-4.06934-.48358,19.24956,19.24956,0,0,1-3.9917-1.43994A8.91816,8.91816,0,0,1,100,128.531a8.87923,8.87923,0,0,1-3.49121,3.02979,18.29637,18.29637,0,0,1-8.05554,1.92352,19.73116,19.73116,0,0,1-8.32788-1.94019,8.77479,8.77479,0,0,1-2.157-1.401l-.90057.3559a17.51485,17.51485,0,0,1-6.53223,1.27856,19.3128,19.3128,0,0,1-8.12219-1.879,8.766,8.766,0,0,1-3.60242-3.06879l-.07233.02783a16.353,16.353,0,0,1-5.59821,1.00629,17.88325,17.88325,0,0,1-7.52179-1.74017A8.363,8.363,0,0,1,42.0273,122.955a41.01574,41.01574,0,0,0-4.45184,7.71344A6.68945,6.68945,0,0,0,43.1794,140.036a40.95325,40.95325,0,0,1,14.45294,3.50525c8.61456,4.13483,22.35858,8.67133,35.49939,5.50055a29.22208,29.22208,0,0,1,13.73657,0c13.14075,3.17078,26.88477-1.36572,35.49939-5.50055a40.95325,40.95325,0,0,1,14.45288-3.50525A6.68951,6.68951,0,0,0,162.42458,130.66841Z"/>
           <path style={{ fill: "#3799CE" }} d="M42.38307,73.816a8.92881,8.92881,0,0,1,2.92975-3.01312,14.50252,14.50252,0,0,1,13.03662-1.71228,10.30723,10.30723,0,0,1,4.353,2.98535l.0556.06671A14.98921,14.98921,0,0,1,76.42842,71.1031a10.309,10.309,0,0,1,4.00275,2.97418c.1-.08893.20569-.17786.31134-.26678,5.576-4.492,11.48-4.442,16.19427.13336A15.923,15.923,0,0,1,100,78.14119a15.82063,15.82063,0,0,1,3.06873-4.19733c4.70886-4.57532,10.61279-4.62537,16.18884-.13336.11108.08893.21118.17786.31689.26678a10.33673,10.33673,0,0,1,4.00269-2.9798A14.99571,14.99571,0,0,1,137.242,72.14265l.05566-.06671a10.34135,10.34135,0,0,1,4.35291-2.98535,14.51577,14.51577,0,0,1,13.04211,1.71228,8.92993,8.92993,0,0,1,2.92993,3.01312,40.35828,40.35828,0,0,0,4.3645-7.22968,6.67067,6.67067,0,0,0-6.08081-9.5556c-5.35754.09045-11.97314-.42737-17.02979-2.85583A43.08769,43.08769,0,0,0,107.5569,51.77a26.55164,26.55164,0,0,1-15.11371,0,43.08773,43.08773,0,0,0-31.31964,2.40485c-5.05658,2.42841-11.67181,2.94623-17.0293,2.85583a6.67075,6.67075,0,0,0-6.08026,9.5567A40.18759,40.18759,0,0,0,42.38307,73.816Z"/>
           </svg>
                     </span>
               </div>
               {/* "CL" in the bottom-right corner */}
               <span
                 style={{
                   position: 'absolute',
                   bottom: '0px',
                   right: '0px',
                   fontSize: '8px',
                   fontWeight: '800',
                   backgroundColor: 'white',
                   // borderRadius:'0.125rem' ,
                   borderTopLeftRadius: '0.5rem' ,
                   borderBottomRightRadius: '0.5rem' ,
                   color:'#3799CE',
                   padding:'3px  0px 1px 2px' ,
                 }}
                 className="h-[14px] w-[14px] "
               >
                   De
               </span>
             </Button>
             </Tooltip>
               <Tooltip
                  label="Bridge"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                    >
                <Button
                styles={{
                  root: {
                    position: 'relative',
                    color: 'white',
                    height: '35px', // Adjust button height
                    width: '35px',  // Adjust button width
                    padding: 0,
                    borderRadius: '0.5rem'
                  },
                }}
              >
                {/* SVG in the middle */}
                <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              width: '100%',
            }}
            >
              <span  className={
              activeButton  === "Bridge"
                ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
            }
            onClick={() => {
              handleButtonClick("Bridge");
              setTargetPath("52");
            }}
                >
                <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 488 535" >
                <g><rect x="220.444" y="256.78" width="47.111" height="33.269"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M281.333,239.022v-9.754h-74.667v9.754c0,5.38,4.387,9.758,9.78,9.758h55.107   C276.946,248.78,281.333,244.403,281.333,239.022z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M116.417,289.357c0.709,4.793,1.362,9.615,1.985,14.447h94.042V256.31c-7.88-1.82-13.778-8.872-13.778-17.287v-9.754   h-0.591h-92.868c0.199,1.035,0.393,2.07,0.624,3.1C110.042,251.153,113.604,270.327,116.417,289.357z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M151.057,184.585h185.886c19.845-19.055,31.65-49.986,31.65-83.122C368.593,45.516,335.363,0,294.519,0   c-8.883,0-17.574,2.144-25.832,6.371c-15.458,7.915-33.916,7.916-49.374,0C211.056,2.144,202.365,0,193.481,0   c-40.845,0-74.074,45.516-74.074,101.463C119.407,134.6,131.212,165.531,151.057,184.585z"/>
                <rect x="220.444" y="298.049" width="47.111" height="28.683"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M220.444,353.614c0,7.932,6.466,14.386,14.415,14.386h18.282c7.948,0,14.415-6.454,14.415-14.386v-18.882h-47.111V353.614z   "/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M433.076,303.805H488v-83.419C469.059,237.828,446.242,265.221,433.076,303.805z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M202.667,221.268h82.667h4.591c5.392,0,9.779-4.377,9.779-9.758v-18.925H188.296v18.925c0,5.38,4.387,9.758,9.779,9.758   H202.667z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M441.481,0c-30.755,0-58.527,26.422-69.354,65.842c2.891,11.172,4.465,23.156,4.465,35.621   c0,12.291-1.546,24.314-4.464,35.634c2.146,7.809,4.994,15.253,8.487,22.134c11.696,23.035,15.02,49.63,9.358,74.887   c-4.168,18.593-7.693,37.572-10.478,56.409c-2.443,16.525-4.377,33.352-5.748,50.013c-0.491,5.964,1.469,11.669,5.519,16.065   c4.044,4.391,9.563,6.809,15.541,6.809c10.778,0,19.805-8.06,20.997-18.748c1.635-14.658,4.845-29.124,9.542-42.995   c14.947-44.145,42.025-74.232,62.653-92V22.579C474.847,8.005,458.379,0,441.481,0z"/>
                <path style={{ fill: "#3799CE" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M423.756,345.554c-1.644,14.743-14.089,25.861-28.948,25.861c-8.12,0-15.929-3.422-21.425-9.389   c-5.501-5.972-8.273-14.042-7.607-22.142c0.768-9.336,1.729-18.72,2.837-28.079h-93.057v41.81   c0,12.344-10.055,22.386-22.415,22.386h-18.282c-12.359,0-22.415-10.042-22.415-22.386v-41.81h-93.057   c1.109,9.359,2.069,18.743,2.837,28.079c0.667,8.099-2.106,16.169-7.607,22.142c-5.496,5.967-13.305,9.389-21.425,9.389   c-14.859,0-27.304-11.118-28.948-25.861c-1.276-11.444-3.568-22.759-6.796-33.749H0V428h488V311.805h-57.448   C427.324,322.795,425.032,334.11,423.756,345.554z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M371.583,289.357c2.813-19.031,6.375-38.205,10.585-56.989c0.231-1.03,0.425-2.065,0.624-3.1h-92.868h-0.591v9.754   c0,8.416-5.898,15.467-13.778,17.287v47.495h94.042C370.22,298.972,370.874,294.151,371.583,289.357z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M0,220.386v83.419h54.924C41.758,265.221,18.941,237.828,0,220.386z"/>
                <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M62.653,301.672c4.697,13.871,7.908,28.337,9.542,42.995c1.191,10.688,10.218,18.748,20.997,18.748   c5.978,0,11.497-2.418,15.541-6.809c4.049-4.396,6.009-10.102,5.519-16.065c-1.371-16.661-3.305-33.487-5.748-50.013   c-2.785-18.837-6.31-37.816-10.478-56.409c-5.661-25.257-2.337-51.852,9.358-74.887c3.493-6.881,6.342-14.325,8.487-22.134   c-2.918-11.32-4.464-23.343-4.464-35.634c0-12.466,1.574-24.449,4.465-35.621C105.045,26.422,77.274,0,46.519,0   C29.621,0,13.153,8.005,0,22.579v187.093C20.628,227.439,47.706,257.527,62.653,301.672z"/></g>

                </svg>
                      </span>
                </div>
                {/* "CL" in the bottom-right corner */}
                <span
                  style={{
                    position: 'absolute',
                    bottom: '0px',
                    right: '0px',
                    fontSize: '8px',
                    fontWeight: '800',
                    backgroundColor: 'white',
                    // borderRadius:'0.125rem' ,
                    borderTopLeftRadius: '0.5rem' ,
                    borderBottomRightRadius: '0.5rem' ,
                    color:'#3799CE',
                    padding:'3px  0px 1px 2px' ,
                  }}
                  className="h-[14px] w-[14px] "
                >
                    Br
                </span>
              </Button>
              </Tooltip>
          </div>
        </Flex>
      </Container>

     {isAlertVisible && isPathSelectionActive && clickedIds.length > 0 &&(
     <div className="flex justify-between border-2 p-1 px-1">
       <Alert
         title={`SelectTeeth : ${getFormattedOutput() || ""}`}
         icon={icon}
         withCloseButton
         onClose={() => setIsAlertVisible(false)} // Close alert
         w={"100%"}
         bg={"cyan"}
         color="white"
       ></Alert>
     </div>
   )}
      <div className=" h-full px-0   border-t-[3px] my-2 border-base-300">
    <Flex style={{ position: 'relative', width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:220}}>
        <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
          </div>
      </Flex>
    <div className=" max-w-[920px] mt-2 mx-auto">

    <div className=" flex h-full px-0  max-h-[320px] ">
                {Dantal.map((svgData) => (
                <div
                  key={svgData.svg_id}
                  className="h-[230px] cursor-pointer hover:bg-[#F2F5F8] parent-div"
                  onClick={() => handleSvgClick(svgData.svg_id)}

                >
             {!hiddenSvgIds.includes(svgData.svg_id) ? (
                  <DentalSvg
                    svgData={svgData}
                    isHidingMode={isHidingMode}
                    highlightedPaths={highlightedPaths}
                    onPathClick={onPathClick}
                    isPathSelectionActive={isPathSelectionActive}
                    hiddenPaths={hiddenPaths}
                    isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                    isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                    isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                  />
                ) : (
              <div
                style={{
                  height: '200px', // Match SVG height
                  width: svgData.width, // Match SVG width
                  alignItems: 'center',
                }}
              />
            )}
            <div className="mt-0.5 flex">
              <Text
                ta="center"
                h={30}
                key={svgData.svg_id}
                className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                style={{ width: svgData.width }}
              >
                {svgData.svg_id}
              </Text>
            </div>
          </div>
                ))}

         </div>
        </div>
        </div>
        <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
        <div className=" h-full px-0   border-b-[3px] border-base-300">
           <Flex style={{ position: 'relative',  width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:14}}>
        <Image
              src={dentalButtom}
              alt={"dentalButtom"}
              width={32}
              height={32}
            />
          </div>
      </Flex>
        <div className=" max-w-[920px]  mx-auto">
            <div className=" flex h-full px-0  max-h-[320px] ">
            <div className="flex h-full px-0 max-h-[320px]">
        {DantalB.map((svgData) => (
       <div
                      key={svgData.svg_id}
                     className="h-[230px]  cursor-pointer hover:bg-[#F2F5F8] parent-div"
                      onClick={() => handleSvgClick(svgData.svg_id)}
                    >
                       <div className="mt-0.5 flex  ">
                   <Text
                       ta="center"
                       h={30}
                       key={svgData.svg_id}
                       className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                       style={{ width: svgData.width }}
                     >
                       {svgData.svg_id}
                     </Text>
                     </div>
         {!hiddenSvgIds.includes(svgData.svg_id) ? (
           <DentalSvg
                  svgData={svgData}
                  isHidingMode={isHidingMode}
                  highlightedPaths={highlightedPaths}
                  onPathClick={onPathClick}
                  isPathSelectionActive={isPathSelectionActive}
                  hiddenPaths={hiddenPaths}
                  isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                  isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                  isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                />
         ) : (
           <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
         )}

                     </div>
                   ))}
     </div>
        </div>
        </div>
        </div>
       {/* <Divider  my="8px" variant="dashed" /> */}
            <div className="mx-auto w-[880px] max-w-[880px] mb-4 ">
            <br/>
            <Button onClick={toggle} className="hover:bg-[#3799CE]/90  w-full">Register </Button>
            </div>

    <div className="border-base-200 mx-4 border-t mt-4">
      <Tabs
        variant="unstyled"
        defaultValue="Therapy"
        classNames={classes}
      >
            <Tabs.List grow className="space-x-0 gap-0   mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={
                  <IconMessageCircle
                    style={{ width: rem(16), height: rem(16) }}
                  />
                }
              >
               All Procedures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={
                  <IconSettings style={{ width: rem(16), height: rem(16) }} />
                }
              >
               Planned
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
              Completed
              </Tabs.Tab>
              <Tabs.Tab
                value="Inventory"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
                Inventory
              </Tabs.Tab>
            </Tabs.List>
             <Tabs.Panel value="All Procedures" className=" mt-2">
           <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsAllProcedures}</Table.Tbody>
              </Table>
              </Tabs.Panel>
            <Tabs.Panel value="Planned" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsPlanned}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Completed" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsCompleted}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Inventory" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsInventory}</Table.Tbody>
              </Table>
            </Tabs.Panel>
      </Tabs>
      <Button rightSection={<MdPrint size={14} />} className="hover:bg-[#3799CE]/90  " mt={6}>Imprimer</Button>
      </div>

            <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
                <Group align="flex">
                < IconDeviceFloppy stroke={2} />
            <Text size="sm" mb="xs" fw={500}>
            Save Modification
            </Text>
            </Group>
            <Group align="flex-end">
              <Button w={"100%"} onClick={sendPathsToApi} className="hover:bg-[#3799CE]/90">save</Button>
            </Group>
          </Dialog>

     </div>
        </Tabs.Panel>
        <Tabs.Panel value="Surgery">
        <div className="my-4 ">
        <Container>
      <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px'}} align="center">
        <div  style={{ position: 'absolute', right: 0 }}>
          <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>All Procedures</Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Download zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Preview all
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Delete all
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          </div>
        <div style={{ margin: '0 auto' }} className=" mb-2 flex flex-end p-2 sm:justify-start space-x-2 ">
           <Tooltip
                          label="Extraction"
                          withArrow
                            className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                            >
                      <Button
                        styles={{
                          root: {
                            position: 'relative',
                            color: 'white',
                            height: '35px', // Adjust button height
                            width: '35px',  // Adjust button width
                            padding: 0,
                            borderRadius: '0.5rem'
                          },
                        }}
                      >
                        {/* SVG in the middle */}
                        <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: '100%',
                      width: '100%',
                    }}
                  >
                  <span  className={
                activeButton  === "Extraction"
                  ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                  : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
              }
              onClick={() => {
                handleButtonClick("Extraction");
                setTargetPath("53");


              }}
                        >
            <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" ><g>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}} d="M89.64,45.95L53.67,32.46l-5.33,2.06c0,0,0.51,2.91-2.59,7.18v3.01l41.02,19.11c2.46,1.14,5.27-0.65,5.27-3.36V49.42   C92.04,47.87,91.08,46.49,89.64,45.95z"/>
              <circle cx="33.04" cy="32.77" r="4.42"/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}} d="M88.94,2.16L32.71,19.58c-3.36,1.04-6.36,3.08-8.41,5.94c-1.92,2.68-3.31,6.39-2.14,11.08   c2.65,10.58,20.48,7.63,20.48,7.63v-3.82c0,0,3.18-4.41,2.81-8.23l45.5-13.07c1.51-0.43,2.55-1.82,2.55-3.39V5.53   C93.51,3.15,91.21,1.46,88.94,2.16z M33.04,38.94c-3.4,0-6.16-2.77-6.16-6.16c0-3.4,2.77-6.16,6.16-6.16c3.4,0,6.16,2.77,6.16,6.16   S36.44,38.94,33.04,38.94z"/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M32.52,47.24c0,0,4.9,4.27,5.24,7.8c0.35,3.53-1.42,9.99-4.06,13.23c-2.65,3.23-4.7,5.59-1.76,6.47   c0.22,0.07,0.52,0.1,0.89,0.1c4.55,0,19.44-5.32,20.57-23.29l-8.53-4.72L32.52,47.24z"/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M16.12,53.11c0.9,0,1.81,0.08,2.71,0.21c-0.01-0.78-0.03-1.66-0.08-2.61c-0.18-3.53,10.85-4.18,10.85-4.18   s-7.28-1.91-9.78-8.46c-2.78-7.25,2.99-14.22,2.99-14.22C9.65,26.77,11.12,47.63,12.15,53.74C13.34,53.32,14.65,53.11,16.12,53.11z   "/>
              <path style={{ fill: "#f5f5f5",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M30.78,77.07c-1.37-0.41-2.23-1.16-2.56-2.22c-0.62-2.03,0.98-3.99,3.2-6.69l0.27-0.33c2.07-2.53,3.53-7.36,3.69-10.57   c-0.22-0.1-0.46-0.21-0.7-0.31c0,0-0.25-0.32-1.73-0.32c-0.74,0-1.8,0.08-3.28,0.32C29.35,57,29,57.03,28.64,57.03   c-3.35,0-8.15-2.08-12.53-2.08c-1.62,0-3.19,0.29-4.6,1.07C5.9,59.14,5.17,67.16,8.45,72.99c3.28,5.83,3.23,22.71,7.99,24.78   c0.37,0.16,0.71,0.23,1.03,0.23c3.8,0,4.32-10.46,7.01-10.8c0.05-0.01,0.09-0.01,0.14-0.01c2.74,0,2.49,10.81,6.66,10.81   c0.04,0,0.09,0,0.13,0c3.64-0.19,4.41-13.86,5.87-21.8c-1.98,0.7-3.77,1.05-5.1,1.05C31.62,77.25,31.17,77.19,30.78,77.07z"/></g>
            </svg>
                              </span>
                        </div>
                        {/* "CL" in the bottom-right corner */}
                        <span
                            style={{
                            position: 'absolute',
                            bottom: '0px',
                            right: '0px',
                            fontSize: '8px',
                            fontWeight: '800',
                            backgroundColor: 'white',
                            // borderRadius:'0.125rem' ,
                            borderTopLeftRadius: '0.5rem' ,
                            borderBottomRightRadius: '0.5rem' ,
                            color:'#3799CE',
                              padding:'3px  0px 1px 2px' ,
                          }}
                          className="h-[14px] w-[14px] "
                        >
                              Ex
                        </span>
                      </Button>
            </Tooltip>
            <Tooltip
                label="Implant"
                withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  >
            <Button
              styles={{
                root: {
                  position: 'relative',
                  color: 'white',
                  height: '35px', // Adjust button height
                  width: '35px',  // Adjust button width
                  padding: 0,
                  borderRadius: '0.5rem'
                },
              }}
            >
              {/* SVG in the middle */}
              <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
          }}
          >
            <span  className={
          activeButton  === "Implant"
          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
          }
          // onClick={() => handleColorSelect("#181C14", "fill")}
          onClick={() => {
          handleButtonClick("Implant");
          setTargetPath("54");
          }}
          >
          <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 16.6 17"  >
          <path style={{display:"inline",fill:"#eaecf1",stroke:"#FFFFFF",strokeMiterlimit:"10"}} d="M13,7.9L13,7.9c0.3-0.8,0.5-1.7,0.5-2.5V4.9c0-0.8-0.2-1.6-0.7-2.3c0,0,0,0,0,0C12.1,1.6,11,1,9.8,1   C9.7,1,9.5,1,9.3,1c-0.3,0-0.7,0-1,0.1c-0.1,0-0.1,0-0.2,0l-0.2,0c-0.2,0-0.6,0-0.8-0.1C6.9,1,6.6,1,6.4,1l-0.7,0   c-1.1,0-2.2,0.4-2.9,1.3C2.4,2.7,2,3.4,2,4.3c0,0,0,0.1,0,0.3c0,1.2,0.2,2.4,0.6,3.6l0,0L13,8.2L13,7.9z"/>
          <path style={{display:"inline",fill:"#eaecf1",stroke:"#FFFFFF",strokeMiterlimit:"10"}} d="M5.8,2.3c0,0-2.5,0-2.5,3.2"/>
          <path style={{display:"inline",fill:"#eaecf1",stroke:"#FFFFFF",strokeMiterlimit:"10"}} d="M4.9,9.4v5c0,0.1,0,0.3,0.1,0.4l0.7,1c0.1,0.2,0.3,0.3,0.6,0.3l2.8,0.1c0.3,0,0.5-0.1,0.6-0.3l0.7-1.1   c0.1-0.1,0.1-0.3,0.1-0.4V9.5"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="10.4" y1="9.2" x2="4.1" y2="10.5"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="11.3" y1="10.6" x2="4.1" y2="11.8"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="11.3" y1="12" x2="4.2" y2="13.3"/>
          <line  style={{fill:"none",stroke:"#3799ce",strokeWidth:"0.75",strokeMiterlimit:"10"}} x1="11.4" y1="13.3" x2="4.2" y2="14.8"/>
          <rect  x="3.3" y="8.2"  width="8.9" height="1.2"/>
          </svg>
                    </span>
              </div>
              {/* "CL" in the bottom-right corner */}
              <span
                  style={{
                  position: 'absolute',
                  bottom: '0px',
                  right: '0px',
                  fontSize: '8px',
                  fontWeight: '800',
                  backgroundColor: 'white',
                  // borderRadius:'0.125rem' ,
                  borderTopLeftRadius: '0.5rem' ,
                  borderBottomRightRadius: '0.5rem' ,
                  color:'#3799CE',
                    padding:'3px  0px 1px 2px' ,
                }}
                className="h-[14px] w-[14px] "
              >
                    Im
              </span>
            </Button>
            </Tooltip>
            <Tooltip
                label="Bone"
                withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  >
            <Button
              styles={{
                root: {
                  position: 'relative',
                  color: 'white',
                  height: '35px', // Adjust button height
                  width: '35px',  // Adjust button width
                  padding: 0,
                  borderRadius: '0.5rem'
                },
              }}
            >
              {/* SVG in the middle */}
              <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
          }}
          >
            <span  className={
          activeButton  === "Bone"
          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
          }
          onClick={() => {
          handleButtonClick("Bone");
          setTargetPath("66");
          }}  >
          <svg xmlns="http://www.w3.org/2000/svg"   version="1.1"
          viewBox="0 0 1777.78 2222.225" x="0px" y="0px" fillRule="evenodd" clipRule="evenodd"><defs>
          </defs><g>
          <path  style={{ fill: "#f5f5f5", stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10 }} d="M431.91 361.84c-2.91,-99.39 -3.21,-158.24 1.4,-200.51 11.91,-109.19 56.6,-148.61 173.68,-159.47 89.92,-1.14 192.84,32.69 283.6,31.61 96.31,-1.14 180.48,-37.06 275.63,-33.18 118.19,4.82 169.38,38.86 179.75,196.87 2.41,36.7 2.61,79.83 0.95,164.39 -0.31,16.11 -13.87,25.63 -29.61,29.04 -281.37,61 -565.54,78.24 -855.79,0 -15.46,-4.17 -29.14,-12.76 -29.61,-28.75zm-209.04 -361.84l-7.53 548.22 139.69 -10.11 -181.51 286.45 -173.52 -272.21 122.62 -4.69 7.5 -545.72 92.76 -1.93zm1422.74 0l-7.53 548.22 139.69 -10.11 -181.51 286.45 -173.52 -272.21 122.62 -4.69 7.5 -545.72 92.76 -1.93zm-871.92 1443.06c6.45,120.73 19.6,135.83 -13.57,132.83 -163.08,-43.72 -178.71,-236.19 -184.76,-279.79 -11.04,-79.58 -74.22,-126.3 -78.48,-194.39 -6.55,-104.5 -10.26,-280.19 -4.19,-335.81 10.35,-94.95 49.22,-129.24 151.04,-138.69 46.88,-0.05 78.28,12.67 108.74,28.42 70.66,36.52 102.79,77.71 186.04,72.99 48.34,-2.74 51.92,-23.42 25.5,-25.96 -42.5,-4.09 -78.56,-13.57 -103.98,-26.83 -19.09,-9.96 -8.04,-28.52 6.08,-28.54 90.05,3.27 160.44,-19.83 217.26,-21.1 143.42,-3.2 192.23,6.93 203,170.87 3.39,51.72 1.76,217.96 -3.67,304.65 -4.27,68.09 -67.44,114.81 -78.48,194.39 -6.05,43.6 -21.68,236.07 -184.76,279.79 -33.17,3 -24.6,-12.42 -13.57,-132.83 21.19,-231.19 -243.81,-217.22 -232.21,0zm-767.17 -244.27c213.95,27.05 315.31,-150.36 405.76,-103.35l0.7 11.52c1.06,16.9 4.27,33.49 9.32,49.64 10.83,34.68 28.65,62.7 46.48,93.74 10.14,17.65 20.48,36.88 23.32,57.31 8,57.67 16.21,106.55 39.37,161.13 39.32,92.65 107.91,161.76 206.89,188.3l6.98 1.87 7.2 0.65c28.73,2.6 55.56,-2.55 78.39,-21.63 33.54,-28.04 36.85,-65.15 33.89,-105.04 -2.35,-31.54 -5.5,-62.7 -7.19,-94.36 -1.19,-22.35 1.72,-51.9 17.85,-69.18 5.72,-6.13 14.32,-11.43 23.06,-10.33 25.94,3.28 25.13,60.14 23.65,76.32 -2.97,32.43 -7.24,65.22 -9.45,97.63 -2.76,40.49 2.32,78.76 36.75,106.11 22.83,18.14 49.33,23.04 77.57,20.48l7.2 -0.65 6.99 -1.87c98.97,-26.53 167.56,-95.64 206.88,-188.3 23.16,-54.59 31.37,-103.46 39.37,-161.13 2.83,-20.43 13.19,-39.67 23.32,-57.31 17.83,-31.05 35.64,-59.07 46.48,-93.76 5.05,-16.15 8.26,-32.72 9.31,-49.62 0.26,-4.13 0.5,-8.27 0.74,-12.42 89.97,-43.63 191.41,131.11 403.89,104.25 2.19,-0.28 4.37,-0.48 6.54,-0.64l0 579.63 -1777.78 0 0 -579.63c2.17,0.16 4.35,0.37 6.54,0.64z"/></g>
          </svg>
                    </span>
              </div>
              {/* "CL" in the bottom-right corner */}
              <span
                  style={{
                  position: 'absolute',
                  bottom: '0px',
                  right: '0px',
                  fontSize: '8px',
                  fontWeight: '800',
                  backgroundColor: 'white',
                  // borderRadius:'0.125rem' ,
                  borderTopLeftRadius: '0.5rem' ,
                  borderBottomRightRadius: '0.5rem' ,
                  color:'#3799CE',
                    padding:'3px  0px 1px 2px' ,
                }}
                className="h-[14px] w-[14px] "
              >
                    Bo
              </span>
            </Button>
            </Tooltip>
            <Tooltip
            // قلع جراحي للجذور المتبقية - Surgical extraction for R.R
                label="Resection"
                withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  >
            <Button
              styles={{
                root: {
                  position: 'relative',
                  color: 'white',
                  height: '35px', // Adjust button height
                  width: '35px',  // Adjust button width
                  padding: 0,
                  borderRadius: '0.5rem'
                },
              }}
            >
              {/* SVG in the middle */}
              <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
          }}
          >
            <span  className={
          activeButton  === "Resection"
          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
          }
          onClick={() => {
          handleButtonClick("Resection");
          setTargetPath("67");
          }}
              >
          <svg xmlns="http://www.w3.org/2000/svg" data-name="11 Tooth extraction" viewBox="0 0 64 80" x="0px" y="0px">
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M38.93,32.043c-2.163-.198-4.192,.303-5.904,1.292-.639,.369-1.412,.369-2.051,0-1.712-.99-3.741-1.49-5.904-1.292-4.572,.418-8.375,4.037-8.979,8.588-.492,3.7,1.043,7.069,3.648,9.163,1.422,1.143,2.26,2.856,2.26,4.681v.895c0,3.017,1.141,5.772,3.172,7.802,.53,.53,1.25,.828,2,.828,1.562,0,2.828-1.266,2.828-2.828v-5.172c0-1.105,.895-2,2-2s2,.895,2,2v5.172c0,1.562,1.266,2.828,2.828,2.828,.75,0,1.47-.298,2-.828,2.031-2.031,3.172-4.785,3.172-7.657v-1.041c0-1.825,.838-3.538,2.26-4.681,2.605-2.093,4.14-5.463,3.648-9.163-.605-4.551-4.407-8.17-8.979-8.588Z"/>
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M42.971,21.56l-11.469-3.728C25.16,15.771,20,7.771,20,0H12c0,11.348,7.321,22.285,17.029,25.44l11.469,3.728c6.859,2.229,11.502,8.621,11.502,15.833v9c4.418,0,8-3.582,8-8v-.999c0-10.677-6.875-20.141-17.029-23.441Z"/>
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M43.874,19.763c4.94-5.012,8.126-12.29,8.126-19.763h-8c0,6.806-3.958,13.787-9.202,16.801l8.791,2.857c.097,.032,.188,.073,.285,.105Z"/>
          <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M23.502,29.168l5.263-1.711-.354-.115c-3.219-1.046-6.19-2.883-8.77-5.269-9.384,3.683-15.641,12.746-15.641,22.927v1c0,4.418,3.582,8,8,8v-9c0-7.212,4.643-13.603,11.502-15.833Z"/>
          </svg>
                    </span>
              </div>
              {/* "CL" in the bottom-right corner */}
              <span
                  style={{
                  position: 'absolute',
                  bottom: '0px',
                  right: '0px',
                  fontSize: '8px',
                  fontWeight: '800',
                  backgroundColor: 'white',
                  // borderRadius:'0.125rem' ,
                  borderTopLeftRadius: '0.5rem' ,
                  borderBottomRightRadius: '0.5rem' ,
                  color:'#3799CE',
                    padding:'3px  0px 1px 2px' ,
                }}
                className="h-[14px] w-[14px] "
              >
                    Re
              </span>
            </Button>
            </Tooltip>

        </div>
      </Flex>
     </Container>
     {isAlertVisible && isPathSelectionActive && clickedIds.length > 0 &&(
     <div className="flex justify-between border-2 p-1 px-1">
       <Alert
         title={`SelectTeeth : ${getFormattedOutput() || ""}`}
         icon={icon}
         withCloseButton
         onClose={() => setIsAlertVisible(false)} // Close alert
         w={"100%"}
         bg={"cyan"}
         color="white"
       ></Alert>
     </div>
   )}
     <div className=" h-full px-0   border-t-[3px] my-2">
    <Flex style={{ position: 'relative', width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:220}}>
        <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
          </div>
      </Flex>
    <div className=" max-w-[920px] mt-2 mx-auto">

    <div className=" flex h-full px-0  max-h-[320px] ">
                {Dantal.map((svgData) => (
                <div
                  key={svgData.svg_id}
                  className="h-[230px] cursor-pointer hover:bg-[#F2F5F8] parent-div"
                  onClick={() => handleSvgClick(svgData.svg_id)}

                >
             {!hiddenSvgIds.includes(svgData.svg_id) ? (
                  <DentalSvg
                    svgData={svgData}
                    isHidingMode={isHidingMode}
                    highlightedPaths={highlightedPaths}
                    onPathClick={onPathClick}
                    isPathSelectionActive={isPathSelectionActive}
                    hiddenPaths={hiddenPaths}
                    isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                    isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                    isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                  />
                ) : (
              <div
                style={{
                  height: '200px', // Match SVG height
                  width: svgData.width, // Match SVG width
                  alignItems: 'center',
                }}
              />
            )}
            <div className="mt-0.5 flex">
              <Text
                ta="center"
                h={30}
                key={svgData.svg_id}
                className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                style={{ width: svgData.width }}
              >
                {svgData.svg_id}
              </Text>
            </div>
          </div>
                ))}

         </div>
        </div>
        </div>
        <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
        <div className=" h-full px-0   border-b-[3px]">
           <Flex style={{ position: 'relative',  width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:14}}>
        <Image
              src={dentalButtom}
              alt={"dentalButtom"}
              width={32}
              height={32}
            />
          </div>
      </Flex>
    <div className=" max-w-[920px]  mx-auto">
        <div className=" flex h-full px-0  max-h-[320px] ">
        <div className="flex h-full px-0 max-h-[320px]">
        {DantalB.map((svgData) => (
       <div
                      key={svgData.svg_id}
                     className="h-[230px]  cursor-pointer hover:bg-[#F2F5F8] parent-div"
                      onClick={() => handleSvgClick(svgData.svg_id)}
                    >
                       <div className="mt-0.5 flex  ">
                   <Text
                       ta="center"
                       h={30}
                       key={svgData.svg_id}
                       className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                       style={{ width: svgData.width }}
                     >
                       {svgData.svg_id}
                     </Text>
                     </div>
         {!hiddenSvgIds.includes(svgData.svg_id) ? (
           <DentalSvg
                  svgData={svgData}
                  isHidingMode={isHidingMode}
                  highlightedPaths={highlightedPaths}
                  onPathClick={onPathClick}
                  isPathSelectionActive={isPathSelectionActive}
                  hiddenPaths={hiddenPaths}
                  isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                  isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                  isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                />
         ) : (
           <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
         )}

                     </div>
                   ))}
     </div>
        </div>
        </div>
        </div>
       {/* <Divider  my="8px" variant="dashed" /> */}
     <div className="mx-auto w-[880px] max-w-[880px] mb-4 ">
     <br/>
     <Button onClick={toggle} className="hover:bg-[#3799CE]/90  w-full">Register </Button>
     </div>

    <div className="border-base-200 mx-4 border-t mt-4">
      <Tabs
        variant="unstyled"
        defaultValue="Therapy"
        classNames={classes}
      >
            <Tabs.List grow className="space-x-0 gap-0   mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={
                  <IconMessageCircle
                    style={{ width: rem(16), height: rem(16) }}
                  />
                }
              >
               All Procedures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={
                  <IconSettings style={{ width: rem(16), height: rem(16) }} />
                }
              >
               Planned
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
              Completed
              </Tabs.Tab>
              <Tabs.Tab
                value="Inventory"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
                Inventory
              </Tabs.Tab>
            </Tabs.List>
             <Tabs.Panel value="All Procedures" className=" mt-2">
           <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsAllProcedures}</Table.Tbody>
              </Table>
              </Tabs.Panel>
            <Tabs.Panel value="Planned" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsPlanned}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Completed" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsCompleted}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Inventory" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsInventory}</Table.Tbody>
              </Table>
            </Tabs.Panel>
      </Tabs>
      <Button rightSection={<MdPrint size={14} />} className="hover:bg-[#3799CE]/90  " mt={6}>Imprimer</Button>
      </div>

     <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
         <Group align="flex">
         < IconDeviceFloppy stroke={2} />
     <Text size="sm" mb="xs" fw={500}>
     Save Modification
     </Text>
     </Group>
     <Group align="flex-end">
       <Button w={"100%"} onClick={sendPathsToApi} className="hover:bg-[#3799CE]/90">save</Button>
     </Group>
   </Dialog>

     </div>
        </Tabs.Panel>
        <Tabs.Panel value="Ortho">
        <div className="my-4 ">
        <Container>
          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px'}} align="center">
            <div  style={{ position: 'absolute', right: 0 }}>
              <Menu withinPortal position="bottom-end" shadow="sm">
                <Menu.Target>
                  <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>All Procedures</Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                    Download zip
                  </Menu.Item>
                  <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                    Preview all
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                    color="red"
                  >
                    Delete all
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
              </div>
            <div style={{ margin: '0 auto' }} className=" mb-2 flex flex-end p-2 sm:justify-start space-x-2 ">

            <Tooltip
             label="TeethCrown"
             withArrow
               className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
               >
         <Button
           styles={{
             root: {
               position: 'relative',
               color: 'white',
               height: '35px', // Adjust button height
               width: '35px',  // Adjust button width
               padding: 0,
               borderRadius: '0.5rem'
             },
           }}
         >
           {/* SVG in the middle */}
           <div
       style={{
         display: 'flex',
         justifyContent: 'center',
         alignItems: 'center',
         height: '100%',
         width: '100%',
       }}
       >
         <span  className={
       activeButton  === "TeethCrown"
       ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
       : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
       }
       onClick={() => {
       handleButtonClick("TeethCrown");
       setTargetPath("69");
       }}
           >

       <svg xmlns="http://www.w3.org/2000/svg" data-name="11 Tooth extraction" viewBox="-25.5 -51.0 561.0 688.5" x="0px" y="0px">
       <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M76.2,108c0,38.9,12.5,86.6,28.1,130.5c4.4,12.5,18.1,18.8,30.5,14.1c77.4-29,168.6-28.4,245.7,2.1  c12.9,5.1,26.9-1.7,31.1-14.6c16.2-50.3,34.1-130.9,11.3-177.5c-12.3-25.1-34.3-35.1-62.4-35.1c-15,0-29,8-44.8,17  c-15.3,8.8-32.2,18.4-53.9,23.5c-0.5,0.1-1,0.2-1.6,0.3c-33.1,2.6-57.8-10.8-79.4-22.6c-14.1-7.7-26.7-14.5-38.4-14.5  C93.6,31.2,76.2,61.4,76.2,108L76.2,108z M319.3"/>

       </svg>

                 </span>
           </div>
           {/* "CL" in the bottom-right corner */}
           <span
               style={{
               position: 'absolute',
               bottom: '0px',
               right: '0px',
               fontSize: '8px',
               fontWeight: '800',
               backgroundColor: 'white',
               // borderRadius:'0.125rem' ,
               borderTopLeftRadius: '0.5rem' ,
               borderBottomRightRadius: '0.5rem' ,
               color:'#3799CE',
                 padding:'3px  0px 1px 2px' ,
             }}
             className="h-[14px] w-[14px] "
           >
                 Re
           </span>
         </Button>
         </Tooltip>
         <Tooltip
             label="ImplantTeethCrown"
             withArrow
               className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
               >
         <Button
           styles={{
             root: {
               position: 'relative',
               color: 'white',
               height: '35px', // Adjust button height
               width: '35px',  // Adjust button width
               padding: 0,
               borderRadius: '0.5rem'
             },
           }}
         >
           {/* SVG in the middle */}
           <div
       style={{
         display: 'flex',
         justifyContent: 'center',
         alignItems: 'center',
         height: '100%',
         width: '100%',
       }}
       >
         <span  className={
       activeButton  === "ImplantTeethCrown"
       ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
       : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
       }
       onClick={() => {
       handleButtonClick("ImplantTeethCrown");

       }}
           >

       <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="-25.5 -51.0 561.0 688.5" >
       <path style={{ fillRule:"evenodd",clipRule:"evenodd",fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}
       d="M76.2,108c0,38.9,12.5,86.6,28.1,130.5c4.4,12.5,18.1,18.8,30.5,14.1c77.4-29,168.6-28.4,245.7,2.1
       c12.9,5.1,26.9-1.7,31.1-14.6c16.2-50.3,34.1-130.9,11.3-177.5c-12.3-25.1-34.3-35.1-62.4-35.1c-15,0-29,8-44.8,17
       c-15.3,8.8-32.2,18.4-53.9,23.5c-0.5,0.1-1,0.2-1.6,0.3c-33.1,2.6-57.8-10.8-79.4-22.6c-14.1-7.7-26.7-14.5-38.4-14.5
       C93.6,31.2,76.2,61.4,76.2,108L76.2,108z M319.3,253.1c-42.2-8.2-87-8.1-129.2,0.1c-3.2,34.5-10.8,43.1-17.2,50.4
       c-8.5,9.7-14.3,16.3,7.3,145.1c2.9,17.3,7.6,28.2,12.7,32.5c2.8,2.4,4.6,1.2,7.6-3.2c7.3-10.7,12.7-34.4,12.1-72.2
       c-1.2-86.9,86.5-86.9,85.3,0c-0.5,37.7,4.9,61.4,12.2,72.2c3,4.4,4.8,5.6,7.6,3.2c5.1-4.3,9.8-15.2,12.7-32.5
       c21.2-126.8,15.1-134,6.4-144.3C330.2,296.7,322.5,287.7,319.3,253.1L319.3,253.1z"/>

       </svg>
                 </span>
           </div>
           {/* "CL" in the bottom-right corner */}
           <span
               style={{
               position: 'absolute',
               bottom: '0px',
               right: '0px',
               fontSize: '8px',
               fontWeight: '800',
               backgroundColor: 'white',
               // borderRadius:'0.125rem' ,
               borderTopLeftRadius: '0.5rem' ,
               borderBottomRightRadius: '0.5rem' ,
               color:'#3799CE',
                 padding:'3px  0px 1px 2px' ,
             }}
             className="h-[14px] w-[14px] "
           >
                 It
           </span>
         </Button>
         </Tooltip>
            </div>
          </Flex>
        </Container>
        {isAlertVisible && isPathSelectionActive && clickedIds.length > 0 &&(
        <div className="flex justify-between border-2 p-1 px-1">
       <Alert
         title={`SelectTeeth : ${getFormattedOutput() || ""}`}
         icon={icon}
         withCloseButton
         onClose={() => setIsAlertVisible(false)} // Close alert
         w={"100%"}
         bg={"cyan"}
         color="white"
       ></Alert>
        </div>
      )}
          <div className=" h-full px-0   border-t-[3px] my-2">
        <Flex style={{ position: 'relative', width: '100%', }} align="center">
            <div  style={{ position: 'absolute', left: 16 ,top:220}}>
            <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
              </div>
          </Flex>
        <div className=" max-w-[920px] mt-2 mx-auto">
        <div className=" flex h-full px-0  max-h-[320px] ">
                {Dantal.map((svgData) => (
                <div
                  key={svgData.svg_id}
                  className="h-[230px] cursor-pointer hover:bg-[#F2F5F8] parent-div"
                  onClick={() => handleSvgClick(svgData.svg_id)}

                >
             {!hiddenSvgIds.includes(svgData.svg_id) ? (
                  <DentalSvg
                    svgData={svgData}
                    isHidingMode={isHidingMode}
                    highlightedPaths={highlightedPaths}
                    onPathClick={onPathClick}
                    isPathSelectionActive={isPathSelectionActive}
                    hiddenPaths={hiddenPaths}
                    isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                    isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                    isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                  />
                ) : (
              <div
                style={{
                  height: '200px', // Match SVG height
                  width: svgData.width, // Match SVG width
                  alignItems: 'center',
                }}
              />
            )}
            <div className="mt-0.5 flex">
              <Text
                ta="center"
                h={30}
                key={svgData.svg_id}
                className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                style={{ width: svgData.width }}
              >
                {svgData.svg_id}
              </Text>
            </div>
          </div>
                ))}

         </div>
        </div>
        </div>
        <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
        <div className=" h-full px-0   border-b-[3px]">
           <Flex style={{ position: 'relative',  width: '100%', }} align="center">
        <div  style={{ position: 'absolute', left: 16 ,top:14}}>
        <Image
              src={dentalButtom}
              alt={"dentalButtom"}
              width={32}
              height={32}
            />
          </div>
      </Flex>
        <div className=" max-w-[920px]  mx-auto">
        <div className=" flex h-full px-0  max-h-[320px] ">
        <div className="flex h-full px-0 max-h-[320px]">
        {DantalB.map((svgData) => (
       <div
        key={svgData.svg_id}
        className="h-[230px]  cursor-pointer hover:bg-[#F2F5F8] parent-div"
        onClick={() => handleSvgClick(svgData.svg_id)}
      >
          <div className="mt-0.5 flex  ">
      <Text
          ta="center"
          h={30}
          key={svgData.svg_id}
          className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
          style={{ width: svgData.width }}
        >
          {svgData.svg_id}
        </Text>
        </div>
         {!hiddenSvgIds.includes(svgData.svg_id) ? (
            <DentalSvg
                   svgData={svgData}
                   isHidingMode={isHidingMode}
                   highlightedPaths={highlightedPaths}
                   onPathClick={onPathClick}
                   isPathSelectionActive={isPathSelectionActive}
                   hiddenPaths={hiddenPaths}
                   isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                   isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                   isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                 />
         ) : (
           <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
         )}

                     </div>
                   ))}
     </div>
        </div>
        </div>
        </div>
       {/* <Divider  my="8px" variant="dashed" /> */}
     <div className="mx-auto w-[880px] max-w-[880px] mb-4 ">
     <br/>
     <Button onClick={toggle} className="hover:bg-[#3799CE]/90  w-full">Register </Button>
     </div>

    <div className="border-base-200 mx-4 border-t mt-4">
      <Tabs
        variant="unstyled"
        defaultValue="Therapy"
        classNames={classes}
      >
            <Tabs.List grow className="space-x-0 gap-0   mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={
                  <IconMessageCircle
                    style={{ width: rem(16), height: rem(16) }}
                  />
                }
              >
               All Procedures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={
                  <IconSettings style={{ width: rem(16), height: rem(16) }} />
                }
              >
               Planned
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
              Completed
              </Tabs.Tab>
              <Tabs.Tab
                value="Inventory"
                leftSection={
                  <IconPhoto style={{ width: rem(16), height: rem(16) }} />
                }
              >
                Inventory
              </Tabs.Tab>
            </Tabs.List>
             <Tabs.Panel value="All Procedures" className=" mt-2">
           <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsAllProcedures}</Table.Tbody>
              </Table>
              </Tabs.Panel>
            <Tabs.Panel value="Planned" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsPlanned}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Completed" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsCompleted}</Table.Tbody>
              </Table>
            </Tabs.Panel>
            <Tabs.Panel value="Inventory" className=" mt-2">
            <Table striped highlightOnHover withTableBorder withColumnBorders>
                <Table.Thead >
                  <Table.Tr>
                    {/* <Table.Th /> */}
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>S</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Teeth</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Procedure</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Qty</Table.Th>
                    <Table.Th style={{textAlign: "left",padding:"5px 15px"}}>Fee</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rowsInventory}</Table.Tbody>
              </Table>
            </Tabs.Panel>
      </Tabs>
      <Button rightSection={<MdPrint size={14} />} className="hover:bg-[#3799CE]/90  " mt={6}>Imprimer</Button>
      </div>

     <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
         <Group align="flex">
         < IconDeviceFloppy stroke={2} />
     <Text size="sm" mb="xs" fw={500}>
     Save Modification
     </Text>
     </Group>
     <Group align="flex-end">
       <Button w={"100%"} onClick={sendPathsToApi} className="hover:bg-[#3799CE]/90">save</Button>
     </Group>
   </Dialog>

     </div>
        </Tabs.Panel>
      </Tabs>
    </>
  );
});

