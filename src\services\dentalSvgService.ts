import api from '../lib/api';

// Types pour les données SVG dentaires
export interface DentalSvgData {
  id: string;
  patient: string;
  patient_name: string;
  patient_birth_date: string;
  patient_age: number | null;
  dental_svg_upper: string;
  dental_svg_lower: string;
  current_age_restriction: 'none' | 'under_6' | 'under_7_5' | 'under_12' | 'under_13_5';
  dental_view_level: 'full' | 'partial' | 'limited' | 'minimal' | 'hidden';
  can_view_dental_data: boolean;
  allowed_teeth_count: number;
  filtered_svg_data: {
    dental_svg_upper: string;
    dental_svg_lower: string;
    view_level: string;
    allowed_teeth: number;
    message?: string;
  };
  created_at: string;
  updated_at: string;
  last_modification_date: string | null;
  modifications: DentalModification[];
}

export interface DentalModification {
  id: string;
  tooth_number: number;
  path_id: string;
  modification_type: 'color' | 'replacement' | 'addition' | 'removal' | 'restoration';
  value: string;
  specialty: 'therapeutic' | 'esthetic' | 'prosthetic' | 'orthodontic' | 'surgery';
  applied_by_button: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface AgeRestrictionInfo {
  patient_id: string;
  age: number | null;
  age_restriction: string;
  dental_view_level: string;
  can_view_dental_data: boolean;
  allowed_teeth_count: number;
  restriction_message: string;
}

export interface CreateDentalSvgData {
  patient: string;
  dental_svg_upper?: string;
  dental_svg_lower?: string;
}

export interface CreateDentalModification {
  patient_id: string;
  tooth_number: number;
  path_id: string;
  modification_type: 'color' | 'replacement' | 'addition' | 'removal' | 'restoration';
  value: string;
  specialty: 'therapeutic' | 'esthetic' | 'prosthetic' | 'orthodontic' | 'surgery';
  applied_by_button: string;
}

export interface BulkUpdateData {
  patient_id: string;
  dental_svg_upper?: string;
  dental_svg_lower?: string;
  modifications?: Omit<CreateDentalModification, 'patient_id'>[];
}

export interface ApiParams {
  [key: string]: string | number | boolean | undefined;
}

// Helper function pour les appels API
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: object | FormData | null,
  params?: ApiParams
) => {
  try {
    const config = { params };
    if (method === 'get') {
      const response = await api.get(endpoint, config);
      return response.data;
    } else {
      const response = await api[method](endpoint, data || {}, config);
      return response.data;
    }
  } catch (error) {
    console.error(`❌ API Error [${method.toUpperCase()} ${endpoint}]:`, error);
    throw error;
  }
};

// Service principal pour les données SVG dentaires
export const dentalSvgService = {
  // Récupérer les données SVG d'un patient
  getPatientDentalData: async (patientId: string): Promise<DentalSvgData> => {
    console.log(`🦷 Fetching dental SVG data for patient ${patientId}...`);
    return makeApiCall(`/api/dental-svg/${patientId}/`);
  },

  // Créer de nouvelles données SVG pour un patient
  createDentalData: async (data: CreateDentalSvgData): Promise<DentalSvgData> => {
    console.log('🦷 Creating new dental SVG data...', data);
    return makeApiCall('/api/dental-svg/', 'post', data);
  },

  // Mettre à jour les données SVG d'un patient
  updateDentalData: async (patientId: string, data: Partial<CreateDentalSvgData>): Promise<DentalSvgData> => {
    console.log(`🦷 Updating dental SVG data for patient ${patientId}...`, data);
    return makeApiCall(`/api/dental-svg/${patientId}/`, 'put', data);
  },

  // Vérifier les restrictions d'âge
  checkAgeRestrictions: async (patientId: string): Promise<AgeRestrictionInfo> => {
    console.log(`🔒 Checking age restrictions for patient ${patientId}...`);
    return makeApiCall(`/api/dental-svg/check-age-restrictions/${patientId}/`);
  },

  // Mise à jour en lot
  bulkUpdate: async (data: BulkUpdateData): Promise<{ dental_svg_data: DentalSvgData; modifications_created: number; message: string }> => {
    console.log('🦷 Performing bulk update...', data);
    return makeApiCall('/api/dental-svg/bulk-update/', 'post', data);
  },

  // Récupérer toutes les données SVG (pour admin)
  getAllDentalData: async (params?: ApiParams): Promise<{ count: number; results: DentalSvgData[] }> => {
    console.log('🦷 Fetching all dental SVG data...');
    return makeApiCall('/api/dental-svg/', 'get', undefined, params);
  }
};

// Service pour les modifications dentaires
export const dentalModificationService = {
  // Récupérer les modifications d'un patient
  getPatientModifications: async (patientId: string, params?: ApiParams): Promise<DentalModification[]> => {
    console.log(`🔧 Fetching modifications for patient ${patientId}...`);
    const response = await makeApiCall('/api/dental-modifications/', 'get', undefined, {
      patient_id: patientId,
      ...params
    });
    return response.results || response;
  },

  // Créer une nouvelle modification
  createModification: async (data: CreateDentalModification): Promise<DentalModification> => {
    console.log('🔧 Creating new dental modification...', data);
    return makeApiCall('/api/dental-modifications/', 'post', data);
  },

  // Mettre à jour une modification
  updateModification: async (id: string, data: Partial<CreateDentalModification>): Promise<DentalModification> => {
    console.log(`🔧 Updating modification ${id}...`, data);
    return makeApiCall(`/api/dental-modifications/${id}/`, 'put', data);
  },

  // Supprimer une modification
  deleteModification: async (id: string): Promise<void> => {
    console.log(`🗑️ Deleting modification ${id}...`);
    return makeApiCall(`/api/dental-modifications/${id}/`, 'delete');
  },

  // Supprimer toutes les modifications d'un patient
  clearPatientModifications: async (patientId: string): Promise<{ message: string; patient_id: string }> => {
    console.log(`🗑️ Clearing all modifications for patient ${patientId}...`);
    return makeApiCall(`/api/dental-modifications/clear-patient/${patientId}/`, 'delete');
  },

  // Récupérer les modifications par spécialité
  getModificationsBySpecialty: async (patientId: string, specialty: string): Promise<DentalModification[]> => {
    console.log(`🔧 Fetching ${specialty} modifications for patient ${patientId}...`);
    return dentalModificationService.getPatientModifications(patientId, { specialty });
  },

  // Récupérer les modifications par dent
  getModificationsByTooth: async (patientId: string, toothNumber: number): Promise<DentalModification[]> => {
    console.log(`🔧 Fetching modifications for tooth ${toothNumber} of patient ${patientId}...`);
    return dentalModificationService.getPatientModifications(patientId, { tooth_number: toothNumber });
  }
};

// Utilitaires pour la gestion des restrictions d'âge
export const ageRestrictionUtils = {
  // Vérifier si un patient peut voir ses données dentaires
  canViewDentalData: (ageInfo: AgeRestrictionInfo): boolean => {
    return ageInfo.can_view_dental_data;
  },

  // Obtenir le nombre de dents autorisées
  getAllowedTeethCount: (ageInfo: AgeRestrictionInfo): number => {
    return ageInfo.allowed_teeth_count;
  },

  // Obtenir le message de restriction
  getRestrictionMessage: (ageInfo: AgeRestrictionInfo): string => {
    return ageInfo.restriction_message;
  },

  // Vérifier si une dent spécifique est autorisée
  isToothAllowed: (toothNumber: number, ageInfo: AgeRestrictionInfo): boolean => {
    const allowedCount = ageInfo.allowed_teeth_count;

    if (allowedCount >= 32) return true; // Toutes les dents autorisées
    if (allowedCount === 0) return false; // Aucune dent autorisée

    // Logique pour déterminer quelles dents sont autorisées selon le nombre
    if (allowedCount === 8) {
      // Dents de devant seulement (incisives)
      return [11, 12, 21, 22, 31, 32, 41, 42].includes(toothNumber);
    } else if (allowedCount === 16) {
      // Dents de lait + quelques permanentes
      return toothNumber <= 16;
    } else if (allowedCount === 24) {
      // Presque toutes sauf les dents de sagesse
      return ![18, 28, 38, 48].includes(toothNumber);
    }

    return true;
  }
};

export default dentalSvgService;
