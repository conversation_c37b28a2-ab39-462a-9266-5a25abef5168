"use client";
import { useState } from 'react';
import React from 'react'
import { Center, Stack, Tooltip, UnstyledButton,} from '@mantine/core';
import SimpleBar from "simplebar-react";

//import authService from '~/services/authService';
import { useRouter } from 'next/navigation';
import {
  // IconDashboard,
  // IconCalendarEvent,
  // IconFlask,
  // IconClipboardList,
  // IconPill,
  // IconClock,
  // IconPrescription,
  // IconCalendarStats,
  //   IconGauge,
  //   IconWindElectricity,
  IconUsers,
  IconSettings,
  IconLogout,
  //IconUserPlus,
  //IconUser,
  
  IconCreditCard,
  IconLicense,
  IconMessageCircle,
  IconFileInvoice,
  IconFileText,
  
  IconPackage,
    
    IconDeviceDesktopAnalytics,
    IconFingerprint,
  
    IconHome2,
    IconSwitchHorizontal,
    
    IconReceiptDollar,
    IconUserBitcoin,
    IconMist,
    IconReplaceUser,
    //IconAbacus,
    type IconProps,
  } from '@tabler/icons-react';
  import classes from '~/styles/layout.module.css';

interface NavbarLinkProps {
    icon: React.ComponentType<IconProps>;
    label: string;
    active?: boolean;
    onClick?: () => void;
    href?: string;
  }
  function NavbarLink({ icon: Icon, label, active, onClick, href }: NavbarLinkProps) {
    return (
      <Tooltip label={label} position="right" transitionProps={{ duration: 0 }} style={{color:"var(--mantine-color-text)"}} withArrow className="bg-[var(--tooltip-bg)] ">
        <UnstyledButton
          component="a"
          href={href}
          onClick={(event) => {
            if (onClick) {
              event.preventDefault();
              onClick();
            }
          }}
          className={classes.link}
          data-active={active || undefined}
        >
          <Icon size={20} stroke={1.5} />
        </UnstyledButton>
      </Tooltip>
    );
  }
  const mockdata = [
    { icon: IconHome2, label: 'Home', href: '/home' },
    { icon: IconUsers,label: 'Patients',  href: '/patients'},
    { icon: IconMist, label: 'Visites', href: '/visites' }, 
    { icon: IconReceiptDollar, label: 'Recettes', href: '/recettes' },
    { icon: IconUserBitcoin, label: 'Mutuelles', href: '/mutuelles' },
    { icon: IconFileInvoice, label: 'Facturation', href: '/facturation' },
    { icon: IconDeviceDesktopAnalytics, label: 'États', href: '/etats' },
    { icon: IconPackage  , label: 'Pharmacy',href: '/pharmacy' },
    { icon: IconMessageCircle ,label: 'Messages',href: '/messaging',  },
    { icon: IconCreditCard ,label: 'Licence', href: '/Licence'  },
   // { icon: IconUser, label: 'Mon profil', href: '/profil',   },
   // {  icon: IconUserPlus ,label: 'Gestion des utilisateurs', href: '/user-management', },
    { icon: IconFileText ,label: 'Documents', href: '/documents' }, 
     { icon: IconFingerprint, label: 'Sécurité', href: '/securite' },
     { icon: IconLicense,label: 'Ordonnances', href: '/prescriptions'  },
     { icon: IconReplaceUser, label: 'gestion des utilisateurs', href: '/user-management' },
    { icon: IconSettings, label: 'Parameters', href: '/parameters' },



    // { icon: IconGauge, label: 'Dashboard', href: '/dashboard' },
    // { icon: IconDeviceDesktopAnalytics, label: 'Analytics', href: '/analytics' },
    // { icon: IconWindElectricity, label: 'Fluxjour', href: '/fluxjour' },
    // { icon: IconCalendarStats, label: 'Releases', href: '/releases' },
    // { icon: IconUser, label: 'Account', href: '/account' },
    // { href: '/dashboard', label: 'Dashboard', icon: IconDashboard },
    // { href: '/appointments', label: 'Appointments', icon: IconCalendarEvent },
    // { href: '/medical-records', label: 'Medical Records', icon: IconClipboardList },
    // { href: '/lab-results', label: 'Lab Results', icon: IconFlask },
    // { href: '/prescriptions', label: 'Prescriptions', icon: IconPrescription },
    // { href: '/medications', label: 'Medications', icon: IconPill },
    // { href: '/billing', label: 'Billing', icon: IconFileInvoice },
    // { href: '/availability', label: 'Availability', icon: IconClock },
  
  ];
  
  interface NavbarProps {
    toggleSidebar: () => void;
  }
  const Navbar = ({ toggleSidebar }: NavbarProps) => {
    const [active, setActive] = useState(2);
      const link = mockdata.map((link, index) => (
        <div key={index}>
        <NavbarLink
          {...link}
          key={link.label}
          active={index === active}
          // onClick={() => setActive(index)}
          href={link.href}
          onClick={() => {router.push(link.href);setActive(index)}}
        />
        </div>
      ));
      const router = useRouter();
const handleLogout = async () => {
  try {
   // await authService.logout();
    // Redirect to the login page instead of the home page
    router.push('/login');
  } catch (error) {
    console.error('Error logging out:', error);
    // Even if there's an error, still try to redirect to login
    router.push('/login');
  }
};
  return (
    <>
    <Center style={{paddingTop:"8px",paddingRight:"6px",height:"56px",borderBottom: "1px solid var(--border-color)"}}>
     <Tooltip label={"Toggle Sidebar"} position="right" transitionProps={{ duration: 0 }}  withArrow className=" bg-[var(--tooltip-bg)] text-daisy">
        <UnstyledButton onClick={toggleSidebar} className={classes.link} >
            <svg xmlns="http://www.w3.org/2000/svg"
            width="22" height="22" viewBox="0 0 24 26"
            fill="none"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="tabler-icon tabler-icon-menu-deep "
            style={{
            transform: "rotate(90deg)",
           }}>
           <path d="M4 10h16" />
       <path d="M4 14h16" />
       <path d="M4 18h16" />
       <path d="M9 22l3 3l3 -3" />
      <path d="M9 6l3 -3l3 3" />
              </svg>
        </UnstyledButton>
      </Tooltip>
 </Center>
  <div className={`${classes.navbarMain}  py-1 `}>
    <Stack justify="center" gap={0} className='h-[600px] bg-[var(---mantine-bg-color)] rounded px-[4px]'>
    <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] ">
       <div className='pr-[4px] bg-[var(--bg-nav-hover)] '>
          {link}
          </div>
      </SimpleBar>
    </Stack>
  </div>
    <div className={`py-1 px-[4px]`}>
      <Stack justify="center" gap={0} className='bg-[var(---mantine-bg-color)] rounded'>
      <div className='pr-[2px] bg-[var(--bg-nav-hover)] '>
    <NavbarLink  onClick={handleLogout}  icon={IconSwitchHorizontal} label="Change account" />
    <NavbarLink  onClick={handleLogout} icon={IconLogout} label="Logout" />
    </div>
  </Stack>
      </div>
  </>
  )
}

export default Navbar