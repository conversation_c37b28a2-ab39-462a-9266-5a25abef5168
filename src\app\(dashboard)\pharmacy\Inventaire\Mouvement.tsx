'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  Radio,
  Tabs,
  Table,
  Card,
  Text,
  ActionIcon,
  Divider,
  NumberInput,
  Textarea,
  Badge,
  ScrollArea,
  Pagination,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconBarcode,
  IconList,
  IconPaperclip,
  IconMessageCircle,
  IconDeviceFloppy,
  IconCheck,
  IconX,
} from '@tabler/icons-react';

// TypeScript interfaces
interface MouvementItem {
  id: string;
  code: string;
  designation: string;
  qte: number;
  prix: number;
  tva: number;
  depot: string;
  montant: number;
}

interface MouvementData {
  numero: string;
  date: Date | null;
  depot: string;
  fournisseur: string;
  modePrix: 'HT' | 'TTC';
  type: 'Entrée' | 'Sortie';
  choisirPatient: string;
  organisme: string;
  numeroDocument: string;
  numeroContrat: string;
  affaire: string;
  commentaire: string;
  items: MouvementItem[];
  montantHT: number;
  montantTVA: number;
  montantTTC: number;
}

const Mouvement = () => {
  const [activeTab, setActiveTab] = useState<string>('details');
  const [currentMouvement, setCurrentMouvement] = useState<MouvementData>({
    numero: '4',
    date: new Date('2022-09-16'),
    depot: 'Dépôt 1',
    fournisseur: '',
    modePrix: 'HT',
    type: 'Sortie',
    choisirPatient: '',
    organisme: '',
    numeroDocument: '',
    numeroContrat: '',
    affaire: '',
    commentaire: '',
    items: [],
    montantHT: 0.00,
    montantTVA: 0.00,
    montantTTC: 0.00,
  });

  const form = useForm({
    initialValues: currentMouvement,
  });

  const itemForm = useForm({
    initialValues: {
      code: '',
      designation: '',
      qte: 1,
      prix: 0,
      tva: 20,
      depot: '',
    },
  });

  const depots = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
    'Dépôt Principal',
  ];

  const fournisseurs = [
    'Fournisseur A',
    'Fournisseur B',
    'Fournisseur C',
    'Fournisseur D',
  ];

  const organismes = [
    'Organisme 1',
    'Organisme 2',
    'Organisme 3',
  ];

  const handleSave = () => {
    notifications.show({
      title: 'Mouvement enregistré',
      message: 'Le mouvement de stock a été enregistré avec succès',
      color: 'green',
    });
  };

  const handleValidate = () => {
    notifications.show({
      title: 'Mouvement validé',
      message: 'Le mouvement de stock a été validé avec succès',
      color: 'blue',
    });
  };

  const handleCancel = () => {
    form.reset();
    notifications.show({
      title: 'Annulé',
      message: 'Les modifications ont été annulées',
      color: 'orange',
    });
  };

  return (
    <div className="p-4">
      {/* Header */}
      <Paper p="md" mb="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconList size={24} className="text-blue-600" />
            <Title order={3} className="text-gray-800">
              Mouvement N°: {currentMouvement.numero}
            </Title>
          </Group>
          <Group align="center">
            <IconList size={16} className="text-gray-600" />
            <Text size="sm" className="text-gray-600">
              Mouvements du stock
            </Text>
          </Group>
        </Group>
      </Paper>

      {/* Main Form */}
      <Card withBorder mb="md">
        <form onSubmit={form.onSubmit(() => {})}>
          <Grid mb="md">
            <Grid.Col span={3}>
              <TextInput
                label="N°. Mouvement"
                placeholder="Numéro"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="Date"
                placeholder="Sélectionner une date"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Text size="sm" fw={500} mb="xs">Mode de prix</Text>
              <Radio.Group
                value={form.values.modePrix}
                onChange={(value) => form.setFieldValue('modePrix', value as 'HT' | 'TTC')}
              >
                <Group>
                  <Radio value="HT" label="HT" />
                  <Radio value="TTC" label="TTC" />
                </Group>
              </Radio.Group>
            </Grid.Col>
            <Grid.Col span={3}>
              <Text size="sm" fw={500} mb="xs">Type</Text>
              <Radio.Group
                value={form.values.type}
                onChange={(value) => form.setFieldValue('type', value as 'Entrée' | 'Sortie')}
              >
                <Group>
                  <Radio value="Entrée" label="Entrée" />
                  <Radio value="Sortie" label="Sortie" />
                </Group>
              </Radio.Group>
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={3}>
              <Select
                label="Dépôt"
                placeholder="Sélectionner un dépôt"
                data={depots}
                {...form.getInputProps('depot')}
                required
                leftSection={<IconSearch size={16} />}
                rightSection={<IconPlus size={16} />}
              />
            </Grid.Col>
            <Grid.Col span={9}>
              <TextInput
                label="Fournisseur"
                placeholder="Fournisseur"
                {...form.getInputProps('fournisseur')}
                leftSection={<IconSearch size={16} />}
                rightSection={<IconPlus size={16} />}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <TextInput
                label="Choisir un patient"
                placeholder="Choisir un patient"
                {...form.getInputProps('choisirPatient')}
                leftSection={<IconSearch size={16} />}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Organisme"
                placeholder="Organisme"
                data={organismes}
                {...form.getInputProps('organisme')}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={6}>
              <TextInput
                label="N°. Document"
                placeholder="N°. Document"
                {...form.getInputProps('numeroDocument')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="N°.Contract"
                placeholder="N°.Contract"
                {...form.getInputProps('numeroContrat')}
                leftSection={<IconSearch size={16} />}
              />
            </Grid.Col>
          </Grid>

          <Grid mb="md">
            <Grid.Col span={12}>
              <Select
                label="Affaire"
                placeholder="Affaire"
                data={[]}
                {...form.getInputProps('affaire')}
                leftSection={<IconSearch size={16} />}
                rightSection={<IconPlus size={16} />}
              />
            </Grid.Col>
          </Grid>
        </form>
      </Card>

      {/* Tabs Section */}
      <Paper withBorder mb="md">
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'details')}>
          <Tabs.List>
            <Tabs.Tab value="details" leftSection={<IconList size={16} />}>
              Détails
            </Tabs.Tab>
            <Tabs.Tab value="pieces-jointes" leftSection={<IconPaperclip size={16} />}>
              Pièces jointes
            </Tabs.Tab>
            <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
              Commentaires
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="details" pt="md">
            {/* Action Buttons for Items */}
            <Group mb="md" justify="flex-end">
              <Button
                leftSection={<IconBarcode size={16} />}
                variant="filled"
                className="bg-blue-500 hover:bg-blue-600"
              >
                Code à barres
              </Button>
              <Button
                leftSection={<IconList size={16} />}
                variant="filled"
                className="bg-blue-500 hover:bg-blue-600"
              >
                Article
              </Button>
              <Button
                leftSection={<IconMessageCircle size={16} />}
                variant="filled"
                className="bg-blue-500 hover:bg-blue-600"
              >
                Commentaire
              </Button>
            </Group>

            {/* Items Table */}
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Prix</Table.Th>
                    <Table.Th>Tva</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Montant</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {currentMouvement.items.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={7} className="text-center text-gray-500 py-8">
                        Aucun élément trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    currentMouvement.items.map((item) => (
                      <Table.Tr key={item.id}>
                        <Table.Td>{item.code}</Table.Td>
                        <Table.Td>{item.designation}</Table.Td>
                        <Table.Td>{item.qte}</Table.Td>
                        <Table.Td>{item.prix.toFixed(2)}</Table.Td>
                        <Table.Td>{item.tva}%</Table.Td>
                        <Table.Td>{item.depot}</Table.Td>
                        <Table.Td>{item.montant.toFixed(2)}</Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Pagination */}
            <Group justify="center" mt="md">
              <Text size="sm" className="text-gray-600">Page</Text>
              <Pagination total={1} />
              <Text size="sm" className="text-gray-600">Lignes par Page</Text>
              <Select
                data={['10', '25', '50', '100']}
                defaultValue="10"
                size="xs"
                w={80}
              />
              <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
            </Group>
          </Tabs.Panel>

          <Tabs.Panel value="pieces-jointes" pt="md">
            <Text className="text-gray-500 text-center py-8">
              Aucune pièce jointe
            </Text>
          </Tabs.Panel>

          <Tabs.Panel value="commentaires" pt="md">
            <Textarea
              label="Commentaire"
              placeholder="Ajouter un commentaire..."
              {...form.getInputProps('commentaire')}
              minRows={4}
            />
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Summary and Action Buttons */}
      <Grid>
        <Grid.Col span={8}>
          {/* Comments Section */}
          <Paper withBorder p="md">
            <Text size="sm" fw={500} mb="xs">Commentaire</Text>
            <div className="min-h-[100px] bg-gray-50 p-3 rounded border">
              {currentMouvement.commentaire || (
                <Text size="sm" className="text-gray-400">
                  Aucun commentaire
                </Text>
              )}
            </div>
          </Paper>
        </Grid.Col>

        <Grid.Col span={4}>
          {/* Summary Totals */}
          <Paper withBorder p="md">
            <div className="space-y-3">
              <Group justify="space-between">
                <Text size="sm" fw={500}>MONTANT HT :</Text>
                <Text size="sm" fw={500}>{currentMouvement.montantHT.toFixed(2)}</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" fw={500}>MONTANT TVA :</Text>
                <Text size="sm" fw={500}>{currentMouvement.montantTVA.toFixed(2)}</Text>
              </Group>
              <Group justify="space-between">
                <Text size="sm" fw={500}>MONTANT TTC :</Text>
                <Text size="sm" fw={500}>{currentMouvement.montantTTC.toFixed(2)}</Text>
              </Group>
            </div>
          </Paper>
        </Grid.Col>
      </Grid>

      {/* Action Buttons */}
      <Group justify="flex-end" mt="xl" gap="md">
        <Button
          variant="outline"
          color="red"
          onClick={handleCancel}
          leftSection={<IconX size={16} />}
        >
          Annuler
        </Button>
        <Button
          variant="outline"
          color="gray"
          onClick={handleValidate}
          leftSection={<IconCheck size={16} />}
        >
          Valider
        </Button>
        <Button
          variant="filled"
          className="bg-blue-500 hover:bg-blue-600"
          onClick={handleSave}
          leftSection={<IconDeviceFloppy size={16} />}
        >
          Enregistrer et quitter
        </Button>
        <Button
          variant="filled"
          className="bg-green-500 hover:bg-green-600"
          onClick={handleSave}
          leftSection={<IconDeviceFloppy size={16} />}
        >
          Enregistrer
        </Button>
      </Group>
    </div>
  );
};

export default Mouvement;
