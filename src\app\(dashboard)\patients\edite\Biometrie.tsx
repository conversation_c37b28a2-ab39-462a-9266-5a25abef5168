

import { useState } from 'react';
import { Group, Title, Button, Text, Tooltip, Menu, Divider,ActionIcon ,Table, ScrollArea,} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {MeasurementDialog} from  './MeasurementDialog'
import {MeasurementTrendsDialog} from  './MeasurementTrendsDialog'

import Icon from '@mdi/react';
const items = [
  {
    id: '1',
    name: 'Poids',
    data: [60, 62, 63.5, 61.8, 65],
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    data: [170, 170, 170, 170, 170],
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
  },
];
interface Measurement {
  id: string;
  date: string;
  values: { [label: string]: number | string | boolean | null };
  comment?: string;
}

interface MeasureDefinition {
  id: string;
  label: string;
  type: 'float' | 'integer' | 'boolean' | 'string' | 'date' | 'calculated';
}
import {
  mdiArrowLeft,
  mdiCardAccountDetails,
mdiPlus,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,

   mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiCalendarPlus,
  mdiChartLine
} from '@mdi/js';
type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
   patient: any;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
   measures: MeasureDefinition[];
  measurements: Measurement[];
};
export const Biometrie =({
  patient,
  onGoBack,
  onGoToContract,
   patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  measures, measurements
}: PatientActionsProps) => {
     const disabled = isFormInvalid || isDraft;

const [modalOpen, setModalOpen] = useState(false);
const [measurementmodalOpen, setMeasurementModalOpen] = useState(false);
       const headers = [
    <th key="date">Date</th>,
    ...measures.map((m) => <th key={m.id}>{m.label}</th>),
    <th key="comment">Commentaire</th>,
  ];

  const rows =
    measurements.length === 0 ? (
      <tr>
        <td colSpan={measures.length + 2}>
          <Text c="dimmed" ta="center">
            Aucune biométrie à afficher
          </Text>
        </td>
      </tr>
    ) : (
      measurements.map((m) => (
        <tr key={m.id}>
          <td>{m.date}</td>
          {measures.map((def) => (
            <td key={def.id}>
              {m.values[def.label] != null ? String(m.values[def.label]) : ''}
            </td>
          ))}
          <td>{m.comment || ''}</td>
        </tr>
      ))
    );
  return (
    <>
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
       <Group justify="space-between" align="center">
         <Group>
           {patient ? (
             <Icon path={mdiCardAccountDetails} size={1} />
           ) : (
             <Button variant="subtle" onClick={onGoBack}>
               <Icon path={mdiArrowLeft} size={1} color={"white"}/>
             </Button>
           )}
           <Title order={2}>Fiche patient</Title>
           <DatePickerInput placeholder="Date de création" />
           23/06/2025
         </Group>
   
         {patient && (
           <Group>
             <Text>{patient.full_name}</Text>
             <Text>{patient.gender}</Text>
             <Text>{patient.age}</Text>
             <Text>{patient.default_insurance}</Text>
             <Text>{patient.file_number}</Text>
             <Text>{patient.last_visit}</Text>
           </Group>
         )}
   
         <Group>
            <Group>
                 <Button leftSection={<Icon path={mdiPlus} size={1.05}   color={"white"} />} variant="subtle" color='white' onClick={setModalOpen(true)}>
       Ajouter des biométries
      </Button>  
          <Button leftSection={<Icon path={mdiChartLine} size={1.05}   color={"white"} />} variant="subtle" color='white' onClick={setMeasurementModalOpen(true)}>
       Tendances
      </Button>  
            </Group>
           <Divider size="sm" orientation="vertical" />
           <Menu shadow="md" width={220}>
             <Menu.Target>
               <Button variant="subtle">
                 <Icon path={mdiApps} size={1} color={"white"}/>
               </Button>
             </Menu.Target>
             <Menu.Dropdown>
               <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />}>Alerts</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}>Relations Patient</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
             </Menu.Dropdown>
           </Menu>
   
           <Tooltip label="Contrat">
             <Button variant="subtle" onClick={onGoToContract}>
               <Icon path={mdiCertificate} size={1} color={"white"}/>
             </Button>
           </Tooltip>
   
           {/* <Tooltip label="Liste patients">
             <Button component="a" href="/pratisoft/patient" variant="subtle">
               <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
             </Button>
           </Tooltip> */}
         </Group>
       </Group>
       
       </div>
{/* --------------------------Start Content ------------------------------*/}
 <ScrollArea>
      <Table striped highlightOnHover withTableBorder withColumnBorders>
        <thead>
          <tr>{headers}</tr>
        </thead>
        <tbody>{rows}</tbody>
      </Table>
    </ScrollArea>
{/* -------------------------end  Content---------------------------------*/}
    
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={onSubmit}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    <MeasurementDialog
  opened={modalOpen}
  onClose={() => setModalOpen(false)}
  onSubmit={(values) => {
    console.log('Biométrie soumise:', values);
    setModalOpen(false);
  }}
/>
<MeasurementTrendsDialog
  opened={measurementmodalOpen}
  onClose={() => setMeasurementModalOpen(false)}
  items={items}
/>
    </>
  )
}


