'use client';

import { useState } from 'react';
import { IconPalette } from '@tabler/icons-react';
import { Autocomplete } from '@mantine/core';
import { 
    Button, Tabs, Select, Group, Title, ActionIcon, Stack, Paper, Grid,
    Modal, TextInput, ColorInput, MultiSelect, Text,Switch,ColorPicker,Table,Menu, 
  } from '@mantine/core';
import Icon from '@mdi/react';
import { mdiCog, mdiPlus, mdiCalendarEdit ,mdiStar, mdiStarOutline,mdiDrag ,mdiPuzzlePlus ,mdiPencil, mdiDelete } from '@mdi/js';
interface AgendaForm {
    name: string;
    color: string;
    description: string;
    services: string[];
  }
interface DefaultSettings {
  physician: string;
  agenda: string;
  entry: {
    reason: string;
    waiting_room: string;
    consultation_room: string;
  };
  payment: {
    type: string;
    bank: string;
  };
}

const reasons = [
    "1er Consultation", "Changement D'élastique", "Chirurgie/Paro", "Collage",
    "Composite", "Consultation", "Contention", "Contr<PERSON>le", "Depose Sutures",
    "Detartrage", "Devis", "Echographie", "Endodontie", "Formation",
    "Implantologie", "Obturation Canalaire", "Orthodontie", "PA ES Chassis/Monta",
    "PA Pose", "PC ESS Armature", "PC Scellement", "PC TP EMP", "Prophylaxie",
    "Urgent", "polissage"
  ].map(value => ({ value, label: value }));
  const banks = [
    "Aucune", "BCP", "BMCI", "BMCE", "AWB", "SGMB", "CDM", "BAM", "ABB", "ABM",
    "BAA", "CDG", "CFG", "CITI", "CIH", "FEC", "MFC", "UMB", "BANCO", "CAIXA",
    "UMNIA", "BTI", "BAY", "ASSAFA", "AAB", "CIB", "UPL", "HLD", "CAT"
  ].map(value => ({ value, label: value }));
  
  const paymentTypes = ["Aucune", "Chèque", "Espèce", "Traite"]
    .map(value => ({ value, label: value }));
    
const defaultPhysicians = [{ value: 'Dr. DEMO DEMO', label: 'Dr. DEMO DEMO' }];
const defaultAgendas = [{ value: 'Cabinet', label: 'Cabinet' }];
const rooms = [
  { value: 'SLT', label: 'SLT' },
  { value: 'FTL', label: 'FTL' }
];
// Add this interface
interface VisitLink {
    key: string;
    title: string;
    is_form?: boolean;
  }
 // Add these interfaces
interface ToolbarLink {
  id: string;
  title: string;
}

interface ToolbarSubmodule {
  id: string;
  name: string;
}
const dosageForms = [
    { value: 'tablet', label: 'Comprimé' },
    { value: 'syrup', label: 'Sirop' },
    { value: 'injection', label: 'Injectable' },
    // Add more dosage forms as needed
  ];
export default function ParametresDeBase() {
    const [activeTab, setActiveTab] = useState<string>('general');
    const [isAgendaModalOpen, setIsAgendaModalOpen] = useState(false);
    const [isColorPickerModalOpen, setIsColorPickerModalOpen] = useState(false);
    const [agendaForm, setAgendaForm] = useState<AgendaForm>({
      name: '',
      color: '#000000',
      description: '',
      services: []
    });
  const [settings, setSettings] = useState<DefaultSettings>({
    physician: 'Dr. DEMO DEMO',
    agenda: 'Cabinet',
    entry: {
      reason: 'Consultation',
      waiting_room: 'SLT',
      consultation_room: 'FTL'
    },
    payment: {
      type: 'Espèce',
      bank: 'Aucune'
    }
  });
  const handleSettingChange = (section: keyof DefaultSettings | 'entry' | 'payment', field: string, value: string) => {
    setSettings(prev => {
      if (section === 'entry' || section === 'payment') {
        return {
          ...prev,
          [section]: {
            ...prev[section],
            [field]: value
          }
        };
      }
      return {
        ...prev,
        [section]: value
      };
    });
  };
  const handleAgendaSubmit = () => {
    // Handle form submission here
    setIsAgendaModalOpen(false);
  };
  const handleTabChange = (value: string | null) => {
    if (value) {
        setActiveTab(value);
    }
};
//tabs.panal visit
const [visitLinks, setVisitLinks] = useState<VisitLink[]>([
    { key: 'dental', title: 'Dentaire' },
    { key: 'periodontics', title: 'Parodontie' },
    // ... add other links
  ]);
  const [favoriteLink, setFavoriteLink] = useState<string>('dental');
const [disabledLinks, setDisabledLinks] = useState<Record<string, boolean>>({});
//tabs.panal custom
const [toolbarLinks, setToolbarLinks] = useState<ToolbarLink[]>([]);
const [toolbarSubmodules, setToolbarSubmodules] = useState<ToolbarSubmodule[]>([]);
//tabs.panal flow
interface WorkflowView {
    id: string;
    name: string;
    is_system?: boolean;
  }
  
  interface WorkflowConfig {
    default: string;
    disabled: Record<string, boolean>;
  }
const [workflowViews, setWorkflowViews] = useState<WorkflowView[]>([
    { id: '1', name: 'Vue générale' }
  ]);
  const [workflowConfig, setWorkflowConfig] = useState<WorkflowConfig>({
    default: '',
    disabled: {}
  });
const handleWorkflowConfig = (type: 'default' | 'disabled', id?: string) => {
    if (type === 'default') {
      setWorkflowConfig(prev => ({ ...prev, default: prev.default === '1' ? '' : '1' }));
    } else if (type === 'disabled' && id) {
      setWorkflowConfig(prev => ({
        ...prev,
        disabled: {
          ...prev.disabled,
          [id]: !prev.disabled[id]
        }
      }));
    }
  };
  // Add these prescription
  interface PrescriptionConfig {
    default_values: {
      dosage_form: string;
    };
    use_bookmarked_db: boolean;
    advance_mp: boolean;
    stock_integration: boolean;
    arab_generator: {
      is_used: boolean;
      generator: {
        formula: string;
        period_prefix: string;
        new_line: string;
      }
    };
  }
  const [prescriptionConfig, setPrescriptionConfig] = useState<PrescriptionConfig>({
    default_values: { dosage_form: '' },
    use_bookmarked_db: false,
    advance_mp: false,
    stock_integration: false,
    arab_generator: {
      is_used: false,
      generator: {
        formula: '',
        period_prefix: '',
        new_line: ''
      }
    }
  });

  interface BillingConfig {
    defaultTab: string;
    generate_contract_consignment_doc: boolean;
    default_contract_view: string;
  }
  
  interface ContractState {
    id: string;
    name: string;
    color: string;
    label: string;
  }
  // tabs billin
  // Add these states
  const [billingConfig, setBillingConfig] = useState<BillingConfig>({
    defaultTab: '',
    generate_contract_consignment_doc: false,
    default_contract_view: ''
  });
  
  const [contractStates, setContractStates] = useState<ContractState[]>([
    { id: '1', name: 'Future', color: '#e935d1', label: '' },
    { id: '2', name: 'Résilié', color: '#30fb2d', label: '' },
    // ... add other states
  ]);
  const handlePrescriptionConfig = (path: string, value: any) => {
    setPrescriptionConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current: any = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };
  const handleBillingConfig = (key: keyof BillingConfig, value: string | boolean) => {
    setBillingConfig(prev => ({ ...prev, [key]: value }));
  };
  
  // Add these functions for workflow management
  const handleWorkflowView = (item: WorkflowView) => {
    // Implement workflow view editing logic
    console.log('Edit workflow view:', item);
  };
  
  const handleRemoveWorkflow = (item: WorkflowView) => {
    if (!item.is_system) {
      setWorkflowViews(prev => prev.filter(view => view.id !== item.id));
    }
  };
  
  // Add these functions for managing links and modules
//   const handleAddVisitLink = (link: VisitLink) => {
//     setVisitLinks(prev => [...prev, link]);
//   };
  
//   const handleAddToolbarModule = (module: ToolbarSubmodule) => {
//     setToolbarLinks(prev => [...prev, { id: module.id, title: module.name }]);
//   };
  
  const handleColorChange = (color: string) => {
    setAgendaForm(prev => ({ ...prev, color }));
  };
  return (
    <Paper p="md">
      <Group mb="md" justify="center">
        <Group>
          <ActionIcon variant="subtle" size="lg">
            <Icon path={mdiCog} size={1} />
          </ActionIcon>
          <Title order={3}>Paramètres de base</Title>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={handleTabChange}>
        <Tabs.List>
          <Tabs.Tab value="general">Général</Tabs.Tab>
          <Tabs.Tab value="visit">Visite</Tabs.Tab>
          <Tabs.Tab value="custom">Module personalisés</Tabs.Tab>
          <Tabs.Tab value="flow">Flux</Tabs.Tab>
          <Tabs.Tab value="prescriptions">Prescriptions</Tabs.Tab>
          <Tabs.Tab value="billing">Facturation</Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="general" pt="xl">
          <Grid>
            <Grid.Col span={4}>
              <Select
                label="Médecin par défaut"
                data={defaultPhysicians}
                value={settings.physician}
                onChange={(value) => handleSettingChange('physician', '', value || '')}
                searchable
              />
            </Grid.Col>

            <Grid.Col span={12}>
          
              <Group grow>
              <Group>
              <Select
              w={"90%"}
              label="Agenda par défaut"
              data={defaultAgendas}
              value={settings.agenda}
              onChange={(value) => handleSettingChange('agenda', '', value || '')}
            />
            <ActionIcon  size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
            onClick={() => setIsAgendaModalOpen(true)}>
            <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            </Group>
         
                <Select
                  label="Salle d'attente"
                  data={rooms}
                  value={settings.entry.waiting_room}
                  onChange={(value) => handleSettingChange('entry', 'waiting_room', value || '')}
                />
                <Select
                  label="Salle de consultation"
                  data={rooms}
                  value={settings.entry.consultation_room}
                  onChange={(value) => handleSettingChange('entry', 'consultation_room', value || '')}
                />
              </Group>
            </Grid.Col>
     
            <Grid.Col span={12}>
              <Group grow>
                <Select
                  label="Motif par défaut"
                  data={reasons}
                  value={settings.entry.reason}
                  onChange={(value) => handleSettingChange('entry', 'reason', value || '')}
                  searchable
                />
                <Select
                  label="Mode de paiement par défaut"
                  data={paymentTypes}
                  value={settings.payment.type}
                  onChange={(value) => handleSettingChange('payment', 'type', value || '')}
                />
                <Select
                  label="Banque par défaut"
                  data={banks}
                  value={settings.payment.bank}
                  onChange={(value) => handleSettingChange('payment', 'bank', value || '')}
                  searchable
                />
              </Group>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
        <Tabs.Panel value="visit" pt="xl">
  <Table striped>
    <Table.Thead>
      <Table.Tr>
        <Table.Th>Lien</Table.Th>
        <Table.Th style={{ textAlign: 'center' }}>Par défaut</Table.Th>
        <Table.Th>Désactivé</Table.Th>
        <Table.Th></Table.Th>
      </Table.Tr>
    </Table.Thead>
    <Table.Tbody>
      {visitLinks.map((link) => (
        <Table.Tr key={link.key}>
          <Table.Td>{link.title}</Table.Td>
          <Table.Td style={{ textAlign: 'center' }}>
            <ActionIcon
              variant="subtle"
              disabled={disabledLinks[link.key] || link.is_form}
              onClick={() => setFavoriteLink(link.key)}
            >
              <Icon 
                path={favoriteLink === link.key ? mdiStar : mdiStarOutline} 
                size={1} 
                color={favoriteLink === link.key ? 'var(--mantine-color-blue-6)' : undefined}
              />
            </ActionIcon>
          </Table.Td>
          <Table.Td>
            <Switch
              disabled={favoriteLink === link.key}
              checked={disabledLinks[link.key] || false}
              onChange={(event) => {
                setDisabledLinks(prev => ({
                  ...prev,
                  [link.key]: event.currentTarget.checked
                }));
              }}
            />
          </Table.Td>
          <Table.Td>
            <ActionIcon variant="subtle">
              <Icon path={mdiDrag} size={1} color="var(--mantine-color-red-6)" />
            </ActionIcon>
          </Table.Td>
        </Table.Tr>
      ))}
    </Table.Tbody>
  </Table>
        </Tabs.Panel>
        <Tabs.Panel value="custom" pt="xl">
  <Paper p="md">
    <Table striped>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>Lien</Table.Th>
          <Table.Th></Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {toolbarLinks.length === 0 ? (
          <Table.Tr>
            <Table.Td colSpan={2}>
              <Text ta="center">Aucun élément trouvé.</Text>
            </Table.Td>
          </Table.Tr>
        ) : (
          toolbarLinks.map((link) => (
            <Table.Tr key={link.id}>
              <Table.Td>{link.title}</Table.Td>
              <Table.Td></Table.Td>
            </Table.Tr>
          ))
        )}
      </Table.Tbody>
    </Table>

    <Group justify="flex-start" mt="md">
      <Menu position="bottom-start">
        <Menu.Target>
          <Button 
            leftSection={<Icon path={mdiPuzzlePlus} size={1} />}
            disabled={toolbarSubmodules.length === 0}
          >
            Ajouter un module
          </Button>
        </Menu.Target>
        <Menu.Dropdown>
          {toolbarSubmodules.map((module) => (
            <Menu.Item key={module.id}>
              {module.name}
            </Menu.Item>
          ))}
        </Menu.Dropdown>
      </Menu>
    </Group>
  </Paper>
</Tabs.Panel>
<Tabs.Panel value="flow" pt="xl">
  <Paper p="md">
    <Table striped>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>Nom de vue</Table.Th>
          <Table.Th style={{ textAlign: 'center' }}>Par défaut</Table.Th>
          <Table.Th style={{ textAlign: 'center' }}>Désactivé</Table.Th>
          <Table.Th></Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {workflowViews.map((item) => (
          <Table.Tr key={item.id}>
            <Table.Td>{item.name}</Table.Td>
            <Table.Td style={{ textAlign: 'center' }}>
              <Switch
                checked={workflowConfig.default === '1'}
                onChange={() => handleWorkflowConfig('default')}
              />
            </Table.Td>
            <Table.Td style={{ textAlign: 'center' }}>
              <Switch
                checked={workflowConfig.disabled[item.id] || false}
                onChange={() => handleWorkflowConfig('disabled', item.id)}
              />
            </Table.Td>
            <Table.Td>
              <Group gap="xs">
                <ActionIcon variant="subtle" onClick={() => handleWorkflowView(item)}>
                  <Icon path={mdiPencil} size={1} />
                </ActionIcon>
                <ActionIcon 
                  variant="subtle" 
                  color="red"
                  disabled={item.is_system}
                  onClick={() => handleRemoveWorkflow(item)}
                >
                  <Icon path={mdiDelete} size={1} />
                </ActionIcon>
              </Group>
            </Table.Td>
          </Table.Tr>
        ))}
      </Table.Tbody>
    </Table>
  </Paper>
</Tabs.Panel>
<Tabs.Panel value="prescriptions" pt="xl">
  <Paper p="md">
    <Title order={3}>Prescriptions</Title>

    <Grid>
      <Grid.Col span={4}>
        <Autocomplete
          label="Forme galénique - Unité"
          data={dosageForms}
          value={prescriptionConfig.default_values.dosage_form}
          onChange={(value) => handlePrescriptionConfig('default_values.dosage_form', value)}
        />
        
        <Switch 
          label="Base Medicament par défaut: Favories"
          checked={prescriptionConfig.use_bookmarked_db}
          onChange={(event) => handlePrescriptionConfig('use_bookmarked_db', event.currentTarget.checked)}
          mt="md"
        />

        <Switch
          label="Utilisation des produits Paramédicaux & Parapharmaceutiques"
          checked={prescriptionConfig.advance_mp}
          onChange={(event) => handlePrescriptionConfig('advance_mp', event.currentTarget.checked)}
          mt="md"
        />
      </Grid.Col>

      <Grid.Col span={8}>
        <Group grow>
          <TextInput
            label="Generateur de posologie (formule)"
            value={prescriptionConfig.arab_generator.generator.formula}
            onChange={(event) => handlePrescriptionConfig('arab_generator.generator.formula', event.currentTarget.value)}
          />
          <TextInput
            label="Generateur de posologie (prefix de la periode)"
            value={prescriptionConfig.arab_generator.generator.period_prefix}
            onChange={(event) => handlePrescriptionConfig('arab_generator.generator.period_prefix', event.currentTarget.value)}
          />
          <TextInput
            label="Generateur de posologie (debut de ligne)"
            value={prescriptionConfig.arab_generator.generator.new_line}
            onChange={(event) => handlePrescriptionConfig('arab_generator.generator.new_line', event.currentTarget.value)}
          />
        </Group>

        <Switch
          label="Integration avec Pharmacy/Stock"
          checked={prescriptionConfig.stock_integration}
          onChange={(event) => handlePrescriptionConfig('stock_integration', event.currentTarget.checked)}
          mt="md"
        />

        <Switch
          label="Utilisation de la posologie en Arabe"
          checked={prescriptionConfig.arab_generator.is_used}
          onChange={(event) => handlePrescriptionConfig('arab_generator.is_used', event.currentTarget.checked)}
          mt="md"
        />
      </Grid.Col>
    </Grid>
  </Paper>
</Tabs.Panel>
<Tabs.Panel value="billing" pt="xl">
  <Paper p="md">
    <Stack>
      <Select
        label="Onglet par défaut"
        data={[
          { value: 'invoice_list', label: 'Liste des factures' },
          { value: 'contract_list', label: 'Liste des contrats' },
          { value: 'billing_workflow', label: 'Flux de faturation' }
        ]}
        value={billingConfig.defaultTab}
        onChange={(value) => handleBillingConfig('defaultTab', value || '')}
      />

      <Switch
        label="Générer un bon de consignation à partir du contrat"
        checked={billingConfig.generate_contract_consignment_doc}
        onChange={(event) => handleBillingConfig('generate_contract_consignment_doc', event.currentTarget.checked)}
      />

      <Select
        label="Affichage par défaut"
        data={[
          { value: 'flattened_list', label: 'Aplatie' },
          { value: 'contract_default_list', label: 'Par défaut' }
        ]}
        value={billingConfig.default_contract_view}
        onChange={(value) => handleBillingConfig('default_contract_view', value || '')}
      />

      <Title order={3}>États des contracts</Title>
      
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>État</Table.Th>
            <Table.Th>Couleur</Table.Th>
            <Table.Th>Motif</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {contractStates.map((state) => (
            <Table.Tr key={state.id}>
              <Table.Td>{state.name}</Table.Td>
              <Table.Td>
                <ColorInput
                  value={state.color}
                  onChange={(color) => {
                    setContractStates(prev => 
                      prev.map(s => s.id === state.id ? { ...s, color } : s)
                    );
                  }}
                  withPicker
                  swatches={[
                    '#25262b', '#868e96', '#fa5252', '#e64980', '#be4bdb',
                    '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886'
                  ]}
                />
              </Table.Td>
              <Table.Td>
                <TextInput
                  value={state.label}
                  onChange={(event) => {
                    setContractStates(prev => 
                      prev.map(s => s.id === state.id ? { ...s, label: event.currentTarget.value } : s)
                    );
                  }}
                />
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </Stack>
  </Paper>
</Tabs.Panel>
      </Tabs>
      <Modal
        opened={isAgendaModalOpen}
        onClose={() => setIsAgendaModalOpen(false)}
        title={
          <Group>
            <Icon path={mdiCalendarEdit} size={1} />
            <Text>Agenda</Text>
          </Group>
        }
        size="md"
      >
        <form onSubmit={(e) => { e.preventDefault(); handleAgendaSubmit(); }}>
          <Stack  gap="md">
            <TextInput
              required
              label="Nom"
              value={agendaForm.name}
              onChange={(e) => setAgendaForm({ ...agendaForm, name: e.target.value })}
            />
           <Group>
            <ColorInput
                label="Couleur"
                value={agendaForm.color}
                onChange={(color) => setAgendaForm({ ...agendaForm, color })}
                w={"80%"}
            />
            <ActionIcon size="input-sm" variant="default" aria-label="ActionIcon the same size as inputs"
                onClick={() => setIsColorPickerModalOpen(true)}>
                <IconPalette stroke={2} />
            </ActionIcon>
        </Group>
      
            <TextInput
              label="Description"
              value={agendaForm.description}
              onChange={(e) => setAgendaForm({ ...agendaForm, description: e.target.value })}
            />
            <MultiSelect
              label="Services désignés"
              data={[]} // Add your services data here
              value={agendaForm.services}
              onChange={(values) => setAgendaForm({ ...agendaForm, services: values })}
              searchable
              clearable
            />
             <Group justify="flex-start">
            <Switch
                defaultChecked
                label="Afficher comme ressource"
                size="xs"
                />
            </Group>
            <Group justify="flex-end">
              <Button variant="filled" type="submit">
                Sauvegarder
              </Button>
              <Button variant="filled" type="submit" color='red'
              onClick={() => setIsAgendaModalOpen(false)}
              >
              Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
      <Modal  
            opened={isColorPickerModalOpen}
            onClose={() => setIsColorPickerModalOpen(false)} 
            withCloseButton={false}
        >
            <ColorPicker 
                format="hex" 
                value={agendaForm.color}
                onChange={handleColorChange}
                swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} 
            />
        </Modal>
    </Paper>
  );
}