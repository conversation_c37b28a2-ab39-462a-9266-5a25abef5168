<div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon md-font-icon="mdi-wrench" md-font-set="mdi" class="md-font mdi mdi-wrench" role="img" aria-label="mdi-wrench"></md-icon>
        </div>
        <h2 translate-once="app_general">Paramètres de base</h2>
        <span flex="" class="flex"></span>
        <button class="md-button md-ink-ripple ng-hide" type="button" ng-transclude="" aria-label="handle workflow" ng-click="vm.handleWorkflowView(null, $event)" ng-show="vm.isWorkflow &amp;&amp; !vm.workflowForm" aria-hidden="true">
            <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
            <span translate-once="wf_new_view" class="ng-scope">Ajouter un vue</span>
        </button>
    </div>
</md-toolbar>

<md-content class="mn-module-body layout-fill flex layout-column ng-scope _md">
    <md-tabs flex="" md-border-bottom="" class="ng-isolate-scope flex"><md-tabs-wrapper> <md-tab-data>
        <md-tab md-active="true" class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        <md-tab class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        <md-tab class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        <md-tab md-on-deselect="vm.workflowForm = vm.isWorkflow = false;" md-on-select="vm.isWorkflow = true" class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        <md-tab class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        

        <md-tab class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        

    </md-tab-data> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <md-tabs-canvas tabindex="0" ng-focus="$mdTabsCtrl.redirectFocus()" ng-class="{ 'md-paginated': $mdTabsCtrl.shouldPaginate, 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" ng-keydown="$mdTabsCtrl.keydown($event)"> <md-pagination-wrapper ng-class="{ 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" md-tab-scroll="$mdTabsCtrl.scroll($event)" role="tablist" aria-label="Use the left and right arrow keys to navigate between tabs" style="transform: translate(0px, 0px);"><!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="0" class="md-tab md-active" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-17" md-tab-id="17" aria-selected="true" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-17" style="">
                <span translate-once="general_app_config" class="ng-scope">Général</span>
            <div class="md-ripple-container" style=""></div></md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-18" md-tab-id="18" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-18" style="">
                <span translate-once="general_visit_config" class="ng-scope">Visite</span>
            <div class="md-ripple-container" style=""></div></md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab " ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-19" md-tab-id="19" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-19">
                <span translate-once="general_main_toolbar_config" class="ng-scope">Module personalisés</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab " ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-20" md-tab-id="20" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-20">
                <span translate-once="general_workflow_config" class="ng-scope">Flux</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab " ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-21" md-tab-id="21" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-21">
                <span translate-once="general_prescription_config" class="ng-scope">Prescriptions</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab " ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-22" md-tab-id="22" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-22">
                <span translate-once="general_billing_config" class="ng-scope">Facturation</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> <md-ink-bar style="left: 0px; right: 437px;" class=""></md-ink-bar> </md-pagination-wrapper> <md-tabs-dummy-wrapper aria-hidden="true" class="md-visually-hidden md-dummy-wrapper"> <!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_app_config" class="ng-scope">Général</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_visit_config" class="ng-scope">Visite</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_main_toolbar_config" class="ng-scope">Module personalisés</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_workflow_config" class="ng-scope">Flux</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_prescription_config" class="ng-scope">Prescriptions</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="general_billing_config" class="ng-scope">Facturation</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> </md-tabs-dummy-wrapper> </md-tabs-canvas> </md-tabs-wrapper> <md-tabs-content-wrapper ng-show="$mdTabsCtrl.hasContent &amp;&amp; $mdTabsCtrl.selectedIndex >= 0" class="_md" aria-hidden="false"> <!-- ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-17" class="_md ng-scope md-active" role="tabpanel" aria-labelledby="tab-item-17" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope" style="">
                <div mn-bind-html="parameters/views/general-app-config.html" class="layout ng-scope"><div flex="" layout="column" class="md-padding layout-column flex">
    
    

    

    

    

    

    

    

    <div class="layout-row">
        <mn-physician flex="33" ng-model="vm.default.physician" mn-label="general_default_physician" ng-change="vm.updateDefault()" class="ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope flex-33 ng-not-empty" aria-invalid="false"><md-input-container class="md-auto-horizontal-margin md-input-has-value">
                        <label translate-once="general_default_physician" aria-hidden="true" for="select_211" class="">Médecin par défaut</label>
                        <md-select class="no-badge ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-not-empty" aria-label="physician" ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id'}" ng-change="vm.internalValueChanged()" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_211" aria-invalid="false" aria-labelledby="select_211 select_value_label_201"><md-select-value class="md-select-value" id="select_value_label_201"><span><div class="md-text">
                                <span ng-bind="::item.full_name" class="ng-binding">Dr. DEMO DEMO</span>
                            </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_212">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id'}" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" aria-label="" id="select_listbox_213">
                            <!-- ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_232" value="[object Object]" selected="selected" aria-selected="true"><div class="md-text">
                                <span ng-bind="::item.full_name" class="ng-binding">Dr. DEMO DEMO</span>
                            </div></md-option><!-- end ngRepeat: item in vm.items track by item.id -->
                        </md-content></md-select-menu></div></md-select>
                        <!-- ngIf: vm.value && !vm.disabled && true --><div class="mn-option-buttons flex-nogrow layout-row ng-scope" ng-if="vm.value &amp;&amp; !vm.disabled &amp;&amp; true">
                            <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                                <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                            </button>
                        </div><!-- end ngIf: vm.value && !vm.disabled && true -->
                    </md-input-container></mn-physician>
    </div>

    <div class="layout-row">
        <mn-agenda class="flex ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope ng-not-empty" ng-model="vm.default.appointment.agenda" label="general_default_agenda" ng-change="vm.updateDefault()" aria-invalid="false">
                <md-input-container class="md-auto-horizontal-margin md-input-has-value">
                    <label translate-once="general_default_agenda" aria-hidden="true" for="select_214" class="">Agenda par défaut</label>
                    <md-select ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id' }" ng-change="vm.internalValueChanged()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-not-empty" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_214" aria-invalid="false" aria-labelledby="select_214 select_value_label_202" aria-label="Agenda par défaut"><md-select-value class="md-select-value" id="select_value_label_202"><span><div class="md-text">
                            <span ng-bind="::item.name" class="flex ng-binding">Cabinet</span>
                        </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_215">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id' }" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" aria-label="" id="select_listbox_216">
                        <!-- ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_233" value="[object Object]" selected="selected" aria-selected="true"><div class="md-text">
                            <span ng-bind="::item.name" class="flex ng-binding">Cabinet</span>
                        </div></md-option><!-- end ngRepeat: item in vm.items track by item.id -->
                    </md-content></md-select-menu></div></md-select>
                    <div class="mn-option-buttons flex-nogrow layout-row">
                        <!-- ngIf: !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled" ng-click="vm.add($event)" aria-label="add" tabindex="-1">
                            <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
                        </button><!-- end ngIf: !vm.disabled -->
                        <!-- ngIf: vm.value && !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="vm.value &amp;&amp; !vm.disabled" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                            <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                        </button><!-- end ngIf: vm.value && !vm.disabled -->
                    </div>
                </md-input-container>
        </mn-agenda>
        <mn-room flex="" ng-model="vm.default.entry.waiting_room" label="general_default_wr_room" no-badge="" type="WR" ng-change="vm.updateDefault()" class="ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope flex ng-not-empty" aria-invalid="false">
            <md-input-container class="md-auto-horizontal-margin md-input-has-placeholder md-input-has-value">
                <label translate-once="waiting_room" aria-hidden="true" for="select_217">Salle d'attente</label>
                <md-select placeholder="Salle d'attente" ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id' }" ng-change="vm.internalValueChanged()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-not-empty" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_217" aria-invalid="false" aria-labelledby="select_217 select_value_label_203" aria-label="Salle d'attente"><md-select-value class="md-select-value" id="select_value_label_203"><span><div class="md-text">
                        <span ng-bind="::item.name" class="flex ng-binding">SLT</span>
                        <span class="mn-badge ng-hide" ng-hide="true" aria-hidden="true">
                            <span ng-bind="::(item.occupy + '/' + item.capacity)" class="ng-binding">0/30</span>
                        </span>
                    </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_218">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id' }" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" aria-label="" id="select_listbox_219">
                    <!-- ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_234" value="[object Object]" selected="selected" aria-selected="true"><div class="md-text">
                        <span ng-bind="::item.name" class="flex ng-binding">SLT</span>
                        <span class="mn-badge ng-hide" ng-hide="true" aria-hidden="true">
                            <span ng-bind="::(item.occupy + '/' + item.capacity)" class="ng-binding">0/30</span>
                        </span>
                    </div></md-option><!-- end ngRepeat: item in vm.items track by item.id -->
                </md-content></md-select-menu></div></md-select>
                <div class="mn-option-buttons flex-nogrow layout-row">
                    <!-- ngIf: !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled" ng-click="vm.add($event)" aria-label="add" tabindex="-1">
                        <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: !vm.disabled -->
                    <!-- ngIf: vm.value && !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="vm.value &amp;&amp; !vm.disabled" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                        <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: vm.value && !vm.disabled -->
                </div>
            </md-input-container>
        </mn-room>
        <mn-room flex="" ng-model="vm.default.entry.consultation_room" label="general_default_consultation_room" no-badge="" type="CR" ng-change="vm.updateDefault()" class="ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope flex ng-not-empty" aria-invalid="false">
            <md-input-container class="md-auto-horizontal-margin md-input-has-placeholder md-input-has-value">
                <label translate-once="consultation_room" aria-hidden="true" for="select_220">Salle de consultation</label>
                <md-select placeholder="Salle de consultation" ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id' }" ng-change="vm.internalValueChanged()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-not-empty" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_220" aria-invalid="false" aria-labelledby="select_220 select_value_label_204" aria-label="Salle de consultation"><md-select-value class="md-select-value" id="select_value_label_204"><span><div class="md-text">
                        <span ng-bind="::item.name" class="flex ng-binding">FTL</span>
                        <span class="mn-badge ng-hide" ng-hide="true" aria-hidden="true">
                            <span ng-bind="::(item.occupy + '/' + item.capacity)" class="ng-binding">0/1</span>
                        </span>
                    </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_221">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id' }" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="_md" aria-label="" id="select_listbox_222">
                    <!-- ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" tabindex="0" class="ng-scope md-ink-ripple" role="option" id="select_option_235" value="[object Object]" selected="selected" aria-selected="true"><div class="md-text">
                        <span ng-bind="::item.name" class="flex ng-binding">FTL</span>
                        <span class="mn-badge ng-hide" ng-hide="true" aria-hidden="true">
                            <span ng-bind="::(item.occupy + '/' + item.capacity)" class="ng-binding">0/1</span>
                        </span>
                    </div></md-option><!-- end ngRepeat: item in vm.items track by item.id -->
                </md-content></md-select-menu></div></md-select>
                <div class="mn-option-buttons flex-nogrow layout-row">
                    <!-- ngIf: !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled" ng-click="vm.add($event)" aria-label="add" tabindex="-1">
                        <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: !vm.disabled -->
                    <!-- ngIf: vm.value && !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="vm.value &amp;&amp; !vm.disabled" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                        <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: vm.value && !vm.disabled -->
                </div>
            </md-input-container>
        </mn-room>
    </div>

    <div class="layout-row">
        <mn-reason flex="" ng-model="vm.default.entry.reason" no-badge="" label="general_default_reason" ng-change="vm.updateDefault()" class="ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope flex ng-not-empty" aria-invalid="false">
            <md-input-container class="md-auto-horizontal-margin md-input-has-value">
               <label translate-once="general_default_reason" aria-hidden="true" for="select_223" class="">Motif par défaut</label>
               <md-select md-on-open="vm.mdSelectEvent('open')" md-on-close="vm.mdSelectEvent('close')" ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id' }" ng-change="vm.internalValueChanged()" class="no-badge ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_223" aria-invalid="false" aria-label="Motif par défaut" aria-labelledby="select_223 select_value_label_205"><md-select-value class="md-select-value" id="select_value_label_205"><span><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Consultation</span>
                       
                   </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_224">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id' }" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="select-with-search _md" aria-label="" id="select_listbox_225"><div> <!-- ngIf: $$loadingAsyncDone === false --></div>
                   <mn-select-search-header subject="vm.eventSubject" search-by="description" items="vm.setItems($event)" init-items="vm.initItems" class="ng-isolate-scope"><md-select-header><input type="search" ng-model-options="{debounce: 300}" ng-keydown="$event.stopPropagation();" ng-model="vm.searchKey" ng-change="vm.search()" translate-once-placeholder="search" class="md-text ng-pristine ng-untouched ng-valid ng-scope ng-empty" mn-auto-focus="vm.inputFocus" autocomplete="off" aria-invalid="false" placeholder="Rechercher"></md-select-header></mn-select-search-header>
                   <!-- ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_237" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">1er Consultation</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_238" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Changement D'élastique</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_239" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Chirurgie/Paro</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_240" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Collage</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_241" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Composite</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_242" aria-hidden="true" value="[object Object]" selected="selected" aria-selected="true" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Consultation</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_243" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Contention</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_244" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Contrôle</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_245" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Depose Sutures</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_246" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Detartrage</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_247" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Devis</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_248" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Echographie</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_249" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Endodontie</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_250" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Formation</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_251" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Implantologie</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_252" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Obturation Canalaire</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_253" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Orthodontie</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_254" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">PA ES Chassis/Monta</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_255" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">PA Pose</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_256" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">PC ESS Armature</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_257" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">PC Scellement</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_258" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">PC TP EMP</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_259" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Prophylaxie</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_260" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">Urgent</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id --><md-option ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_261" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                       <span ng-bind="::item.description" class="flex ng-binding">polissage</span>
                       
                   </div></md-option><!-- end ngRepeat: item in vm.items track by item.id -->
               </md-content></md-select-menu></div></md-select>
               <div class="mn-option-buttons flex-nogrow layout-row">
                   <!-- ngIf: !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled" ng-click="vm.add($event)" aria-label="add" tabindex="-1">
                       <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
                   </button><!-- end ngIf: !vm.disabled -->
                   <!-- ngIf: vm.value && !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="vm.value &amp;&amp; !vm.disabled" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                       <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                   </button><!-- end ngIf: vm.value && !vm.disabled -->
               </div>
            </md-input-container>
        </mn-reason>
        <mn-list flex="" mn-model="PaymentModeType" mn-label="general_default_payment_mode" ng-model="vm.default.payment.type" ng-change="vm.updateDefault()" class="ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope flex ng-not-empty" aria-invalid="false">
            <md-input-container class="md-auto-horizontal-margin md-input-has-value">
                
                <label translate-once="general_default_payment_mode" aria-hidden="true" for="select_226" class="">Mode de paiement par défaut</label>
                <md-select aria-label="select" md-on-open="vm.mdSelectEvent('open')" md-on-close="vm.mdSelectEvent('close')" ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id' }" ng-change="vm.internalValueChanged()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_226" aria-invalid="false" aria-labelledby="select_226 select_value_label_206"><md-select-value class="md-select-value" id="select_value_label_206"><span><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Espèce</span>
                       </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_227">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id' }" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="select-with-search _md" aria-label="" id="select_listbox_228"><div> <!-- ngIf: $$loadingAsyncDone === false --></div>
                       
            <mn-select-search-header subject="vm.eventSubject" search-by="value" items="vm.setItems($event)" init-items="vm.initItems" class="ng-isolate-scope"><md-select-header><input type="search" ng-model-options="{debounce: 300}" ng-keydown="$event.stopPropagation();" ng-model="vm.searchKey" ng-change="vm.search()" translate-once-placeholder="search" class="md-text ng-pristine ng-untouched ng-valid ng-scope ng-empty" mn-auto-focus="vm.inputFocus" autocomplete="off" aria-invalid="false" placeholder="Rechercher"></md-select-header></mn-select-search-header>
        
                       <!-- ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_262" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Aucune</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_263" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Chèque</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_264" aria-hidden="true" value="[object Object]" selected="selected" aria-selected="true" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Espèce</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_265" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Traite</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id -->
                </md-content></md-select-menu></div></md-select>
                <div class="mn-option-buttons flex-nogrow layout-row">
                    <!-- ngIf: !vm.disabled && !vm.locked --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled &amp;&amp; !vm.locked" ng-click="vm.add($event)" aria-label="add" tabindex="-1">
                        <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: !vm.disabled && !vm.locked -->
                    <!-- ngIf: vm.value && !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="vm.value &amp;&amp; !vm.disabled" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                        <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: vm.value && !vm.disabled -->
                </div>
            </md-input-container>
        </mn-list>
        <mn-list flex="" mn-model="Bank" mn-label="general_default_bank" ng-model="vm.default.payment.bank" ng-change="vm.updateDefault()" class="ng-pristine ng-untouched ng-valid layout-row layout-align-start-center ng-isolate-scope flex ng-not-empty" aria-invalid="false">
            <md-input-container class="md-auto-horizontal-margin md-input-has-value">
                
                <label translate-once="general_default_bank" aria-hidden="true" for="select_229" class="">Banque par défaut</label>
                <md-select aria-label="select" md-on-open="vm.mdSelectEvent('open')" md-on-close="vm.mdSelectEvent('close')" ng-model="vm.internalValue" ng-model-options="{ trackBy: '$value.id' }" ng-change="vm.internalValueChanged()" class="ng-pristine ng-untouched ng-valid md-auto-horizontal-margin ng-empty" tabindex="0" aria-disabled="false" role="button" aria-haspopup="listbox" id="select_229" aria-invalid="false" aria-labelledby="select_229 select_value_label_207"><md-select-value class="md-select-value" id="select_value_label_207"><span><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Aucune</span>
                       </div></span><span class="md-select-icon" aria-hidden="true"></span></md-select-value><div class="md-select-menu-container" aria-hidden="true" role="presentation" id="select_container_230">  <md-select-menu role="presentation" ng-model-options="{ trackBy: '$value.id' }" class="_md"><md-content role="listbox" tabindex="-1" aria-multiselectable="false" class="select-with-search _md" aria-label="" id="select_listbox_231"><div> <!-- ngIf: $$loadingAsyncDone === false --></div>
                       
            <mn-select-search-header subject="vm.eventSubject" search-by="value" items="vm.setItems($event)" init-items="vm.initItems" class="ng-isolate-scope"><md-select-header><input type="search" ng-model-options="{debounce: 300}" ng-keydown="$event.stopPropagation();" ng-model="vm.searchKey" ng-change="vm.search()" translate-once-placeholder="search" class="md-text ng-pristine ng-untouched ng-valid ng-scope ng-empty" mn-auto-focus="vm.inputFocus" autocomplete="off" aria-invalid="false" placeholder="Rechercher"></md-select-header></mn-select-search-header>
        
                       <!-- ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_266" aria-hidden="true" value="[object Object]" selected="selected" aria-selected="true" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">Aucune</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_267" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BCP</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_268" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BMCI</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_269" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BMCE</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_270" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">AWB</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_271" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">SGMB</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_272" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CDM</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_273" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BAM</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_274" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">ABB</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_275" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">ABM</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_276" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BAA</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_277" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CDG</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_278" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CFG</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_279" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CITI</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_280" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CIH</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_281" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">FEC</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_282" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">MFC</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_283" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">UMB</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_284" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BANCO</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_285" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CAIXA</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_286" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">UMNIA</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_287" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BTI</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_288" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">BAY</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_289" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">ASSAFA</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_290" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">AAB</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_291" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CIB</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_292" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">UPL</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_293" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">HLD</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id --><!-- ngIf: vm.items --><md-option ng-if="vm.items" ng-repeat="item in vm.items track by item.id" ng-value="item" ng-show="$$loadingAsyncDone" tabindex="0" class="ng-scope md-ink-ripple ng-hide" role="option" id="select_option_294" aria-hidden="true" value="[object Object]" style=""><div class="md-text">
                           <span ng-bind="::item.value" class="ng-binding">CAT</span>
                       </div></md-option><!-- end ngIf: vm.items --><!-- end ngRepeat: item in vm.items track by item.id -->
                </md-content></md-select-menu></div></md-select>
                <div class="mn-option-buttons flex-nogrow layout-row">
                    <!-- ngIf: !vm.disabled && !vm.locked --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="!vm.disabled &amp;&amp; !vm.locked" ng-click="vm.add($event)" aria-label="add" tabindex="-1">
                        <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: !vm.disabled && !vm.locked -->
                    <!-- ngIf: vm.value && !vm.disabled --><button class="md-icon-button md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-if="vm.value &amp;&amp; !vm.disabled" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">
                        <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>
                    </button><!-- end ngIf: vm.value && !vm.disabled -->
                </div>
            </md-input-container>
        </mn-list>
    </div>

    

    

</div>
</div>
            </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-18" class="_md ng-scope md-right" role="tabpanel" aria-labelledby="tab-item-18" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-19" class="_md ng-scope md-right" role="tabpanel" aria-labelledby="tab-item-19" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-20" class="_md ng-scope md-right" role="tabpanel" aria-labelledby="tab-item-20" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-21" class="_md ng-scope md-right" role="tabpanel" aria-labelledby="tab-item-21" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-22" class="_md ng-scope md-right" role="tabpanel" aria-labelledby="tab-item-22" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }"> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --> </md-tabs-content-wrapper></md-tabs>
</md-content>
</div>