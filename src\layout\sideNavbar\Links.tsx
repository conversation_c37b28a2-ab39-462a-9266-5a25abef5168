import { useEffect, useMemo, useState } from 'react';
import {
  Box,
  Collapse,
  Group,
  Menu,
  Text,
  Tooltip,
  UnstyledButton,
} from '@mantine/core';
import { IconChevronRight, IconProps } from '@tabler/icons-react';
import { usePathname, useRouter } from 'next/navigation';
import classes from './Links.module.css';

interface LinksGroupProps {
  icon?: React.ComponentType<IconProps>;
  label: string;
  initiallyOpened?: boolean;
  link?: string;
  links?: {
    label: string;
    link: string;
  }[];

  isMini?: boolean;
}
export function LinksGroup(props: LinksGroupProps) {
  const {
    icon: Icon,
    label,
    initiallyOpened,
    link,
    links,
    isMini,
  } = props;
  const router = useRouter();
  const pathname = usePathname();
  const hasLinks = Array.isArray(links);
  const [opened, setOpened] = useState(initiallyOpened || false);
  const ChevronIcon = IconChevronRight;

  const LinkItem = ({ link }: { link: { label: string; link: string } }) => {
    return (
      <Text
        component="button"
        className={classes.link}
        onClick={() => {
          router.push(link.link);

        }}
        data-active={link.link.toLowerCase() === pathname || undefined}
        data-mini={isMini}
      >
        {link.label}
      </Text>
    );
  };
  const items = (hasLinks ? links : []).map((link) =>
    isMini ? (
      <Menu.Item key={`menu-${link.label}`}>
        <LinkItem link={link} />
      </Menu.Item>
    ) : (
      <LinkItem key={link.label} link={link} />
    ),
  );
  const content: React.ReactElement = useMemo(() => {
    let view: React.ReactElement;
    if (isMini) {
      view = (
        <>
          <Menu
            position="right-start"
            withArrow
            arrowPosition="center"
            trigger="hover"
            openDelay={100}
            closeDelay={400}
          >
            <Menu.Target>
              <UnstyledButton
                onClick={() => {
                  setOpened((o) => !o);
                  if (link) {
                    router.push(link);
                  }
                }}
                className={classes.control}
                data-active={opened || undefined}
                data-mini={isMini}
              >
                <Tooltip
                  label={label}
                  position="right"
                  transitionProps={{ duration: 0 }}
                >
                  {Icon ? <Icon size={24} /> : null}
                </Tooltip>
              </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown>{items}</Menu.Dropdown>
          </Menu>
        </>
      );
    } else {
      view = (
        <>
          <UnstyledButton
            onClick={() => {
              setOpened((o) => !o);
              if (link) {
                router.push(link);
              }
            }}
            className={classes.control}
            data-active={opened || undefined}
            data-mini={isMini}
          >
            <Group justify="space-between" gap={0}>
              <Box style={{ display: 'flex', alignItems: 'center' }}>
                {Icon ? <Icon size={18} /> : null}
                {!isMini && <Box ml="md">{label}</Box>}
              </Box>
              {hasLinks && (
                <ChevronIcon
                  className={classes.chevron}
                  size="1rem"
                  stroke={1.5}
                  style={{
                    transform: opened ? `rotate(90deg)` : 'none',
                  }}
                />
              )}
            </Group>
          </UnstyledButton>
          {hasLinks ? <Collapse in={opened}>{items}</Collapse> : null}
        </>
      );
    }

    return view;
  }, [
    ChevronIcon,
    Icon,

    hasLinks,
    isMini,
    items,
    label,
    link,
    opened,
    router,
  ]);

  useEffect(() => {
    const paths = pathname.split('/');
    setOpened(paths.includes(label.toLowerCase()));
  }, [pathname, label]);

  return <>{content}</>;
}