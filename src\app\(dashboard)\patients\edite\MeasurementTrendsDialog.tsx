import {
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
  <PERSON>rollA<PERSON>,
  Text,
  UnstyledButton,
  rem,
} from '@mantine/core';
import { IconChartLine, IconX, IconListDetails, IconAlertCircle } from '@tabler/icons-react';
import { useState } from 'react';
import { Line } from 'react-chartjs-2';
import type { ChartData } from 'chart.js';

interface MeasurementItem {
  id: string;
  name: string;
  data: number[];
  labels: string[];
}

interface Props {
  opened: boolean;
  onClose: () => void;
  items: MeasurementItem[];
}

export function MeasurementTrendsDialog({ opened, onClose, items }: Props) {
  const [currentItem, setCurrentItem] = useState<MeasurementItem | null>(null);

  return (
    <Drawer
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconChartLine size={20} />
          <Text fw={600}>Tendances</Text>
        </Group>
      }
      position="right"
      size="xl"
    >
      <Group align="flex-start" gap="lg" mt="md">
        {/* Sidebar */}
        <ScrollArea w={200} h={400}>
          <Stack>
            <Group>
              <IconListDetails size={18} />
              <Text size="sm" fw={500}>Biométries disponibles</Text>
            </Group>
            {items.map((item) => (
              <UnstyledButton
                key={item.id}
                onClick={() => setCurrentItem(item)}
                style={{
                  backgroundColor:
                    currentItem?.id === item.id ? '#e7f5ff' : 'transparent',
                  padding: rem(8),
                  borderRadius: rem(6),
                  textAlign: 'left',
                }}
              >
                {item.name}
              </UnstyledButton>
            ))}
          </Stack>
        </ScrollArea>

        {/* Chart area */}
        <div style={{ flex: 1 }}>
          {!currentItem ? (
            <Group mt="lg" align="center" gap="xs">
              <IconAlertCircle size={20} color="gray" />
              <Text>Aucun élément sélectionné</Text>
            </Group>
          ) : (
            <Line
              height={300}
              data={{
                labels: currentItem.labels,
                datasets: [
                  {
                    label: currentItem.name,
                    data: currentItem.data,
                    fill: false,
                    borderColor: '#339af0',
                    tension: 0.3,
                  },
                ],
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: { beginAtZero: false },
                },
              }}
            />
          )}
        </div>
      </Group>
    </Drawer>
  );
}
