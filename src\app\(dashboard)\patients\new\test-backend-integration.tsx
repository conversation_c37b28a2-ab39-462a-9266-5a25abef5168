"use client";
import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Paper, 
  Group, 
  Button, 
  Badge, 
  Text, 
  Stack,
  Alert,
  List,
  Code,
  Grid,
  Card,
  JsonInput,
  Tabs,
  Loader,
  Title
} from '@mantine/core';
import { 
  IconUser, 
  IconCheck, 
  IconX,
  IconRefresh,
  IconCalendar,
  IconPlus,
  IconMedicalCross,
  IconShield,
  IconHome,
  IconDatabase,
  IconApi,
  IconTestPipe,
  IconBrowserCheck
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

// Import our complete services
import { completePatientService, CompletePatientData } from '../../../services/completePatientService';

/**
 * Composant de test pour valider l'intégration backend de la page de création de patient
 */
const TestBackendIntegration: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState<'unknown' | 'connected' | 'error'>('unknown');

  // Test de connexion API
  const testApiConnection = async () => {
    setLoading(true);
    try {
      console.log('🔄 Test de connexion API...');
      const doctorsData = await completePatientService.getDoctors();
      setDoctors(doctorsData);
      setApiStatus('connected');
      console.log('✅ API connectée, médecins récupérés:', doctorsData.length);
      
      setTestResults(prev => [{
        id: Date.now(),
        type: 'api_connection',
        status: 'success',
        message: `${doctorsData.length} médecins récupérés`,
        timestamp: new Date().toISOString(),
        data: doctorsData
      }, ...prev]);
      
    } catch (error) {
      console.error('❌ Erreur de connexion API:', error);
      setApiStatus('error');
      
      setTestResults(prev => [{
        id: Date.now(),
        type: 'api_connection',
        status: 'error',
        message: 'Erreur de connexion API',
        timestamp: new Date().toISOString(),
        error: error
      }, ...prev]);
    } finally {
      setLoading(false);
    }
  };

  // Test de validation des données
  const testDataValidation = () => {
    const testData: CompletePatientData = {
      // Données valides
      firstName: 'Jean',
      lastName: 'Dupont',
      email: '<EMAIL>',
      phone: '**********',
      dateOfBirth: new Date('1990-01-01'),
      age: '34',
      gender: 'male',
      nationalIdNumber: '**********',
      passportNumber: '',
      maritalStatus: 'single',
      
      // Adresse
      address: '123 Rue de la Paix',
      city: 'Paris',
      state: 'Île-de-France',
      zipCode: '75001',
      
      // Informations médicales
      medicalHistory: 'Aucun antécédent particulier',
      allergies: 'Aucune allergie connue',
      
      // Assurance
      insuranceCompany: 'aetna',
      insurancePolicyNumber: 'POL123456',
      
      // Contact d'urgence
      emergencyContactName: 'Marie Dupont',
      emergencyContactPhone: '**********',
      emergencyContactRelationship: 'Épouse',
      
      // Informations de visite
      doctorAssigned: 'doctor-1',
      visitType: 'diagnosis',
      visitDuration: '30',
      agenda: 'regular',
      comments: 'Première consultation',
      diagnosticRoom: 'room-a',
      additionalNotes: 'Patient très coopératif',
      
      // Rendez-vous
      appointment_date: new Date(),
      duration: 30,
      resourceId: 1,
      consultation_type: 'consultation'
    };

    const validation = completePatientService.validatePatientData(testData);
    console.log('🧪 Résultat de validation:', validation);
    
    setTestResults(prev => [{
      id: Date.now(),
      type: 'data_validation',
      status: validation.isValid ? 'success' : 'error',
      message: validation.isValid ? 'Validation réussie' : `Erreurs: ${validation.errors.join(', ')}`,
      timestamp: new Date().toISOString(),
      data: { validation, testData }
    }, ...prev]);
    
    return validation;
  };

  // Créer un patient de test complet
  const createTestPatient = async () => {
    setLoading(true);
    try {
      const testData: CompletePatientData = {
        firstName: 'Test',
        lastName: `Patient-${Date.now()}`,
        email: `test.patient.${Date.now()}@example.com`,
        phone: '**********',
        dateOfBirth: new Date('1985-05-15'),
        age: '39',
        gender: 'female',
        nationalIdNumber: '**********',
        passportNumber: '',
        maritalStatus: 'married',
        
        address: '456 Avenue des Tests',
        city: 'Lyon',
        state: 'Rhône-Alpes',
        zipCode: '69000',
        
        medicalHistory: 'Historique médical de test',
        allergies: 'Allergie aux tests automatisés',
        
        insuranceCompany: 'blue-cross',
        insurancePolicyNumber: 'TEST123456',
        
        emergencyContactName: 'Contact Test',
        emergencyContactPhone: '**********',
        emergencyContactRelationship: 'Famille',
        
        doctorAssigned: doctors.length > 0 ? doctors[0].id : 'doctor-1',
        visitType: 'examination',
        visitDuration: '20',
        agenda: 'regular',
        comments: 'Patient créé automatiquement pour test',
        diagnosticRoom: 'room-b',
        additionalNotes: 'Test d\'intégration backend depuis la page',
        
        appointment_date: new Date(),
        duration: 30,
        resourceId: 2,
        consultation_type: 'consultation'
      };

      console.log('🚀 Création du patient de test depuis la page:', testData);
      const result = await completePatientService.createCompletePatient(testData);
      
      setTestResults(prev => [{
        id: Date.now(),
        type: 'patient_creation',
        timestamp: new Date().toISOString(),
        data: testData,
        result: result,
        status: 'success',
        message: `Patient ${testData.firstName} ${testData.lastName} créé avec succès`
      }, ...prev]);

      console.log('✅ Patient de test créé avec succès:', result);
      
      notifications.show({
        title: 'Test réussi',
        message: `Patient ${testData.firstName} ${testData.lastName} créé avec succès`,
        color: 'green',
      });
      
    } catch (error) {
      console.error('❌ Erreur lors de la création du patient de test:', error);
      
      setTestResults(prev => [{
        id: Date.now(),
        type: 'patient_creation',
        timestamp: new Date().toISOString(),
        error: error,
        status: 'error',
        message: 'Erreur lors de la création du patient'
      }, ...prev]);
      
      notifications.show({
        title: 'Test échoué',
        message: 'Erreur lors de la création du patient de test',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Test au montage du composant
  useEffect(() => {
    testApiConnection();
  }, []);

  // Réinitialiser les tests
  const resetTests = () => {
    setTestResults([]);
    setApiStatus('unknown');
    setDoctors([]);
    console.clear();
  };

  return (
    <Container size="xl" className="py-6">
      <Stack gap="md">
        {/* Header */}
        <Paper shadow="sm" p="md" radius="md" withBorder>
          <Group justify="space-between" align="center">
            <div>
              <Text size="xl" fw={700}>🔗 Test Intégration Backend - Page de Création</Text>
              <Text size="sm" c="dimmed">
                Validation de l'intégration backend de la page frontend/dental_medicine/src/app/(dashboard)/patients/new
              </Text>
            </div>
            <Group gap="xs">
              <Badge 
                color={apiStatus === 'connected' ? 'green' : apiStatus === 'error' ? 'red' : 'gray'} 
                size="lg"
              >
                <IconDatabase size={14} style={{ marginRight: 4 }} />
                {apiStatus === 'connected' ? 'Connecté' : apiStatus === 'error' ? 'Erreur' : 'Inconnu'}
              </Badge>
            </Group>
          </Group>
        </Paper>

        {/* Statut de l'intégration */}
        <Paper shadow="sm" p="md" radius="md" withBorder>
          <Tabs defaultValue="integration-status">
            <Tabs.List>
              <Tabs.Tab value="integration-status" leftSection={<IconBrowserCheck size={16} />}>
                Statut Intégration
              </Tabs.Tab>
              <Tabs.Tab value="doctors" leftSection={<IconUser size={16} />}>
                Médecins ({doctors.length})
              </Tabs.Tab>
              <Tabs.Tab value="tests" leftSection={<IconTestPipe size={16} />}>
                Tests ({testResults.length})
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="integration-status" pt="md">
              <Stack gap="md">
                <Title order={4}>✅ Intégrations Réalisées</Title>
                
                <Grid>
                  <Grid.Col span={6}>
                    <Card withBorder p="sm" radius="md">
                      <Group gap="sm" mb="xs">
                        <IconApi size={16} color="green" />
                        <Text fw={500} size="sm">Service Backend</Text>
                      </Group>
                      <Text size="xs" c="dimmed">completePatientService intégré</Text>
                      <Badge color="green" size="sm" mt="xs">✅ Actif</Badge>
                    </Card>
                  </Grid.Col>
                  
                  <Grid.Col span={6}>
                    <Card withBorder p="sm" radius="md">
                      <Group gap="sm" mb="xs">
                        <IconUser size={16} color="blue" />
                        <Text fw={500} size="sm">Médecins Dynamiques</Text>
                      </Group>
                      <Text size="xs" c="dimmed">Chargement depuis backend</Text>
                      <Badge color="blue" size="sm" mt="xs">✅ Intégré</Badge>
                    </Card>
                  </Grid.Col>
                  
                  <Grid.Col span={6}>
                    <Card withBorder p="sm" radius="md">
                      <Group gap="sm" mb="xs">
                        <IconCheck size={16} color="orange" />
                        <Text fw={500} size="sm">Validation Avancée</Text>
                      </Group>
                      <Text size="xs" c="dimmed">Multi-niveaux avec feedback</Text>
                      <Badge color="orange" size="sm" mt="xs">✅ Implémenté</Badge>
                    </Card>
                  </Grid.Col>
                  
                  <Grid.Col span={6}>
                    <Card withBorder p="sm" radius="md">
                      <Group gap="sm" mb="xs">
                        <IconDatabase size={16} color="red" />
                        <Text fw={500} size="sm">Création Transactionnelle</Text>
                      </Group>
                      <Text size="xs" c="dimmed">Patient + profil + rendez-vous</Text>
                      <Badge color="red" size="sm" mt="xs">✅ Fonctionnel</Badge>
                    </Card>
                  </Grid.Col>
                </Grid>
                
                <Alert color="green" title="✅ Intégration Backend Complète">
                  <Text size="sm">
                    La page de création de patient est entièrement intégrée au backend avec :
                  </Text>
                  <List size="sm" mt="xs">
                    <List.Item>Chargement dynamique des médecins</List.Item>
                    <List.Item>Validation avancée des données</List.Item>
                    <List.Item>Création transactionnelle complète</List.Item>
                    <List.Item>Gestion d'erreur robuste</List.Item>
                  </List>
                </Alert>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="doctors" pt="md">
              <Stack gap="sm">
                <Group justify="space-between">
                  <Text fw={500}>Médecins chargés depuis le backend</Text>
                  <Button 
                    variant="light"
                    leftSection={<IconRefresh size={16} />}
                    onClick={testApiConnection}
                    loading={loading}
                    size="sm"
                  >
                    Recharger
                  </Button>
                </Group>
                
                {doctors.length > 0 ? (
                  doctors.map((doctor, index) => (
                    <Card key={index} withBorder p="sm">
                      <Group justify="space-between">
                        <div>
                          <Text fw={500}>
                            {doctor.first_name} {doctor.last_name}
                          </Text>
                          <Text size="sm" c="dimmed">
                            {doctor.specialization} {doctor.is_assistant ? '(Assistant)' : ''}
                          </Text>
                        </div>
                        <Badge color={doctor.is_assistant ? 'blue' : 'green'}>
                          ID: {doctor.id}
                        </Badge>
                      </Group>
                    </Card>
                  ))
                ) : (
                  <Text c="dimmed">Aucun médecin chargé</Text>
                )}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="tests" pt="md">
              <Stack gap="sm">
                {testResults.length > 0 ? (
                  testResults.map((result, index) => (
                    <Alert 
                      key={result.id} 
                      color={result.status === 'success' ? 'green' : 'red'} 
                      title={`${result.type} - ${result.status}`}
                    >
                      <Text size="sm" mb="xs">
                        <strong>Message :</strong> {result.message}
                      </Text>
                      <Text size="sm" mb="xs">
                        <strong>Timestamp :</strong> {new Date(result.timestamp).toLocaleString()}
                      </Text>
                      {result.data && (
                        <JsonInput
                          label="Données"
                          value={JSON.stringify(result.data, null, 2)}
                          readOnly
                          minRows={3}
                          maxRows={6}
                        />
                      )}
                    </Alert>
                  ))
                ) : (
                  <Text c="dimmed">Aucun test exécuté</Text>
                )}
              </Stack>
            </Tabs.Panel>
          </Tabs>
        </Paper>

        {/* Tests Backend */}
        <Paper shadow="sm" p="md" radius="md" withBorder>
          <Text size="lg" fw={600} mb="md">🧪 Tests d'Intégration Backend</Text>
          
          <Stack gap="sm">
            <Group justify="space-between" align="center">
              <div>
                <Text fw={500}>Test Connexion API</Text>
                <Text size="sm" c="dimmed">
                  Tester la connexion au backend et récupérer les médecins
                </Text>
              </div>
              <Button 
                leftSection={<IconApi size={16} />}
                onClick={testApiConnection}
                loading={loading}
                variant="light"
              >
                Tester Connexion
              </Button>
            </Group>

            <Group justify="space-between" align="center">
              <div>
                <Text fw={500}>Test Validation Données</Text>
                <Text size="sm" c="dimmed">
                  Tester la validation des données patient
                </Text>
              </div>
              <Button 
                leftSection={<IconCheck size={16} />}
                onClick={testDataValidation}
                variant="light"
                color="orange"
              >
                Tester Validation
              </Button>
            </Group>

            <Group justify="space-between" align="center">
              <div>
                <Text fw={500}>Test Création Patient</Text>
                <Text size="sm" c="dimmed">
                  Créer un patient de test via l'API backend
                </Text>
              </div>
              <Button 
                leftSection={<IconPlus size={16} />}
                onClick={createTestPatient}
                loading={loading}
                disabled={apiStatus !== 'connected'}
                color="green"
              >
                Créer Patient Test
              </Button>
            </Group>
          </Stack>
        </Paper>

        {/* Actions */}
        <Paper shadow="sm" p="md" radius="md" withBorder>
          <Group justify="space-between" align="center">
            <div>
              <Text fw={500}>Actions de Test</Text>
              <Text size="sm" c="dimmed">
                Gérer les tests et résultats
              </Text>
            </div>
            <Group gap="sm">
              <Button 
                variant="light"
                leftSection={<IconRefresh size={16} />}
                onClick={resetTests}
              >
                Réinitialiser
              </Button>
              <Button 
                component="a"
                href="/patients/new"
                target="_blank"
                leftSection={<IconBrowserCheck size={16} />}
                color="blue"
              >
                Ouvrir Page
              </Button>
            </Group>
          </Group>
        </Paper>

        {/* Instructions */}
        <Paper shadow="sm" p="md" radius="md" withBorder>
          <Text size="lg" fw={600} mb="md">📋 Instructions de Test</Text>
          
          <List spacing="sm">
            <List.Item>
              <strong>1. Tester la connexion :</strong> Vérifiez que l'API backend répond
            </List.Item>
            <List.Item>
              <strong>2. Examiner les médecins :</strong> Consultez la liste des médecins chargés
            </List.Item>
            <List.Item>
              <strong>3. Tester la validation :</strong> Validez les règles métier
            </List.Item>
            <List.Item>
              <strong>4. Créer un patient :</strong> Testez la création complète
            </List.Item>
            <List.Item>
              <strong>5. Ouvrir la page :</strong> Testez l'interface utilisateur réelle
            </List.Item>
          </List>
        </Paper>
      </Stack>
    </Container>
  );
};

export default TestBackendIntegration;
