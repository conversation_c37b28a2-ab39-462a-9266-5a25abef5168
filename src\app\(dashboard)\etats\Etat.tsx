import React from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import RapportDesRendezVous from './Rapport_des_rendez_vous'
import EcheanceDesCheques from './Echeance_des_chequeq'
import EcheanceDesChequesEspece from './Echeance_des_cheques_espece'
import EtatDeRelance from './Etat_de_relance'
import EtatsDuPatient from './Etats_du_patient'
import MedecinTraitants from"./Medecin_traitants"
import NouveauPatients from"./Nouveau_patients"
const Etat = () => {
  return (
    <div>
<Tabs variant="outline" radius="md" orientation="vertical" defaultValue="Rapport_des_rendez_vous" w={"100%"} mt={10}>
      <Tabs.List>
        <Tabs.Tab value="Rapport_des_rendez_vous" leftSection={<IconPhoto size={12} />}>
          Rapport des rendez vous
        </Tabs.Tab>
        <Tabs.Tab value="Echeance_des_chequeq" leftSection={<IconMessageCircle size={12} />}>
          Echeance des chequeq
        </Tabs.Tab>
        <Tabs.Tab value="Echeance_des_cheques_espece" leftSection={<IconSettings size={12} />}>
          Echeance des cheques espece
        </Tabs.Tab>
        <Tabs.Tab value="EtatDeRelance" leftSection={<IconSettings size={12} />}>
         Etat De Relance
        </Tabs.Tab>
        <Tabs.Tab value="EtatsDuPatient" leftSection={<IconSettings size={12} />}>
         Etats Du Patient
        </Tabs.Tab>
        <Tabs.Tab value="MedecinTraitants" leftSection={<IconSettings size={12} />}>
         Medecin Traitants
        </Tabs.Tab>
        <Tabs.Tab value="NouveauPatients" leftSection={<IconSettings size={12} />}>
         Nouveau Patients
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="Rapport_des_rendez_vous" ml={20}>
       <RapportDesRendezVous/>
      </Tabs.Panel>

      <Tabs.Panel value="Echeance_des_chequeq"ml={20} >
        <EcheanceDesCheques/>
      </Tabs.Panel>

      <Tabs.Panel value="Echeance_des_cheques_espece" ml={20}>
        <EcheanceDesChequesEspece/>
      </Tabs.Panel>
         <Tabs.Panel value="EtatDeRelance" ml={20}>
        <EtatDeRelance/>
      </Tabs.Panel>
         <Tabs.Panel value="EtatsDuPatient" ml={20}>
        <EtatsDuPatient/>
      </Tabs.Panel>
         <Tabs.Panel value="MedecinTraitants" ml={20}>
        <MedecinTraitants/>
      </Tabs.Panel>
         <Tabs.Panel value="NouveauPatients" ml={20}>
        <NouveauPatients/>
      </Tabs.Panel>
    </Tabs>
    </div>
  )
}

export default Etat
