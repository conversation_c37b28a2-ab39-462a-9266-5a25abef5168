
"use client";

import React, { useState, useCallback, useMemo, forwardRef, useImperativeHandle,  useEffect } from "react";
import { IconSquareRoundedPlusFilled, IconFileZip, IconEye, IconTrash } from '@tabler/icons-react';
import {  Divider,   Flex,Tooltip,Group,Avatar,Radio} from "@mantine/core";
import { DentalSvg } from '@/components/TDentalSvgMin';
import { Dantal, DantalB } from '@/data/13_ans_et_demi';
import Image from "next/image";
import dentaltop from "../../../../../public/dentaltop.jpg";
import dentalButtom from "../../../../../public/dentalButtom.jpg";
import { ColorTarget } from '@/types/dental';
import { SVGPathStyle } from '../../shared/types';
import { isAllowedPosition,isOnlyallowed} from '@/utils/dentalUtils';
import { CheckIcon } from '@mantine/core';
import { rem, <PERSON><PERSON>, Text,  Menu,   } from '@mantine/core';
import {  IconSettings,  } from '@tabler/icons-react';
import { EnhancedDentalSvg, ToothSelection } from '@/components/dental/EnhancedDentalSvg';
import { DentalSpecialtyTabProps, SaveManagerRef, ModificationState,  } from '../../shared/types';
import { useSaveManager } from '../../shared/SaveManager';
import { useEstimateReactive } from '@/hooks/useEstimateReactive';
export const Therapeutic13ansetdemi  = forwardRef<SaveManagerRef, DentalSpecialtyTabProps>(({
  onModificationChange,

}, ref) => {
   const [isHidingMode] = useState(false);
    const [currentColor, setCurrentColor] = useState<string>("");
    const [highlightedPaths, setHighlightedPaths] = useState<Record<string, SVGPathStyle>>({});
    const [isPathSelectionActive, setIsPathSelectionActive] = useState(false);
    // Using hiddenSvgIds as a read-only state since its setter is not used
    const [hiddenSvgIds] = useState<string[]>([]);
   const [checked, setChecked] = useState(false);
    // Using brokenRedStrokeSvgs as a read-only state since its setter is not used
    const [brokenRedStrokeSvgs] = useState<Set<string>>(new Set());
    // Using gradientEffectSvgs as a read-only state since its setter is not used
    const [gradientEffectSvgs] = useState<Set<string>>(new Set()); // For Dental
    const [gradientBottomEffectSvgs, setGradientBottomEffectSvgs] = useState<Set<string>>(new Set()); // For DentalB
    const [currentColorTarget, setCurrentColorTarget] = useState<ColorTarget>("fill");
    const [pendingPath19Toggle, setPendingPath19Toggle] = useState(false);
    const [clickedIds, setClickedIds] = useState<string[]>([]);
  // États locaux pour la thérapie
   const generatePathsToShowByDefault = () => {
    const svgIds = Array.from({ length: 32 }, (_, i) => `${i + 1}`);
    const pathIds = Array.from({ length: 16 }, (_, i) => `${i + 1}`);
    return svgIds.flatMap((svgId) => pathIds.map((pathId) => ({ svg_id: svgId, path_id: pathId })));
  };
  const pathsToShowByDefault = generatePathsToShowByDefault();

  // Fonction pour convertir svg_id vers numéro de dent dentaire standard
  const getToothNumber = useCallback((svgId: string) => {
    const id = parseInt(svgId);

    // Mapping selon la numérotation dentaire standard
    if (id >= 1 && id <= 8) {
      // upperRight (dents 1-8) → numéros dentaires 18-11 (de droite à gauche depuis le centre)
      return 18 - (id - 1);
    }
    if (id >= 9 && id <= 16) {
      // upperLeft (dents 9-16) → numéros dentaires 21-28
      return 20 + (id - 8);
    }
    if (id >= 17 && id <= 24) {
      // lowerLeft (dents 17-24) → numéros dentaires 38-31 (de gauche à droite depuis le centre)
      return 38 - (id - 17);
    }
    if (id >= 25 && id <= 32) {
      // lowerRight (dents 25-32) → numéros dentaires 41-48
      return 40 + (id - 24);
    }

    return id; // Fallback
  }, []);

  // Organiser les dents selon l'ordre d'Adulte.tsx : upperRight → upperLeft → lowerLeft → lowerRight
  const organizedTeeth = useMemo(() => {
    const allTeeth = [...Dantal, ...DantalB];

    // Fonction pour déterminer le quadrant d'une dent selon son svg_id
    const getQuadrant = (svgId: string) => {
      const id = parseInt(svgId);
      if (id >= 1 && id <= 8) return 'upper_right';      // Dents 1-8 (supérieur droit)
      if (id >= 9 && id <= 16) return 'upper_left';      // Dents 9-16 (supérieur gauche)
      if (id >= 17 && id <= 24) return 'lower_left';     // Dents 17-24 (inférieur gauche)
      if (id >= 25 && id <= 32) return 'lower_right';    // Dents 25-32 (inférieur droit)
      return 'unknown';
    };

    // Organiser par quadrant selon l'ordre d'Adulte.tsx
    const upperRight = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'upper_right').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));
    const upperLeft = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'upper_left').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));
    const lowerLeft = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'lower_left').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));
    const lowerRight = allTeeth.filter(tooth => getQuadrant(tooth.svg_id) === 'lower_right').sort((a, b) => parseInt(a.svg_id) - parseInt(b.svg_id));

    // Retourner dans l'ordre : upperRight → upperLeft → lowerLeft → lowerRight
    return [...upperRight, ...upperLeft, ...lowerLeft, ...lowerRight];
  }, []);

  const initialHiddenPaths = useMemo(() => {
    return organizedTeeth.reduce((acc, svgData) => {
      svgData.paths.forEach((path) => {
        const key = `${svgData.svg_id}-${path.id}`;
        const isVisibleByDefault = pathsToShowByDefault.some(
          (visiblePath) => visiblePath.svg_id === svgData.svg_id && visiblePath.path_id === path.id
        );
        // Hide path 20 by default
        if (path.id === "20") {
          acc[key] = true;
        } else {
          acc[key] = !isVisibleByDefault;
        }
      });
      return acc;
    }, {} as Record<string, boolean>);
  }, [pathsToShowByDefault, organizedTeeth]);
  const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>(initialHiddenPaths);


  const [activeButton, setActiveButton] = useState<string>('');
  const [targetPath, setTargetPath] = useState<string>('');

  // État pour contrôler le mode Enhanced (cases à cocher)
  const [isEnhancedMode, setIsEnhancedMode] = useState<boolean>(false);

  // État de modification pour cette spécialité
  const modificationState: ModificationState = {
    hiddenPaths,
    highlightedPaths,
    clickedIds,
    activeButton,
    targetPath
  };

  // Gestionnaire de sauvegarde
  const { save, hasChanges, SaveManagerComponent } = useSaveManager(
    modificationState,
    onModificationChange,
    'therapeutic'
  );
  const {
      session,
      isSaving,
      modifications,
      initializeSession,

    } = useEstimateReactive({
      patientId: 'patient-123', // TODO: Récupérer l'ID du patient actuel
      sessionName: 'Session Devis',
      autoSave: true,
      autoSaveDelay: 2000,
      autoInitialize: false, // NOUVEAU: Désactiver l'initialisation automatique
    });

    // Initialiser la session manuellement au montage du composant
    useEffect(() => {
      if (!session) {
        console.log('🔄 Initialisation de la session...');
        initializeSession('patient-123', 'Session Thérapeutique')
          .then(() => console.log('✅ Session initialisée avec succès'))
          .catch(error => console.error('❌ Erreur initialisation session:', error));
      } else {
        console.log('✅ Session déjà active:', session);
      }
    }, [session, initializeSession]);

    // Debug: Afficher l'état des boutons
    useEffect(() => {
      console.log('🔍 État des boutons:', {
        session: !!session,
        isSaving,
        modificationsCount: modifications.length,
        saveButtonDisabled: isSaving,
        resetButtonDisabled: isSaving
      });
    }, [session, isSaving, modifications.length]);
  const handleColorSelect = (color: string, target: "fill" | "stroke") => {
    console.log(`Color selected: ${color}, target: ${target}`);
    // Update the color states to enable color application
    setCurrentColor(color);
    setCurrentColorTarget(target);
    setIsPathSelectionActive(true);
  };

  // Gestionnaires pour le nouveau composant Enhanced
  const handleToothSelectionChange = useCallback((selections: ToothSelection[]) => {
    console.log('Tooth selections changed:', selections);
    // Ici on peut synchroniser avec l'état global ou sauvegarder
  }, []);

  const handlePathSelectionChange = useCallback((toothNumber: number, pathId: string, isSelected: boolean) => {
    console.log(`Path ${pathId} of tooth ${toothNumber} ${isSelected ? 'selected' : 'deselected'}`);
    // Synchroniser avec les états existants si nécessaire
  }, []);

  const handleTreatmentApply = useCallback((toothNumber: number, pathIds: string[], treatment: string, color: string) => {
    console.log(`Applying treatment ${treatment} with color ${color} to tooth ${toothNumber}, paths:`, pathIds);

    // Appliquer le traitement via l'API existante
    pathIds.forEach(pathId => {
      const svgId = toothNumber.toString();
      const key = `${svgId}-${pathId}`;

      setHighlightedPaths(prev => ({
        ...prev,
        [key]: {
          [currentColorTarget]: color
        }
      }));
    });

    // Sauvegarder via onModificationChange si disponible
    if (onModificationChange) {
      pathIds.forEach(pathId => {
        onModificationChange(toothNumber.toString(), pathId, false, highlightedPaths)
          .catch(error => console.error('Erreur sauvegarde traitement:', error));
      });
    }
  }, [currentColorTarget, highlightedPaths, onModificationChange]);
  const handleButtonClick = (buttonId: string) => {
    // Set the active button
    setActiveButton((prev) => (prev === buttonId ? '' : buttonId));
    // Set the target path based on the button clicked
    if (buttonId === "viewCleaning") {
      setTargetPath("17");
    } else if (buttonId === "viewFluoride") {
      setTargetPath("18");
    } else if (buttonId === "viewModeSealant") {
      setTargetPath("19");
    } else if (buttonId === "viewModeWhitening") {
      setActiveButton(''); // No target path for Whitening mode
    }
    if (buttonId === "RestoratinPermanent") {
      setTargetPath("RestoratinPermanent");
    }
    if (buttonId === "RestoratinTemporary") {
      setTargetPath("25");
    }
    if (buttonId === "RestoratinAmalgam") {
      setTargetPath("26");
    }
    if (buttonId === "RestoratinGlassIonomer") {
      setTargetPath("27");
    }
    if (buttonId === "PostCare") {
      setTargetPath("36");
    }
    if (buttonId === "Veneer") {
      setTargetPath("37");
    }
    if (buttonId === "Onlay") {
      setTargetPath("39");
    }
    if (buttonId === "CrownPermanent") {
      setTargetPath("41");
    }
    if (buttonId === "CrownTemporary" ) {
      setTargetPath("42");
    }
    if (buttonId === "CrownGold" ) {
      setTargetPath("44");
    }
    if (buttonId === "CrownZirconia") {
      setTargetPath("47");
    }
    if (buttonId === "Extraction") {
      setTargetPath("53");
    }
  };
  const onPathClick = (pathId: string, svgId: string) => {
     const positionKey = `${svgId}(${pathId})`;
           const key = `${svgId}-${pathId}`;

          if (currentColor === "#FF4444" && !isAllowedPosition(positionKey)) {
            return;
          }
          if (currentColor === "#8E1616" && !isOnlyallowed(positionKey)) {
           return;
         }
           if (pendingPath19Toggle && pathId === "19" && svgId === "1") {
             setHiddenPaths((prev) => ({
               ...prev,
               [`${svgId}-${pathId}`]: !prev[`${svgId}-${pathId}`], // Toggle hidden state
             }));
             setPendingPath19Toggle(false); // Reset pending state after applying
           }
     setHighlightedPaths((prev) => {
       const newHighlightedPaths = { ...prev };
       if (!newHighlightedPaths[key]) {
         newHighlightedPaths[key] = {};
       }
       if (currentColorTarget === "both") {
         if (newHighlightedPaths[key].fill && newHighlightedPaths[key].stroke) {
           delete newHighlightedPaths[key].fill;
           delete newHighlightedPaths[key].stroke;
         } else {
           newHighlightedPaths[key] = {
             fill: currentColor,
             stroke: "#2563EB"
           };
         }
       } else {
         if (newHighlightedPaths[key][currentColorTarget]) {
           delete newHighlightedPaths[key][currentColorTarget];
         } else {
           newHighlightedPaths[key][currentColorTarget] = currentColor;
         }
       }
       if (Object.keys(newHighlightedPaths[key]).length === 0) {
         delete newHighlightedPaths[key];
       }
       return newHighlightedPaths;
     });
     if (isPathSelectionActive) {
       setClickedIds((prevIds) => [...prevIds, pathId]);
     }
      // Toggle visibility of Path19
   };
  const handleSvgClick = useCallback(
     (svgId: string) => {
       const togglePathVisibility = (pathId: string, visible: boolean) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => {
           const newHiddenPaths = { ...prev, [key]: visible };

           // Sauvegarder la modification de manière réactive
           if (onModificationChange) {
             onModificationChange(svgId, pathId, !visible, highlightedPaths)
               .catch(error => console.error('Erreur sauvegarde:', error));
           }

           return newHiddenPaths;
         });
       };

       // Fonction pour appliquer les modifications à toutes les dents sélectionnées

       const handleWhiteningMode = () => {
         // Reset all paths to hidden first, then show only whitening paths
         const newHiddenPaths: Record<string, boolean> = {};

         // Hide all paths for this tooth (1-100)
         for (let i = 1; i <= 100; i++) {
           newHiddenPaths[`${svgId}-${i}`] = true;
         }

         // Show base tooth paths (8-16)
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           newHiddenPaths[`${svgId}-${pathId}`] = false;
         });

         // Show whitening paths (20-23)
         newHiddenPaths[`${svgId}-20`] = false;
         newHiddenPaths[`${svgId}-21`] = false;
         newHiddenPaths[`${svgId}-22`] = false;
         newHiddenPaths[`${svgId}-23`] = false;

         setHiddenPaths((prev) => ({ ...prev, ...newHiddenPaths }));

       };
       const handleCleaningMode = () => {
         // Reset all paths to hidden first, then show only cleaning paths
         const newHiddenPaths: Record<string, boolean> = {};

         // Hide all paths for this tooth (1-100)
         for (let i = 1; i <= 100; i++) {
           newHiddenPaths[`${svgId}-${i}`] = true;
         }

         // Show base tooth paths (8-16)
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           newHiddenPaths[`${svgId}-${pathId}`] = false;
         });

         // Show cleaning path (17)
         newHiddenPaths[`${svgId}-17`] = false;

         setHiddenPaths((prev) => ({ ...prev, ...newHiddenPaths }));

         // Sauvegarder la modification réactive pour le path 17 (cleaning)
         if (onModificationChange) {
           onModificationChange(svgId, '17', false, highlightedPaths)
             .catch(error => console.error('Erreur sauvegarde cleaning:', error));
         }
       };
       const handleFluorideMode = () => {
         if (DantalB.some((svgData) => svgData.svg_id === svgId)) {
           setGradientBottomEffectSvgs((prev) => {
             const newSet = new Set(prev);
             if (newSet.has(svgId)) {
               newSet.delete(svgId);
             } else {
               newSet.add(svgId);
             }
             return newSet;
           });
         }
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-17`]: true,
             [`${svgId}-18`]: !prev[`${svgId}-18`],
             [`${svgId}-19`]: true,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-24`]: true,
             [`${svgId}-25`]: true,
             [`${svgId}-26`]: true,
             [`${svgId}-27`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-29`]: true,
             [`${svgId}-30`]: true,
             [`${svgId}-31`]: true,
             [`${svgId}-32`]: true,
             [`${svgId}-33`]: true,
             [`${svgId}-34`]: true,
             [`${svgId}-35`]: true,
             [`${svgId}-36`]: true,
             [`${svgId}-37`]: true,
             [`${svgId}-38`]: true,
             [`${svgId}-39`]: true,
             [`${svgId}-40`]: true,
             [`${svgId}-41`]: true,
             [`${svgId}-42`]: true,
             [`${svgId}-43`]: true,
             [`${svgId}-44`]: true,
             [`${svgId}-45`]: true,
             [`${svgId}-46`]: true,
             [`${svgId}-47`]: true,
             [`${svgId}-48`]: true,
             [`${svgId}-49`]: true,
             [`${svgId}-50`]: true,
             [`${svgId}-51`]: true,
             [`${svgId}-52`]: true,
             [`${svgId}-53`]: true,
             [`${svgId}-54`]: true,
             [`${svgId}-55`]: true,
             [`${svgId}-56`]: true,
             [`${svgId}-57`]: true,
             [`${svgId}-58`]: true,
             [`${svgId}-59`]: true,
             [`${svgId}-60`]: true,
             [`${svgId}-61`]: true,
             [`${svgId}-62`]: true,
             [`${svgId}-63`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-65`]: true,
             [`${svgId}-67`]: true,
             [`${svgId}-68`]: true,
             [`${svgId}-69`]: true,

           }));
         });

       };
       const handleSealantMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-17`]: true,
             [`${svgId}-18`]: true,
             [`${svgId}-19`]: !prev[`${svgId}-19`],
             [`${svgId}-20`]: true,
             [`${svgId}-21`]: true,
             [`${svgId}-22`]: true,
             [`${svgId}-23`]: true,
             [`${svgId}-24`]: true,
             [`${svgId}-25`]: true,
             [`${svgId}-26`]: true,
             [`${svgId}-27`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-28`]: true,
             [`${svgId}-29`]: true,
             [`${svgId}-30`]: true,
             [`${svgId}-31`]: true,
             [`${svgId}-32`]: true,
             [`${svgId}-33`]: true,
             [`${svgId}-34`]: true,
             [`${svgId}-35`]: true,
             [`${svgId}-36`]: true,
             [`${svgId}-37`]: true,
             [`${svgId}-38`]: true,
             [`${svgId}-39`]: true,
             [`${svgId}-40`]: true,
             [`${svgId}-41`]: true,
             [`${svgId}-42`]: true,
             [`${svgId}-43`]: true,
             [`${svgId}-44`]: true,
             [`${svgId}-45`]: true,
             [`${svgId}-46`]: true,
             [`${svgId}-47`]: true,
             [`${svgId}-48`]: true,
             [`${svgId}-49`]: true,
             [`${svgId}-50`]: true,
             [`${svgId}-51`]: true,
             [`${svgId}-52`]: true,
             [`${svgId}-53`]: true,
             [`${svgId}-54`]: true,
             [`${svgId}-55`]: true,
             [`${svgId}-56`]: true,
             [`${svgId}-57`]: true,
             [`${svgId}-58`]: true,
             [`${svgId}-59`]: true,
             [`${svgId}-60`]: true,
             [`${svgId}-61`]: true,
             [`${svgId}-62`]: true,
             [`${svgId}-63`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-64`]: true,
             [`${svgId}-65`]: true,
             [`${svgId}-67`]: true,
             [`${svgId}-68`]: true,
             [`${svgId}-69`]: true,
           }));
         });

       };
       const handleTargetPath = () => {
         if (targetPath) {
           togglePathVisibility(targetPath, !hiddenPaths[`${svgId}-${targetPath}`]);
         }
       };
       const handleRestoratinPermanentMode = () => {
         // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
    [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths((prev) => ({ ...prev,
        [key]: false,
        [`${svgId}-20`]: true,
        [`${svgId}-21`]: true,
        [`${svgId}-22`]: true,
        [`${svgId}-23`]: true,
        [`${svgId}-17`]: true,
        [`${svgId}-18`]: true,
        [`${svgId}-19`]: true,
        [`${svgId}-24`]: true,
        [`${svgId}-25`]: true,
        [`${svgId}-26`]: true,
        [`${svgId}-27`]: true,
        [`${svgId}-28`]: true,
        [`${svgId}-28`]: true,
        [`${svgId}-30`]: true,
        [`${svgId}-31`]: true,
        [`${svgId}-32`]: true,
        [`${svgId}-33`]: true,
        [`${svgId}-34`]: true,
        [`${svgId}-35`]: true,
        [`${svgId}-36`]: true,
        [`${svgId}-37`]: true,
        [`${svgId}-41`]: true,
        [`${svgId}-42`]: true,
        [`${svgId}-43`]: true,
        [`${svgId}-44`]: true,
        [`${svgId}-49`]: true,
        [`${svgId}-50`]: true,
        [`${svgId}-51`]: true,
        [`${svgId}-52`]: true,
        [`${svgId}-53`]: true,
        [`${svgId}-54`]: true,
        [`${svgId}-55`]: true,
        [`${svgId}-56`]: true,
        [`${svgId}-57`]: true,
        [`${svgId}-58`]: true,
        [`${svgId}-59`]: true,
        [`${svgId}-60`]: true,
        [`${svgId}-61`]: true,
        [`${svgId}-62`]: true,
        [`${svgId}-63`]: true,
        [`${svgId}-64`]: true,
        [`${svgId}-64`]: true,
        [`${svgId}-65`]: true,
        [`${svgId}-67`]: true,
        [`${svgId}-68`]: true,
        [`${svgId}-69`]: true,
      }));
    });
       };
       const handleTemporaryMode = () => {
            // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
       [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: !prev[`${svgId}-24`],
           [`${svgId}-25`]: !prev[`${svgId}-25`],
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
         }));
       });
       };
       const handleAmalgamMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
            [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: !prev[`${svgId}-26`],
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           }));
         });
       };
       const handleGlassIonomerMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
            [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: !prev[`${svgId}-27`],
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });
       };
       const handleRootPermanentMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
               [key]: false,
               [`${svgId}-20`]: true,
               [`${svgId}-21`]: true,
               [`${svgId}-22`]: true,
               [`${svgId}-23`]: true,
               [`${svgId}-17`]: true,
               [`${svgId}-18`]: true,
               [`${svgId}-19`]: true,
               [`${svgId}-24`]: true,
               [`${svgId}-25`]: true,
               [`${svgId}-26`]: true,
               [`${svgId}-27`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-29`]: true,
               [`${svgId}-30`]: true,
               [`${svgId}-31`]: true,
               [`${svgId}-32`]: true,
               [`${svgId}-33`]: true,
               [`${svgId}-34`]: true,
               [`${svgId}-35`]: true,
               [`${svgId}-36`]: true,
               [`${svgId}-37`]: true,
               [`${svgId}-38`]: true,
               [`${svgId}-39`]: true,
               [`${svgId}-40`]: true,
               [`${svgId}-41`]: true,
               [`${svgId}-42`]: true,
               [`${svgId}-43`]: true,
               [`${svgId}-44`]: true,
               [`${svgId}-45`]: true,
               [`${svgId}-46`]: true,
               [`${svgId}-47`]: true,
               [`${svgId}-48`]: true,
               [`${svgId}-49`]: true,
               [`${svgId}-50`]: true,
               [`${svgId}-51`]: true,
               [`${svgId}-52`]: true,
               [`${svgId}-53`]: true,
               [`${svgId}-54`]: true,
               [`${svgId}-55`]: true,
               [`${svgId}-56`]: true,
               [`${svgId}-57`]: true,
               [`${svgId}-58`]: true,
               [`${svgId}-59`]: true,
               [`${svgId}-60`]: true,
               [`${svgId}-61`]: true,
               [`${svgId}-62`]: true,
               [`${svgId}-63`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-65`]: true,
               [`${svgId}-67`]: true,
               [`${svgId}-68`]: true,
               [`${svgId}-69`]: true,
           }));
         });
       };
       const handleRootTemporaryMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: !prev[`${svgId}-28`],
           [`${svgId}-29`]: !prev[`${svgId}-29`],
           [`${svgId}-30`]: true,

           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

           }));
         });

       };
       const handleRootCalciumMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: !prev[`${svgId}-30`],
           [`${svgId}-31`]: !prev[`${svgId}-31`],
           [`${svgId}-32`]: !prev[`${svgId}-32`],
           [`${svgId}-33`]: !prev[`${svgId}-33`],
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleRootGuttaPerchaMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: !prev[`${svgId}-34`],
           [`${svgId}-35`]: !prev[`${svgId}-35`],
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

           }));
         });

       };
       const handlePostCareMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
             [key]: false,
             [`${svgId}-20`]: true,
               [`${svgId}-21`]: true,
               [`${svgId}-22`]: true,
               [`${svgId}-23`]: true,
               [`${svgId}-17`]: true,
               [`${svgId}-18`]: true,
               [`${svgId}-19`]: true,
               [`${svgId}-24`]: true,
               [`${svgId}-25`]: true,
               [`${svgId}-26`]: true,
               [`${svgId}-27`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-28`]: true,
               [`${svgId}-30`]: true,
               [`${svgId}-31`]: true,
               [`${svgId}-32`]: true,
               [`${svgId}-33`]: true,
               [`${svgId}-34`]: true,
               [`${svgId}-35`]: true,
               [`${svgId}-36`]: !prev[`${svgId}-36`],
               [`${svgId}-37`]: true,
               [`${svgId}-38`]: true,
               [`${svgId}-39`]: true,
               [`${svgId}-40`]: true,
               [`${svgId}-41`]: true,
               [`${svgId}-42`]: true,
               [`${svgId}-43`]: true,
               [`${svgId}-44`]: true,
               [`${svgId}-45`]: true,
               [`${svgId}-46`]: true,
               [`${svgId}-47`]: true,
               [`${svgId}-48`]: true,
               [`${svgId}-49`]: true,
               [`${svgId}-50`]: true,
               [`${svgId}-51`]: true,
               [`${svgId}-52`]: true,
               [`${svgId}-53`]: true,
               [`${svgId}-54`]: true,
               [`${svgId}-55`]: true,
               [`${svgId}-56`]: true,
               [`${svgId}-57`]: true,
               [`${svgId}-58`]: true,
               [`${svgId}-59`]: true,
               [`${svgId}-60`]: true,
               [`${svgId}-61`]: true,
               [`${svgId}-62`]: true,
               [`${svgId}-63`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-64`]: true,
               [`${svgId}-65`]: true,
               [`${svgId}-67`]: true,
               [`${svgId}-68`]: true,
               [`${svgId}-69`]: true,
           }));
         });

       };
       const handleVeneerMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: !prev[`${svgId}-37`],
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleCrownPermanentMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: true,
           [key]: !prev[key] ,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: !prev[`${svgId}-41`],
           [`${svgId}-41`]: !prev[`${svgId}-41`],

           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,
           }));
         });

       };
       const handleCrownTemporaryMode = () => {
       // Toggle visibility of paths 8, 9, 10, 11, 12, 13, 14, 15, and 16
       [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
         const key = `${svgId}-${pathId}`;
         setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: !prev[`${svgId}-24`],
           [`${svgId}-25`]: !prev[`${svgId}-25`],
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

         }));
       });

       };
       const handleCrownGoldMode = () => {
         setHiddenPaths((prev) => ({
           ...prev,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-29`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: !prev[`${svgId}-43`],
           [`${svgId}-44`]: !prev[`${svgId}-44`],
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: true,
           [`${svgId}-52`]: true,
           [`${svgId}-53`]: true,
           [`${svgId}-54`]: true,
           [`${svgId}-55`]: true,
           [`${svgId}-56`]: true,
           [`${svgId}-57`]: true,
           [`${svgId}-58`]: true,
           [`${svgId}-59`]: true,
           [`${svgId}-60`]: true,
           [`${svgId}-61`]: true,
           [`${svgId}-62`]: true,
           [`${svgId}-63`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-64`]: true,
           [`${svgId}-65`]: true,
           [`${svgId}-67`]: true,
           [`${svgId}-68`]: true,
           [`${svgId}-69`]: true,

         }));
       };
       const handleOnlayMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = false;
           updatedPaths[`${svgId}-2`] = false;
           updatedPaths[`${svgId}-3`] = false;
           updatedPaths[`${svgId}-4`] = false;
           updatedPaths[`${svgId}-5`] = false;
           updatedPaths[`${svgId}-6`] = false;
           updatedPaths[`${svgId}-7`] = false;
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  !prev[`${svgId}-38`];
           updatedPaths[`${svgId}-39`] =  !prev[`${svgId}-39`];
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleCrownZirconiaMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] = !prev[`${svgId}-45`];
           updatedPaths[`${svgId}-46`]= !prev[`${svgId}-46`];
           updatedPaths[`${svgId}-47`]= !prev[`${svgId}-47`];
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });
       };
       const handleDentureMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = false;
           updatedPaths[`${svgId}-2`] = false;
           updatedPaths[`${svgId}-3`] = false;
           updatedPaths[`${svgId}-4`] = false;
           updatedPaths[`${svgId}-5`] = false;
           updatedPaths[`${svgId}-6`] = false;
           updatedPaths[`${svgId}-7`] = false;
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  !prev[`${svgId}-48`];
           updatedPaths[`${svgId}-49`] =  !prev[`${svgId}-49`];
           updatedPaths[`${svgId}-50`] =  !prev[`${svgId}-50`];
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleBridgeMode = () => {
         [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
           const key = `${svgId}-${pathId}`;
           setHiddenPaths((prev) => ({ ...prev,
           [key]: false,
           [`${svgId}-20`]: true,
           [`${svgId}-21`]: true,
           [`${svgId}-22`]: true,
           [`${svgId}-23`]: true,
           [`${svgId}-17`]: true,
           [`${svgId}-18`]: true,
           [`${svgId}-19`]: true,
           [`${svgId}-24`]: true,
           [`${svgId}-25`]: true,
           [`${svgId}-26`]: true,
           [`${svgId}-27`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-28`]: true,
           [`${svgId}-30`]: true,
           [`${svgId}-31`]: true,
           [`${svgId}-32`]: true,
           [`${svgId}-33`]: true,
           [`${svgId}-34`]: true,
           [`${svgId}-35`]: true,
           [`${svgId}-36`]: true,
           [`${svgId}-37`]: true,
           [`${svgId}-38`]: true,
           [`${svgId}-39`]: true,
           [`${svgId}-40`]: true,
           [`${svgId}-41`]: true,
           [`${svgId}-42`]: true,
           [`${svgId}-43`]: true,
           [`${svgId}-44`]: true,
           [`${svgId}-45`]: true,
           [`${svgId}-46`]: true,
           [`${svgId}-47`]: true,
           [`${svgId}-48`]: true,
           [`${svgId}-49`]: true,
           [`${svgId}-50`]: true,
           [`${svgId}-51`]: !prev[`${svgId}-51`],
           [`${svgId}-52`]: !prev[`${svgId}-52`],

           }));
         });

       };
       const handleExtractionMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel

           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] = true;
           updatedPaths[`${svgId}-46`]= true;
           updatedPaths[`${svgId}-47`]= true;
           updatedPaths[`${svgId}-48`]= true;
           updatedPaths[`${svgId}-49`]= true;
           updatedPaths[`${svgId}-50`]= true;
           updatedPaths[`${svgId}-51`]= true;
           updatedPaths[`${svgId}-52`]= true;
           updatedPaths[`${svgId}-53`]= true;
           updatedPaths[`${svgId}-54`]= true;
           updatedPaths[`${svgId}-55`]= true;
           updatedPaths[`${svgId}-56`]= true;
           updatedPaths[`${svgId}-57`]= true;
           updatedPaths[`${svgId}-58`]= true;
           updatedPaths[`${svgId}-59`]= true;
           updatedPaths[`${svgId}-60`]= true;
           updatedPaths[`${svgId}-61`]= true;
           updatedPaths[`${svgId}-62`]= true;
           updatedPaths[`${svgId}-63`]= true;
           updatedPaths[`${svgId}-64`]= true;
           updatedPaths[`${svgId}-65`]= true;
           updatedPaths[`${svgId}-66`]= true;
           updatedPaths[`${svgId}-67`]= true;
           updatedPaths[`${svgId}-68`]= true;
           updatedPaths[`${svgId}-69`]= true;
           return  updatedPaths
         });
       };
       const handleImplantMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  !prev[`${svgId}-54`];
           updatedPaths[`${svgId}-55`] =  !prev[`${svgId}-55`];
           updatedPaths[`${svgId}-56`] =  !prev[`${svgId}-56`];
           updatedPaths[`${svgId}-57`] =  !prev[`${svgId}-57`];
           updatedPaths[`${svgId}-58`] =  !prev[`${svgId}-58`];
           updatedPaths[`${svgId}-59`] =  !prev[`${svgId}-59`];
           updatedPaths[`${svgId}-60`] =  !prev[`${svgId}-60`];
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleBoneMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  !prev[`${svgId}-61`];
           updatedPaths[`${svgId}-62`] =  !prev[`${svgId}-62`];
           updatedPaths[`${svgId}-63`] =  !prev[`${svgId}-63`];
           updatedPaths[`${svgId}-64`] =  !prev[`${svgId}-64`];
           updatedPaths[`${svgId}-65`] =  !prev[`${svgId}-65`];
           updatedPaths[`${svgId}-66`] =  !prev[`${svgId}-66`];
           updatedPaths[`${svgId}-67`] =  !prev[`${svgId}-67`];
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });
       };
       const handleResectionMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths

           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  !prev[`${svgId}-68`];
           updatedPaths[`${svgId}-69`] =  true;
           return  updatedPaths
         });

       };
       const handleTeethCrownMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  true;
           updatedPaths[`${svgId}-55`] =  true;
           updatedPaths[`${svgId}-56`] =  true;
           updatedPaths[`${svgId}-57`] =  true;
           updatedPaths[`${svgId}-58`] =  true;
           updatedPaths[`${svgId}-59`] =  true;
           updatedPaths[`${svgId}-60`] =  true;
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  !prev[`${svgId}-69`];
           return  updatedPaths
         });

       };
       const handleImplantTeethCrownMode = () => {
         setHiddenPaths((prev) => {
           const updatedPaths = { ...prev };
           // Mise à jour des paths de 8 à 16
           [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
             const key = `${svgId}-${pathId}`;
             updatedPaths[key] = !prev[key]; // Bascule l'état actuel
           });
           // Mise à jour des autres paths
           updatedPaths[`${svgId}-1`] = !prev[`${svgId}-1`];
           updatedPaths[`${svgId}-2`] = !prev[`${svgId}-2`];
           updatedPaths[`${svgId}-3`] = !prev[`${svgId}-3`];
           updatedPaths[`${svgId}-4`] = !prev[`${svgId}-4`];
           updatedPaths[`${svgId}-5`] = !prev[`${svgId}-5`];
           updatedPaths[`${svgId}-6`] = !prev[`${svgId}-6`];
           updatedPaths[`${svgId}-7`] = !prev[`${svgId}-7`];
           updatedPaths[`${svgId}-17`] =  true;
           updatedPaths[`${svgId}-18`] =  true;
           updatedPaths[`${svgId}-19`] =  true;
           updatedPaths[`${svgId}-20`] =  true;
           updatedPaths[`${svgId}-21`] =  true;
           updatedPaths[`${svgId}-22`] =  true;
           updatedPaths[`${svgId}-23`] =  true;
           updatedPaths[`${svgId}-24`] =  true;
           updatedPaths[`${svgId}-25`] =  true;
           updatedPaths[`${svgId}-26`] =  true;
           updatedPaths[`${svgId}-27`] =  true;
           updatedPaths[`${svgId}-28`] =  true;
           updatedPaths[`${svgId}-29`] =  true;
           updatedPaths[`${svgId}-30`] =  true;
           updatedPaths[`${svgId}-31`] =  true;
           updatedPaths[`${svgId}-32`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-34`] =  true;
           updatedPaths[`${svgId}-35`] =  true;
           updatedPaths[`${svgId}-33`] =  true;
           updatedPaths[`${svgId}-37`] =  true;
           updatedPaths[`${svgId}-38`] =  true;
           updatedPaths[`${svgId}-39`] =  true;
           updatedPaths[`${svgId}-40`] =  true;
           updatedPaths[`${svgId}-41`] =  true;
           updatedPaths[`${svgId}-42`] =  true;
           updatedPaths[`${svgId}-43`] =  true;
           updatedPaths[`${svgId}-44`] =  true;
           updatedPaths[`${svgId}-45`] =  true;
           updatedPaths[`${svgId}-46`] =  true;
           updatedPaths[`${svgId}-47`] =  true;
           updatedPaths[`${svgId}-48`] =  true;
           updatedPaths[`${svgId}-49`] =  true;
           updatedPaths[`${svgId}-50`] =  true;
           updatedPaths[`${svgId}-51`] =  true;
           updatedPaths[`${svgId}-52`] =  true;
           updatedPaths[`${svgId}-53`] =  true;
           updatedPaths[`${svgId}-54`] =  !prev[`${svgId}-54`];
           updatedPaths[`${svgId}-55`] =  !prev[`${svgId}-55`];
           updatedPaths[`${svgId}-56`] =  !prev[`${svgId}-56`];
           updatedPaths[`${svgId}-57`] =  !prev[`${svgId}-57`];
           updatedPaths[`${svgId}-58`] =  !prev[`${svgId}-58`];
           updatedPaths[`${svgId}-59`] =  !prev[`${svgId}-59`];
           updatedPaths[`${svgId}-60`] =  !prev[`${svgId}-60`];
           updatedPaths[`${svgId}-61`] =  true;
           updatedPaths[`${svgId}-62`] =  true;
           updatedPaths[`${svgId}-63`] =  true;
           updatedPaths[`${svgId}-64`] =  true;
           updatedPaths[`${svgId}-65`] =  true;
           updatedPaths[`${svgId}-66`] =  true;
           updatedPaths[`${svgId}-67`] =  true;
           updatedPaths[`${svgId}-68`] =  true;
           updatedPaths[`${svgId}-69`] =  !prev[`${svgId}-69`];
           return  updatedPaths
         });

       };

       switch (activeButton) {
         case "viewModeWhitening":
           handleWhiteningMode();
           break;
         case "viewCleaning":
           handleCleaningMode();
           break;
         case "viewFluoride":
           handleFluorideMode();
           break;
         case "viewModeSealant":
           handleSealantMode();
           break;
           case "RestoratinPermanent":
             handleRestoratinPermanentMode();
             break;
           case "RestoratinTemporary":
           handleTemporaryMode();
           break;
           case "RestoratinAmalgam":
           handleAmalgamMode();
           break;
           case "RestoratinGlassIonomer":
           handleGlassIonomerMode();
           break;
           case "RootPermanent":
           handleRootPermanentMode();
           break;
           case "RootTemporary":
           handleRootTemporaryMode();
           break;
           case "RootCalcium":
           handleRootCalciumMode();
           break;
           case "RootGuttaPercha":
           handleRootGuttaPerchaMode();
           break;
           case "PostCare":
             handlePostCareMode();
           break;
           case "Veneer":
             handleVeneerMode();
           break;
           case "Onlay":
             handleOnlayMode();
           break;
           case "CrownPermanent":
             handleCrownPermanentMode();
           break;
           case "CrownTemporary":
             handleCrownTemporaryMode();
           break;
           case "CrownGold":
             handleCrownGoldMode();
           break;
           case "CrownZirconia":
             handleCrownZirconiaMode();
           break;
           case "Denture":
             handleDentureMode();
           break;
           case "Bridge":
             handleBridgeMode();
           break;
           case "Extraction":
             handleExtractionMode();
           break;
           case "Implant":
             handleImplantMode();
           break;
           case "Bone":
             handleBoneMode();
           break;
           case "Resection":
             handleResectionMode();
           break;
           case "TeethCrown":
             handleTeethCrownMode();
           break;
           case "ImplantTeethCrown":
             handleImplantTeethCrownMode();
           break;
         default:
           handleTargetPath();
           break;
       }
     },
     [targetPath, activeButton, hiddenPaths, highlightedPaths, onModificationChange]
   );

  // Exposer les méthodes via ref
  useImperativeHandle(ref, () => ({
     triggerSave: async () => {
       await save();
     },
     hasUnsavedChanges: () => hasChanges()
   }), [save, hasChanges]);



  // Gestionnaire de changement de bouton




  return (
    <>
      {/* Composant de sauvegarde invisible */}
      {SaveManagerComponent}

      <div className="my-4">

          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px' }} align="center">
            <div style={{ position: 'absolute', right: 16 }}>
              <Group gap="md">
                {/* Bouton pour basculer le mode Enhanced */}
                <Button
                  variant={isEnhancedMode ? "filled" : "outline"}
                  color={isEnhancedMode ? "blue" : "gray"}
                  size="sm"
                  onClick={() => setIsEnhancedMode(!isEnhancedMode)}
                  leftSection={<IconSettings size={14} />}
                >
                  {isEnhancedMode ? "Mode Simple" : "Mode Avancé"}
                </Button>

                <Menu withinPortal position="bottom-end" shadow="sm">
                  <Menu.Target>
                    <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                      Procédures Thérapeutiques
                    </Button>
                  </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item>Détartrage</Menu.Item>
                  <Menu.Item>Obturation</Menu.Item>
                  <Menu.Item>Traitement de canal</Menu.Item>
                  <Menu.Item>Application fluorée</Menu.Item>
                  <Menu.Item>Scellement</Menu.Item>
                </Menu.Dropdown>
              </Menu>
              </Group>
            </div>

            {/* Contrôles spécifiques à la thérapie */}
             <Group justify="space-between" mb={4} gap="xl"  w={"70%"}>
                  {/* Menu "Tous les Traitements" */}
                  <Menu withinPortal position="bottom-end" shadow="sm">
                        <Menu.Target>
                          <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                            Tous les Traitements
                          </Button>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                            Télécharger zip
                          </Menu.Item>
                          <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                            Aperçu de tous
                          </Menu.Item>
                          <Menu.Item
                            leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                            color="red"
                          >
                            Supprimer tous
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                <div style={{ margin: '0 auto' }} className=" mb-2 flex flex-end p-2 sm:justify-start space-x-2 ">

                        <Menu shadow="md" width={210}  trigger="hover" openDelay={100} closeDelay={400}>
                                   <Tooltip
                                         label="Restoratin استعادة"
                                         withArrow
                                         className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                       >
                                   <Menu.Target>
                                         <Button
                                   styles={{
                                     root: {
                                       position: 'relative',
                                       color: 'white',
                                       height: '35px', // Adjust button height
                                       width: '35px',  // Adjust button width
                                       padding: 0,
                                       borderRadius: '0.5rem'
                                     },
                                   }}
                                 >
                                   {/* SVG in the middle */}
                                   <div
                                   style={{
                                     display: 'flex',
                                     justifyContent: 'center',
                                     alignItems: 'center',
                                     height: '100%',
                                     width: '100%',
                                   }}
                                 >

                                 <span  className={
                                   (  activeButton === "RestoratinPermanent" ||
                                     activeButton === 'RestoratinTemporary' ||
                                     activeButton === 'RestoratinAmalgam' ||
                                     activeButton === 'RestoratinGlassIonomer'||
                                       isHidingMode
                                   )
                                     ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                                     : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
                                 }
                                 onClick={() =>
                                   setChecked(!checked) // Toggle checked state
                                   }>


                                 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 45" x="0px" y="0px">
                                   <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="M19.4,12a6.26611,6.26611,0,0,0-2.83.91986,7.50773,7.50773,0,0,1-3.57,1.08,7.50773,7.50773,0,0,1-3.57-1.08A6.26611,6.26611,0,0,0,6.6,12C3.25,12,2,16.28955,2,18.99933a19.1719,19.1719,0,0,0,1.73,8.48926L7.13,33.488a.9904.9904,0,0,0,.78.51,1.02437,1.02437,0,0,0,.86005-.36l3.62-4.34961a.8045.8045,0,0,1,1.22,0L17.23,33.638a1.00772,1.00772,0,0,0,.77.36h.09a.9904.9904,0,0,0,.78-.51l3.41-6.01947A19.177,19.177,0,0,0,24,18.99933C24,16.28955,22.75,12,19.4,12Z"/>
                                   <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="M33,3H21a1.003,1.003,0,0,0-1,1H17.82A3.01033,3.01033,0,0,0,15,2H11A3.00879,3.00879,0,0,0,8,5V7A1.003,1.003,0,0,0,9,8h3v3a1,1,0,0,0,2,0V8h3a1.003,1.003,0,0,0,1-1V6h2a1.003,1.003,0,0,0,1,1h9V17a1.003,1.003,0,0,0,1,1h2a1.003,1.003,0,0,0,1-1V4A1.003,1.003,0,0,0,33,3Z"/>
                                   </svg>

                                         </span>
                                   </div>
                                   {/* "CL" in the bottom-right corner */}
                                   <span
                                      style={{
                                       position: 'absolute',
                                       bottom: '0px',
                                       right: '0px',
                                       fontSize: '8px',
                                       fontWeight: '800',
                                       backgroundColor: 'white',
                                       // borderRadius:'0.125rem' ,
                                       borderTopLeftRadius: '0.5rem' ,
                                       borderBottomRightRadius: '0.5rem' ,
                                       color:'#3799CE',
                                        padding:'3px  0px 1px 2px' ,
                                     }}
                                     className="h-[14px] w-[14px] "
                                   >
                                       Re
                                   </span>
                                 </Button>
                                   </Menu.Target>
                                   </Tooltip>
                                   <Menu.Dropdown>
                                   <Menu.Label>Restoratin</Menu.Label>
                                   <Menu.Item className={
                                     activeButton === 'RestoratinPermanent'
                                         ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                         : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                     }
                                     onClick={() => {
                                       handleButtonClick("RestoratinPermanent");
                                       // Always set the color when button is clicked
                                       handleColorSelect("#8E1616", "fill");
                                     }}
                                   >
                                     <Group  className="h-[28px] w-[186.4px] " >
                                     <Radio
                                         checked={activeButton === 'RestoratinPermanent' }
                                         onChange={(event) =>
                                         setChecked(event.currentTarget.checked)
                                         }
                                         icon={CheckIcon}
                                     />
                                     <div className="flex">
                                     <Avatar
                                       color="blue"
                                       radius="sm"
                                       style={{ width: "28px", height: "28px" }}
                                         px={0.5}
                                     >
                                         <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 640" x="0px" y="0px">
                                         <path style={{fill:"#3799CE"}} d="M479.9,25.19,472,17.3a19.2,19.2,0,0,0-27.11,0L304.61,157.6a13.29,13.29,0,0,0-14.21,3l-95.3,95.31a13.27,13.27,0,0,0-3,14.21l-15.87,15.86c-9.78,9.79-29.57,32-25,47.51L95.65,389.06a9.3,9.3,0,0,1-2.91,2L38.43,414.27a19.68,19.68,0,0,0-9.6,27.39l27.79,56.95.06,0a2,2,0,0,0,.52.84,3,3,0,0,0,5-3.09c-1.19-3.86-16.28-58-17.65-65.12a2,2,0,0,1,.87-.7l54.31-23.24a26.76,26.76,0,0,0,8.45-5.72l55.61-55.61a17.16,17.16,0,0,0,4.87.68c15,0,33.84-16.89,42.64-25.68l15.86-15.87a13.22,13.22,0,0,0,14.21-3l95.31-95.3a13.29,13.29,0,0,0,3-14.21L479.89,52.31A19.2,19.2,0,0,0,479.9,25.19ZM312.47,163.88,442.34,34l20.85,20.85L333.32,184.73ZM103.9,397.31a20.78,20.78,0,0,1-6.57,4.44L43,425a8,8,0,0,0-4.42,4.74c-.42,1.23-.61,1.79,8.67,35.58.1.35.2.7.29,1.05L34.2,439l-.06-.11a13.68,13.68,0,0,1,6.65-19.08L95.1,396.54a14.86,14.86,0,0,0,4.79-3.24L154.23,339c.32.39.67.78,1,1.15l1.81,1.81c.37.38.76.72,1.15,1.05Zm100.29-83.45c-14.7,14.7-33.5,27.54-40,21l-1.81-1.81c-6.54-6.54,6.3-25.34,21-40l15.07-15.07,20.86,20.85ZM329.57,199.73,234.26,295a3.27,3.27,0,0,1-4.61,0l-27.48-27.48a3.27,3.27,0,0,1,0-4.61l95.3-95.3a3.26,3.26,0,0,1,4.61,0l27.49,27.49A3.27,3.27,0,0,1,329.57,199.73ZM472.82,45.23l-5.39,5.39L446.58,29.77,452,24.38a9.18,9.18,0,0,1,13,0l7.89,7.88A9.18,9.18,0,0,1,472.82,45.23Z"/>
                                         <path style={{fill:"#3799CE"}} d="M252,230.31a5,5,0,1,0-3.54-1.47A5,5,0,0,0,252,230.31Z"/>
                                         <path style={{fill:"#3799CE"}} d="M266.09,216.16a5,5,0,0,0,3.54-1.47,5,5,0,1,0-7.08,0A5,5,0,0,0,266.09,216.16Z"/>
                                         <path style={{fill:"#3799CE"}} d="M237.81,244.44a5,5,0,1,0-3.54-1.46A5,5,0,0,0,237.81,244.44Z"/>
                                         <path style={{fill:"#3799CE"}} d="M227.2,250.06a5,5,0,1,0,0,7.06A5,5,0,0,0,227.2,250.06Z"/>
                                         <path style={{fill:"#3799CE"}} d="M280.23,202a5,5,0,1,0-3.53-1.47A5,5,0,0,0,280.23,202Z"/>
                                         <path style={{fill:"#3799CE"}} d="M294.38,187.87a4.93,4.93,0,0,0,3.53-1.46,5,5,0,1,0-7.07,0A4.94,4.94,0,0,0,294.38,187.87Z"/>
                                         <path style={{fill:"#3799CE"}} d="M237.17,260a5,5,0,1,0,0,7.07A5,5,0,0,0,237.17,260Z"/>
                                         <path style={{fill:"#3799CE"}} d="M307.88,189.32a5,5,0,1,0,0,7.07A5,5,0,0,0,307.88,189.32Z"/>
                                         <path style={{fill:"#3799CE"}} d="M279.6,217.6a5,5,0,1,0,0,7.08A5,5,0,0,0,279.6,217.6Z"/>
                                         <path style={{fill:"#3799CE"}} d="M293.74,203.47a5,5,0,1,0,0,7.06A5,5,0,0,0,293.74,203.47Z"/>
                                         <path style={{fill:"#3799CE"}} d="M265.46,231.74a5,5,0,1,0-7.07,7.08,5,5,0,0,0,7.07,0A5,5,0,0,0,265.46,231.74Z"/>
                                         <path style={{fill:"#3799CE"}} d="M251.31,245.89a5,5,0,0,0-7.07,7.06,5,5,0,0,0,7.07-7.06Z"/>
                                         <path style={{fill:"#3799CE"}} d="M296.64,213.44a5,5,0,1,0,7.08,0A5,5,0,0,0,296.64,213.44Z"/>
                                         <path style={{fill:"#3799CE"}} d="M310.79,199.3a5,5,0,1,0,7.07,0A5,5,0,0,0,310.79,199.3Z"/>
                                         <path style={{fill:"#3799CE"}} d="M240.08,270a5,5,0,1,0,7.07,0A5,5,0,0,0,240.08,270Z"/>
                                         <path style={{fill:"#3799CE"}} d="M254.22,255.86a5,5,0,1,0,7.07,0A5,5,0,0,0,254.22,255.86Z"/>
                                         <path style={{fill:"#3799CE"}} d="M268.36,241.72a5,5,0,0,0,3.54,8.54,4.92,4.92,0,0,0,3.53-1.48,5,5,0,0,0-7.07-7.06Z"/>
                                         <path style={{fill:"#3799CE"}} d="M282.5,227.57a5,5,0,1,0,7.07,0A5,5,0,0,0,282.5,227.57Z"/>

                                         </svg>
                                       </Avatar>
                                       <Text fw={500} ml={6}> Permanent</Text>
                                         </div>
                                   </Group>
                                       </Menu.Item>
                                   <Menu.Item className={
                                     activeButton === 'RestoratinTemporary'
                                           ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                           : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                       }onClick={() => {
                                         handleButtonClick("RestoratinTemporary");
                                         setTargetPath("25");

                                       }}
                                     >
                                 <Group  className="h-[28px] w-[186.4px] " >
                                 <Radio
                                     checked={activeButton === 'RestoratinTemporary'}
                                     onChange={(event) =>
                                     setChecked(event.currentTarget.checked)
                                     }
                                     icon={CheckIcon}
                                 />
                                 <div className="flex">
                                 <Avatar
                                   color="blue"
                                   radius="sm"
                                   style={{ width: "28px", height: "28px" }}
                                    px={0.5}
                                 >
                                 <svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 0 60 40" x="0px" y="0px">
                                   <path style={{fill:"#3799CE"}} d="M9.27,41.15a4.47,4.47,0,0,0,7.61-2.75l.8-8.77a1.63,1.63,0,0,1,.52-1,1.6,1.6,0,0,1,2.68,1l.8,8.77A4.47,4.47,0,0,0,30.6,38v-.22a11.49,11.49,0,1,0,7.92-21.18,11.46,11.46,0,0,0-.15-1.74A11.34,11.34,0,0,0,19.28,8.77,11.26,11.26,0,0,0,9.51,5.68,11.31,11.31,0,0,0,4,25.49,10.83,10.83,0,0,1,8,33.69V38A4.42,4.42,0,0,0,9.27,41.15ZM46,27.9a9.5,9.5,0,1,1-9.5-9.5A9.51,9.51,0,0,1,46,27.9ZM5.33,24A9.32,9.32,0,1,1,18.51,10.89a1,1,0,0,0,.77.36h0a1,1,0,0,0,.77-.36,9.33,9.33,0,0,1,16.35,4.3,8.27,8.27,0,0,1,.12,1.21h0A11.49,11.49,0,0,0,28.6,36.26V38a2.46,2.46,0,0,1-4.13,1.82,2.45,2.45,0,0,1-.8-1.59l-.8-8.77a3.58,3.58,0,0,0-3.59-3.28,3.6,3.6,0,0,0-3.59,3.28l-.8,8.77a2.47,2.47,0,0,1-4.21,1.51A2.41,2.41,0,0,1,10,38v-4.3A12.8,12.8,0,0,0,5.33,24Z"/><path d="M27.29,12l-1.42-1.41a9.33,9.33,0,0,1-13.18,0L11.28,12a11.33,11.33,0,0,0,16,0Z"/>
                                   <path style={{fill:"#3799CE"}} d="M35.48,19.4v8.5a1,1,0,0,0,1,1H43.1v-2H37.48V19.4Z"/>
                                 </svg>
                                  </Avatar>
                                  <Text fw={500} ml={6}> Temporary</Text>
                                     </div>
                                 </Group>
                                   </Menu.Item>
                                   <Menu.Item className={
                                  activeButton === 'RestoratinAmalgam'
                                     ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                     : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                 }
                                   onClick={() => {
                                     handleButtonClick("RestoratinAmalgam");
                                     setTargetPath("26");

                                 }}
                               >
                                 <Group  className="h-[28px] w-[186.4px] " >
                                 <Radio
                                     checked={activeButton === 'RestoratinAmalgam'}
                                     onChange={(event) =>
                                     setChecked(event.currentTarget.checked)
                                     }
                                     icon={CheckIcon}
                                 />
                                 <div className="flex">
                                 <Avatar
                                   color="blue"
                                   radius="sm"
                                   style={{ width: "28px", height: "28px" }}
                                    px={0.5}
                                 >
                                 <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="30 0 250 270" >
                                 <path style={{fill:"#3799CE",stroke:"#3799CE"}} d="M203.5,76.9c-8.1-7.5-16.2-10.8-26.6-10.8c-8.3,0-12.5,1.7-17.8,3.8c-3.6,1.5-7.7,3.1-14.1,4.6c-8.9-3.3-18.1-6.4-27.7-5.5  c-11.8,1-23,8.5-28.5,19c-1.4,2.6-2.4,5.3-3.1,8.1c-0.2,0.4-0.3,0.8-0.3,1.2c-2.3,9.9-0.8,20.2,1.1,27.8c2.6,10.3,6.4,20.3,10.1,30  l1.8,4.6c0,0.1,4.3,12.4,5.4,32.3c0.4,8.1,3.1,18.4,6.7,26.1c4.8,10.3,11,15.7,17.8,15.7c0.2,0,0.4,0,0.6,0c2.6-0.1,4.8-1.2,6.5-3.3  c6-7.1,4.9-24.5,3.7-34.3c-0.4-3.1,0-6.1,1-8.6c1.6-3.9,4.1-6,7.7-6.4c0,0,1-0.1,2.5-0.1c1.5,0,2.7,0.2,2.8,0.2  c3.4,0.4,6,2.6,7.6,6.4c1.1,2.6,1.4,5.6,1,8.6c-1.2,9.7-2.3,27.1,3.7,34.3c1.7,2.1,3.9,3.2,6.5,3.3c3.7,0.1,7.4-1.3,10.7-4.4  c9.3-8.6,14.1-27.4,14.4-37.5c0.6-18.6,3.8-29.2,3.8-29.3c2.5-8.3,4.7-16.2,7-24.2c1.8-6.3,3.6-12.6,5.5-19.1  C218.4,102.8,214.8,87.4,203.5,76.9z M209.6,118.1c-1.9,6.5-3.7,12.9-5.5,19.1c-2.3,8-4.5,15.9-7,24.2c-0.1,0.5-3.3,11.2-4,30.4  c-0.3,9.7-5.1,27.1-13.1,34.6c-2.5,2.3-5.1,3.4-7.7,3.3c-1.4-0.1-2.5-0.6-3.5-1.8c-3.6-4.2-4.6-15.8-2.8-31c0.5-3.8,0-7.5-1.3-10.8  c-2.2-5.3-6-8.4-11-9c-0.1,0-1.5-0.2-3.3-0.2c-1.8,0-3,0.2-3,0.2c-5,0.6-8.8,3.7-11,9c-1.3,3.2-1.8,7-1.3,10.8  c1.9,15.2,0.8,26.8-2.8,31c-1,1.2-2.1,1.7-3.5,1.8c-0.1,0-0.2,0-0.4,0c-11.9,0-19.7-24.9-20.4-37.9c-1.1-20.5-5.5-33-5.7-33.5  l-1.8-4.7C97,144,93.2,134,90.7,124c-1.8-7-3.2-16.4-1.2-25.4c3.6-1.5,16-6.3,27.4-6c6.4,0.2,11.5,2.6,17,5.2  c6.2,2.9,12.7,5.9,21.3,5.9c8.4,0,14.8-3.4,20.5-6.4c5.5-2.9,10.3-5.4,16-4.7c9.9,1.2,16.8,4,19,4.9  C212.1,103.8,211.7,110.8,209.6,118.1z"/>
                                 </svg>
                                  </Avatar>
                                  <Text fw={500} ml={6}> Amalgam</Text>
                                     </div>
                                  </Group>
                                   </Menu.Item>
                                   <Menu.Item className={
                                      activeButton === 'RestoratinGlassIonomer'
                                         ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                         : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                     }onClick={() => {
                                       handleButtonClick("RestoratinGlassIonomer");
                                       setTargetPath("27");
                                     }}
                                   >
                                     <Group  className="h-[28px] w-[186.4px] " >
                                     <Radio
                                         checked={activeButton === 'RestoratinGlassIonomer'}
                                         onChange={(event) =>
                                         setChecked(event.currentTarget.checked)
                                         }
                                         icon={CheckIcon}
                                     />
                                     <div className="flex">
                                     <Avatar
                                       color="blue"
                                       radius="sm"
                                       style={{ width: "28px", height: "28px" }}
                                       px={0.5}
                                     >
                                         <svg xmlns="http://www.w3.org/2000/svg"  version="1.0" x="0px" y="0px" viewBox="0 0 106 112.5"  >
                                         <g>
                                           <path style={{ fill:"#3799CE"}} d="M80.41,70.82H72.2c-0.07-0.68-0.14-1.37-0.18-2.05c-0.12-2.12-0.04-4.27,0.68-6.28c0.4-1.12,1-2.18,1.47-3.27   c0.49-1.15,0.95-2.31,1.31-3.51c0.83-2.75,1.47-5.59,1.77-8.45c0.25-2.35,0.3-4.9-0.5-7.16c-0.71-1.99-2.13-3.57-4.2-4.15   c-2.84-0.78-5.98,0.15-8.62,1.19c-1.01,0.4-2,0.84-2.98,1.32c-0.87,0.42-0.11,1.72,0.76,1.3c2.8-1.36,6.03-2.76,9.22-2.56   c1.88,0.12,3.33,1.05,4.13,2.71c0.9,1.85,0.92,4.15,0.79,6.18c-0.17,2.65-0.73,5.27-1.43,7.83c-0.56,2.05-1.28,4.03-2.19,5.95   c-0.44,0.94-0.91,1.86-1.2,2.86c-0.28,0.96-0.43,1.96-0.51,2.96c-0.14,1.72-0.02,3.42,0.15,5.13H68.2   c-0.91-11.26-5.95-16.57-15.68-16.57c-6.38,0-10.73,2.29-13.24,7.02c1.51-6.47,4.75-14.86,6.92-18.13   c0.67-1.01,1.53-2.02,2.45-3.09c1.15-1.34,2.34-2.73,3.14-4.15c1.17-2.07,1.26-5.86,1.24-7.45c1.28-0.23,2.26-1.35,2.26-2.7v-0.82   h1.29c0.96,0,1.75-0.79,1.75-1.75v-2.39h0.18c0.96,0,1.75-0.79,1.75-1.75V5c0-0.41-0.34-0.75-0.75-0.75H44.44   c-0.41,0-0.75,0.34-0.75,0.75v14.03c0,0.96,0.79,1.75,1.75,1.75h0.18v2.39c0,0.96,0.79,1.75,1.75,1.75h1.29v0.82   c0,1.52,1.23,2.75,2.75,2.75h0.11c0.01,1.49-0.07,4.93-1.05,6.67c-0.73,1.3-1.87,2.63-2.97,3.91c-0.91,1.06-1.85,2.16-2.56,3.24   c-3.15,4.78-8.47,19.75-7.96,25.52c0.01,0.14,0.07,0.26,0.15,0.37c-0.13,0.83-0.23,1.71-0.3,2.62h-1.16   c0.07-0.71,0.14-1.43,0.18-2.15c0.1-1.98,0.03-4.02-0.53-5.94c-0.3-1.04-0.79-1.99-1.25-2.97c-0.51-1.09-0.98-2.19-1.37-3.33   c-0.95-2.72-1.63-5.62-2.01-8.49c-0.29-2.23-0.43-4.62,0.16-6.81c0.46-1.69,1.44-3.13,3.13-3.67c2.56-0.82,5.49,0.13,7.88,1.05   c0.95,0.37,1.88,0.79,2.79,1.23c0.86,0.42,1.63-0.87,0.76-1.3c-0.39-0.19-0.82-0.39-1.27-0.59c1.57-1.27,3.36-2.2,5.38-2.49   c0.4-0.06,0.62-0.57,0.52-0.92c-0.12-0.43-0.52-0.58-0.92-0.52c-2.48,0.35-4.66,1.64-6.52,3.28c-4.28-1.71-9.8-2.9-12.37,1.5   c-1.14,1.96-1.34,4.38-1.26,6.59c0.09,2.78,0.62,5.54,1.31,8.23c0.51,1.98,1.14,3.91,1.96,5.8c0.47,1.09,1.07,2.13,1.46,3.25   c0.34,0.97,0.54,1.99,0.63,3.01c0.15,1.75,0.04,3.49-0.15,5.23h-8.59c-0.41,0-0.75,0.34-0.75,0.75V101c0,0.41,0.34,0.75,0.75,0.75   h54.81c0.41,0,0.75-0.34,0.75-0.75V71.57C81.16,71.15,80.82,70.82,80.41,70.82z M45.19,19.03V5.75h13.56v13.28   c0,0.14-0.11,0.25-0.25,0.25H45.44C45.3,19.28,45.19,19.16,45.19,19.03z M47.12,23.17v-2.39h9.7v2.39c0,0.14-0.11,0.25-0.25,0.25   h-2.04h-5.11h-2.04C47.23,23.42,47.12,23.31,47.12,23.17z M50.16,25.74v-0.82h3.61v0.82c0,0.69-0.56,1.25-1.25,1.25h-1.11   C50.72,26.99,50.16,26.43,50.16,25.74z M52.52,55.75c6.49,0,13.12,1.9,14.19,15.07h-2.97c-2.29-6.05-6.17-7.61-11.22-7.61   s-8.93,1.56-11.22,7.61h-2.97C39.4,57.65,46.02,55.75,52.52,55.75z M66.87,74.77c0,2.99-0.33,5.68-0.98,8.04   c-0.27-4.37-0.83-7.8-1.64-10.49h2.56C66.84,73.1,66.87,73.91,66.87,74.77z M62.13,70.82H42.9c2.26-5.45,5.82-6.11,9.62-6.11   S59.88,65.36,62.13,70.82z M38.23,72.32h2.56c-0.81,2.69-1.37,6.12-1.64,10.49c-0.65-2.36-0.98-5.04-0.98-8.04   C38.17,73.91,38.19,73.1,38.23,72.32z M36.72,72.32c-0.04,0.79-0.06,1.61-0.06,2.45c0,4.77,0.81,8.83,2.4,12.08   c0.15,0.31,0.49,0.48,0.83,0.4c0.34-0.07,0.58-0.37,0.59-0.71c0.16-6.45,0.83-11,1.87-14.22h20.32c1.03,3.22,1.71,7.77,1.87,14.22   c0.01,0.35,0.25,0.64,0.59,0.71c0.05,0.01,0.11,0.02,0.16,0.02c0.28,0,0.55-0.16,0.67-0.42c1.59-3.25,2.4-7.31,2.4-12.08   c0-0.84-0.02-1.66-0.06-2.45h2.55c0.22,1.85,0.46,3.71,0.5,5.57c0.06,3.01-0.68,6.1-1.49,8.98c-0.47,1.68-1.05,3.33-1.84,4.89   c-0.38,0.75-0.81,1.49-1.3,2.18c-0.33,0.47-0.96,1.54-1.61,1.51c-1.17-0.05-1.1-2.65-1.15-3.45c-0.1-1.56-0.11-3.13-0.19-4.69   c-0.17-3.16-0.58-6.53-2.61-9.09c-0.35-0.44-0.75-0.82-1.18-1.18c0.43-0.87,0.88-1.56,1.3-2.11c0.25-0.33,0.19-0.8-0.14-1.05   c-0.33-0.25-0.8-0.19-1.05,0.14c-1.57,2.04-3.5,5.92-3.81,12.81c-0.24,5.22-1.25,8.08-3.02,8.52c-0.39,0.11-0.8,0.02-1.23-0.28   c-1.2-0.82-2.77-3.48-3.28-8.32c-0.5-3.6-0.69-7.23-0.5-9.48c0.15-1.76-0.13-2.98-0.14-3.03c-0.09-0.4-0.49-0.65-0.9-0.56   c-0.4,0.09-0.65,0.49-0.56,0.9c0,0.01,0.19,0.85,0.13,2.12c-2.61,1.98-3.62,5.01-4.01,8.16c-0.38,3.04-0.08,6.16-0.61,9.18   c-0.09,0.53-0.26,1.48-1,1.42c-0.63-0.04-1.22-1.05-1.55-1.51c-1.01-1.41-1.76-3-2.35-4.62c-1.03-2.81-1.67-5.78-2.14-8.74   c-0.3-1.89-0.12-3.85,0.08-5.74c0.09-0.84,0.19-1.68,0.29-2.52H36.72z M79.66,100.25H26.34V72.32h7.67   c-0.24,2.04-0.51,4.09-0.51,6.14c0.01,1.54,0.27,3.05,0.56,4.56c0.31,1.62,0.68,3.23,1.15,4.82c0.54,1.79,1.2,3.57,2.1,5.22   c0.43,0.79,0.92,1.55,1.48,2.26c0.53,0.67,1.1,1.4,1.98,1.59c0.83,0.18,1.67-0.18,2.19-0.83c0.5-0.62,0.62-1.39,0.73-2.16   c0.22-1.58,0.25-3.19,0.31-4.78c0.1-3.19,0.17-6.77,2.01-9.53c0.2-0.3,0.43-0.58,0.68-0.85c-0.04,2.28,0.16,5.2,0.57,8.17   c0.47,4.52,1.94,8.02,3.92,9.38c0.57,0.39,1.17,0.59,1.76,0.59c0.23,0,0.47-0.03,0.7-0.09c3.34-0.82,3.96-6.05,4.14-9.9   c0.17-3.65,0.8-6.39,1.58-8.42c2.16,2.07,2.7,5.31,2.89,8.19c0.11,1.65,0.12,3.31,0.21,4.97c0.08,1.41,0.03,3.38,1,4.51   c0.53,0.62,1.4,0.94,2.2,0.74c0.86-0.22,1.44-0.97,1.96-1.64c1.13-1.45,1.96-3.1,2.63-4.8c1.18-2.98,1.84-6.13,2.38-9.28   c0.51-2.94,0.1-5.9-0.26-8.85h7.29V100.25z"/><path d="M50.1,41.36c1.41-0.62,2.9-1.15,4.46-1.15c2.14-0.01,4.1,0.93,5.85,2.09c0.81,0.54,1.56-0.76,0.76-1.3   c-1.96-1.31-4.2-2.31-6.6-2.3c-1.84,0.01-3.55,0.63-5.21,1.36C48.46,40.45,49.22,41.74,50.1,41.36z"/><path d="M42.8,41.85c-0.08-0.01-0.17-0.02-0.25-0.02c-0.02,0-0.01,0-0.02,0c-0.03,0-0.06-0.01-0.09-0.01   c-0.34-0.05-0.68-0.13-1.02-0.23c-0.81-0.24-1.39-0.51-2.15-0.93c-0.34-0.19-0.83-0.09-1.03,0.27c-0.19,0.35-0.1,0.82,0.27,1.03   c1.47,0.82,3.08,1.4,4.78,1.43c0.39,0.01,0.77-0.35,0.75-0.75c-0.02-0.41-0.33-0.74-0.75-0.75C43.12,41.88,42.96,41.87,42.8,41.85z   "/><path d="M57.69,37.34c0.35,0.17,0.83,0.11,1.03-0.27c0.18-0.34,0.11-0.84-0.27-1.03c-1.51-0.75-3.08-1.42-4.72-1.84   c-0.38-0.1-0.83,0.12-0.92,0.52c-0.09,0.39,0.12,0.82,0.52,0.92C54.89,36.05,56.27,36.63,57.69,37.34z"/><path d="M77.04,78.84c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S76.62,78.84,77.04,78.84   z"/><path d="M75.88,84.41c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S75.46,84.41,75.88,84.41   z"/>
                                           <path style={{ fill:"#3799CE"}}d="M76.4,89.7h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S76.81,89.7,76.4,89.7z"/>
                                           <path style={{ fill:"#3799CE"}}d="M72.73,92.69h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S73.14,92.69,72.73,92.69z"/>
                                           <path style={{ fill:"#3799CE"}}d="M76.4,95.89h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S76.81,95.89,76.4,95.89z"/>
                                           <path style={{ fill:"#3799CE"}}d="M69.4,94.76h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S69.81,94.76,69.4,94.76z"/>
                                           <path style={{ fill:"#3799CE"}}d="M72.06,97.44h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S72.47,97.44,72.06,97.44z"/>
                                           <path style={{ fill:"#3799CE"}}d="M65.49,97.57h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S65.91,97.57,65.49,97.57z"/>
                                           <path style={{ fill:"#3799CE"}}d="M59.46,97.28h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S59.88,97.28,59.46,97.28z"/>
                                           <path style={{ fill:"#3799CE"}}d="M55.17,96.82h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S55.58,96.82,55.17,96.82z"/>
                                           <path style={{ fill:"#3799CE"}}d="M50.57,97.88h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S50.98,97.88,50.57,97.88z"/>
                                           <path style={{ fill:"#3799CE"}}d="M46.24,96.49h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S46.65,96.49,46.24,96.49z"/>
                                           <path style={{ fill:"#3799CE"}}d="M40.11,97.36H40.1c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S40.53,97.36,40.11,97.36z"/>
                                           <path style={{ fill:"#3799CE"}}d="M34.75,92.32h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S35.16,92.32,34.75,92.32z"/>
                                           <path style={{ fill:"#3799CE"}}d="M32.35,89.36c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S32.35,89.78,32.35,89.36z   "/>
                                           <path style={{ fill:"#3799CE"}}d="M33.35,83.44h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S33.76,83.44,33.35,83.44z"/>
                                           <path style={{ fill:"#3799CE"}}d="M32.87,95.16h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S33.28,95.16,32.87,95.16z"/>
                                           <path style={{ fill:"#3799CE"}}d="M34.75,97.88h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S35.16,97.88,34.75,97.88z"/>
                                           <path style={{ fill:"#3799CE"}}d="M29.73,91.72h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S30.14,91.72,29.73,91.72z"/>
                                           <path style={{ fill:"#3799CE"}}d="M28.63,87.25c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S28.21,87.25,28.63,87.25   z"/>
                                           <path style={{ fill:"#3799CE"}}d="M31.9,80.52c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S31.9,80.93,31.9,80.52z"/>
                                           <path style={{ fill:"#3799CE"}}d="M31.06,76.72c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S30.64,76.72,31.06,76.72   z"/>
                                           <path style={{ fill:"#3799CE"}}d="M27.99,96.12h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S28.41,96.12,27.99,96.12z"/>
                                           <path style={{ fill:"#3799CE"}}d="M72.69,88.77c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S72.28,88.77,72.69,88.77   z"/>
                                           <path style={{ fill:"#3799CE"}}d="M73.9,75.5c0.41,0,0.75-0.34,0.75-0.75S74.32,74,73.9,74h-0.01c-0.41,0-0.74,0.34-0.74,0.75S73.49,75.5,73.9,75.5z"/>
                                           <path style={{ fill:"#3799CE"}}d="M46.17,93.07c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S46.17,93.48,46.17,93.07z   "/>
                                           <path style={{ fill:"#3799CE"}}d="M46.06,88.27c0.41,0,0.75-0.34,0.75-0.75s-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75S45.65,88.27,46.06,88.27   z"/>
                                           <path style={{ fill:"#3799CE"}}d="M59.74,93.82c0-0.41-0.34-0.75-0.75-0.75h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75S59.74,94.24,59.74,93.82z   "/>
                                           <path style={{ fill:"#3799CE"}}d="M59.46,87.37h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S59.88,87.37,59.46,87.37z"/>
                                           <path style={{ fill:"#3799CE"}}d="M59.79,82.7h-0.01c-0.41,0-0.74,0.34-0.74,0.75s0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75S60.2,82.7,59.79,82.7z"/>
                                           </g>
                                         </svg>
                                     </Avatar>
                                     <Text fw={500} ml={6}>  Glass Ionomer</Text>
                                         </div>
                                 </Group>
                                   </Menu.Item>
                                   </Menu.Dropdown>
                                 </Menu>
                          <Menu shadow="md" width={210}  trigger="hover" openDelay={100} closeDelay={400}>
                                          <Tooltip
                                                label="Root Canal"
                                                withArrow
                                                className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                              >
                                          <Menu.Target>
                                                <Button
                                          styles={{
                                            root: {
                                              position: 'relative',
                                              color: 'white',
                                              height: '35px',
                                              width: '35px',
                                              padding: 0,
                                              borderRadius: '0.5rem'
                                            },
                                          }}
                                        >
                                          <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        height: '100%',
                                        width: '100%',
                                      }}
                                    >
                                        <span  className={
                                        (
                                        // viewCleaning4 === true ||
                                        activeButton  === "RootPermanent"||
                                        activeButton  === "RootTemporary"||
                                        activeButton  === "RootCalcium"  ||
                                        activeButton  === "RootGuttaPercha" ||
                                        isHidingMode
                                        )
                                          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                                          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
                                      }
                                      onClick={() =>
                                        setChecked(!checked)
                                      }
                                          >
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 60" x="0px" y="0px">
                                        <path style={{ fill: "#ffffff",  stroke:"#fff" ,strokeWidth:0.25,strokeMiterlimit:10 }} d="M5,25.69a13.84,13.84,0,0,1,5,10.52v5.42a5.37,5.37,0,0,0,10.72.48l1-11a2.29,2.29,0,0,1,4.56,0l1,11A5.37,5.37,0,0,0,38,41.63V36.21a13.84,13.84,0,0,1,5-10.52A14,14,0,1,0,24,5.19,14,14,0,1,0,5,25.69Zm24.3,16.24-1-11a4.29,4.29,0,0,0-8.54,0l-1,11a3.35,3.35,0,0,1-1.52,2.52l1-16.2a4,4,0,0,1,3.51-3.8,19.66,19.66,0,0,1,4.6,0,4,4,0,0,1,3.51,3.8l1,16.2A3.35,3.35,0,0,1,29.27,41.93ZM2.19,12.87a12,12,0,0,1,21-5.55,1,1,0,0,0,1.54,0,12,12,0,1,1,17,16.85,15.8,15.8,0,0,0-5.74,12v5.42a3.37,3.37,0,0,1-1.38,2.7l.63-18.86A1.35,1.35,0,0,1,36,24.28a1.41,1.41,0,0,1,1.39,0l.19.13a2.45,2.45,0,0,0,3.76-1.69l.9-6a5.9,5.9,0,0,0-2-5.11,6.13,6.13,0,0,0-5.5-1.26,42.39,42.39,0,0,1-21.4,0,6.13,6.13,0,0,0-5.5,1.26,5.91,5.91,0,0,0-2,5.16l.89,5.92a2.43,2.43,0,0,0,1.44,1.88,2.5,2.5,0,0,0,2.37-.22l.15-.1a1.39,1.39,0,0,1,1.38,0,1.35,1.35,0,0,1,.76,1.19l.63,18.86A3.37,3.37,0,0,1,12,41.63V36.21a15.8,15.8,0,0,0-5.74-12A12,12,0,0,1,2.19,12.87Z"/>
                                        </svg>
                                                </span>
                                          </div>
                                          <span
                                              style={{
                                              position: 'absolute',
                                              bottom: '0px',
                                              right: '0px',
                                              fontSize: '8px',
                                              fontWeight: '800',
                                              backgroundColor: 'white',
                                              // borderRadius:'0.125rem' ,
                                              borderTopLeftRadius: '0.5rem' ,
                                              borderBottomRightRadius: '0.5rem' ,
                                              color:'#3799CE',
                                                padding:'3px  0px 1px 2px' ,
                                            }}
                                            className="h-[14px] w-[14px] "
                                          >
                                            Ro
                                          </span>
                                        </Button>
                                          </Menu.Target>
                                          </Tooltip>
                                          <Menu.Dropdown>
                                          <Menu.Label>Root Canal</Menu.Label>
                                          <Menu.Item className={
                                    activeButton  === "RootPermanent"
                                        ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                        : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                  }
                                  onClick={() => {
                                    handleButtonClick("RootPermanent");
                                    // Always set the color when button is clicked
                                    handleColorSelect("#FF4444", "fill");
                                  }}
                                  >
                                  <Group  className="h-[28px] w-[186.4px] " >
                                    <Radio
                                        checked={activeButton  === "RootPermanent"}
                                        onChange={(event) =>
                                        setChecked(event.currentTarget.checked)
                                        }
                                        icon={CheckIcon}
                                        className="cursor-pointer"
                                    />
                                        <div className="flex">
                                        <Avatar
                                          color="blue"
                                          radius="sm"
                                          style={{ width: "28px", height: "28px" }}
                                          px={0.5}
                                        >
                                      <svg xmlns="http://www.w3.org/2000/svg"
                                          version="1.1" x="0px" y="0px" viewBox="0 0 400 420" >
                                          <path style={{fill:"#3799CE"}} d="M200.185 128.344c4.119,-1.685 8.045,-3.332 11.635,-4.906 9.732,-4.265 16.997,-7.995 23.996,-10.952 -0.086,0.497 -0.132,1.012 -0.132,1.543 0,3.415 1.858,6.739 4.679,8.626 -5.152,2.105 -10.227,4.596 -15.265,6.958 -3.293,11.774 -5.356,23.053 -7.41,35.056 -0.311,1.814 -0.615,3.636 -0.913,5.465 3.68,5.454 7.171,10.804 9.816,15.683 1.723,3.18 3.082,6.146 4.38,9.769l-0.005 0.002c1.304,3.64 2.537,7.889 4.002,13.618 2.886,11.281 6.804,28.816 10.724,46.361 1.933,5.908 3.265,12.282 4.208,19.285 0.977,7.254 1.538,15.188 1.89,23.462 0.258,6.052 0.403,12.318 0.52,18.641 0.217,0.12 0.439,0.229 0.665,0.324 0.295,-6.993 0.882,-13.999 2.637,-21.053 1.172,-4.718 2.864,-9.438 4.88,-14.044 2.011,-4.593 4.341,-9.065 6.8,-13.301 1.45,-2.499 2.94,-4.911 4.428,-7.232 0.641,-3.794 1.286,-7.584 2.068,-11.183 0.817,-3.76 1.795,-7.38 3.095,-10.717 1.55,-3.979 3.52,-7.454 5.572,-11.074 1.362,-2.405 2.763,-4.877 4.058,-7.522 3.02,-6.171 5.683,-13.666 8.319,-21.259 1.383,-16.172 1.743,-32.218 0.784,-46.611 -1.038,-9.706 -2.494,-21.207 -9.586,-28.522 -0.76,-0.784 -1.563,-1.488 -2.401,-2.12 -0.468,-2.333 -1.763,-4.497 -3.579,-6.03 2.735,-1.138 4.725,-3.774 4.966,-7.049 2.598,1.359 5.029,3.122 7.217,5.378 6.632,6.84 11.026,18.198 12.892,35.649 1.865,17.45 1.202,40.994 -1.368,62.217 -2.57,21.222 -7.046,40.124 -12.518,56.787 -5.472,16.663 -11.937,31.087 -16.746,40.372 -4.808,9.285 -7.958,13.43 -11.357,15.586 -3.399,2.156 -7.047,2.321 -11.441,0.994 -4.393,-1.326 -9.534,-4.144 -12.932,-12.766 -3.399,-8.622 -5.057,-23.047 -5.886,-33.658 -0.829,-10.611 -0.829,-17.409 -4.477,-23.792 -3.648,-6.384 -10.943,-12.353 -18.321,-14.259 -7.378,-1.907 -14.839,0.249 -21.223,3.896 -6.383,3.648 -11.689,8.787 -14.341,18.818 -2.653,10.031 -2.653,24.954 -2.985,35.068 -0.331,10.113 -0.994,15.419 -2.404,19.564 -1.409,4.145 -3.565,7.13 -7.544,8.207 -3.979,1.078 -9.782,0.249 -17.824,-7.958 -8.041,-8.207 -18.321,-23.792 -26.114,-42.197 -6.25,-14.762 -10.9,-31.337 -13.737,-46.389l-0.141 -0.58c-5.106,-21.093 -8.173,-46.211 -9.376,-63.413 -1.202,-17.202 -0.538,-26.487 1.99,-35.233 2.528,-8.746 6.921,-16.954 14.258,-21.14 7.337,-4.187 17.617,-4.352 27.151,-2.238 9.534,2.113 18.321,6.507 27.824,10.512 4.832,2.037 9.849,3.973 14.595,5.549l-31.277 26.867 51.254 -23.059zm47.874 -56.344l23.387 0c1.342,0 2.496,0.504 3.408,1.488 0.912,0.984 1.327,2.172 1.226,3.51l-2.035 26.784 2.117 0.602c1.815,0.517 3.301,2.544 3.301,4.506l0 0.001c0,1.962 -1.486,3.145 -3.301,2.628l-2.647 -0.753 -0.66 8.686 2.09 0.595c1.814,0.517 3.299,2.544 3.299,4.506l0 0.001c0,1.962 -1.485,3.145 -3.299,2.628l-2.62 -0.746 -0.66 8.688 2.583 0.735c1.816,0.517 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.484,3.145 -3.3,2.628l-3.113 -0.886 -0.66 8.687 1.859 0.529c1.815,0.516 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.485,3.144 -3.3,2.628l-2.39 -0.68 -0.659 8.687 2.179 0.62c1.815,0.517 3.3,2.544 3.3,4.506l0 0.001c0,1.963 -1.485,3.145 -3.3,2.628l-2.71 -0.771 -0.66 8.687 1.977 0.563c1.815,0.516 3.3,2.543 3.3,4.506l0 0.001c0,1.962 -1.485,3.144 -3.3,2.628l-2.507 -0.714 -0.66 8.688 1.949 0.554c1.815,0.516 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.485,3.145 -3.3,2.628l-2.479 -0.706 -0.66 8.687 1.921 0.547c1.815,0.517 3.3,2.544 3.3,4.506l0 0.001c0,1.962 -1.485,3.145 -3.3,2.628l-2.451 -0.698 -0.798 10.497c-0.183,2.429 -2.196,4.295 -4.632,4.295 -2.437,0.001 -4.451,-1.865 -4.635,-4.295l-1.037 -13.656 -2.211 -0.63c-1.816,-0.517 -3.3,-2.544 -3.3,-4.505l0 -0.002c0,-1.962 1.484,-3.144 3.3,-2.628l1.657 0.472 -0.689 -9.071 -2.187 -0.622c-1.816,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.962 1.484,-3.144 3.3,-2.627l1.633 0.465 -0.689 -9.072 -2.162 -0.615c-1.815,-0.516 -3.3,-2.544 -3.3,-4.506l0 -0.001c0,-1.962 1.485,-3.145 3.3,-2.628l1.608 0.458 -0.689 -9.071 -2.311 -0.658c-1.815,-0.516 -3.3,-2.544 -3.3,-4.506l0 -0.001c0,-1.962 1.485,-3.145 3.3,-2.628l1.757 0.5 -0.688 -9.07 -1.939 -0.552c-1.815,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.962 1.485,-3.144 3.3,-2.627l1.384 0.394 -0.689 -9.072 -2.61 -0.742c-1.815,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.961 1.485,-3.144 3.3,-2.627l2.057 0.585 -0.689 -9.071 -2.064 -0.587c-1.815,-0.517 -3.3,-2.544 -3.3,-4.506l0 -0.002c0,-1.962 1.485,-3.144 3.3,-2.627l1.51 0.43 -0.689 -9.071 -2.039 -0.58c-1.815,-0.517 -3.3,-2.545 -3.3,-4.507l0 -0.001c0,-1.962 1.485,-3.144 3.3,-2.628l1.485 0.423 -1.403 -18.468c-0.101,-1.338 0.314,-2.526 1.226,-3.51 0.912,-0.984 2.066,-1.488 3.408,-1.488zm-31.974 102.476c-0.429,2.771 -0.836,5.556 -1.215,8.351 2.615,9.181 5.217,18.006 7.763,25.325 3.312,9.517 6.568,16.603 9.823,23.668 1.597,1.904 3.094,3.847 4.465,5.838 0.932,1.354 1.806,2.728 2.624,4.126 -2.742,-12.179 -5.35,-23.514 -7.48,-31.84 -1.461,-5.711 -2.667,-9.882 -3.91,-13.351l-0.006 0c-1.25,-3.488 -2.55,-6.327 -4.193,-9.359 -2.152,-3.972 -4.906,-8.302 -7.871,-12.758zm-2.159 15.887c-0.832,7.292 -1.424,14.626 -1.638,21.934 4.763,4.094 9.432,8.223 13.703,12.504 -2.04,-4.646 -4.093,-9.667 -6.18,-15.666 -1.947,-5.599 -3.913,-12.012 -5.885,-18.772zm-1.715 25.817c-0.059,5.601 0.12,11.177 0.597,16.699 9.057,1.664 18.015,8.558 22.541,16.478 4.305,7.534 4.281,16.331 4.938,24.741 0.474,6.073 1.501,24.841 6.224,35.414 0.76,1.705 1.677,3.358 2.76,4.731 -0.108,-5.337 -0.246,-10.632 -0.467,-15.812 -0.348,-8.182 -0.902,-16.024 -1.866,-23.182 -0.958,-7.109 -2.323,-13.543 -4.332,-19.481 -1.999,-5.904 -4.635,-11.316 -8.145,-16.412 -5.696,-8.272 -13.743,-15.797 -22.25,-23.176zm43.734 101.566c1.197,-0.108 2.49,-0.559 3.882,-1.442 0.609,-0.386 1.437,-1.217 2.407,-2.434 0.117,-6.885 0.458,-13.827 3.703,-21.501 3.48,-8.232 10.235,-17.089 16.989,-25.947l0.053 -0.064 0.002 -0.003 0.05 0.043c0.527,-1.558 0.928,-2.77 1.176,-3.525 1.893,-7.128 3.615,-14.789 5.126,-22.766 -1.516,2.551 -3.168,5.224 -5.073,8.126l-0.006 0c-1.182,1.8 -2.475,3.711 -3.829,5.711l0.004 0.003c-3.366,4.973 -7.109,10.503 -10.55,16.433 -2.412,4.156 -4.692,8.528 -6.648,12.996 -1.95,4.456 -3.585,9.017 -4.716,13.565 -1.721,6.919 -2.284,13.86 -2.57,20.805zm9.42 -8.438c1.428,-2.341 2.952,-5.177 4.426,-8.405 3.648,-7.988 7.338,-17.757 10.11,-25.509 -4.64,6.276 -8.801,12.43 -11.213,18.134 -2.323,5.496 -3.057,10.684 -3.323,15.78zm25.5 -77.827c1.05,-6.332 1.966,-12.807 2.726,-19.325 -1.397,3.654 -2.846,7.139 -4.397,10.308 -1.366,2.792 -2.772,5.273 -4.14,7.686 -2,3.528 -3.919,6.915 -5.385,10.677 -1.225,3.144 -2.162,6.624 -2.954,10.271 -0.306,1.41 -0.589,2.835 -0.858,4.27 0.709,-1.054 1.407,-2.086 2.09,-3.095l0.006 0c1.337,-1.976 2.617,-3.867 3.806,-5.677l-0.005 -0.003c3.778,-5.755 6.495,-10.483 9.111,-15.112z"/>
                                      </svg>
                                        </Avatar>
                                        <Text fw={500} ml={6}> Permanent {/* الجدور */}</Text>
                                            </div>
                                    </Group>
                    </Menu.Item>
                      <Menu.Item className={
                                activeButton  === "RootTemporary"
                                  ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                  : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                              }onClick={() => {
                                handleButtonClick("RootTemporary");
                                setTargetPath("28");
                              }}
                            >
                              <Group  className="h-[28px] w-[186.4px] " >
                              <Radio
                                  checked={activeButton  === "RootTemporary"}
                                  onChange={(event) =>
                                  setChecked(event.currentTarget.checked)
                                  }
                                  icon={CheckIcon}
                              />
                              <div className="flex">
                              <Avatar
                                color="blue"
                                radius="sm"
                                style={{ width: "28px", height: "28px" }}
                                  px={0.5}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 90 85"  >
                                <path style={{ fill: "#3799CE" }} d="M66.562,5c-7.158,0-13.376,4.1-16.43,10.074c-5.418-0.499-10.791,1.702-14.797,4.021c-4.119-2.39-8.075-3.745-11.773-4.029  c-5.846-0.449-10.899,1.894-14.612,6.776c-2.179,2.865-3.472,6.067-3.84,9.517c-0.312,2.917,0.037,6.027,1.039,9.245  c1.768,5.683,5.244,10.685,7.848,13.88c1.667,2.046,2.585,4.604,2.585,7.203V77.5c0,2.003,0.779,3.887,2.194,5.304  C20.192,84.22,22.076,85,24.08,85c3.555,0,6.647-2.529,7.354-6.014l1.975-9.774c0.296-1.462,1.55-1.576,1.926-1.576  c0.377,0,1.63,0.114,1.926,1.574l1.975,9.776c0.343,1.691,1.267,3.225,2.604,4.32c1.4,1.144,3.165,1.742,4.977,1.689  c4.01-0.12,7.27-3.539,7.27-7.621V61.676c0-2.596,0.914-5.152,2.573-7.196c2.464-3.035,5.615-7.579,7.451-12.768  c0.802,0.106,1.62,0.163,2.45,0.163C76.729,41.875,85,33.604,85,23.437S76.729,5,66.562,5L66.562,5z M53.022,51.526  c-2.335,2.878-3.621,6.482-3.621,10.151v15.698c0,1.574-1.221,2.891-2.725,2.936c-1.34,0.062-2.602-0.938-2.845-2.253l-1.976-9.775  c-0.646-3.189-3.266-5.332-6.52-5.332c-3.254,0-5.875,2.143-6.521,5.333l-1.975,9.774c-0.264,1.307-1.425,2.256-2.759,2.256  c-1.516,0.031-2.842-1.299-2.811-2.812V61.686c0-3.675-1.292-7.284-3.639-10.164c-2.343-2.875-5.461-7.345-7.006-12.313  c-1.733-5.57-1.041-10.458,2.056-14.53c2.78-3.656,6.221-5.271,10.523-4.941c2.781,0.214,5.543,1.23,7.873,2.388  c-1.872,1.618-3.798,3.716-5.177,6.3c-0.609,1.143-0.177,2.563,0.965,3.171c1.119,0.604,2.559,0.188,3.171-0.965  c1.763-3.304,4.861-5.693,6.57-6.833c5.103-3.189,9.057-4.101,11.902-4.111c-0.251,1.211-0.383,2.464-0.383,3.748  c0,7.692,4.736,14.299,11.445,17.058C57.939,44.916,55.189,48.857,53.022,51.526L53.022,51.526z M66.562,37.187  c-7.582,0-13.751-6.169-13.751-13.75s6.169-13.75,13.751-13.75c7.581,0,13.75,6.169,13.75,13.75S74.144,37.187,66.562,37.187  L66.562,37.187z M66.562,37.187"/>
                                <path style={{fill:"#3799CE"}} d="M66.562,14.375c-1.295,0-2.344,1.049-2.344,2.344v4.375h-2.812c-1.295,0-2.344,1.049-2.344,2.344  c0,1.295,1.049,2.344,2.344,2.344h5.156c1.294,0,2.344-1.049,2.344-2.344v-6.719C68.906,15.424,67.856,14.375,66.562,14.375  L66.562,14.375z M66.562,14.375"/>
                                </svg>
                                </Avatar>
                                <Text fw={500} ml={6}> Temporary</Text>
                                  </div>
                              </Group>
                      </Menu.Item>
                          <Menu.Item className={
                                  activeButton  === "RootCalcium"
                                    ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                    : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                }onClick={() => {
                                  handleButtonClick("RootCalcium");
                                  setTargetPath("30");
                                }}
                              >
                                <Group  className="h-[28px] w-[186.4px] " >
                                <Radio
                                    checked={activeButton  === "RootCalcium" }
                                    onChange={(event) =>
                                    setChecked(event.currentTarget.checked)
                                    }
                                    icon={CheckIcon}
                                />
                                <div className="flex">
                                <Avatar
                                  color="blue"
                                  radius="sm"
                                  style={{ width: "28px", height: "28px" }}
                                    px={0.5}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg"    version="1.1"viewBox="0 0 846.66 880" x="0px" y="0px" >
                        <path style={{fill:"#3799CE"}} d="M423.33 148.5c-24.94,0.08 -45.13,-4.82 -64.69,-9.58 -28.86,-7.01 -56.4,-13.7 -95.77,-1.57 -14.95,4.6 -26.46,11.13 -36.08,18.8 -9.79,7.8 -17.84,16.95 -25.64,26.57 -18.68,23.03 -30.03,48.47 -34.4,76.54 -2.19,14.06 -2.65,28.83 -1.43,44.35 29.12,4.33 44.55,25.97 58.2,45.1 9.63,13.5 18.2,25.48 28.77,25.36 17.79,-0.22 30.06,15.02 42.1,30 11.46,14.26 22.72,28.25 35.35,18.95 38.92,-28.64 63.8,-10.87 92.32,9.53 7.25,5.18 14.79,10.58 22.56,15.06 18.17,10.47 30.91,-0.04 43.95,-10.81 12.21,-10.09 24.66,-20.37 42.45,-20.29 13.32,0.06 22.25,3.81 29.93,7.02 11.29,4.73 18.75,7.84 42.03,-21.7 4.93,-6.27 9.86,-16.59 15.04,-27.46l6.88 -14.08c12.88,-25.35 28.62,-47.79 54.67,-41.01 3.1,-21.32 3.27,-41.31 0.35,-60.02 -4.37,-28.07 -15.72,-53.51 -34.4,-76.54 -7.8,-9.62 -15.85,-18.77 -25.64,-26.57 -9.62,-7.67 -21.13,-14.2 -36.08,-18.8 -39.37,-12.13 -66.91,-5.44 -95.77,1.57 -19.57,4.75 -39.75,9.66 -64.7,9.58zm-255.93 172.81c1.6,10.34 3.86,20.99 6.81,31.96 8.53,31.72 22.47,56.75 36.45,81.87 12.76,22.91 25.56,45.92 34.76,74.19 8.66,26.65 13.1,57.97 17.4,88.44 7.97,56.49 15.53,109.99 47.97,117.52 20.24,4.7 30.71,-18.67 38.2,-50.91 3.77,-16.28 6.7,-34.32 9.63,-52.42 5.57,-34.45 11.21,-69.24 22.53,-92.78 4.65,-9.67 10.75,-17.35 17.62,-22.68 7.45,-5.8 15.89,-8.89 24.53,-8.92 8.68,0.03 17.11,3.12 24.57,8.9 6.89,5.34 13,13.02 17.65,22.67l0.21 0.47c11.18,23.53 16.79,58.1 22.32,92.34 2.93,18.1 5.86,36.14 9.63,52.38l0.08 0.37c7.48,32.07 17.94,55.26 38.12,50.58 32.44,-7.53 40,-61.04 47.97,-117.53 4.3,-30.47 8.73,-61.78 17.39,-88.43 9.2,-28.27 22.01,-51.29 34.77,-74.2 13.98,-25.1 27.92,-50.15 36.45,-81.86 1.55,-5.76 2.91,-11.42 4.08,-17.01 -15.97,-3.68 -27.08,13.05 -36.56,31.67l-6.68 13.72c-5.58,11.71 -10.88,22.82 -17.02,30.62 -31.25,39.65 -43.4,34.58 -61.81,26.86 -6.3,-2.63 -13.65,-5.71 -23.51,-5.76 -11.63,-0.05 -21.72,8.28 -31.61,16.45 -17.61,14.55 -34.8,28.74 -63.13,12.41 -8.69,-5.01 -16.46,-10.57 -23.94,-15.92 -23.22,-16.6 -43.48,-31.07 -72.56,-9.67 -25.64,18.87 -41.91,-1.37 -58.49,-21.99 -9.5,-11.82 -19.18,-23.83 -28.74,-23.72 -19.4,0.23 -30.38,-15.13 -42.73,-32.44 -10.76,-15.09 -22.83,-32.01 -42.36,-37.18zm195.2 -198.79c18.61,4.53 37.82,9.19 60.73,9.12 22.9,0.07 42.13,-4.6 60.74,-9.12 31.18,-7.58 60.92,-14.8 104.69,-1.31 17.23,5.3 30.51,12.85 41.64,21.72 10.97,8.74 19.75,18.71 28.22,29.15 20.6,25.39 33.12,53.51 37.96,84.6 3.66,23.44 2.93,48.48 -1.96,75.23l-0.03 0.2 -0.03 0.21 -0.02 0.08 -0.02 0.12 -0.04 0.21 -0.01 0.03c-1.5,8.13 -3.4,16.42 -5.67,24.87 -9.04,33.58 -23.52,59.61 -38.05,85.7 -12.31,22.11 -24.68,44.32 -33.43,71.22 -8.22,25.29 -12.54,55.82 -16.73,85.53 -8.8,62.4 -17.15,121.5 -60.95,131.67 -33.39,7.76 -48.63,-21.78 -58.32,-63.17l-0.1 -0.4c-3.79,-16.32 -6.81,-34.92 -9.83,-53.58 -5.33,-33.01 -10.74,-66.35 -20.9,-87.74l-0.19 -0.37c-3.5,-7.27 -7.91,-12.9 -12.75,-16.65 -4.51,-3.5 -9.42,-5.37 -14.22,-5.4 -4.78,0.03 -9.68,1.91 -14.19,5.41 -4.89,3.76 -9.3,9.38 -12.77,16.61 -10.28,21.38 -15.72,54.93 -21.09,88.14 -3.02,18.66 -6.04,37.26 -9.83,53.58 -9.69,41.6 -24.92,71.36 -58.42,63.57 -43.8,-10.17 -52.15,-69.26 -60.95,-131.66 -4.19,-29.71 -8.52,-60.25 -16.74,-85.54 -8.75,-26.89 -21.11,-49.1 -33.42,-71.21 -14.53,-26.1 -29.02,-52.14 -38.05,-85.71 -4.19,-15.59 -7.09,-30.63 -8.67,-45.15l-0.03 -0.21 -0.02 -0.21c-2.09,-19.38 -1.8,-37.83 0.94,-55.38 4.84,-31.09 17.36,-59.21 37.96,-84.6 8.47,-10.44 17.25,-20.41 28.22,-29.15 11.13,-8.87 24.41,-16.42 41.64,-21.72 43.77,-13.49 73.51,-6.27 104.69,1.31z"/>
                        </svg>

                                  </Avatar>
                                  <Text fw={500} ml={6}> Calcium</Text>
                                    </div>
                              </Group>
                                  </Menu.Item>
                                    <Menu.Item className={
                                                activeButton  === "RootGuttaPercha"
                                                    ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                                    : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                                }onClick={() => {
                                                  handleButtonClick("RootGuttaPercha");
                                                  setTargetPath("34");
                                                }}
                                              >
                                                <Group  className="h-[28px] w-[186.4px] " >
                                                <Radio
                                                    checked={activeButton  === "RootGuttaPercha"}
                                                    onChange={(event) =>
                                                    setChecked(event.currentTarget.checked)
                                                    }
                                                    icon={CheckIcon}
                                                />
                                                <div className="flex">
                                                <Avatar
                                                  color="blue"
                                                  radius="sm"
                                                  style={{ width: "28px", height: "28px" }}
                                                  px={0.5}
                                                >

                                                      <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 104 80" >
                                                  <path style={{fill:"#3799CE"}} d="M81.23,21.11c-3.54-5.34-11.02-3.26-15.87-1.29c-1.13,0.46-2.23,0.96-3.33,1.49c-0.87,0.42-0.11,1.71,0.76,1.29
                                                    c3.35-1.61,7.03-3.18,10.79-3.37c2.17-0.12,4.31,0.33,5.76,1.89c1.33,1.42,1.91,3.55,2.1,5.55c0.25,2.66-0.02,5.36-0.47,7.99
                                                    c-0.51,3-1.28,5.95-2.19,8.86c-0.44,1.42-0.92,2.83-1.42,4.23c-0.46,1.26-1,2.48-1.56,3.7c-0.61,1.33-1.18,2.59-1.41,4.04
                                                    c-0.45,2.91-0.31,5.94-0.26,8.87c0.05,3.01,0.15,6.02,0.1,9.03c-0.08,5.26-0.65,10.6-2.64,15.51V65.46
                                                    c-0.01-6.52,1.25-12.89,3.76-18.95c0.28-0.69,0.48-1.16,0.54-1.28c0.01-0.03,0.03-0.07,0.04-0.1c0.87-2.49,2.8-10.21,0.3-12.94
                                                    c-3.34-3.65-11.01-0.3-13.28,0.81c-3.89,1.9-7.28,2.74-11.01,2.74c-0.42,0-0.75,0.34-0.75,0.75c0,0.42,0.33,0.75,0.75,0.75
                                                    c3.97,0,7.57-0.89,11.67-2.89c4-1.97,9.42-3.44,11.51-1.15c1.6,1.75,0.69,7.73-0.59,11.4c-0.09,0.18-0.25,0.55-0.57,1.34
                                                    c-0.38,0.92-0.72,1.85-1.05,2.79c-1.44-0.93-2.91-2.07-3.11-2.57c-0.02-0.05-0.2-0.56,0.16-2.57C70,43.4,70,43.22,70.02,43.03
                                                    c1.28-0.57,2.56-0.42,2.57-0.42c0.41,0.06,0.79-0.23,0.85-0.64c0.06-0.41-0.23-0.79-0.64-0.84c-0.07-0.01-1.31-0.17-2.75,0.29
                                                    c-0.07-0.79-0.26-1.55-0.58-2.24c-0.17-0.38-0.62-0.55-0.99-0.37c-0.38,0.17-0.54,0.62-0.37,0.99c0.47,1.03,0.6,2.25,0.38,3.52
                                                    c-0.19,1.08-0.26,1.81-0.25,2.33c-0.56,0.04-1.14,0.19-1.73,0.36c-0.25,0.06-0.5,0.13-0.76,0.2c-0.17-0.5-0.44-1.22-0.82-2
                                                    c0.11-0.36,0.43-1.08,1.23-1.56c0.36-0.21,0.48-0.67,0.27-1.03c-0.21-0.35-0.67-0.47-1.03-0.26c-0.63,0.37-1.07,0.85-1.37,1.3
                                                    c-0.39-0.58-0.85-1.14-1.37-1.6c-0.32-0.27-0.79-0.24-1.06,0.07c-0.27,0.32-0.24,0.79,0.07,1.06c1.33,1.17,2.19,3.17,2.58,4.27
                                                    c-1.27,0.1-2.71-0.16-4.47-1.36c-0.34-0.24-0.81-0.15-1.04,0.19c-0.23,0.35-0.15,0.81,0.19,1.05c0.51,0.34,0.99,0.61,1.45,0.84
                                                    c-0.51,0.52-1.12,1.39-1.3,2.71c-0.05,0.41,0.24,0.79,0.65,0.84c0.03,0.01,0.07,0.01,0.1,0.01c0.37,0,0.69-0.27,0.74-0.65
                                                    c0.21-1.58,1.27-2.2,1.33-2.24c0.04-0.02,0.06-0.06,0.1-0.09c0.63,0.16,1.22,0.23,1.78,0.23c1.21,0,2.24-0.29,3.13-0.54
                                                    c0.68-0.19,1.27-0.34,1.74-0.31c0.78,1.11,2.78,2.44,3.78,3.06c-1.23,3.88-1.97,7.87-2.23,11.93l-0.41,0.08
                                                    c-2.34,0.43-4.96,0.92-7.02,0.32c-1.01-1.66-2.1-3.08-3.24-4.24c0.45-0.16,1-0.39,1.59-0.68c0.35,0.12,1.08,0.43,1.56,1.24
                                                    c0.14,0.23,0.39,0.37,0.64,0.37c0.13,0,0.26-0.04,0.38-0.11c0.36-0.21,0.48-0.67,0.27-1.03c-0.38-0.63-0.85-1.06-1.3-1.36
                                                    c0.58-0.4,1.14-0.85,1.6-1.38c0.27-0.31,0.23-0.78-0.08-1.06c-0.31-0.27-0.78-0.24-1.06,0.08c-1.42,1.63-4.09,2.54-4.86,2.77   c-2.03-1.67-4.21-2.55-6.44-2.55c-1.46,0-2.9,0.38-4.29,1.11c-0.01,0-0.03,0-0.04,0.01c-2.51,1.4-5.86,0.01-8.82-1.23   c-2.44-1.01-4.94-2.05-6.54-1.05c-0.62-2.54-1.43-5.03-2.44-7.47c-0.32-0.79-0.47-1.16-0.56-1.35c-1.29-3.67-2.2-9.65-0.6-11.4   c1.45-1.58,4.33-1.27,6.5-0.73c0.5,0.12,1.02,0.28,1.55,0.46l-2.4,15.06c-0.07,0.41,0.21,0.8,0.62,0.86   c0.04,0.01,0.08,0.01,0.12,0.01c0.36,0,0.68-0.26,0.74-0.63l6.87-43.11l0.06,0.01c0.04,0.01,0.08,0.01,0.11,0.01   c0.37,0,0.69-0.27,0.74-0.64c0.06-0.41-0.22-0.79-0.63-0.85l-1.6-0.24c-0.42-0.05-0.79,0.23-0.85,0.64   C40.31,4.46,40.59,4.84,41,4.9l0.05,0.01l-2.54,15.97c-3.52-1.43-7.66-2.7-11.4-1.66c-2.73,0.76-4.55,2.9-5.4,5.54   c-1,3.12-0.84,6.59-0.43,9.8c0.52,4.11,1.47,8.28,2.9,12.18c0.53,1.43,1.14,2.84,1.79,4.22c0.56,1.19,1.13,2.34,1.47,3.62   c0.68,2.51,0.68,5.14,0.5,7.72c-0.35,5.03-1.64,10.19-0.8,15.24c0.63,3.73,1.4,7.47,2.68,11.04c0.76,2.12,1.69,4.22,2.93,6.11   c1.04,1.57,2.67,4.1,4.9,3.18c1.93-0.79,2.05-3.2,2.21-4.98c0.18-1.96,0.19-3.92,0.27-5.87c0.15-3.96,0.41-8.35,2.81-11.67   c0.32-0.46,0.71-0.86,1.11-1.25c-0.09,3.02,0.17,7.04,0.73,11.14c0.62,5.82,2.72,12.82,5.67,14.75c1.35,0.88,2.13,0.55,2.34,0.49   c3.97-0.98,4.97-9.99,5.21-15.27c0.25-5.41,1.28-9.28,2.48-12.01c1.04,2.08,1.65,4.29,2,6.63c0.45,2.97,0.49,5.99,0.57,8.98   c0.04,1.6,0.08,3.2,0.12,4.79c0.02,1.09-0.09,2.38,0.72,3.24c0.93,1,2.45,1.02,3.62,0.47c1.1-0.52,1.87-1.51,2.55-2.48   c6.98-9.83,5.51-22.34,5.55-33.69c0-1.59-0.01-3.2,0.17-4.78c0.17-1.51,0.69-2.79,1.32-4.15c0.93-2.03,1.74-4.09,2.45-6.2   c1.01-3,1.89-6.06,2.53-9.17c0.6-2.88,1.03-5.85,0.94-8.81C82.95,25.66,82.56,23.12,81.23,21.11z M32.07,90.23   c-1.55-3.57-2.45-7.42-3.15-11.24c-0.34-1.85-0.57-3.67-0.52-5.57c0.06-2.53,0.41-5.05,0.69-7.57c0.57-4.94,0.9-9.99-1.32-14.59   c-1.24-2.56-2.33-5.17-3.1-7.91c-0.52-1.81-0.98-3.65-1.34-5.5c-0.66-3.27-1.09-6.68-0.73-10.02c0.26-2.41,1.09-5.08,3.24-6.44   c2.73-1.72,6.36-1.01,9.24-0.13c1.08,0.32,2.14,0.72,3.19,1.14l-1.44,9.03c-0.49-0.16-0.96-0.3-1.43-0.42   c-3.67-0.91-6.43-0.5-7.97,1.18c-2.5,2.73-0.57,10.45,0.3,12.94c0.01,0.03,0.03,0.07,0.05,0.1c0.05,0.11,0.25,0.59,0.53,1.29   c2.5,6,3.76,12.34,3.76,18.85V90.23z M36.35,81.23l-0.03,15.21c-0.31-0.14-0.6-0.38-0.85-0.66c-0.73-0.81-1.36-1.7-1.92-2.64   c0.01-0.04,0.02-0.08,0.02-0.12V65.37c0-3.46-0.35-6.87-1.04-10.21c0.71-1.16,2.29-0.67,5.57,0.7c2.09,0.87,4.36,1.8,6.55,1.91   c-1,0.93-3.92,4.85-5,6.99C37.52,69.01,36.37,76.26,36.35,81.23z M70.09,65.46v26.51c-0.13,0.22-1.64,2.73-2.51,3.51   c-0.07,0.06-0.15,0.12-0.24,0.17l-0.03-14.42c-0.01-3.66-0.63-8.55-1.81-12.57c-0.22-1.13-0.83-2.5-1.49-3.91   c-0.09-0.17-0.18-0.33-0.27-0.5c0.4,0.04,0.81,0.07,1.22,0.07c1.7,0,5.17-0.65,5.17-0.65C70.11,64.27,70.09,64.86,70.09,65.46z"/><path d="M57.06,18.6c-0.17,0.05-0.36,0.18-0.45,0.34c-0.17,0.33-0.11,0.86,0.27,1.03c1.08,0.48,2.14,1.03,3.18,1.59   c0.34,0.19,0.83,0.1,1.03-0.27c0.18-0.35,0.1-0.83-0.27-1.03c-1.04-0.57-2.1-1.11-3.18-1.59C57.45,18.59,57.27,18.54,57.06,18.6z"/>
                                                    <path style={{fill:"#3799CE"}} d="M51.69,18.23c0.49,0.09,0.98,0.22,1.46,0.36l-5.64,16.77c-0.51-0.1-1.02-0.21-1.55-0.35c-0.4-0.11-0.81,0.14-0.91,0.54
                                                    c-0.1,0.4,0.14,0.81,0.54,0.92c0.49,0.13,0.97,0.23,1.44,0.33l-4.18,12.42c-0.13,0.39,0.08,0.82,0.47,0.95
                                                    c0.08,0.03,0.16,0.04,0.24,0.04c0.31,0,0.61-0.2,0.71-0.51L59.1,5.59l0.11,0.04c0.08,0.03,0.16,0.04,0.24,0.04
                                                    c0.31,0,0.61-0.2,0.71-0.51c0.13-0.39-0.08-0.82-0.47-0.95l-1.65-0.55c-0.39-0.13-0.82,0.08-0.95,0.47
                                                    c-0.13,0.39,0.08,0.82,0.47,0.95l0.11,0.04l-4.05,12.05c-0.51-0.15-1.02-0.28-1.54-0.38c-0.2-0.04-0.4-0.03-0.58,0.08
                                                    c-0.16,0.09-0.3,0.27-0.34,0.45C51.08,17.67,51.27,18.15,51.69,18.23z"/>
                                                    <path style={{fill:"#3799CE"}} d="M42.59,18.04c-0.31,0.23-0.51,0.66-0.27,1.03c0.2,0.31,0.69,0.52,1.03,0.27c0.17-0.13,0.35-0.25,0.54-0.35
                                                    c0.09-0.05,0.19-0.1,0.29-0.15c0.05-0.02,0.1-0.05,0.15-0.07c0.02-0.01,0.04-0.02,0.05-0.02c0.38-0.16,0.77-0.28,1.17-0.38
                                                    c0.27-0.07,0.54-0.11,0.82-0.16L42.28,33.7c-0.55-0.24-1.23-0.55-1.7-0.77c-0.35-0.16-0.62-0.29-0.71-0.32   c-0.38-0.16-0.82,0.02-0.98,0.4c-0.16,0.38,0.02,0.82,0.4,0.98c0.08,0.03,0.33,0.15,0.66,0.3c0.57,0.26,1.36,0.62,1.94,0.88   L37.3,52.61c-0.11,0.4,0.13,0.81,0.53,0.92c0.06,0.02,0.13,0.02,0.19,0.02c0.33,0,0.64-0.22,0.72-0.56L50.14,9.74l0.11,0.03   c0.06,0.02,0.13,0.02,0.19,0.02c0.33,0,0.64-0.22,0.73-0.56c0.1-0.4-0.14-0.81-0.54-0.92l-1.68-0.44c-0.4-0.1-0.81,0.14-0.91,0.54   c-0.1,0.4,0.14,0.81,0.54,0.92l0.11,0.03l-1.92,7.27C45.3,16.83,43.8,17.13,42.59,18.04z"/>
                                                    <circle cx="35.14" cy="91.73" r="0.75"/>
                                                    <circle cx="34.39" cy="88.37" r="0.75"/>
                                                    <circle cx="35.58" cy="85.52" r="0.75"/>
                                                    <circle cx="34.83" cy="81.91" r="0.75"/>
                                                    <circle cx="34.48" cy="78.64" r="0.75"/>
                                                    <circle cx="35.7" cy="75.65" r="0.75"/>
                                                    <circle cx="34.85" cy="72.35" r="0.75"/>
                                                    <circle cx="36.79" cy="68.77" r="0.75"/>
                                                    <circle cx="34.54" cy="65.88" r="0.75"/>
                                                    <circle cx="34.64" cy="61.87" r="0.75"/>
                                                    <circle cx="37.07" cy="59.51" r="0.75"/>
                                                    <circle cx="41.48" cy="58.31" r="0.75"/>
                                                    <circle cx="40.33" cy="61.87" r="0.75"/>
                                                    <circle cx="34.41" cy="56.36" r="0.75"/>
                                                    <circle cx="68.66" cy="91.46" r="0.75"/>
                                                    <circle cx="69.22" cy="87.99" r="0.75"/>
                                                    <circle cx="68.85" cy="84.97" r="0.75"/>
                                                    <circle cx="68.85" cy="80.64" r="0.75"/>
                                                    <circle cx="69.17" cy="77.98" r="0.75"/>
                                                    <circle cx="67.81" cy="74.13" r="0.75"/>
                                                    <circle cx="68.91" cy="71.6" r="0.75"/>
                                                    <circle cx="69.03" cy="68.39" r="0.75"/>
                                                    <circle cx="67.04" cy="65.36" r="0.75"/>
                                                    <circle cx="66.19" cy="67.64" r="0.75"/>
                                                    </svg>

                                                </Avatar>
                                                <Text fw={500} ml={6}>  Gutta-Percha</Text>
                                                    </div>
                                                </Group>
                                                  </Menu.Item>
                                          </Menu.Dropdown>
                                        </Menu>
                              <Tooltip
                                  label="Post & Care"
                                  withArrow
                                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                                    >
                              <Button
                                styles={{
                                  root: {
                                    position: 'relative',
                                    color: 'white',
                                    height: '35px', // Adjust button height
                                    width: '35px',  // Adjust button width
                                    padding: 0,
                                    borderRadius: '0.5rem'
                                  },
                                }}
                              >
                                {/* SVG in the middle */}
                                <div
                            style={{
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              height: '100%',
                              width: '100%',
                            }}
                          >
                              <span  className={
                              activeButton  === "PostCare"
                                ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
                                : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
                            }
                            onClick={() => {
                            handleButtonClick("PostCare");
                            setTargetPath("36");
                          }}
                                >
                    <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" enableBackground="new 0 0 100 100" >
                    <path fillRule="evenodd" clipRule="evenodd" style={{ fill: "#ffffff",   stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10 }}
                    d="M42.369,28.706c-0.77-0.299-1.154-1.164-0.855-1.936  c0.297-0.771,1.164-1.154,1.932-0.855c2.102,0.813,4.326,1.221,6.557,1.221c2.229,0,4.451-0.407,6.553-1.221  c0.77-0.299,1.635,0.085,1.934,0.855c0.297,0.771-0.086,1.637-0.857,1.936c-2.461,0.952-5.049,1.431-7.629,1.431  C47.42,30.137,44.83,29.658,42.369,28.706 M50.002,53.913c-4.559,0-8.25,3.691-8.25,8.248c0,0,0,4.629,0,13.888  c0.002,0.49-0.268,0.941-0.699,1.171c-0.432,0.231-0.959,0.2-1.365-0.078C31.09,71.171,27.705,62.62,29.541,51.485  c0.281-1.714-0.059-2.114-0.846-3.498c-3.738-6.565-4.188-13.826-2.213-20.802c3.131-8.3,9.139-9.625,18.02-3.975  c3.514,1.359,7.484,1.359,10.998,0c8.881-5.65,14.887-4.325,18.021,3.975c1.971,6.976,1.523,14.236-2.215,20.804  c-0.787,1.382-1.131,1.782-0.846,3.496c1.836,11.135-1.549,19.686-10.15,25.656c-0.404,0.278-0.928,0.31-1.363,0.078  c-0.432-0.229-0.701-0.681-0.699-1.171c0-9.259,0-13.888,0-13.888C58.248,57.604,54.555,53.913,50.002,53.913z M8.27,41.257  c-0.828,0-1.5-0.671-1.5-1.499c0-0.83,0.672-1.501,1.5-1.501h9.697c-0.646-6.949,0.725-13.581,3.779-18.395  c1.273-2.01,2.85-3.716,4.703-5.008c1.871-1.305,4.012-2.179,6.396-2.513c4.275-0.6,9.309,0.546,14.934,4.073  c0.717,0.21,1.465,0.318,2.223,0.318c0.756,0,1.504-0.108,2.221-0.318c5.623-3.527,10.656-4.673,14.934-4.073  c2.383,0.334,4.525,1.208,6.396,2.513c1.854,1.292,3.43,2.998,4.703,5.008c3.055,4.813,4.426,11.445,3.779,18.395h9.697  c0.828,0,1.498,0.671,1.498,1.501c0,0.828-0.67,1.499-1.498,1.499H80.355v-0.002c-0.07,0-0.137-0.005-0.205-0.017  c-0.82-0.111-1.393-0.868-1.277-1.686c0.959-6.864-0.217-13.463-3.146-18.086c-1.074-1.692-2.379-3.109-3.885-4.16  c-1.486-1.038-3.191-1.733-5.096-2.001c-3.641-0.509-8.037,0.545-13.078,3.74l-0.002-0.001c-0.105,0.065-0.221,0.12-0.342,0.16  c-1.09,0.352-2.211,0.528-3.322,0.528c-1.078,0-2.16-0.167-3.217-0.494c-0.154-0.038-0.309-0.1-0.453-0.193  c-5.037-3.195-9.436-4.249-13.078-3.74c-1.902,0.268-3.607,0.963-5.096,2.001c-1.506,1.05-2.809,2.468-3.881,4.16  c-2.922,4.603-4.098,11.16-3.162,17.988c0.021,0.098,0.031,0.197,0.031,0.303c0,0.828-0.67,1.499-1.498,1.499H8.27z M75.344,46.601  c0.389-0.075,0.787-0.117,1.195-0.117H95v34.198c0,3.927-3.203,7.128-7.127,7.128H12.129C8.203,87.81,5,84.608,5,80.682V46.483  h18.48c0.4,0,0.795,0.04,1.176,0.115c0.418,0.972,0.895,1.93,1.432,2.874c0.143,0.252,0.443,0.712,0.553,0.988  c0.006,0.153-0.037,0.401-0.061,0.537c-1.941,11.771,1.473,21.723,11.4,28.61c1.32,0.914,3.059,1.015,4.479,0.262  c1.412-0.752,2.293-2.222,2.293-3.821c-0.002-4.631,0-9.261,0-13.888c0-2.902,2.35-5.249,5.25-5.249  c2.898,0,5.248,2.347,5.248,5.249c0,4.627,0,9.257,0,13.888c-0.002,1.6,0.879,3.069,2.293,3.821c1.42,0.753,3.156,0.652,4.479-0.262  c9.926-6.888,13.34-16.839,11.4-28.61c-0.025-0.136-0.066-0.384-0.063-0.535c0.107-0.278,0.41-0.738,0.553-0.99  C74.449,48.53,74.924,47.574,75.344,46.601z"/>
                    </svg>
                                      </span>
                                </div>
                                {/* "CL" in the bottom-right corner */}
                                <span
                                  style={{
                                    position: 'absolute',
                                    bottom: '0px',
                                    right: '0px',
                                    fontSize: '8px',
                                    fontWeight: '800',
                                    backgroundColor: 'white',
                                    // borderRadius:'0.125rem' ,
                                    borderTopLeftRadius: '0.5rem' ,
                                    borderBottomRightRadius: '0.5rem' ,
                                    color:'#3799CE',
                                    padding:'3px  0px 1px 2px' ,
                                  }}
                                  className="h-[14px] w-[14px] "
                                >
                                    Po
                                </span>
                              </Button>
                              </Tooltip>
                        </div>
                </Group>
          </Flex>

          {/* SVG Dentaire - Toutes les 32 dents */}
          {isEnhancedMode ? (
            /* Mode Enhanced avec cases à cocher */
            <div className="enhanced-dental-container p-4  rounded-lg bg-gray-50" >
              <EnhancedDentalSvg
                patientId="current-patient"
                specialty="therapeutic"
                onToothSelectionChange={handleToothSelectionChange}
                onPathSelectionChange={handlePathSelectionChange}
                onTreatmentApply={handleTreatmentApply}
                activeButton={activeButton}
                currentColor={currentColor}
                currentColorTarget={currentColorTarget}
              />
            </div>
          ) : (
            /* Mode normal existant */
            <>
            <div className=" h-full px-0  my-2">
                <Flex style={{ position: 'relative', width: '100%', }} align="center">
                  <div  style={{ position: 'absolute', left: 16 ,top:220}}>
                  <Image src={dentaltop} alt={"dentaltop"} width={32} height={32} />
                    </div>
                </Flex>
                 <div className=" max-w-[920px] mt-2 mx-auto" >
                 <div className=" flex h-full px-0  max-h-[320px] "style={{borderTop:" 1px solid var(--border-color)"}}>
                          {Dantal.map((svgData) => (
                          <div
                            key={svgData.svg_id}
                            className="h-[230px] cursor-pointer hover:bg-[#F2F5F8] parent-div"
                            onClick={() => handleSvgClick(svgData.svg_id)}

                          >
                             <div className="mt-0.5 flex">
                                                        <Text ta="center" h={30} className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE]" style={{ width: svgData.width }}>
                                                          {getToothNumber(svgData.svg_id)}
                                                        </Text>
                                                      </div>
                       {!hiddenSvgIds.includes(svgData.svg_id) ? (
                            <DentalSvg
                              svgData={svgData}
                              isHidingMode={isHidingMode}
                              highlightedPaths={highlightedPaths}
                              onPathClick={onPathClick}
                              isPathSelectionActive={isPathSelectionActive}
                              hiddenPaths={hiddenPaths}
                              isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                              isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                              isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                            />
                          ) : (
                        <div
                          style={{
                            height: '200px', // Match SVG height
                            width: svgData.width, // Match SVG width
                            alignItems: 'center',
                          }}
                        />
                      )}
                  
                    </div>
                          ))}

                   </div>
                  </div>
                  </div>
                  <Divider   variant="dashed" className=" max-w-[980px] mt-2 mx-auto"/>
                  <div className=" h-full px-0 ">
                     <Flex style={{ position: 'relative',  width: '100%', }} align="center">
                  <div  style={{ position: 'absolute', left: 16 ,top:14}}>
                  <Image
                        src={dentalButtom}
                        alt={"dentalButtom"}
                        width={32}
                        height={32}
                      />
                    </div>
                </Flex>
              <div className=" max-w-[920px]  mx-auto">
                  <div className=" flex h-full px-0  max-h-[320px] "style={{borderBottom:" 1px solid var(--border-color)"}}>
                  <div className="flex h-full px-0 max-h-[320px]">
                  {DantalB.map((svgData) => (
                 <div
                                key={svgData.svg_id}
                               className="h-[230px]  cursor-pointer hover:bg-[#F2F5F8] parent-div"
                                onClick={() => handleSvgClick(svgData.svg_id)}
                              >
                               
                   {!hiddenSvgIds.includes(svgData.svg_id) ? (
                     <DentalSvg
                        svgData={svgData}
                        isHidingMode={isHidingMode}
                        highlightedPaths={highlightedPaths}
                        onPathClick={onPathClick}
                        isPathSelectionActive={isPathSelectionActive}
                        hiddenPaths={hiddenPaths}
                        isBrokenRedStrokeActive={brokenRedStrokeSvgs.has(svgData.svg_id)}
                        isGradientEffectActive={gradientEffectSvgs.has(svgData.svg_id)}
                        isGradientBottomEffectActive={gradientBottomEffectSvgs.has(svgData.svg_id)}
                          />
                   ) : (
                     <div style={{ height: '200px', width: svgData.width, alignItems: 'center' }} />
                   )}
  <div className="mt-0.5 flex  ">
                             <Text
                                 ta="center"
                                 h={30}
                                 key={svgData.svg_id}
                                 className="pt-1 text-justify text-[#868e96] hover:bg[#3799CE] child-div"
                                 style={{ width: svgData.width }}
                               >
                                 {getToothNumber(svgData.svg_id)}
                               </Text>
                               </div>
                               </div>
                             ))}
               </div>
                  </div>
                  </div>
                  </div>
            
            </>
          )}
      </div>
    </>
  );
});

Therapeutic13ansetdemi .displayName = 'Therapeutic13ansetdemi ';
