// frontend/dental_medicine/src/components/content/dental/shared/treatmentCompatibility.ts

export interface TreatmentInfo {
  id: string;
  name: string;
  category: string;
  pathIds: string[];
  priority: number; // Plus élevé = affiché au-dessus
  isDestructive: boolean; // Détruit la dent (extraction)
}

export interface TreatmentState {
  svgId: string;
  appliedTreatments: string[];
  conflictingTreatments: string[];
  suggestedSequence: string[];
}

// Définition de tous les traitements disponibles
export const TREATMENTS: Record<string, TreatmentInfo> = {
  // DENTISTERIE ESTHÉTIQUE
  whitening: {
    id: 'whitening',
    name: '<PERSON><PERSON><PERSON>',
    category: 'esthetic',
    pathIds: ['20'],
    priority: 1,
    isDestructive: false
  },
  veneer: {
    id: 'veneer',
    name: 'Facettes',
    category: 'esthetic',
    pathIds: ['21', '37'],
    priority: 3,
    isDestructive: false
  },
  bonding: {
    id: 'bonding',
    name: 'Collage',
    category: 'esthetic',
    pathIds: ['22'],
    priority: 2,
    isDestructive: false
  },

  // DENTISTERIE THÉRAPEUTIQUE
  cleaning: {
    id: 'cleaning',
    name: '<PERSON>toyage',
    category: 'preventive',
    pathIds: ['17'],
    priority: 1,
    isDestructive: false
  },
  fluoride: {
    id: 'fluoride',
    name: 'Fluorure',
    category: 'preventive',
    pathIds: ['18'],
    priority: 1,
    isDestructive: false
  },
  sealant: {
    id: 'sealant',
    name: 'Scellant',
    category: 'preventive',
    pathIds: ['19'],
    priority: 2,
    isDestructive: false
  },
  restoration: {
    id: 'restoration',
    name: 'Restauration',
    category: 'restorative',
    pathIds: ['24', '25', '26', '27'],
    priority: 3,
    isDestructive: false
  },
  root_canal: {
    id: 'root_canal',
    name: 'Canal Radiculaire',
    category: 'endodontic',
    pathIds: ['28', '29', '30', '31', '32', '33', '34', '35'],
    priority: 4,
    isDestructive: false
  },

  // PROSTHODONTIE
  crown: {
    id: 'crown',
    name: 'Couronne',
    category: 'prosthetic',
    pathIds: ['40', '41'],
    priority: 5,
    isDestructive: false
  },
  onlay: {
    id: 'onlay',
    name: 'Onlay',
    category: 'prosthetic',
    pathIds: ['38', '39'],
    priority: 4,
    isDestructive: false
  },
  bridge: {
    id: 'bridge',
    name: 'Bridge',
    category: 'prosthetic',
    pathIds: ['51', '52'],
    priority: 5,
    isDestructive: false
  },
  denture: {
    id: 'denture',
    name: 'Prothèse',
    category: 'prosthetic',
    pathIds: ['48', '49', '50'],
    priority: 5,
    isDestructive: false
  },
  implant: {
    id: 'implant',
    name: 'Implant',
    category: 'prosthetic',
    pathIds: ['54', '55', '56', '57', '58', '59', '60'],
    priority: 6,
    isDestructive: false
  },

  // CHIRURGIE DENTAIRE
  extraction: {
    id: 'extraction',
    name: 'Extraction',
    category: 'surgical',
    pathIds: ['53'],
    priority: 10,
    isDestructive: true
  },
  bone_graft: {
    id: 'bone_graft',
    name: 'Greffe Osseuse',
    category: 'surgical',
    pathIds: ['61', '62', '63', '64', '65', '66'],
    priority: 7,
    isDestructive: false
  },
  implant_surgery: {
    id: 'implant_surgery',
    name: 'Chirurgie Implant',
    category: 'surgical',
    pathIds: ['56', '57', '58'],
    priority: 6,
    isDestructive: false
  },
  sinus_lift: {
    id: 'sinus_lift',
    name: 'Sinus Lift',
    category: 'surgical',
    pathIds: ['62', '63'],
    priority: 7,
    isDestructive: false
  },
  periodontal_surgery: {
    id: 'periodontal_surgery',
    name: 'Chirurgie Parodontale',
    category: 'surgical',
    pathIds: ['64', '65', '66'],
    priority: 5,
    isDestructive: false
  },

  // ORTHODONTIE
  braces: {
    id: 'braces',
    name: 'Brackets',
    category: 'orthodontic',
    pathIds: ['66'],
    priority: 8,
    isDestructive: false
  },
  aligners: {
    id: 'aligners',
    name: 'Gouttières',
    category: 'orthodontic',
    pathIds: ['67'],
    priority: 8,
    isDestructive: false
  },
  retainer: {
    id: 'retainer',
    name: 'Contention',
    category: 'orthodontic',
    pathIds: ['70'],
    priority: 8,
    isDestructive: false
  },
  expander: {
    id: 'expander',
    name: 'Expanseur',
    category: 'orthodontic',
    pathIds: ['72'],
    priority: 8,
    isDestructive: false
  },
  wire_adjustment: {
    id: 'wire_adjustment',
    name: 'Ajustement',
    category: 'orthodontic',
    pathIds: ['74'],
    priority: 8,
    isDestructive: false
  },
  elastic: {
    id: 'elastic',
    name: 'Élastiques',
    category: 'orthodontic',
    pathIds: ['76'],
    priority: 8,
    isDestructive: false
  }
};

// Règles de compatibilité entre traitements
export const COMPATIBILITY_RULES = {
  // Traitements mutuellement exclusifs (un seul à la fois)
  mutuallyExclusive: [
    ['crown', 'denture', 'bridge'], // Une seule prothèse majeure
    ['extraction'], // Extraction exclut tout le reste
    ['braces', 'aligners'], // Un seul type d'orthodontie
    ['onlay', 'crown'], // Onlay ou couronne, pas les deux
  ],

  // Traitements compatibles (peuvent coexister)
  compatible: [
    ['cleaning', 'fluoride', 'sealant'], // Tous les préventifs
    ['whitening', 'cleaning', 'fluoride'], // Esthétique + préventif
    ['root_canal', 'crown'], // Séquence endodontique classique
    ['bone_graft', 'implant'], // Séquence implantaire
    ['extraction', 'bone_graft'], // Extraction + greffe immédiate
    ['veneer', 'whitening'], // Esthétique combinée
    ['restoration', 'crown'], // Restauration temporaire + couronne
  ],

  // Prérequis (A nécessite B)
  prerequisites: {
    crown: ['root_canal'], // Couronne souvent après canal
    implant: ['extraction', 'bone_graft'], // Implant après extraction/greffe
    veneer: ['cleaning'], // Facettes après nettoyage
    bridge: ['crown'], // Bridge nécessite des piliers
  },

  // Séquences recommandées
  sequences: {
    implant_complete: ['extraction', 'bone_graft', 'implant', 'crown'],
    endodontic_complete: ['root_canal', 'crown'],
    esthetic_complete: ['cleaning', 'whitening', 'veneer'],
    preventive_complete: ['cleaning', 'fluoride', 'sealant'],
  }
};
