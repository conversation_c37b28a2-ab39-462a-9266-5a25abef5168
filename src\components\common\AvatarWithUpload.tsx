'use client';

import React, { useState, useEffect } from 'react';
import { Avatar, Box, ActionIcon } from '@mantine/core';
import { IconCamera } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle,  } from '@tabler/icons-react';

interface AvatarWithUploadProps {
  src?: string;
  size?: number;
  radius?: number | string;
  color?: string;
  initials?: string;
  onImageUpload?: (file: File) => Promise<void>;
  imageProps?: React.ComponentPropsWithoutRef<'img'>;
}

/**
 * Avatar component with image upload functionality
 * Displays a camera icon in the bottom left corner that triggers file upload when clicked
 */
const AvatarWithUpload: React.FC<AvatarWithUploadProps> = ({
  src,
  size = 120,
  radius = 120,
  color = 'blue',
  initials = '',
  onImageUpload,
  imageProps = {},
}) => {
  const [uploading, setUploading] = useState(false);
  const [previewSrc, setPreviewSrc] = useState<string | undefined>(src);

  // Update preview when src prop changes
  useEffect(() => {
    setPreviewSrc(src);
  }, [src]);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      if (!event.target.files || event.target.files.length === 0) {
        return;
      }

      const file = event.target.files[0];
      console.log('Selected file:', file.name, file.type, file.size);

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        notifications.show({
          title: 'Error',
          message: 'Please select an image file',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      // Check file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        notifications.show({
          title: 'Error',
          message: 'Image file is too large. Maximum size is 5MB.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }

      // Create a preview of the image
      const objectUrl = URL.createObjectURL(file);
      setPreviewSrc(objectUrl);

      setUploading(true);

      try {
        // Call the provided upload handler
        if (onImageUpload) {
          await onImageUpload(file);
        } else {
          console.warn('No image upload handler provided');
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        console.error('Error uploading image:', error);
        notifications.show({
          title: 'Error',
          message: error.message || 'Failed to upload image. Please try again.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } finally {
        setUploading(false);

        // Reset the file input
        event.target.value = '';
      }

      // Note: We don't revoke the objectUrl here because we want to keep showing the preview
      // The browser will clean it up when the component unmounts
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('Error handling image upload:', error);
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  // Generate a unique ID for the file input
  const inputId = `avatar-upload-${Math.random().toString(36).substring(2, 9)}`;

  // Add a cleanup effect to revoke object URLs when component unmounts
  useEffect(() => {
    return () => {
      // Only revoke if it's an object URL (starts with blob:)
      if (previewSrc && previewSrc.startsWith('blob:')) {
        URL.revokeObjectURL(previewSrc);
      }
    };
  }, [previewSrc]);

  return (
    <Box pos="relative">
      <Avatar
        size={size}
        radius={radius}
        color={color}
        src={previewSrc}
        imageProps={{
          onError: (e) => {
            // If image fails to load, show initials instead
            console.log('Image failed to load, showing initials instead');
            e.currentTarget.src = '';
          },
          crossOrigin: "anonymous",
          ...imageProps
        }}
      >
        {initials}
      </Avatar>

      {/* Camera icon for image upload */}
      <ActionIcon
        variant="filled"
        color="blue"
        radius="xl"
        size="md"
        pos="absolute"
        bottom={0}
        left={0}
        loading={uploading}
        onClick={() => document.getElementById(inputId)?.click()}
        style={{ zIndex: 10 }}
      >
        <IconCamera size={16} />
      </ActionIcon>

      {/* Hidden file input */}
      <input
        id={inputId}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleImageUpload}
      />
    </Box>
  );
};

export default AvatarWithUpload;
