import { Select } from '@mantine/core';

interface Physician {
  id: number;
  full_name: string;
}

interface PhysicianSelectProps {
  value: number | null;
  onChange: (id: number | null) => void;
  physicians: Physician[];
  readOnly?: boolean;
  required?: boolean;
}

export function PhysicianSelect({
  value,
  on<PERSON><PERSON><PERSON>,
  physicians,
  readOnly = false,
  required = true,
}: PhysicianSelectProps) {
  return (
    <Select
      label="Docteur"
      placeholder="Sélectionner un médecin"
      data={physicians.map((p) => ({
        value: p.id.toString(),
        label: p.full_name,
      }))}
      value={value?.toString() ?? null}
      onChange={(val) => onChange(val ? parseInt(val) : null)}
      disabled={readOnly}
      required={required}
      withinPortal
      searchable
      nothingFoundMessage="Aucun médecin"
    />
  );
}
