// frontend/dental_medicine/src/components/content/dental/shared/DentalSvgWrapper.tsx

import React, { useMemo } from 'react';
import { DentalSvg } from '@/components/TDentalSvgMin';
import { Dan<PERSON>, DantalB } from '../../../../utils/Tdantal';
import { SVGPathStyle } from './types';

interface DentalSvgWrapperProps {
  svgId: string;
  hiddenPaths: Record<string, boolean>;
  highlightedPaths: Record<string, SVGPathStyle>;
  onSvgClick: (svgId: string) => void;
  isSelected?: boolean;
  isMultiSelectMode?: boolean;
}

export const DentalSvgWrapper: React.FC<DentalSvgWrapperProps> = ({
  svgId,
  hiddenPaths,
  highlightedPaths,
  onSvgClick,
  isSelected = false,
  isMultiSelectMode = false
}) => {

  // Trouver les données SVG correspondantes à l'ID
  const svgData = useMemo(() => {
    const allSvgData = [...Dantal, ...DantalB];
    return allSvgData.find(svg => svg.svg_id === svgId);
  }, [svgId]);

  // Gestionnaire de clic sur path
  const handlePathClick = (pathId: string, svgId: string) => {
    console.log(`🦷 Clic sur path ${pathId} du SVG ${svgId}`);
    // Ici on pourrait ajouter une logique spécifique si nécessaire
  };

  // Si aucune donnée SVG trouvée, afficher un placeholder
  if (!svgData) {
    console.warn(`⚠️ Aucune donnée SVG trouvée pour l'ID: ${svgId}`);
    return (
      <div
        className="h-[200px] w-[100px] bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer"
        onClick={() => onSvgClick(svgId)}
      >
        <div className="text-center">
          <div className="text-gray-400 text-sm">SVG {svgId}</div>
          <div className="text-gray-400 text-xs">Non trouvé</div>
        </div>
      </div>
    );
  }

  // Classes CSS dynamiques pour la sélection
  const containerClasses = [
    "cursor-pointer",
    "transition-all duration-200",
    isSelected
      ? "bg-blue-100 border-2 border-blue-500 rounded-lg shadow-md"
      : "hover:bg-[#F2F5F8]",
    isMultiSelectMode && !isSelected
      ? "hover:border-2 hover:border-blue-300 hover:rounded-lg"
      : ""
  ].filter(Boolean).join(" ");

  return (
    <div
      className={containerClasses}
      onClick={() => onSvgClick(svgId)}
      title={isMultiSelectMode ? `Dent ${svgId} - Cliquez pour ${isSelected ? 'désélectionner' : 'sélectionner'}` : `Dent ${svgId}`}
    >
      <DentalSvg
        svgData={svgData}
        isHidingMode={false}
        highlightedPaths={highlightedPaths}
        onPathClick={handlePathClick}
        isPathSelectionActive={true}
        hiddenPaths={hiddenPaths}
        isBrokenRedStrokeActive={false}
        isGradientEffectActive={false}
        isGradientBottomEffectActive={false}
      />
    </div>
  );
};

export default DentalSvgWrapper;
