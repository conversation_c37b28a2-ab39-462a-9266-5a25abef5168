// frontend/dental_medicine/src/components/content/dental/specialties/TherapeuticDentistry/TherapeuticTab.tsx

import React, { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Tabs, rem, Button, Group, Text, Dialog, Menu, Container, Flex } from '@mantine/core';
import { IconMedicalCross, IconSettings, IconPhoto, IconSquareRoundedPlusFilled } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { DentalSpecialtyTabProps, SaveManagerRef, ModificationState, SVGPathStyle } from '../../shared/types';
import { useSaveManager } from '../../shared/SaveManager';
import TherapeuticControls from './TherapeuticControls';
import TherapeuticProcedures from './TherapeuticProcedures';
import { DentalSvgWrapper } from '../../shared/DentalSvgWrapper';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/utils/Tdantal';

export const TherapeuticTab = forwardRef<SaveManagerRef, DentalSpecialtyTabProps>(({
  onModificationChange,

}, ref) => {

  // États locaux pour la thérapie
  const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>({});
  const [highlightedPaths, ] = useState<Record<string, SVGPathStyle>>({});
  const [clickedIds, ] = useState<string[]>([]);
  const [activeButton, setActiveButton] = useState<string>('');
  const [targetPath, setTargetPath] = useState<string>('');
  const [opened, {  close }] = useDisclosure(false);

  // État de modification pour cette spécialité
  const modificationState: ModificationState = {
    hiddenPaths,
    highlightedPaths,
    clickedIds,
    activeButton,
    targetPath
  };

  // Gestionnaire de sauvegarde
  const { save, hasChanges, SaveManagerComponent } = useSaveManager(
    modificationState,
    onModificationChange,
    'therapeutic'
  );

  // Exposer les méthodes via ref
  useImperativeHandle(ref, () => ({
     triggerSave: async () => {
       await save();
     },
     hasUnsavedChanges: () => hasChanges()
   }), [save, hasChanges]);

  // Gestionnaire de clic sur SVG
  const handleSvgClick = useCallback((svgId: string) => {
    const togglePathVisibility = (pathId: string, visible: boolean) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths(prev => {
        const newHiddenPaths = { ...prev, [key]: visible };

        // Sauvegarder automatiquement si onModificationChange est disponible
        if (onModificationChange) {
          onModificationChange(svgId, pathId, !visible, highlightedPaths)
            .catch(error => console.error('Erreur sauvegarde thérapeutique:', error));
        }

        return newHiddenPaths;
      });
    };

    // Appliquer le traitement selon le bouton actif
    if (activeButton) {
      console.log(`💊 Application du traitement ${activeButton} sur la dent ${svgId}`);

      switch (activeButton) {
        case 'cleaning':
          console.log('🧽 Application de nettoyage/détartrage');
          togglePathVisibility('17', false);
          break;
        case 'fluoride':
          console.log('💧 Application de fluorure');
          togglePathVisibility('18', false);
          break;
        case 'sealant':
          console.log('🔒 Application de scellant');
          togglePathVisibility('19', false);
          break;
        case 'restoration':
          console.log('🔧 Application de restauration');
          if (targetPath) {
            togglePathVisibility(targetPath, false);
          }
          break;
        case 'root_canal':
          console.log('🦷 Application de traitement de canal');
          if (targetPath) {
            togglePathVisibility(targetPath, false);
          }
          break;
        default:
          if (targetPath) {
            togglePathVisibility(targetPath, false);
          }
          break;
      }
    }
  }, [activeButton, targetPath, onModificationChange, highlightedPaths]);

  // Gestionnaire de changement de bouton
  const handleButtonClick = useCallback((buttonId: string) => {
    setActiveButton(buttonId);
    console.log(`💊 Bouton thérapeutique activé: ${buttonId}`);
  }, []);

  // Gestionnaire de changement de path cible
  const handleTargetPathChange = useCallback((pathId: string) => {
    setTargetPath(pathId);
    console.log(`🎯 Path cible thérapeutique: ${pathId}`);
  }, []);

  return (
    <>
      {/* Composant de sauvegarde invisible */}
      {SaveManagerComponent}

      <div className="my-4">
        <Container>
          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px' }} align="center">
            <div style={{ position: 'absolute', right: 16 }}>
              <Menu withinPortal position="bottom-end" shadow="sm">
                <Menu.Target>
                  <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                    Procédures Thérapeutiques
                  </Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item>Détartrage</Menu.Item>
                  <Menu.Item>Obturation</Menu.Item>
                  <Menu.Item>Traitement de canal</Menu.Item>
                  <Menu.Item>Application fluorée</Menu.Item>
                  <Menu.Item>Scellement</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </div>

            {/* Contrôles spécifiques à la thérapie */}
            <TherapeuticControls
              activeButton={activeButton}
              onButtonClick={handleButtonClick}
              onTargetPathChange={handleTargetPathChange}
            />
          </Flex>

          {/* SVG Dentaire - Toutes les 32 dents */}
          <div className="dental-svg-container">
            <div className="max-w-[920px] mt-2 mx-auto">
              {/* Mâchoire supérieure (dents 1-16) */}
              <div className="flex h-full px-0 max-h-[160px] mb-2">
                {Dantal.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                  />
                ))}
              </div>
              {/* Mâchoire inférieure (dents 17-32) */}
              <div className="flex h-full px-0 max-h-[160px]">
                {DantalB.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                  />
                ))}
              </div>
            </div>
          </div>
        </Container>

        {/* Onglets de procédures */}
        <div className="border-base-200 mx-4 border-t mt-4">
          <Tabs variant="unstyled" defaultValue="All Procedures">
            <Tabs.List grow className="space-x-0 gap-0 mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={<IconMedicalCross style={{ width: rem(16), height: rem(16) }} />}
              >
                Toutes les Procédures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
              >
                Planifiées
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={<IconPhoto style={{ width: rem(16), height: rem(16) }} />}
              >
                Terminées
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="All Procedures">
              <TherapeuticProcedures
                type="all"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Planned">
              <TherapeuticProcedures
                type="planned"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Completed">
              <TherapeuticProcedures
                type="completed"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>
          </Tabs>
        </div>

        {/* Dialog de sauvegarde */}
        <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
            <Text size="sm" mb="xs" fw={500}>
              Sauvegarder les Modifications Thérapeutiques
            </Text>
          </Group>
          <Group align="flex-end">
            <Button
              w="100%"
              onClick={async () => {
                await save();
                close();
              }}
              className="hover:bg-[#3799CE]/90"
            >
              Sauvegarder
            </Button>
          </Group>
        </Dialog>
      </div>
    </>
  );
});

TherapeuticTab.displayName = 'TherapeuticTab';
