import { Modal, Table, Button, Text ,Group,TextInput,List} from '@mantine/core';

import { useState } from 'react';
import { TbHours12 } from "react-icons/tb";
import { AiOutlinePlus } from "react-icons/ai";
import { RiCloseFill } from "react-icons/ri";
import { useDisclosure } from '@mantine/hooks';
import { Drawer} from '@mantine/core';
import { IconSearch, } from '@tabler/icons-react';
import { PiListThin } from "react-icons/pi";

import { IconArrowBigRightLineFilled } from '@tabler/icons-react';

// import { AlertsDropdown } from './components/AlertsDropdown';
import { AlertsDropdown } from './AlertsDropdown';
// import { AlertModal, AlertData } from './components/AlertModal';
import { AlertModal, AlertData } from './AlertModal';
import { notifications } from '@mantine/notifications';

import { AddAlertModal } from './AddAlertModal';
import {  IconPlus } from '@tabler/icons-react';
import { CloseButton } from '@mantine/core';
import {  ThemeIcon } from '@mantine/core';
import {  IconCircleDashed } from '@tabler/icons-react';
import { IoArrowForwardOutline } from "react-icons/io5";
interface AlertsModalProps {
  opened: boolean;
  onClose: () => void;
  title?: string;
}
const elements = [
  { position: 6, mass: 12.011, symbol: 'C', name: 'Carbon' },
  { position: 7, mass: 14.007, symbol: 'N', name: 'Nitrogen' },
  { position: 39, mass: 88.906, symbol: 'Y', name: 'Yttrium' },
  { position: 56, mass: 137.33, symbol: 'Ba', name: 'Barium' },
  { position: 58, mass: 140.12, symbol: 'Ce', name: 'Cerium' },
];

export function AlertsModal({ opened, onClose, title = "Alerts - ABADI SQUAD" }: AlertsModalProps) {
  const rows = elements.map((element) => (
    <Table.Tr key={element.name}>
      <Table.Td style={{textAlign: "left",paddingLeft: "15px",width: '30%'}}>{element.position}</Table.Td>
      <Table.Td style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}>{element.name}</Table.Td>
      <Table.Td style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}>{element.symbol}</Table.Td>
      <Table.Td style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}>{element.mass}</Table.Td>
      <Table.Td style={{textAlign: "left",paddingLeft: "15px",width: '30%'}}>{element.mass}</Table.Td>
      <Table.Td style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}></Table.Td>
    </Table.Tr>
  ));
  // Drawer
const [Draweropened, { open:openDrawer,close:closeDrawer }] = useDisclosure(false);
// Add this to your component's state
const [patientAlerts, setPatientAlerts] = useState<string[]>([]);
const [alertSearch, setAlertSearch] = useState('');
// Add this import at the top of your file


// Add these state variables to your component
const [alertModalOpened, setAlertModalOpened] = useState(false);
const [alertOpened, setAlertOpened] = useState(false);
const doctors = [
  { value: 'MEDECIN YOUSSEF', label: 'MEDECIN YOUSSEF' },
  { value: 'dr.Kader', label: 'dr.Kader' },
  { value: 'dr.Kaders', label: 'dr.Kaders' },
];
const triggers = [
  { value: 'salle_attente', label: 'Salle d\'attente' },
  { value: 'retard', label: 'Retard' },
  { value: 'urgence', label: 'Urgence' },
];
// Add this function to handle saving alerts
const handleSaveAlert = (alertData: AlertData) => {
  console.log('Alert saved:', alertData);
  // Here you would typically save the alert to your backend
  notifications.show({
    title: 'Alerte créée',
    message: `Alerte pour ${alertData.doctor} a été créée`,
    color: 'blue',
  });
};



// Add this function to open the alert modal
const openAddAlertModal = () => setAlertModalOpened(true);
const closeAddAlertModal = () => setAlertModalOpened(false);
  return (
<>
    <Modal.Root opened={opened} onClose={onClose} size="xl" centered>
      <Modal.Overlay />
      <Modal.Content>
          <Modal.Header style={{ minHeight: "30px", background: "#3799CE", padding: "0px 11px", color:"white" }}>
          <Modal.Title >
            <Group justify="space-between" >
            <div className="flex items-center gap-2 ">
              <TbHours12 size={20} />
              <Text fw={500}>{title}</Text>
            </div>
            </Group>
          </Modal.Title>
           <div  className="flex  gap-2 ">
            < AiOutlinePlus size={20} className="mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96] hover:bg-[var(--mantine-color-white)] rounded-sm"

            onClick={() => setAlertOpened(true)}
            />
            <RiCloseFill size={20} className="mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96] hover:bg-[var(--mantine-color-white)] rounded-sm"
            onClick={() => onClose()}
            />
            </div>
        </Modal.Header>
        <Modal.Body p={8}>
        <Table striped highlightOnHover withTableBorder withColumnBorders horizontalSpacing="md">
      <Table.Thead style={{borderBottom: "2px solid #3799CE"}}>
        <Table.Tr>
          <Table.Th  style={{textAlign: "left",paddingLeft: "15px",width: '30%'}} >Déclencheur</Table.Th>
          <Table.Th style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}>Niveau</Table.Th>
          <Table.Th style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}>Pub...</Table.Th>
          <Table.Th style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}>Per...</Table.Th>
          <Table.Th style={{textAlign: "left",paddingLeft: "15px",width: '30%'}}>Description</Table.Th>
          <Table.Th style={{textAlign: "left",paddingLeft: "15px",width: '10%'}}></Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>{rows}</Table.Tbody>
      
    </Table>
    Aucun élément trouvé
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>
{/* Drawer opened */}
    <Drawer offset={8} radius="md" opened={Draweropened} onClose={closeDrawer} withCloseButton={false} position="right" size="xs" bg={"blue"}>
    
          <Group justify="space-between">
               <TextInput
                  placeholder="Rechercher"
                  mb="md"
                  leftSection={<IconSearch size={16} stroke={1.5} />}
                  value={alertSearch}
                  onChange={(e) => setAlertSearch(e.currentTarget.value)}
                  className="mt-4"
                  rightSection={
                    <CloseButton
                      size={20} 
                      aria-label="Clear input"
                      onClick={() => setAlertSearch('')}
                      style={{ display: alertSearch ? undefined : 'none' }}
                    />
                  }
               w={"210px"}
                /> 
         
          <div  className="flex  gap-2 ">
            <PiListThin size={20} className="mantine-focus-always text-[#3799CE] hover:text-[var(--mantine-color-white)] hover:bg-[#3799CE] rounded-sm"
            onClick={() => setAlertOpened(true)}
            />
            <IconArrowBigRightLineFilled size={20} className="mantine-focus-always text-[#3799CE] hover:text-[var(--mantine-color-white)] hover:bg-[#3799CE] rounded-sm"
            onClick={() => closeDrawer()}
            />
            </div>
                </Group>

{/* Add the medical alerts dropdown here */}
<div className="flex gap-4 mb-2">
  <AlertsDropdown
    value={patientAlerts}
    onChange={setPatientAlerts}
    label="Alertes médicales"
  />
</div>
        

         
        <List
      spacing="xs"
      className='w-[272px] py-6'
      size="sm"
      center
      icon={
        <ThemeIcon color="teal" size={24} radius="xl">
          <IoArrowForwardOutline size={16}/>
        </ThemeIcon>
      }
    >
      <List.Item>Allaitante depuis: (Breastfeeding since:)</List.Item>
      <List.Item>Allergique à l&apos;Aspirine (Allergic to Aspirin)</List.Item>
      <List.Item>Allergique à la Pénicilline (Allergic to Penicillin)</List.Item>
      <List.Item>Arthrose (Osteoarthritis)</List.Item>
          <List.Item>Cardiaque Anticoagulant sintro...</List.Item>
          <List.Item>Cardiaque prothèse valvulaire (Cardiac valve prosthesis)</List.Item>
          <List.Item>Cardiaque trouble du rythme (Cardiac rhythm disorder)</List.Item>
          <List.Item>Diabétique ID</List.Item>
          <List.Item>Diabétique NID</List.Item>
          <List.Item>Enceinte depuis: (Pregnant since:)</List.Item>
          <List.Item>Gastralgie : ulcère anti-inflamm... (Gastralgia: anti-inflammatory ulcer)</List.Item>
          <List.Item>Hypertension</List.Item>
          <List.Item>Hypotension</List.Item>
          <List.Item>Thyroïde (Thyroid)</List.Item>
      <List.Item
        icon={
          <ThemeIcon color="blue" size={24} radius="xl">
            <IconCircleDashed size={16} />
          </ThemeIcon>
        }
      >
        Submit a pull request once you are done
      </List.Item>
    </List>
    <Button 
            variant="filled" 
            color="#3799CE" 
            leftSection={<IconPlus size={16} />}
            onClick={openAddAlertModal}
            w={"100%"}
          >
           Ajouter une alerte médicale
          </Button>
      </Drawer>

      <AlertModal
  opened={alertOpened}
  onClose={() => setAlertOpened(false)}
  onSave={handleSaveAlert}
  doctors={doctors}
  triggers={triggers}
/>
<AddAlertModal
  opened={alertModalOpened}
  onClose={closeAddAlertModal}
  onAddAlert={(alert) => {
    setPatientAlerts([...patientAlerts, alert]);
    notifications.show({
      title: 'Alerte ajoutée',
      message: `L'alerte "${alert}" a été ajoutée`,
      color: 'blue',
    });
  }}
  existingAlerts={patientAlerts}
/>
    </>
  );
}

// Example usage
export function AlertsButton() {
  const [opened, setOpened] = useState(false);

  return (
    <>
      <Button onClick={() => setOpened(true)} color="blue">
        Show Alerts
      </Button>
      <AlertsModal opened={opened} onClose={() => setOpened(false)} />
    </>
  );
}