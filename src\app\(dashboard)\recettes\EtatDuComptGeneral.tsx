'use client';
import React, { useState } from 'react';
// Import du composant Nouvel_encaissement_modal
import Nouvel_encaissement_modal from './Nouvel_encaissement_modal';
import {
  Box,
  Card,
  Group,
  Button,
  TextInput,
  Table,
  Text,

  ActionIcon,
  Radio,
  Select,
  Switch,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconChevronDown,
  IconChevronUp,
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconMenu2,
} from '@tabler/icons-react';

// Interface pour les données de compte
interface CompteData {
  id: number;
  nomComplet: string;
  montantDu: number;
  montantEncaisse: number;
  resteARegler: number;
}

const EtatDuComptGeneral = () => {
  // États pour les filtres
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('Patient');
  const [afficherComptesDebiteurs, setAfficherComptesDebiteurs] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Données d'exemple pour les comptes patients
  const comptesPatients: CompteData[] = [
    {
      id: 1,
      nomComplet: 'Mr OUARHOU ANIS',
      montantDu: 2000.00,
      montantEncaisse: 1500.00,
      resteARegler: 500.00,
    },
    {
      id: 2,
      nomComplet: 'OUARHOU TEST',
      montantDu: 3000.00,
      montantEncaisse: 0.00,
      resteARegler: 3000.00,
    },
    {
      id: 3,
      nomComplet: 'CHOUMITA IDRISS',
      montantDu: 1200.00,
      montantEncaisse: 0.00,
      resteARegler: 1200.00,
    },
    {
      id: 4,
      nomComplet: 'TEST DEMO',
      montantDu: 3560.00,
      montantEncaisse: 200.00,
      resteARegler: 3360.00,
    },
    {
      id: 5,
      nomComplet: 'FALAHI MOHAMED',
      montantDu: 69200.00,
      montantEncaisse: 0.00,
      resteARegler: 69200.00,
    },
    {
      id: 6,
      nomComplet: 'EL KANBI HAMZA',
      montantDu: 26400.00,
      montantEncaisse: 23400.00,
      resteARegler: 3000.00,
    },
    {
      id: 7,
      nomComplet: 'FATNA CHANGHITI',
      montantDu: 19400.00,
      montantEncaisse: 900.00,
      resteARegler: 18500.00,
    },
    {
      id: 8,
      nomComplet: 'DARIF AMINE',
      montantDu: 20700.00,
      montantEncaisse: 9500.00,
      resteARegler: 11200.00,
    },
    {
      id: 9,
      nomComplet: 'IDRISS CHOUMITA',
      montantDu: 2100.00,
      montantEncaisse: 800.00,
      resteARegler: 1300.00,
    },
    {
      id: 10,
      nomComplet: 'EL KANBI YAHYA',
      montantDu: 33300.00,
      montantEncaisse: 0.00,
      resteARegler: 33300.00,
    },
  ];

  // Données d'exemple pour les organismes/tiers payants
  const comptesOrganismes: CompteData[] = [
    {
      id: 1,
      nomComplet: 'CNSS',
      montantDu: 45000.00,
      montantEncaisse: 35000.00,
      resteARegler: 10000.00,
    },
    {
      id: 2,
      nomComplet: 'CNOPS',
      montantDu: 32000.00,
      montantEncaisse: 28000.00,
      resteARegler: 4000.00,
    },
    {
      id: 3,
      nomComplet: 'RAMED',
      montantDu: 15000.00,
      montantEncaisse: 12000.00,
      resteARegler: 3000.00,
    },
    {
      id: 4,
      nomComplet: 'Mutuelle Générale',
      montantDu: 25000.00,
      montantEncaisse: 20000.00,
      resteARegler: 5000.00,
    },
    {
      id: 5,
      nomComplet: 'Assurance Privée A',
      montantDu: 18000.00,
      montantEncaisse: 15000.00,
      resteARegler: 3000.00,
    },
    {
      id: 6,
      nomComplet: 'Assurance Privée B',
      montantDu: 22000.00,
      montantEncaisse: 18000.00,
      resteARegler: 4000.00,
    },
  ];

  // Sélectionner les données selon le type de filtre
  const comptesData = filterType === 'Patient' ? comptesPatients : comptesOrganismes;

  // Filtrer les données selon les critères
  const filteredComptes = comptesData.filter(compte => {
    const matchesSearch = compte.nomComplet.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDebiteurs = !afficherComptesDebiteurs || compte.resteARegler > 0;
    return matchesSearch && matchesDebiteurs;
  });

  // Calcul de la pagination
  const totalPages = Math.ceil(filteredComptes.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentComptes = filteredComptes.slice(startIndex, startIndex + itemsPerPage);

  // Calcul du total de la balance
  const totalBalance = filteredComptes.reduce((sum, item) => sum + item.resteARegler, 0);

  // Fonction de tri
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Rendu de l'icône de tri
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <IconChevronUp size={14} /> : <IconChevronDown size={14} />;
  };
  const [isNouvelEncaissementModalOpen, setIsNouvelEncaissementModalOpen] = useState(false);
  return (
    <Box className="h-screen flex flex-col bg-gray-50 w-full" >
      {/* Header */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          {/* <Group align="center" gap="sm">
            <Text size="sm" fw={500} className="text-white">
              💰 Recettes
            </Text>
          </Group> */}
          <Group gap="sm">
            <Button
                       size="sm"
                       variant="filled"
                       color="blue"
                       leftSection={<IconPlus size={16} />}
                       className="bg-blue-500 hover:bg-blue-600"
                       onClick={() => setIsNouvelEncaissementModalOpen(true)}
                     >
                       Nouvel encaissement
                     </Button>
          </Group>
        </Group>
      </Card>

      {/* Tabs */}
      

      {/* Filtres */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group gap="md" align="end">
          {/* Recherche */}
          <TextInput
            placeholder="Rechercher"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            leftSection={<IconSearch size={16} />}
            className="flex-1 max-w-xs"
            size="sm"
          />

          {/* Radio buttons pour Patient/Organisme */}
          <Radio.Group
            value={filterType}
            onChange={setFilterType}
            size="sm"
          >
            <Group gap="md">
              <Radio value="Patient" label="Patient" />
              <Radio value="Organisme/Tiers payant" label="Organisme/Tiers payant" />
            </Group>
          </Radio.Group>

          {/* Toggle pour afficher les comptes débiteurs */}
          <Group gap="xs" align="center">
            <Switch
              checked={afficherComptesDebiteurs}
              onChange={(event) => setAfficherComptesDebiteurs(event.currentTarget.checked)}
              size="sm"
              color="blue"
            />
            <Text size="sm" className="text-gray-700">
              Afficher les comptes débiteurs
            </Text>
          </Group>

          {/* Balance */}
          <Group gap="xs" align="center" className="ml-auto">
            <Text size="sm" fw={500} className="text-gray-700">
              Balance :
            </Text>
            <Text size="sm" fw={700} className="text-red-600">
              {totalBalance.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </Text>
          </Group>
        </Group>
      </Card>

      {/* Tableau des comptes */}
      <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer"
                onClick={() => handleSort('nomComplet')}
              >
                <Group gap="xs" justify="space-between">
                  {filterType === 'Patient' ? 'Nom complet' : 'Organisme'}
                  {renderSortIcon('nomComplet')}
                </Group>
              </Table.Th>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer text-right"
                onClick={() => handleSort('montantDu')}
              >
                <Group gap="xs" justify="space-between">
                  Montant dû
                  {renderSortIcon('montantDu')}
                </Group>
              </Table.Th>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer text-right"
                onClick={() => handleSort('montantEncaisse')}
              >
                <Group gap="xs" justify="space-between">
                  Montant encaissé
                  {renderSortIcon('montantEncaisse')}
                </Group>
              </Table.Th>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer text-right"
                onClick={() => handleSort('resteARegler')}
              >
                <Group gap="xs" justify="space-between">
                  Reste à régler
                  {renderSortIcon('resteARegler')}
                </Group>
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm text-center">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentComptes.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={5} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentComptes.map((compte) => (
                <Table.Tr key={compte.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {compte.nomComplet}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {compte.montantDu.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {compte.montantEncaisse.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className={compte.resteARegler > 0 ? "text-red-600" : "text-gray-800"}>
                      {compte.resteARegler.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-center">
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                    >
                      <IconMenu2 size={14} />
                    </ActionIcon>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group gap="sm" align="center">
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(1)}
            >
              <IconChevronsLeft size={14} />
            </ActionIcon>
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              <IconChevronLeft size={14} />
            </ActionIcon>
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              <IconChevronRight size={14} />
            </ActionIcon>
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(totalPages)}
            >
              <IconChevronsRight size={14} />
            </ActionIcon>
          </Group>

          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">Page</Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">Lignes par Page</Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 10)}
              data={['10', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              {startIndex + 1} - {Math.min(startIndex + itemsPerPage, filteredComptes.length)} de {filteredComptes.length}
            </Text>
            <Text size="sm" className="text-gray-600">K</Text>
          </Group>
        </Group>
      </Card>
      {/* Modale pour le nouvel encaissement */}
          <Nouvel_encaissement_modal
            opened={isNouvelEncaissementModalOpen}
            onClose={() => setIsNouvelEncaissementModalOpen(false)}
          />
    </Box>
  );
};

export default EtatDuComptGeneral;
