
"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import React from "react";
import { rem } from "@mantine/core";
import MetaSeo from"./MetaSeo"
import Icon from '@mdi/react';
import { mdiCalendarBlankMultiple } from '@mdi/js';
import {
  Container,
  Paper,
  Title,
  Text,
  Group,
  Loader,
  
} from '@mantine/core';
import { IconMessage, IconUsers } from '@tabler/icons-react';
import authService from '@/services/authService';
import SpecialtyMessaging from '@/components/SpecialtyMessaging';

// Define a simplified user interface for our component
interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  user_type: string;
  specialization?: string;
}
import "~/styles/tab.css";

function  AppointmentsPage() {
  const iconStyle = { width: rem(14), height: rem(14) };
  const [toggleState, setToggleState] = useState(1);
 const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      try {
        // Check if token exists in localStorage
        const token = localStorage.getItem('token');
        console.log('Current auth token:', token ? 'exists' : 'missing');

        if (!token) {
          console.log('No token found, redirecting to login');
          router.push('/login');
          return;
        }

        // Get user type from localStorage
        const userType = localStorage.getItem('userType') || 'doctor';

        // Set a default user based on user type
        const defaultUser: {
          id: string;
          first_name: string;
          last_name: string;
          email: string;
          user_type: string;
          specialization?: string;
        } = {
          id: '1',
          first_name: userType === 'doctor' ? 'Doctor' : userType === 'assistant' ? 'Assistant' : 'Staff',
          last_name: '',
          email: userType === 'doctor' ? '<EMAIL>' : '<EMAIL>',
          user_type: userType
        };

        // Add specialty information if available
        if (userType === 'doctor') {
          const specialty = localStorage.getItem('specialty');
          if (specialty) {
            defaultUser.specialization = specialty;
          }
        }

        setUser(defaultUser);

        // Try to fetch the actual user profile, but don't block on it
        try {
          const userProfile = await authService.getProfile();
          setUser(userProfile);
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
          // Continue with the default user
        }
      } catch (error) {
        console.error('Authentication error:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return (
      <Container size="xl" py="xl">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <Loader size="xl" />
        </div>
      </Container>
    );
  }

  // Only doctors should access this page
  if (user?.user_type !== 'doctor') {
    return (
      <Container size="xl" py="xl">
        <Paper p="xl" radius="md" withBorder>
          <Title order={3}>Access Restricted</Title>
          <Text mt="md">
            The messaging feature is only available to doctors. Please contact your administrator if you believe this is an error.
          </Text>
        </Paper>
      </Container>
    );
  }

const icons = [
  { icon: <IconUsers style={iconStyle} key="SpecialtyColleagues" />, label: "Specialty Colleagues" },
  {
    icon: <IconMessage style={iconStyle} key="AllMessages" />,
    label: "All Messages",
  },
  {
    icon: <Icon path={mdiCalendarBlankMultiple} size={1}key="Test-2" />,
    label: "Test-2",
  },
  {
    icon: <Icon path={mdiCalendarBlankMultiple} size={1} key="Test-3" />,
    label: "Test-3",
  },
 
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (  <Paper p="xl" radius="md" withBorder mb="xl" w={"100%"}>
              <Group justify="space-between" mb="md">
                <div>
                  <Title order={2}>Messaging</Title>
                  <Text c="dimmed">Communicate with other doctors in your specialty</Text>
                  {user?.specialization && (
                    <Text size="sm" c="dimmed">Specialty: {user.specialization}</Text>
                  )}
                </div>
              </Group>
               <SpecialtyMessaging
                          specialtyId={user?.specialization}
                          currentDoctorId={user?.id}
                          currentDoctorName={`Dr. ${user?.first_name} ${user?.last_name}`}
                        />
            </Paper> )
    
     case 2:
      return (  <Paper p="xl" radius="md" withBorder w={"100%"}>
            <Title order={4}>All Messages</Title>
            <Text c="dimmed" mt="md">
              This section will show all your messages, including those outside your specialty.
              This feature is coming soon.
            </Text>
          </Paper>)
      case 3:
        return (  <div>Test-3</div>)
        case 4:
          return <div>Test-4</div>;

    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 