import api from '../lib/api';

// Define interfaces for API parameters
export interface ApiParams {
  [key: string]: string | number | boolean | undefined;
}

export interface ApiData {
  [key: string]: unknown;
}

// Helper function to make API calls with fallback to direct service
const makeApiCall = async (
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: ApiData,
  params?: ApiParams
) => {
  // Try the API gateway endpoint first
  try {
    const config = { params };
    if (method === 'get') {
      const response = await api.get(endpoint, config);
      return response.data;
    } else {
      const response = await api[method](endpoint, data, config);
      return response.data;
    }
  } catch (gatewayError) {
    console.warn(`API gateway endpoint failed for ${method.toUpperCase()} ${endpoint}, using mock data:`, gatewayError);

    // Instead of trying to connect to a service that doesn't exist,
    // we'll throw the error to let the calling function handle it with mock data
    throw new Error(`API call failed for ${endpoint}`);

    // Note: The code below is commented out because the direct service doesn't exist
    // If you want to enable it in the future, uncomment this code
    /*
    // Fall back to direct patient service endpoint
    const directServiceUrl = process.env.NEXT_PUBLIC_PATIENT_SERVICE_URL || 'http://localhost:8002';
    const fullUrl = `${directServiceUrl}${endpoint}`;

    try {
      if (method === 'get') {
        const response = await axios.get(fullUrl, {
          params,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          }
        });
        return response.data;
      } else {
        const response = await axios[method](fullUrl, data, {
          params,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          }
        });
        return response.data;
      }
    } catch (directError) {
      console.error(`Direct service call failed for ${method.toUpperCase()} ${endpoint}:`, directError);
      throw directError;
    }
    */
  }
};

export interface Patient {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  date_of_birth?: string;
  age?: string;
  gender?: string;
  national_id_number?: string;
  passport_number?: string;
  marital_status?: string;
  phone_number?: string;
  address?: string;
  medical_history?: string;
  doctor_assigned?: string;
  visit_type?: string;
  visit_duration?: string;
  agenda?: string;
  comments?: string;
  diagnostic_room?: string;
  insurance_company?: string;
  insurance_policy_number?: string;
  additional_notes?: string;
  is_active?: boolean;
  status?: string;
  created_at: string;
  updated_at: string;
}

export interface MedicalRecord {
  id: string;
  patient_id: string;
  doctor: {
    id: string;
    first_name: string;
    last_name: string;
    specialization: string;
  };
  title: string;
  description: string;
  date: string;
  record_type: string;
  attachments: Attachment[];
  created_at: string;
  updated_at: string;
}

export interface Attachment {
  id: string;
  name: string;
  file_url: string;
  file_type: string;
  created_at: string;
}

export interface Medication {
  id: string;
  patient_id: string;
  name: string;
  dosage: string;
  frequency: string;
  start_date: string;
  end_date: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LabResult {
  id: string;
  patient_id: string;
  doctor: {
    id: string;
    first_name: string;
    last_name: string;
    specialization: string;
  };
  test_name: string;
  test_date: string;
  result: string;
  normal_range: string;
  units: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

const patientService = {
  async getPatients(params?: ApiParams): Promise<Patient[]> {
    try {
      const data = await makeApiCall('/api/patients/', 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error('Error fetching patients:', error);

      // Return mock data for development
      const mockPatients: Patient[] = [
        {
          id: '101',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          date_of_birth: '1980-05-15',
          gender: 'Male',
          phone_number: '(*************',
          address: '123 Main St, Anytown, USA',
          medical_history: 'Hypertension, Allergies to penicillin',
          created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '102',
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith',
          date_of_birth: '1975-08-22',
          gender: 'Female',
          phone_number: '(*************',
          address: '456 Oak St, Anytown, USA',
          medical_history: 'Diabetes type 2, No known allergies',
          created_at: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '103',
          email: '<EMAIL>',
          first_name: 'Robert',
          last_name: 'Johnson',
          date_of_birth: '1990-03-10',
          gender: 'Male',
          phone_number: '(*************',
          address: '789 Pine St, Anytown, USA',
          medical_history: 'Asthma, Allergies to shellfish',
          created_at: new Date(Date.now() - 250 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '104',
          email: '<EMAIL>',
          first_name: 'Emily',
          last_name: 'Davis',
          date_of_birth: '1985-11-28',
          gender: 'Female',
          phone_number: '(*************',
          address: '101 Maple St, Anytown, USA',
          medical_history: 'Migraines, No known allergies',
          created_at: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '105',
          email: '<EMAIL>',
          first_name: 'Michael',
          last_name: 'Brown',
          date_of_birth: '1970-07-04',
          gender: 'Male',
          phone_number: '(*************',
          address: '202 Cedar St, Anytown, USA',
          medical_history: 'Arthritis, Allergies to sulfa drugs',
          created_at: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      // Filter mock patients based on params
      if (params) {
        if (params.limit) {
          // Convert limit to number to ensure it works with slice()
          const limit = typeof params.limit === 'string' ? parseInt(params.limit, 10) : Number(params.limit);
          // Only use limit if it's a valid number
          if (!isNaN(limit)) {
            return mockPatients.slice(0, limit);
          }
        }
      }

      return mockPatients;
    }
  },

  async getPatient(id: string): Promise<Patient | null> {
    try {
      return await makeApiCall(`/api/patients/${id}/`);
    } catch (error) {
      console.error(`Error fetching patient ${id}:`, error);
      return null;
    }
  },

  async createPatient(data: Partial<Patient>): Promise<Patient | null> {
    try {
      return await makeApiCall('/api/patients/', 'post', data);
    } catch (error) {
      console.error('Error creating patient:', error);
      return null;
    }
  },

  async updatePatient(id: string, data: Partial<Patient>): Promise<Patient | null> {
    try {
      return await makeApiCall(`/api/patients/${id}/`, 'patch', data);
    } catch (error) {
      console.error(`Error updating patient ${id}:`, error);
      return null;
    }
  },

  async getMedicalRecords(patientId: string, params?: ApiParams): Promise<MedicalRecord[]> {
    try {
      const data = await makeApiCall(`/api/patients/${patientId}/medical-records/`, 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching medical records for patient ${patientId}:`, error);
      return [];
    }
  },

  async getMedicalRecord(patientId: string, recordId: string): Promise<MedicalRecord | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medical-records/${recordId}/`);
    } catch (error) {
      console.error(`Error fetching medical record ${recordId} for patient ${patientId}:`, error);
      return null;
    }
  },

  async createMedicalRecord(patientId: string, data: Partial<MedicalRecord>): Promise<MedicalRecord | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medical-records/`, 'post', data);
    } catch (error) {
      console.error(`Error creating medical record for patient ${patientId}:`, error);
      return null;
    }
  },

  async getMedications(patientId: string, params?: ApiParams): Promise<Medication[]> {
    try {
      const data = await makeApiCall(`/api/patients/${patientId}/medications/`, 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching medications for patient ${patientId}:`, error);
      return [];
    }
  },

  async createMedication(patientId: string, data: Partial<Medication>): Promise<Medication | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medications/`, 'post', data);
    } catch (error) {
      console.error(`Error creating medication for patient ${patientId}:`, error);
      return null;
    }
  },

  async updateMedication(patientId: string, medicationId: string, data: Partial<Medication>): Promise<Medication | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/medications/${medicationId}/`, 'patch', data);
    } catch (error) {
      console.error(`Error updating medication ${medicationId} for patient ${patientId}:`, error);
      return null;
    }
  },

  async getLabResults(patientId: string, params?: ApiParams): Promise<LabResult[]> {
    try {
      const data = await makeApiCall(`/api/patients/${patientId}/lab-results/`, 'get', undefined, params);
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching lab results for patient ${patientId}:`, error);
      return [];
    }
  },

  async createLabResult(patientId: string, data: Partial<LabResult>): Promise<LabResult | null> {
    try {
      return await makeApiCall(`/api/patients/${patientId}/lab-results/`, 'post', data);
    } catch (error) {
      console.error(`Error creating lab result for patient ${patientId}:`, error);
      return null;
    }
  }
};

export default patientService;
