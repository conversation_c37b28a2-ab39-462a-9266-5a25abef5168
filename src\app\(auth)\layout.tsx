'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AppShell, Center, } from '@mantine/core';
import authService from '~/services/authService';
import SimpleBar from "simplebar-react";

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  const router = useRouter();

  useEffect(() => {
    // Skip authentication check for registration with resumeRegistration parameter
    const isResumingRegistration = window.location.href.includes('resumeRegistration=true');

    // If user is already authenticated and not resuming registration, redirect to dashboard
    if (authService.isAuthenticated() && !isResumingRegistration) {
      router.push('/dashboard');
    }
  }, [router]);

  return (
    <SimpleBar style={{ maxHeight: "100vh" }}>
    <AppShell
      padding="md"
      styles={{
        main: {
          backgroundColor: '#f8f9fa',
          backgroundImage: 'url(/auth-background.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        },
      }}
    >
       
          
        
          <Center
        style={{
          height: "100vh",
          width: "100vw",
        }}
        className="relative"
      >
         
        {children}
        </Center>
    </AppShell>
    </SimpleBar>
  );
}
