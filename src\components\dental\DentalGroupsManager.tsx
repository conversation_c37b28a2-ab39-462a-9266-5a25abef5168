"use client";

import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Title,
  Text,
  Group,
  Badge,
  Grid,
  Stack,
  Button,
  Select,
  Divider,
  Alert
} from '@mantine/core';
import {
  IconTooth,
  IconChevronUp,
  IconChevronDown,
  IconInfoCircle,
  IconPlus
} from '@tabler/icons-react';
import { 
  TOOTH_NAMES, 
  DENTAL_GROUPS, 
  getTeethByGroup, 
  createToothInfo,
  type DentalGroup,
  type ToothInfo 
} from '@/data/ToothMapping';
import ToothButtonComponent from './ToothButtonComponent';

// Types pour les restrictions d'âge
interface AgeRestriction {
  value: number;
  label: string;
  description: string;
  restrictedTeeth: number[];
}

// Restrictions d'âge disponibles
const AGE_RESTRICTIONS: AgeRestriction[] = [
  {
    value: 13.5,
    label: "13.5 ans et plus",
    description: "Traitements avancés complets",
    restrictedTeeth: []
  },
  {
    value: 12.0,
    label: "12 ans à 13.5 ans", 
    description: "Traitements intermédiaires",
    restrictedTeeth: [18, 28, 38, 48] // Dents de sagesse
  },
  {
    value: 7.5,
    label: "7.5 ans à 12 ans",
    description: "Traitements de base",
    restrictedTeeth: [14, 15, 24, 25, 34, 35, 44, 45, 18, 28, 38, 48] // Prémolaires + sagesse
  },
  {
    value: 6.0,
    label: "6 ans à 7.5 ans",
    description: "Traitements préventifs",
    restrictedTeeth: [14, 15, 24, 25, 34, 35, 44, 45, 17, 27, 37, 47, 18, 28, 38, 48] // Prémolaires + molaires
  },
  {
    value: 0.0,
    label: "Moins de 6 ans",
    description: "Traitements primaires uniquement",
    restrictedTeeth: [11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 43, 44, 45, 46, 47, 48] // Toutes les permanentes
  }
];

interface DentalGroupsManagerProps {
  patientAge?: number;
  onToothSelect?: (toothNumber: number, isSelected: boolean) => void;
  onSpecialtyChange?: (toothNumber: number, specialty: string, data: any) => void;
}

const DentalGroupsManager: React.FC<DentalGroupsManagerProps> = ({
  patientAge = 13.5,
  onToothSelect,
  onSpecialtyChange
}) => {
  const [activeGroup, setActiveGroup] = useState<DentalGroup>('general');
  const [selectedAge, setSelectedAge] = useState<number>(patientAge);
  const [expandedTooth, setExpandedTooth] = useState<number | null>(null);

  // Obtenir la restriction d'âge actuelle
  const getCurrentAgeRestriction = (): AgeRestriction => {
    return AGE_RESTRICTIONS.find(restriction => 
      selectedAge >= restriction.value
    ) || AGE_RESTRICTIONS[AGE_RESTRICTIONS.length - 1];
  };

  // Filtrer les dents visibles selon l'âge
  const getVisibleTeeth = (ageRestriction: AgeRestriction): number[] => {
    const groupTeeth = getTeethByGroup(activeGroup);
    return groupTeeth.filter(toothNumber => 
      !ageRestriction.restrictedTeeth.includes(toothNumber)
    );
  };

  // Créer les informations des dents
  const createTeethInfo = (toothNumbers: number[]): ToothInfo[] => {
    return toothNumbers.map(createToothInfo);
  };

  const currentRestriction = getCurrentAgeRestriction();
  const visibleTeethNumbers = getVisibleTeeth(currentRestriction);
  const visibleTeeth = createTeethInfo(visibleTeethNumbers);

  // Grouper les dents par quadrant pour un meilleur affichage
  const groupTeethByQuadrant = (teeth: ToothInfo[]) => {
    const quadrants = {
      upper_right: teeth.filter(t => t.quadrant === 'upper_right'),
      upper_left: teeth.filter(t => t.quadrant === 'upper_left'),
      lower_left: teeth.filter(t => t.quadrant === 'lower_left'),
      lower_right: teeth.filter(t => t.quadrant === 'lower_right')
    };
    return quadrants;
  };

  const quadrants = groupTeethByQuadrant(visibleTeeth);

  const handleToothExpand = (toothNumber: number) => {
    setExpandedTooth(expandedTooth === toothNumber ? null : toothNumber);
  };

  const getGroupStats = (groupType: DentalGroup) => {
    const allTeeth = getTeethByGroup(groupType);
    const visibleCount = allTeeth.filter(tooth => 
      !currentRestriction.restrictedTeeth.includes(tooth)
    ).length;
    
    return {
      total: allTeeth.length,
      visible: visibleCount,
      hidden: allTeeth.length - visibleCount
    };
  };

  return (
    <Card withBorder p="lg">
      <Stack gap="lg">
        {/* En-tête */}
        <Group justify="space-between" align="center">
          <Group gap="sm">
            <IconTooth size={24} color="blue" />
            <Title order={3}>Groupes Dentaires</Title>
          </Group>
          
          <Select
            label="Tranche d'âge"
            value={selectedAge.toString()}
            onChange={(value) => setSelectedAge(parseFloat(value || '13.5'))}
            data={AGE_RESTRICTIONS.map(restriction => ({
              value: restriction.value.toString(),
              label: restriction.label
            }))}
            w={200}
          />
        </Group>

        {/* Information sur la restriction d'âge */}
        <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
          <Text size="sm">
            <strong>{currentRestriction.label}:</strong> {currentRestriction.description}
            {currentRestriction.restrictedTeeth.length > 0 && (
              <Text size="xs" c="dimmed" mt={4}>
                {currentRestriction.restrictedTeeth.length} dent(s) masquée(s) pour cette tranche d'âge
              </Text>
            )}
          </Text>
        </Alert>

        {/* Onglets des groupes */}
        <Tabs value={activeGroup} onChange={(value) => setActiveGroup(value as DentalGroup)}>
          <Tabs.List>
            <Tabs.Tab value="general" leftSection={<IconTooth size={16} />}>
              <Group gap="xs">
                <Text>Général</Text>
                <Badge size="xs" variant="light" color="gray">
                  {getGroupStats('general').visible}
                </Badge>
              </Group>
            </Tabs.Tab>
            
            <Tabs.Tab value="upper" leftSection={<IconChevronUp size={16} />}>
              <Group gap="xs">
                <Text>Supérieur</Text>
                <Badge size="xs" variant="light" color="blue">
                  {getGroupStats('upper').visible}
                </Badge>
              </Group>
            </Tabs.Tab>
            
            <Tabs.Tab value="lower" leftSection={<IconChevronDown size={16} />}>
              <Group gap="xs">
                <Text>Inférieur</Text>
                <Badge size="xs" variant="light" color="green">
                  {getGroupStats('lower').visible}
                </Badge>
              </Group>
            </Tabs.Tab>
          </Tabs.List>

          {/* Contenu des onglets */}
          <Tabs.Panel value="general" pt="md">
            <DentalGroupContent
              teeth={visibleTeeth}
              groupType="general"
              expandedTooth={expandedTooth}
              onToothExpand={handleToothExpand}
              ageRestriction={currentRestriction}
              onToothSelect={onToothSelect}
              onSpecialtyChange={onSpecialtyChange}
            />
          </Tabs.Panel>

          <Tabs.Panel value="upper" pt="md">
            <DentalGroupContent
              teeth={visibleTeeth}
              groupType="upper"
              expandedTooth={expandedTooth}
              onToothExpand={handleToothExpand}
              ageRestriction={currentRestriction}
              onToothSelect={onToothSelect}
              onSpecialtyChange={onSpecialtyChange}
            />
          </Tabs.Panel>

          <Tabs.Panel value="lower" pt="md">
            <DentalGroupContent
              teeth={visibleTeeth}
              groupType="lower"
              expandedTooth={expandedTooth}
              onToothExpand={handleToothExpand}
              ageRestriction={currentRestriction}
              onToothSelect={onToothSelect}
              onSpecialtyChange={onSpecialtyChange}
            />
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Card>
  );
};

// Composant pour le contenu d'un groupe dentaire
interface DentalGroupContentProps {
  teeth: ToothInfo[];
  groupType: DentalGroup;
  expandedTooth: number | null;
  onToothExpand: (toothNumber: number) => void;
  ageRestriction: AgeRestriction;
  onToothSelect?: (toothNumber: number, isSelected: boolean) => void;
  onSpecialtyChange?: (toothNumber: number, specialty: string, data: any) => void;
}

const DentalGroupContent: React.FC<DentalGroupContentProps> = ({
  teeth,
  groupType,
  expandedTooth,
  onToothExpand,
  ageRestriction,
  onToothSelect,
  onSpecialtyChange
}) => {
  if (teeth.length === 0) {
    return (
      <Alert color="orange" variant="light">
        <Text>Aucune dent disponible pour cette tranche d'âge dans le groupe {groupType}.</Text>
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      <Group justify="space-between">
        <Text size="sm" c="dimmed">
          {teeth.length} dent(s) disponible(s)
        </Text>
        <Button variant="light" size="xs" leftSection={<IconPlus size={14} />}>
          Sélectionner tout
        </Button>
      </Group>

      <Grid>
        {teeth.map((tooth) => (
          <Grid.Col key={tooth.tooth_number} span={{ base: 12, sm: 6, md: 4, lg: 3 }}>
            <ToothButtonComponent
              tooth={tooth}
              isExpanded={expandedTooth === tooth.tooth_number}
              onExpand={() => onToothExpand(tooth.tooth_number)}
              ageRestriction={ageRestriction}
              onSelect={onToothSelect}
              onSpecialtyChange={onSpecialtyChange}
            />
          </Grid.Col>
        ))}
      </Grid>
    </Stack>
  );
};

export default DentalGroupsManager;
