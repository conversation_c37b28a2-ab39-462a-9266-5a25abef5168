// frontend/dental_medicine/src/components/content/dental/specialties/Surgery/SurgicalProcedures.tsx

import React, { useState, useMemo } from 'react';
import { Table, Checkbox, Text, Badge, Button, Group, TextInput, Select } from '@mantine/core';
import { IconSearch, IconFilter } from '@tabler/icons-react';
import { SurgicalProcedure, ModificationState, SVGPathStyle } from '../../shared/types';

interface SurgicalProceduresProps {
  type: 'all' | 'planned' | 'completed';
  modificationState?: ModificationState; // Optionnel car non utilisé actuellement
  onModificationChange?: (
    svgId: string,
    pathId: string,
    isVisible: boolean,
    highlightedPaths?: Record<string, SVGPathStyle>
  ) => Promise<void>;
}

export const SurgicalProcedures: React.FC<SurgicalProceduresProps> = ({
  type,
  onModificationChange
}) => {

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Données des procédures chirurgicales
  const allSurgicalProcedures: SurgicalProcedure[] = [
    {
      position: 1,
      mass: 1.008,
      symbol: 'EXT',
      name: 'Extraction Simple',
      type: 'extraction',
      pathId: '53',
      cost: 80,
      duration: 30,
      category: 'extraction',
      anesthesia: 'local',
      complexity: 'simple'
    },
    {
      position: 2,
      mass: 4.003,
      symbol: 'EXC',
      name: 'Extraction Complexe',
      type: 'extraction',
      pathId: '54',
      cost: 150,
      duration: 60,
      category: 'extraction',
      anesthesia: 'local',
      complexity: 'complex'
    },
    {
      position: 3,
      mass: 6.941,
      symbol: 'IMP',
      name: 'Pose Implant',
      type: 'implant_surgery',
      pathId: '56',
      cost: 1200,
      duration: 90,
      category: 'implant',
      anesthesia: 'local',
      complexity: 'moderate'
    },
    {
      position: 4,
      mass: 9.012,
      symbol: 'GRE',
      name: 'Greffe Osseuse',
      type: 'bone_graft',
      pathId: '59',
      cost: 800,
      duration: 120,
      category: 'greffe',
      anesthesia: 'sedation',
      complexity: 'complex'
    },
    {
      position: 5,
      mass: 10.811,
      symbol: 'SIN',
      name: 'Sinus Lift',
      type: 'sinus_lift',
      pathId: '62',
      cost: 600,
      duration: 90,
      category: 'greffe',
      anesthesia: 'local',
      complexity: 'moderate'
    },
    {
      position: 6,
      mass: 12.011,
      symbol: 'SAG',
      name: 'Dent de Sagesse',
      type: 'wisdom_tooth',
      pathId: '55',
      cost: 200,
      duration: 45,
      category: 'extraction',
      anesthesia: 'local',
      complexity: 'moderate'
    },
    {
      position: 7,
      mass: 14.007,
      symbol: 'PAR',
      name: 'Chirurgie Parodontale',
      type: 'extraction',
      pathId: '64',
      cost: 400,
      duration: 75,
      category: 'parodontale',
      anesthesia: 'local',
      complexity: 'moderate'
    },
    {
      position: 8,
      mass: 15.999,
      symbol: 'API',
      name: 'Apicectomie',
      type: 'extraction',
      pathId: '65',
      cost: 350,
      duration: 60,
      category: 'endodontie',
      anesthesia: 'local',
      complexity: 'complex'
    }
  ];

  // Filtrer les procédures selon le type
  const filteredProcedures = useMemo(() => {
    let procedures = allSurgicalProcedures;

    // Filtrer par type (all, planned, completed)
    if (type === 'planned') {
      procedures = procedures.filter(proc => proc.position <= 4);
    } else if (type === 'completed') {
      procedures = procedures.filter(proc => proc.position > 4);
    }

    // Filtrer par terme de recherche
    if (searchTerm) {
      procedures = procedures.filter(proc =>
        proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (filterCategory !== 'all') {
      procedures = procedures.filter(proc => proc.category === filterCategory);
    }

    return procedures;
  }, [type, searchTerm, filterCategory]);

  // Gestionnaire de sélection de ligne
  const handleRowSelection = (position: number) => {
    setSelectedRows(prev =>
      prev.includes(position)
        ? prev.filter(p => p !== position)
        : [...prev, position]
    );
  };

  // Gestionnaire d'application de procédure
  const handleApplyProcedure = async (procedure: SurgicalProcedure) => {
    if (onModificationChange && procedure.pathId) {
      try {
        await onModificationChange('1', procedure.pathId, true, {
          [`1-${procedure.pathId}`]: {
            fill: getColorForProcedure(procedure.type),
            stroke: '#dc3545',
            strokeWidth: 1
          }
        });
        console.log(`✅ Procédure chirurgicale appliquée: ${procedure.name}`);
      } catch (error) {
        console.error(`❌ Erreur application procédure: ${procedure.name}`, error);
      }
    }
  };

  // Obtenir la couleur selon le type de procédure
  const getColorForProcedure = (type: SurgicalProcedure['type']): string => {
    switch (type) {
      case 'extraction': return '#ffe6e6';
      case 'implant_surgery': return '#ffcccc';
      case 'bone_graft': return '#ffb3b3';
      case 'sinus_lift': return '#ff9999';
      case 'wisdom_tooth': return '#ff8080';
      default: return '#f8f9fa';
    }
  };

  // Obtenir le badge de statut
  const getStatusBadge = () => {
    if (type === 'completed') {
      return <Badge color="green" size="sm">Terminé</Badge>;
    } else if (type === 'planned') {
      return <Badge color="blue" size="sm">Planifié</Badge>;
    } else {
      return <Badge color="gray" size="sm">Disponible</Badge>;
    }
  };

  // Obtenir le badge de complexité
  const getComplexityBadge = (complexity: string) => {
    const colors: Record<string, string> = {
      'simple': 'green',
      'moderate': 'yellow',
      'complex': 'red'
    };
    return <Badge color={colors[complexity] || 'gray'} variant="light" size="xs">{complexity}</Badge>;
  };

  // Obtenir le badge d'anesthésie
  const getAnesthesiaBadge = (anesthesia: string) => {
    const colors: Record<string, string> = {
      'local': 'blue',
      'sedation': 'orange',
      'general': 'red'
    };
    return <Badge color={colors[anesthesia] || 'gray'} variant="outline" size="xs">{anesthesia}</Badge>;
  };

  const rows = filteredProcedures.map((procedure) => (
    <Table.Tr
      key={procedure.position}
      bg={selectedRows.includes(procedure.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(procedure.position)}
          onChange={() => handleRowSelection(procedure.position)}
        />
      </Table.Td>
      <Table.Td>
        <Text fw={500} size="sm">{procedure.symbol}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.name}</Text>
      </Table.Td>
      <Table.Td>
        {procedure.complexity && getComplexityBadge(procedure.complexity)}
      </Table.Td>
      <Table.Td>
        {procedure.anesthesia && getAnesthesiaBadge(procedure.anesthesia)}
      </Table.Td>
      <Table.Td>
        {getStatusBadge()}
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.cost}€</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.duration}min</Text>
      </Table.Td>
      <Table.Td>
        <Button
          size="xs"
          variant="light"
          color="red"
          onClick={() => handleApplyProcedure(procedure)}
        >
          Appliquer
        </Button>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <div className="p-4">
      {/* Filtres et recherche */}
      <Group mb="md">
        <TextInput
          placeholder="Rechercher une procédure..."
          leftSection={<IconSearch size={16} />}
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.currentTarget.value)}
          style={{ flex: 1 }}
        />
        <Select
          placeholder="Catégorie"
          leftSection={<IconFilter size={16} />}
          data={[
            { value: 'all', label: 'Toutes' },
            { value: 'extraction', label: 'Extractions' },
            { value: 'implant', label: 'Implants' },
            { value: 'greffe', label: 'Greffes' },
            { value: 'parodontale', label: 'Parodontale' },
            { value: 'endodontie', label: 'Endodontie' }
          ]}
          value={filterCategory}
          onChange={(value) => setFilterCategory(value || 'all')}
          w={150}
        />
      </Group>

      {/* Statistiques */}
      <Group mb="md">
        <Text size="sm" c="dimmed">
          {filteredProcedures.length} procédure(s) • {selectedRows.length} sélectionnée(s)
        </Text>
        {selectedRows.length > 0 && (
          <Button size="xs" variant="light" color="red">
            Appliquer la sélection
          </Button>
        )}
      </Group>

      {/* Tableau des procédures */}
      <Table striped highlightOnHover>
        <Table.Thead>
          <Table.Tr>
            <Table.Th />
            <Table.Th>Code</Table.Th>
            <Table.Th>Procédure</Table.Th>
            <Table.Th>Complexité</Table.Th>
            <Table.Th>Anesthésie</Table.Th>
            <Table.Th>Statut</Table.Th>
            <Table.Th>Coût</Table.Th>
            <Table.Th>Durée</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>

      {filteredProcedures.length === 0 && (
        <Text ta="center" c="dimmed" mt="xl">
          Aucune procédure trouvée
        </Text>
      )}
    </div>
  );
};

export default SurgicalProcedures;
