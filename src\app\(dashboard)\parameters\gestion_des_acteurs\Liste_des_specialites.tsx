'use client';

import { useForm } from '@mantine/form';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Stack,
  Title,
  TextInput,
  Button,
  Table,
  Group,
  ActionIcon,
  Paper,
  Tooltip,
  Modal,
  Checkbox,
  Text,
  Switch,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconStethoscope,
} from '@tabler/icons-react';
import Icon from '@mdi/react';
import {  mdiPlaylistPlus,mdiClose} from '@mdi/js';
// Types
interface SpecialtyPermission {
  chir: boolean;
  inte: boolean;
  dia: boolean;
  the: boolean;
  bas1: boolean;
  bas2: boolean;
}

interface Specialty {
  id: string;
  shortCode: string;
  longName: string;
  permissions: SpecialtyPermission;
}

// Mock data for specialties
const mockSpecialties: Specialty[] = [
    {
      id: '1',
      shortCode: 'ALL',
      longName: 'Allergologie',
      permissions: { chir: false, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '2',
      shortCode: 'ANA',
      longName: 'Anatomopathologie',
      permissions: { chir: false, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '3',
      shortCode: 'AND',
      longName: 'Andrologie',
      permissions: { chir: true, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '4',
      shortCode: 'ANG',
      longName: 'Angiologie',
      permissions: { chir: false, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '5',
      shortCode: 'ANS',
      longName: 'Anesthésiologie',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: true }
    },
    {
      id: '6',
      shortCode: 'CAN',
      longName: 'Cancérologie',
      permissions: { chir: false, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '7',
      shortCode: 'CBMF',
      longName: 'Chirurgie buccale maxillofaciale',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '8',
      shortCode: 'CHG',
      longName: 'Chirurgie générale',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '9',
      shortCode: 'CHP',
      longName: 'Chirurgie plastique',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '10',
      shortCode: 'CHP',
      longName: 'Chirurgie pédiatrique',
      permissions: { chir: true, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '11',
      shortCode: 'CHV',
      longName: 'Chirurgie viscérale',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '12',
      shortCode: 'CMF',
      longName: 'Chirurgie maxillo-faciale',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '13',
      shortCode: 'CRD',
      longName: 'Cardiologie',
      permissions: { chir: true, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '14',
      shortCode: 'CTV',
      longName: 'Chirurgie thoracique et cardio vasculaire',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '15',
      shortCode: 'CVA',
      longName: 'Chirurgie vasculaire',
      permissions: { chir: true, inte: false, dia: false, the: false, bas1: false, bas2: false }
    },
    {
      id: '16',
      shortCode: 'DEP',
      longName: 'Dentisterie pédiatrique',
      permissions: { chir: true, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '17',
      shortCode: 'DER',
      longName: 'Dermatologie',
      permissions: { chir: false, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '18',
      shortCode: 'DIA',
      longName: 'Diabétologie',
      permissions: { chir: false, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '19',
      shortCode: 'DIE',
      longName: 'Diététicien',
      permissions: { chir: false, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
    {
      id: '20',
      shortCode: 'DNT',
      longName: 'Dentaire',
      permissions: { chir: true, inte: false, dia: true, the: false, bas1: false, bas2: false }
    },
];

const Liste_des_specialites = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [specialties, setSpecialties] = useState<Specialty[]>([]);
  const [modalOpened, setModalOpened] = useState(false);
  const [EditSpecialtymodalOpened, setEditSpecialtyModalOpened] = useState(false);
  const [newSpecialty, setNewSpecialty] = useState({
    shortCode: '',
    longName: '',
    permissions: {
      chir: false,
      inte: false,
      dia: false,
      the: false,
      bas1: false,
      bas2: false,
    }
  });

  useEffect(() => {
    setSpecialties(mockSpecialties);
  }, []);

  // Filter specialties based on search query
  const filteredSpecialties = specialties.filter((specialty: Specialty) =>
    specialty.longName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    specialty.shortCode.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Render permission dot
  const renderPermissionDot = (hasPermission: boolean) => (
    <div
      className={`w-4 h-4 rounded-full ${
        hasPermission ? 'bg-green-500' : 'bg-red-500'
      }`}
    />
  );
const router = useRouter();

  const form = useForm({
    initialValues: {
      short_title: '',
      long_title: '',
      is_surgical: false,
      is_internal: false,
      is_diagnostic: true,
      is_therapeutic: false,
      is_organ_based: false,
      is_technique_based: false,
    },

    validate: {
      short_title: (value) => (value ? null : 'Champ requis'),
      long_title: (value) => (value ? null : 'Champ requis'),
    },
  });

  const handleSubmit = (values: typeof form.values) => {
    // TODO: Envoyer les données via API
    console.log('Formulaire soumis :', values);
  };

  return (
    <Stack gap="lg" className="w-full" mb={60}>
      {/* Header */}
      <Paper p="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconStethoscope size={24} className="text-blue-600" />
            <Title order={3} className="text-gray-700">
              Liste des spécialités
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="blue"
            size="sm"
            onClick={() => setModalOpened(true)}
          >
            Nouvelle spécialité
          </Button>
        </Group>
      </Paper>

      {/* Search */}
      <Paper p="md" withBorder>
        <TextInput
          placeholder="Rechercher"
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(event) => setSearchQuery(event.currentTarget.value)}
          className="w-full max-w-md"
        />
      </Paper>

      {/* Table */}
      <Paper withBorder>
        <Table striped highlightOnHover withTableBorder>
          <Table.Thead>
            <Table.Tr className="bg-gray-50">
              <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                Désignation courte
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                Désignation longue
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Chir...
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Inte...
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Dia...
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Thé...
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Bas...
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                Bas...
              </Table.Th>
              <Table.Th className="font-semibold text-gray-700 text-center">
                Actions
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredSpecialties.map((specialty) => (
              <Table.Tr key={specialty.id} className="hover:bg-gray-50">
                <Table.Td className="font-medium border-r border-gray-200">
                  {specialty.shortCode}
                </Table.Td>
                <Table.Td className="border-r border-gray-200">
                  {specialty.longName}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionDot(specialty.permissions.chir)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionDot(specialty.permissions.inte)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionDot(specialty.permissions.dia)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionDot(specialty.permissions.the)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionDot(specialty.permissions.bas1)}
                </Table.Td>
                <Table.Td className="text-center border-r border-gray-200">
                  {renderPermissionDot(specialty.permissions.bas2)}
                </Table.Td>
                <Table.Td className="text-center">
                  <Group gap="xs" justify="center">
                    <Tooltip label="Modifier">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        size="sm"
                        onClick={() => {
                          // Handle edit action
                          console.log('Edit specialty:', specialty.id);
                          setEditSpecialtyModalOpened(true)
                        }}
                        
                      >
                        <IconEdit size={16} />
                      </ActionIcon>
       
                    </Tooltip>
                    <Tooltip label="Supprimer">
                      <ActionIcon
                        variant="subtle"
                        color="red"
                        size="sm"
                        onClick={() => {
                          // Handle delete action
                          console.log('Delete specialty:', specialty.id);
                        }}
                      >
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Paper>

      {/* Modal for Edit specialty */}
      <Modal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        title="Nouvelle spécialité"
        size="md"
        centered
      >
        <Stack gap="md">
          <TextInput
            label="Désignation courte"
            placeholder="Ex: ALL, ANA, AND..."
            value={newSpecialty.shortCode}
            onChange={(event) =>
              setNewSpecialty({ ...newSpecialty, shortCode: event.currentTarget.value })
            }
            required
          />

          <TextInput
            label="Désignation longue"
            placeholder="Ex: Allergologie, Anatomopathologie..."
            value={newSpecialty.longName}
            onChange={(event) =>
              setNewSpecialty({ ...newSpecialty, longName: event.currentTarget.value })
            }
            required
          />

          <Text size="sm" fw={500} mt="md">
            Types autorisés :
          </Text>

          <Group gap="md">
            <Checkbox
              label="Chir..."
              checked={newSpecialty.permissions.chir}
              onChange={(event) =>
                setNewSpecialty({
                  ...newSpecialty,
                  permissions: { ...newSpecialty.permissions, chir: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Inte..."
              checked={newSpecialty.permissions.inte}
              onChange={(event) =>
                setNewSpecialty({
                  ...newSpecialty,
                  permissions: { ...newSpecialty.permissions, inte: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Dia..."
              checked={newSpecialty.permissions.dia}
              onChange={(event) =>
                setNewSpecialty({
                  ...newSpecialty,
                  permissions: { ...newSpecialty.permissions, dia: event.currentTarget.checked }
                })
              }
            />
          </Group>

          <Group gap="md">
            <Checkbox
              label="Thé..."
              checked={newSpecialty.permissions.the}
              onChange={(event) =>
                setNewSpecialty({
                  ...newSpecialty,
                  permissions: { ...newSpecialty.permissions, the: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Bas..."
              checked={newSpecialty.permissions.bas1}
              onChange={(event) =>
                setNewSpecialty({
                  ...newSpecialty,
                  permissions: { ...newSpecialty.permissions, bas1: event.currentTarget.checked }
                })
              }
            />
            <Checkbox
              label="Bas..."
              checked={newSpecialty.permissions.bas2}
              onChange={(event) =>
                setNewSpecialty({
                  ...newSpecialty,
                  permissions: { ...newSpecialty.permissions, bas2: event.currentTarget.checked }
                })
              }
            />
          </Group>

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => {
                setModalOpened(false);
                setNewSpecialty({
                  shortCode: '',
                  longName: '',
                  permissions: {
                    chir: false,
                    inte: false,
                    dia: false,
                    the: false,
                    bas1: false,
                    bas2: false,
                  }
                });
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                // Add new specialty to the list
                const newId = (specialties.length + 1).toString();
                const specialty: Specialty = {
                  id: newId,
                  shortCode: newSpecialty.shortCode,
                  longName: newSpecialty.longName,
                  permissions: newSpecialty.permissions
                };
                setSpecialties([...specialties, specialty]);

                // Reset form and close modal
                setNewSpecialty({
                  shortCode: '',
                  longName: '',
                  permissions: {
                    chir: false,
                    inte: false,
                    dia: false,
                    the: false,
                    bas1: false,
                    bas2: false,
                  }
                });
                setModalOpened(false);
              }}
              disabled={!newSpecialty.shortCode || !newSpecialty.longName}
            >
              Ajouter
            </Button>
          </Group>
        </Stack>
      </Modal>
       <Modal opened={EditSpecialtymodalOpened}
        onClose={() => setEditSpecialtyModalOpened(false)} >
        <Paper withBorder p="md" shadow="md" radius="md" maw={600} mx="auto">
      <Group justify='center' mb="md">
        <Group>
          <Icon path={mdiPlaylistPlus} size={1} />
          <Title order={3}>Nouvelle spécialité</Title>
        </Group>
        <Button
          variant="subtle"
          color="red"
          onClick={() => router.back()}
          leftSection={<Icon path={mdiClose} size={1} />}
        >
          Fermer
        </Button>
      </Group>

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <Group grow>
            <TextInput
              label="Désignation courte"
              required
              {...form.getInputProps('short_title')}
            />
            <TextInput
              label="Désignation longue"
              required
              {...form.getInputProps('long_title')}
            />
          </Group>

          <Group grow>
            <Switch
              label="Chirurgicale"
              {...form.getInputProps('is_surgical', { type: 'checkbox' })}
            />
            <Switch
              label="Interne"
              {...form.getInputProps('is_internal', { type: 'checkbox' })}
            />
            <Switch
              label="Diagnostic"
              {...form.getInputProps('is_diagnostic', { type: 'checkbox' })}
            />
          </Group>

          <Group grow>
            <Switch
              label="Thérapeutique"
              {...form.getInputProps('is_therapeutic', { type: 'checkbox' })}
            />
            <Switch
              label="Basé organe"
              {...form.getInputProps('is_organ_based', { type: 'checkbox' })}
            />
            <Switch
              label="Basé technique"
              {...form.getInputProps('is_technique_based', {
                type: 'checkbox',
              })}
            />
          </Group>

          <Group justify='flex-end' mt="md">
            <Button color="gray" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={!form.isValid()}>
              Enregistrer
            </Button>
          </Group>
        </Stack>
      </form>
    </Paper>
      </Modal>
    </Stack>
  );
};

export default Liste_des_specialites;
