import { useState } from 'react';
import { Paper, Title, Group, TextInput,  Select, ActionIcon, Button, Radio, Switch, Grid, Modal } from '@mantine/core';
import { Icon } from '@mdi/react';
import { mdiFolderStar, mdiFolderOpen, mdiPlus, mdiRefresh, mdiMagnify, mdiClose } from '@mdi/js';
import { DateInput } from '@mantine/dates';
export const MutuellesId = () => {
  const [isPatientModalOpen, setIsPatientModalOpen] = useState(false);
  const [dossierNumber, setDossierNumber] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedPatient, ] = useState('');
  const [selectedInsurance, setSelectedInsurance] = useState('');
  const [affiliateNumber, setAffiliateNumber] = useState('');
  const [beneficiary, setBeneficiary] = useState('PATIENT');

  const mockInsuranceOptions = [
    { value: '1', label: 'AMO' },
    { value: '2', label: 'ATLANTA' },
    { value: '3', label: 'CNOPS' },
    // ... other insurance options
  ];

  return (
    <Paper p="md" shadow="sm" radius="md">
      <Group justify="space-between" mb="xl">
        <Group gap="xs">
          <Icon path={mdiFolderStar} size={1.2} />
          <Title order={2}>Nouvelle Mutuelle</Title>
        </Group>
        <Button 
          leftSection={<Icon path={mdiFolderOpen} size={1} />}
          variant="subtle"
        >
          Mutuelles
        </Button>
      </Group>

      <Grid gutter="xl">
        <Grid.Col span={4}>
          <DateInput
            label="Date"
            value={selectedDate}
            onChange={setSelectedDate}
            required
            placeholder="Select date"
          />
        </Grid.Col>
        
        <Grid.Col span={4}>
          <TextInput
            label="Numéro de dossier"
            value={dossierNumber}
            onChange={(e) => setDossierNumber(e.currentTarget.value)}
            required
          />
        </Grid.Col>

        <Grid.Col span={4}>
          <Select
            label="Docteur"
            data={[{ value: '1', label: 'Dr. DEMO DEMO' }]}
            defaultValue="1"
            required
          />
        </Grid.Col>

        <Grid.Col span={8}>
          <TextInput
            label="Patient"
            value={selectedPatient}
            readOnly
            rightSectionWidth={100}
            rightSection={
              <ActionIcon 
                variant="filled" 
                onClick={() => setIsPatientModalOpen(true)}
              >
                <Icon path={mdiMagnify} size={1} />
              </ActionIcon>
            }
          />
        </Grid.Col>

        <Grid.Col span={4}>
          <Group>
            <Select
              label="Assurances médicales"
              data={mockInsuranceOptions}
              value={selectedInsurance}
              onChange={setSelectedInsurance}
              searchable
              required
              style={{ flex: 1 }}
            />
            <ActionIcon variant="default" mt={26}>
              <Icon path={mdiPlus} size={1} />
            </ActionIcon>
            <ActionIcon variant="default" mt={26}>
              <Icon path={mdiRefresh} size={1} />
            </ActionIcon>
          </Group>
        </Grid.Col>

        <Grid.Col span={12}>
          <TextInput
            label="N° Affiliation"
            value={affiliateNumber}
            onChange={(e) => setAffiliateNumber(e.currentTarget.value)}
          />
        </Grid.Col>

        <Grid.Col span={12}>
          <Radio.Group
            label="Bénéficiaire (Patient - Assuré)"
            value={beneficiary}
            onChange={setBeneficiary}
          >
            <Group mt="sm">
              <Radio value="PATIENT" label="Lui même" />
              <Radio value="PARTNER" label="Conjoint" />
              <Radio value="CHILD" label="Enfant" />
              <Radio value="PARENT" label="Parent" />
            </Group>
          </Radio.Group>
        </Grid.Col>

        <Grid.Col span={12}>
          <Switch
            label="Synchroniser les données d'assurance"
            mt="md"
          />
        </Grid.Col>
      </Grid>

      <Modal
        opened={isPatientModalOpen}
        onClose={() => setIsPatientModalOpen(false)}
        title="Recherche de patient"
        size="lg"
      >
        {/* Patient search modal content */}
      </Modal>

      <Group justify="flex-end" mt="xl">
        <Button variant="default" leftSection={<Icon path={mdiClose} size={1} />}>
          Annuler
        </Button>
        <Button color="blue" leftSection={<Icon path={mdiPlus} size={1} />}>
          Enregistrer
        </Button>
      </Group>
    </Paper>
  );
};