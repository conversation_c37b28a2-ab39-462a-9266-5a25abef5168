import { UserProfile } from '@/services/authService';

/**
 * Interface for profile data that includes file uploads
 */
export interface ProfileDataWithFiles extends Omit<UserProfile,
  'profile_image' | 'profile_image_medium' | 'profile_image_large' |
  'clinic_photo_1' | 'clinic_photo_2' | 'clinic_photo_3'> {
  profile_image?: File | string | null;
  profile_image_medium?: File | string | null;
  profile_image_large?: File | string | null;
  clinic_photo_1?: File | string | null;
  clinic_photo_2?: File | string | null;
  clinic_photo_3?: File | string | null;
  // Form field names
  phone?: string;
  landline?: string;
}

/**
 * Interface for personal form values
 */
export interface PersonalFormValues {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;  // Form field name
  phone_number?: string;  // API field name
  landline: string;  // Form field name
  landline_number?: string;  // API field name
  address: string;
  country: string;
  region: string;
  city: string;
  bio: string;
  specialization: string;
  profile_image: File | string | null;
  profile_image_medium: File | string | null;
  profile_image_large: File | string | null;
}

/**
 * Interface for password form values
 */
export interface PasswordFormValues {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

/**
 * Interface for professional form values
 */
export interface ProfessionalFormValues {
  license_number: string;
  years_of_experience: string;
  education: string;
  certifications: string;
  hospital_affiliations: string;
  languages: string;
  consultation_fee: string;
  accepting_new_patients: boolean;
}

/**
 * Interface for social media and clinic photos form values
 */
export interface SocialMediaFormValues {
  facebook_url: string;
  twitter_url: string;
  linkedin_url: string;
  youtube_url: string;
  telegram_url: string;
  whatsapp_number: string;
  clinic_photo_1: File | string | null;
  clinic_photo_2: File | string | null;
  clinic_photo_3: File | string | null;
}
