"use client";
import { useState } from 'react';
import Overviews from "./Overviews";
import Estimates from "./Estimates";
import { Container, Tabs, rem } from "@mantine/core";
import {
  IconWindow,
  IconBrandDatabricks,
} from "@tabler/icons-react";
import { RiHistoryLine } from "react-icons/ri";
import { LiaFileInvoiceSolid } from "react-icons/lia";
import { IconStackPush } from '@tabler/icons-react';
import { LuFileStack } from "react-icons/lu";
import { IconChartArrowsVertical } from '@tabler/icons-react';
const Management = () => {
  const iconStyle = { width: rem(16), height: rem(16) };
  const [activeTab, setActiveTab] = useState<string | null>('Overviews');
  return (
    <>
      <Container
        fluid
        className="mb-[60px] w-full overflow-hidden rounded-b-lg p-1 shadow-lg "
      >
         <Tabs value={activeTab} onChange={setActiveTab}>
      <Tabs.List>
        <Tabs.Tab value="Overviews" leftSection={<IconWindow style={iconStyle} />}>
        Overviews
        {activeTab === 'Overviews' ? (
            <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
              12
            </span>
        ) : (
          <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
            12
          </span>
        )}
        </Tabs.Tab>
        <Tabs.Tab
              value="History"
              leftSection={<RiHistoryLine style={iconStyle} />}
            >History
        {activeTab === 'History' ? (
                 <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
                 12
               </span>
             ) : (
               <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
                 2
               </span>
             )}
        </Tabs.Tab>
        <Tabs.Tab
              value="Invoices"
              leftSection={<LiaFileInvoiceSolid style={iconStyle} />}
            >
              Invoices
              {activeTab === 'Invoices' ? (
            <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
              1
            </span>
          ) : (
            <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
              2
            </span>
          )}
            </Tabs.Tab>
            <Tabs.Tab
              value="Estimates"
              leftSection={<IconChartArrowsVertical style={iconStyle} />}
            >
              Estimates
              {activeTab === 'Estimates' ? (
                 <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
                 1
               </span>
             ) : (
               <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
                 12
               </span>
             )}
            </Tabs.Tab>
            <Tabs.Tab
              value="Documents"
              leftSection={<IconBrandDatabricks style={iconStyle} />}
            >
              Documents
              {activeTab === 'Documents' ? (
                 <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
                 2
               </span>
             ) : (
               <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
                 2
               </span>
             )}
            </Tabs.Tab>
            <Tabs.Tab value="Tasks" leftSection={<LuFileStack style={iconStyle} />}>
             Tasks
             {activeTab === 'Tasks' ? (
                <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
                12
              </span>
            ) : (
              <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
                12
              </span>
            )}
            </Tabs.Tab>
            <Tabs.Tab value="Storage" leftSection={<IconStackPush style={iconStyle} />}>
             Storage
             {activeTab === 'Storage' ? (
                <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center">
                12
              </span>
            ) : (
              <span className="inline-block ml-1 bg-default text-xs px-2 py-1 rounded w-[26.95px] h-[30px] text-center border-none">
                12
              </span>
            )}
            </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="Overviews">
            <Overviews />
          </Tabs.Panel>
          <Tabs.Panel value="History">
            History
          </Tabs.Panel>
          <Tabs.Panel value="Invoices">
            Invoices
          </Tabs.Panel>
          <Tabs.Panel value="Estimates">
            <Estimates />
          </Tabs.Panel>
          <Tabs.Panel value="Documents">
            Perio
          </Tabs.Panel>
          <Tabs.Panel value="Tasks">
            Storage
          </Tabs.Panel>
          <Tabs.Panel value="Storage">
            Storage
          </Tabs.Panel>
    </Tabs>
        {/* <Tabs defaultValue="Overviews">
          <Tabs.List>
            <Tabs.Tab
              value="Overviews"
              leftSection={<IconWindow style={iconStyle} />}
            >
              Overviews

              <Button  variant="default"  size="xs"  p={6} >  12  </Button>
            </Tabs.Tab>
            <Tabs.Tab
              value="History"
              leftSection={<RiHistoryLine style={iconStyle} />}
            >
             History <Button  variant="default"  size="xs"  p={6}>  5  </Button>
            </Tabs.Tab>
            <Tabs.Tab
              value="Invoices"
              leftSection={<LiaFileInvoiceSolid style={iconStyle} />}
            >
              Invoices <Button  variant="default"  size="xs"  p={6}>  1  </Button>
            </Tabs.Tab>
            <Tabs.Tab
              value="Estimates"
              leftSection={<IconChartArrowsVertical style={iconStyle} />}
            >
              Estimates <Button  variant="default"  size="xs"  p={6}>  2  </Button>
            </Tabs.Tab>
            <Tabs.Tab
              value="Documents"
              leftSection={<IconBrandDatabricks style={iconStyle} />}
            >
              Documents <Button  variant="default"  size="xs"  p={6}>  2  </Button>
            </Tabs.Tab>
            <Tabs.Tab value="Tasks" leftSection={<LuFileStack style={iconStyle} />}>
             Tasks <Button  variant="default"  size="xs"  p={6}>  4  </Button>
            </Tabs.Tab>
            <Tabs.Tab value="Storage" leftSection={<IconStackPush style={iconStyle} />}>
             Storage <Button  variant="default"  size="xs"  p={6}>  1  </Button>
            </Tabs.Tab>
          </Tabs.List>
          <Tabs.Panel value="Overviews">
            <Overviews />
          </Tabs.Panel>
          <Tabs.Panel value="History">
            <History />
          </Tabs.Panel>
          <Tabs.Panel value="Invoices">
            <Invoices />
          </Tabs.Panel>
          <Tabs.Panel value="Estimates">
            <Perio />
          </Tabs.Panel>
          <Tabs.Panel value="Documents">
            <Perio />
          </Tabs.Panel>
          <Tabs.Panel value="Tasks">
            <Storage />
          </Tabs.Panel>
          <Tabs.Panel value="Storage">
            <Storage />
          </Tabs.Panel>
        </Tabs> */}
      </Container>
    </>
  );
};

export default Management;
