'use client';
import React, { useState } from 'react';
import {
  Box,
  Card,
  Group,
  Button,
  Select,
  TextInput,
  Table,
  Text,
  Tabs,
  Indicator,
  ActionIcon,
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconChevronDown,
  IconChevronUp,
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
} from '@tabler/icons-react';

// Import du composant Nouvel_encaissement_modal
import Nouvel_encaissement_modal from './Nouvel_encaissement_modal';
// Import du composant EtatDuComptGeneral
import EtatDuComptGeneral from './EtatDuComptGeneral';

// Interface pour les données d'encaissement
interface EncaissementData {
  id: number;
  date: string;
  patient: string;
  payeur: string;
  mode: string;
  montantEncaisse: number;
  montantConsomme: number;
  reliquat: number;
}

const Encaissements = () => {
  // État pour gérer les tabs
  const [activeTab, setActiveTab] = useState('encaissements');

  // États pour les filtres
  const [searchTerm, setSearchTerm] = useState('');
  const [docteur, setDocteur] = useState('');
  const [modePaiement, setModePaiement] = useState('');
  const [dateDebut, setDateDebut] = useState<Date | null>(new Date('2022-09-12'));
  const [dateFin, setDateFin] = useState<Date | null>(new Date('2022-09-18'));
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [isNouvelEncaissementModalOpen, setIsNouvelEncaissementModalOpen] = useState(false);

  // Données d'exemple pour les encaissements
  const encaissementsData: EncaissementData[] = [
    // Aucun élément trouvé pour correspondre à l'image
  ];

  // Filtrer les données selon les critères
  const filteredEncaissements = encaissementsData.filter(encaissement =>
    encaissement.patient.toLowerCase().includes(searchTerm.toLowerCase()) ||
    encaissement.payeur.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calcul de la pagination
  const totalPages = Math.ceil(filteredEncaissements.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentEncaissements = filteredEncaissements.slice(startIndex, startIndex + itemsPerPage);

  // Calcul des totaux
  const totalMontantEncaisse = filteredEncaissements.reduce((sum, item) => sum + item.montantEncaisse, 0);
  const totalMontantConsomme = filteredEncaissements.reduce((sum, item) => sum + item.montantConsomme, 0);
  const totalReliquat = filteredEncaissements.reduce((sum, item) => sum + item.reliquat, 0);

  // Fonction de tri
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Rendu de l'icône de tri
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <IconChevronUp size={14} /> : <IconChevronDown size={14} />;
  };
  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et bouton */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <Text size="lg" fw={600} className="text-gray-800">
              📋 Recettes
            </Text>
          </Group>

          <Button
            size="sm"
            variant="filled"
            color="blue"
            leftSection={<IconPlus size={16} />}
            className="bg-blue-500 hover:bg-blue-600"
            onClick={() => setIsNouvelEncaissementModalOpen(true)}
          >
            Nouvel encaissement
          </Button>
        </Group>
      </Card>

      {/* Onglets */}
      <Card
        shadow="none"
        padding={0}
        radius={0}
        className="bg-white border-b border-gray-200"
      >
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'encaissements')} className="w-full">
          <Tabs.List className="border-b border-gray-200">
            <Tabs.Tab
              value="encaissements"
              className="text-sm font-medium"
            >
              Encaissements
            </Tabs.Tab>
            <Tabs.Tab
              value="etat-compte"
              className="text-sm font-medium"
            >
              État du Compte général
            </Tabs.Tab>
          </Tabs.List>
        </Tabs>
      </Card>

      {/* Contenu conditionnel selon l'onglet actif */}
      {activeTab === 'encaissements' ? (
        <>
          {/* Filtres */}
          <Card
            shadow="none"
            padding="md"
            radius={0}
            className="bg-white border-b border-gray-200"
          >
            <Group gap="md" align="end">
              {/* Recherche */}
              <TextInput
                placeholder="Rechercher"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftSection={<IconSearch size={16} />}
                className="flex-1 max-w-xs"
                size="sm"
              />

              {/* Docteur */}
              <Select
                placeholder="Docteur"
                value={docteur}
                onChange={(value) => setDocteur(value || '')}
                data={['MEDECIN', 'Dr. SMITH', 'Dr. MARTIN']}
                className="w-32"
                size="sm"
              />

              {/* Mode de paiement */}
              <Select
                placeholder="Mode de paiement"
                value={modePaiement}
                onChange={(value) => setModePaiement(value || '')}
                data={['Espèces', 'Carte', 'Chèque', 'Virement']}
                className="w-40"
                size="sm"
              />

              {/* Date début */}
              <TextInput
                placeholder="Date début"
                value={dateDebut?.toLocaleDateString() || ''}
                onChange={(e) => {
                  const date = new Date(e.target.value);
                  setDateDebut(isNaN(date.getTime()) ? null : date);
                }}
                className="w-32"
                size="sm"
              />

              {/* Date fin */}
              <TextInput
                placeholder="Date fin"
                value={dateFin?.toLocaleDateString() || ''}
                onChange={(e) => {
                  const date = new Date(e.target.value);
                  setDateFin(isNaN(date.getTime()) ? null : date);
                }}
                className="w-32"
                size="sm"
              />

              {/* Boutons de période */}
              <Group gap="xs">
                <Button
                  variant="filled"
                  color="blue"
                  size="xs"
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  Aujourd&apos;hui
                </Button>
                <Button
                  variant="outline"
                  color="blue"
                  size="xs"
                >
                  Cette semaine
                </Button>
                <Button
                  variant="outline"
                  color="blue"
                  size="xs"
                >
                  Mois dernier
                </Button>
                <Button
                  variant="outline"
                  color="blue"
                  size="xs"
                >
                  Ce mois
                </Button>
                <Button
                  variant="outline"
                  color="red"
                  size="xs"
                >
                  ✕
                </Button>
              </Group>
            </Group>
          </Card>

          {/* Tableau des encaissements */}
          <div className="flex-1 bg-white overflow-hidden">
        <Table
          striped={false}
          highlightOnHover={true}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50 sticky top-0">
            <Table.Tr>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer"
                onClick={() => handleSort('date')}
              >
                <Group gap="xs" justify="space-between">
                  Date
                  {renderSortIcon('date')}
                </Group>
              </Table.Th>
              <Table.Th
                className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm cursor-pointer"
                onClick={() => handleSort('patient')}
              >
                <Group gap="xs" justify="space-between">
                  Patient / Payeur
                  {renderSortIcon('patient')}
                </Group>
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Mode
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm text-right">
                Montant encaissé
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm text-right">
                Montant consommé
              </Table.Th>
              <Table.Th className="bg-gray-100 text-gray-700 font-medium text-sm text-right">
                Reliquat
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentEncaissements.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} className="text-center py-8">
                  <Text size="sm" className="text-gray-500">
                    Aucun élément trouvé
                  </Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              currentEncaissements.map((encaissement) => (
                <Table.Tr key={encaissement.id} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.patient}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.mode}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.montantEncaisse.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-right">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.montantConsomme.toFixed(2)}
                    </Text>
                  </Table.Td>
                  <Table.Td className="text-right">
                    <Text size="sm" className="text-gray-800">
                      {encaissement.reliquat.toFixed(2)}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </div>

      {/* Footer avec totaux et pagination */}
      <Card
        shadow="none"
        padding="sm"
        radius={0}
        className="bg-white border-t border-gray-200"
      >
        {/* Ligne des totaux */}
        <div className="border-b border-gray-200 pb-2 mb-3">
          <Table>
            <Table.Tbody>
              <Table.Tr className="bg-gray-50">
                <Table.Td className="border-r border-gray-300 font-medium text-sm">
                  {/* Vide pour Date */}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 font-medium text-sm">
                  {/* Vide pour Patient */}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 font-medium text-sm">
                  {/* Vide pour Mode */}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-right font-medium text-sm">
                  {totalMontantEncaisse.toFixed(2)}
                </Table.Td>
                <Table.Td className="border-r border-gray-300 text-right font-medium text-sm">
                  {totalMontantConsomme.toFixed(2)}
                </Table.Td>
                <Table.Td className="text-right font-medium text-sm">
                  {totalReliquat.toFixed(2)}
                </Table.Td>
              </Table.Tr>
            </Table.Tbody>
          </Table>
        </div>

        {/* Légende et pagination */}
        <Group justify="space-between" align="center">
          <Group gap="md" align="center">
            {/* Légende des statuts */}
            <Group gap="sm" align="center">
              <Group gap="xs" align="center">
                <Indicator color="green" size={12} />
                <Text size="sm" className="text-gray-600">
                  Payé(e)
                </Text>
              </Group>
              <Group gap="xs" align="center">
                <Indicator color="orange" size={12} />
                <Text size="sm" className="text-gray-600">
                  Réglé(e) Partiellement
                </Text>
              </Group>
              <Group gap="xs" align="center">
                <Indicator color="red" size={12} />
                <Text size="sm" className="text-gray-600">
                  Non Réglé(e)
                </Text>
              </Group>
              <Group gap="xs" align="center">
                <Indicator color="gray" size={12} />
                <Text size="sm" className="text-gray-600">
                  Dispensé(e)/Clôturé(e)
                </Text>
              </Group>
            </Group>
          </Group>

          <Group gap="sm" align="center">
            <Text size="sm" className="text-gray-600">Page</Text>
            <Select
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(Number(value) || 1)}
              data={Array.from({ length: totalPages || 1 }, (_, i) => (i + 1).toString())}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">Lignes par Page</Text>
            <Select
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(Number(value) || 10)}
              data={['10', '25', '50', '100']}
              size="xs"
              className="w-16"
            />
            <Text size="sm" className="text-gray-600">
              0 - 0 de 0
            </Text>

            <Group gap="xs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(1)}
              >
                <IconChevronsLeft size={14} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              >
                <IconChevronLeft size={14} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              >
                <IconChevronRight size={14} />
              </ActionIcon>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(totalPages)}
              >
                <IconChevronsRight size={14} />
              </ActionIcon>
            </Group>
          </Group>
        </Group>
          </Card>

          {/* Modale pour le nouvel encaissement */}
          <Nouvel_encaissement_modal
            opened={isNouvelEncaissementModalOpen}
            onClose={() => setIsNouvelEncaissementModalOpen(false)}
          />
        </>
      ) : (
        /* Onglet État du Compte général */
        <EtatDuComptGeneral />
      )}
    </Box>
  );
};

export default Encaissements;
