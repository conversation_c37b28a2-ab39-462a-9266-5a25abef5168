import React from 'react'
// import {
//     FileType2,
//     Bookmark,
//     ListFilter,
//   } from "lucide-react";
//   import { MdOutlineCalendarMonth } from "react-icons/md";
// import { RiCalendar2Line } from "react-icons/ri";
// import { IoIosSearch } from "react-icons/io";
// import { PiCalendarPlusLight } from "react-icons/pi";
// import { HiOutlineUserGroup } from "react-icons/hi2";
// import { LiaCalendarWeekSolid } from "react-icons/lia";
import { useDisclosure, useMediaQuery } from "@mantine/hooks";
import {Chip,Badge,Tooltip,rem,Indicator ,Alert, Flex,List,Card,Textarea,Radio,Avatar,Select,AppShell,  Grid,  Paper, Button, Modal, TextInput, NumberInput, Group, Title, Text, ActionIcon, Menu, Box, Divider, Stack,Switch,ScrollArea
} from '@mantine/core';
// import SimpleBar from "simplebar-react";
import PatientRecord from './PatientRecord';
export const ToolbarCalendarNav = () => {
   const [Fichepatientpened, { open:openedFichepatient, close:closeFichepatient }] = useDisclosure(false);
  return (
    <>
      <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
              
                <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                {/* <FileType2 className="h-4 w-4  hover:text-[#3799CE]" /> */}
                FileType2
                </h2>
                
            </Tooltip>
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
              
                <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                {/* <Bookmark className="mr-2 h-4 w-4 hover:text-[#3799CE]" />{" "} */}
                </h2>
                
            </Tooltip>
       
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
            {/* <LiaCalendarWeekSolid className="h-4 w-4 hover:text-[#3799CE]" /> */}
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Fiche Patient"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              {/* <HiOutlineUserGroup  className="h-4 w-4 hover:text-[#3799CE]" onClick={() => openedFichepatient()}/>  */}
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              {/* <RiCalendar2Line className="h-4 w-4 hover:text-[#3799CE]" />  */}
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              {/* <MdOutlineCalendarMonth className="h-4 w-4 hover:text-[#3799CE]" />  */}
            </h2>
            </Tooltip>
            <span className="-ml-4 -mr-6 p-1">|</span>
            <Tooltip
              label="Gérer événement"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              {/* <PiCalendarPlusLight className="h-4 w-4 hover:text-[#3799CE]" />  */}
            </h2> 
            </Tooltip>
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Chercher Des rendez-Vous"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {/* <IoIosSearch className="h-4 w-4 hover:text-[#3799CE]" /> */}
            </h2>
            </Tooltip>
            <span className="-mx-6 p-1">|</span>
            <Tooltip
              label="Messages"
              position="bottom"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
            >
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {/* <ListFilter className="h-3.5 w-3.5 hover:text-[#3799CE]" /> */}
            </h2>
            </Tooltip>

               {/* Start Fiche patient */}
                    <Modal.Root
                    opened={Fichepatientpened}
                    onClose={closeFichepatient}
                    //title="This is a fullscreen modal"
                    fullScreen
                    radius={0}
                    transitionProps={{ transition: 'fade', duration: 200 }}
                  >
                    <Modal.Content  className="overflow-y-hidden ">
                       <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px",  }}>
                        <Modal.Title>
                          <Group justify="space-between" gap="sm">
                          <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24"
                            >
                              <g
                                fill="none"
                                stroke="currentColor"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                              >
                                <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                                <circle cx={16} cy={16} r={6}></circle>
                              </g>
                            </svg>
                            Fiche patient 
                          </Text>
                          <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
                          patient name
                          </p>
                          </Group>  
                        </Modal.Title>
                        <Modal.CloseButton className="mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96]" />
                      </Modal.Header>
                      <Modal.Body style={{ padding: '0px',}}>
                      <div className="py-2 pl-4 h-[600px]">
                        {/* <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]"> */}
                        <div className=" pr-4">
                   <PatientRecord/>
                   </div>
                   {/* </SimpleBar> */}
                   </div>
                   </Modal.Body>
                   </Modal.Content>
                  </Modal.Root>
                   {/* End Fiche patient */}
    </>
  )
}
