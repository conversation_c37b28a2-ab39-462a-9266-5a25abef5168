'use client';
import { useState } from 'react';
import {
 
  Title,
  Text,
  Paper,
  Group,
  Button,
  Tabs,
  Table,
  Badge,
  Card,
  SimpleGrid,
  ThemeIcon,
  ActionIcon,
  Tooltip,
  TextInput,
  Select,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Image,
  Grid,
  Pagination,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPill,
  IconPlus,
  IconSearch,
  IconFilter,
  IconCheck,
  IconShoppingCart,
  IconPackage,
  IconAlertCircle,
  IconTruckDelivery,
  IconClipboardList,
  IconEdit,
  IconTrash,
  IconEye,
  IconShoppingCartPlus,
  IconFileText,
} from '@tabler/icons-react';
import ListeeDesFamilles from './ListeeDesFamilles';

export default function PharmacyPage() {
  const [activeTab, setActiveTab] = useState<string | null>('inventory');
  const [opened, { open, close }] = useDisclosure(false);
  const [modalType, setModalType] = useState<'add' | 'order'>('add');
  const [selectedMedication, setSelectedMedication] = useState<any>(null);

  // Mock data for medications
  const medications = [
    { id: 'MED-001', name: 'Amoxicillin', category: 'antibiotic', stock: 120, unit: 'tablets', price: 0.75, threshold: 30 },
    { id: 'MED-002', name: 'Lisinopril', category: 'cardiovascular', stock: 85, unit: 'tablets', price: 0.50, threshold: 25 },
    { id: 'MED-003', name: 'Metformin', category: 'diabetes', stock: 45, unit: 'tablets', price: 0.35, threshold: 20 },
    { id: 'MED-004', name: 'Ibuprofen', category: 'pain', stock: 200, unit: 'tablets', price: 0.15, threshold: 50 },
    { id: 'MED-005', name: 'Albuterol', category: 'respiratory', stock: 15, unit: 'inhalers', price: 25.00, threshold: 5 },
  ];

  // Mock data for orders
  const orders = [
    { id: 'ORD-001', date: '2023-05-15', supplier: 'MedSupply Inc.', items: 3, status: 'delivered', total: 450.00 },
    { id: 'ORD-002', date: '2023-05-20', supplier: 'PharmaDirect', items: 2, status: 'processing', total: 320.00 },
    { id: 'ORD-003', date: '2023-05-25', supplier: 'MedSupply Inc.', items: 5, status: 'pending', total: 780.00 },
  ];

  const handleAddMedication = () => {
    setSelectedMedication(null);
    setModalType('add');
    open();
  };

  const handlePlaceOrder = () => {
    setModalType('order');
    open();
  };

  const handleEditMedication = (medication: any) => {
    setSelectedMedication(medication);
    setModalType('add');
    open();

    notifications.show({
      title: 'Edit Medication',
      message: `Editing ${medication.name}`,
      color: 'blue',
    });
  };

  const handleOrderMedication = (medication: any) => {
    setSelectedMedication(medication);
    setModalType('order');
    open();

    notifications.show({
      title: 'Order Medication',
      message: `Adding ${medication.name} to order`,
      color: 'green',
    });
  };

  const handleDeleteMedication = (medication: any) => {
    notifications.show({
      title: 'Delete Medication',
      message: `${medication.name} has been removed from inventory.`,
      color: 'red',
    });
  };

  const handleViewOrder = (order: any) => {
    notifications.show({
      title: 'View Order Details',
      message: `Viewing details for order ${order.id}`,
      color: 'blue',
    });
  };

  const handleCancelOrder = (order: any) => {
    notifications.show({
      title: 'Cancel Order',
      message: `Order ${order.id} has been cancelled.`,
      color: 'red',
    });
  };

  const handleAddToOrder = (medication: any) => {
    notifications.show({
      title: 'Added to Order',
      message: `${medication.name} has been added to your order.`,
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  const handleSubmit = () => {
    notifications.show({
      title: modalType === 'add' ? 'Medication Added' : 'Order Placed',
      message: modalType === 'add'
        ? 'The medication has been added to inventory.'
        : 'Your order has been placed successfully.',
      color: 'green',
      icon: <IconCheck size={16} />,
    });
    close();
  };

  const getStockStatus = (stock: number, threshold: number) => {
    if (stock <= 0) {
      return { color: 'red', label: 'Out of Stock' };
    } else if (stock <= threshold) {
      return { color: 'orange', label: 'Low Stock' };
    } else {
      return { color: 'green', label: 'In Stock' };
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'green';
      case 'processing':
        return 'blue';
      case 'pending':
        return 'yellow';
      default:
        return 'gray';
    }
  };

  return (
    <>
      <Paper p="xl" radius="md" withBorder mb="xl" w={"100%"}>
        <Group justify="space-between" mb="md">
          <div>
            <Title order={2}>Pharmacy & Medication Management</Title>
            <Text c="dimmed">Manage your medication inventory and orders</Text>
          </div>
          <Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={handleAddMedication}
            >
              Add Medication
            </Button>
            <Button
              variant="outline"
              leftSection={<IconShoppingCart size={16} />}
              onClick={handlePlaceOrder}
            >
              Place Order
            </Button>
          </Group>
        </Group>

        <SimpleGrid cols={{ base: 1, md: 3 }} mt="xl">
          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="blue">
                <IconPill size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Total Medications</Text>
                <Text fw={700} size="xl">42</Text>
              </div>
            </Group>
          </Card>

          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="orange">
                <IconAlertCircle size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Low Stock Items</Text>
                <Text fw={700} size="xl">5</Text>
              </div>
            </Group>
          </Card>

          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="green">
                <IconTruckDelivery size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Pending Orders</Text>
                <Text fw={700} size="xl">2</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>
      </Paper>
 <Paper p="xl" radius="md" withBorder mb="xl" w={"100%"}>
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List mb="md">
          <Tabs.Tab value="inventory" leftSection={<IconPackage size={16} />}>
            Inventory
          </Tabs.Tab>
          <Tabs.Tab value="orders" leftSection={<IconClipboardList size={16} />}>
            Orders
          </Tabs.Tab>
          <Tabs.Tab value="catalog" leftSection={<IconPill size={16} />}>
            Medication Catalog
          </Tabs.Tab>
          <Tabs.Tab value="families" leftSection={<IconFileText size={16} />}>
            Liste des familles
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="inventory">
          <Paper p="md" withBorder>
            <Group mb="md">
              <TextInput
                placeholder="Search medications"
                leftSection={<IconSearch size={16} />}
                style={{ flex: 1 }}
              />
              <Select
                placeholder="Filter by category"
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: 'all', label: 'All Categories' },
                  { value: 'antibiotic', label: 'Antibiotics' },
                  { value: 'cardiovascular', label: 'Cardiovascular' },
                  { value: 'diabetes', label: 'Diabetes' },
                  { value: 'pain', label: 'Pain Relief' },
                  { value: 'respiratory', label: 'Respiratory' },
                ]}
                defaultValue="all"
              />
              <Select
                placeholder="Filter by status"
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: 'all', label: 'All Status' },
                  { value: 'in-stock', label: 'In Stock' },
                  { value: 'low-stock', label: 'Low Stock' },
                  { value: 'out-of-stock', label: 'Out of Stock' },
                ]}
                defaultValue="all"
              />
            </Group>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>ID</Table.Th>
                  <Table.Th>Name</Table.Th>
                  <Table.Th>Category</Table.Th>
                  <Table.Th>Stock</Table.Th>
                  <Table.Th>Unit</Table.Th>
                  <Table.Th>Price</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {medications.map((medication) => {
                  const status = getStockStatus(medication.stock, medication.threshold);
                  return (
                    <Table.Tr key={medication.id}>
                      <Table.Td>{medication.id}</Table.Td>
                      <Table.Td>{medication.name}</Table.Td>
                      <Table.Td style={{ textTransform: 'capitalize' }}>{medication.category}</Table.Td>
                      <Table.Td>{medication.stock}</Table.Td>
                      <Table.Td>{medication.unit}</Table.Td>
                      <Table.Td>${medication.price.toFixed(2)}</Table.Td>
                      <Table.Td>
                        <Badge color={status.color}>
                          {status.label}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="Edit">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => handleEditMedication(medication)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Order">
                            <ActionIcon
                              variant="subtle"
                              color="green"
                              onClick={() => handleOrderMedication(medication)}
                            >
                              <IconShoppingCartPlus size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Delete">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteMedication(medication)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  );
                })}
              </Table.Tbody>
            </Table>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="orders">
          <Paper p="md" withBorder>
            <Group mb="md">
              <TextInput
                placeholder="Search orders"
                leftSection={<IconSearch size={16} />}
                style={{ flex: 1 }}
              />
              <Select
                placeholder="Filter by status"
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: 'all', label: 'All Status' },
                  { value: 'pending', label: 'Pending' },
                  { value: 'processing', label: 'Processing' },
                  { value: 'delivered', label: 'Delivered' },
                ]}
                defaultValue="all"
              />
            </Group>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Order ID</Table.Th>
                  <Table.Th>Date</Table.Th>
                  <Table.Th>Supplier</Table.Th>
                  <Table.Th>Items</Table.Th>
                  <Table.Th>Total</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {orders.map((order) => (
                  <Table.Tr key={order.id}>
                    <Table.Td>{order.id}</Table.Td>
                    <Table.Td>{order.date}</Table.Td>
                    <Table.Td>{order.supplier}</Table.Td>
                    <Table.Td>{order.items}</Table.Td>
                    <Table.Td>${order.total.toFixed(2)}</Table.Td>
                    <Table.Td>
                      <Badge color={getOrderStatusColor(order.status)} style={{ textTransform: 'capitalize' }}>
                        {order.status}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <Tooltip label="View Details">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleViewOrder(order)}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                        </Tooltip>
                        {order.status === 'pending' && (
                          <Tooltip label="Cancel Order">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleCancelOrder(order)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        )}
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="catalog">
          <Paper p="md" withBorder>
            <Group mb="md">
              <TextInput
                placeholder="Search medications"
                leftSection={<IconSearch size={16} />}
                style={{ flex: 1 }}
              />
              <Select
                placeholder="Filter by category"
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: 'all', label: 'All Categories' },
                  { value: 'antibiotic', label: 'Antibiotics' },
                  { value: 'cardiovascular', label: 'Cardiovascular' },
                  { value: 'diabetes', label: 'Diabetes' },
                  { value: 'pain', label: 'Pain Relief' },
                  { value: 'respiratory', label: 'Respiratory' },
                ]}
                defaultValue="all"
              />
            </Group>

            <Grid gutter="md">
              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card withBorder p="md">
                  <Card.Section>
                    <Image
                      src="https://placehold.co/400x200/e6f7ff/0099cc?text=Amoxicillin"
                      height={160}
                      alt="Amoxicillin"
                    />
                  </Card.Section>
                  <Group justify="space-between" mt="md" mb="xs">
                    <Text fw={500}>Amoxicillin</Text>
                    <Badge color="blue">Antibiotic</Badge>
                  </Group>
                  <Text size="sm" c="dimmed" mb="md">
                    Broad-spectrum antibiotic used to treat bacterial infections.
                  </Text>
                  <Group justify="space-between">
                    <Text fw={700}>${(0.75).toFixed(2)} per tablet</Text>
                    <Button
                      variant="light"
                      leftSection={<IconShoppingCartPlus size={16} />}
                      onClick={() => handleAddToOrder({ name: 'Amoxicillin' })}
                    >
                      Add to Order
                    </Button>
                  </Group>
                </Card>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card withBorder p="md">
                  <Card.Section>
                    <Image
                      src="https://placehold.co/400x200/e6f7ff/0099cc?text=Lisinopril"
                      height={160}
                      alt="Lisinopril"
                    />
                  </Card.Section>
                  <Group justify="space-between" mt="md" mb="xs">
                    <Text fw={500}>Lisinopril</Text>
                    <Badge color="green">Cardiovascular</Badge>
                  </Group>
                  <Text size="sm" c="dimmed" mb="md">
                    ACE inhibitor used to treat high blood pressure and heart failure.
                  </Text>
                  <Group justify="space-between">
                    <Text fw={700}>${(0.50).toFixed(2)} per tablet</Text>
                    <Button
                      variant="light"
                      leftSection={<IconShoppingCartPlus size={16} />}
                      onClick={() => handleAddToOrder({ name: 'Lisinopril' })}
                    >
                      Add to Order
                    </Button>
                  </Group>
                </Card>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card withBorder p="md">
                  <Card.Section>
                    <Image
                      src="https://placehold.co/400x200/e6f7ff/0099cc?text=Metformin"
                      height={160}
                      alt="Metformin"
                    />
                  </Card.Section>
                  <Group justify="space-between" mt="md" mb="xs">
                    <Text fw={500}>Metformin</Text>
                    <Badge color="orange">Diabetes</Badge>
                  </Group>
                  <Text size="sm" c="dimmed" mb="md">
                    First-line medication for the treatment of type 2 diabetes.
                  </Text>
                  <Group justify="space-between">
                    <Text fw={700}>${(0.35).toFixed(2)} per tablet</Text>
                    <Button
                      variant="light"
                      leftSection={<IconShoppingCartPlus size={16} />}
                      onClick={() => handleAddToOrder({ name: 'Metformin' })}
                    >
                      Add to Order
                    </Button>
                  </Group>
                </Card>
              </Grid.Col>
            </Grid>

            <Group justify="center" mt="xl">
              <Pagination total={10} />
            </Group>
          </Paper>
        </Tabs.Panel>
      </Tabs>
</Paper>
      <Modal opened={opened} onClose={close} title={modalType === 'add' ? "Add Medication" : "Place Order"} size="lg">
        <div>
          {modalType === 'add' ? (
            <>
              <TextInput
                label="Medication Name"
                placeholder="Enter medication name"
                required
                mb="md"
              />

              <Select
                label="Category"
                placeholder="Select category"
                data={[
                  { value: 'antibiotic', label: 'Antibiotic' },
                  { value: 'cardiovascular', label: 'Cardiovascular' },
                  { value: 'diabetes', label: 'Diabetes' },
                  { value: 'pain', label: 'Pain Relief' },
                  { value: 'respiratory', label: 'Respiratory' },
                  { value: 'other', label: 'Other' },
                ]}
                required
                mb="md"
              />

              <Group grow mb="md">
                <NumberInput
                  label="Initial Stock"
                  placeholder="0"
                  min={0}
                  required
                />

                <TextInput
                  label="Unit"
                  placeholder="tablets, bottles, etc."
                  required
                />
              </Group>

              <Group grow mb="md">
                <NumberInput
                  label="Price per Unit"
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                  prefix="$"
                  required
                />

                <NumberInput
                  label="Low Stock Threshold"
                  placeholder="0"
                  min={0}
                  required
                />
              </Group>

              <Textarea
                label="Description"
                placeholder="Enter medication description"
                mb="md"
              />

              <TextInput
                label="Manufacturer"
                placeholder="Enter manufacturer name"
                mb="md"
              />

              <Divider my="md" label="Storage Information" labelPosition="center" />

              <Group grow mb="md">
                <TextInput
                  label="Storage Conditions"
                  placeholder="e.g., Room temperature"
                />

                <TextInput
                  label="Expiration Date Format"
                  placeholder="e.g., MM/YYYY"
                />
              </Group>
            </>
          ) : (
            <>
              <Select
                label="Supplier"
                placeholder="Select supplier"
                data={[
                  { value: 'medsupply', label: 'MedSupply Inc.' },
                  { value: 'pharmadirect', label: 'PharmaDirect' },
                  { value: 'globalpharm', label: 'Global Pharmaceuticals' },
                  { value: 'medexpress', label: 'MedExpress' },
                ]}
                required
                mb="md"
              />

              <Divider my="md" label="Order Items" labelPosition="center" />

              <Group mb="md" grow>
                <Select
                  label="Medication"
                  placeholder="Select medication"
                  data={medications.map(med => ({ value: med.id, label: med.name }))}
                />
                <NumberInput
                  label="Quantity"
                  placeholder="0"
                  min={1}
                />
              </Group>

              <Group mb="md" grow>
                <Select
                  label="Medication"
                  placeholder="Select medication"
                  data={medications.map(med => ({ value: med.id, label: med.name }))}
                />
                <NumberInput
                  label="Quantity"
                  placeholder="0"
                  min={1}
                />
              </Group>

              <Button variant="outline" fullWidth mb="md">
                Add Another Item
              </Button>

              <Divider my="md" />

              <TextInput
                label="Purchase Order Number"
                placeholder="Enter PO number (optional)"
                mb="md"
              />

              <Textarea
                label="Notes"
                placeholder="Enter any additional notes for this order"
                mb="md"
              />

              <Group justify="space-between" mb="md">
                <Text fw={500}>Estimated Total:</Text>
                <Text>$0.00</Text>
              </Group>
            </>
          )}

          <Group justify="flex-end" mt="xl">
            <Button variant="outline" onClick={close}>Cancel</Button>
            <Button onClick={handleSubmit}>{modalType === 'add' ? 'Add Medication' : 'Place Order'}</Button>
          </Group>
        </div>
      </Modal>
    </>
  );
}
