export interface PathData {
    svg_id: string;
    path_id: string;
    color: string;
  }
  
  // export interface DentalPath {
  //   id: string;
  //   code: string;
  //   path: string;
  //   polygon?: string;
  //   // style: React.CSSProperties;
  //   style: {
  //     cursor: string;
  //     [key: string]: string;
  //   };
  // }
  export interface DentalPath {
    id: string;
    code: string;
    path?: string;  //Optional for paths that use 'polygon' instead
    paths?: {
      id: string;
      path: string;
      code: string;
      style?: React.CSSProperties;
       transforms?: string;
    }[];
    position: string;
    polygon?: string; // Optional for paths that use 'path'
    transforms?: string;
    style: {
      cursor: string;
      [key: string]: string;
    };
    rect?: {
      x: string;
      y: string;
      width: string;
      height: string;
    };
  }
  export interface DentalSvg {
    svg_id: string; // Change string -> number
    width: string;
    position: string;
    paths: DentalPath[];
    polygon?: DentalPath[];
    tooth_number:number;
    tooth_name:string;
    tooth_type:string;
    quadrant:string;
    tooth_position:number;
    is_permanent:boolean;
    // pathsreplaceFluoride: DentalPath[];
    // pathsreplaceLine: DentalPath[];
    // pathsreplaceImplant: DentalPath[];
    // pathsreplaceResection: DentalPath[];
    // pathsreplaceCrown: DentalPath[];
    // pathsreplaceDenture: DentalPath[];
    // pathsreplaceWhitening: DentalPath[];
    // pathsreplaceOnlay: DentalPath[];
    // pathsreplaceVeneer: DentalPath[];
    // pathsreplaceBridge: DentalPath[];
    // pathsreplacePostCare: DentalPath[];
    // pathsreplaceSealant: DentalPath[];
    // pathsreplaceCleaning: DentalPath[];
    // pathsreplaceCleaning1: DentalPath[];
    // pathsreplaceCleaning2: DentalPath[];
    // pathsreplaceCleaning3: DentalPath[];
    // pathsreplaceCleaning4: DentalPath[];
    // pathsreplaceCleaning5: DentalPath[];
    // pathsreplaceCleaning6: DentalPath[];
    // pathsreplaceCleaning7: DentalPath[];
    // pathsreplaceCleaning8: DentalPath[];
    // pathsreplaceCleaning9: DentalPath[];
    // pathsreplaceCleaning10: DentalPath[];
    // pathsreplaceBone: DentalPath[];

  }
 
  export interface HighlightedPathStyles {
    fill?: string;
    stroke?: string;
  }
  
  export interface ApiPath {
    svg_id: string;
    path_id: string;
    fill_color: string | null;
    stroke_color: string | null;
  }
  
  export interface ApiResponse {
    paths: ApiPath[];
    success: boolean;
    message?: string;
  }
// ****************
export interface ButtonState {
  isActive: boolean;
  toggle: () => void;
  activate: () => void;
  deactivate: () => void;
}

export interface DentalButtonProps {
  isActive?: boolean;
  onClick?: () => void;
}

export interface ColorButtonProps extends DentalButtonProps {
  onColorSelect: (color: string, target: ColorTarget) => void;
  selectedTeeth: string[];
}


export type ColorTarget = "fill" | "stroke" | "both"; 
export interface DentalPosition {
  quadrant: number;
  tooth: number;
}