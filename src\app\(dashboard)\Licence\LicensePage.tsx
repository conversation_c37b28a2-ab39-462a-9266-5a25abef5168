'use client';

import { useState, useEffect } from 'react';
import { Container, Title, Tabs, Text, Alert } from '@mantine/core';
import { IconLicense, IconKey, IconHistory, IconAlertCircle } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import LicenseInfo from '@/components/license/LicenseInfo';
import LicenseActivation from '@/components/license/LicenseActivation';

export default function LicensePage() {
  const searchParams = useSearchParams();
  const message = searchParams?.get('message') || null;
  const [activeTab, setActiveTab] = useState<string | null>('info');

  // Set the active tab based on the message parameter
  useEffect(() => {
    if (message === 'pending_activation') {
      setActiveTab('activate');
    }
  }, [message]);

  // Get message text based on the message parameter
  const getMessageText = () => {
    switch (message) {
      case 'no_license':
        return 'You do not have a license yet. Please contact support to obtain one.';
      case 'pending_activation':
        return 'Your license is pending activation. Please enter your activation code to activate it.';
      case 'expired':
        return 'Your license has expired. Please renew it to continue using the application.';
      case 'revoked':
        return 'Your license has been revoked. Please contact support for assistance.';
      default:
        return null;
    }
  };

  const messageText = getMessageText();

  return (
    <Container size="lg" py="xl">
      <Title order={2} mb="xl">License Management</Title>

      {messageText && (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          color={message === 'pending_activation' ? 'blue' : 'red'}
          mb="xl"
        >
          {messageText}
        </Alert>
      )}

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="info" leftSection={<IconLicense size="1rem" />}>
            License Information
          </Tabs.Tab>
          <Tabs.Tab value="activate" leftSection={<IconKey size="1rem" />}>
            Activate License
          </Tabs.Tab>
          <Tabs.Tab value="history" leftSection={<IconHistory size="1rem" />}>
            License History
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="info" pt="xl">
          <LicenseInfo />
        </Tabs.Panel>

        <Tabs.Panel value="activate" pt="xl">
          <LicenseActivation onActivationSuccess={() => setActiveTab('info')} />
        </Tabs.Panel>

        <Tabs.Panel value="history" pt="xl">
          <Text>License history will be available in a future update.</Text>
        </Tabs.Panel>
      </Tabs>
    </Container>
  );
}
