// frontend/dental_medicine/src/components/content/dental/specialties/Prosthodontics/ProsthodonticControls.tsx

"use client";
import React, { useState, useCallback } from "react";
import { Button, Tooltip, Menu, rem, Group, Radio, Avatar, Text } from '@mantine/core';
import { IconSquareRoundedPlusFilled, IconFileZip, IconEye, IconTrash } from '@tabler/icons-react';
import { CheckIcon } from "@mantine/core";
import { DentalControlsProps } from '../../shared/types';
import { useDentalInteraction } from '@/hooks/useDentalInteraction';
import { notifications } from '@mantine/notifications';

// Interface pour les contrôles prosthodontiques (hérite de DentalControlsProps)
type ProsthodonticControlsProps = DentalControlsProps;

export const ProsthodonticControls: React.FC<ProsthodonticControlsProps> = ({
  activeButton,
  onButtonClick,
  onTargetPathChange
}) => {
  const [, setChecked] = useState(false);

  // Hook pour la gestion des interactions dentaires
  const {
    handleButtonClick: handleTreatmentClick,
    applyTreatmentToSelectedTeeth,
    getButtonInfo,
    hasSelectedTeeth,
    canApplyTreatment
  } = useDentalInteraction({
    specialty: 'prosthetic',
    patientId: '123e4567-e89b-12d3-a456-************', // UUID de test valide
    showNotifications: true,
    autoSave: false
  });

  // Gestionnaire unifié pour les clics de boutons
  const handleButtonClickWithBackend = useCallback(async (buttonId: string, pathId: string) => {
    try {
      // 1. Mettre à jour l'état local (UI)
      onButtonClick(buttonId);
      onTargetPathChange(pathId);

      // 2. Gérer l'interaction avec le backend
      await handleTreatmentClick(buttonId);

      // 3. Afficher une notification de succès
      notifications.show({
        title: 'Traitement sélectionné',
        message: `${getButtonInfo(buttonId)?.label || buttonId} activé`,
        color: 'blue',
        autoClose: 2000
      });

    } catch (error) {
      console.error('Erreur lors de l\'activation du traitement:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible d\'activer le traitement',
        color: 'red',
        autoClose: 3000
      });
    }
  }, [onButtonClick, onTargetPathChange, handleTreatmentClick, getButtonInfo]);

  // Gestionnaire pour appliquer le traitement aux dents sélectionnées
  const handleApplyTreatment = useCallback(async () => {
    if (!activeButton) {
      notifications.show({
        title: 'Aucun traitement sélectionné',
        message: 'Veuillez d\'abord sélectionner un traitement',
        color: 'orange'
      });
      return;
    }

    try {
      const result = await applyTreatmentToSelectedTeeth(activeButton);

      notifications.show({
        title: 'Traitement appliqué',
        message: `${result.modificationsCreated} modifications créées`,
        color: 'green'
      });

    } catch (error) {
      console.error('Erreur lors de l\'application du traitement:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible d\'appliquer le traitement',
        color: 'red'
      });
    }
  }, [activeButton, applyTreatmentToSelectedTeeth]);
    const simpleControls = [
    {
      id: 'Veneer',
      label: 'Veneer',
      pathId: '37',
      tooltip: 'Veneer',
      shortCode: 'Ve',
      icon: (
      <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 13.14 16.28"><defs>
      </defs>
        <path style={{ fill: "#ffffff",  stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10  }} d="M14.86,15.58a3.7,3.7,0,0,0-2.39-.94h-.35a.79.79,0,0,0-.7.4c-.44.82-1.07,1.37-1.56,1.37-.31,0-.68-.1-.88-.94A.64.64,0,0,0,8.35,15a.66.66,0,0,0-.64.47c-.2.83-.57.94-.87.94-.49,0-1.12-.55-1.57-1.37a.78.78,0,0,0-.69-.41H4.23a3.71,3.71,0,0,0-**********.18,0,0,0,0,.27.23.23,0,0,0,.29,0A3.31,3.31,0,0,1,4.23,15h.35a.36.36,0,0,1,.33.19,2.57,2.57,0,0,0,1.93,1.58c.65,0,1.08-.42,1.28-1.24a.24.24,0,0,1,.46,0c.2.82.63,1.24,1.28,1.24a2.57,2.57,0,0,0,1.93-**********,0,0,1,.33-.19h.35a3.26,3.26,0,0,1,*********.23,0,0,0,.29,0A.18.18,0,0,0,14.86,15.58Z" transform="translate(-1.78 -0.51)"/>
        <polygon style={{ fill: "#3799CE"}} points="11.08 5.28 2.32 5.39 1.18 4.19 1.33 2.1 2.43 0.82 3.93 0.29 5.58 0.53 7.14 1.97 6.78 1.11 7.96 0.46 9.68 0.29 11.03 0.94 11.96 2.22 12.07 3.69 11.08 5.28"/>
        <polygon style={{ fill: "#ffffff" }} points="4.9 6.29 3.56 6.44 2.24 7.63 2.1 8.75 2.54 10.11 3.4 10.99 3.5 11.88 3.61 13.22 4.25 14.57 4.9 15.11 5.58 14.64 5.79 11.3 6.65 10.79 7.17 11.03 7.51 11.46 7.57 13 7.67 14.03 7.89 15.04 8.32 15.06 8.65 14.82 9.29 13.93 9.65 12.79 9.72 11.26 10.04 10.7 10.71 10.05 11.11 9.14 10.96 7.72 9.51 6.38 8.25 6.34 6.57 6.61 4.9 6.29"/>
    </svg>
      )
    },
   {
      id: 'Onlay',
      label: 'Onlay',
      pathId: '39',
      tooltip: 'Onlay',
      shortCode: 'On',
      icon: (
     <svg xmlns="http://www.w3.org/2000/svg" id="Calque_1" data-name="Calque 1" viewBox="0 0 14.67 15.82">
      <path style={{ fill: "#3799CE", }} d="M14.21,1.49a4.51,4.51,0,0,0-3-1,5.28,5.28,0,0,0-2,.36,10.44,10.44,0,0,1-1.58.43A8.1,8.1,0,0,0,4.58.75,4.13,4.13,0,0,0,1.4,2.54a2.89,2.89,0,0,0-.***********,0,0,0,0,.11A4.89,4.89,0,0,0,1.14,6,23,23,0,0,0,2.27,8.86l.2.44a10.63,10.63,0,0,1,.6,3,6.37,6.37,0,0,0,.75,2.46,2.38,2.38,0,0,0,2,1.48h.07A1,1,0,0,0,6.6,16c.67-.67.55-2.31.42-3.23a1.37,1.37,0,0,1,.11-.81,1,1,0,0,1,.86-.6h.59a1,1,0,0,1,.85.61,1.39,1.39,0,0,1,.11.81c-.13.91-.26,2.55.41,3.23a1,1,0,0,0,.73.31,1.82,1.82,0,0,0,1.19-.41,5.31,5.31,0,0,0,1.61-3.54,10.32,10.32,0,0,1,.43-2.76c.28-.78.52-1.53.78-2.28l.61-1.8A3.36,3.36,0,0,0,14.21,1.49Zm.68,3.89c-.21.61-.41,1.21-.62,1.8-.25.75-.5,1.5-.78,2.28a10.71,10.71,0,0,0-.44,2.86,5.08,5.08,0,0,1-1.47,3.27,1.36,1.36,0,0,1-.***********,0,0,1-.39-.17c-.4-.4-.51-1.49-.31-2.92a1.69,1.69,0,0,0-.15-1,1.47,1.47,0,0,0-1.22-.85s-.17,0-.37,0a2.66,2.66,0,0,0-.34,0,1.49,1.49,0,0,0-1.23.85,1.75,1.75,0,0,0-.14,1c.21,1.43.09,2.52-.31,2.92a.6.6,0,0,1-.39.17h0c-1.33,0-2.2-2.35-2.28-3.58a10.28,10.28,0,0,0-.63-3.15l-.2-.45a23.76,23.76,0,0,1-1.1-2.79,4.41,4.41,0,0,1-.13-2.39A9.51,9.51,0,0,1,4.54,3a5.49,5.49,0,0,1,1.9.49A6.12,6.12,0,0,0,8.81,4a5.24,5.24,0,0,0,2.29-.61A3.44,3.44,0,0,1,12.89,3,9.26,9.26,0,0,1,15,3.43,3.38,3.38,0,0,1,14.89,5.38Z" transform="translate(-0.89 -0.47)"/>
      <path style={{ fill: "#ffffff",     stroke: "#3799ce",strokeWidth: "0.5",     strokeMiterlimit: "10"}} d="M4.58.75,14.89,5.38c-.21.61-.41,1.21-.62,1.8-.25.75-.5,1.5-.78,2.28a10.71,10.71,0,0,0-.44,2.86,5.08,5.08,0,0,1-1.47,3.27,1.36,1.36,0,0,1-.***********,0,0,1-.39-.17c-.4-.4-.51-1.49-.31-2.92a1.69,1.69,0,0,0-.15-1,1.47,1.47,0,0,0-1.22-.85s-.17,0-.37,0a2.66,2.66,0,0,0-.34,0,1.49,1.49,0,0,0-1.23.85,1.75,1.75,0,0,0-.14,1c.21,1.43.09,2.52-.31,2.92a.6.6,0,0,1-.39.17h0c-1.33,0-2.2-2.35-2.28-3.58a10.28,10.28,0,0,0-.63-3.15l-.2-.45a23.76,23.76,0,0,1-1.1-2.79,4.41,4.41,0,0,1-.13-2.39A9.51,9.51,0,0,1,4.54,3a5.49,5.49,0,0,1,1.9.49A6.12,6.12,0,0,0,8.81,4a5.24,5.24,0,0,0,2.29-.61A3.44,3.44,0,0,1,12.89,3,9.26,9.26,0,0,1,15,3.43,3.38,3.38,0,0,1,14.89,5.38Z" transform="translate(-0.89 -0.47)"/>
      </svg>
      )
    },
     {
      id: 'Denture',
      label: 'Denture',
      pathId: '48',
      tooltip: 'Denture',
      shortCode: 'De',
      icon: (
     <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" viewBox="0 0 200 250" x="0px" y="0px">
           <path style={{ fill: "#f5f5f5" }} d="M81.50276,99.34975a19.183,19.183,0,0,0,13.56049.01477,3.9079,3.9079,0,0,0,2.52856-3.03333,41.42989,41.42989,0,0,0,.21606-12.35046A12.464,12.464,0,0,0,94.22243,76.737c-3.28333-3.191-6.94879-3.19055-11.0387.1048a13.08632,13.08632,0,0,0-4.73358,8.96558A38.501,38.501,0,0,0,78.99074,96.4,3.90159,3.90159,0,0,0,81.50276,99.34975Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M74.933,74.69441a11.20236,11.20236,0,0,0-10.8407,1.18414,5.51714,5.51714,0,0,0-2.36273,3.24109,30.25884,30.25884,0,0,0-.29468,14.50983,5.61916,5.61916,0,0,0,3.99158,4.114,19.26465,19.26465,0,0,0,9.92346.139,7.76628,7.76628,0,0,1-.189-.78943,42.63142,42.63142,0,0,1-.58374-11.66351,17.28758,17.28758,0,0,1,3.17993-8.44464A6.44071,6.44071,0,0,0,74.933,74.69441Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M57.03749,72.75417a10.56981,10.56981,0,0,0-9.63989,1.33429,5.11786,5.11786,0,0,0-2.20709,2.99646,28.48221,28.48221,0,0,0-.30573,13.65369A5.26216,5.26216,0,0,0,48.60951,94.658a18.10067,18.10067,0,0,0,9.12842.21124c-.03333-.11676-.06671-.23907-.09448-.36133a34.2021,34.2021,0,0,1,.32245-16.37781,9.41705,9.41705,0,0,1,1.78455-3.519A6.41461,6.41461,0,0,0,57.03749,72.75417Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M104.93874,99.36452a19.18248,19.18248,0,0,0,13.5603-.01477A3.90178,3.90178,0,0,0,121.01112,96.4a38.50358,38.50358,0,0,0,.54065-10.59259,13.08639,13.08639,0,0,0-4.73364-8.96558c-4.08984-3.29535-7.75537-3.29578-11.0387-.1048a12.46387,12.46387,0,0,0-3.58533,7.24371,41.42975,41.42975,0,0,0,.21582,12.35046A3.90819,3.90819,0,0,0,104.93874,99.36452Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M124.65187,97.8824a19.26512,19.26512,0,0,0,9.92358-.139,5.6192,5.6192,0,0,0,3.99146-4.114,30.25732,30.25732,0,0,0-.29468-14.50983,5.5168,5.5168,0,0,0-2.36267-3.24109,11.20238,11.20238,0,0,0-10.8407-1.18414,6.44037,6.44037,0,0,0-2.8241,2.29041,17.28757,17.28757,0,0,1,3.17993,8.44464A42.629,42.629,0,0,1,124.841,97.093,7.75034,7.75034,0,0,1,124.65187,97.8824Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M142.26393,94.86922a18.10067,18.10067,0,0,0,9.12842-.21124,5.26206,5.26206,0,0,0,3.72473-3.91937,28.48148,28.48148,0,0,0-.30579-13.65369,5.117,5.117,0,0,0-2.207-2.99646,10.56978,10.56978,0,0,0-9.63989-1.33429,6.41366,6.41366,0,0,0-2.71289,1.85687,9.415,9.415,0,0,1,1.78442,3.519,34.20212,34.20212,0,0,1,.32251,16.37781C142.33058,94.63014,142.29726,94.75246,142.26393,94.86922Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M78.87874,124.187a5.05264,5.05264,0,0,0,2.87317,3.82385,14.58254,14.58254,0,0,0,13.0545.0506,4.977,4.977,0,0,0,2.75024-3.67187,55.60663,55.60663,0,0,0,.16064-15.33588,5.04,5.04,0,0,0-3.95459-4.26447,26.63105,26.63105,0,0,0-11.05811-.00006A5.04016,5.04016,0,0,0,78.75,109.05367,55.70243,55.70243,0,0,0,78.87874,124.187Z"/>
           <path  style={{ fill: "#f5f5f5" }} d="M76.73976,104.19222a5.06025,5.06025,0,0,0-1.05072-.34467,25.821,25.821,0,0,0-10.72394,0,4.878,4.878,0,0,0-3.83038,4.13055,53.824,53.824,0,0,0,.12225,14.67664,4.89758,4.89758,0,0,0,2.78528,3.70813,14.17873,14.17873,0,0,0,11.58563.52258A8.60762,8.60762,0,0,1,75.033,124.784a59.82378,59.82378,0,0,1-.139-16.25549A8.96981,8.96981,0,0,1,76.73976,104.19222Z"/>
           <path style={{ fill: "#f5f5f5" }}d="M59.41682,102.80238a4.37552,4.37552,0,0,0-1.5899-.67822,23.5916,23.5916,0,0,0-9.74554,0,4.44069,4.44069,0,0,0-3.4801,3.75812,49.13206,49.13206,0,0,0,.11115,13.33685,4.46085,4.46085,0,0,0,2.52954,3.369,12.9339,12.9339,0,0,0,10.16241.61145A57.9102,57.9102,0,0,1,57.2765,107.45,8.74689,8.74689,0,0,1,59.41682,102.80238Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M102.44533,124.38954a4.97673,4.97673,0,0,0,2.75,3.67188,14.58293,14.58293,0,0,0,13.05469-.0506,5.05276,5.05276,0,0,0,2.873-3.82385,55.7,55.7,0,0,0,.12891-15.1333,5.04037,5.04037,0,0,0-3.95471-4.26453,26.63047,26.63047,0,0,0-11.058.00006,5.0398,5.0398,0,0,0-3.95459,4.26447A55.58766,55.58766,0,0,0,102.44533,124.38954Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M124.374,126.88545a14.1786,14.1786,0,0,0,11.58557-.52258,4.89772,4.89772,0,0,0,2.78528-3.70813,53.82153,53.82153,0,0,0,.12231-14.67664,4.87826,4.87826,0,0,0-3.83044-4.13055,25.82128,25.82128,0,0,0-10.724,0,5.0609,5.0609,0,0,0-1.05066.34467,8.96924,8.96924,0,0,1,1.8457,4.3363,59.82133,59.82133,0,0,1-.139,16.25549A8.60472,8.60472,0,0,1,124.374,126.88545Z"/>
           <path style={{ fill: "#f5f5f5" }} d="M152.7599,122.58809a4.46058,4.46058,0,0,0,2.52954-3.369,49.13941,49.13941,0,0,0,.11121-13.33685,4.4409,4.4409,0,0,0-3.48022-3.75812,23.5913,23.5913,0,0,0-9.74548,0,4.37716,4.37716,0,0,0-1.59.67822,8.74607,8.74607,0,0,1,2.14038,4.64758,57.90776,57.90776,0,0,1-.12793,15.74957A12.934,12.934,0,0,0,152.7599,122.58809Z"/>
           <path style={{ fill: "#3799CE" }} d="M162.42458,130.66841a41.01544,41.01544,0,0,0-4.4519-7.71344,8.31738,8.31738,0,0,1-3.58008,3.16321,17.96006,17.96006,0,0,1-7.533,1.74573,16.37692,16.37692,0,0,1-5.59827-1.00629l-.06677-.02783a8.78045,8.78045,0,0,1-3.60791,3.06879,19.37357,19.37357,0,0,1-8.1167,1.879,17.47232,17.47232,0,0,1-6.53223-1.27856l-.90613-.3559a9.0125,9.0125,0,0,1-2.15149,1.401,19.85284,19.85284,0,0,1-8.32788,1.94019,17.40325,17.40325,0,0,1-4.06934-.48358,19.24956,19.24956,0,0,1-3.9917-1.43994A8.91816,8.91816,0,0,1,100,128.531a8.87923,8.87923,0,0,1-3.49121,3.02979,18.29637,18.29637,0,0,1-8.05554,1.92352,19.73116,19.73116,0,0,1-8.32788-1.94019,8.77479,8.77479,0,0,1-2.157-1.401l-.90057.3559a17.51485,17.51485,0,0,1-6.53223,1.27856,19.3128,19.3128,0,0,1-8.12219-1.879,8.766,8.766,0,0,1-3.60242-3.06879l-.07233.02783a16.353,16.353,0,0,1-5.59821,1.00629,17.88325,17.88325,0,0,1-7.52179-1.74017A8.363,8.363,0,0,1,42.0273,122.955a41.01574,41.01574,0,0,0-4.45184,7.71344A6.68945,6.68945,0,0,0,43.1794,140.036a40.95325,40.95325,0,0,1,14.45294,3.50525c8.61456,4.13483,22.35858,8.67133,35.49939,5.50055a29.22208,29.22208,0,0,1,13.73657,0c13.14075,3.17078,26.88477-1.36572,35.49939-5.50055a40.95325,40.95325,0,0,1,14.45288-3.50525A6.68951,6.68951,0,0,0,162.42458,130.66841Z"/>
           <path style={{ fill: "#3799CE" }} d="M42.38307,73.816a8.92881,8.92881,0,0,1,2.92975-3.01312,14.50252,14.50252,0,0,1,13.03662-1.71228,10.30723,10.30723,0,0,1,4.353,2.98535l.0556.06671A14.98921,14.98921,0,0,1,76.42842,71.1031a10.309,10.309,0,0,1,4.00275,2.97418c.1-.08893.20569-.17786.31134-.26678,5.576-4.492,11.48-4.442,16.19427.13336A15.923,15.923,0,0,1,100,78.14119a15.82063,15.82063,0,0,1,3.06873-4.19733c4.70886-4.57532,10.61279-4.62537,16.18884-.13336.11108.08893.21118.17786.31689.26678a10.33673,10.33673,0,0,1,4.00269-2.9798A14.99571,14.99571,0,0,1,137.242,72.14265l.05566-.06671a10.34135,10.34135,0,0,1,4.35291-2.98535,14.51577,14.51577,0,0,1,13.04211,1.71228,8.92993,8.92993,0,0,1,2.92993,3.01312,40.35828,40.35828,0,0,0,4.3645-7.22968,6.67067,6.67067,0,0,0-6.08081-9.5556c-5.35754.09045-11.97314-.42737-17.02979-2.85583A43.08769,43.08769,0,0,0,107.5569,51.77a26.55164,26.55164,0,0,1-15.11371,0,43.08773,43.08773,0,0,0-31.31964,2.40485c-5.05658,2.42841-11.67181,2.94623-17.0293,2.85583a6.67075,6.67075,0,0,0-6.08026,9.5567A40.18759,40.18759,0,0,0,42.38307,73.816Z"/>
           </svg>
      )
    },
    {
      id: 'Bridge',
      label: 'Bridge',
      pathId: '52',
      tooltip: 'Bridge',
      shortCode: 'Br',
      icon: (
    <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 488 535" >
      <g><rect x="220.444" y="256.78" width="47.111" height="33.269"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M281.333,239.022v-9.754h-74.667v9.754c0,5.38,4.387,9.758,9.78,9.758h55.107   C276.946,248.78,281.333,244.403,281.333,239.022z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M116.417,289.357c0.709,4.793,1.362,9.615,1.985,14.447h94.042V256.31c-7.88-1.82-13.778-8.872-13.778-17.287v-9.754   h-0.591h-92.868c0.199,1.035,0.393,2.07,0.624,3.1C110.042,251.153,113.604,270.327,116.417,289.357z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M151.057,184.585h185.886c19.845-19.055,31.65-49.986,31.65-83.122C368.593,45.516,335.363,0,294.519,0   c-8.883,0-17.574,2.144-25.832,6.371c-15.458,7.915-33.916,7.916-49.374,0C211.056,2.144,202.365,0,193.481,0   c-40.845,0-74.074,45.516-74.074,101.463C119.407,134.6,131.212,165.531,151.057,184.585z"/>
      <rect x="220.444" y="298.049" width="47.111" height="28.683"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M220.444,353.614c0,7.932,6.466,14.386,14.415,14.386h18.282c7.948,0,14.415-6.454,14.415-14.386v-18.882h-47.111V353.614z   "/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M433.076,303.805H488v-83.419C469.059,237.828,446.242,265.221,433.076,303.805z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M202.667,221.268h82.667h4.591c5.392,0,9.779-4.377,9.779-9.758v-18.925H188.296v18.925c0,5.38,4.387,9.758,9.779,9.758   H202.667z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M441.481,0c-30.755,0-58.527,26.422-69.354,65.842c2.891,11.172,4.465,23.156,4.465,35.621   c0,12.291-1.546,24.314-4.464,35.634c2.146,7.809,4.994,15.253,8.487,22.134c11.696,23.035,15.02,49.63,9.358,74.887   c-4.168,18.593-7.693,37.572-10.478,56.409c-2.443,16.525-4.377,33.352-5.748,50.013c-0.491,5.964,1.469,11.669,5.519,16.065   c4.044,4.391,9.563,6.809,15.541,6.809c10.778,0,19.805-8.06,20.997-18.748c1.635-14.658,4.845-29.124,9.542-42.995   c14.947-44.145,42.025-74.232,62.653-92V22.579C474.847,8.005,458.379,0,441.481,0z"/>
      <path style={{ fill: "#3799CE" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M423.756,345.554c-1.644,14.743-14.089,25.861-28.948,25.861c-8.12,0-15.929-3.422-21.425-9.389   c-5.501-5.972-8.273-14.042-7.607-22.142c0.768-9.336,1.729-18.72,2.837-28.079h-93.057v41.81   c0,12.344-10.055,22.386-22.415,22.386h-18.282c-12.359,0-22.415-10.042-22.415-22.386v-41.81h-93.057   c1.109,9.359,2.069,18.743,2.837,28.079c0.667,8.099-2.106,16.169-7.607,22.142c-5.496,5.967-13.305,9.389-21.425,9.389   c-14.859,0-27.304-11.118-28.948-25.861c-1.276-11.444-3.568-22.759-6.796-33.749H0V428h488V311.805h-57.448   C427.324,322.795,425.032,334.11,423.756,345.554z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M371.583,289.357c2.813-19.031,6.375-38.205,10.585-56.989c0.231-1.03,0.425-2.065,0.624-3.1h-92.868h-0.591v9.754   c0,8.416-5.898,15.467-13.778,17.287v47.495h94.042C370.22,298.972,370.874,294.151,371.583,289.357z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M0,220.386v83.419h54.924C41.758,265.221,18.941,237.828,0,220.386z"/>
      <path style={{ fill: "#ffffff" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M62.653,301.672c4.697,13.871,7.908,28.337,9.542,42.995c1.191,10.688,10.218,18.748,20.997,18.748   c5.978,0,11.497-2.418,15.541-6.809c4.049-4.396,6.009-10.102,5.519-16.065c-1.371-16.661-3.305-33.487-5.748-50.013   c-2.785-18.837-6.31-37.816-10.478-56.409c-5.661-25.257-2.337-51.852,9.358-74.887c3.493-6.881,6.342-14.325,8.487-22.134   c-2.918-11.32-4.464-23.343-4.464-35.634c0-12.466,1.574-24.449,4.465-35.621C105.045,26.422,77.274,0,46.519,0   C29.621,0,13.153,8.005,0,22.579v187.093C20.628,227.439,47.706,257.527,62.653,301.672z"/></g>

      </svg>
      )
    },
  ];
// Menus déroulants
  const menuControls = [
    {
      id: 'Crown',
      label: 'Crown',
      tooltip: 'Crown',
      shortCode: 'Cr',
      icon: (
         <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 16.6 17" >
            <path  style={{ fill: "#ffffff", stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}} d="M2.4,3.9l11.5,0.1c0.2,0,0.4-0.2,0.3-0.4C13.6,2.4,12-0.6,8.9,1.2c-0.5,0.3-1,0.3-1.5,0     C6,0.6,3.4-0.2,2,3.5C2,3.7,2.1,3.9,2.4,3.9z"/>
            <path  style={{ fill: "#3799CE", stroke:"#ffffff" ,strokeWidth:0.25,strokeMiterlimit:10     }} d="M11.4,5.7c-0.9-0.2-4.3-1-6.7,0C4.6,5.7,4.5,5.8,4.5,6c0,0.3,0,0.9,0,1.2c0,0.2,0.1,0.3,0.3,0.3l6.4,0.1     c0.2,0,0.3-0.1,0.3-0.3V6C11.6,5.8,11.5,5.7,11.4,5.7z"/>
            <path  style={{ fill: "#ffffff", stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10 }} d="M13.4,6.9h-11C2.2,6.9,2,7.1,2,7.4v0.4C2,8,2,8.2,2.2,8.3l1.7,2c0.1,0.2,0.2,0.4,0.2,0.6l-0.3,4.6     c0,0.3,0.2,0.6,0.6,0.7c0.7,0.1,1.9,0,3.2-2c0.2-0.2,0.2-0.5,0.3-0.8l0-0.7c0-0.1,0.1-0.1,0.1-0.1l0.4,0c0.1,0,0.2,0.1,0.2,0.2     v0.7c0,0.4,0.1,0.8,0.3,1.1c0.4,0.7,1.2,1.8,3,1.7c0.3,0,0.6-0.3,0.6-0.5c0-1.2-0.1-4.3-0.1-5.2c0-0.2,0.1-0.4,0.2-0.5l1.2-1.2     c0.2-0.2,0.4-0.5,0.4-0.9V7.5C14.1,7.2,13.8,6.9,13.4,6.9z"/>
            </svg>
),
      options: [
        {
          id: 'CrownPermanent',
          label: 'Crown',
          pathId: '41',
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 80" x="0px" y="0px">
              <path style={{fill:"#3799CE"}}  d="M12.73,85.51H6.37a1,1,0,0,0,0,2h6.36a1,1,0,1,0,0-2Z"/>
              <path style={{fill:"#3799CE"}}  d="M80.69,85.51H75.8a1,1,0,0,0,0,2h4.89a1,1,0,0,0,0-2Z"/>
              <path style={{fill:"#3799CE"}}  d="M40.67,11a19.1,19.1,0,0,0,14.94-.23A1,1,0,1,0,54.82,9a17.2,17.2,0,0,1-13.37.21A1,1,0,0,0,40.67,11Z"/>
              <path style={{fill:"#3799CE"}}  d="M95,55H90.35a14.32,14.32,0,0,1-10-3.84A19,19,0,0,0,69.9,46a2.73,2.73,0,0,0-2.23-2.28l-1.92-5.47a4.31,4.31,0,0,0-4-2.87H34.3a4.31,4.31,0,0,0-4,2.87l-1.9,5.41a2.74,2.74,0,0,0-2.63,2.4,19.06,19.06,0,0,0-10.07,5.06C11,55.56,5.62,55,1,55a1,1,0,0,0-1,1V92.51a1,1,0,0,0,1,1H95a1,1,0,0,0,1-1V56A1,1,0,0,0,95,55ZM32.14,38.91a2.3,2.3,0,0,1,2.16-1.53H61.7a2.3,2.3,0,0,1,2.16,1.53l1.67,4.73H30.47Zm-3.65,6.73H67.13A.83.83,0,0,1,67.8,47a10.2,10.2,0,0,0-1.14,7.34,24,24,0,0,1,.7,4.77A21,21,0,0,1,62.15,73a1.14,1.14,0,0,1-1.9-1.21,16.78,16.78,0,0,0,.85-5.32C61.1,61.08,56,55.6,51.64,52a5.67,5.67,0,0,0-7.48,0c-4.31,3.64-9.45,9.11-9.45,14.52,0,4.57,1.45,5.39.71,6.41a1.18,1.18,0,0,1-1.76.12,21,21,0,0,1-5.22-13.94,23.36,23.36,0,0,1,.67-4.6,10.18,10.18,0,0,0-1.32-7.56A.83.83,0,0,1,28.49,45.64ZM23.89,57a1,1,0,0,0,0-2H13.71A15.11,15.11,0,0,0,17,52.56,17.11,17.11,0,0,1,26.12,48a8.07,8.07,0,0,1,1,6,25.26,25.26,0,0,0-.71,5,23,23,0,0,0,5.72,15.26,3.09,3.09,0,0,0,2.34,1,3.14,3.14,0,0,0,3-4.14,14.81,14.81,0,0,1-.77-4.74c0-4.88,5.48-10.23,8.74-13a3.67,3.67,0,0,1,4.9,0c3.27,2.76,8.75,8.11,8.75,13a15.09,15.09,0,0,1-.74,4.69,3.14,3.14,0,0,0,5.29,3.16,23,23,0,0,0,5.71-15.26,25.49,25.49,0,0,0-.75-5.21A8.22,8.22,0,0,1,69.53,48,17.13,17.13,0,0,1,79,52.56,16.29,16.29,0,0,0,90.35,57H94V84.6c-3.79-2.62-6.58-5-11.87-5-7.63,0-7.43.36-15.16,4.37a13.86,13.86,0,0,1-6.4,1.57H36.43a22.23,22.23,0,0,1-9.32-2.07L19.68,80A24.24,24.24,0,0,0,9.52,77.74H2V57ZM94,91.51H2V79.74H9.52a22.23,22.23,0,0,1,9.32,2.07l7.43,3.45a24.24,24.24,0,0,0,10.16,2.25H60.57a15.93,15.93,0,0,0,7.32-1.79l5-2.59a14,14,0,0,1,6.4-1.56c3.35,0,6.43-.27,10.08,2.25L94,87Z"/>
              <path style={{fill:"#3799CE"}}  d="M27.91,31h40A6.07,6.07,0,0,0,73,28.23C78.74,19.41,75.51,7,62.73,3.05a13.47,13.47,0,0,0-9.48.69A13.06,13.06,0,0,1,43,4,16.54,16.54,0,0,0,20.39,16.23,16.34,16.34,0,0,0,22.72,28.1,6.1,6.1,0,0,0,27.91,31ZM22.35,16.58A14.53,14.53,0,0,1,42.22,5.8,15.06,15.06,0,0,0,54,5.58,11.56,11.56,0,0,1,62.13,5c11.41,3.55,14.25,14.39,9.18,22.18A4.1,4.1,0,0,1,67.88,29h-40a4,4,0,0,1-3.48-2A14.46,14.46,0,0,1,22.35,16.58Z"/>
            </svg>
          )
        },
        {
          id: 'CrownTemporary',
          label: 'Temporary',
          pathId: '42',
          icon: (
               <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 110 95" >
                <path style={{fill:"#3799CE"}} d="M12.152,56.961c0.357,0.619,1.018,1,1.732,1s1.375-0.381,1.732-1L22.5,45.037c0.357-0.619,0.357-1.381,0-2   c-0.357-0.619-1.018-1-1.732-1h-3.947c3.633-15.418,17.318-26.379,33.404-26.379c12.986,0,24.717,7.193,30.616,18.773   c0.502,0.984,1.707,1.375,2.69,0.875c0.984-0.501,1.376-1.706,0.875-2.69c-6.585-12.927-19.682-20.958-34.18-20.958   c-18.28,0-33.791,12.676-37.509,30.379H7c-0.714,0-1.375,0.381-1.732,1c-0.357,0.619-0.357,1.381,0,2L12.152,56.961z    M17.305,46.037l-3.42,5.924l-3.42-5.924H17.305z"/>
                <path style={{fill:"#3799CE"}} d="M77.5,54.961c-0.357,0.619-0.357,1.381,0,2c0.357,0.619,1.018,1,1.732,1h4.398c-3.628,15.42-17.313,26.382-33.404,26.382   c-12.872,0-24.556-7.101-30.493-18.531c-0.509-0.979-1.718-1.361-2.697-0.853c-0.98,0.509-1.362,1.716-0.853,2.697   c6.628,12.761,19.672,20.688,34.042,20.688c18.285,0,33.796-12.677,37.51-30.382H93c0.714,0,1.375-0.381,1.732-1   c0.357-0.619,0.357-1.381,0-2l-6.884-11.924c-0.357-0.619-1.018-1-1.732-1s-1.375,0.381-1.732,1L77.5,54.961z M86.116,48.037   l3.42,5.924h-6.841L86.116,48.037z"/>
                <path style={{fill:"#3799CE"}} d="M68.96,29.967c-3.331-4.026-9.336-6.16-14.945-5.315c-1.363,0.202-2.709,0.589-4.015,1.153   c-1.305-0.564-2.651-0.951-4.01-1.152c-5.612-0.849-11.62,1.288-14.95,5.314c-5.96,7.205-2.444,18.268,4.577,24.079   c0.166,0.138,0.253,0.337,0.228,0.52c-0.994,7.101-0.698,11.846,3.677,18.078c0.521,0.74,1.183,1.415,2.061,2.105   c0.331,0.268,0.857,0.637,1.64,0.752c0.129,0.016,0.261,0.024,0.396,0.024c1.284,0,2.745-0.74,3.129-2.086   c0.13-0.435,0.155-0.887,0.128-1.342c0.015-0.187,0.013-0.376-0.028-0.569c-0.042-0.197-0.083-0.38-0.124-0.565   c-0.009-0.048-0.016-0.097-0.025-0.145l-0.007,0.001c0-0.002-0.001-0.003-0.001-0.005c-0.075-0.331-0.148-0.651-0.219-0.953   c-0.453-1.93-0.726-3.095-0.448-5.133c0.281-2.056,1.805-3.074,3.138-3.341c0.618-0.125,2.728-0.359,3.999,1.802   c1.293,2.199,0.632,5.323,0.048,8.079l-0.055,0.261c-0.04,0.191-0.042,0.379-0.028,0.563c-0.028,0.453-0.006,0.901,0.121,1.324   c0.432,1.509,2.173,2.256,3.571,2.079c0.744-0.109,1.27-0.479,1.58-0.729c0.899-0.708,1.561-1.382,2.085-2.126   c4.373-6.229,4.668-10.974,3.675-18.073c-0.025-0.184,0.062-0.383,0.225-0.519C71.404,48.235,74.92,37.172,68.96,29.967z    M61.828,50.969c-1.241,1.031-1.852,2.582-1.633,4.151c0.853,6.099,0.668,9.76-2.619,14.675c0.482-2.79,0.65-5.882-0.968-8.634   c-1.709-2.906-4.944-4.358-8.233-3.696c-3.371,0.676-5.85,3.315-6.314,6.722c-0.307,2.249-0.09,3.838,0.259,5.451   c-3.187-4.838-3.357-8.485-2.513-14.52c0.218-1.568-0.393-3.119-1.636-4.152c-4.806-3.978-8.886-12.602-4.048-18.45   c2.43-2.937,7.064-4.545,11.276-3.908c1.256,0.186,2.502,0.586,3.704,1.19c0.041,0.02,0.085,0.029,0.127,0.046   c0.043,0.027,0.08,0.06,0.126,0.084c1.547,0.82,2.946,1.965,4.043,3.311c0.396,0.485,0.971,0.736,1.551,0.736   c0.445,0,0.892-0.147,1.263-0.45c0.856-0.698,0.984-1.958,0.286-2.814c-0.61-0.748-1.303-1.435-2.039-2.075   c0.05-0.008,0.099-0.021,0.148-0.028c4.21-0.636,8.842,0.973,11.271,3.909C70.716,38.365,66.636,46.99,61.828,50.969z"/>

              </svg>
          )
        },
        {
          id: 'CrownGold',
          label: 'Amalgame',
          pathId: '44',
          icon: (
           <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 512 500">
            <path style={{fill:"#3799CE"}} d="M303.752,451.781c-4.43,0-8.959-1.248-13.461-3.709c-1.263-0.689-1.997-2.061-1.873-3.492     c5.102-58.812-3.563-73.54-17.919-97.939c-4.336-7.365-9.145-15.54-14.22-25.963c-5.079,10.431-9.892,18.61-14.229,25.979     c-14.359,24.399-23.026,39.127-17.964,97.925c0.123,1.433-0.61,2.8-1.87,3.49c-4.497,2.462-9.024,3.71-13.456,3.71     c-9.279,0-17.923-5.327-25.692-15.836c-7.021-9.496-13.315-23.223-18.708-40.799c-5.233-17.054-9.53-37.41-12.773-60.508     c-3.179-22.639-5.27-47.391-6.219-73.593c-7.173-4.916-13.527-12.086-18.908-21.342c-5.436-9.351-9.629-20.375-12.466-32.768     c-2.809-12.271-4.114-25.122-3.879-38.197c0.243-13.572,2.142-26.627,5.645-38.803c2.802-9.741,6.6-18.733,11.287-26.726     c5.016-8.552,11.063-15.974,17.975-22.061c7.405-6.521,15.861-11.558,25.136-14.972c9.855-3.628,20.767-5.468,32.431-5.468     c10.237,0,20.31,1.643,29.939,4.882c8.451,2.843,16.429,6.868,23.751,11.979c7.309-5.11,15.28-9.134,23.73-11.978     c9.627-3.24,19.698-4.883,29.937-4.883c11.668,0,22.582,1.841,32.439,5.471c9.278,3.417,17.735,8.456,25.142,14.98     c6.912,6.089,12.962,13.515,17.977,22.071c4.688,7.996,8.486,16.992,11.286,26.736c3.499,12.175,5.396,25.229,5.636,38.797     c0.232,13.073-1.076,25.921-3.888,38.188c-2.839,12.39-7.038,23.412-12.477,32.76c-5.385,9.253-11.744,16.421-18.923,21.335     c-0.948,26.205-3.038,50.954-6.213,73.589c-3.24,23.099-7.534,43.455-12.762,60.504c-5.391,17.577-11.684,31.304-18.702,40.8     c-7.77,10.511-16.417,15.842-25.702,15.843C303.757,451.781,303.755,451.781,303.752,451.781z M295.848,442.74     c2.704,1.19,5.354,1.793,7.904,1.793c0.002,0,0.003,0,0.005,0c13.978-0.003,27.288-18.301,37.476-51.519     c10.271-33.496,16.924-81.116,18.729-134.093c0.039-1.204,0.677-2.309,1.696-2.948c13.512-8.467,24.378-26.925,29.813-50.642     c5.48-23.91,4.88-50.651-1.648-73.364c-6.871-23.907-26.385-64.012-79.878-64.012c-18.716,0-36.526,5.875-51.506,16.987     c-1.281,0.951-3.033,0.952-4.316,0.001c-15.009-11.114-32.829-16.989-51.535-16.989c-53.475,0-72.99,40.087-79.864,63.982     c-6.533,22.713-7.14,49.46-1.665,73.38c5.429,23.724,16.287,42.188,29.79,50.656c1.02,0.64,1.655,1.743,1.696,2.947     c1.804,52.965,8.46,100.589,18.744,134.1c10.194,33.22,23.502,51.514,37.471,51.512c2.551,0,5.2-0.603,7.899-1.793     c-2.22-27.303-1.553-46.482,2.135-61.951c3.568-14.967,9.797-25.552,17.01-37.808c5.175-8.794,11.04-18.759,17.173-32.326     c0.587-1.298,1.878-2.132,3.302-2.132c1.423,0,2.716,0.834,3.303,2.132c6.13,13.558,11.992,23.521,17.163,32.31     c7.214,12.26,13.442,22.847,17.005,37.816C297.431,396.248,298.086,415.434,295.848,442.74z"/>
              <path style={{fill:"#FFCF40"}} d="M300.248,118.865c-1.615,0-3.088-1.085-3.507-2.722c-3.695-14.372-11.552-23.861-23.354-28.206     c-8.851-3.257-16.572-2.32-16.649-2.31c-1.985,0.254-3.8-1.149-4.054-3.135c-0.254-1.985,1.149-3.8,3.135-4.054     c0.385-0.049,9.576-1.166,20.072,2.698c6.215,2.288,11.615,5.845,16.053,10.573c5.481,5.842,9.456,13.455,11.815,22.628     c0.498,1.938-0.669,3.914-2.607,4.413C300.849,118.828,300.545,118.865,300.248,118.865z"/>
            <path style={{fill:"#FFCF40"}} d="M202.587,227.268c-0.376,0-0.759-0.059-1.136-0.184c-16.853-5.56-28.735-15.054-35.313-28.221     c-2.562-5.125-4.296-10.805-5.157-16.882c-0.682-4.814-0.819-9.886-0.409-15.077c0.7-8.831,2.744-14.968,2.831-15.225     c0.64-1.896,2.696-2.915,4.593-2.275c1.895,0.639,2.913,2.692,2.276,4.587l0,0c-0.019,0.056-1.885,5.709-2.491,13.709     c-0.549,7.239-0.151,17.969,4.867,27.978c5.691,11.348,16.145,19.598,31.074,24.522c1.9,0.627,2.933,2.676,2.306,4.577     C205.525,226.302,204.109,227.268,202.587,227.268z"/>
            </svg>
          )
        },
        {
          id: 'CrownZirconia',
          label: 'Glass Ionomer',
          pathId: '47',
          icon: (
           <svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="-5.0 -10.0 110.0 125.0">
              <path style={{fill:"#3799CE"}} d="m92.242 55.523h-0.023438l-4.0312 0.074218h-0.023438c-0.48047 0-0.9375-0.29688-1.1992-0.77344l-0.42969-0.79297c-0.69531-1.2852-2-2.0859-3.3984-2.0859h-6.3086c-1.3047 0-2.5195 0.6875-3.25 1.832l-0.67969 1.0703c-0.26953 0.42188-0.69531 0.67188-1.1406 0.67188l-4.4102 0.003907c-0.0625 0-0.12109 0.011718-0.18359 0.023437-0.015624 0.003906-0.03125-0.003906-0.050781 0-11.121 2.1016-23.227 2.1055-33.23 0.011719-0.0625-0.015625-0.125-0.023438-0.1875-0.027344-0.046874-0.003906-0.085937-0.003906-0.14062-0.003906l-4.0117 0.074218h-0.023438c-0.48047 0-0.9375-0.29687-1.1992-0.77734l-0.42969-0.78906c-0.69531-1.2852-2-2.0859-3.3984-2.0859h-6.3164c-1.3047 0-2.5195 0.68359-3.25 1.832l-0.67969 1.0703c-0.26562 0.42188-0.69141 0.67188-1.1367 0.67188h-4.4023c-0.41406 0-0.80078 0.20312-1.0312 0.54687-0.23438 0.34375-0.28125 0.77734-0.12891 1.1602 0.16406 0.42188 0.29688 0.82031 0.39844 1.2148 0.50781 1.9531 0.41016 3.0586 0.26563 4.7305-0.0625 0.72656-0.13281 1.5469-0.16406 2.543-0.09375 3.5 1.4922 7.9141 5.2891 9.9062 0.40234 0.19141 0.82812 0.28516 1.25 0.28516 0.42969 0 0.85937-0.10156 1.2539-0.30078 0.42188-0.21094 0.68359-0.64453 0.68359-1.1133v-10.641c0-1.3281 0.5-2.5625 1.4219-3.4844 0.82813-0.85156 1.9492-1.3477 3.1055-1.3945h0.14844c1.207 0.046876 2.3281 0.54297 3.168 1.4062 0.91016 0.91406 1.4102 2.1484 1.4102 3.4727v10.641c0 0.47266 0.26562 0.90234 0.68359 1.1133 0.78125 0.39453 1.6914 0.39844 2.543-0.007813 3.7578-1.9531 5.3438-6.3711 5.25-9.8867-0.027344-0.99219-0.097656-1.8164-0.16406-2.543-0.14844-1.6758-0.24219-2.7812 0.26172-4.7227 0.023438-0.078125 0.042969-0.16016 0.070313-0.24219 9.7734 1.8828 21.332 1.8594 32.074-0.035156 0.027344 0.085937 0.0625 0.17969 0.085937 0.26562 0.50781 1.9531 0.41016 3.0586 0.26562 4.7305-0.0625 0.72656-0.13281 1.5469-0.16406 2.543-0.09375 3.5 1.4922 7.9141 5.2891 9.9062 0.40234 0.19141 0.82812 0.28516 1.25 0.28516 0.42969 0 0.85938-0.10156 1.2539-0.29688 0.42188-0.21094 0.6875-0.64453 0.6875-1.1172v-10.633c0-1.3281 0.5-2.5625 1.418-3.4844 0.82812-0.85156 1.9492-1.3477 3.1094-1.3945h0.14844c1.207 0.046876 2.3281 0.54297 3.168 1.4062 0.90625 0.91406 1.4062 2.1445 1.4062 3.4727v10.641c0 0.47266 0.26563 0.90234 0.6875 1.1172 0.78125 0.39453 1.6914 0.39844 2.543-0.007812 3.7578-1.9531 5.3438-6.375 5.25-9.8867-0.027343-0.99609-0.097656-1.8164-0.16406-2.543-0.14844-1.6758-0.24219-2.7812 0.26172-4.7188 0.10547-0.39453 0.24219-0.79297 0.41016-1.2227 0.15625-0.39062 0.10156-0.82812-0.13281-1.1719-0.23438-0.33984-0.61719-0.53906-1.0273-0.53906zm-60.641 7.8711c0.0625 0.72266 0.12891 1.4727 0.15234 2.3906 0.070313 2.6055-0.99609 5.8203-3.4766 7.3633v-9.293c0-1.9922-0.75781-3.8516-2.1289-5.2266-1.2773-1.3125-3-2.0781-4.8984-2.1523h-0.25c-1.8516 0.074219-3.5703 0.83984-4.8398 2.1406-1.3789 1.3828-2.1367 3.2422-2.1367 5.2383v9.2852c-2.4766-1.582-3.543-4.8555-3.4766-7.3555 0.023437-0.91797 0.089844-1.668 0.15234-2.3945 0.14453-1.6719 0.27344-3.1211-0.28125-5.3711h2.7031c1.3047 0 2.5195-0.6875 3.25-1.832l0.67969-1.0703c0.26953-0.42188 0.69531-0.67188 1.1406-0.67188h6.3164c0.48828 0 0.9375 0.28906 1.1992 0.77734l0.42969 0.78906c0.71094 1.3125 2.0234 2.1016 3.4609 2.0859l2.2852-0.042968c-0.55078 2.2305-0.42578 3.6758-0.28125 5.3398zm58.641-0.003906c0.0625 0.72656 0.12891 1.4727 0.15234 2.3945 0.070313 2.6055-0.99219 5.8203-3.4766 7.3594v-9.293c0-1.9922-0.75781-3.8555-2.125-5.2266-1.2773-1.3125-3-2.0781-4.8984-2.1523h-0.25c-1.8516 0.074219-3.5742 0.83984-4.8398 2.1406-1.3789 1.3828-2.1367 3.2422-2.1367 5.2383v9.2852c-2.4766-1.582-3.5469-4.8555-3.4766-7.3555 0.023438-0.91797 0.089844-1.668 0.15234-2.3945 0.14453-1.6719 0.27344-3.1211-0.28125-5.3711h2.7031c1.3047 0 2.5195-0.68359 3.25-1.832l0.67969-1.0703c0.26563-0.42188 0.69141-0.67188 1.1367-0.67188h6.3164c0.48047 0 0.9375 0.29688 1.1992 0.77344l0.42969 0.79297c0.71094 1.3125 2.0273 2.1016 3.4648 2.0859l2.2852-0.042969c-0.55469 2.2344-0.42969 3.6797-0.28516 5.3398z"/>
              <path style={{fill:"#3799CE"}} d="m13.629 30.543h0.11328c0.68359 0 1.2422-0.54688 1.25-1.2305 0.007812-0.69141-0.54297-1.2578-1.2344-1.2695h-0.125c-3.8281 0-6.418 2.4961-7.0039 4.9805-0.45703 1.8867-0.31641 3.9492 0.40625 5.9531 0.18359 0.50781 0.66406 0.82422 1.1758 0.82422 0.14062 0 0.28516-0.023437 0.42578-0.074219 0.64844-0.23437 0.98438-0.95312 0.75-1.6016-0.55859-1.543-0.67188-3.1016-0.32812-4.5234 0.29297-1.2383 1.75-3.0586 4.5703-3.0586z"/>
              <path style={{fill:"#3799CE"}} d="m44.355 30.543h0.11328c0.68359 0 1.2422-0.54688 1.25-1.2305 0.007812-0.69141-0.54297-1.2578-1.2344-1.2695h-0.125c-3.8281 0-6.418 2.4961-7.0039 4.9805-0.45703 1.8906-0.31641 3.9492 0.40625 5.9531 0.18359 0.50781 0.66406 0.82422 1.1758 0.82422 0.14062 0 0.28516-0.023437 0.42578-0.074219 0.64844-0.23437 0.98438-0.95312 0.75-1.6016-0.55859-1.543-0.67188-3.1016-0.32812-4.5234 0.29297-1.2383 1.75-3.0586 4.5703-3.0586z"/>
              <path style={{fill:"#3799CE"}} d="m90.938 24.734c-2.0039-0.80469-4.1641-0.85938-6.0781-0.14844-0.78516 0.28906-1.8711 0.78125-2.3555 1.0352-0.60938 0.32422-0.83984 1.082-0.51562 1.6914s1.0781 0.83984 1.6914 0.51562c0.35547-0.19141 1.332-0.63281 2.0508-0.89844 1.3359-0.49219 2.8516-0.44922 4.3008 0.13281 1.1836 0.45312 2.207 1.2383 2.9648 2.2891 1.7461 2.3516 2.3086 5.5 1.4688 8.2109-0.4375 1.3984-1.2031 2.6953-2.0156 4.0703-0.55469 0.9375-1.1211 1.8984-1.5938 2.9219l-20.25-0.003907c-0.47656-1.0312-1.0469-2-1.6094-2.9453-0.80859-1.3672-1.5703-2.6562-2.0078-4.0391-0.83984-2.7188-0.27734-5.8672 1.4766-8.2305 0.75-1.0352 1.7695-1.8203 2.957-2.2734 1.0273-0.39453 2.082-0.52734 3.1953-0.38672 0.011718 0 1.2852 0.12891 2.9258 1.2148 0.44141 0.29688 0.85938 0.58984 1.2578 0.87109 1.1641 0.81641 2.2617 1.5859 3.4766 2.1211 1.2891 0.56641 3.2305 1.1055 5.1641 0.46094 0.65625-0.21875 1.0078-0.92578 0.78906-1.582s-0.92578-1.0078-1.5781-0.78906c-1.1758 0.39062-2.4844 0.007813-3.3711-0.37891-0.98438-0.43359-1.9414-1.1016-3.0469-1.8789-0.41406-0.28906-0.84375-0.58984-1.3086-0.90234-2.1953-1.4531-3.9297-1.6133-4.0547-1.6211-1.4727-0.19141-2.9375-0.007812-4.3516 0.53906-1.6406 0.625-3.0547 1.7148-4.0781 3.1328-0.42578 0.57422-0.77734 1.1953-1.0938 1.832-0.3125-0.63281-0.66016-1.25-1.0859-1.8203-1.0352-1.4297-2.4492-2.5156-4.0703-3.1367-2.0039-0.80469-4.1641-0.85938-6.0781-0.14844-0.78516 0.28906-1.8711 0.78125-2.3555 1.0352-0.60937 0.32422-0.83984 1.082-0.51562 1.6914 0.32422 0.60938 1.0781 0.83984 1.6914 0.51562 0.35547-0.19141 1.332-0.63281 2.0508-0.89844 1.332-0.49219 2.8555-0.44922 4.3008 0.13281 1.1836 0.45312 2.207 1.2383 2.9648 2.2891 1.7461 2.3516 2.3086 5.5 1.4688 8.2109-0.4375 1.3984-1.2031 2.6953-2.0156 4.0703-0.55469 0.9375-1.1211 1.8984-1.5938 2.9219l-20.23-0.007813c-0.47656-1.0312-1.0469-2-1.6094-2.9453-0.80859-1.3672-1.5703-2.6562-2.0078-4.0391-0.83984-2.7188-0.27734-5.8672 1.4766-8.2305 0.75-1.0352 1.7695-1.8203 2.957-2.2734 1.0273-0.39453 2.082-0.52734 3.1953-0.38672 0.011719 0 1.2852 0.12891 2.9258 1.2148 0.44141 0.29688 0.85938 0.58984 1.2578 0.87109 1.1641 0.81641 2.2617 1.5859 3.4766 2.1211 1.2891 0.56641 3.2344 1.1055 5.1641 0.46094 0.65625-0.21875 1.0078-0.92578 0.78906-1.582s-0.92578-1.0078-1.5781-0.78906c-1.1797 0.39062-2.4844 0.007813-3.3711-0.37891-0.98438-0.43359-1.9414-1.1016-3.0469-1.8789-0.41406-0.28906-0.84375-0.58984-1.3086-0.90234-2.1953-1.4531-3.9297-1.6133-4.0547-1.6211-1.4727-0.19141-2.9375-0.007812-4.3516 0.53906-1.6406 0.625-3.0547 1.7148-4.0781 3.1328-0.42578 0.57422-0.77734 1.1953-1.0938 1.832-0.3125-0.63281-0.66016-1.25-1.0859-1.8203-1.0352-1.4297-2.4453-2.5156-4.0664-3.1367-2.0039-0.80469-4.1641-0.85938-6.082-0.14844-0.78516 0.28906-1.8711 0.78125-2.3555 1.0352-0.60938 0.32422-0.83984 1.082-0.51562 1.6914s1.0781 0.83984 1.6914 0.51562c0.35547-0.19141 1.332-0.63281 2.0508-0.89844 1.332-0.49219 2.8555-0.44922 4.3008 0.13281 1.1836 0.45312 2.207 1.2383 2.9648 2.2891 1.7461 2.3516 2.3086 5.5 1.4688 8.2109-0.4375 1.3984-1.2031 2.6953-2.0156 4.0703-0.55469 0.9375-1.1211 1.8984-1.5938 2.9219l-20.23-0.007813c-0.47656-1.0312-1.0469-2-1.6094-2.9453-0.80859-1.3672-1.5703-2.6562-2.0078-4.0391-0.83984-2.7188-0.27734-5.8672 1.4766-8.2305 0.75-1.0352 1.7695-1.8203 2.957-2.2734 1.0273-0.39453 2.082-0.52734 3.1953-0.38672 0.011719 0 1.2852 0.12891 2.9258 1.2148 0.44531 0.29688 0.86328 0.58984 1.2656 0.875 1.1602 0.8125 2.2578 1.5859 3.4688 2.1172 1.293 0.56641 3.2344 1.1055 5.1641 0.46094 0.65625-0.21875 1.0078-0.92578 0.78906-1.582s-0.92578-1.0078-1.5781-0.78906c-1.1797 0.39062-2.4805 0.007813-3.3711-0.37891-0.98438-0.43359-1.9375-1.1016-3.0391-1.875-0.41406-0.29297-0.84766-0.59375-1.3125-0.90625-2.1992-1.4531-3.9336-1.6133-4.0547-1.6211-1.4766-0.19141-2.9375-0.007812-4.3516 0.53906-1.6406 0.625-3.0547 1.7148-4.0781 3.1328-2.2148 2.9766-2.9219 6.9805-1.8516 10.453 0.52344 1.6641 1.3984 3.1406 2.2422 4.5664 0.67969 1.1484 1.3203 2.2344 1.7734 3.3789 0.1875 0.47656 0.64844 0.79297 1.1641 0.79297l21.902-0.003907c0.51172 0 0.97266-0.3125 1.1641-0.78906 0.45312-1.1406 1.0859-2.2188 1.7617-3.3633 0.51562-0.87891 1.0352-1.7812 1.4883-2.7266 0.44922 0.9375 0.96875 1.8359 1.4844 2.707 0.67969 1.1484 1.3203 2.2344 1.7734 3.3789 0.1875 0.47656 0.64844 0.79297 1.1641 0.79297h21.895c0.51172 0 0.97266-0.3125 1.1641-0.78906 0.45312-1.1406 1.0859-2.2188 1.7617-3.3633 0.51562-0.87891 1.0352-1.7812 1.4883-2.7266 0.44922 0.9375 0.96875 1.8359 1.4844 2.707 0.67969 1.1484 1.3203 2.2344 1.7734 3.3789 0.1875 0.47656 0.64844 0.79297 1.1641 0.79297h21.887c0.51172 0 0.97266-0.3125 1.1641-0.78906 0.45312-1.1406 1.0859-2.2188 1.7617-3.3633 0.84375-1.4336 1.7188-2.918 2.2461-4.5938 1.0703-3.4648 0.36328-7.4688-1.8438-10.434-1.0312-1.4297-2.4453-2.5156-4.0664-3.1367z"/>
              <path style={{fill:"#3799CE"}} d="m75.082 30.543h0.09375c0.68359 0.050781 1.2578-0.54297 1.2656-1.2305 0.007813-0.69141-0.54297-1.2578-1.2344-1.2695h-0.125c-3.8281 0-6.418 2.4961-7.0039 4.9805-0.45703 1.8906-0.31641 3.9492 0.40625 5.9531 0.18359 0.50781 0.66406 0.82422 1.1758 0.82422 0.14062 0 0.28516-0.023437 0.42578-0.074219 0.64844-0.23437 0.98437-0.95312 0.75-1.6016-0.55859-1.543-0.67188-3.1016-0.32813-4.5234 0.29688-1.2383 1.7539-3.0586 4.5742-3.0586z"/>
              </svg>
          )
        }
      ]
    },

  ];
  return (
     <Group justify="space-between" mb={4} gap="xl"  w={"70%"}>
      {/* Menu "Tous les Traitements" */}
     <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                Tous les Traitements
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Télécharger zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Aperçu de tous
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Supprimer tous
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>

      {/* Bouton d'application du traitement */}
      <Button
        variant="filled"
        color={canApplyTreatment ? "green" : "gray"}
        size="sm"
        disabled={!canApplyTreatment}
        onClick={handleApplyTreatment}
        leftSection={<IconSquareRoundedPlusFilled size={14} />}
      >
        Appliquer {hasSelectedTeeth ? `(${hasSelectedTeeth ? 'Dents sélectionnées' : 'Aucune dent'})` : ''}
      </Button>

      {/* Boutons de traitement */}
      <div style={{ margin: '0 auto' }} className="mb-2 flex flex-end p-2 sm:justify-start space-x-2">
        {simpleControls.slice(0, 2).map((control) => (
    <Tooltip
    key={control.id}
    label={control.tooltip}
    withArrow
    className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
    >
    <Button
    styles={{
    root: {
    position: 'relative',
    color: 'white',
    height: '35px', // Adjust button height
    width: '35px',  // Adjust button width
    padding: 0,
    borderRadius: '0.5rem'
    },
    }}
    >
    <div
    style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    }}
    >
    <span  className={
        activeButton === control.id
          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
      }
  onClick={() => handleButtonClickWithBackend(control.id, control.pathId)}
    >
    {control.icon}
    </span>
    </div>
    {/* "CL" in the bottom-right corner */}
    <span
    style={{
    position: 'absolute',
    bottom: '0px',
    right: '0px',
    fontSize: '8px',
    fontWeight: '800',
    backgroundColor: 'white',
    // borderRadius:'0.125rem' ,
    borderTopLeftRadius: '0.5rem' ,
    borderBottomRightRadius: '0.5rem' ,
    color:'#3799CE',
    padding:'3px  0px 1px 2px' ,
    }}
    className="h-[14px] w-[14px] "
    >
    {control.shortCode}
    </span>
    </Button>
    </Tooltip>

    ))}

     {menuControls.map((menuControl) => (
<Menu key={menuControl.id} shadow="md" width={210} trigger="hover" openDelay={100} closeDelay={400}>
              <Tooltip
                label={menuControl.tooltip}
                withArrow
                className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                >
                <Menu.Target>
                <Button
                styles={{
                  root: {
                    position: 'relative',
                    color: 'white',
                    height: '35px', // Adjust button height
                    width: '35px',  // Adjust button width
                    padding: 0,
                    borderRadius: '0.5rem',
                  },
                }}
              >
            {/* SVG in the middle */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
              }}
            >
             <span
                      className={
                        menuControl.options.some(option => activeButton === option.id)
                          ? "block h-[35px] w-[35px] rounded-md bg-[#28a745] p-1 hover:bg-[#28a745]"
                          : "block h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#28a745]"
                      }
                    >
           {menuControl.icon}
                  </span>
            </div>
            <span
              style={{
                position: 'absolute',
                bottom: '0px',
                right: '0px',
                fontSize: '8px',
                fontWeight: '800',
                backgroundColor: 'white',
                // borderRadius:'0.125rem' ,
                borderTopLeftRadius: '0.5rem' ,
                borderBottomRightRadius: '0.5rem' ,
                color:'#3799CE',
                padding:'3px  0px 1px 2px' ,
              }}
              className="h-[14px] w-[14px] "
            >
             {menuControl.shortCode}
            </span>
          </Button>
            </Menu.Target>
              </Tooltip>
              <Menu.Dropdown >
              <Menu.Label>{menuControl.label}</Menu.Label>
             {menuControl.options.map((option) => (

                                    <Menu.Item
                                     key={option.id}
                                     className={
                                       activeButton === option.id
                                          ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                                          : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                                      }
                                    onClick={() => handleButtonClickWithBackend(option.id, option.pathId)}
                                    >
                                      <Group  className="h-[28px] w-[186.4px] " >
                                      <Radio
                                          checked={activeButton === option.id}
                                          onChange={(event) =>
                                          setChecked(event.currentTarget.checked)
                                          }
                                          icon={CheckIcon}
                                      />
                                      <div className="flex">
                                      <Avatar
                                        color="blue"
                                        radius="sm"
                                        style={{ width: "28px", height: "28px" }}
                                        px={0.5}
                                      >
                                         {option.icon}
                                      </Avatar>
                                      <Text fw={500} ml={6}>  {option.label}</Text>
                                          </div>
                                  </Group>
                                    </Menu.Item>
                                     ))}
              </Menu.Dropdown>
            </Menu>
            ))}
      {simpleControls.slice(2, 4).map((control) => (
    <Tooltip
    key={control.id}
    label={control.tooltip}
    withArrow
    className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
    >
    <Button
    styles={{
    root: {
    position: 'relative',
    color: 'white',
    height: '35px', // Adjust button height
    width: '35px',  // Adjust button width
    padding: 0,
    borderRadius: '0.5rem'
    },
    }}
    >
    <div
    style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    }}
    >
    <span  className={
        activeButton === control.id
          ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
          : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
      }
  onClick={() => handleButtonClickWithBackend(control.id, control.pathId)}
    >
    {control.icon}
    </span>
    </div>
    {/* "CL" in the bottom-right corner */}
    <span
    style={{
    position: 'absolute',
    bottom: '0px',
    right: '0px',
    fontSize: '8px',
    fontWeight: '800',
    backgroundColor: 'white',
    // borderRadius:'0.125rem' ,
    borderTopLeftRadius: '0.5rem' ,
    borderBottomRightRadius: '0.5rem' ,
    color:'#3799CE',
    padding:'3px  0px 1px 2px' ,
    }}
    className="h-[14px] w-[14px] "
    >
    {control.shortCode}
    </span>
    </Button>
    </Tooltip>

    ))}
      </div>
    </Group>
  );
};

export default ProsthodonticControls;
