

import { useState } from 'react';
import { Group, Title, Button, Text, Tooltip, Menu,Select, ActionIcon ,Textarea,Box} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {  IconTrash } from '@tabler/icons-react';
import { FreeDictionaryInput } from './FreeDictionaryInput';
import { AutocompleteChipInput } from './AutocompleteChipInput';

import { Icon } from '@mdi/react';
import {
   mdiViewGrid,
  mdiArrowLeft,
  mdiArrowRight,
  mdiCardAccountDetails,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,
  mdiFormatListBulleted,
   mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiCalendarPlus,
  mdiHistory,
  mdiMicrophone,
  mdiClipboardText,
  mdiDeleteSweep,
} from '@mdi/js';
export interface FreeDictionaryField {
  id: string;
  key: string;
  type: string;
  label: string;
  value: string;
  meta_data?: {
    small?: boolean;
  };
  dictUid?: string;
  blockUid?: string;
}
type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
   patient: any;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onGoToContract: () => void;
    versions: string[];
  currentVersionIndex: number;
  onSelect: (index: number) => void;
   label: string;
  value: string;
  onChange: (val: string) => void;
  onVoice?: () => void;
  onShowModels?: () => void;
  onViewHistory?: () => void;
  onClear?: () => void;
  required?: boolean;
  readOnly?: boolean;
  showHistory?: boolean;
  showModels?: boolean;
  fields: FreeDictionaryField[];

  data: string[];
 
  
  requireMatch?: boolean;
};
const FicheMedicale =({
  patient,
  onGoBack,
  onGoToContract,
   patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
  versions,
  currentVersionIndex,
  onSelect,
 
  
  onVoice,
  onShowModels,
  onViewHistory,
  onClear,
  required,
  
  showHistory = false,
  showModels = false,
 fields,
 label = 'Pathologies',
  data,
  value,
  onChange,
 
  readOnly = false,
  requireMatch = false,
}: PatientActionsProps) => {
     const disabled = isFormInvalid || isDraft;
      const [recording, setRecording] = useState(false);
 const handleVoiceInput = () => {
    setRecording((r) => !r);
    // Implémenter ici une intégration de Web Speech API si souhaité
    alert('Démarrage simulation de reconnaissance vocale...');
  };
  const [search, setSearch] = useState('');

  const handleAdd = (input: string) => {
    if (!input.trim()) return;
    if (requireMatch && !data.includes(input)) return;
    if (!value.includes(input)) {
      onChange([...value, input]);
    }
    setSearch('');
  };

  const handleRemove = (item: string) => {
    onChange(value.filter((v) => v !== item));
  };

  const handleClear = () => {
    onChange([]);
  };
  return (
    <>
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
       <Group justify="space-between" align="center">
         <Group>
           {patient ? (
             <Icon path={mdiCardAccountDetails} size={1} />
           ) : (
             <Button variant="subtle" onClick={onGoBack}>
               <Icon path={mdiArrowLeft} size={1} color={"white"}/>
             </Button>
           )}
           <Title order={2}>Fiche patient</Title>
           <DatePickerInput placeholder="Date de création" />
           23/06/2025
         </Group>
   
         {patient && (
           <Group>
             <Text>{patient.full_name}</Text>
             <Text>{patient.gender}</Text>
             <Text>{patient.age}</Text>
             <Text>{patient.default_insurance}</Text>
             <Text>{patient.file_number}</Text>
             <Text>{patient.last_visit}</Text>
           </Group>
         )}
   
         <Group>
           
           {/* <Divider size="sm" orientation="vertical" /> */}
           <Menu shadow="md" width={220}>
             <Menu.Target>
               <Button variant="subtle">
                 <Icon path={mdiApps} size={1} color={"white"}/>
               </Button>
             </Menu.Target>
             <Menu.Dropdown>
               <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />}>Alerts</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}>Relations Patient</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
               <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
             </Menu.Dropdown>
           </Menu>
   
           <Tooltip label="Contrat">
             <Button variant="subtle" onClick={onGoToContract}>
               <Icon path={mdiCertificate} size={1} color={"white"}/>
             </Button>
           </Tooltip>
   
           <Tooltip label="Liste patients">
             <Button component="a" href="/pratisoft/patient" variant="subtle">
               <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
             </Button>
           </Tooltip>
         </Group>
       </Group>
       
       </div>

    {/* --------------------------Start Content ------------------------------*/}
 <Group align="center" justify="space-between" p="md" bg="gray.1">
      <Group align="center">
        <Icon path={mdiViewGrid} size={1} />
        <Title order={3}>Fiche médicale</Title>
      </Group>

      <Group>
        <Tooltip label="Version précédente">
          <ActionIcon
            variant="light"
            onClick={onPrevious}
            disabled={disabled || currentVersionIndex === versions.length - 1}
          >
            <Icon path={mdiArrowLeft} size={1} />
          </ActionIcon>
        </Tooltip>

        <Select
          placeholder="Aucune version trouvée"
          data={versions.map((v, i) => ({ label: `Version ${i + 1}`, value: i.toString() }))}
          value={currentVersionIndex.toString()}
          onChange={(value) => onSelect(Number(value))}
          disabled={disabled}
        />

        <Tooltip label="Version suivante">
          <ActionIcon
            variant="light"
            onClick={onNext}
            disabled={disabled || currentVersionIndex === 0}
          >
            <Icon path={mdiArrowRight} size={1} />
          </ActionIcon>
        </Tooltip>
      </Group>
    </Group>
    <Group>
         <div>
      <Group justify="space-between" align="center">
        <Text fw={500}>
          {label}
          {required && <span style={{ color: 'red' }}> *</span>}
        </Text>
        <Group gap="xs">
          {showHistory && (
            <Tooltip label="Historique">
              <Button
                variant="subtle"
                size="xs"
                onClick={onViewHistory}
                disabled={readOnly}
              >
                <Icon path={mdiHistory} size={1} />
              </Button>
            </Tooltip>
          )}
          {onVoice && (
            <Tooltip label="Enregistrer">
              <Button
                variant="subtle"
                size="xs"
                onClick={onVoice}
                disabled={readOnly}
              >
                <Icon path={mdiMicrophone} size={1} />
              </Button>
            </Tooltip>
          )}
          {showModels && (
            <Tooltip label="Modèles">
              <Button
                variant="subtle"
                size="xs"
                onClick={onShowModels}
                disabled={readOnly}
              >
                <Icon path={mdiClipboardText} size={1} />
              </Button>
            </Tooltip>
          )}
          {onClear && (
            <Tooltip label="Effacer">
              <Button
                variant="subtle"
                size="xs"
                color="red"
                onClick={onClear}
                disabled={readOnly || !value}
              >
                <Icon path={mdiDeleteSweep} size={1} />
              </Button>
            </Tooltip>
          )}
        </Group>
      </Group>

      <Textarea
        placeholder="Ajouter"
        value={value}
        onChange={(e) => onChange(e.currentTarget.value)}
        disabled={readOnly}
        autosize
        minRows={2}
      />
    </div>
          {fields
        .filter((field) => field.type === 'free-dictionary')
        .map((field) => (
          <Box
            key={field.id}
            style={{ flex: field.meta_data?.small ? '0 0 50%' : '0 0 100%' }}
          >
            <FreeDictionaryInput
              label={field.label}
              value={values[field.key] || ''}
              onChange={(val) => onChange(field.key, val)}
              onClear={() => onChange(field.key, '')}
              showHistory={!!field.dictUid && !!field.blockUid}
              showModels={!!field.dictUid}
              required={false}
              readOnly={readOnly}
              onViewHistory={() =>
                console.log(`History clicked for ${field.key}`)
              }
              onShowModels={() =>
                console.log(`Show models clicked for ${field.key}`)
              }
            />
          </Box>
        ))}

    </Group>
    <Group>
<FreeDictionaryInput
  label="Antécédents"
  value={fieldValues['antecedents'] ?? ''}
  onChange={(val) => setFieldValues((prev) => ({ ...prev, antecedents: val }))}
  onClear={() => setFieldValues((prev) => ({ ...prev, antecedents: '' }))}
  onShowModels={() => console.log('Afficher les modèles')}
  onViewHistory={() => console.log('Afficher l’historique')}
  showModels={true}
  showHistory={true}
/>
    <AutocompleteChipInput
  label="Traitement en cours"
  data={['Doliprane', 'Amoxicilline', 'Ibuprofène']}
  value={formValues.treatments}
  onChange={(updated) => setFormValues((prev) => ({ ...prev, treatments: updated }))}
  required
/>

    </Group>
     <Box>
      <Group justify="space-between" mb="xs">
        <Text fw={500}>{label}</Text>
        <Group gap="xs">
          {onCustomAction && (
            <Tooltip label="Ajouter une pathologie personnalisée">
              <ActionIcon onClick={onCustomAction} disabled={readOnly} variant="light">
                <IconTag size={18} />
              </ActionIcon>
            </Tooltip>
          )}
          <ActionIcon onClick={handleClear} disabled={readOnly || value.length === 0} color="red" variant="light">
            <IconTrash size={18} />
          </ActionIcon>
        </Group>
      </Group>

      <Autocomplete
        data={data}
        value={search}
        onChange={setSearch}
        onKeyDown={(event) => {
          if (event.key === 'Enter') {
            event.preventDefault();
            handleAdd(search);
          }
        }}
        placeholder="Saisir"
        disabled={readOnly}
      />

      <Group mt="xs" spacing="xs" wrap="wrap">
        {value.map((item) => (
          <Chip
            key={item}
            checked
            onChange={() => handleRemove(item)}
            disabled={readOnly}
            variant="filled"
          >
            {item}
          </Chip>
        ))}
      </Group>
    </Box>
{/* -------------------------end  Content---------------------------------*/}
     <div style={{marginTop:"120px" , borderTop: "1px solid light-dark(var(--mantine-color-gray-2), var(--mantine-color-dark-5))"}}>
      
       <Group justify="space-between" wrap="wrap" mt="md" mb={"auto"}>
    <Group gap="xs">
        {patientId && (
        <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
<Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        
        </>
        )}
    </Group>

    <Group gap="xs">
        <Tooltip label="Commencer la visite">
        <ActionIcon variant="filled" aria-label="Settings" radius="4px"
        onClick={onStartVisit}
            disabled={disabled}>
        <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
        <ActionIcon variant="filled" aria-label="Settings"radius="4px"
        onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
        Annuler
        </Button>

        {patientId && (
        <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
        >
            Enregistrer & Nouvelle fiche
        </Button>
        )}

        <Button
        variant="filled"
        color="blue"
        onClick={onSaveQuit}
        disabled={isFormInvalid}
        >
        Enregistrer et quitter
        </Button>

        <Button
        variant="filled"
        color="blue"
        type="submit"
        onClick={onSubmit}
        disabled={isFormInvalid}
        >
        Enregistrer la fiche
        </Button>
    </Group>
    </Group>
      
    </div>
    </>
  )
}

export default FicheMedicale

