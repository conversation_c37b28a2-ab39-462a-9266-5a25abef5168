'use client';
import React, { useState } from 'react';
import { useDisclosure } from '@mantine/hooks';
import { Group, Select,Text ,Textarea,Card,Tree,TextInput,ActionIcon,Modal,} from '@mantine/core';
import { Checkbox,  RenderTreeNodePayload,  } from '@mantine/core';
import Icon from '@mdi/react';
import { mdiViewGrid ,mdiArrowLeft,mdiArrowRight,mdiPlusBox,mdiHistory,mdiMicrophone,mdiClipboardText,mdiDeleteSweep,mdiMagnify,mdiViewHeadline,
mdiArrowRightBoldBox,mdiPlaylistCheck,mdiTagPlus
} from '@mdi/js';
import { TreeNodeData } from '@mantine/core';
import { IconChevronDown } from '@tabler/icons-react';
const renderTreeNode = ({
  node,
  expanded,
  hasChildren,
  elementProps,
  tree,
}: RenderTreeNodePayload) => {
  const checked = tree.isNodeChecked(node.value);
  const indeterminate = tree.isNodeIndeterminate(node.value);

  return (
    <Group gap="xs" {...elementProps}>
      <Checkbox.Indicator
        checked={checked}
        indeterminate={indeterminate}
        onClick={() => (!checked ? tree.checkNode(node.value) : tree.uncheckNode(node.value))}
      />

      <Group gap={5} onClick={() => tree.toggleExpanded(node.value)}>
        <span>{node.label}</span>

        {hasChildren && (
          <IconChevronDown
            size={14}
            style={{ transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
          />
        )}
      </Group>
    </Group>
  );
};
export const dataFacteurs: TreeNodeData[] = [
  {
    label: 'Facteurs de risque',
    value: 'src',
    children: [
          { label: 'Age', value: 'Age' },
          { label: 'Alcool', value: 'Alcool' },
          { label: 'Avoir une alimentation riche en sucres', value: 'Avoir une alimentation riche en sucres' },
          { label: 'Avoir une mauvaise hygiène bucco-dentaire', value: 'Avoir une mauvaise hygiène bucco-dentaire' },
          { label: 'Diabète', value: 'Diabète' },
          { label: "Etre en perte d'\autonomie", value: "Etre en perte d'\autonomie" },
          { label: 'La génétique', value: 'La génétique' },
          { label: 'La position des dents', value: 'La position des dents' },
          { label: 'Le grincement des dents (Bruxine)', value: 'Le grincement des dents (Bruxine)' },

          { label: 'Le syndrome de sjogrem', value: 'Le syndrome de sjogrem' },
          { label: 'Les traitements radiothérapie', value: 'Les traitements radiothérapie' },
           { label: 'L’HTA (hypertension artérielle)', value: 'L’HTA (hypertension artérielle)' },
            { label: 'Prise de certains médicaments:', value: 'Prise de certains médicaments:' },
             { label: 'Souffrir de sécheresse de la bouche', value: 'Souffrir de sécheresse de la bouche' },
              { label: 'Stress', value: 'Stress' },
               { label: 'Tabac', value: 'Tabac' },
                { label: 'Tabagisme', value: 'Tabagisme' },
               
    ],
  },
];
export const dataAllergies: TreeNodeData[] = [
  {
    label: 'Allergies médicamenteuses',
    value: 'Allergies médicamenteuses',
    children: [
      {
        label: 'Antalgiques',
        value: 'Antalgiques',
        children: [
          { label: 'Anesthésiques locaux', value: 'Anesthésiques locaux' },
          { label: 'Aspirine', value: 'Aspirine' },
          
        ],
      },
    ],
  },
  {
    label: 'Antibiotiques',
    value: 'Antibiotiques',
    children: [
     
          { label: 'Céphalosporines', value: 'Céphalosporines' },
          { label: 'Cyclines', value: 'Cyclines' },
           { label: 'Pénicillines et dérivés', value: 'Pénicillines et dérivés' },
          { label: 'Sulfamides', value: 'Sulfamides' },
       
     
     
    ],
  },
  {
    label: 'package.json',
    value: 'package.json',
  },
  {
    label: 'tsconfig.json',
    value: 'tsconfig.json',
  },
];
export const FicheMedicale = () => {
    
    const [isFacteursVisible, setIsFacteursVisible] = useState(false); // State to control sidebar visibility
   const toggleFacteursSidebar = () => {
         setIsFacteursVisible(!isFacteursVisible);
       };
  const [opened, { open, close }] = useDisclosure(false);
  const [isAllergiesVisible, setIsAllergiesVisible] = useState(false); // State to control sidebar visibility
  const toggleAllergiesSidebar = () => {
         setIsAllergiesVisible(!isAllergiesVisible);
       };
  const [openedAllergies, { open:Allergiesopen, close:Allergiesclose }] = useDisclosure(false);

const [isAntecedentsVisible, setIsAntecedentsVisible] = useState(false); // State to control sidebar visibility
  const toggleAntecedentsSidebar = () => {
         setIsAntecedentsVisible(!isAntecedentsVisible);
       };
  const [openedAntecedents, { open:Antecedentsopen, close:Antecedentsclose }] = useDisclosure(false);
  
const [isTraitementVisible, setIsTraitementVisible] = useState(false); // State to control sidebar visibility
  const toggleTraitementSidebar = () => {
         setIsTraitementVisible(!isTraitementVisible);
       };
  const [openedTraitement , { open:Traitementopen, close:Traitementclose }] = useDisclosure(false);

  const [isPathologiesVisible, setIsPathologiesVisible] = useState(false); // State to control sidebar visibility
  const togglePathologiesSidebar = () => {
         setIsPathologiesVisible(!isPathologiesVisible);
       };
  const [openedPathologies , { open:Pathologiesopen, close:Pathologiesclose }] = useDisclosure(false);
  
    
  return (
    <>
    <Group justify="space-between" mb={10}>
      <Group>
       <Icon path={mdiViewGrid} size={1} />
         <Text fw={700}>Fiche médicale</Text>
      </Group>
      <Group justify="flex-end">
      <Icon path={mdiArrowLeft} size={1} />
     <Select
    
      placeholder="Pick value"
      data={['25/06/2025', '26/06/2025', '27/06/2025', '28/06/2025']}
    />
      <Icon path={mdiArrowRight} size={1} />
    </Group>
    </Group>
    <Group>
<div className={isFacteursVisible || isAllergiesVisible || isAntecedentsVisible ||isTraitementVisible ||isPathologiesVisible? 'w-[79%] ':'w-[100%] '}>
    {/* --------------------------1-------------------------  */}
 <Group  w={"100%"}>
  <div className={ isAllergiesVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Facteurs de risque</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleAllergiesSidebar(); // Toggle sidebar visibility
              }}
    />
</div>
     <div className={isAllergiesVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    
    <Group gap="sm">
  <Text fw={500}>Allergies médicamenteuses
</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
       
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleAllergiesSidebar(); 
              }}
    />
    
</div>
 </Group>
 {/* ---------------------------2----------------------- */}
  <Group  w={"100%"}>
  <div className={isAntecedentsVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Antécédents</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleAntecedentsSidebar(); // Toggle sidebar visibility
              }}
    />
</div>
     <div className={isTraitementVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    
    <Group gap="sm">
  <Text fw={500}>Traitement en cours

</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        {/* <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/> */}
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
       
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Saisir"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleTraitementSidebar(); 
              }}
    />
    
</div>
 </Group>
 {/* ---------------------------3--------------------------- */}
  <Group  w={"100%"}>
  <div className={isPathologiesVisible ? 'w-[49%]':'w-[49%]'}>
  <Group  justify="space-between" gap="sm"  > 
    <Group gap="sm">
  <Text fw={500}>Pathologies
</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiTagPlus} size={1} color={"#3799ce"}/>
        {/* <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/> */}
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Saisir"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                togglePathologiesSidebar(); // Toggle sidebar visibility
              }}
    />
</div>
     <div className={isAllergiesVisible ? 'w-[49%]':'w-[49%]'}>
  {/* <Group  justify="space-between" gap="sm"  > 
    
    <Group gap="sm">
  <Text fw={500}>Allergies médicamenteuses
</Text>
    </Group>
    <Group justify="flex-end" gap="sm">
        <Icon path={mdiPlusBox} size={1} color={"#3799ce"}/>
        <Icon path={mdiHistory} size={1} color={"#3799ce"}/>
        <Icon path={mdiMicrophone} size={1} color={"#3799ce"}/>
        <Icon path={mdiClipboardText} size={1} color={"#3799ce"}/>
        <Icon path={mdiDeleteSweep} size={1} color={"#e53935"}/>
       
    </Group>
   </Group>
    <Textarea
      size="xl"
      radius="md"
      placeholder="Ajouter"
      mt={10}
      mb={10}
       onClick={(event) => {
                event.preventDefault();
                toggleAllergiesSidebar(); 
              }}
    /> */}
    
</div>
 </Group>
  </div>
   {/* --------------------------1-------------------------  */}
    {isFacteursVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={open}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleFacteursSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataFacteurs} />
      </Card>
 </div>
    )}
    
     { isAllergiesVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={Allergiesopen}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleAllergiesSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataAllergies} />
      </Card>
 </div>
    )}
     {/* ---------------------------2----------------------- */}
      {isAntecedentsVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={Antecedentsopen}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleAntecedentsSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataFacteurs} />
      </Card>
 </div>
    )}
    
     { isTraitementVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={Traitementopen}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => toggleTraitementSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataAllergies} />
      </Card>
 </div>
    )}
     {/* ---------------------------3--------------------------- */}
       {isPathologiesVisible &&(
    <div className='w-[20%]'>
        <Group justify="space-between">
        <TextInput
        placeholder="Custom layout"
        leftSection={<Icon path={mdiMagnify} size={1} />}
       
      />
        <Group>
            <ActionIcon variant="filled" aria-label="Settings"  onClick={Pathologiesopen}>
      <Icon path={mdiViewHeadline} size={1} />
       </ActionIcon>
       <ActionIcon variant="filled" aria-label="Settings"  onClick={() => togglePathologiesSidebar()}>
     <Icon path={mdiArrowRightBoldBox} size={1}     />
       </ActionIcon>
      </Group>
      </Group>
      <Card shadow="sm" padding="lg" radius="md" withBorder >
    <Tree data={dataFacteurs} />
      </Card>
 </div>
    )}
    
   
    </Group>

     <Modal opened={opened} onClose={close} title="Authentication" centered>
       <Icon path={mdiPlaylistCheck} size={1} /> Choix multiple
      </Modal>
       <Modal opened={openedAllergies} onClose={Allergiesclose} title={<> <Group><Icon path={mdiPlaylistCheck} size={1} /> Choix multiple</Group></>} centered>
       <Tree data={dataAllergies} levelOffset={23} expandOnClick={false} renderNode={renderTreeNode} />
      </Modal>
 {/* ---------------------------2----------------------- */}
  <Modal opened={openedAntecedents} onClose={Antecedentsclose} title="Authentication" centered>
       <Icon path={mdiPlaylistCheck} size={1} /> Choix multiple
      </Modal>
       <Modal opened={openedTraitement } onClose={Traitementclose} title={<> <Group><Icon path={mdiPlaylistCheck} size={1} /> Choix multiple</Group></>} centered>
       <Tree data={dataAllergies} levelOffset={23} expandOnClick={false} renderNode={renderTreeNode} />
      </Modal>
       {/* --------------------------3----------------------- */}
        <Modal opened={openedPathologies} onClose={Pathologiesclose} title="Authentication" centered>
       <Icon path={mdiPlaylistCheck} size={1} /> Choix multiple
      </Modal>
      
    </>  
  )
}
