'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Group,
  Stack,
  Text,
  Button,
  Badge,
  ActionIcon,
  Grid,
  Title,
  Divider,
  Tooltip,
  Select,
  Textarea,
  NumberInput,
} from '@mantine/core';
import {
  IconSparkles,
  IconTool,
  IconScalpel,
  IconAdjustments,
  IconPlus,
  IconTrash,
  IconEdit,
  IconCheck,
  IconX,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { Tooth } from '../../../services/toothService';

// Types pour les traitements
interface TreatmentCategory {
  id: string;
  name: string;
  display_name: string;
  description: string;
  icon: string;
  color: string;
  order: number;
}

interface TreatmentType {
  id: string;
  category: string;
  code: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  duration_minutes: number;
  base_price: number;
  complexity_level: string;
  requires_anesthesia: boolean;
  requires_followup: boolean;
}

interface ToothTreatment {
  id: string;
  tooth: string;
  treatment_type: TreatmentType;
  status: string;
  planned_date: string | null;
  completed_date: string | null;
  notes: string;
  custom_price: number | null;
  surfaces_affected: string[];
  created_at: string;
}

interface ToothTreatmentManagerProps {
  tooth: Tooth | null;
  opened: boolean;
  onClose: () => void;
  onSave: (treatments: ToothTreatment[]) => void;
}

const ToothTreatmentManager: React.FC<ToothTreatmentManagerProps> = ({
  tooth,
  opened,
  onClose,
  onSave,
}) => {
  const [activeTab, setActiveTab] = useState<string>('esthetic');
  const [categories, setCategories] = useState<TreatmentCategory[]>([]);
  const [treatmentTypes, setTreatmentTypes] = useState<TreatmentType[]>([]);
  const [appliedTreatments, setAppliedTreatments] = useState<ToothTreatment[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data - À remplacer par des appels API
  const mockCategories: TreatmentCategory[] = [
    {
      id: '1',
      name: 'esthetic',
      display_name: 'Dentisterie Esthétique',
      description: 'Traitements visant à améliorer l\'apparence des dents',
      icon: 'IconSparkles',
      color: '#E91E63',
      order: 1,
    },
    {
      id: '2',
      name: 'prosthetic',
      display_name: 'Prothèses Thérapeutiques',
      description: 'Remplacement et restauration des dents',
      icon: 'IconTool',
      color: '#2196F3',
      order: 2,
    },
    {
      id: '3',
      name: 'surgery',
      display_name: 'Chirurgie',
      description: 'Interventions chirurgicales dentaires',
      icon: 'IconScalpel',
      color: '#FF5722',
      order: 3,
    },
    {
      id: '4',
      name: 'orthodontics',
      display_name: 'Orthopédie',
      description: 'Correction de l\'alignement des dents',
      icon: 'IconAdjustments',
      color: '#4CAF50',
      order: 4,
    },
  ];

  const mockTreatmentTypes: TreatmentType[] = [
    // Dentisterie Esthétique
    {
      id: '1',
      category: 'esthetic',
      code: 'EST001',
      name: 'Blanchiment Dentaire',
      description: 'Éclaircissement de la couleur des dents',
      icon: 'IconSun',
      color: '#FFC107',
      duration_minutes: 60,
      base_price: 150,
      complexity_level: 'simple',
      requires_anesthesia: false,
      requires_followup: true,
    },
    {
      id: '2',
      category: 'esthetic',
      code: 'EST002',
      name: 'Facettes Céramique',
      description: 'Pose de facettes en céramique',
      icon: 'IconRectangle',
      color: '#E1F5FE',
      duration_minutes: 120,
      base_price: 800,
      complexity_level: 'complex',
      requires_anesthesia: true,
      requires_followup: true,
    },
    // Prothèses Thérapeutiques
    {
      id: '3',
      category: 'prosthetic',
      code: 'PRO001',
      name: 'Couronne Céramique',
      description: 'Pose d\'une couronne en céramique',
      icon: 'IconCrown',
      color: '#FFE0B2',
      duration_minutes: 90,
      base_price: 600,
      complexity_level: 'complex',
      requires_anesthesia: true,
      requires_followup: true,
    },
    {
      id: '4',
      category: 'prosthetic',
      code: 'PRO002',
      name: 'Implant Dentaire',
      description: 'Pose d\'un implant dentaire',
      icon: 'IconScrew',
      color: '#F1F8E9',
      duration_minutes: 120,
      base_price: 1000,
      complexity_level: 'expert',
      requires_anesthesia: true,
      requires_followup: true,
    },
    // Chirurgie
    {
      id: '5',
      category: 'surgery',
      code: 'CHI001',
      name: 'Extraction Simple',
      description: 'Extraction d\'une dent simple',
      icon: 'IconMinus',
      color: '#FFEBEE',
      duration_minutes: 30,
      base_price: 80,
      complexity_level: 'simple',
      requires_anesthesia: true,
      requires_followup: true,
    },
    // Orthopédie
    {
      id: '6',
      category: 'orthodontics',
      code: 'ORT001',
      name: 'Appareil Orthodontique',
      description: 'Pose d\'un appareil orthodontique',
      icon: 'IconBraces',
      color: '#E8F5E8',
      duration_minutes: 60,
      base_price: 2000,
      complexity_level: 'complex',
      requires_anesthesia: false,
      requires_followup: true,
    },
  ];

  useEffect(() => {
    if (opened) {
      setCategories(mockCategories);
      setTreatmentTypes(mockTreatmentTypes);
      // Charger les traitements existants pour cette dent
      loadExistingTreatments();
    }
  }, [opened, tooth]);

  const loadExistingTreatments = async () => {
    // TODO: Charger les traitements existants depuis l'API
    setAppliedTreatments([]);
  };

  const addTreatment = (treatmentType: TreatmentType) => {
    const newTreatment: ToothTreatment = {
      id: `temp-${Date.now()}`,
      tooth: tooth?.id || '',
      treatment_type: treatmentType,
      status: 'planned',
      planned_date: null,
      completed_date: null,
      notes: '',
      custom_price: null,
      surfaces_affected: [],
      created_at: new Date().toISOString(),
    };

    setAppliedTreatments(prev => [...prev, newTreatment]);
    
    notifications.show({
      title: 'Traitement ajouté',
      message: `${treatmentType.name} ajouté à la dent ${tooth?.number}`,
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  const removeTreatment = (treatmentId: string) => {
    setAppliedTreatments(prev => prev.filter(t => t.id !== treatmentId));
    
    notifications.show({
      title: 'Traitement supprimé',
      message: 'Le traitement a été supprimé',
      color: 'orange',
      icon: <IconTrash size={16} />,
    });
  };

  const getIconComponent = (iconName: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      IconSparkles,
      IconTool,
      IconScalpel,
      IconAdjustments,
    };
    const IconComponent = icons[iconName] || IconSparkles;
    return <IconComponent size={20} />;
  };

  const getComplexityColor = (level: string) => {
    switch (level) {
      case 'simple': return 'green';
      case 'moderate': return 'yellow';
      case 'complex': return 'orange';
      case 'expert': return 'red';
      default: return 'gray';
    }
  };

  const handleSave = () => {
    onSave(appliedTreatments);
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconEdit size={20} />
          <span>Traitements - Dent {tooth?.number}</span>
        </Group>
      }
      size="xl"
      styles={{
        body: { padding: 0 },
        header: { borderBottom: '1px solid #e9ecef' },
      }}
    >
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          {categories.map((category) => (
            <Tabs.Tab
              key={category.id}
              value={category.name}
              leftSection={getIconComponent(category.icon)}
              color={category.color}
            >
              {category.display_name}
            </Tabs.Tab>
          ))}
        </Tabs.List>

        {categories.map((category) => (
          <Tabs.Panel key={category.id} value={category.name} p="md">
            <Stack gap="md">
              <Text size="sm" c="dimmed">
                {category.description}
              </Text>

              <Grid>
                {treatmentTypes
                  .filter(type => type.category === category.name)
                  .map((treatmentType) => (
                    <Grid.Col key={treatmentType.id} span={{ base: 12, md: 6 }}>
                      <Card withBorder p="md" h="100%">
                        <Stack gap="sm">
                          <Group justify="space-between">
                            <Group gap="xs">
                              <Badge size="xs" variant="light">
                                {treatmentType.code}
                              </Badge>
                              <Text fw={600} size="sm">
                                {treatmentType.name}
                              </Text>
                            </Group>
                            <ActionIcon
                              variant="light"
                              color="blue"
                              onClick={() => addTreatment(treatmentType)}
                            >
                              <IconPlus size={16} />
                            </ActionIcon>
                          </Group>

                          <Text size="xs" c="dimmed">
                            {treatmentType.description}
                          </Text>

                          <Group gap="xs">
                            <Badge
                              size="xs"
                              color={getComplexityColor(treatmentType.complexity_level)}
                            >
                              {treatmentType.complexity_level}
                            </Badge>
                            <Badge size="xs" variant="outline">
                              {treatmentType.duration_minutes}min
                            </Badge>
                            <Badge size="xs" variant="outline">
                              {treatmentType.base_price}€
                            </Badge>
                          </Group>

                          {(treatmentType.requires_anesthesia || treatmentType.requires_followup) && (
                            <Group gap="xs">
                              {treatmentType.requires_anesthesia && (
                                <Badge size="xs" color="red" variant="light">
                                  Anesthésie
                                </Badge>
                              )}
                              {treatmentType.requires_followup && (
                                <Badge size="xs" color="blue" variant="light">
                                  Suivi
                                </Badge>
                              )}
                            </Group>
                          )}
                        </Stack>
                      </Card>
                    </Grid.Col>
                  ))}
              </Grid>
            </Stack>
          </Tabs.Panel>
        ))}
      </Tabs>

      {/* Traitements appliqués */}
      {appliedTreatments.length > 0 && (
        <div style={{ borderTop: '1px solid #e9ecef', padding: '1rem' }}>
          <Title order={5} mb="md">
            Traitements Planifiés ({appliedTreatments.length})
          </Title>
          <Stack gap="xs">
            {appliedTreatments.map((treatment) => (
              <Card key={treatment.id} withBorder p="sm">
                <Group justify="space-between">
                  <Group gap="xs">
                    <Badge size="xs" variant="light">
                      {treatment.treatment_type.code}
                    </Badge>
                    <Text size="sm" fw={500}>
                      {treatment.treatment_type.name}
                    </Text>
                    <Badge size="xs" color="blue">
                      {treatment.status}
                    </Badge>
                  </Group>
                  <ActionIcon
                    variant="light"
                    color="red"
                    size="sm"
                    onClick={() => removeTreatment(treatment.id)}
                  >
                    <IconTrash size={14} />
                  </ActionIcon>
                </Group>
              </Card>
            ))}
          </Stack>
        </div>
      )}

      {/* Actions */}
      <Group justify="flex-end" p="md" style={{ borderTop: '1px solid #e9ecef' }}>
        <Button variant="light" onClick={onClose}>
          Annuler
        </Button>
        <Button onClick={handleSave} disabled={appliedTreatments.length === 0}>
          Sauvegarder ({appliedTreatments.length} traitements)
        </Button>
      </Group>
    </Modal>
  );
};

export default ToothTreatmentManager;
