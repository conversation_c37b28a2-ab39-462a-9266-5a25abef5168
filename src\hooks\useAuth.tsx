'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import authService, { AuthTokens, ApiError } from '~/services/authService';

interface User {
  id?: string;
  email: string;
  first_name: string;
  last_name: string;
  user_type: string;
  role?: string;
  specialization?: string;
  specialty?: string;
  license_number?: string;
  assigned_doctor?: string;
  assigned_doctor_name?: string;
  // Profile images
  profile_image?: string;
  profile_image_medium?: string;
  profile_image_large?: string;
  // Trial information
  is_trial?: boolean;
  trial_start_date?: string;
  trial_end_date?: string;
  trial_duration_months?: number;
  is_trial_active?: boolean;
  trial_days_remaining?: number;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string, userType: string) => Promise<AuthTokens>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in on initial load
    const checkAuth = async () => {
      setLoading(true);
      try {
        // Ensure we're on the client side
        if (typeof window === 'undefined') {
          setLoading(false);
          return;
        }

        // Check if token exists in localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          setUser(null);
          setLoading(false);
          return;
        }

        // Get user type from localStorage
        const userType = localStorage.getItem('userType') || 'doctor';

        // Set a default user based on user type
        const defaultUser = {
          first_name: userType === 'doctor' ? 'Doctor' : userType === 'assistant' ? 'Assistant' : 'Staff',
          last_name: '',
          email: userType === 'doctor' ? '<EMAIL>' : '<EMAIL>',
          user_type: userType as 'doctor' | 'assistant' | 'staff' | 'patient',
          // Add trial information for development/testing
          is_trial: process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true',
          trial_start_date: '2023-01-01',
          trial_end_date: '2023-12-31',
          trial_duration_months: 12,
          is_trial_active: true,
          trial_days_remaining: 30
        };

        // Try to fetch the actual user profile
        try {
          const userData = await authService.getCurrentUser();
          setUser(userData);
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
          // Continue with the default user
          setUser(defaultUser);
        }
      } catch (error) {
        console.error('Authentication error:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string, userType: string) => {
    setLoading(true);
    setError(null);
    try {
      // Create the credentials object as expected by authService.login
      const credentials = {
        email,
        password,
        userType
      };

      const result = await authService.login(credentials);

      // Store user type in localStorage (client-side only)
      if (typeof window !== 'undefined') {
        localStorage.setItem('userType', userType);
      }

      // Fetch user profile
      try {
        const userData = await authService.getCurrentUser();
        setUser(userData);
      } catch (profileError) {
        console.error('Error fetching user profile after login:', profileError);
        // Set a default user based on user type
        setUser({
          first_name: userType === 'doctor' ? 'Doctor' : userType === 'assistant' ? 'Assistant' : 'Staff',
          last_name: '',
          email: email,
          user_type: userType as 'doctor' | 'assistant' | 'staff' | 'patient',
          // Add trial information for development/testing
          is_trial: process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true',
          trial_start_date: '2023-01-01',
          trial_end_date: '2023-12-31',
          trial_duration_months: 12,
          is_trial_active: true,
          trial_days_remaining: 30
        });
      }

      return result;
    } catch (error) {
      const apiError = error as ApiError;
      console.error('Login error:', apiError);
      setError(apiError.message || 'Failed to login');
      throw apiError;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const refreshUser = async () => {
    setLoading(true);
    try {
      const userData = await authService.getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('Error refreshing user:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, error, login, logout, refreshUser }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
