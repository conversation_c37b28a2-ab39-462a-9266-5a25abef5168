'use client';

import { useState } from 'react';
import {
  Container,
  Title,
  Text,
  Paper,
  Group,
  Button,
  Tabs,
  Table,
  Badge,
  Card,
  SimpleGrid,
  ThemeIcon,
  ActionIcon,
  Tooltip,
  TextInput,
  Select,
  Modal,
  FileInput,
  Textarea,
  Divider,
  Checkbox,
  List,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconFileText,
  IconUpload,
  IconDownload,
  IconTrash,
  IconShare,
  IconPlus,
  IconSearch,
  IconFilter,
  IconCalendar,
  IconUser,
  IconCheck,
  IconFolder,
  IconFolderPlus,
  IconFileDescription,
  IconFileUpload,
  IconLock,
  IconEye,
} from '@tabler/icons-react';
import { DatePickerInput } from '@mantine/dates';

export default function DocumentsPage() {
  const [activeTab, setActiveTab] = useState<string | null>('all');
  const [opened, { open, close }] = useDisclosure(false);
  const [modalType, setModalType] = useState<'upload' | 'folder'>('upload');
  const [selectedDocument, setSelectedDocument] = useState<any>(null);

  // Mock data for documents
  const documents = [
    { id: 'DOC-001', name: 'Patient Consent Form.pdf', type: 'consent', date: '2023-05-15', size: '245 KB', shared: true },
    { id: 'DOC-002', name: 'Medical History.docx', type: 'medical', date: '2023-05-18', size: '125 KB', shared: false },
    { id: 'DOC-003', name: 'Lab Results - Blood Test.pdf', type: 'lab', date: '2023-05-20', size: '1.2 MB', shared: true },
    { id: 'DOC-004', name: 'Treatment Plan.pdf', type: 'treatment', date: '2023-05-22', size: '350 KB', shared: false },
    { id: 'DOC-005', name: 'Insurance Claim Form.pdf', type: 'insurance', date: '2023-05-25', size: '180 KB', shared: false },
  ];

  const handleUploadDocument = () => {
    setSelectedDocument(null);
    setModalType('upload');
    open();
  };

  const handleCreateFolder = () => {
    setModalType('folder');
    open();
  };

  const handleViewDocument = (document: any) => {
    setSelectedDocument(document);
    notifications.show({
      title: 'View Document',
      message: `Viewing ${document.name}`,
      color: 'blue',
    });
  };

  const handleDownloadDocument = (document: any) => {
    notifications.show({
      title: 'Download Document',
      message: `Downloading ${document.name}`,
      color: 'green',
    });
  };

  const handleShareDocument = (document: any) => {
    notifications.show({
      title: 'Share Document',
      message: `Sharing options for ${document.name}`,
      color: 'blue',
    });
  };

  const handleDeleteDocument = (document: any) => {
    notifications.show({
      title: 'Delete Document',
      message: `${document.name} has been deleted`,
      color: 'red',
    });
  };

  const handleRevokeAccess = (document: any) => {
    notifications.show({
      title: 'Access Revoked',
      message: `Access to ${document.name} has been revoked`,
      color: 'red',
    });
  };

  const handleUseTemplate = (template: string) => {
    notifications.show({
      title: 'Template Selected',
      message: `Using ${template} template`,
      color: 'blue',
    });
  };

  const handleSubmit = () => {
    notifications.show({
      title: modalType === 'upload' ? 'Document Uploaded' : 'Folder Created',
      message: modalType === 'upload'
        ? 'The document has been uploaded successfully.'
        : 'The folder has been created successfully.',
      color: 'green',
      icon: <IconCheck size={16} />,
    });
    close();
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'consent':
        return 'blue';
      case 'medical':
        return 'green';
      case 'lab':
        return 'orange';
      case 'treatment':
        return 'violet';
      case 'insurance':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'consent':
        return 'Consent Form';
      case 'medical':
        return 'Medical Record';
      case 'lab':
        return 'Lab Result';
      case 'treatment':
        return 'Treatment Plan';
      case 'insurance':
        return 'Insurance';
      default:
        return 'Document';
    }
  };

  return (
    <>
      <Paper p="xl" radius="md" withBorder mb="xl" w={"100%"}>
        <Group justify="space-between" mb="md">
          <div>
            <Title order={2}>Document Management</Title>
            <Text c="dimmed">Upload, organize, and share patient documents</Text>
          </div>
          <Group>
            <Button
              leftSection={<IconUpload size={16} />}
              onClick={handleUploadDocument}
            >
              Upload Document
            </Button>
            <Button
              variant="outline"
              leftSection={<IconFolderPlus size={16} />}
              onClick={handleCreateFolder}
            >
              Create Folder
            </Button>
          </Group>
        </Group>

        <SimpleGrid cols={{ base: 1, md: 3 }} mt="xl">
          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="blue">
                <IconFileText size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Total Documents</Text>
                <Text fw={700} size="xl">42</Text>
              </div>
            </Group>
          </Card>

          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="green">
                <IconFolder size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Folders</Text>
                <Text fw={700} size="xl">8</Text>
              </div>
            </Group>
          </Card>

          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="orange">
                <IconShare size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Shared Documents</Text>
                <Text fw={700} size="xl">15</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>
      </Paper>
 <Paper p="xl" radius="md" withBorder mb="xl" w={"100%"}>
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List mb="md">
          <Tabs.Tab value="all" leftSection={<IconFileText size={16} />}>
            All Documents
          </Tabs.Tab>
          <Tabs.Tab value="medical" leftSection={<IconFileDescription size={16} />}>
            Medical Records
          </Tabs.Tab>
          <Tabs.Tab value="shared" leftSection={<IconShare size={16} />}>
            Shared Documents
          </Tabs.Tab>
          <Tabs.Tab value="templates" leftSection={<IconFileUpload size={16} />}>
            Templates
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="all">
          <Paper p="md" withBorder>
            <Group mb="md">
              <TextInput
                placeholder="Search documents"
                leftSection={<IconSearch size={16} />}
                style={{ flex: 1 }}
              />
              <Select
                placeholder="Filter by type"
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: 'all', label: 'All Types' },
                  { value: 'consent', label: 'Consent Forms' },
                  { value: 'medical', label: 'Medical Records' },
                  { value: 'lab', label: 'Lab Results' },
                  { value: 'treatment', label: 'Treatment Plans' },
                  { value: 'insurance', label: 'Insurance' },
                ]}
                defaultValue="all"
              />
              <DatePickerInput
                placeholder="Date range"
                leftSection={<IconCalendar size={16} />}
                type="range"
              />
            </Group>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Name</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Date</Table.Th>
                  <Table.Th>Size</Table.Th>
                  <Table.Th>Shared</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {documents.map((document) => (
                  <Table.Tr key={document.id}>
                    <Table.Td>{document.name}</Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(document.type)}>
                        {getTypeLabel(document.type)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>{document.date}</Table.Td>
                    <Table.Td>{document.size}</Table.Td>
                    <Table.Td>
                      {document.shared ? (
                        <Badge color="green">Shared</Badge>
                      ) : (
                        <Badge color="gray" variant="outline">Private</Badge>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <Tooltip label="View">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleViewDocument(document)}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Download">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleDownloadDocument(document)}
                          >
                            <IconDownload size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Share">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleShareDocument(document)}
                          >
                            <IconShare size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Delete">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            onClick={() => handleDeleteDocument(document)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="medical">
          <Paper p="md" withBorder>
            <Title order={3} mb="md">Medical Records</Title>
            <Text c="dimmed" mb="xl">
              Access and manage patient medical records
            </Text>

            <SimpleGrid cols={{ base: 1, md: 2 }} spacing="md">
              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="blue">
                    <IconUser size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Patient Records</Text>
                    <Text size="sm" c="dimmed">Access records by patient</Text>
                  </div>
                </Group>
                <Group>
                  <Select
                    placeholder="Select patient"
                    data={[
                      { value: 'john-doe', label: 'John Doe' },
                      { value: 'jane-smith', label: 'Jane Smith' },
                      { value: 'robert-johnson', label: 'Robert Johnson' },
                    ]}
                    style={{ flex: 1 }}
                  />
                  <Button>View Records</Button>
                </Group>
              </Card>

              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="green">
                    <IconFileUpload size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Upload Medical Record</Text>
                    <Text size="sm" c="dimmed">Add a new medical record</Text>
                  </div>
                </Group>
                <Group>
                  <Select
                    placeholder="Select patient"
                    data={[
                      { value: 'john-doe', label: 'John Doe' },
                      { value: 'jane-smith', label: 'Jane Smith' },
                      { value: 'robert-johnson', label: 'Robert Johnson' },
                    ]}
                    style={{ flex: 1 }}
                  />
                  <Button leftSection={<IconUpload size={16} />}>Upload</Button>
                </Group>
              </Card>
            </SimpleGrid>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="shared">
          <Paper p="md" withBorder>
            <Title order={3} mb="md">Shared Documents</Title>
            <Text c="dimmed" mb="xl">
              Documents shared with patients or other healthcare providers
            </Text>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Name</Table.Th>
                  <Table.Th>Shared With</Table.Th>
                  <Table.Th>Date Shared</Table.Th>
                  <Table.Th>Expiration</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                <Table.Tr>
                  <Table.Td>Patient Consent Form.pdf</Table.Td>
                  <Table.Td>John Doe</Table.Td>
                  <Table.Td>2023-05-15</Table.Td>
                  <Table.Td>Never</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Tooltip label="View">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => handleViewDocument({ name: 'Patient Consent Form.pdf' })}
                        >
                          <IconEye size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Revoke Access">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleRevokeAccess({ name: 'Patient Consent Form.pdf' })}
                        >
                          <IconLock size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
                <Table.Tr>
                  <Table.Td>Lab Results - Blood Test.pdf</Table.Td>
                  <Table.Td>Jane Smith</Table.Td>
                  <Table.Td>2023-05-20</Table.Td>
                  <Table.Td>2023-06-20</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <Tooltip label="View">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => handleViewDocument({ name: 'Lab Results - Blood Test.pdf' })}
                        >
                          <IconEye size={16} />
                        </ActionIcon>
                      </Tooltip>
                      <Tooltip label="Revoke Access">
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleRevokeAccess({ name: 'Lab Results - Blood Test.pdf' })}
                        >
                          <IconLock size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              </Table.Tbody>
            </Table>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="templates">
          <Paper p="md" withBorder>
            <Title order={3} mb="md">Document Templates</Title>
            <Text c="dimmed" mb="xl">
              Standardized templates for common documents
            </Text>

            <SimpleGrid cols={{ base: 1, md: 3 }} spacing="md">
              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="blue">
                    <IconFileText size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Consent Form</Text>
                    <Text size="sm" c="dimmed">Standard patient consent</Text>
                  </div>
                </Group>
                <Group>
                  <Button
                    variant="outline"
                    leftSection={<IconEye size={16} />}
                    style={{ flex: 1 }}
                    onClick={() => handleViewDocument({ name: 'Consent Form Template' })}
                  >
                    Preview
                  </Button>
                  <Button
                    leftSection={<IconDownload size={16} />}
                    onClick={() => handleUseTemplate('Consent Form')}
                  >
                    Use
                  </Button>
                </Group>
              </Card>

              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="green">
                    <IconFileText size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Medical History</Text>
                    <Text size="sm" c="dimmed">Patient history form</Text>
                  </div>
                </Group>
                <Group>
                  <Button variant="outline" leftSection={<IconEye size={16} />} style={{ flex: 1 }}>Preview</Button>
                  <Button leftSection={<IconDownload size={16} />}>Use</Button>
                </Group>
              </Card>

              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="orange">
                    <IconFileText size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Treatment Plan</Text>
                    <Text size="sm" c="dimmed">Standard treatment plan</Text>
                  </div>
                </Group>
                <Group>
                  <Button variant="outline" leftSection={<IconEye size={16} />} style={{ flex: 1 }}>Preview</Button>
                  <Button leftSection={<IconDownload size={16} />}>Use</Button>
                </Group>
              </Card>
            </SimpleGrid>
          </Paper>
        </Tabs.Panel>
      </Tabs>
</Paper>
      <Modal opened={opened} onClose={close} title={modalType === 'upload' ? "Upload Document" : "Create New Folder"} size="lg">
        <div>
          {modalType === 'upload' ? (
            <>
              <FileInput
                label="Select File"
                placeholder="Click to select or drag and drop"
                accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/jpeg,image/png"
                mb="md"
              />

              <Select
                label="Document Type"
                placeholder="Select document type"
                data={[
                  { value: 'consent', label: 'Consent Form' },
                  { value: 'medical', label: 'Medical Record' },
                  { value: 'lab', label: 'Lab Result' },
                  { value: 'treatment', label: 'Treatment Plan' },
                  { value: 'insurance', label: 'Insurance' },
                  { value: 'other', label: 'Other' },
                ]}
                mb="md"
              />

              <Select
                label="Patient"
                placeholder="Select patient"
                data={[
                  { value: 'john-doe', label: 'John Doe' },
                  { value: 'jane-smith', label: 'Jane Smith' },
                  { value: 'robert-johnson', label: 'Robert Johnson' },
                  { value: 'emily-davis', label: 'Emily Davis' },
                  { value: 'michael-brown', label: 'Michael Brown' },
                ]}
                mb="md"
              />

              <Select
                label="Folder"
                placeholder="Select folder"
                data={[
                  { value: 'root', label: 'Root Directory' },
                  { value: 'medical-records', label: 'Medical Records' },
                  { value: 'lab-results', label: 'Lab Results' },
                  { value: 'consent-forms', label: 'Consent Forms' },
                  { value: 'insurance', label: 'Insurance' },
                ]}
                mb="md"
              />

              <Textarea
                label="Description"
                placeholder="Enter a description for this document"
                mb="md"
              />

              <Checkbox
                label="Share with patient"
                mb="md"
              />

              <Divider my="md" />

              <Text size="sm" fw={500} mb="xs">Access Permissions</Text>
              <List size="sm" spacing="xs" mb="md">
                <List.Item>Only you can view this document by default</List.Item>
                <List.Item>If shared with patient, they will have view access</List.Item>
                <List.Item>You can modify sharing settings later</List.Item>
              </List>
            </>
          ) : (
            <>
              <TextInput
                label="Folder Name"
                placeholder="Enter folder name"
                mb="md"
              />

              <Select
                label="Parent Folder"
                placeholder="Select parent folder"
                data={[
                  { value: 'root', label: 'Root Directory' },
                  { value: 'medical-records', label: 'Medical Records' },
                  { value: 'lab-results', label: 'Lab Results' },
                  { value: 'consent-forms', label: 'Consent Forms' },
                  { value: 'insurance', label: 'Insurance' },
                ]}
                mb="md"
              />

              <Textarea
                label="Description"
                placeholder="Enter a description for this folder"
                mb="md"
              />
            </>
          )}

          <Group justify="flex-end" mt="xl">
            <Button variant="outline" onClick={close}>Cancel</Button>
            <Button onClick={handleSubmit}>{modalType === 'upload' ? 'Upload Document' : 'Create Folder'}</Button>
          </Group>
        </div>
      </Modal>
    </>
  );
}
