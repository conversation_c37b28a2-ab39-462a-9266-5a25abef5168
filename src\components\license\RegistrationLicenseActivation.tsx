'use client';

import { useState } from 'react';
import {
  Paper,
  Title,
  Text,
  TextInput,
  Button,
  Stack,
  Alert,
  Divider,
  Code
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { I<PERSON><PERSON><PERSON><PERSON>, IconArrowRight } from '@tabler/icons-react';
import api from '~/lib/api';
import { AxiosError } from 'axios';
import LicenseError from './LicenseError';

interface RegistrationLicenseActivationProps {
  onActivationSuccess?: () => void;
}

export default function RegistrationLicenseActivation({
  onActivationSuccess
}: RegistrationLicenseActivationProps) {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm({
    initialValues: {
      license_number: '', // Don't pre-fill the license number
      activation_code: '',
    },
    validate: {
      license_number: (value) => (!value ? 'License number is required' : null),
      activation_code: (value) => (!value ? 'Activation code is required' : null),
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input format before sending to server
      const licenseNumber = values.license_number.trim();
      const activationCode = values.activation_code.trim();

      // Basic client-side validation
      if (!licenseNumber) {
        setError('Please enter a valid license number');
        return;
      }

      if (!activationCode) {
        setError('Please enter a valid activation code');
        return;
      }

      console.log('Submitting license activation:', {
        license_number: licenseNumber,
        activation_code: activationCode,
      });

      const response = await api.post('/api/auth/license/activate/', {
        license_number: licenseNumber,
        activation_code: activationCode,
      });

      console.log('License activation successful:', response.data);

      setSuccess(true);
      notifications.show({
        title: 'License Activated',
        message: 'Your license has been successfully activated.',
        color: 'green',
        icon: <IconCheck size="1.1rem" />,
      });

      // Set a flag to indicate successful activation
      try {
        sessionStorage.setItem('licenseActivated', 'true');
      } catch {
        // Ignore storage errors - this is just a convenience flag
        // and not critical to the application's functionality
      }

      // Call the success callback if provided
      if (onActivationSuccess) {
        onActivationSuccess();
      }

      // Redirect to the login page after successful activation
      console.info('License activated successfully. Redirecting to login page...');

      // Use a timeout to allow the success message to be seen
      setTimeout(() => {
        try {
          // Redirect to the login page
          window.location.href = '/login';
        } catch (routerError) {
          console.error('Navigation error:', routerError);
        }
      }, 1500);
    } catch (error) {
      const err = error as AxiosError<{
        license_number?: string | string[];
        activation_code?: string | string[];
        detail?: string;
        [key: string]: unknown;
      }>;

      console.error('License activation error:', err);

      // Handle different types of errors with user-friendly messages
      if (err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);

        // Map error codes to user-friendly messages
        if (err.response.status === 400) {
          // Handle validation errors
          if (err.response.data) {
            const responseData = err.response.data;

            // Check for license number errors
            if (responseData.license_number) {
              if (typeof responseData.license_number === 'string') {
                setError(`The license number you entered is invalid: ${responseData.license_number}`);
              } else if (Array.isArray(responseData.license_number)) {
                setError(`The license number you entered is invalid: ${responseData.license_number[0]}`);
              } else {
                setError('The license number you entered is invalid. Please check and try again.');
              }
              return;
            }

            // Check for activation code errors
            if (responseData.activation_code) {
              if (typeof responseData.activation_code === 'string') {
                setError(`The activation code you entered is invalid: ${responseData.activation_code}`);
              } else if (Array.isArray(responseData.activation_code)) {
                setError(`The activation code you entered is invalid: ${responseData.activation_code[0]}`);
              } else {
                setError('The activation code you entered is invalid. Please check and try again.');
              }
              return;
            }

            // Check for detail message
            if (responseData.detail) {
              const detailMessage = responseData.detail;
              if (typeof detailMessage === 'string') {
                if (detailMessage.includes('License number not found')) {
                  setError('The license number you entered does not exist in our system. Please check and try again.');
                } else if (detailMessage.includes('Invalid activation code')) {
                  setError('The activation code you entered is incorrect. Please check and try again.');
                } else if (detailMessage.includes('already')) {
                  setError(detailMessage);
                } else {
                  setError(detailMessage);
                }
              } else {
                setError('An error occurred while activating your license. Please try again.');
              }
              return;
            }

            // Generic validation error
            setError('There was a problem with the information you provided. Please check your license number and activation code.');
          } else {
            setError('Please check your license number and activation code and try again.');
          }
        } else if (err.response.status === 401) {
          setError('Your session has expired. Please log in again to activate your license.');
        } else if (err.response.status === 403) {
          setError('You do not have permission to activate this license. Please contact support.');
        } else if (err.response.status === 404) {
          setError('The license number you entered does not exist in our system. Please check and try again.');
        } else if (err.response.status >= 500) {
          setError('We\'re experiencing technical difficulties. Please try again later or contact support.');
        } else {
          setError('An error occurred while activating your license. Please try again or contact support.');
        }
      } else if (err.request) {
        // The request was made but no response was received
        setError('Unable to connect to our servers. Please check your internet connection and try again.');
      } else {
        // Something happened in setting up the request
        setError('An unexpected error occurred. Please try again or contact support.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Paper p="xl" radius="md" withBorder style={{
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        backgroundColor: 'white'
      }}>
        <Stack gap="md">
          <Alert
            icon={<IconCheck size="1.1rem" />}
            title="License Activated Successfully"
            color="green"
            variant="filled"
            styles={{
              root: { borderRadius: '8px' },
              title: { fontWeight: 600 }
            }}
          >
            Your license has been successfully activated. You will be redirected to the login page.
          </Alert>

          <Text size="sm" mt="md" style={{ lineHeight: 1.6 }}>
            Thank you for activating your license. Please log in to access your account.
          </Text>

          <Button
            component="a"
            href="/login"
            size="lg"
            fullWidth
            leftSection={<IconArrowRight size="1.1rem" />}
            color="blue"
            loading={true}
            styles={{
              root: {
                height: '48px',
                fontSize: '16px',
                fontWeight: 600,
                borderRadius: '8px',
                transition: 'all 0.2s ease'
              }
            }}
          >
            Redirecting to Login Page...
          </Button>

          <Text size="xs" c="dimmed" ta="center" style={{ marginTop: '8px' }}>
            You will be redirected to the login page automatically
          </Text>
        </Stack>
      </Paper>
    );
  }

  return (
    <Paper p="xl" radius="md" withBorder style={{
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      backgroundColor: 'white'
    }}>
      <Title order={3} mb="md" style={{ fontWeight: 600, color: '#1c2a3a' }}>Activate Your License</Title>

      <Text size="sm" mb="lg" c="dimmed" style={{ lineHeight: 1.6 }}>
        Enter your license number and activation code to activate your account.
        If you don&rsquo;t have an activation code, please check your email or contact support.
      </Text>

      {error && (
        <LicenseError
          message={error}
          onRetry={() => {
            setError(null);
            form.reset();
          }}
        />
      )}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="License Number"
            placeholder="Enter your license number"
            required
            {...form.getInputProps('license_number')}
            // Remove the disabled property to make it editable
            styles={{
              input: {
                height: '42px',
                fontSize: '15px',
                borderRadius: '6px',
                border: '1px solid #dee2e6'
              },
              label: {
                fontSize: '14px',
                fontWeight: 500,
                marginBottom: '4px'
              }
            }}
          />

          <TextInput
            label="Activation Code"
            placeholder="Enter your activation code"
            required
            {...form.getInputProps('activation_code')}
            styles={{
              input: {
                height: '42px',
                fontSize: '15px',
                borderRadius: '6px',
                border: '1px solid #dee2e6'
              },
              label: {
                fontSize: '14px',
                fontWeight: 500,
                marginBottom: '4px'
              }
            }}
          />

          <Button
            type="submit"
            loading={loading}
            fullWidth
            mt="md"
            size="lg"
            color="blue"
            styles={{
              root: {
                height: '48px',
                fontSize: '16px',
                fontWeight: 600,
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                marginTop: '16px'
              }
            }}
          >
            Activate License
          </Button>
        </Stack>
      </form>

      <Divider my="lg" label="Need Help?" labelPosition="center" styles={{
        label: {
          fontSize: '14px',
          fontWeight: 500,
          color: '#6c757d'
        }
      }} />

      <Text size="sm" mb="md" style={{ lineHeight: 1.6 }}>
        If you don&rsquo;t have an activation code or are experiencing issues, please contact our support team:
      </Text>

      <Code block style={{
        backgroundColor: '#f8f9fa',
        padding: '12px',
        borderRadius: '6px',
        fontSize: '14px',
        lineHeight: 1.6
      }}>
        <EMAIL>
        +1 (555) 123-4567
      </Code>
    </Paper>
  );
}
