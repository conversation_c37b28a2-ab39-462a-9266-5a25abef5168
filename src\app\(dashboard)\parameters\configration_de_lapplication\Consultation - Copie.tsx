import React, { useState } from 'react';
import {
  Button,
  Card,
  Group,
  Text,
  TextInput,
  Select,
  Modal,
  Tabs,
  ActionIcon,
  Badge,
  Stack,
  Switch,
} from '@mantine/core';
import {
  IconPlus,
  IconArrowLeft,
  IconX,
  IconDeviceFloppy,
  IconFileText,
  IconColorPicker,
  IconStethoscope,
  IconActivity,
  IconMedicalCross,
  IconBabyCarriage,
  IconFolder,
} from '@tabler/icons-react';

// Types pour les données
interface BlocData {
  id: string;
  name: string;
  color: string;
  isEditing?: boolean;
}

interface ProcedureData {
  id: string;
  name: string;
  isEditing?: boolean;
}

const Consultation = () => {
  // États pour la gestion des modals
  const [newBlocModalOpened, setNewBlocModalOpened] = useState(false);
  const [newProcedureModalOpened, setNewProcedureModalOpened] = useState(false);
  const [scrollDisabled, setScrollDisabled] = useState(false);

  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string | null>('titre');

  // États pour les données
  const [blocs, setBlocs] = useState<BlocData[]>([]);
  const [procedures, setProcedures] = useState<ProcedureData[]>([]);

  // États pour les formulaires des modals
  const [newBlocName, setNewBlocName] = useState('');
  const [newBlocColor, setNewBlocColor] = useState('#ff0000');
  const [newProcedureName, setNewProcedureName] = useState('');

  // Fonctions pour gérer les modals
  const openNewBlocModal = () => setNewBlocModalOpened(true);
  const closeNewBlocModal = () => {
    setNewBlocModalOpened(false);
    setNewBlocName('');
    setNewBlocColor('#ff0000');
  };

  const openNewProcedureModal = () => setNewProcedureModalOpened(true);
  const closeNewProcedureModal = () => {
    setNewProcedureModalOpened(false);
    setNewProcedureName('');
  };

  // Fonctions pour ajouter des éléments
  const handleAddBloc = () => {
    if (newBlocName.trim()) {
      const newBloc: BlocData = {
        id: Date.now().toString(),
        name: newBlocName.trim(),
        color: newBlocColor,
      };
      setBlocs([...blocs, newBloc]);
      closeNewBlocModal();
    }
  };

  const handleAddProcedure = () => {
    if (newProcedureName.trim()) {
      const newProcedure: ProcedureData = {
        id: Date.now().toString(),
        name: newProcedureName.trim(),
      };
      setProcedures([...procedures, newProcedure]);
      closeNewProcedureModal();
    }
  };

  // Fonctions pour supprimer des éléments
  const handleRemoveBloc = (id: string) => {
    setBlocs(blocs.filter(bloc => bloc.id !== id));
  };

  const handleRemoveProcedure = (id: string) => {
    setProcedures(procedures.filter(procedure => procedure.id !== id));
  };

  // Fonctions de sauvegarde
  const handleSave = () => {
    console.log('Sauvegarde des données');
    // Logique de sauvegarde ici
  };

  const handleSaveAndExit = () => {
    console.log('Sauvegarde et sortie');
    // Logique de sauvegarde et redirection ici
  };

  const handleCancel = () => {
    console.log('Annulation');
    // Logique d'annulation ici
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête avec flèche de retour et titre */}
      <div className="bg-[#3799CE] text-white px-6 py-4">
        <div className="flex items-center gap-3">
          <IconArrowLeft size={20} className="cursor-pointer hover:bg-[#2d89bd] rounded p-1" />
          <Text size="lg" fw={600}>
            Modèle consultation
          </Text>
          <div className="ml-auto flex items-center gap-2">
            <Switch
              checked={scrollDisabled}
              onChange={(event) => setScrollDisabled(event.currentTarget.checked)}
              size="sm"
              color="white"
              thumbIcon={
                scrollDisabled ? (
                  <IconX size={12} color="#3799CE" />
                ) : (
                  <IconDeviceFloppy size={12} color="#3799CE" />
                )
              }
            />
            <Text size="sm">Désactiver le scroll automatique</Text>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 p-6">
        <Card shadow="sm" padding="lg" radius="md" className="bg-white h-full">
          {/* Onglets */}
          <Tabs value={activeTab} onChange={setActiveTab} className="mb-6">
            <Tabs.List>
              <Tabs.Tab
                value="titre"
                leftSection={<IconFileText size={16} />}
                className="text-red-600"
              >
                Titre *
              </Tabs.Tab>
              <Tabs.Tab
                value="couleur"
                leftSection={<IconColorPicker size={16} />}
              >
                Couleur
              </Tabs.Tab>
              <Tabs.Tab
                value="fiche-medical"
                leftSection={<IconStethoscope size={16} />}
              >
                Fiche médical
              </Tabs.Tab>
              <Tabs.Tab
                value="biometrie"
                leftSection={<IconActivity size={16} />}
              >
                Biométrie
              </Tabs.Tab>
              <Tabs.Tab
                value="diagnostique"
                leftSection={<IconMedicalCross size={16} />}
              >
                Diagnostique et Pathologies
              </Tabs.Tab>
              <Tabs.Tab
                value="suivi-grossesse"
                leftSection={<IconBabyCarriage size={16} />}
              >
                Suivi de grossesse
              </Tabs.Tab>
              <Tabs.Tab
                value="gestionnaire"
                leftSection={<IconFolder size={16} />}
              >
                Gestionnaire de fichiers
              </Tabs.Tab>
            </Tabs.List>
          </Tabs>

          {/* Boutons d'ajout */}
          <div className="flex gap-4 mb-6">
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={openNewBlocModal}
              className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
              size="sm"
            >
              Ajouter un nouveau bloc
            </Button>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={openNewProcedureModal}
              className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
              size="sm"
            >
              Ajouter une nouvelle procédure
            </Button>
          </div>

          {/* Zone de contenu principal */}
          <div className="flex-1 min-h-[400px] border border-gray-200 rounded-lg p-4 bg-gray-50">
            {/* Affichage des blocs ajoutés */}
            {blocs.length > 0 && (
              <div className="mb-4">
                <Text size="sm" fw={500} className="mb-2">Blocs de saisie :</Text>
                <div className="flex flex-wrap gap-2">
                  {blocs.map((bloc) => (
                    <Badge
                      key={bloc.id}
                      color={bloc.color}
                      variant="filled"
                      rightSection={
                        <ActionIcon
                          size="xs"
                          color="white"
                          radius="xl"
                          variant="transparent"
                          onClick={() => handleRemoveBloc(bloc.id)}
                        >
                          <IconX size={10} />
                        </ActionIcon>
                      }
                    >
                      {bloc.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Affichage des procédures ajoutées */}
            {procedures.length > 0 && (
              <div className="mb-4">
                <Text size="sm" fw={500} className="mb-2">Procédures :</Text>
                <div className="flex flex-wrap gap-2">
                  {procedures.map((procedure) => (
                    <Badge
                      key={procedure.id}
                      color="blue"
                      variant="light"
                      rightSection={
                        <ActionIcon
                          size="xs"
                          color="blue"
                          radius="xl"
                          variant="transparent"
                          onClick={() => handleRemoveProcedure(procedure.id)}
                        >
                          <IconX size={10} />
                        </ActionIcon>
                      }
                    >
                      {procedure.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Message si aucun élément */}
            {blocs.length === 0 && procedures.length === 0 && (
              <div className="text-center text-gray-500 py-20">
                <Text size="sm">
                  Aucun bloc ou procédure ajouté. Utilisez les boutons ci-dessus pour commencer.
                </Text>
              </div>
            )}
          </div>

          {/* Boutons d'action en bas */}
          <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              color="red"
              onClick={handleCancel}
              size="sm"
            >
              Annuler
            </Button>
            <Button
              onClick={handleSaveAndExit}
              className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
              size="sm"
            >
              Enregistrer et quitter
            </Button>
            <Button
              onClick={handleSave}
              className="bg-blue-500 hover:bg-blue-600 text-white"
              size="sm"
            >
              Enregistrer
            </Button>
          </div>
        </Card>
      </div>

      {/* Modal pour ajouter un nouveau bloc */}
      <Modal.Root opened={newBlocModalOpened} onClose={closeNewBlocModal} size="md">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#ff6b35", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="white" className="flex gap-2 text-lg">
                <IconPlus size={20} />
                Bloc de saisie
              </Text>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#e55a2b]" />
          </Modal.Header>
          <Modal.Body p="md">
            <Stack gap="md">
              <TextInput
                label="Nom du bloc"
                placeholder="Entrez le nom du bloc"
                value={newBlocName}
                onChange={(e) => setNewBlocName(e.currentTarget.value)}
                required
              />
              <div>
                <Text size="sm" fw={500} mb="xs">Couleur</Text>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={newBlocColor}
                    onChange={(e) => setNewBlocColor(e.target.value)}
                    className="w-10 h-10 rounded border border-gray-300 cursor-pointer"
                  />
                  <Text size="sm" c="dimmed">{newBlocColor}</Text>
                </div>
              </div>
              <Group justify="flex-end" mt="md">
                <Button variant="default" onClick={closeNewBlocModal}>
                  Annuler
                </Button>
                <Button
                  onClick={handleAddBloc}
                  disabled={!newBlocName.trim()}
                  className="bg-[#ff6b35] hover:bg-[#e55a2b] text-white"
                >
                  Ajouter
                </Button>
              </Group>
            </Stack>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>

      {/* Modal pour ajouter une nouvelle procédure */}
      <Modal.Root opened={newProcedureModalOpened} onClose={closeNewProcedureModal} size="md">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#00BFFF", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="white" className="flex gap-2 text-lg">
                <IconPlus size={20} />
                Procédure
              </Text>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#00A5E6]" />
          </Modal.Header>
          <Modal.Body p="md">
            <Stack gap="md">
              <Select
                label="Procédure"
                placeholder="Sélectionner une procédure"
                value={newProcedureName}
                onChange={(value) => setNewProcedureName(value || '')}
                data={[
                  { value: 'Analyse Biologique', label: 'Analyse Biologique' },
                  { value: 'Radiologie', label: 'Radiologie' },
                  { value: 'Échographie', label: 'Échographie' },
                  { value: 'Consultation spécialisée', label: 'Consultation spécialisée' },
                  { value: 'Intervention chirurgicale', label: 'Intervention chirurgicale' },
                ]}
                searchable
                required
              />
              <Group justify="flex-end" mt="md">
                <Button variant="default" onClick={closeNewProcedureModal}>
                  Annuler
                </Button>
                <Button
                  onClick={handleAddProcedure}
                  disabled={!newProcedureName.trim()}
                  className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
                >
                  Ajouter
                </Button>
              </Group>
            </Stack>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </div>
  );
};

export default Consultation
