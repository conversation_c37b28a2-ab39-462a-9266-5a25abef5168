"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { notifications } from '@mantine/notifications';
import Link from 'next/link';
import {
  Avatar,
  Group,
  rem,
  Text,
  UnstyledButton,
  Menu,

} from "@mantine/core";
import {
  IconLogout,
  IconMessage,
  IconSettings,
  IconTrash,
  IconChevronDown,
  IconUser,
  IconSearch,
} from "@tabler/icons-react";
import authService from '~/services/authService';

interface UserProfile {
  first_name?: string;
  firstName?: string;
  last_name?: string;
  lastName?: string;
  email?: string;
  user_type?: string;
  profile_image?: string;
}

const UserActive = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const router = useRouter();
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Check if user is authenticated using the validateAuthentication method
        const isAuth = await authService.validateAuthentication();
        setIsAuthenticated(isAuth);

        if (!isAuth) {
          router.push('/login');
          return;
        }

        // Fetch user profile
        try {
          const profileData = await authService.getProfile();
          setUserProfile(profileData);
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
          // Set default user
          const defaultUser = {
            first_name: 'Doctor',
            last_name: '',
            email: '<EMAIL>',
            user_type: 'doctor'
          };
          setUserProfile(defaultUser);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to load dashboard data',
          color: 'red',
        });
      }
    };

    fetchDashboardData();
  }, [router]);
  const handleLogout = async () => {
    try {
      await authService.logout();
      setIsAuthenticated(false);
      setUserProfile(null);
      // Redirect to the login page instead of the home page
      router.push('/login');
    } catch (error) {
      console.error('Error logging out:', error);
      // Even if there's an error, still try to redirect to login
      router.push('/login');
    }
  };
  const getUserInitials = () => {
    if (!userProfile) return '';

    const firstName = userProfile.first_name || userProfile.firstName || '';
    const lastName = userProfile.last_name || userProfile.lastName || '';

    return `${firstName.charAt(0)}${lastName.charAt(0)}`;
  };

  // Get user display name
  const getUserDisplayName = () => {
    if (!userProfile) return '';

    const firstName = userProfile.first_name || userProfile.firstName || '';
    const lastName = userProfile.last_name || userProfile.lastName || '';

    return `${firstName} ${lastName}`;
  };
  // Handle image loading errors
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error('Error loading profile image:', e);
    // Set the src to null to trigger the fallback to initials
    e.currentTarget.src = '';
    // Force a re-render to show initials instead
    if (userProfile) {
      const updatedProfile = { ...userProfile };
      updatedProfile.profile_image = '';
      setUserProfile(updatedProfile);
    }
  };

  return (
    <>
   {isAuthenticated ? (
    <Menu
      width={260}
      position="bottom-end"
      transitionProps={{ transition: "pop-top-right" }}
      withinPortal
      shadow="lg"
      zIndex={1000010}
      withArrow
    >
      <Menu.Target>
        <UnstyledButton>
          <Group gap={7} pl={"3px"}>
            {userProfile?.profile_image ? (
              <Avatar
                src={userProfile.profile_image}
                radius="xl"
                onError={handleImageError}
                size={20}
              />
            ) : (
              <Avatar color="blue" radius="xl">
                {getUserInitials()}
              </Avatar>
            )}
            <Text size="sm" fw={500}>
              {getUserDisplayName().slice(0, 12)}
            </Text>
            <IconChevronDown
              style={{ width: rem(12), height: rem(12) }}
              stroke={1.5}
            />
          </Group>
        </UnstyledButton>
      </Menu.Target>
      <Menu.Dropdown style={{top: "46.6406px"}}>
        <Menu.Item
          leftSection={<IconUser style={{ width: rem(16), height: rem(16) }} />}
          component={Link}
          href="/profile"
        >
          My Profile
        </Menu.Item>

        <Menu.Item
          leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
          component={Link}
          href="/settings"
        >
          Settings
        </Menu.Item>

        <Menu.Item
          leftSection={<IconMessage style={{ width: rem(16), height: rem(16) }} />}
          component={Link}
          href="/messages"
        >
          Messages
        </Menu.Item>

        <Menu.Divider />

        <Menu.Item
          leftSection={<IconSearch size={14} />}
          rightSection={
            <Text size="xs" c="dimmed">
              ⌘K
            </Text>
          }
        >
          Search
        </Menu.Item>

        <Menu.Item
          leftSection={<IconTrash style={{ width: rem(16), height: rem(16) }} color="red" />}
          color="red"
          component={Link}
          href="/delete-account"
        >
          Delete Account
        </Menu.Item>

        <Menu.Item
          leftSection={<IconLogout style={{ width: rem(16), height: rem(16) }} />}
          onClick={handleLogout}
        >
          Logout
        </Menu.Item>

        <Menu.Divider />


      </Menu.Dropdown>
    </Menu>
  ) : null}
  </>
  );
};

export default UserActive;
