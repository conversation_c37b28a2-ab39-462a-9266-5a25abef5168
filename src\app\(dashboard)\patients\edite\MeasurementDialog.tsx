import {
  <PERSON>ton,
  Group,
  Modal,
  NumberInput,
  Stack,
  Textarea,
 
} from '@mantine/core';
import { DateInput, TimeInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { IconPlus } from '@tabler/icons-react';

interface MeasurementValues {
  poids?: number;
  taille?: number;
  imc?: number;
  pouls?: number;
  temperature?: number;
  taSys?: number;
  taDia?: number;
  so2?: number;
  comment?: string;
  date?: Date;
  time?: Date;
}

interface MeasurementDialogProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (values: MeasurementValues) => void;
}

export function MeasurementDialog({
  opened,
  onClose,
  onSubmit,
}: MeasurementDialogProps) {
  const form = useForm<MeasurementValues>({
    initialValues: {
      date: new Date(),
      time: new Date(),
    },
  });

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconPlus size={20} />
          <span>Ajouter biométrie</span>
        </Group>
      }
      centered
      size="lg"
    >
      <form onSubmit={form.onSubmit(onSubmit)}>
        <Group grow>
          <DateInput
            label="Date des biométries"
            value={form.values.date}
            onChange={(date) => form.setFieldValue('date', date)}
          />
          <TimeInput
            label="Heure"
            value={form.values.time}
            onChange={(time) => form.setFieldValue('time', time)}
          />
        </Group>

        <Stack mt="md">
          <NumberInput
            label="Poids (Kg)"
            precision={2}
            {...form.getInputProps('poids')}
          />
          <NumberInput
            label="Taille (cm)"
            precision={2}
            {...form.getInputProps('taille')}
          />
          <NumberInput
            label="IMC (Kg/m²)"
            disabled
            value={form.values.imc}
          />
          <NumberInput
            label="Pouls (/min)"
            precision={0}
            {...form.getInputProps('pouls')}
          />
          <NumberInput
            label="T° (C°)"
            precision={2}
            {...form.getInputProps('temperature')}
          />
          <NumberInput
            label="T.A SYS (mmHg)"
            precision={2}
            {...form.getInputProps('taSys')}
          />
          <NumberInput
            label="T.A DIA (mmHg)"
            precision={2}
            {...form.getInputProps('taDia')}
          />
          <NumberInput
            label="SO2 (%)"
            precision={0}
            {...form.getInputProps('so2')}
          />
          <Textarea
            label="Commentaire"
            autosize
            minRows={2}
            {...form.getInputProps('comment')}
          />
        </Stack>

        <Group justify="flex-end" mt="xl">
          <Button type="submit" variant="filled">
            Enregistrer
          </Button>
          <Button variant="light" color="red" onClick={onClose}>
            Annuler
          </Button>
        </Group>
      </form>
    </Modal>
  );
}
