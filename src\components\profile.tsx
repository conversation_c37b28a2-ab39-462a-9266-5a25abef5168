'use client';

import React, { useState, useEffect } from 'react';
import {

  Title,
  Text,
  Button,
  Group,
  Paper,
  Stack,
  Loader,
  Divider,
  TextInput,
  Textarea,
  Select,
  FileInput,
  Grid,
  Tabs,
  PasswordInput,
  Card,
  Badge,
  Switch,
  Image,
  SimpleGrid,
} from '@mantine/core';
import { useAuth } from '@/hooks/useAuth';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconAlertCircle,
  IconCheck,
  IconUser,
  IconUpload,
  IconLock,
  IconBriefcase,
  IconCertificate,
  IconBuildingHospital,
  IconPhone,
  IconMail,
  IconMapPin,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandLinkedin,
  IconBrandYoutube,
  IconBrandTelegram,
  IconBrandWhatsapp,
  IconPhoto,
  // IconBrandInstagram,
} from '@tabler/icons-react';
import { UserProfile } from '@/services/authService';
import profileService from '@/services/profileService';
import {  extractErrorMessage, logError } from '@/utils/errorUtils';
export class ApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'ApiError';
  }
}
import AvatarWithUpload from '@/components/common/AvatarWithUpload';
import { PersonalFormValues, ProfessionalFormValues, PasswordFormValues, SocialMediaFormValues } from '@/types/profile';

export default function ProfilePage() {
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [activeTab, setActiveTab] = useState<string | null>('personal');
  const { refreshUser } = useAuth();
  // const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  const socialMediaForm = useForm<SocialMediaFormValues>({
    initialValues: {
      facebook_url: '',
      twitter_url: '',
      linkedin_url: '',
      youtube_url: '',
      telegram_url: '',
      whatsapp_number: '',
      clinic_photo_1: null,
      clinic_photo_2: null,
      clinic_photo_3: null,
    },
    validate: {
      facebook_url: (value) => (!value || value.includes('facebook.com') || value === '' ? null : 'Invalid Facebook URL'),
      twitter_url: (value) => (!value || value.includes('twitter.com') || value.includes('x.com') || value === '' ? null : 'Invalid Twitter URL'),
      linkedin_url: (value) => (!value || value.includes('linkedin.com') || value === '' ? null : 'Invalid LinkedIn URL'),
      youtube_url: (value) => (!value || value.includes('youtube.com') || value === '' ? null : 'Invalid YouTube URL'),
      whatsapp_number: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) || value === '' ? null : 'Invalid WhatsApp number'),
    },
  });

  const personalForm = useForm<PersonalFormValues>({
    initialValues: {
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      landline: '',
      address: '',
      country: '',
      region: '',
      city: '',
      bio: '',
      specialization: '',
      profile_image: null,
      profile_image_medium: null,
      profile_image_large: null,
    },
    validate: {
      first_name: (value) => (value.trim().length < 2 ? 'First name must be at least 2 characters' : null),
      last_name: (value) => (value.trim().length < 2 ? 'Last name must be at least 2 characters' : null),
      email: (value) => (/^\S+@\S+\.\S+$/.test(value) ? null : 'Invalid email address'),
      phone: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid phone number'),
      landline: (value) => (!value || /^[0-9()\-\s+]+$/.test(value) ? null : 'Invalid landline number'),
      specialization: (value) => (!value ? 'Specialization is required' : null),
    },
  });

  const passwordForm = useForm<PasswordFormValues>({
    initialValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
    validate: {
      current_password: (value) => (!value ? 'Current password is required' : null),
      new_password: (value) => (value.length < 8 ? 'Password must be at least 8 characters' : null),
      confirm_password: (value, values) => (value !== values.new_password ? 'Passwords do not match' : null),
    },
  });

  const professionalForm = useForm<ProfessionalFormValues>({
    initialValues: {
      license_number: '',
      years_of_experience: '',
      education: '',
      certifications: '',
      hospital_affiliations: '',
      languages: '',
      consultation_fee: '',
      accepting_new_patients: true,
    },
  });

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);

        // Fetch current doctor profile using the profile service
        console.log('Fetching doctor profile...');
        const doctorProfile = await profileService.getProfile();
        console.log('Doctor profile received:', doctorProfile);

        if (!doctorProfile) {
          throw new Error('No profile data received');
        }

        setProfile(doctorProfile);

        // Set form values
        personalForm.setValues({
          first_name: doctorProfile.first_name || '',
          last_name: doctorProfile.last_name || '',
          email: doctorProfile.email || '',
          phone: doctorProfile.phone_number || '',
          landline: doctorProfile.landline_number || '',
          address: doctorProfile.address || '',
          country: doctorProfile.country || doctorProfile.country_name || '',
          region: doctorProfile.region || doctorProfile.region_name || '',
          city: doctorProfile.city || doctorProfile.city_name || '',
          bio: doctorProfile.bio || '',
          specialization: doctorProfile.specialization || '',
          profile_image: null,
          profile_image_medium: null,
          profile_image_large: null,
        });

        professionalForm.setValues({
          license_number: doctorProfile.license_number || '',
          years_of_experience: doctorProfile.years_of_experience?.toString() || '',
          education: doctorProfile.education || '',
          certifications: doctorProfile.certifications || '',
          hospital_affiliations: doctorProfile.hospital_affiliations || '',
          languages: doctorProfile.languages || '',
          consultation_fee: doctorProfile.consultation_fee?.toString() || '',
          accepting_new_patients: doctorProfile.accepting_new_patients !== false,
        });

        // Set social media form values
        socialMediaForm.setValues({
          facebook_url: doctorProfile.facebook_url || '',
          twitter_url: doctorProfile.twitter_url || '',
          linkedin_url: doctorProfile.linkedin_url || '',
          youtube_url: doctorProfile.youtube_url || '',
          telegram_url: doctorProfile.telegram_url || '',
          whatsapp_number: doctorProfile.whatsapp_number || '',
          clinic_photo_1: null,
          clinic_photo_2: null,
          clinic_photo_3: null,
        });

        console.log('Profile forms initialized successfully');
      } catch (error: unknown) {
        // Log the error with detailed information
        logError('fetching profile', error as ApiError);

        // Extract the error message
        const errorMessage = extractErrorMessage(error as ApiError, 'Failed to load profile data. Please try again.');

        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });

        // If in development mode, use mock data
        if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
          console.log('Using mock profile data in development mode');
          const mockProfile = {
            id: '1',
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Smith',
            user_type: 'doctor',
            specialization: 'Cardiology',
            license_number: 'DOC-12345',
            bio: 'Experienced cardiologist with over 10 years of practice.',
            years_of_experience: 10,
            education: 'MD from Harvard Medical School',
            certifications: 'Board Certified in Cardiology',
            hospital_affiliations: 'City General Hospital',
            languages: 'English, Spanish',
            consultation_fee: 150,
            accepting_new_patients: true,
            phone_number: '+****************',
            address: '123 Medical Center Dr',
            country: 'United States',
            region: 'California',
            city: 'Los Angeles'
          };

          setProfile(mockProfile);

          // Set form values with mock data
          personalForm.setValues({
            first_name: mockProfile.first_name,
            last_name: mockProfile.last_name,
            email: mockProfile.email,
            phone: mockProfile.phone_number,
            landline: '',
            address: mockProfile.address,
            country: mockProfile.country,
            region: mockProfile.region,
            city: mockProfile.city,
            bio: mockProfile.bio,
            specialization: mockProfile.specialization,
            profile_image: null,
            profile_image_medium: null,
            profile_image_large: null,
          });

          professionalForm.setValues({
            license_number: mockProfile.license_number,
            years_of_experience: mockProfile.years_of_experience.toString(),
            education: mockProfile.education,
            certifications: mockProfile.certifications,
            hospital_affiliations: mockProfile.hospital_affiliations,
            languages: mockProfile.languages,
            consultation_fee: mockProfile.consultation_fee.toString(),
            accepting_new_patients: mockProfile.accepting_new_patients,
          });
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);



  const handleUpdatePersonalInfo = async (values: typeof personalForm.values) => {
    try {
      setSubmitting(true);

      // Check if we have any image files
      const hasImageFiles = values.profile_image || values.profile_image_medium || values.profile_image_large;

      // Prepare the data for the API call
      const updateData: Partial<PersonalFormValues> = {
        first_name: values.first_name,
        last_name: values.last_name,
        email: values.email,
        phone_number: values.phone,
        landline_number: values.landline,
        address: values.address,
        country: values.country,
        region: values.region,
        city: values.city,
        bio: values.bio,
        specialization: values.specialization,
      };

      // Add image files if they exist
      if (values.profile_image) {
        updateData.profile_image = values.profile_image;
      }
      if (values.profile_image_medium) {
        updateData.profile_image_medium = values.profile_image_medium;
      }
      if (values.profile_image_large) {
        updateData.profile_image_large = values.profile_image_large;
      }

      // Log the update data for debugging
      console.log('Updating profile with data:', {
        ...updateData,
        profile_image: values.profile_image ? 'File present' : 'No file',
        profile_image_medium: values.profile_image_medium ? 'File present' : 'No file',
        profile_image_large: values.profile_image_large ? 'File present' : 'No file',
      });

      // Call the API to update the profile
      const updatedProfile = await profileService.updateProfile(updateData);

      // Update the local state with the response from the server
      setProfile(updatedProfile);

      // Reset the file inputs after successful upload
      personalForm.setFieldValue('profile_image', null);
      personalForm.setFieldValue('profile_image_medium', null);
      personalForm.setFieldValue('profile_image_large', null);

      // Show success notification
      notifications.show({
        title: 'Success',
        message: hasImageFiles
          ? 'Personal information and profile images updated successfully'
          : 'Personal information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error: unknown) {
      // Log the error with detailed information
      logError('updating personal info', error as ApiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(error as ApiError, 'Failed to update personal information. Please try again.');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdatePassword = async (values: typeof passwordForm.values) => {
    try {
      setSubmitting(true);

      // Call the API to update the password
      await profileService.updatePassword(
        values.current_password,
        values.new_password
      );

      notifications.show({
        title: 'Success',
        message: 'Password updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      passwordForm.reset();
    } catch (error: unknown) {
      // Log the error with detailed information
      logError('updating password', error as ApiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(error as ApiError, 'Failed to update password. Please try again.');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateProfessionalInfo = async (values: typeof professionalForm.values) => {
    try {
      setSubmitting(true);

      // Prepare the data for the API call
      const updateData = {
        license_number: values.license_number,
        years_of_experience: parseInt(values.years_of_experience) || 0,
        education: values.education,
        certifications: values.certifications,
        hospital_affiliations: values.hospital_affiliations,
        languages: values.languages,
        consultation_fee: parseFloat(values.consultation_fee) || 0,
        accepting_new_patients: values.accepting_new_patients,
      };

      // Call the API to update the profile
      const updatedProfile = await profileService.updateProfile(updateData);

      // Update the local state with the response from the server
      setProfile(updatedProfile);

      notifications.show({
        title: 'Success',
        message: 'Professional information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error: unknown) {
      // Log the error with detailed information
      logError('updating professional info', error as ApiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(error as ApiError, 'Failed to update professional information. Please try again.');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateSocialMedia = async (values: typeof socialMediaForm.values) => {
    try {
      setSubmitting(true);

      // Check if we have any image files
      const hasImageFiles = values.clinic_photo_1 instanceof File ||
                           values.clinic_photo_2 instanceof File ||
                           values.clinic_photo_3 instanceof File;

      // Prepare the data for the API call
      const updateData: Record<string, string | File | null> = {
        facebook_url: values.facebook_url,
        twitter_url: values.twitter_url,
        linkedin_url: values.linkedin_url,
        youtube_url: values.youtube_url,
        telegram_url: values.telegram_url,
        whatsapp_number: values.whatsapp_number,
      };

      // Add image files if they exist
      if (values.clinic_photo_1 instanceof File) {
        updateData.clinic_photo_1 = values.clinic_photo_1;
      }
      if (values.clinic_photo_2 instanceof File) {
        updateData.clinic_photo_2 = values.clinic_photo_2;
      }
      if (values.clinic_photo_3 instanceof File) {
        updateData.clinic_photo_3 = values.clinic_photo_3;
      }

      // Log the update data for debugging
      console.log('Updating social media with data:', {
        ...updateData,
        clinic_photo_1: values.clinic_photo_1 instanceof File ? 'File present' : 'No file',
        clinic_photo_2: values.clinic_photo_2 instanceof File ? 'File present' : 'No file',
        clinic_photo_3: values.clinic_photo_3 instanceof File ? 'File present' : 'No file',
      });

      // Call the API to update the profile
      const updatedProfile = await profileService.updateProfile(updateData);

      // Update the local state with the response from the server
      setProfile(updatedProfile);

      // Reset the file inputs after successful upload
      socialMediaForm.setFieldValue('clinic_photo_1', null);
      socialMediaForm.setFieldValue('clinic_photo_2', null);
      socialMediaForm.setFieldValue('clinic_photo_3', null);

      // Show success notification
      notifications.show({
        title: 'Success',
        message: hasImageFiles
          ? 'Social media information and clinic photos updated successfully'
          : 'Social media information updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error: unknown) {
      // Log the error with detailed information
      logError('updating social media info', error as ApiError);

      // Extract the error message
      const errorMessage = extractErrorMessage(error as ApiError, 'Failed to update social media information. Please try again.');

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
   
        <Paper shadow="xs" p="xl" withBorder w={"100%"}>
          <Stack align="center" gap="md">
            <Loader />
            <Text>Loading profile...</Text>
          </Stack>
        </Paper>
 
    );
  }

  return (


      <Paper shadow="xs" p="md" withBorder mb={60} w={"100%"}>
        <Grid gutter="md">
          <Grid.Col span={4}>
            <Card shadow="sm" p="lg" radius="md" withBorder>
              <Stack align="center" gap="md">
                <AvatarWithUpload
                  size={120}
                  radius={120}
                  color="blue"
                  src={profile?.profile_image_medium
                    ? `${profile.profile_image_medium}?t=${Date.now()}`
                    : profile?.profile_image
                      ? `${profile.profile_image}?t=${Date.now()}`
                      : undefined}
                  initials={`${profile?.first_name?.charAt(0) || ''}${profile?.last_name?.charAt(0) || ''}`}
                  onImageUpload={async (file) => {
                    try {
                      const formData = new FormData();
                      formData.append('profile_image', file);

                      // Show loading notification
                      notifications.show({
                        id: 'uploading-image',
                        title: 'Uploading image',
                        message: 'Please wait while your image is being uploaded...',
                        loading: true,
                        autoClose: false,
                      });

                      const updatedProfile = await profileService.updateProfileImage(formData);
                      setProfile(updatedProfile);

                      // Refresh user data in the AuthContext to update all instances of the avatar
                      await refreshUser();

                      // Show success notification
                      notifications.update({
                        id: 'uploading-image',
                        title: 'Success',
                        message: 'Profile image updated successfully',
                        color: 'green',
                        icon: <IconCheck size={16} />,
                        autoClose: 3000,
                      });

                      console.log('Profile updated with new image:', updatedProfile);
                    } catch (error: unknown) {
                      // Log the error with detailed information
                      logError('uploading profile image', error as ApiError);

                      // Extract the error message
                      const errorMessage = extractErrorMessage(error as ApiError, 'Failed to upload profile image. Please try again.');

                      // Update notification to show error
                      notifications.update({
                        id: 'uploading-image',
                        title: 'Error',
                        message: errorMessage,
                        color: 'red',
                        icon: <IconAlertCircle size={16} />,
                        autoClose: 3000,
                      });
                    }
                  }}
                />

                <Title order={3}>Dr. {profile?.first_name} {profile?.last_name}</Title>

                <Badge size="lg" color="blue">
                  {profile?.specialization || 'Doctor'}
                </Badge>

                <Divider style={{ width: '100%' }} />

                <Stack gap="xs" style={{ width: '100%' }}>
                  <Group gap="xs">
                    <IconMail size={16} />
                    <Text size="sm">{profile?.email}</Text>
                  </Group>

                  {profile?.phone_number && (
                    <Group gap="xs">
                      <IconPhone size={16} />
                      <Text size="sm">{profile?.phone_number} (Mobile)</Text>
                    </Group>
                  )}

                  {profile?.landline_number && (
                    <Group gap="xs">
                      <IconPhone size={16} />
                      <Text size="sm">{profile?.landline_number} (Landline)</Text>
                    </Group>
                  )}

                  {profile?.address && (
                    <Group gap="xs" align="flex-start">
                      <IconMapPin size={16} style={{ marginTop: 4 }} />
                      <Text size="sm">{profile?.address}</Text>
                    </Group>
                  )}

                  {(profile?.city_name || profile?.region_name || profile?.country_name) && (
                    <Group gap="xs" align="flex-start">
                      <IconMapPin size={16} style={{ marginTop: 4 }} />
                      <Text size="sm">
                        {[
                          profile?.city_name,
                          profile?.region_name,
                          profile?.country_name
                        ].filter(Boolean).join(', ')}
                      </Text>
                    </Group>
                  )}
                </Stack>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={8}>
            <Tabs value={activeTab} onChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Tab value="personal" leftSection={<IconUser size={14} />}>
                  Personal Information
                </Tabs.Tab>
                <Tabs.Tab value="professional" leftSection={<IconBriefcase size={14} />}>
                  Professional Details
                </Tabs.Tab>
                <Tabs.Tab value="social" leftSection={<IconBrandFacebook size={14} />}>
                  Social Media & Clinic
                </Tabs.Tab>
                <Tabs.Tab value="security" leftSection={<IconLock size={14} />}>
                  Security
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="personal" pt="xs">
                <form onSubmit={personalForm.onSubmit(handleUpdatePersonalInfo)}>
                  <Stack gap="md" mt="md">
                    <Group grow>
                      <TextInput
                        required
                        label="First Name"
                        placeholder="Your first name"
                        {...personalForm.getInputProps('first_name')}
                      />

                      <TextInput
                        required
                        label="Last Name"
                        placeholder="Your last name"
                        {...personalForm.getInputProps('last_name')}
                      />
                    </Group>

                    <Group grow>
                      <TextInput
                        required
                        label="Email"
                        placeholder="<EMAIL>"
                        {...personalForm.getInputProps('email')}
                      />

                      <TextInput
                        label="Mobile Phone"
                        placeholder="Your mobile phone number"
                        {...personalForm.getInputProps('phone')}
                      />
                    </Group>

                    <TextInput
                      label="Landline Number"
                      placeholder="Your landline phone number"
                      {...personalForm.getInputProps('landline')}
                    />

                    <TextInput
                      label="Address"
                      placeholder="Your address"
                      {...personalForm.getInputProps('address')}
                    />

                    <Group grow>
                      <TextInput
                        label="Country"
                        placeholder="Your country"
                        {...personalForm.getInputProps('country')}
                      />

                      <TextInput
                        label="Region/State"
                        placeholder="Your region or state"
                        {...personalForm.getInputProps('region')}
                      />
                    </Group>

                    <TextInput
                      label="City"
                      placeholder="Your city"
                      {...personalForm.getInputProps('city')}
                    />

                    <Select
                      required
                      label="Specialization"
                      placeholder="Select your specialization"
                      data={[
                        { value: 'Cardiology', label: 'Cardiology' },
                        { value: 'Dermatology', label: 'Dermatology' },
                        { value: 'Endocrinology', label: 'Endocrinology' },
                        { value: 'Gastroenterology', label: 'Gastroenterology' },
                        { value: 'General Practice', label: 'General Practice' },
                        { value: 'Neurology', label: 'Neurology' },
                        { value: 'Obstetrics & Gynecology', label: 'Obstetrics & Gynecology' },
                        { value: 'Oncology', label: 'Oncology' },
                        { value: 'Ophthalmology', label: 'Ophthalmology' },
                        { value: 'Orthopedics', label: 'Orthopedics' },
                        { value: 'Pediatrics', label: 'Pediatrics' },
                        { value: 'Psychiatry', label: 'Psychiatry' },
                        { value: 'Radiology', label: 'Radiology' },
                        { value: 'Urology', label: 'Urology' },
                      ]}
                      {...personalForm.getInputProps('specialization')}
                    />

                    <Textarea
                      label="Bio"
                      placeholder="A brief description about yourself and your practice"
                      minRows={4}
                      {...personalForm.getInputProps('bio')}
                    />

                    <Title order={5} mt="md">Profile Images</Title>
                    <Text size="sm" c="dimmed" mb="xs">
                      Upload different sizes of your profile image for various displays throughout the application
                    </Text>

                    <FileInput
                      label="Profile Image (Thumbnail)"
                      description="Small image used in navigation and comments"
                      placeholder="Upload a thumbnail profile image"
                      accept="image/png,image/jpeg"
                      leftSection={<IconUpload size={14} />}
                      {...personalForm.getInputProps('profile_image')}
                    />

                    <FileInput
                      label="Profile Image (Medium)"
                      description="Medium-sized image used on home page and listings"
                      placeholder="Upload a medium profile image"
                      accept="image/png,image/jpeg"
                      leftSection={<IconUpload size={14} />}
                      {...personalForm.getInputProps('profile_image_medium')}
                    />

                    <FileInput
                      label="Profile Image (Large)"
                      description="Large image used on your doctor detail page"
                      placeholder="Upload a large profile image"
                      accept="image/png,image/jpeg"
                      leftSection={<IconUpload size={14} />}
                      {...personalForm.getInputProps('profile_image_large')}
                    />

                    <Group justify="flex-start" mt="md">
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="professional" pt="xs">
                <form onSubmit={professionalForm.onSubmit(handleUpdateProfessionalInfo)}>
                  <Stack gap="md" mt="md">
                    <Group grow>
                      <TextInput
                        label="License Number"
                        placeholder="Your medical license number"
                        leftSection={<IconCertificate size={14} />}
                        {...professionalForm.getInputProps('license_number')}
                      />

                      <TextInput
                        label="Years of Experience"
                        placeholder="e.g., 10"
                        type="number"
                        {...professionalForm.getInputProps('years_of_experience')}
                      />
                    </Group>

                    <Textarea
                      label="Education"
                      placeholder="Your educational background"
                      minRows={2}
                      {...professionalForm.getInputProps('education')}
                    />

                    <Textarea
                      label="Certifications"
                      placeholder="Your professional certifications"
                      minRows={2}
                      {...professionalForm.getInputProps('certifications')}
                    />

                    <TextInput
                      label="Hospital Affiliations"
                      placeholder="Hospitals you're affiliated with"
                      leftSection={<IconBuildingHospital size={14} />}
                      {...professionalForm.getInputProps('hospital_affiliations')}
                    />

                    <TextInput
                      label="Languages Spoken"
                      placeholder="e.g., English, Spanish"
                      {...professionalForm.getInputProps('languages')}
                    />

                    <Group grow>
                      <TextInput
                        label="Consultation Fee"
                        placeholder="e.g., 100"
                        type="number"
                        {...professionalForm.getInputProps('consultation_fee')}
                      />

                      <div style={{ display: 'flex', alignItems: 'flex-end', height: '100%' }}>
                        <Switch
                          label="Accepting New Patients"
                          checked={professionalForm.values.accepting_new_patients}
                          onChange={(event) => professionalForm.setFieldValue('accepting_new_patients', event.currentTarget.checked)}
                        />
                      </div>
                    </Group>

                    <Group justify="flex-end" mt="md">
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="social" pt="xs">
                <form onSubmit={socialMediaForm.onSubmit(handleUpdateSocialMedia)}>
                  <Stack gap="md" mt="md">
                    <Title order={5}>Social Media Profiles</Title>
                    <Text size="sm" c="dimmed" mb="xs">
                      Add your social media profiles to help patients connect with you
                    </Text>

                    <Group grow>
                      <TextInput
                        label="Facebook"
                        placeholder="https://facebook.com/yourusername"
                        leftSection={<IconBrandFacebook size={14} color="#1877F2" />}
                        {...socialMediaForm.getInputProps('facebook_url')}
                      />

                      <TextInput
                        label="Twitter / X"
                        placeholder="https://twitter.com/yourusername"
                        leftSection={<IconBrandTwitter size={14} color="#1DA1F2" />}
                        {...socialMediaForm.getInputProps('twitter_url')}
                      />
                    </Group>

                    <Group grow>
                      <TextInput
                        label="LinkedIn"
                        placeholder="https://linkedin.com/in/yourusername"
                        leftSection={<IconBrandLinkedin size={14} color="#0A66C2" />}
                        {...socialMediaForm.getInputProps('linkedin_url')}
                      />

                      <TextInput
                        label="YouTube"
                        placeholder="https://youtube.com/c/yourchannel"
                        leftSection={<IconBrandYoutube size={14} color="#FF0000" />}
                        {...socialMediaForm.getInputProps('youtube_url')}
                      />
                    </Group>

                    <Group grow>
                      <TextInput
                        label="Telegram"
                        placeholder="https://t.me/yourusername"
                        leftSection={<IconBrandTelegram size={14} color="#0088CC" />}
                        {...socialMediaForm.getInputProps('telegram_url')}
                      />

                      <TextInput
                        label="WhatsApp Number"
                        placeholder="+**********"
                        leftSection={<IconBrandWhatsapp size={14} color="#25D366" />}
                        {...socialMediaForm.getInputProps('whatsapp_number')}
                      />
                    </Group>

                    <Divider my="md" />

                    <Title order={5}>Clinic Photos</Title>
                    <Text size="sm" c="dimmed" mb="xs">
                      Upload photos of your clinic to help patients get familiar with your practice
                    </Text>

                    {/* Display existing clinic photos if available */}
                    {(profile?.clinic_photo_1 || profile?.clinic_photo_2 || profile?.clinic_photo_3) && (
                      <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md" mb="md">
                        {profile?.clinic_photo_1 && (
                          <Card shadow="sm" padding="xs" radius="md" withBorder>
                            <Card.Section>
                              <Image
                                src={`${profile.clinic_photo_1}?t=${Date.now()}`}
                                height={160}
                                alt="Clinic photo 1"
                              />
                            </Card.Section>
                            <Text size="sm" mt="xs">Clinic Photo 1</Text>
                          </Card>
                        )}

                        {profile?.clinic_photo_2 && (
                          <Card shadow="sm" padding="xs" radius="md" withBorder>
                            <Card.Section>
                              <Image
                                src={`${profile.clinic_photo_2}?t=${Date.now()}`}
                                height={160}
                                alt="Clinic photo 2"
                              />
                            </Card.Section>
                            <Text size="sm" mt="xs">Clinic Photo 2</Text>
                          </Card>
                        )}

                        {profile?.clinic_photo_3 && (
                          <Card shadow="sm" padding="xs" radius="md" withBorder>
                            <Card.Section>
                              <Image
                                src={`${profile.clinic_photo_3}?t=${Date.now()}`}
                                height={160}
                                alt="Clinic photo 3"
                              />
                            </Card.Section>
                            <Text size="sm" mt="xs">Clinic Photo 3</Text>
                          </Card>
                        )}
                      </SimpleGrid>
                    )}

                    <FileInput
                      label="Clinic Photo 1"
                      description="Upload a photo of your clinic reception or waiting area"
                      placeholder="Upload clinic photo 1"
                      accept="image/png,image/jpeg"
                      leftSection={<IconPhoto size={14} />}
                      {...socialMediaForm.getInputProps('clinic_photo_1')}
                    />

                    <FileInput
                      label="Clinic Photo 2"
                      description="Upload a photo of your consultation room"
                      placeholder="Upload clinic photo 2"
                      accept="image/png,image/jpeg"
                      leftSection={<IconPhoto size={14} />}
                      {...socialMediaForm.getInputProps('clinic_photo_2')}
                    />

                    <FileInput
                      label="Clinic Photo 3"
                      description="Upload a photo of your clinic exterior or additional facilities"
                      placeholder="Upload clinic photo 3"
                      accept="image/png,image/jpeg"
                      leftSection={<IconPhoto size={14} />}
                      {...socialMediaForm.getInputProps('clinic_photo_3')}
                    />

                    <Group justify="flex-start" mt="md">
                      <Button type="submit" loading={submitting}>
                        Save Changes
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>

              <Tabs.Panel value="security" pt="xs">
                <form onSubmit={passwordForm.onSubmit(handleUpdatePassword)}>
                  <Stack gap="md" mt="md">
                    <PasswordInput
                      required
                      label="Current Password"
                      placeholder="Your current password"
                      {...passwordForm.getInputProps('current_password')}
                    />

                    <PasswordInput
                      required
                      label="New Password"
                      placeholder="Your new password"
                      {...passwordForm.getInputProps('new_password')}
                    />

                    <PasswordInput
                      required
                      label="Confirm New Password"
                      placeholder="Confirm your new password"
                      {...passwordForm.getInputProps('confirm_password')}
                    />

                    <Group justify="flex-end" mt="md">
                      <Button type="submit" loading={submitting}>
                        Change Password
                      </Button>
                    </Group>
                  </Stack>
                </form>
              </Tabs.Panel>
            </Tabs>
          </Grid.Col>
        </Grid>
      </Paper>






  );
}
