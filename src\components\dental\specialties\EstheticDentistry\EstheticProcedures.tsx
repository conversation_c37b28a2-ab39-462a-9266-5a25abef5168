// frontend/dental_medicine/src/components/content/dental/specialties/EstheticDentistry/EstheticProcedures.tsx

import React, { useState, useMemo } from 'react';
import { Table, Checkbox, Text, Badge, Button, Group, TextInput, Select } from '@mantine/core';
import { IconSearch, IconFilter } from '@tabler/icons-react';
import { EstheticProcedure, ModificationState , SVGPathStyle} from '../../shared/types';

interface EstheticProceduresProps {
  type: 'all' | 'planned' | 'completed';
  modificationState?: ModificationState; // Optional since not currently used
  onModificationChange?: (
    svgId: string,
    pathId: string,
    isVisible: boolean,
    highlightedPaths?: Record<string, SVGPathStyle>
  ) => Promise<void>;
}

export const EstheticProcedures: React.FC<EstheticProceduresProps> = ({
  type,
  onModificationChange
}) => {

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Données des procédures esthétiques
  const allEstheticProcedures: EstheticProcedure[] = [
    {
      position: 1,
      mass: 1.008,
      symbol: 'BL1',
      name: 'Blanchiment Niveau 1',
      type: 'whitening',
      pathId: '20',
      cost: 150,
      duration: 60,
      category: 'blanchiment',
      shadeFrom: 'A3',
      shadeTo: 'A2'
    },
    {
      position: 2,
      mass: 4.003,
      symbol: 'BL2',
      name: 'Blanchiment Niveau 2',
      type: 'whitening',
      pathId: '21',
      cost: 250,
      duration: 90,
      category: 'blanchiment',
      shadeFrom: 'A3',
      shadeTo: 'A1'
    },
    {
      position: 3,
      mass: 6.941,
      symbol: 'FAC',
      name: 'Facettes Céramique',
      type: 'veneer',
      pathId: '22',
      cost: 800,
      duration: 120,
      category: 'facettes',
      shadeFrom: 'A3',
      shadeTo: 'A1'
    },
    {
      position: 4,
      mass: 9.012,
      symbol: 'COL',
      name: 'Collage Composite',
      type: 'bonding',
      pathId: '23',
      cost: 200,
      duration: 45,
      category: 'collage'
    },
    {
      position: 5,
      mass: 10.811,
      symbol: 'DES',
      name: 'Design du Sourire',
      type: 'smile_design',
      pathId: '24',
      cost: 2500,
      duration: 180,
      category: 'design'
    },
    {
      position: 6,
      mass: 12.011,
      symbol: 'CES',
      name: 'Couronne Esthétique',
      type: 'crown_aesthetic',
      pathId: '25',
      cost: 900,
      duration: 90,
      category: 'couronne'
    }
  ];

  // Filtrer les procédures selon le type
  const filteredProcedures = useMemo(() => {
    let procedures = allEstheticProcedures;

    // Filtrer par type (all, planned, completed)
    if (type === 'planned') {
      procedures = procedures.filter(proc => proc.position <= 3); // Exemple: les 3 premières sont planifiées
    } else if (type === 'completed') {
      procedures = procedures.filter(proc => proc.position > 3); // Exemple: les autres sont terminées
    }

    // Filtrer par terme de recherche
    if (searchTerm) {
      procedures = procedures.filter(proc =>
        proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (filterCategory !== 'all') {
      procedures = procedures.filter(proc => proc.category === filterCategory);
    }

    return procedures;
  }, [type, searchTerm, filterCategory]);

  // Gestionnaire de sélection de ligne
  const handleRowSelection = (position: number) => {
    setSelectedRows(prev =>
      prev.includes(position)
        ? prev.filter(p => p !== position)
        : [...prev, position]
    );
  };

  // Gestionnaire d'application de procédure
  const handleApplyProcedure = async (procedure: EstheticProcedure) => {
    if (onModificationChange && procedure.pathId) {
      try {
        await onModificationChange('1', procedure.pathId, true, {
          [`1-${procedure.pathId}`]: {
            fill: getColorForProcedure(procedure.type),
            stroke: '#3799CE',
            strokeWidth: 1
          }
        });
        console.log(`✅ Procédure esthétique appliquée: ${procedure.name}`);
      } catch (error) {
        console.error(`❌ Erreur application procédure: ${procedure.name}`, error);
      }
    }
  };

  // Obtenir la couleur selon le type de procédure
  const getColorForProcedure = (type: EstheticProcedure['type']): string => {
    switch (type) {
      case 'whitening': return '#ffffff';
      case 'veneer': return '#f8f9fa';
      case 'bonding': return '#e9ecef';
      case 'crown_aesthetic': return '#dee2e6';
      case 'smile_design': return '#ced4da';
      default: return '#adb5bd';
    }
  };

  // Obtenir le badge de statut
  const getStatusBadge = () => {
    if (type === 'completed') {
      return <Badge color="green" size="sm">Terminé</Badge>;
    } else if (type === 'planned') {
      return <Badge color="blue" size="sm">Planifié</Badge>;
    } else {
      return <Badge color="gray" size="sm">Disponible</Badge>;
    }
  };

  const rows = filteredProcedures.map((procedure) => (
    <Table.Tr
      key={procedure.position}
      bg={selectedRows.includes(procedure.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(procedure.position)}
          onChange={() => handleRowSelection(procedure.position)}
        />
      </Table.Td>
      <Table.Td>
        <Text fw={500} size="sm">{procedure.symbol}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.name}</Text>
      </Table.Td>
      <Table.Td>
        {getStatusBadge()}
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.cost}€</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.duration}min</Text>
      </Table.Td>
      <Table.Td>
        {procedure.shadeFrom && procedure.shadeTo && (
          <Text size="xs" c="dimmed">
            {procedure.shadeFrom} → {procedure.shadeTo}
          </Text>
        )}
      </Table.Td>
      <Table.Td>
        <Button
          size="xs"
          variant="light"
          color="blue"
          onClick={() => handleApplyProcedure(procedure)}
        >
          Appliquer
        </Button>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <div className="p-4">
      {/* Filtres et recherche */}
      <Group mb="md">
        <TextInput
          placeholder="Rechercher une procédure..."
          leftSection={<IconSearch size={16} />}
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.currentTarget.value)}
          style={{ flex: 1 }}
        />
        <Select
          placeholder="Catégorie"
          leftSection={<IconFilter size={16} />}
          data={[
            { value: 'all', label: 'Toutes' },
            { value: 'blanchiment', label: 'Blanchiment' },
            { value: 'facettes', label: 'Facettes' },
            { value: 'collage', label: 'Collage' },
            { value: 'design', label: 'Design' },
            { value: 'couronne', label: 'Couronne' }
          ]}
          value={filterCategory}
          onChange={(value) => setFilterCategory(value || 'all')}
          w={150}
        />
      </Group>

      {/* Statistiques */}
      <Group mb="md">
        <Text size="sm" c="dimmed">
          {filteredProcedures.length} procédure(s) • {selectedRows.length} sélectionnée(s)
        </Text>
        {selectedRows.length > 0 && (
          <Button size="xs" variant="light" color="blue">
            Appliquer la sélection
          </Button>
        )}
      </Group>

      {/* Tableau des procédures */}
      <Table striped highlightOnHover>
        <Table.Thead>
          <Table.Tr>
            <Table.Th />
            <Table.Th>Code</Table.Th>
            <Table.Th>Procédure</Table.Th>
            <Table.Th>Statut</Table.Th>
            <Table.Th>Coût</Table.Th>
            <Table.Th>Durée</Table.Th>
            <Table.Th>Teinte</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>

      {filteredProcedures.length === 0 && (
        <Text ta="center" c="dimmed" mt="xl">
          Aucune procédure trouvée
        </Text>
      )}
    </div>
  );
};

export default EstheticProcedures;
