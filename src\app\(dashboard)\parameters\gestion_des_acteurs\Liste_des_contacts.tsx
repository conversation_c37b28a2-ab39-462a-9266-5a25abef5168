import React, { useState, useEffect } from 'react';
import {
  <PERSON>ack,
  Title,
  Button,
  Table,
  Group,
  ActionIcon,
  Paper,
  Tooltip,
  Modal,
  Text,
  Tabs,
  TextInput,
  Select,
  Switch,
  Grid,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconUsers,
  IconUserCheck,
  IconUser,
} from '@tabler/icons-react';

// Types
interface Contact {
  id: string;
  fullName: string;
  initial: string;
  inp: string;
  type: 'medecin' | 'particulier';
}

// Mock data for contacts
const mockContacts: Contact[] = [
  {
    id: '1',
    fullName: 'ESSAIH AYOUB',
    initial: 'EA',
    inp: '',
    type: 'medecin',
  },
  {
    id: '2',
    fullName: 'Dr. MARTIN JEAN',
    initial: 'MJ',
    inp: '12345',
    type: 'medecin',
  },
  {
    id: '3',
    fullName: 'DUPONT MARIE',
    initial: 'DM',
    inp: '',
    type: 'particulier',
  },
  {
    id: '4',
    fullName: 'BERNARD PAUL',
    initial: 'BP',
    inp: '67890',
    type: 'particulier',
  },
];

const Liste_des_contacts = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [activeTab, setActiveTab] = useState('medecin');
  const [modalOpened, setModalOpened] = useState(false);
  const [newContact, setNewContact] = useState({
    isMedecin: true,
    titre: '',
    nom: '',
    prenom: '',
    initial: '',
    dateNaissance: '',
    inp: '',
    licence: false,
    specialitePrincipale: '',
    telephone: '',
    email: '',
    adresse: '',
  });

  useEffect(() => {
    setContacts(mockContacts);
  }, []);

  // Filter contacts based on active tab
  const filteredContacts = contacts.filter((contact: Contact) =>
    activeTab === 'medecin' ? contact.type === 'medecin' : contact.type === 'particulier'
  );

  // Reset form
  const resetForm = () => {
    setNewContact({
      isMedecin: true,
      titre: '',
      nom: '',
      prenom: '',
      initial: '',
      dateNaissance: '',
      inp: '',
      licence: false,
      specialitePrincipale: '',
      telephone: '',
      email: '',
      adresse: '',
    });
  };

  return (
    <Stack gap="lg" className="w-full">
      {/* Header */}
      <Paper p="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUsers size={24} className="text-blue-600" />
            <Title order={3} className="text-gray-700">
              Listes des contacts
            </Title>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="blue"
            size="sm"
            onClick={() => {
              resetForm();
              setModalOpened(true);
            }}
          >
            Nouveau contact
          </Button>
        </Group>
      </Paper>

      {/* Tabs */}
      <Paper withBorder>
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'medecin')}>
          <Tabs.List>
            <Tabs.Tab value="medecin" leftSection={<IconUserCheck size={16} />}>
              Médecin contact
            </Tabs.Tab>
            <Tabs.Tab value="particulier" leftSection={<IconUser size={16} />}>
              Particulier
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="medecin" pt="md">
            {/* Medecin Contacts Table */}
            <Table striped highlightOnHover withTableBorder>
              <Table.Thead>
                <Table.Tr className="bg-gray-50">
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Nom complet
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                    Initial
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                    INP
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center">
                    Actions
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredContacts.map((contact) => (
                  <Table.Tr key={contact.id} className="hover:bg-gray-50">
                    <Table.Td className="font-medium border-r border-gray-200">
                      {contact.fullName}
                    </Table.Td>
                    <Table.Td className="text-center border-r border-gray-200">
                      {contact.initial}
                    </Table.Td>
                    <Table.Td className="text-center border-r border-gray-200">
                      {contact.inp || '-'}
                    </Table.Td>
                    <Table.Td className="text-center">
                      <Group gap="xs" justify="center">
                        <Tooltip label="Modifier">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            size="sm"
                            onClick={() => {
                              // Handle edit action
                              console.log('Edit contact:', contact.id);
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Supprimer">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => {
                              // Handle delete action
                              console.log('Delete contact:', contact.id);
                            }}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Tabs.Panel>

          <Tabs.Panel value="particulier" pt="md">
            {/* Particulier Contacts Table */}
            <Table striped highlightOnHover withTableBorder>
              <Table.Thead>
                <Table.Tr className="bg-gray-50">
                  <Table.Th className="font-semibold text-gray-700 border-r border-gray-200">
                    Nom complet
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                    Initial
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center border-r border-gray-200">
                    INP
                  </Table.Th>
                  <Table.Th className="font-semibold text-gray-700 text-center">
                    Actions
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredContacts.map((contact) => (
                  <Table.Tr key={contact.id} className="hover:bg-gray-50">
                    <Table.Td className="font-medium border-r border-gray-200">
                      {contact.fullName}
                    </Table.Td>
                    <Table.Td className="text-center border-r border-gray-200">
                      {contact.initial}
                    </Table.Td>
                    <Table.Td className="text-center border-r border-gray-200">
                      {contact.inp || '-'}
                    </Table.Td>
                    <Table.Td className="text-center">
                      <Group gap="xs" justify="center">
                        <Tooltip label="Modifier">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            size="sm"
                            onClick={() => {
                              // Handle edit action
                              console.log('Edit contact:', contact.id);
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Supprimer">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={() => {
                              // Handle delete action
                              console.log('Delete contact:', contact.id);
                            }}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Modal for adding new contact */}
      <Modal
        opened={modalOpened}
        onClose={() => {
          setModalOpened(false);
          resetForm();
        }}
        title={
          <Group gap="sm" className="text-white">
            <IconUsers size={20} />
            <Text size="lg" fw={500} className="text-white">
              Contact
            </Text>
          </Group>
        }
        size="lg"
        centered
        styles={{
          header: {
            backgroundColor: '#3b82f6',
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
            padding: '16px 20px',
          },
          close: {
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          },
          content: {
            padding: 0,
          },
          body: {
            padding: '20px',
          },
        }}
      >
        <Stack gap="md">
          {/* Toggle Médecin */}
          <Group justify="space-between" align="center">
            <Text size="sm" fw={500}>
              Médecin
            </Text>
            <Switch
              checked={newContact.isMedecin}
              onChange={(event) =>
                setNewContact({ ...newContact, isMedecin: event.currentTarget.checked })
              }
              color="blue"
            />
          </Group>

          {/* Form Fields */}
          <Grid>
            <Grid.Col span={6}>
              <Select
                label="Titre"
                placeholder="Sélectionnez un titre"
                value={newContact.titre}
                onChange={(value) =>
                  setNewContact({ ...newContact, titre: value || '' })
                }
                data={[
                  { value: 'Dr', label: 'Dr' },
                  { value: 'Pr', label: 'Pr' },
                  { value: 'M', label: 'M' },
                  { value: 'Mme', label: 'Mme' },
                  { value: 'Mlle', label: 'Mlle' },
                ]}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="Nom"
                placeholder=""
                value={newContact.nom}
                onChange={(event) =>
                  setNewContact({ ...newContact, nom: event.currentTarget.value })
                }
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Prénom"
                placeholder=""
                value={newContact.prenom}
                onChange={(event) =>
                  setNewContact({ ...newContact, prenom: event.currentTarget.value })
                }
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="Initial"
                placeholder=""
                value={newContact.initial}
                onChange={(event) =>
                  setNewContact({ ...newContact, initial: event.currentTarget.value })
                }
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Date de naissance"
                placeholder=""
                type="date"
                value={newContact.dateNaissance}
                onChange={(event) =>
                  setNewContact({ ...newContact, dateNaissance: event.currentTarget.value })
                }
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="INP"
                placeholder=""
                value={newContact.inp}
                onChange={(event) =>
                  setNewContact({ ...newContact, inp: event.currentTarget.value })
                }
              />
            </Grid.Col>
          </Grid>

          {newContact.isMedecin && (
            <>
              <Group justify="space-between" align="center">
                <Text size="sm" fw={500}>
                  Licence
                </Text>
                <Switch
                  checked={newContact.licence}
                  onChange={(event) =>
                    setNewContact({ ...newContact, licence: event.currentTarget.checked })
                  }
                  color="blue"
                />
              </Group>

              <TextInput
                label="Spécialité principale"
                placeholder=""
                value={newContact.specialitePrincipale}
                onChange={(event) =>
                  setNewContact({ ...newContact, specialitePrincipale: event.currentTarget.value })
                }
              />
            </>
          )}

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Téléphone"
                placeholder=""
                value={newContact.telephone}
                onChange={(event) =>
                  setNewContact({ ...newContact, telephone: event.currentTarget.value })
                }
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="Email"
                placeholder=""
                type="email"
                value={newContact.email}
                onChange={(event) =>
                  setNewContact({ ...newContact, email: event.currentTarget.value })
                }
              />
            </Grid.Col>
          </Grid>

          <TextInput
            label="Adresse"
            placeholder=""
            value={newContact.adresse}
            onChange={(event) =>
              setNewContact({ ...newContact, adresse: event.currentTarget.value })
            }
          />

          <Group justify="flex-end" mt="xl" gap="sm">
            <Button
              variant="filled"
              color="gray"
              onClick={() => {
                setModalOpened(false);
                resetForm();
              }}
              styles={{
                root: {
                  backgroundColor: '#9ca3af',
                  '&:hover': {
                    backgroundColor: '#6b7280',
                  },
                },
              }}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={() => {
                // Add new contact to the list
                const newId = (contacts.length + 1).toString();
                const fullName = `${newContact.titre} ${newContact.nom} ${newContact.prenom}`.trim();
                const contact: Contact = {
                  id: newId,
                  fullName: fullName,
                  initial: newContact.initial,
                  inp: newContact.inp,
                  type: newContact.isMedecin ? 'medecin' : 'particulier'
                };
                setContacts([...contacts, contact]);

                // Reset form and close modal
                resetForm();
                setModalOpened(false);
              }}
              disabled={!newContact.nom || !newContact.prenom || !newContact.initial}
            >
              Annuler
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default Liste_des_contacts;
