import React, { useState } from 'react';
import {
  Stack,
  Title,
  Paper,
  Text,
  TextInput,
  Button,
  Modal,
  Table,
  ActionIcon,
  Group,
  Tooltip,
  ScrollArea,
  Switch,
  UnstyledButton,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconList,
} from '@tabler/icons-react';

// Types pour les données
interface ListItem {
  id: number;
  nom: string;
  description: string;
  cache: boolean;
  sexe?: 'M' | 'F' | 'Indéfini';
}

interface ListType {
  id: string;
  label: string;
  hasGender?: boolean;
}

const GestionDesListes = () => {
  // Types de listes disponibles
  const listTypes: ListType[] = [
    { id: 'titre', label: 'Titre', hasGender: true },
    { id: 'profession', label: 'Profession' },
    { id: 'etat-civil', label: 'État civil' },
    { id: 'origine', label: 'Origine ethnique' },
    { id: 'nationalite', label: 'Nationalité' },
    { id: 'spoken-language', label: 'SpokenLanguage' },
    { id: 'unite', label: 'Unité' },
    { id: 'unite-inventaire', label: 'Unité d\'inventaire' },
    { id: 'groupe-sanguin', label: 'Groupe sanguin' },
    { id: 'banque', label: 'Banque' },
    { id: 'mode-paiement', label: 'Mode de paiement' },
    { id: 'prescription-indication', label: 'PrescriptionIndication' },
    { id: 'indication-examen', label: 'Indication d\'examen' },
    { id: 'forme-posologique', label: 'Forme posologique' },
    { id: 'tva', label: 'TVA' },
    { id: 'observation-indication', label: 'ObservationIndication' },
    { id: 'categories-depenses', label: 'Catégories des dépenses' },
    { id: 'categories-contacts', label: 'Catégories des contacts' },
    { id: 'raison-resiliation', label: 'Raison de résiliation de con...' },
    { id: 'types-appareils', label: 'Types d\'appareils de réhabil...' },
  ];

  // États
  const [activeListType, setActiveListType] = useState<string>('titre');
  const [modalOpened, setModalOpened] = useState(false);
  const [editingItem, setEditingItem] = useState<ListItem | null>(null);

  // Formulaire
  const [form, setForm] = useState({
    nom: '',
    description: '',
    sexe: 'M' as 'M' | 'F' | 'Indéfini',
    cache: false,
  });

  // Données de test pour chaque type de liste
  const [listData, setListData] = useState<Record<string, ListItem[]>>({
    'titre': [
      { id: 1, nom: 'Mr', description: '', cache: false, sexe: 'M' },
      { id: 2, nom: 'Mlle', description: '', cache: false, sexe: 'F' },
      { id: 3, nom: 'Mme', description: '', cache: false, sexe: 'F' },
      { id: 4, nom: 'Dr.', description: '', cache: false, sexe: 'Indéfini' },
      { id: 5, nom: 'Pr.', description: '', cache: false, sexe: 'Indéfini' },
      { id: 6, nom: 'Garçon', description: '', cache: false, sexe: 'M' },
      { id: 7, nom: 'Fille', description: '', cache: false, sexe: 'F' },
      { id: 8, nom: 'Bébé', description: '', cache: false, sexe: 'Indéfini' },
    ],
    'profession': [
      { id: 1, nom: 'Employé(e)', description: '', cache: false },
      { id: 2, nom: 'Sans Profession', description: '', cache: false },
      { id: 3, nom: 'Professeur', description: '', cache: false },
      { id: 4, nom: 'Technicien', description: '', cache: false },
      { id: 5, nom: 'Médecin', description: '', cache: false },
      { id: 6, nom: 'Militaire', description: '', cache: false },
      { id: 7, nom: 'Administration', description: '', cache: false },
      { id: 8, nom: 'Agriculture', description: '', cache: false },
      { id: 9, nom: 'Police', description: '', cache: false },
      { id: 10, nom: 'Comptable', description: '', cache: false },
      { id: 11, nom: 'Tailleur', description: '', cache: false },
      { id: 12, nom: 'Commercial', description: '', cache: false },
      { id: 13, nom: 'Pilote', description: '', cache: false },
      { id: 14, nom: 'Femme Ménage', description: '', cache: false },
      { id: 15, nom: 'Cadre', description: '', cache: false },
    ],
    'etat-civil': [
      { id: 1, nom: 'Célibataire', description: '', cache: false },
      { id: 2, nom: 'Marié(e)', description: '', cache: false },
      { id: 3, nom: 'Divorcé(e)', description: '', cache: false },
      { id: 4, nom: 'Veuf(ve)', description: '', cache: false },
    ],
    'origine': [],
    'nationalite': [],
  });

  // Obtenir les données de la liste active
  const getCurrentListData = (): ListItem[] => {
    return listData[activeListType] || [];
  };

  // Obtenir le type de liste actuel
  const getCurrentListType = (): ListType | undefined => {
    return listTypes.find(type => type.id === activeListType);
  };

  // Fonctions pour gérer les modales
  const openModal = (item?: ListItem) => {
    if (item) {
      setEditingItem(item);
      setForm({
        nom: item.nom,
        description: item.description,
        sexe: item.sexe || 'M',
        cache: item.cache,
      });
    } else {
      setEditingItem(null);
      setForm({
        nom: '',
        description: '',
        sexe: 'M',
        cache: false,
      });
    }
    setModalOpened(true);
  };

  const closeModal = () => {
    setModalOpened(false);
    setEditingItem(null);
  };

  // Fonction pour sauvegarder
  const handleSave = () => {
    if (!form.nom.trim()) return;

    const newItem: ListItem = {
      id: editingItem ? editingItem.id : Date.now(),
      nom: form.nom,
      description: form.description,
      cache: form.cache,
      ...(getCurrentListType()?.hasGender && { sexe: form.sexe }),
    };

    setListData(prev => ({
      ...prev,
      [activeListType]: editingItem
        ? prev[activeListType]?.map(item => item.id === editingItem.id ? newItem : item) || []
        : [...(prev[activeListType] || []), newItem]
    }));

    closeModal();
  };

  // Fonction pour supprimer
  const handleDelete = (id: number) => {
    setListData(prev => ({
      ...prev,
      [activeListType]: prev[activeListType]?.filter(item => item.id !== id) || []
    }));
  };

  // Fonction pour basculer le cache
  const toggleCache = (id: number) => {
    setListData(prev => ({
      ...prev,
      [activeListType]: prev[activeListType]?.map(item =>
        item.id === id ? { ...item, cache: !item.cache } : item
      ) || []
    }));
  };

  return (
    <Stack gap="lg" className="w-full">
      <Title order={2} className="text-gray-800 flex items-center gap-2">
        <IconList size={24} className="text-blue-600" />
        Gestion des listes
      </Title>

      <div className="flex gap-4 h-[600px]">
        {/* Sidebar avec les types de listes */}
        <Paper p="md" withBorder className="w-64 flex-shrink-0">
          <div className="bg-blue-500 text-white px-3 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconList size={16} />
            <Text fw={600} size="sm">Types</Text>
          </div>

          <ScrollArea h={500}>
            <Stack gap="xs">
              {listTypes.map((type) => (
                <UnstyledButton
                  key={type.id}
                  onClick={() => setActiveListType(type.id)}
                  className={`w-full p-2 text-left rounded hover:bg-gray-100 transition-colors ${
                    activeListType === type.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-gray-400 rounded-sm flex-shrink-0" />
                    <Text size="sm" className={activeListType === type.id ? 'text-blue-700 font-medium' : 'text-gray-700'}>
                      {type.label}
                    </Text>
                    {activeListType === type.id && (
                      <div className="w-2 h-2 bg-green-500 rounded-full ml-auto" />
                    )}
                  </div>
                </UnstyledButton>
              ))}
            </Stack>
          </ScrollArea>
        </Paper>

        {/* Zone principale avec tableau */}
        <Paper p="md" withBorder className="flex-1">
          <div className="flex justify-between items-center mb-4">
            <Title order={4} className="text-gray-700">
              {getCurrentListType()?.label || 'Liste'}
            </Title>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => openModal()}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Ajouter
            </Button>
          </div>

          {getCurrentListData().length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Text>Aucun élément trouvé.</Text>
            </div>
          ) : (
            <ScrollArea h={450}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Description</Table.Th>
                    <Table.Th className="text-center">Caché</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {getCurrentListData().map((item) => (
                    <Table.Tr key={item.id}>
                      <Table.Td>
                        <Text fw={500}>{item.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm" c="dimmed">{item.description || '-'}</Text>
                      </Table.Td>
                      <Table.Td className="text-center">
                        <Switch
                          checked={item.cache}
                          onChange={() => toggleCache(item.id)}
                          size="sm"
                        />
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openModal(item)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDelete(item.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          )}
        </Paper>
      </div>

      {/* Modal pour ajouter/modifier */}
      <Modal
        opened={modalOpened}
        onClose={closeModal}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconList size={20} />
            <Text fw={600}>
              Ajouter {getCurrentListType()?.label}
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label={<span>Valeur <span className="text-red-500">*</span></span>}
            value={form.nom}
            onChange={(e) => setForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <TextInput
            label="Description"
            value={form.description}
            onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
          />

          {/* Champ Sexe uniquement pour les titres */}
          {getCurrentListType()?.hasGender && (
            <div>
              <Text size="sm" fw={500} mb="xs">Sexe</Text>
              <div className="flex gap-4">
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="sexe-m"
                    name="sexe"
                    checked={form.sexe === 'M'}
                    onChange={() => setForm(prev => ({ ...prev, sexe: 'M' }))}
                  />
                  <label htmlFor="sexe-m">M</label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="sexe-f"
                    name="sexe"
                    checked={form.sexe === 'F'}
                    onChange={() => setForm(prev => ({ ...prev, sexe: 'F' }))}
                  />
                  <label htmlFor="sexe-f">F</label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="radio"
                    id="sexe-indefini"
                    name="sexe"
                    checked={form.sexe === 'Indéfini'}
                    onChange={() => setForm(prev => ({ ...prev, sexe: 'Indéfini' }))}
                  />
                  <label htmlFor="sexe-indefini">Indéfini</label>
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="red"
                onClick={closeModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSave}
                disabled={!form.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default GestionDesListes;
