import { useState } from 'react';
import {
  Modal,
  TextInput,
  Select,
  Radio,
  Group,
  Button,
  Switch,
  Textarea,
  Text,
  ActionIcon,
  Tooltip,
  Drawer,
  Collapse,
  List,
  ThemeIcon
} from '@mantine/core';
import { IconAlertCircle, IconMicrophone, IconSearch, IconChevronDown, IconChevronRight } from '@tabler/icons-react';
import { FaMicrophoneAlt } from "react-icons/fa";
import { FaRegFileAlt } from "react-icons/fa";
import { MdDeleteSweep } from "react-icons/md";
import { notifications } from '@mantine/notifications';
interface AlertModalProps {
  opened: boolean;
  onClose: () => void;
  onSave: (alertData: AlertData) => void;
  doctors: { value: string; label: string }[];
  triggers: { value: string; label: string }[];
}

export interface AlertData {
  doctor: string;
  trigger: string;
  level: 'minimum' | 'moyen' | 'haut';
  description: string;
  isPermanent: boolean;
}

export function AlertModal({ opened, onClose, onSave, doctors, triggers }: AlertModalProps) {
  const [doctor, setDoctor] = useState<string | null>(null);
  const [trigger, setTrigger] = useState<string | null>(null);
  const [level, setLevel] = useState<'minimum' | 'moyen' | 'haut'>('minimum');
  const [description, setDescription] = useState('');
  const [isPermanent, setIsPermanent] = useState(false);

  // Drawer state
  const [drawerOpened, setDrawerOpened] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [alertsExpanded, setAlertsExpanded] = useState(true);

  // Audio recording state
  const [isRecording, setIsRecording] = useState(false);
  const [isRecordingDescription, setIsRecordingDescription] = useState(false);

  // Medical alerts data
  const medicalAlerts = [
    'Allaitante depuis:',
    'Allergique à l\'Aspirine',
    'Allergique à la Pénicilline',
    'Arthrose',
    'Cardiaque Anticoagulant sintro...',
    'Cardiaque prothèse valvulaire',
    'Cardiaque trouble du rythme',
    'Diabétique ID',
    'Diabétique NID',
    'Enceinte depuis:',
    'Gastralgie : ulcère anti-inflamm...',
    'Hypertension',
    'Hypotension',
    'Thyroïde'
  ];

  // Filter alerts based on search
  const filteredAlerts = medicalAlerts.filter(alert =>
    alert.toLowerCase().includes(searchValue.toLowerCase())
  );

  // Audio recording functions
  const handleStartRecording = () => {
    setIsRecording(true);
    // Simulate recording - in real app, you would use Web Audio API
    console.log('Starting audio recording...');

    // Simulate stopping after 3 seconds
    setTimeout(() => {
      setIsRecording(false);
      console.log('Recording stopped');
    }, 3000);
  };

  const handleStartDescriptionRecording = () => {
    setIsRecordingDescription(true);
    console.log('Starting description recording...');

    // Simulate recording and transcription
    setTimeout(() => {
      setIsRecordingDescription(false);
      setDescription(prev => prev + ' [Texte transcrit de l\'audio]');
      console.log('Description recording stopped and transcribed');
    }, 3000);
  };

  // Delete/Clear function
  const handleClear = () => {
    setDoctor(null);
    setTrigger(null);
    setLevel('minimum');
    setDescription('');
    setIsPermanent(false);

    console.log('Form cleared');
  };

  const handleSave = (andTrigger: boolean = false) => {
    if (!doctor || !trigger) return;

    onSave({
      doctor,
      trigger,
      level,
      description,
      isPermanent
    });

    // Reset form
    setDoctor(null);
    setTrigger(null);
    setLevel('minimum');
    setDescription('');
    setIsPermanent(false);
    onClose();

    if (andTrigger) {
      console.log('Alert saved and triggered');
    } else {
      console.log('Alert saved');
    }
  };

  return (
    <>
      <Modal.Root opened={opened} onClose={onClose} size="lg">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="var(--mantine-color-white)" className="flex gap-2 text-lg">
                <IconAlertCircle size={24} />
                Alerte - ABADI SQUAD
              </Text>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#2d89bd]" />
          </Modal.Header>
        <Modal.Body p="md">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Text component="label" htmlFor="doctor-select" className="min-w-32">
                Déclencher pour <span className="text-red-500">*</span>
              </Text>
              <Select
                id="doctor-select"
                value={doctor}
                onChange={setDoctor}
                data={doctors}
                placeholder="Sélectionner un médecin"
                required
                className="flex-1"
                clearable
              />
            </div>

            <div className="flex items-center gap-2">
              <Text component="label" htmlFor="trigger-select" className="min-w-32">
                Déclencheur
              </Text>
              <Select
                id="trigger-select"
                value={trigger}
                onChange={setTrigger}
                data={triggers}
                placeholder="Sélectionner un déclencheur"
                required
                className="flex-1"
                clearable
              />
            </div>

            <div className="flex items-center gap-2">
              <Text component="label" className="min-w-32">
                Niveau
              </Text>
              <Group justify="space-between" w={"100%"}>
              <Radio.Group value={level} onChange={(value) => setLevel(value as 'minimum' | 'moyen' | 'haut')}>
                <Group>
                  <Radio value="minimum" label="Minimum" />
                  <Radio value="moyen" label="Moyen" />
                  <Radio value="haut" label="Haut" />
               
                </Group>
                 
              </Radio.Group>
                <Group justify='flex-end'>
                   <Tooltip label="Enregistrement audio">
                    <ActionIcon
                      variant="subtle"
                      color={isRecording ? "red" : "gray"}
                      size="sm"
                      onClick={handleStartRecording}
                    >
                      <FaMicrophoneAlt size={14} />
                    </ActionIcon>
                  </Tooltip>
                   <Tooltip label="Afficher les alertes">
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                      onClick={() => setDrawerOpened(true)}
                    >
                      <FaRegFileAlt size={14} />
                    </ActionIcon>
                  </Tooltip>
                  <Tooltip label="Supprimé">
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                      onClick={handleClear}
                    >
                      <MdDeleteSweep size={14} />
                    </ActionIcon>
                  </Tooltip>
                </Group>
                </Group>
            </div>
 
            <div className="flex items-start gap-2">
              <Text component="label" htmlFor="description" className="min-w-32 pt-2">
                Description <span className="text-red-500">*</span>
              </Text>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.currentTarget.value)}
                placeholder="Description de l'alerte..."
                required
                className="flex-1"
                minRows={3}
                rightSection={
                  <ActionIcon
                    variant="subtle"
                    color={isRecordingDescription ? "red" : "gray"}
                    onClick={handleStartDescriptionRecording}
                    className="cursor-pointer"
                  >
                    <IconMicrophone size={18} />
                  </ActionIcon>
                }
              />
            </div>

            <div className="flex justify-between items-center pt-4 border-t">
              <Switch
                checked={isPermanent}
                onChange={(e) => setIsPermanent(e.currentTarget.checked)}
                label="Permanente"
                labelPosition="left"
              />

              <Group>
                <Button
                  variant="filled"
                  color="red"
                  onClick={onClose}
                >
                  Annuler
                </Button>
                <Button
                  variant="filled"
                  color="blue"
                  onClick={() => handleSave(false)}
                >
                  Sauvegarder
                </Button>
                <Button
                  variant="filled"
                  color="green"
                  onClick={() => handleSave(true)}
                >
                  Enregistrer et déclencher
                </Button>
              </Group>
            </div>
          </div>
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>

    {/* Alerts Drawer */}
    <Drawer
      opened={drawerOpened}
      onClose={() => setDrawerOpened(false)}
      position="left"
      size="sm"
      title=""
      withCloseButton={false}
      styles={{
        content: { backgroundColor: 'white' },
        body: { padding: '16px' }
      }}
    >
      <div className="space-y-4">
        {/* Search Field */}
        <TextInput
          placeholder="Rechercher"
          value={searchValue}
          onChange={(e) => setSearchValue(e.currentTarget.value)}
          leftSection={<IconSearch size={16} />}
          styles={{
            input: {
              backgroundColor: 'white',
              border: '1px solid #e9ecef'
            }
          }}
        />

        {/* Close Button */}
        <div className="flex justify-end">
          <Group gap="xs">
            <ActionIcon
              variant="filled"
              color="blue"
              size="sm"
              onClick={() => setDrawerOpened(false)}
              style={{ backgroundColor: '#3799CE' }}
            >
              <IconChevronRight size={16} />
            </ActionIcon>
          </Group>
        </div>

        {/* Alerts Section */}
        <div>
          <Group
            justify="space-between"
            className="cursor-pointer p-2 hover:bg-gray-100 rounded"
            onClick={() => setAlertsExpanded(!alertsExpanded)}
          >
            <Text fw={500} size="sm">Alertes</Text>
            <ActionIcon variant="subtle" size="xs">
              {alertsExpanded ? <IconChevronDown size={14} /> : <IconChevronRight size={14} />}
            </ActionIcon>
          </Group>

          <Collapse in={alertsExpanded}>
            <List spacing="xs" size="sm" className="mt-2">
              {filteredAlerts.map((alert, index) => (
                <List.Item
                  key={index}
                  className="text-gray-700 hover:text-blue-600 cursor-pointer py-1"
                  styles={{
                    itemWrapper: { alignItems: 'flex-start' },
                    itemLabel: { fontSize: '13px', lineHeight: '1.4' }
                  }}
                >
                  {alert}
                </List.Item>
              ))}
            </List>
          </Collapse>
        </div>
      </div>
    </Drawer>
    </>
  );
}