// frontend/dental_medicine/src/hooks/useEstimateReactive.ts

import { useState, useEffect, useCallback, useRef } from 'react';
import { estimateService, EstimateSession, ToothModification } from '../services/estimateService';
import { notifications } from '@mantine/notifications';

interface UseEstimateReactiveOptions {
  patientId?: string;
  sessionName?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
  autoInitialize?: boolean; // NOUVEAU: Contrôler l'initialisation automatique
}

interface UseEstimateReactiveReturn {
  // État de la session
  session: EstimateSession | null;
  isLoading: boolean;
  isSaving: boolean;
  lastSaved: Date | null;

  // Fonctions de gestion
  initializeSession: (patientId?: string, sessionName?: string) => Promise<void>;
  saveModification: (svgId: string, pathId: string, isVisible: boolean, highlightedPaths?: Record<string, any>) => Promise<void>;
  loadSession: (sessionId: string) => Promise<void>;
  forceSave: () => Promise<void>;
  resetSession: () => void;

  // Données dérivées
  modifications: ToothModification[];
  stats: {
    total: number;
    applied: number;
    planned: number;
    not_applied: number;
    by_type: Record<string, number>;
  };

  // Utilitaires
  getToothModifications: (toothNumber: number) => ToothModification[];
  isPathVisible: (svgId: string, pathId: string) => boolean;
  getModificationStatus: (svgId: string, pathId: string) => 'not_applied' | 'planned' | 'applied';
}

export const useEstimateReactive = (options: UseEstimateReactiveOptions = {}): UseEstimateReactiveReturn => {
  const {
    patientId,
    sessionName = 'Session par défaut',
    autoSave = true,
    autoSaveDelay = 2000,
    autoInitialize = true // Par défaut, garder l'ancien comportement
  } = options;

  // États
  const [session, setSession] = useState<EstimateSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Références
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializedRef = useRef(false);

  // Initialiser la session
  const initializeSession = useCallback(async (pid?: string, sname?: string) => {
    setIsLoading(true);
    try {
      const newSession = await estimateService.initializeSession(
        pid || patientId,
        sname || sessionName
      );
      setSession(newSession);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Erreur lors de l\'initialisation:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible d\'initialiser la session',
        color: 'red',
      });
    } finally {
      setIsLoading(false);
    }
  }, [patientId, sessionName]);

  // Initialiser la session silencieusement (sans notification)
  const initializeSessionSilently = useCallback(async (pid?: string, sname?: string) => {
    try {
      const newSession = await estimateService.initializeSession(
        pid || patientId,
        sname || sessionName
      );
      setSession(newSession);
      setLastSaved(new Date());

      // Pas de notification pour l'initialisation silencieuse
      console.log('🔇 Session initialisée silencieusement:', newSession.session_name);
    } catch (error) {
      console.error('Erreur lors de l\'initialisation silencieuse:', error);
      // Pas de notification d'erreur non plus
    }
  }, [patientId, sessionName]);

  // Sauvegarder une modification
  const saveModification = useCallback(async (
    svgId: string,
    pathId: string,
    isVisible: boolean,
    highlightedPaths?: Record<string, any>
  ) => {
    if (!session) {
      console.warn('Aucune session active, initialisation silencieuse...');
      // Initialisation silencieuse sans notification
      await initializeSessionSilently();
    }

    try {
      await estimateService.saveModificationReactive(svgId, pathId, isVisible, highlightedPaths);

      // Mettre à jour l'état local
      const updatedSession = estimateService.getCurrentSession();
      if (updatedSession) {
        setSession({ ...updatedSession });
      }

      // Programmer la sauvegarde automatique si activée
      if (autoSave) {
        scheduleAutoSave();
      }

      console.log(`🔄 Modification sauvegardée: Dent ${svgId}, Path ${pathId}, Visible: ${isVisible}`);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      notifications.show({
        title: 'Erreur de sauvegarde',
        message: 'Impossible de sauvegarder la modification',
        color: 'red',
      });
    }
  }, [session, autoSave, autoSaveDelay, initializeSession]);

  // Programmer la sauvegarde automatique
  const scheduleAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(async () => {
      setIsSaving(true);
      try {
        await estimateService.forceSave();
        setLastSaved(new Date());

        notifications.show({
          title: 'Sauvegardé',
          message: 'Modifications sauvegardées automatiquement',
          color: 'blue',
          autoClose: 2000,
        });
      } catch (error) {
        console.error('Erreur sauvegarde auto:', error);
      } finally {
        setIsSaving(false);
      }
    }, autoSaveDelay);
  }, [autoSaveDelay]);

  // Charger une session existante
  const loadSession = useCallback(async (sessionId: string) => {
    setIsLoading(true);
    try {
      const loadedSession = await estimateService.loadSession(sessionId);
      if (loadedSession) {
        setSession(loadedSession);
        setLastSaved(new Date());

        notifications.show({
          title: 'Session chargée',
          message: `Session "${loadedSession.session_name}" chargée avec succès`,
          color: 'green',
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de charger la session',
        color: 'red',
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Forcer la sauvegarde
  const forceSave = useCallback(async () => {
    setIsSaving(true);
    try {
      await estimateService.forceSave();
      setLastSaved(new Date());

      notifications.show({
        title: 'Sauvegardé',
        message: 'Session sauvegardée avec succès',
        color: 'green',
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde forcée:', error);
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de sauvegarder la session',
        color: 'red',
      });
    } finally {
      setIsSaving(false);
    }
  }, []);

  // Réinitialiser la session
  const resetSession = useCallback(() => {
    estimateService.resetSession();
    setSession(null);
    setLastSaved(null);

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    notifications.show({
      title: 'Session réinitialisée',
      message: 'Nouvelle session créée',
      color: 'blue',
    });
  }, []);

  // Utilitaires
  const getToothModifications = useCallback((toothNumber: number): ToothModification[] => {
    return estimateService.getToothModifications(toothNumber);
  }, [session]);

  const isPathVisible = useCallback((svgId: string, pathId: string): boolean => {
    if (!session) return false;

    const modification = session.modifications.find(
      mod => mod.svg_id === svgId && mod.path_id === pathId
    );

    return modification?.is_visible || false;
  }, [session]);

  const getModificationStatus = useCallback((svgId: string, pathId: string): 'not_applied' | 'planned' | 'applied' => {
    if (!session) return 'not_applied';

    const modification = session.modifications.find(
      mod => mod.svg_id === svgId && mod.path_id === pathId
    );

    return modification?.status || 'not_applied';
  }, [session]);

  // Données dérivées
  const modifications = session?.modifications || [];
  const stats = estimateService.getModificationStats();

  // Initialisation automatique (seulement si autoInitialize est true)
  useEffect(() => {
    if (!isInitializedRef.current && patientId && autoInitialize) {
      isInitializedRef.current = true;
      initializeSession();
    }
  }, [patientId, autoInitialize, initializeSession]);

  // Nettoyage
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  return {
    // État
    session,
    isLoading,
    isSaving,
    lastSaved,

    // Fonctions
    initializeSession,
    saveModification,
    loadSession,
    forceSave,
    resetSession,

    // Données
    modifications,
    stats,

    // Utilitaires
    getToothModifications,
    isPathVisible,
    getModificationStatus,
  };
};

export default useEstimateReactive;
