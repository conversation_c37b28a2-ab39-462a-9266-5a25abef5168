'use client';
import { useState } from 'react';
// import Image from 'next/legacy/image'

import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconArrowLeft } from '@tabler/icons-react';
import authService from '~/services/authService';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Box,
  TextInput,
  PasswordInput,
  Checkbox,
  Anchor,
  Paper,
  Title,
  Text,
  Group,
  Button,
  Alert,
  Stack,
  SegmentedControl,
  Divider,
  Center
} from '@mantine/core';
import Link from "next/link";

import Languages from "~/layout/navBarButton/Iconbar/Languagespage";
import { useTranslation } from 'react-i18next';

import dynamic from "next/dynamic";
const SwitchColorMode = dynamic(() => import("~/components/SwitchColorMode"), { ssr: false });
export default function LoginPage() {

  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
 // Add null check for searchParams
 const redirectTo = searchParams ? searchParams.get('redirectTo') || '/dashboard' : '/dashboard';
 const error = searchParams ? searchParams.get('error') : null;
 const verified = searchParams ? searchParams.get('verified') === 'true' : false;
 const passwordReset = searchParams ? searchParams.get('passwordReset') === 'true' : false;
 const activated = searchParams ? searchParams.get('activated') === 'true' : false;
 const { t, } = useTranslation('auth');
 console.log('Login page loaded with redirectTo:', redirectTo);

 const form = useForm({
   initialValues: {
     email: '',
     password: '',
     rememberMe: true,
     userType: 'doctor', // Default to doctor
   },
   validate: {
     email: (value) => (/^\S+@\S+\.[a-zA-Z]{2,}$/.test(value) ? null : 'Invalid email address'),
     password: (value) => (value.length < 1 ? 'Password is required' : null),
   },
 });
 const handleSubmit = async (values: typeof form.values) => {
  try {
    setLoading(true);

    console.log('Attempting login with:', values.email);
    console.log('Current redirectTo value:', redirectTo);
    console.log('Selected user type:', values.userType);

    // Add additional logging for debugging
    console.log('Login form values:', {
      email: values.email,
      userType: values.userType,
      rememberMe: values.rememberMe
    });

    // Call the auth service to log in the user
    // The authService will automatically use the appropriate endpoint based on user type
    const authResult = await authService.login({
      email: values.email,
      password: values.password,
      userType: values.userType
    });

    console.log('Login successful, tokens received:', authResult);

    // Ensure tokens are properly stored
    if (authResult.access) {
      console.log('Storing access token in localStorage');
      localStorage.setItem('token', authResult.access);

      // Store user type in localStorage
      console.log('Storing user type in localStorage:', values.userType);
      localStorage.setItem('userType', values.userType);

      if (values.rememberMe) {
        console.log('Storing refresh token in localStorage (remember me enabled)');
        localStorage.setItem('refreshToken', authResult.refresh);
      }

      // Also store token in a cookie for the middleware to detect
      console.log('Setting token cookie for middleware detection');
      // Set the cookie with SameSite=Lax to ensure it's sent with navigation requests
      document.cookie = `token=${authResult.access}; path=/; max-age=86400; SameSite=Lax`;

      // Log the token for debugging
      console.log('Token stored in localStorage:', authResult.access.substring(0, 10) + '...');
      console.log('Cookie set with token:', authResult.access.substring(0, 10) + '...');

      // Verify the token was set correctly
      setTimeout(() => {
        const storedToken = localStorage.getItem('token');
        const cookies = document.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);

        console.log('Verification - localStorage token:', storedToken ? storedToken.substring(0, 10) + '...' : 'none');
        console.log('Verification - cookie token:', cookies.token ? cookies.token.substring(0, 10) + '...' : 'none');
      }, 100);

      // Fetch user profile to get additional information
      try {
        console.log('Fetching user profile for additional information');
        const userProfile = await authService.getProfile();
        console.log('User profile fetched:', userProfile);

        // Define a more specific type for user profile data
        interface UserProfileData {
          assigned_doctor?: string;
          assigned_doctor_name?: string;
          specialization?: string;
          [key: string]: unknown;
        }

        // Use type assertion with a more specific type
        // First convert to unknown to avoid TypeScript error
        const profileData = (userProfile as unknown) as UserProfileData;

        // Store assigned doctor information for assistants and staff
        if ((values.userType === 'assistant' || values.userType === 'staff') && profileData.assigned_doctor) {
          console.log('Storing assigned doctor information:', profileData.assigned_doctor);
          localStorage.setItem('assignedDoctor', profileData.assigned_doctor);
          localStorage.setItem('assignedDoctorName', profileData.assigned_doctor_name || '');
        }

        // Store specialty information for doctors
        if (values.userType === 'doctor' && profileData.specialization) {
          console.log('Storing specialty information:', profileData.specialization);
          localStorage.setItem('specialty', profileData.specialization);
        }
      } catch (profileError) {
        console.error('Error fetching user profile:', profileError);
        // Continue with login process even if profile fetch fails
      }

      notifications.show({
        title: 'Login Successful',
        message: 'You have been logged in successfully.',
        color: 'green',
      });

      // Add a small delay to ensure tokens are saved before redirect
      console.log('Setting up redirect with timeout...');
      setTimeout(() => {
        // Determine the appropriate redirect based on user type
        let targetRedirect;

        // Redirect based on user type
        if (values.userType === 'assistant') {
          console.log(`User is assistant, redirecting to Assistants-dashboard`);
          targetRedirect = '/Assistants-dashboard';

          // Force setting userType in localStorage and cookie for assistant
          localStorage.setItem('userType', values.userType);
          document.cookie = `userType=${values.userType}; path=/; max-age=86400; SameSite=Lax`;
          console.log(`Explicitly set userType to ${values.userType} in localStorage and cookie`);
        } else if (values.userType === 'staff') {
          console.log(`User is staff, redirecting to staff-dashboard`);
          targetRedirect = '/staff-dashboard';

          // Force setting userType in localStorage and cookie for staff
          localStorage.setItem('userType', values.userType);
          document.cookie = `userType=${values.userType}; path=/; max-age=86400; SameSite=Lax`;
          console.log(`Explicitly set userType to ${values.userType} in localStorage and cookie`);
        }
        // For doctors, use the provided redirect or default to dashboard
        else if (values.userType === 'doctor') {
          if (redirectTo) {
            // Allow redirection to the license page
            targetRedirect = decodeURIComponent(redirectTo);
            console.log('Using provided redirect URL:', targetRedirect);
          } else {
            targetRedirect = '/dashboard';
            console.log('No redirect URL provided, using default dashboard');
          }
        }
        // Default fallback
        else {
          targetRedirect = '/dashboard';
        }

        console.log('Redirecting to:', targetRedirect);

        // For assistant and staff accounts, use direct navigation to avoid middleware issues
        if (values.userType === 'assistant' || values.userType === 'staff') {
          console.log('Assistant/Staff account detected, using direct navigation');
          window.location.href = targetRedirect;
        } else {
          try {
            console.log('Calling router.push()...');
            router.push(targetRedirect);
            console.log('router.push() called successfully');

            // Force a hard navigation as a fallback
            setTimeout(() => {
              console.log('Fallback: Using window.location for navigation');
              window.location.href = targetRedirect;
            }, 1000);
          } catch (navError) {
            console.error('Navigation error:', navError);
            console.log('Fallback: Using window.location for navigation after error');
            window.location.href = targetRedirect;
          }
        }
      }, 500);
    } else {
      throw new Error('No access token received');
    }
  } catch (error) {
    console.error('Login error:', error);
    // Define a type for API errors
    interface ApiError {
      response?: {
        data?: unknown;
        status?: number;
      };
      userTypeError?: boolean;
      message?: string;
    }

    const apiError = error as ApiError;
    console.error('Error details:', apiError.response?.data);

    // Check for specific error messages related to user type
    if (apiError.userTypeError ||
        (apiError.response?.data &&
         typeof apiError.response.data === 'object' &&
         'detail' in apiError.response.data &&
         typeof apiError.response.data.detail === 'string' &&
         apiError.response.data.detail.toLowerCase().includes('user type'))) {

      // Special handling for assistant/staff login attempts
      if (values.userType === 'assistant' || values.userType === 'staff') {
        notifications.show({
          title: 'Login Failed',
          message: 'Please make sure your doctor has created an account for you with the correct user type.',
          color: 'red',
        });
      } else {
        notifications.show({
          title: 'User Type Error',
          message: 'The selected user type does not match your account. Please select the correct user type.',
          color: 'red',
        });
      }
    } else if (apiError.response?.status === 401) {
      // Special handling for assistant/staff login attempts with 401 errors
      if (values.userType === 'assistant' || values.userType === 'staff') {
        notifications.show({
          title: 'Login Failed',
          message: 'Invalid email or password. Please check your credentials or contact your supervising doctor.',
          color: 'red',
        });
      } else {
        notifications.show({
          title: 'Login Failed',
          message: 'Invalid email or password. Please try again.',
          color: 'red',
        });
      }
    } else {
      // Extract error detail if available
      let errorDetail = 'An error occurred during login. Please try again.';
      if (apiError.response?.data &&
          typeof apiError.response.data === 'object' &&
          'detail' in apiError.response.data &&
          typeof apiError.response.data.detail === 'string') {
        errorDetail = apiError.response.data.detail;
      }

      notifications.show({
        title: 'Login Failed',
        message: errorDetail,
        color: 'red',
      });
    }
  } finally {
    setLoading(false);
  }
};
  
  return (
    <>
      <>
      <title>{t('page-title')} | Doctor Portal </title>
      <meta
        name="description"
        content={t('description')}
      />
      </>
        <Stack>
          {/* <Title ta="center">{t('Welcome')}</Title> */}
          <Center mb={10}>
          <Box >
            <Center >
          {/* <Image
              src="/logo.svg"
               alt="Doctor Portal logo"
              width={96}
              height={96}
            /> */}
            </Center>
            <Title order={1} ta="center" style={{ color: '#1c7ed6' }}>
              Doctor Portal
            </Title>
            <Text ta="center" c="dimmed">
              Manage your practice efficiently
            </Text> 
          </Box>
        </Center>
          {/* <Text ta="center">{t('form-to-get')}</Text> */}
          <Paper radius="md" p="xl" withBorder style={{
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
          <Group gap={4}>
          <Group justify="flex-start" mb="2px">
            <Button
              component="a"
              href="http://localhost:3001/login"
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              color="blue"
            >
              {/* Back  */}
            </Button>
          </Group>
        <Title order={2} ta="center" mt="md" mb="md">
          Welcome to Medical Practice Portal
        </Title>
        </Group>
        {activated && (
          <Alert icon={<IconAlertCircle size={16} />} title="License Activated" color="green" mb="md">
            Your license has been successfully activated. Please log in to access your account.
          </Alert>
        )}

        {verified && (
          <Alert icon={<IconAlertCircle size={16} />} title="Email Verified" color="green" mb="md">
            Your email has been verified successfully. You can now log in.
          </Alert>
        )}

        {passwordReset && (
          <Alert icon={<IconAlertCircle size={16} />} title="Password Reset" color="green" mb="md">
            Your password has been reset successfully. You can now log in with your new password.
          </Alert>
        )}

        {error === 'unauthorized' && (
          <Alert icon={<IconAlertCircle size={16} />} title="Unauthorized" color="red" mb="md">
            You are not authorized to access this portal. Please check your credentials and user type.
          </Alert>
        )}

        <form onSubmit={(e) => {
          console.log('Form submit event triggered');
          // Prevent default form submission
          e.preventDefault();

          // Log form values (without showing the actual password)
          console.log('Form values:', {
            email: form.values.email,
            password: '********'
          });

          // Call the submit handler manually
          handleSubmit(form.values);
        }}>
          <Stack>
            <TextInput
              label="Email"
              placeholder="<EMAIL>"
              required
              {...form.getInputProps('email')}
            />

            <PasswordInput
              label="Password"
              placeholder="Your password"
              required
              {...form.getInputProps('password')}
            />

            <Group style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Checkbox
                label="Remember me"
                {...form.getInputProps('rememberMe', { type: 'checkbox' })}
              />
              <Anchor component={Link} href="/forgot-password" size="sm">
                Forgot password?
              </Anchor>
            </Group>

            <Divider label="Login as" labelPosition="center" my="md" />

            <SegmentedControl
              fullWidth
              data={[
                { label: 'Doctor', value: 'doctor' },
                { label: 'Assistant', value: 'assistant' },
                { label: 'Staff (Administrative)', value: 'staff' }
              ]}
              {...form.getInputProps('userType')}
            />

            <Text size="xs" c="dimmed" ta="center" mb="xs">
              Assistants and staff accounts are created by doctors from their control panel
            </Text>

            {form.values.userType !== 'doctor' && (
              <Alert color="blue" radius="md">
                <Text size="sm" fw={500}>
                  {form.values.userType === 'assistant'
                    ? 'You are logging in as an Assistant. Make sure your doctor has created an account for you.'
                    : 'You are logging in as Staff. Make sure your doctor has created an account for you.'}
                </Text>
              </Alert>
            )}

            <Button
              fullWidth
              mt="xl"
              type="submit"
              loading={loading}
            >
              Login
            </Button>
          </Stack>
        </form>

        <Text ta="center" mt="md">
          Don&apos;t have an account?{' '}
          <Anchor component={Link} href="/register">
            Register
          </Anchor>
        </Text>
      </Paper>
        </Stack> 
        {/* <div className="fixed right-10 top-[calc(50%-12px)] "> */}
        {/* <div className='absolute md:right-10 md:top-[calc(50%-0px)] top-[calc(6%-0px)] right-6 '> */}
        <div className='absolute md:right-10 md:top-[calc(50%-0px)] top-[calc(6%-0px)] right-6 '>
        <SwitchColorMode />
        <Languages />
       </div>

        
     
    </>
  );
};

