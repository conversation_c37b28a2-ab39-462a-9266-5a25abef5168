import { UserProfile } from '@/services/authService';

// Extend the UserProfile interface to include File types for image fields
declare module '@/services/authService' {
  interface UserProfile {
    profile_image?: string | File;
    profile_image_medium?: string | File;
    profile_image_large?: string | File;
  }
}

// Define a type for profile data that can include File objects
export type ProfileDataWithFiles = Omit<UserProfile, 'profile_image' | 'profile_image_medium' | 'profile_image_large'> & {
  profile_image?: string | File;
  profile_image_medium?: string | File;
  profile_image_large?: string | File;
};
