import { useState, useEffect, useCallback } from 'react';
import { notifications } from '@mantine/notifications';
import { medicamentService, Medicament, MedicamentFilters, MedicamentStats } from '../services/medicamentService';

export interface UseMedicamentsReturn {
  // Données
  medicaments: Medicament[];
  stats: MedicamentStats | null;
  
  // États de chargement
  loading: boolean;
  statsLoading: boolean;
  exporting: boolean;
  
  // Erreurs
  error: string | null;
  statsError: string | null;
  
  // Actions
  fetchMedicaments: (filters?: MedicamentFilters) => Promise<void>;
  fetchStats: (filters?: MedicamentFilters) => Promise<void>;
  refreshData: () => Promise<void>;
  exportData: (filters?: MedicamentFilters, format?: 'excel' | 'pdf') => Promise<void>;
  
  // Filtres actuels
  currentFilters: MedicamentFilters | null;
}

export const useMedicaments = (initialFilters?: MedicamentFilters): UseMedicamentsReturn => {
  // États principaux
  const [medicaments, setMedicaments] = useState<Medicament[]>([]);
  const [stats, setStats] = useState<MedicamentStats | null>(null);
  const [currentFilters, setCurrentFilters] = useState<MedicamentFilters | null>(initialFilters || null);
  
  // États de chargement
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  
  // États d'erreur
  const [error, setError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Fonction pour récupérer les médicaments
  const fetchMedicaments = useCallback(async (filters?: MedicamentFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching medicaments with filters:', filters);
      const data = await medicamentService.getMedicaments(filters);
      setMedicaments(data);
      setCurrentFilters(filters || null);
      
      notifications.show({
        title: 'Données chargées',
        message: `${data.length} médicaments récupérés`,
        color: 'green',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des médicaments';
      setError(errorMessage);
      console.error('Error fetching medicaments:', err);
      
      notifications.show({
        title: 'Erreur',
        message: errorMessage,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Fonction pour récupérer les statistiques
  const fetchStats = useCallback(async (filters?: MedicamentFilters) => {
    setStatsLoading(true);
    setStatsError(null);
    
    try {
      console.log('Fetching medicament stats with filters:', filters);
      const statsData = await medicamentService.getMedicamentStats(filters);
      setStats(statsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des statistiques';
      setStatsError(errorMessage);
      console.error('Error fetching stats:', err);
      
      notifications.show({
        title: 'Erreur statistiques',
        message: errorMessage,
        color: 'orange',
      });
    } finally {
      setStatsLoading(false);
    }
  }, []);

  // Fonction pour rafraîchir toutes les données
  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchMedicaments(currentFilters || undefined),
      fetchStats(currentFilters || undefined),
    ]);
  }, [fetchMedicaments, fetchStats, currentFilters]);

  // Fonction pour exporter les données
  const exportData = useCallback(async (filters?: MedicamentFilters, format: 'excel' | 'pdf' = 'excel') => {
    setExporting(true);
    
    try {
      console.log('Exporting medicaments data:', { filters, format });
      const blob = await medicamentService.exportMedicaments(filters, format);
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `medicaments_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      notifications.show({
        title: 'Export réussi',
        message: `Les données ont été exportées en ${format.toUpperCase()}`,
        color: 'green',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'export';
      console.error('Error exporting data:', err);
      
      notifications.show({
        title: 'Erreur d\'export',
        message: errorMessage,
        color: 'red',
      });
    } finally {
      setExporting(false);
    }
  }, []);

  // Chargement initial des données
  useEffect(() => {
    fetchMedicaments(initialFilters);
    fetchStats(initialFilters);
  }, []); // Exécuter seulement au montage

  return {
    // Données
    medicaments,
    stats,
    
    // États de chargement
    loading,
    statsLoading,
    exporting,
    
    // Erreurs
    error,
    statsError,
    
    // Actions
    fetchMedicaments,
    fetchStats,
    refreshData,
    exportData,
    
    // Filtres actuels
    currentFilters,
  };
};

export default useMedicaments;
