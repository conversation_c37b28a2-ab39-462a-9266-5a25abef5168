// frontend/dental_medicine/src/components/content/dental/specialties/EstheticDentistry/EstheticControls.tsx

import React from 'react';
import { <PERSON><PERSON>, Tooltip, <PERSON>u, rem ,Group} from '@mantine/core';
import { IconSquareRoundedPlusFilled, IconFileZip, IconEye, IconTrash } from '@tabler/icons-react';
import { DentalControlsProps } from '../../shared/types';


// Interface pour les contrôles esthétiques (hérite de DentalControlsProps)
type EstheticControlsProps = DentalControlsProps;

export const EstheticControls: React.FC<EstheticControlsProps> = ({
  activeButton,
  onButtonClick,
  onTargetPathChange
}) => {

  const controls = [
    {
      id: 'Cleaning',
      label: 'Cleaning',
      pathId: '17',
      tooltip: 'Cleaning',
      shortCode: 'Cl',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="-5.0 -10.0 110.0 135.0">
            <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="m87.484 32.52c-3.4727-1.6875-10.57-5.2656-13.992-6.9219v-0.003906c-0.94531-0.46484-2.0547-0.46484-3.0039 0l-13.977 6.9258c-1.2305 0.58984-1.9805 1.8672-1.8945 3.2266 0.35156 5.7656 2.6016 19.934 15.871 26.875 0.94531 0.49609 2.0742 0.49609 3.0195 0 13.27-6.9375 15.516-21.109 15.855-26.875 0.089844-1.3594-0.65234-2.6367-1.8789-3.2266zm-5.0586 6.8281-11.789 11.789h-0.003907c-0.29687 0.30859-0.70312 0.48047-1.1328 0.48047-0.42578 0-0.83594-0.17188-1.1328-0.48047l-6.793-6.7969c-0.60547-0.62891-0.59766-1.625 0.019531-2.2422 0.61719-0.61719 1.6172-0.625 2.2461-0.023437l5.6719 5.6523 10.648-10.648v0.003906c0.62891-0.60547 1.625-0.59766 2.2422 0.019531 0.61719 0.61719 0.625 1.6133 0.019531 2.2422z"/>
            <path style={{ fill: "#f5f5f5" ,stroke:"#3799CE",strokeWidth:0.25,strokeMiterlimit:10}} d="m68.996 65.48c-14.715-7.7109-17.188-23.227-17.574-29.539-0.16406-2.6523 1.2812-5.1406 3.6602-6.3164l13.992-6.9062c2.9531-1.6211 5.9883-0.046875 8.6406 1.3828-0.51953-5.3633-0.16797-16.254-7.8711-15.645-6.668 0.63281-14.691 5.1523-19.934 9.3008-2.7461 2.2344-6.582 2.5742-9.6797 0.85547-0.46094-0.28516-0.74219-0.79688-0.72656-1.3398-0.003906-0.52344 0.25781-1.0156 0.69141-1.3086 0.43359-0.29688 0.98828-0.35547 1.4727-0.16016 0.60156 0.26172 1.2227 0.47266 1.8594 0.62109 0.89844 0.14062 1.8164 0.042969 2.6641-0.28516 1.8438-0.85938 3.5547-1.9805 5.0781-3.3281 1.043-0.82812 2.1719-1.543 3.3633-2.1406l-3.1328-2.2969c-5.7891-4.0508-12.973-1.8945-17.43 2.9727h0.003907c-1.7852 1.7227-4.5586 1.8906-6.5391 0.40234-3.957-3.082-12.855-6.0664-15.066 0.59375-3.375 11.344-2.0859 23.566 3.582 33.957 1.7656 3.2109 2.4688 6.9023 2.0078 10.539-1.1094 8.4805-1.0938 24.496 11.406 36.305l-0.003906-0.003906c0.625 0.59375 1.5352 0.76172 2.3281 0.42969 0.79297-0.32812 1.3164-1.0977 1.3359-1.9531-0.33203-4.2578 0.83203-44.207 15.23-33.461 6.5547 7.0664 6.9375 27.387 6.9062 33.445 0.011719 0.85938 0.53125 1.6328 1.3281 1.9648 0.79297 0.33594 1.707 0.16797 2.332-0.42578 7.3164-7.1328 11.543-16.852 11.777-27.066-0.59375-0.125-1.1641-0.32422-1.7031-0.59375z"/>
            </svg>
      )
    },
    {
      id: 'Fluoride',
      label: 'Fluoride',
      pathId: '18',
      tooltip: 'Fluoride',
      shortCode: 'Fl',
      icon: (
          <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 16.6 17"
            >
               <style jsx>{`
                .st42{fill:#FAF8F8;stroke:#3799ce;stroke-width:0.75;stroke-miterlimit:10;}
                .st43 { fill:none; }
                .st44 { fill-rule:evenodd;clip-rule:evenodd;fill:#ffffff; }
                .st45 { fill:none;stroke:#3799ce;stroke-width:0.5;stroke-miterlimit:10; }
              `}</style>
               <path className="st42" d="M13.6,8.2l0.2-0.4c0.5-0.8,0.8-1.7,0.8-2.7V4.8c0-1.3-0.8-2.6-2.2-3.3c-0.1,0-0.1-0.1-0.2-0.1   c-0.2-0.1-0.5-0.2-0.8-0.2c-0.1,0-0.1,0-0.2,0c-0.5,0-1,0-1.5,0.1c-0.4,0-0.8,0.1-1.2,0.1c-0.4,0-0.8-0.1-1.1-0.1   C7,1.2,6.5,1.2,6,1.2H5.3c-0.3,0-0.5,0-0.8,0.1C4,1.5,3.1,2,2.4,2.9C2.1,3.4,2,3.9,2,4.5c0,0.2,0,0.4,0,0.6c0,0.9,0.2,1.8,0.6,2.6   L3.3,9c0.1,0.2,0.2,0.4,0.2,0.6l0,2.2c0,0.8,0.2,1.6,0.5,2.3c0.3,0.7,0.9,1.4,1.6,1.4c0,0,1.4,0.2,1.4-1.8c0,0,0.1-1.1,0.1-1.8   c0-0.4,0.2-0.9,0.6-1.2C8,10.5,8.5,10.4,9,11c0.2,0.3,0.4,0.6,0.4,1c0,0.3,0,0.8,0.1,1.2c0,0.3,0.1,0.6,0.1,1v0   c0.3,0.8,1.4,2.8,2.8-0.3c0,0,0.7-1.7,0.7-3.7C13.1,9.4,13.3,8.8,13.6,8.2z"/>
               <path className="st43" d="M15.9,9.2l0.2-0.4c0.1-0.1,0.2-0.3,0.3-0.5c0.2-0.3,0.3-0.7,0.5-1c0.3-0.7,0.4-1.5,0.4-2.3l-3.7-0.1         c0,0.3-0.1,0.7-0.2,1c-0.1,0.4-0.3,0.6-0.6,1.1L15.9,9.2z"/>
                    <path className="st44" d="M15.3,5.8l0.4,0.1c0-0.2,0.1-0.4,0.1-0.7l-0.4,0C15.3,5.4,15.3,5.6,15.3,5.8z"/>
                    <path className="st44" d="M14.9,6.9L15.3,7c0.1-0.2,0.2-0.4,0.2-0.6l-0.4-0.1C15.1,6.5,15,6.7,14.9,6.9z"/>
                    <path className="st44" d="M14.4,7.8L14.7,8c0.1-0.2,0.2-0.4,0.3-0.5l-0.3-0.2C14.6,7.5,14.5,7.7,14.4,7.8z"/>

                      <path className="st43" d="M17.4,5l0-0.5c0-0.1,0-0.4,0-0.6c0-0.4-0.1-0.9-0.3-1.3c-0.3-0.8-0.8-1.5-1.4-2.1l-2.6,2.7         c0.2,0.2,0.4,0.5,0.5,0.7c0,0.1,0.1,0.2,0.1,0.4c0,0.1,0,0.1,0,0.2l0,0.4L17.4,5z"/>

                    <path className="st44" d="M14.8,2.7l0.3-0.2c-0.1-0.2-0.3-0.4-0.4-0.5l-0.3,0.3C14.6,2.3,14.7,2.5,14.8,2.7z"/>
                    <path className="st44" d="M15.3,3.6l0.4-0.1c-0.1-0.2-0.1-0.4-0.2-0.6L15,3.1C15.1,3.3,15.2,3.5,15.3,3.6z"/>
                    <path className="st44" d="M15.4,4.7h0.4c0-0.2,0-0.4,0-0.7l-0.4,0C15.4,4.3,15.4,4.5,15.4,4.7z"/>

                      <path className="st43" d="M15.7,0.6c-0.6-0.6-1.3-1-2-1.3l-0.3-0.1c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.5-0.1-0.7-0.1         c-0.1,0-0.2,0-0.4,0l-0.2,0l-0.5,0l0,3.7c0.1,0,0.2,0,0.4,0l0.2,0l0.1,0c0,0,0.1,0,0.1,0l0.1,0l0.1,0.1         c0.3,0.1,0.6,0.3,0.8,0.5L15.7,0.6z"/>

                    <path className="st44" d="M12.2,0.9l0.1-0.4c-0.2,0-0.4,0-0.7,0l0,0.4C11.8,0.8,12,0.9,12.2,0.9z"/>
                    <path className="st44" d="M13.2,1.2l0.2-0.3c-0.2-0.1-0.4-0.2-0.6-0.3L12.6,1C12.8,1.1,13,1.1,13.2,1.2z"/>
                    <path className="st44" d="M14.1,1.8l0.2-0.3c-0.2-0.1-0.3-0.3-0.5-0.4l-0.2,0.3C13.8,1.6,13.9,1.7,14.1,1.8z"/>

                      <path className="st43" d="M11.3-1.1l-1,0l-0.9,0C9-1,8.5-1,8.1-1.1L7.7,2.7c0.7,0.1,1.4,0,2,0l0.5,0l0.3,0l0.7,0L11.3-1.1z"/>

                    <path className="st44" d="M8.8,1l0-0.4c-0.2,0-0.4,0-0.6,0l0,0.4C8.4,1,8.6,1,8.8,1z"/>
                    <polygon className="st44" points="9.9,0.9 9.9,0.5 9.3,0.6 9.3,1       "/>
                    <polygon className="st44" points="11,0.9 11,0.5 10.4,0.5 10.4,0.9       "/>

                      <path className="st43" d="M8.1-1.1L6.3-1.2c-0.3,0-0.7-0.1-0.9,0H4.9l-0.3,0c-0.1,0-0.2,0-0.4,0l0.4,3.7l0.1,0l0.1,0h0.4         c0.3,0,0.5,0,0.7,0c0.5,0,0.8,0.1,1.6,0.1L8.1-1.1z"/>

                    <polygon className="st44" points="5.4,0.8 5.4,0.4 4.8,0.4 4.8,0.8       "/>
                    <polygon className="st44" points="6.5,0.8 6.5,0.5 5.9,0.4 5.9,0.8       "/>
                    <polygon className="st44" points="7.6,0.9 7.7,0.5 7.1,0.5 7,0.9       "/>

                      <path className="st43" d="M4.3-1.2C3.9-1.2,3.3-1,3-0.9C2.6-0.7,2.3-0.5,2-0.3C1.3,0.1,0.7,0.6,0.2,1.2l2.9,2.3         C3.4,3.3,3.6,3,3.9,2.8c0.3-0.2,0.7-0.4,0.8-0.3L4.3-1.2z"/>

                    <path className="st44" d="M2.4,1.8L2.1,1.6C2,1.7,1.8,1.9,1.7,2l0.3,0.2C2.1,2.1,2.2,2,2.4,1.8z"/>
                    <path className="st44" d="M3.3,1.2L3.1,0.9C2.9,1,2.7,1.1,2.5,1.2l0.2,0.3C2.9,1.4,3.1,1.3,3.3,1.2z"/>
                    <path className="st44" d="M4.3,0.8L4.2,0.5C4,0.5,3.8,0.6,3.6,0.7L3.7,1C3.9,0.9,4.1,0.9,4.3,0.8z"/>

                      <path className="st43" d="M0.2,1.2C0,1.5-0.3,2-0.5,2.4c-0.2,0.5-0.3,0.9-0.3,1.4l0,0.9c0,0.4,0,0.8,0.1,1.1L3,5.4         C3,5.2,3,5,3,4.8L3,4c0-0.2,0-0.3,0.2-0.5L0.2,1.2z"/>

                    <path className="st44" d="M1.2,4.8l-0.4,0c0,0.2,0,0.4,0,0.7l0.4,0C1.2,5.2,1.2,5,1.2,4.8z"/>
                    <path className="st44" d="M1.3,3.6L0.9,3.6c0,0.2,0,0.5,0,0.7l0.4,0C1.2,4,1.2,3.8,1.3,3.6z"/>
                    <path className="st44" d="M1.6,2.7L1.3,2.5C1.2,2.6,1.1,2.8,1,3.1l0.4,0.1C1.4,3,1.5,2.8,1.6,2.7z"/>

                      <path className="st43" d="M-0.7,5.9C-0.6,6.7-0.4,7.4,0,8.1c0.2,0.4,0.3,0.6,0.4,0.8l0.4,0.7l3.3-1.8L3.3,6.4         C3.2,6.1,3,5.7,3,5.4L-0.7,5.9z"/>

                    <rect x="2" y="8" transform="matrix(0.8751 -0.484 0.484 0.8751 -3.7334 2.069)" className="st44" width="0.4" height="0.6"/>
                    <path className="st44" d="M1.6,6.9L1.3,7.1c0.1,0.2,0.2,0.4,0.3,0.6l0.3-0.2C1.8,7.3,1.7,7.1,1.6,6.9z"/>
                    <path className="st44" d="M1.3,5.9L0.9,5.9C1,6.1,1,6.4,1.1,6.6l0.4-0.1C1.4,6.3,1.4,6.1,1.3,5.9z"/>

                      <path className="st43" d="M0.8,9.6l0.1,0.2l0,0l0,0.7l0,1.6L4.7,12l0-1.5l0-0.9c0-0.5-0.2-1-0.4-1.5L4.1,7.8L0.8,9.6z"/>


                      <rect x="2.6" y="11.2" transform="matrix(0.9998 -1.784261e-02 1.784261e-02 0.9998 -0.2047 5.136523e-02)" className="st44" width="0.4" height="0.6"/>

                      <rect x="2.6" y="10.1" transform="matrix(0.9998 -1.784262e-02 1.784262e-02 0.9998 -0.1845 5.082545e-02)" className="st44" width="0.4" height="0.6"/>
                    <path className="st44" d="M2.7,8.9L2.4,9.1c0.1,0.2,0.2,0.3,0.2,0.5l0.4-0.1C2.9,9.3,2.8,9.1,2.7,8.9z"/>

                      <path className="st43" d="M1,12.1c0,0.7,0.1,1.5,0.4,2.2c0.3,1,1,2.1,1.7,2.7l1.9-3.8l0-0.1c-0.1-0.3-0.2-0.7-0.2-1.1L1,12.1z"/>

                    <path className="st44" d="M3.6,14.4l-0.3,0.3c0.1,0.2,0.2,0.4,0.4,0.6l0.2-0.3C3.8,14.7,3.7,14.6,3.6,14.4z"/>
                    <path className="st44" d="M3.2,13.4l-0.4,0.1c0.1,0.2,0.1,0.4,0.2,0.6L3.4,14C3.3,13.8,3.2,13.6,3.2,13.4z"/>
                    <path className="st44" d="M3,12.3l-0.4,0c0,0.2,0,0.4,0.1,0.7l0.4-0.1C3,12.7,3,12.5,3,12.3z"/>

                      <path className="st43" d="M3.1,17c0.7,0.5,1.4,0.8,2.3,0.8c1.1,0,2.1-0.6,2.7-1.5l-3.1-3.1L3.1,17z"/>

                    <path className="st44" d="M6.1,15.4l0.2,0.4c0.2-0.1,0.3-0.3,0.4-0.5L6.4,15C6.3,15.1,6.2,15.3,6.1,15.4z"/>
                    <path className="st44" d="M5.1,15.7l0,0.4l0.1,0c0.2,0,0.4,0,0.6-0.1l-0.1-0.4C5.5,15.7,5.3,15.7,5.1,15.7L5.1,15.7z"/>
                    <path className="st44" d="M4.2,15.2L4,15.6c0.2,0.2,0.4,0.3,0.6,0.4l0.1-0.4C4.5,15.5,4.3,15.4,4.2,15.2z"/>

                      <path className="st43" d="M8,16.2c0.3-0.4,0.4-0.9,0.5-1.4c0.1-0.3,0.1-0.5,0.1-0.8l0-0.8l0-0.9l0-0.2l0,0.1c0,0,0,0.1,0,0.1         l-3.4-1.6C5.2,11,5.1,11.3,5,11.6c0,0.2,0,0.3,0,0.5l0,0.2l0,0.8l0,0.1L8,16.2z"/>

                    <path className="st44" d="M6.7,12.4l0.4,0c0-0.2,0-0.4,0.1-0.6l-0.4-0.1C6.7,11.9,6.7,12.2,6.7,12.4z"/>
                    <polygon className="st44" points="6.7,13.5 7,13.5 7.1,12.9 6.7,12.9       "/>
                    <path className="st44" d="M6.6,14.5l0.3,0.3C7,14.6,7,14.3,7,14.1l-0.4-0.2C6.6,14.1,6.6,14.3,6.6,14.5z"/>

                      <path className="st43" d="M8.7,12.3c0,0.1-0.1,0.1-0.1,0.1c0,0,0,0-0.1,0.1c-0.1,0-0.3,0-0.4,0c-0.3-0.1-0.4-0.2-0.4-0.2         c0,0,0-0.1,0-0.1l3.6-0.9c-0.1-0.6-0.4-1.1-0.8-1.6c-0.3-0.4-0.9-0.9-1.8-1C7.7,8.7,6.9,9,6.5,9.3C6,9.6,5.6,10.1,5.3,10.7         L8.7,12.3z"/>

                    <path className="st44" d="M9.1,11l-0.3,0.3c0.1,0.1,0.2,0.3,0.3,0.4l0.3-0.1C9.4,11.3,9.3,11.1,9.1,11z"/>
                    <path className="st44" d="M8,10.5l0.1,0.4c0.2,0,0.3,0,0.5,0.1l0.2-0.3C8.5,10.5,8.2,10.5,8,10.5z"/>
                    <path className="st44" d="M7,11.2l0.3,0.2c0.1-0.1,0.2-0.3,0.4-0.4l-0.2-0.3C7.3,10.9,7.1,11,7,11.2z"/>

                      <path className="st43" d="M7.6,12.3L7.6,12.3l0,0.1l0,0.4c0,0.3,0,0.5,0.1,0.9l0.1,1.1l0,0.1l0,0l0,0L8,15.2l0,0l0,0l0.1,0.1         l0.1,0.3c0.1,0.2,0.2,0.4,0.3,0.5l3.1-2.1c0,0-0.1-0.1-0.1-0.2l-0.1-0.1l0-0.1l0,0l0,0l0,0c0.1,0.2-0.1-0.4,0.1,0.4l0,0l0,0         l0,0l0,0c0-0.2,0-0.4-0.1-0.6c0-0.2,0-0.6-0.1-0.8l0-0.4l0-0.3c0-0.1,0-0.3-0.1-0.4L7.6,12.3z"/>

                    <path className="st44" d="M9.8,14.3l-0.4,0.1c0.1,0.3,0.2,0.4,0.3,0.6l0.3-0.2C10,14.7,9.8,14.5,9.8,14.3z"/>
                    <path className="st44" d="M9.7,13.2l-0.4,0c0,0.2,0,0.4,0.1,0.6l0.4,0C9.7,13.6,9.7,13.4,9.7,13.2z"/>
                    <polygon className="st44" points="9.6,12.1 9.2,12.1 9.3,12.7 9.7,12.7       "/>


                      <path className="st43" d="M8.5,16.2c0.3,0.4,0.6,0.8,1.1,1.2c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.4,0.1,0.6,0.2c0.2,0,0.5,0,0.7,0         c0.2,0,0.4-0.1,0.6-0.2c0.7-0.3,1.1-0.7,1.4-1c0.3-0.3,0.5-0.7,0.7-1L11,13.7c-0.1,0.2-0.2,0.3-0.3,0.4         c-0.1,0.1-0.1,0.1,0.1,0c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.2,0.1c0.1,0,0.1,0,0.1,0.1c0.1,0.1,0,0,0-0.1         L8.5,16.2z"/>

                    <path className="st44" d="M12,15.2l0.3,0.3c0.2-0.2,0.3-0.3,0.4-0.5l-0.3-0.2C12.3,14.9,12.2,15.1,12,15.2z"/>
                    <path className="st44" d="M11.2,15.7l0,0.4c0.3,0,0.5-0.1,0.7-0.2l-0.2-0.3C11.5,15.7,11.3,15.7,11.2,15.7z"/>
                    <path className="st44" d="M10.3,15.2L10,15.5c0.1,0.2,0.3,0.3,0.5,0.5l0.2-0.3C10.6,15.5,10.5,15.4,10.3,15.2z"/>

                      <path className="st43" d="M14.2,15.6c0.2-0.3,0.3-0.6,0.5-1c0.1-0.3,0.2-0.6,0.3-1c0.2-0.6,0.3-1.3,0.4-1.9l-3.7-0.5         c-0.1,0.5-0.2,0.9-0.3,1.4c-0.1,0.2-0.1,0.4-0.2,0.6c0,0.1-0.2,0.3-0.3,0.5L14.2,15.6z"/>

                    <path className="st44" d="M13.4,12.2l0.4,0.1c0-0.2,0.1-0.4,0.1-0.6l-0.4-0.1C13.5,11.8,13.4,12,13.4,12.2z"/>
                    <path className="st44" d="M13.1,13.3l0.4,0.1c0.1-0.2,0.1-0.4,0.2-0.6l-0.4-0.1C13.2,12.9,13.1,13.1,13.1,13.3z"/>
                    <path className="st44" d="M12.6,14.3l0.3,0.2c0.1-0.2,0.2-0.4,0.3-0.6l-0.3-0.1C12.8,14,12.7,14.1,12.6,14.3z"/>

                      <path className="st43" d="M15.5,11.7c0-0.3,0.1-0.7,0.1-1l0-0.5c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.5,0.3-0.8l-3.1-2         c-0.4,0.7-0.8,1.5-0.9,2.4c0,0.2,0,0.5,0,0.6l0,0.3c0,0.2,0,0.4-0.1,0.7L15.5,11.7z"/>

                    <path className="st44" d="M13.8,8.9L14.2,9c0.1-0.2,0.2-0.4,0.2-0.5l-0.3-0.2C14,8.5,13.9,8.7,13.8,8.9z"/>
                    <path className="st44" d="M13.6,10l0.4,0c0-0.2,0-0.4,0.1-0.6l-0.4-0.1C13.6,9.6,13.6,9.8,13.6,10z"/>
                    <path className="st44" d="M13.6,11.1l0.4,0c0-0.2,0-0.4,0.1-0.6l-0.4,0C13.6,10.7,13.6,10.9,13.6,11.1z"/>

          <path className="st45" d="M6.5,2.8c0,0-1.1-1.1-2.7,1.1"/>
        </svg>
      )
    },
    {
      id: 'Sealant',
      label: 'Sealant',
      pathId: '19',
      tooltip: 'Sealant',
      shortCode: 'Se',
      icon: (
       <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 120" x="0px" y="0px">
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M75.49,77.49a7.21,7.21,0,0,1-4.16-13.11c-1.58-6,5.88-10.32,10.35-6a37.65,37.65,0,0,1,3.69-5.46c-.71-1.52-1.61-2.18-3.6-2.86a2.7,2.7,0,0,1,0-5.1c2.45-.85,3.25-1.65,4.09-4.1a2.71,2.71,0,0,1,5.1,0s0,.09.05.13A23.94,23.94,0,0,0,50,20.56a25,25,0,0,1-14.71,7.55h-.14a1,1,0,0,1-.13-2,23,23,0,0,0,13.53-6.94,19.87,19.87,0,0,0-1.9-1.53A24.42,24.42,0,0,1,37.89,23a1,1,0,0,1-.67-1.88,22.65,22.65,0,0,0,7.71-4.63A23.93,23.93,0,0,0,9.66,43.78a9.17,9.17,0,0,1,3.44-.2,20.81,20.81,0,0,1,1-15.7,1,1,0,0,1,1.34-.46,1,1,0,0,1,.45,1.34,18.8,18.8,0,0,0-.44,15.48,8.86,8.86,0,0,1,4,3.48,6.24,6.24,0,0,1,6.9,6.13,7.58,7.58,0,1,1-3.63,14.66A40,40,0,0,1,25,77.34l2,11.24a7.25,7.25,0,0,0,14.41-.39L43,72.86A7,7,0,0,1,45.49,68a13.51,13.51,0,0,0-8.1,6,1,1,0,0,1-1.73-1c6-10.41,23.61-10,29,.58a1,1,0,1,1-1.78.91A13.27,13.27,0,0,0,54.48,68,7,7,0,0,1,57,72.86L58.5,87.92a7.53,7.53,0,0,0,15,.4L75,81.24C75.47,77.29,75.59,77.49,75.49,77.49ZM43.56,52.92a5.83,5.83,0,0,1-4.05-1.64,6,6,0,1,1-1-11.78A6,6,0,1,1,49,44.89,5.87,5.87,0,0,1,43.56,52.92ZM63.4,17.48a16.32,16.32,0,0,1,17.36,4.63A1,1,0,0,1,80,23.77a1,1,0,0,1-.75-.34A14.34,14.34,0,0,0,64,19.38a1,1,0,0,1-.64-1.9Zm2.9,21.43c-.91-2.92-1.63-3.65-4.56-4.56a2.46,2.46,0,0,1,0-4.69c2.93-.92,3.65-1.64,4.56-4.56a2.46,2.46,0,0,1,4.7,0c.91,2.92,1.63,3.64,4.56,4.56a2.46,2.46,0,0,1,0,4.69c-2.93.91-3.65,1.64-4.56,4.56A2.46,2.46,0,0,1,66.3,38.91Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M69.09,38.31c1.1-3.53,2.33-4.76,5.87-5.87a.46.46,0,0,0,0-.87c-3.54-1.11-4.77-2.34-5.87-5.87a.46.46,0,0,0-.88,0c-1.1,3.53-2.33,4.76-5.87,5.87a.45.45,0,0,0,0,.87c3.54,1.11,4.77,2.34,5.87,5.87A.46.46,0,0,0,69.09,38.31Z"/>
                   <circle cx="5.23" cy="71.15" r="4.1" style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}}/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M20.93,7.28a3.09,3.09,0,1,0-3.09-3.09A3.09,3.09,0,0,0,20.93,7.28Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M44.11,37.54A3.93,3.93,0,0,0,40.2,42a3.95,3.95,0,1,0,0,6.91,3.87,3.87,0,0,0,7.25-1.87,3.82,3.82,0,0,0-.89-2.43A4,4,0,0,0,44.11,37.54Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M94.4,46.8c-3-1-4.29-2.29-5.33-5.33a.7.7,0,0,0-1.32,0c-1,3-2.29,4.29-5.33,5.33a.7.7,0,0,0,0,1.32c3,1,4.29,2.29,5.33,5.33a.7.7,0,0,0,1.32,0c1-3,2.29-4.29,5.33-5.33A.7.7,0,0,0,94.4,46.8Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M30.63,62.61a5.56,5.56,0,0,0-6.73-6.68,4.22,4.22,0,0,0-5.45-5.86,7,7,0,1,0-6.89,9.39,5.2,5.2,0,0,0,8.88,4.78A5.58,5.58,0,0,0,30.63,62.61Z"/>
                   <path style={{ fill: "#ffffff",stroke:"#42aee3" ,strokeWidth:1, strokeMiterlimit:10}} d="M81.47,64a4.24,4.24,0,1,0-7.54,1.36A5.21,5.21,0,1,0,80,72.84,5.57,5.57,0,1,0,81.47,64Z"/>
                 </svg>
      )
    },
    {
      id: 'viewModeWhitening',
      label: 'Whitening',
      pathId: '20',
      tooltip: 'Whitening',
      shortCode: 'Wh',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 100 125" enableBackground="new 0 0 100 100">
          <g>
            <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M48.146,22.038c-23.132-9.042-35.014,2.7-26.877,27.101c1.381,4.139,11.02,34.895,17.751,32.745   c6.173-1.973,7.039-14.389,10.76-17.195c5.452,0.963,5.986,20.604,13.325,17.041c8.523-4.139,15.563-30.213,17.313-38.475   c1.656-7.838,2.085-24.431-9.303-25.353C62.975,17.24,56.018,22.238,48.146,22.038z"/>
          </g>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M82.689,5.845c0,9.515-9.79,9.789-9.79,9.789s9.79,0.274,9.79,9.789c0-9.515,9.789-9.789,9.789-9.789  S82.689,15.36,82.689,5.845z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M18.71,64.422c0,9.113-9.376,9.376-9.376,9.376s9.376,0.262,9.376,9.374c0-9.112,9.375-9.374,9.375-9.374  S18.71,73.535,18.71,64.422z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M18.71,7.758c0,7.655-7.876,7.875-7.876,7.875s7.876,0.22,7.876,7.875c0-7.654,7.875-7.875,7.875-7.875  S18.71,15.414,18.71,7.758z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M81.211,61.422c0,7.656-7.877,7.876-7.877,7.876s7.877,0.22,7.877,7.874c0-7.654,7.875-7.874,7.875-7.874  S81.211,69.078,81.211,61.422z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M57.148,4.84c0,6.32-6.502,6.501-6.502,6.501s6.502,0.181,6.502,6.499c0-6.318,6.5-6.499,6.5-6.499  S57.148,11.16,57.148,4.84z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M28.898,76.672c0,6.32-6.502,6.502-6.502,6.502s6.502,0.181,6.502,6.498c0-6.317,6.5-6.498,6.5-6.498  S28.898,82.992,28.898,76.672z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M85.712,51.5c0,6.32-6.502,6.502-6.502,6.502s6.502,0.181,6.502,6.498c0-6.317,6.5-6.498,6.5-6.498  S85.712,57.82,85.712,51.5z"/>
          <path style={{ fill: "#ffffff", stroke: "#42aee3", strokeWidth: 1, strokeMiterlimit: 10 }} d="M13.462,22c0,4.862-5.002,5.001-5.002,5.001s5.002,0.14,5.002,4.999c0-4.859,5-4.999,5-4.999S13.462,26.862,13.462,22z"/>
        </svg>
      )
    }
  ];

  return (
    <Group justify="space-between" mb={4} gap="xl"  w={"70%"}>
      {/* Menu "Tous les Traitements" */}
      <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                Tous les Traitements
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Télécharger zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Aperçu de tous
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Supprimer tous
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
      {/* Boutons de traitement */}
      <div style={{ margin: '0 auto' }} className="mb-2 flex flex-end p-2 sm:justify-start space-x-2">
         {controls.map((control) => (
   <Tooltip
        key={control.id}
        label={control.tooltip}
        withArrow
        className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
        <Button
        styles={{
        root: {
        position: 'relative',
        color: 'white',
        height: '35px', // Adjust button height
        width: '35px',  // Adjust button width
        padding: 0,
        borderRadius: '0.5rem'
        },
      }}
    >
        <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      width: '100%',
    }}
    >
      <span  className={
          activeButton === control.id
        ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
        : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
    }
    onClick={() => {
              onButtonClick(control.id);
              onTargetPathChange(control.pathId);
            }}
        >
   {control.icon}
      </span>
        </div>
        <span
          style={{
            position: 'absolute',
            bottom: '0px',
            right: '0px',
            fontSize: '8px',
            fontWeight: '800',
            backgroundColor: 'white',
            borderTopLeftRadius: '0.5rem' ,
            borderBottomRightRadius: '0.5rem' ,
            color:'#3799CE',
            padding:'3px  0px 1px 2px' ,
          }}
          className="h-[14px] w-[14px] "
        >
            {control.shortCode}
        </span>
      </Button>
    </Tooltip>
    ))}
      </div>
    
    </Group>
  );
};

export default EstheticControls;
