import React, { useState } from "react";
import { DentalSvg as DentalSvgType } from '../utils/Tdantal';
interface DentalSvgProps {
  svgData: DentalSvgType;
  isHidingMode: boolean;
  highlightedPaths: Record<string, { fill?: string; stroke?: string }>;
  onPathClick: (pathId: string, svgId: string) => void;
  isPathSelectionActive: boolean;
  hiddenPaths: Record<string, boolean>; // Update this line
  isBrokenRedStrokeActive: boolean;
  isGradientEffectActive: boolean; // Add this prop
  isGradientBottomEffectActive: boolean; // Add this prop

}
export const DentalSvg: React.FC<DentalSvgProps> = ({
  svgData,
  highlightedPaths,
  onPathClick,
  isPathSelectionActive,
  isHidingMode,
  hiddenPaths,
  isBrokenRedStrokeActive, // Add this prop
  isGradientEffectActive, // Add this prop
  isGradientBottomEffectActive, // Add this prop
}) => {
  // États pour les cases à cocher cachées
  const [isToothSelected, setIsToothSelected] = useState(false);
  const [selectedToothParts, setSelectedToothParts] = useState<Set<string>>(new Set());

  const activePaths = svgData.paths.filter(
    (path) => !hiddenPaths[`${svgData.svg_id}-${path.id}`] // Only show paths that are not hidden
  );

  return (
    <svg
      version="1.1"
      x="0px"
      y="0px"
      viewBox={svgData.position}
      className={isHidingMode ? "h-[200px] bg-[#F2F5F8] border-r-[1.5px] border-dashed z-20" : "h-[200px] z-20"}
    >
      <style jsx>{`
        .st0 { fill: #f5f5f5; stroke: #c2c2c2; stroke-miterlimit: 10; }
        .st1 { fill: #efeabd; stroke: #c2c2c2; stroke-miterlimit: 10; }
        .st2 { fill: #aca77b; stroke: #c2c2c2; stroke-miterlimit: 10; }
        .st3 { fill: #ddd8b1; stroke: #c2c2c2; stroke-miterlimit: 10; }
        .st4 { fill: #e8e8e8; stroke: #c2c2c2; stroke-miterlimit: 10; }
        .st5 { fill: #ACA77B; stroke: #c2c2c2; stroke-miterlimit: 10; }
        .st6 { fill: none; stroke: #000000; stroke-miterlimit: 10; }
        .st7 { fill: none; stroke: #000000; stroke-miterlimit: 10; }
        .st8 { fill: #FFFFFF; stroke: #000000; stroke-miterlimit: 10; }
        .st9 { fill: #03baf2;  }
        .st10 { fill: none; stroke: #03baf2; stroke-miterlimit: 10; }
        .st11 { stroke:#f9f4f4;fill:url(#DegradeTOP); }
        .st018 {stroke:#f9f4f4; }
        .st180 { stroke:#f9f4f4;fill:url(#DegradeBottom); }
        .st12{stroke-miterlimit:10;}
        .st13{fill:none;}
        .st14{fill:#fa2222;}
        .st15{fill:url(#Dégradé_sans_nom);stroke:#f9f4f4;stroke-miterlimit:10;}
        .st16{fill:none;stroke-width:2px;,stroke-miterlimit:10;stroke:#fc5254;}
        .st17{stroke-miterlimit:10;stroke:#fc5254;fill:#f70606;}
        .st18{fill:#f90606;stroke:#f90606;stroke-miterlimit:10;}
        .st19{fill:none;stroke-miterlimit:10;stroke:#fc0606;}
        .st20{fill:#fc0606;stroke-miterlimit:10;}
        .st21{fill: none;stroke: #f70808;stroke-dasharray:5,5 !important;}
        .st22{ fill: #f70808;}
        .st23{stroke:#c2c2c2;stroke-miterlimit:10;fill:#9e9b9b;stroke-width:0.5px;}
        // .st24{ stroke: #c2c2c2} fill-opacity: 0.04;
         .st24{fill:#F5F5F5;stroke: #F60002; stroke-dasharray: 5,5}
         .st25{ fill: rgba(230, 233, 236, 0.04) !important;stroke: #F60002 !important; stroke-dasharray: 5,5}
        .st26{ stroke-miterlimit:10;stroke:#c2c2c2;fill-rule:evenodd;fill:#E6E9EC;}
        .st27{fill:#f5f5f5;stroke:#c2c2c2;stroke-miterlimit:10;}
        .st34{stroke-miterlimit:10;stroke:#c2c2c2;fill-rule:evenodd;fill:#E6E9EC;}

        .st43{fill: #F7D000; stroke: #E8B903; stroke-miterlimit: 10;}
        .st50{fill: #f70808;stroke: #f70808;}
        .st67{fill: #c2c2c2;stroke:rgb(141, 141, 141);stroke-miterlimit: 10;}
      `}</style>
      {/* Define the gradients */}
      <linearGradient id="DegradeTOP" x1="25.24" y1="108.07" x2="25.24" y2="69.59" gradientUnits="userSpaceOnUse">
        <stop offset="0" stopColor="#fff" stopOpacity="0" />
        <stop offset="0.09" stopColor="#fff1f1" stopOpacity="0.05" />
        <stop offset="0.26" stopColor="#fecece" stopOpacity="0.2" />
        <stop offset="0.5" stopColor="#fe9494" stopOpacity="0.43" />
        <stop offset="0.79" stopColor="#fd4646" stopOpacity="0.74" />
        <stop offset="1" stopColor="#fc0606" />
      </linearGradient>


     <linearGradient id="DegradeBottom" x1="25.24" y1="60.59" x2="25.24" y2="96.07" gradientUnits="userSpaceOnUse">
  <stop offset="0" stopColor="#fff" stopOpacity="0" />
  <stop offset="0.09" stopColor="#fff1f1" stopOpacity="0.05" />
  <stop offset="0.26" stopColor="#fecece" stopOpacity="0.2" />
  <stop offset="0.5" stopColor="#fe9494" stopOpacity="0.43" />
  <stop offset="0.79" stopColor="#fd4646" stopOpacity="0.74" />
  <stop offset="1" stopColor="#fc0606" /> </linearGradient>
      {/* //DegradeBottom */}
      {activePaths.map((path,index) => {
        // Apply broken red stroke for path.id === 1 when the effect is active
        const isPath1 = path.id === "1";
        const isPath18 = path.id === "18";
        const strokeStyle = isPath1 && isBrokenRedStrokeActive
          ? { stroke: "red", strokeDasharray: "5,5" }
          : {};
 // Apply gradient fill for path.id === 1 if the gradient effect is active for this SVG
 const gradientStyle = isPath18 && isGradientEffectActive
 ? { fill: "url(#DegradeTOP)" }
 : {};
 // Apply gradient bottom fill for path.id === 1 if the gradient bottom effect is active for this SVG
 const gradientBottomStyle = isPath18 && isGradientBottomEffectActive
  ? { fill: "url(#DegradeBottom)" } // Ensure this matches the gradient ID
  : {};
        return (
          <React.Fragment key={index}>
            <path
              id={path.id}
              d={path.path}
              className={path.code}
              style={{
                ...path.style,
                ...highlightedPaths[`${svgData.svg_id}-${path.id}`],
                ...strokeStyle, // Apply broken red stroke if applicable
                ...gradientStyle, // Apply gradient fill if applicable
                ...gradientBottomStyle, // Apply gradient bottom fill if applicable
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (isPathSelectionActive) {
                  onPathClick(path.id, svgData.svg_id);
                }
              }}
              transform={path.transforms ?? undefined}
            />
            {path.polygon && (
              <polygon
                points={path.polygon}
                className={path.code}
                style={{
                  ...path.style,
                  ...highlightedPaths[`${svgData.svg_id}-${path.id}`],
                  ...strokeStyle, // Apply broken red stroke if applicable
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  if (isPathSelectionActive) {
                    onPathClick(path.id, svgData.svg_id);
                  }
                }}
                transform={path.transforms ?? undefined}
              />
            )}
            {path.rect && (
              <rect
                x={path.rect.x}
                y={path.rect.y}
                width={path.rect.width}
                height={path.rect.height}
                className={path.code}
                style={{
                  ...path.style,
                  ...highlightedPaths[`${svgData.svg_id}-${path.id}`],
                  ...strokeStyle, // Apply broken red stroke if applicable
                }}
                transform={path.transforms}
                onClick={(e) => {
                  e.stopPropagation();
                  if (isPathSelectionActive) {
                    onPathClick(path.id, svgData.svg_id);
                  }
                }}
              />
            )}
          </React.Fragment>
        );
      })}

      {/* Case à cocher cachée pour toute la dent */}
      <foreignObject x="2" y="2" width="20" height="20">
        <input
          id={`tooth-checkbox-${svgData.svg_id}`}
          type="checkbox"
          checked={isToothSelected}
          onChange={(e) => setIsToothSelected(e.target.checked)}
          data-tooth-id={svgData.svg_id}
          data-tooth-secondary-id={`secondary-${svgData.svg_id}`}
          style={{
            opacity: 0,
            position: 'absolute',
            width: '16px',
            height: '16px',
            cursor: 'pointer',
            zIndex: 100,
            display: 'none'
          }}
        />
      </foreignObject>

      {/* Cases à cocher cachées pour toutes les parties des dents */}
      {activePaths.map((path, index) => (
        <foreignObject
          key={`checkbox-${path.id}`}
          x={5 + (index % 4) * 15}
          y={25 + Math.floor(index / 4) * 15}
          width="12"
          height="12"
        >
          <input
            type="checkbox"
            checked={selectedToothParts.has(path.id)}
            onChange={(e) => {
              const newSelected = new Set(selectedToothParts);
              if (e.target.checked) {
                newSelected.add(path.id);
              } else {
                newSelected.delete(path.id);
              }
              setSelectedToothParts(newSelected);
            }}
            style={{
              opacity: 0,
              position: 'absolute',
              width: '10px',
              height: '10px',
              cursor: 'pointer',
              zIndex: 99
            }}
            onMouseEnter={(e) => (e.target as HTMLInputElement).style.opacity = '0.6'}
            onMouseLeave={(e) => (e.target as HTMLInputElement).style.opacity = selectedToothParts.has(path.id) ? '1' : '0'}
          />
        </foreignObject>
      ))}
    </svg>
  );
};