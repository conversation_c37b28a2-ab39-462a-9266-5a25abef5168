import { notifications } from '@mantine/notifications';

// Define a custom API error interface
export interface ApiError {
  response?: {
    status?: number;
    data?: {
      detail?: string;
      code?: string;
      message?: string;
      [key: string]: unknown;
    };
  };
  request?: unknown;
  message?: string;
  config?: {
    url?: string;
    [key: string]: unknown;
  };
  isAxiosError?: boolean;
  code?: string;
}

// Define error categories
export enum ErrorCategory {
  PAYMENT = 'payment',
  AUTHENTICATION = 'authentication',
  NETWORK = 'network',
  SERVER = 'server',
  VALIDATION = 'validation',
  UNKNOWN = 'unknown',
}

// Define error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Define a type for context values
export type ContextValue = string | number | boolean | null | undefined | ContextObject | ContextArray;
export interface ContextObject {
  [key: string]: ContextValue;
}
export type ContextArray = ContextValue[];

// Interface for error tracking
export interface ErrorTrackingOptions {
  category: ErrorCategory;
  severity: ErrorSeverity;
  message?: string;
  code?: string;
  context?: ContextObject;
  user?: string;
  notify?: boolean;
  notifyAdmin?: boolean;
}

// Interface for parsed API error
export interface ParsedApiError {
  message: string;
  details: string | ContextObject;
  category: ErrorCategory;
  severity: ErrorSeverity;
  code?: string;
  context?: ContextObject;
}

/**
 * Parse an API error response into a structured format
 */
function parseApiError(error: ApiError | unknown): ParsedApiError {
  // Default error structure
  const parsedError: ParsedApiError = {
    message: 'An unexpected error occurred. Please try again.',
    details: {},
    category: ErrorCategory.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
  };

  // No error object
  if (!error) {
    return parsedError;
  }

  // Type guard to check if error is an ApiError
  function isApiError(err: unknown): err is ApiError {
    return typeof err === 'object' && err !== null && ('response' in err || 'request' in err || 'message' in err);
  }

  // If it's not an ApiError, return default error
  if (!isApiError(error)) {
    if (typeof error === 'string') {
      parsedError.message = error;
    } else if (error instanceof Error) {
      parsedError.message = error.message;
    }
    return parsedError;
  }

  // Handle axios error responses
  if (error.response) {
    const status = error.response.status || 0;
    const data = error.response.data;

    // Set category based on status code
    if (status === 401 || status === 403) {
      parsedError.category = ErrorCategory.AUTHENTICATION;
    } else if (status === 400 || status === 422) {
      parsedError.category = ErrorCategory.VALIDATION;
    } else if (status >= 500) {
      parsedError.category = ErrorCategory.SERVER;
    }

    // Set severity based on status code
    if (status >= 500) {
      parsedError.severity = ErrorSeverity.HIGH;
    } else if (status === 401 || status === 403) {
      parsedError.severity = ErrorSeverity.MEDIUM;
    }

    // Extract message and details from response data
    if (data) {
      // Handle string response
      if (typeof data === 'string') {
        parsedError.message = data;
      }
      // Handle object with detail field
      else if (data.detail) {
        parsedError.message = data.detail;
      }
      // Handle validation errors (field-specific errors)
      else if (typeof data === 'object') {
        // For payment errors, provide a more specific message
        if (parsedError.category === ErrorCategory.VALIDATION &&
            (data.payment || data.transaction_id || data.card)) {
          parsedError.category = ErrorCategory.PAYMENT;
          parsedError.message = 'Payment validation failed. Please check your payment details.';
        }

        // Format field errors into a readable message
        const fieldErrors = Object.entries(data)
          .map(([field, errors]) => {
            if (Array.isArray(errors)) {
              return `${field}: ${errors.join(', ')}`;
            }
            return `${field}: ${errors}`;
          })
          .join('\n');

        if (fieldErrors.length > 0) {
          parsedError.details = fieldErrors;
        }
      }
    }

    // Set error code if available
    if (data && data.code) {
      parsedError.code = data.code;
    } else {
      parsedError.code = `HTTP_${status}`;
    }
  }
  // Handle network errors
  else if (error.request) {
    parsedError.message = 'Network error. Please check your internet connection.';
    parsedError.category = ErrorCategory.NETWORK;
    parsedError.severity = ErrorSeverity.MEDIUM;
    parsedError.code = 'NETWORK_ERROR';
  }
  // Handle other errors
  else if (error.message) {
    parsedError.message = error.message;
  }

  // For payment-specific errors
  if (error.config && error.config.url &&
      (error.config.url.includes('/payment') ||
       error.config.url.includes('/subscription'))) {
    parsedError.category = ErrorCategory.PAYMENT;
  }

  return parsedError;
}

/**
 * Track an error for analytics and monitoring
 */
function trackError(options: ErrorTrackingOptions): void {
  const {
    category,
    severity,
    message,
    code,
    context,
    user,
    notify = true,
    notifyAdmin = false
  } = options;

  // Log error to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error(`[${category.toUpperCase()}][${severity.toUpperCase()}] ${message || 'Error occurred'}`, {
      code,
      context,
      user,
    });
  }

  // In a real application, you would send this to your error tracking service
  // Example: Sentry.captureException(error, { tags: { category, severity } });

  // Show notification to user if requested
  if (notify) {
    notifications.show({
      title: category === ErrorCategory.PAYMENT ? 'Payment Failed' : 'Error',
      message: message || 'An unexpected error occurred. Please try again.',
      color: severity === ErrorSeverity.CRITICAL ? 'red' : 'orange',
      // Skip icon to avoid JSX issues in TypeScript
      autoClose: 5000,
    });
  }

  // Notify admin in a real application
  if (notifyAdmin && severity === ErrorSeverity.CRITICAL) {
    // Example: Send email to admin or trigger alert
    console.log('Admin notification would be sent for critical error');
  }
}

/**
 * Handle payment-specific errors
 */
function handlePaymentError(error: ApiError | unknown): ParsedApiError {
  const parsedError = parseApiError(error);

  // Override category to ensure it's treated as a payment error
  parsedError.category = ErrorCategory.PAYMENT;

  // Track the error
  trackError({
    category: ErrorCategory.PAYMENT,
    severity: parsedError.severity,
    message: parsedError.message,
    code: parsedError.code,
    context: { details: parsedError.details },
    notify: true,
  });

  return parsedError;
}

// Export the error service functions
const errorService = {
  parseApiError,
  trackError,
  handlePaymentError
};

export default errorService;
