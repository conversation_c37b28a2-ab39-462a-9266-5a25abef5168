    {controls.map((control) => (
        <Tooltip
          key={control.id}
          label={control.tooltip}
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Button
            styles={{
              root: {
                position: 'relative',
                color: 'white',
                height: '35px',
                width: '35px',
                padding: 0,
                borderRadius: '0.5rem'
              },
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
              }}
            >
              <span
                className={
                  activeButton === control.id
                    ? "block h-[35px] w-[35px] rounded-md bg-[#dc3545] hover:bg-[#dc3545]"
                    : "block h-[35px] w-[35px] rounded-md bg-[#5A5A5A] hover:bg-[#dc3545]"
                }
                onClick={() => {
                  onButtonClick(control.id);
                  onTargetPathChange(control.pathId);
                }}
              >
                <div style={{ padding: '4px', width: '100%', height: '100%' }}>
                  {control.icon}
                </div>
              </span>
            </div>
            <span
              style={{
                position: 'absolute',
                bottom: '0px',
                right: '0px',
                fontSize: '8px',
                fontWeight: '800',
                backgroundColor: 'white',
                borderTopLeftRadius: '0.5rem',
                borderBottomRightRadius: '0.5rem',
                color: '#dc3545',
                padding: '3px 0px 1px 2px',
              }}
              className="h-[14px] w-[14px]"
            >
              {control.shortCode}
            </span>
          </Button>
        </Tooltip>
      ))}

      --------------------------
           {menuControls.map((menuControl) => (
<Menu key={menuControl.id} shadow="md" width={210} trigger="hover" openDelay={100} closeDelay={400}>
              <Tooltip
                label={menuControl.tooltip}
                withArrow
                className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                >
                <Menu.Target>
                <Button
                styles={{
                  root: {
                    position: 'relative',
                    color: 'white',
                    height: '35px', // Adjust button height
                    width: '35px',  // Adjust button width
                    padding: 0,
                    borderRadius: '0.5rem',
                  },
                }}
              >
            {/* SVG in the middle */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
              }}
            >
             <span
                      className={
                        menuControl.options.some(option => activeButton === option.id)
                          ? "block h-[35px] w-[35px] rounded-md bg-[#28a745] p-1 hover:bg-[#28a745]"
                          : "block h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#28a745]"
                      }
                    >
           {menuControl.icon}
                  </span>
            </div>
            <span
              style={{
                position: 'absolute',
                bottom: '0px',
                right: '0px',
                fontSize: '8px',
                fontWeight: '800',
                backgroundColor: 'white',
                // borderRadius:'0.125rem' ,
                borderTopLeftRadius: '0.5rem' ,
                borderBottomRightRadius: '0.5rem' ,
                color:'#3799CE',
                padding:'3px  0px 1px 2px' ,
              }}
              className="h-[14px] w-[14px] "
            >
             {menuControl.shortCode}
            </span>
          </Button>
            </Menu.Target>
              </Tooltip>
              <Menu.Dropdown >
              <Menu.Label>{menuControl.label}</Menu.Label>
              {menuControl.options.map((option) => (
              <Menu.Item    key={option.id} className={
                 activeButton === option.id
                             ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                             : " h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
                         }
              onClick={() => {
                    onButtonClick(option.id);
                    onTargetPathChange(option.pathId);
                  }}
              >

               <Group  className="h-[28px] w-[186.4px] " >
                                    <Radio
                                        checked={activeButton === option.id}
                                        onChange={(event) =>
                                        setChecked(event.currentTarget.checked)
                                        }
                                        icon={CheckIcon}
                                    />
                                    <div className="flex">
                                    <Avatar
                                      color="blue"
                                      radius="sm"
                                      style={{ width: "28px", height: "28px" }}
                                      px={0.5}
                                    >
                                       {option.icon}
                                    </Avatar>
                                    <Text fw={500} ml={6}>  {option.label}</Text>
                                        </div>
                                </Group>
              </Menu.Item>
            ))}
              </Menu.Dropdown>
            </Menu>
            ))}