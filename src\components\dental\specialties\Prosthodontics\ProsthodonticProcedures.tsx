// frontend/dental_medicine/src/components/content/dental/specialties/Prosthodontics/ProsthodonticProcedures.tsx

import React, { useState, useMemo } from 'react';
import { Table, Checkbox, Text, Badge, Button, Group, TextInput, Select } from '@mantine/core';
import { IconSearch, IconFilter } from '@tabler/icons-react';
import { ProsthodonticProcedure, ModificationState, SVGPathStyle } from '../../shared/types';

interface ProsthodonticProceduresProps {
  type: 'all' | 'planned' | 'completed';
  modificationState: ModificationState;
  onModificationChange?: (
    svgId: string,
    pathId: string,
    isVisible: boolean,
        highlightedPaths?: Record<string, SVGPathStyle>
  ) => Promise<void>;
}

export const ProsthodonticProcedures: React.FC<ProsthodonticProceduresProps> = ({
  type,

  onModificationChange
}) => {

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Données des procédures prosthodontiques (mémorisées pour éviter les re-rendus)
  const allProsthodonticProcedures = useMemo((): ProsthodonticProcedure[] => [
    {
      position: 1,
      mass: 1.008,
      symbol: 'CER',
      name: 'Couronne Céramique',
      type: 'crown',
      pathId: '41',
      cost: 800,
      duration: 120,
      category: 'couronne',
      material: 'ceramic',
      warranty: 60
    },
    {
      position: 2,
      mass: 4.003,
      symbol: 'CZR',
      name: 'Couronne Zircone',
      type: 'crown',
      pathId: '42',
      cost: 900,
      duration: 120,
      category: 'couronne',
      material: 'zirconia',
      warranty: 84
    },
    {
      position: 3,
      mass: 6.941,
      symbol: 'BRI',
      name: 'Bridge 3 Éléments',
      type: 'bridge',
      pathId: '44',
      cost: 2100,
      duration: 180,
      category: 'bridge',
      material: 'ceramic',
      warranty: 60
    },
    {
      position: 4,
      mass: 9.012,
      symbol: 'IMP',
      name: 'Implant Titane',
      type: 'implant',
      pathId: '50',
      cost: 1200,
      duration: 90,
      category: 'implant',
      material: 'titanium',
      warranty: 120
    },
    {
      position: 5,
      mass: 10.811,
      symbol: 'PAR',
      name: 'Prothèse Partielle',
      type: 'denture',
      pathId: '47',
      cost: 600,
      duration: 150,
      category: 'prothese',
      material: 'metal',
      warranty: 36
    },
    {
      position: 6,
      mass: 12.011,
      symbol: 'COM',
      name: 'Prothèse Complète',
      type: 'denture',
      pathId: '48',
      cost: 1000,
      duration: 200,
      category: 'prothese',
      material: 'ceramic',
      warranty: 48
    },
    {
      position: 7,
      mass: 14.007,
      symbol: 'INL',
      name: 'Inlay Céramique',
      type: 'inlay_onlay',
      pathId: '43',
      cost: 400,
      duration: 90,
      category: 'inlay',
      material: 'ceramic',
      warranty: 36
    },
    {
      position: 8,
      mass: 15.999,
      symbol: 'ONL',
      name: 'Onlay Or',
      type: 'inlay_onlay',
      pathId: '45',
      cost: 600,
      duration: 90,
      category: 'inlay',
      material: 'gold',
      warranty: 120
    }
  ], []);

  // Filtrer les procédures selon le type
  const filteredProcedures = useMemo(() => {
    let procedures = allProsthodonticProcedures;

    // Filtrer par type (all, planned, completed)
    if (type === 'planned') {
      procedures = procedures.filter(proc => proc.position <= 4);
    } else if (type === 'completed') {
      procedures = procedures.filter(proc => proc.position > 4);
    }

    // Filtrer par terme de recherche
    if (searchTerm) {
      procedures = procedures.filter(proc =>
        proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrer par catégorie
    if (filterCategory !== 'all') {
      procedures = procedures.filter(proc => proc.category === filterCategory);
    }

    return procedures;
  }, [type, searchTerm, filterCategory, allProsthodonticProcedures]);

  // Gestionnaire de sélection de ligne
  const handleRowSelection = (position: number) => {
    setSelectedRows(prev =>
      prev.includes(position)
        ? prev.filter(p => p !== position)
        : [...prev, position]
    );
  };

  // Gestionnaire d'application de procédure
  const handleApplyProcedure = async (procedure: ProsthodonticProcedure) => {
    if (onModificationChange && procedure.pathId) {
      try {
        await onModificationChange('1', procedure.pathId, true, {
          [`1-${procedure.pathId}`]: {
            fill: getColorForProcedure(procedure.type),
            stroke: '#dc3545',
            strokeWidth: 1
          }
        });
        console.log(`✅ Procédure prosthodontique appliquée: ${procedure.name}`);
      } catch (error) {
        console.error(`❌ Erreur application procédure: ${procedure.name}`, error);
      }
    }
  };

  // Obtenir la couleur selon le type de procédure
  const getColorForProcedure = (type: ProsthodonticProcedure['type']): string => {
    switch (type) {
      case 'crown': return '#ffe6e6';
      case 'bridge': return '#ffcccc';
      case 'denture': return '#ffb3b3';
      case 'implant': return '#ff9999';
      case 'inlay_onlay': return '#ff8080';
      default: return '#f8f9fa';
    }
  };

  // Obtenir le badge de statut
  const getStatusBadge = () => {
    if (type === 'completed') {
      return <Badge color="green" size="sm">Terminé</Badge>;
    } else if (type === 'planned') {
      return <Badge color="blue" size="sm">Planifié</Badge>;
    } else {
      return <Badge color="gray" size="sm">Disponible</Badge>;
    }
  };

  // Obtenir le badge de matériau
  const getMaterialBadge = (material: string) => {
    const colors: Record<string, string> = {
      'ceramic': 'blue',
      'zirconia': 'purple',
      'metal': 'gray',
      'titanium': 'dark',
      'gold': 'yellow'
    };
    return <Badge color={colors[material] || 'gray'} variant="light" size="xs">{material}</Badge>;
  };

  const rows = filteredProcedures.map((procedure) => (
    <Table.Tr
      key={procedure.position}
      bg={selectedRows.includes(procedure.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(procedure.position)}
          onChange={() => handleRowSelection(procedure.position)}
        />
      </Table.Td>
      <Table.Td>
        <Text fw={500} size="sm">{procedure.symbol}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.name}</Text>
      </Table.Td>
      <Table.Td>
        {procedure.material && getMaterialBadge(procedure.material)}
      </Table.Td>
      <Table.Td>
        {getStatusBadge()}
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.cost}€</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{procedure.duration}min</Text>
      </Table.Td>
      <Table.Td>
        <Text size="xs" c="dimmed">
          {procedure.warranty} mois
        </Text>
      </Table.Td>
      <Table.Td>
        <Button
          size="xs"
          variant="light"
          color="red"
          onClick={() => handleApplyProcedure(procedure)}
        >
          Appliquer
        </Button>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <div className="p-4">
      {/* Filtres et recherche */}
      <Group mb="md">
        <TextInput
          placeholder="Rechercher une procédure..."
          leftSection={<IconSearch size={16} />}
          value={searchTerm}
          onChange={(event) => setSearchTerm(event.currentTarget.value)}
          style={{ flex: 1 }}
        />
        <Select
          placeholder="Catégorie"
          leftSection={<IconFilter size={16} />}
          data={[
            { value: 'all', label: 'Toutes' },
            { value: 'couronne', label: 'Couronnes' },
            { value: 'bridge', label: 'Bridges' },
            { value: 'prothese', label: 'Prothèses' },
            { value: 'implant', label: 'Implants' },
            { value: 'inlay', label: 'Inlay/Onlay' }
          ]}
          value={filterCategory}
          onChange={(value) => setFilterCategory(value || 'all')}
          w={150}
        />
      </Group>

      {/* Statistiques */}
      <Group mb="md">
        <Text size="sm" c="dimmed">
          {filteredProcedures.length} procédure(s) • {selectedRows.length} sélectionnée(s)
        </Text>
        {selectedRows.length > 0 && (
          <Button size="xs" variant="light" color="red">
            Appliquer la sélection
          </Button>
        )}
      </Group>

      {/* Tableau des procédures */}
      <Table striped highlightOnHover>
        <Table.Thead>
          <Table.Tr>
            <Table.Th />
            <Table.Th>Code</Table.Th>
            <Table.Th>Procédure</Table.Th>
            <Table.Th>Matériau</Table.Th>
            <Table.Th>Statut</Table.Th>
            <Table.Th>Coût</Table.Th>
            <Table.Th>Durée</Table.Th>
            <Table.Th>Garantie</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>

      {filteredProcedures.length === 0 && (
        <Text ta="center" c="dimmed" mt="xl">
          Aucune procédure trouvée
        </Text>
      )}
    </div>
  );
};

export default ProsthodonticProcedures;
