
"use client";
import { useState } from "react";
import React from "react";

import Icon from '@mdi/react';
import { mdiCalendarOutline } from '@mdi/js';
import MetaSeo from"./MetaSeo"

import Pathologies from "./Pathologies"
import PatientVisitsAssures from "./Patient-visits-assures"
import Balance_Agee from './Balance_Agee'

//import UtilisationDesMedicaments from"./Utilisation des medicaments"
import UtilisationDesMedicamentsAPI from "./Utilisation_des_medicaments_api"
import Etat from "./Etat";
import "~/styles/tab.css";

function  AppointmentsPage() {
 
  const [toggleState, setToggleState] = useState(1);
 

const icons = [
  { icon: <Icon path={mdiCalendarOutline} size={1} key="Pathologies" />, label: "Pathologies" },
   { icon: <Icon path={mdiCalendarOutline} size={1} key="PatientVisitsAssures" />, label: "PatientVisitsAssures" },
   { icon: <Icon path={mdiCalendarOutline} size={1} key="Balance_Agee" />, label: "Balance_Agee" },
 { icon: <Icon path={mdiCalendarOutline} size={1} key="Etat" />, label: "Etat" },

//  { icon: <CalendarDays style={iconStyle} key="UtilisationDesMedicaments"/>, label: "Utilisation Des Medicaments" },
 { icon: <Icon path={mdiCalendarOutline} size={1} key="UtilisationDesMedicamentsAPI" />, label: "Utilisation Des MedicamentsAPI" },
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<Pathologies/> )
   case 2:
      return (<PatientVisitsAssures/> )
    case 3:
      return (<Balance_Agee/> )
  case 4 :
      return (<div className="w-full"><Etat/></div> )
      //  case 11:
      // return (<UtilisationDesMedicaments/> )
       case 5:
      return (<UtilisationDesMedicamentsAPI/> )
    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 
