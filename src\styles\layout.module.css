

/* navbar styles */
.navbar {
    width: 62px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid  var(--border-color);
    background-color: var(--mantine-color-body); 
    /* color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0)); */
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
    .navbarMain {
      flex: 1;
      /* margin-top: 50px; */
      
    }
    .link {
      width: 40px;
      height: 40px;
      margin-left: 2.5px;
      margin-bottom: 10px;
      border-radius: var(--mantine-radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
    
      &:hover {
        background-color:var(--bg-nav-hover);
        height: 40px;
        width: 40px;
        margin-left: 2.5px;
        color: #3799ce;
      }
      &[data-active] {
        &,
        &:hover {
          background-color: var(--blue-color);
          color: var(--text-daisy-white); 
          height: 40px;
           width: 40px;
          margin-left: 2.5px;
          /* background-color: var(--mantine-color-blue-light);
          color: var(--mantine-color-blue-light-color); */
        }
      }
    }
    :-moz-ui-invalid.header {
    /* height: 56px; */
    margin-bottom: 120px;
    background-color: var(--mantine-color-body);
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    padding-left: var(--mantine-spacing-md);
    padding-right: var(--mantine-spacing-md);
  }
  .inner {
    /* height: 56px; */
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .link {
    display: block;
    line-height: 1;
    padding: 8px 10px;
    border-radius: var(--mantine-radius-sm);
    text-decoration: none;
    color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
    font-size: var(--mantine-font-size-sm);
    font-weight: 500;
    @mixin hover {
      background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
    }
  }
  .navbarDesktop {
    display: none;
  }
  
  @media (min-width: 768px) {
    .navbarDesktop {
      display: flex;
    }
  }
  .navbarHidden {
    display: none;
  }
    /* header styles */
  .header {
   height: 56px; 
    background-color: var(--mantine-color-body);
    /* border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)); */
    padding-left: var(--mantine-spacing-md);
    padding-right: var(--mantine-spacing-md);
    width: 100%;
   z-index: 999;
    
  }
  
  @media (min-width: 768px) {
    .header {
      width: 600px;
      margin-left: 250px;
      margin-right: 20px; 
      /* height: 50px; */
      padding: 0px;
      display: none;
      
    }
    .inner {
      height: 40px;
       display: flex;
     /* justify-content: space-between; */
     align-items: center;
     /* background-color: blueviolet; */
   }
  }
  
  .inner {
      height: 56px;  
      display: flex;
    justify-content: space-between;
    align-items: center;
    
  }
    .link_H {
      display: block;
      line-height: 1;
      padding: 8px 12px;
      border-radius: var(--mantine-radius-sm);
      text-decoration: none;
      color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
      font-size: var(--mantine-font-size-sm);
      font-weight: 500;
      @mixin hover {
        background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
      }
    }
    /* Footer styles */
  .footer {
    height: 60px;
    background-color: var(--mantine-color-body);
    border-top: 1px solid var(--border-color);
    padding: var(--mantine-spacing-md);
    margin-left: 56px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 56px;
    padding-left: var(--mantine-spacing-md);
    padding-right: var(--mantine-spacing-md);
    z-index: 101;
  }
  
  .footerInner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }
  
  .footerLinks {
    display: flex;
    gap: var(--mantine-spacing-md);
  }
  
  .footerLink {
    color: light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-1));
    font-size: var(--mantine-font-size-sm);
    text-decoration: none;
    &:hover {
      text-decoration: underline;
      color: var(--mantine-color-blue-6);
    }
  }
  
  .footerCopyright {
    font-size: var(--mantine-font-size-xs);
    color: light-dark(var(--mantine-color-gray-5), var(--mantine-color-dark-2));
  }
  .headerWithSidebar {
    margin-left: 240px;
  }
  
  .headerWithoutSidebar {
    margin-left: 56px;
    @media (max-width: 768px) {
      margin-left: 0px;
    }
  }
  
  .mobileMenu {
    display: none;
    padding: var(--mantine-spacing-md);
    background-color: var(--mantine-color-body);
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    position: absolute;
    width: 100%;
    z-index: 9999;
  }
  
  @media (max-width: 48em) {
    .mobileMenu {
      display: block;
    }
  }
  
  .mobileLinks {
    display: flex;
    flex-direction: column;
    gap: var(--mantine-spacing-xs);
  }
  /* SideNavbar styles */
  .navbarSide {
    background-color: light-dark(var(--mantine-color-white), var(--mantine-color-dark-6));
    width: 240px;
    padding: var(--mantine-spacing-md);
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    border-right: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    height: 100vh; /* Couvre toute la hauteur de l'écran */
    position: fixed;
    top: 0;
    z-index: 99;
  }
  
  .headerSide {
    padding: var(--mantine-spacing-md);
    padding-top: 0;
    margin-left: calc(var(--mantine-spacing-md) * -1);
    margin-right: calc(var(--mantine-spacing-md) * -1);
    color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  }
  
  .linksSide {
    flex: 1;
    margin-left: calc(var(--mantine-spacing-md) * -1);
    margin-right: calc(var(--mantine-spacing-md) * -1);
  }
  
  .linksInnerSide {
    padding-top: var(--mantine-spacing-xl);
    padding-bottom: var(--mantine-spacing-xl);
  }
  .footerSide {
    margin-left: calc(var(--mantine-spacing-md) * -1);
    margin-right: calc(var(--mantine-spacing-md) * -1);
    border-top: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  }
  .pageContainer {
    display: flex;
    min-height: calc(100vh - 116px); /* Hauteur totale moins header et footer */
    position: relative;
  }
  .sidebarContainer {
    position: fixed;
    top: 0;
    left: 56px; /* Largeur de la navbar */
    height: 100vh; /* Couvre toute la hauteur de l'écran */
    width: 240px;
    background-color: white;
    border-right: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    z-index: 95;
    /* overflow-y: auto; */
    transition: transform 0.3s ease;
    padding-bottom: 60px; /* Ajoute un espace en bas pour éviter que le contenu ne soit caché par le pied de page */
  }
  .mainContent {
    flex: 1;
    margin-left: 56px; /* Largeur de la navbar */
    padding: 0px;
    transition: margin-left 0.3s ease;
    z-index: 10;
  }
  /* Pour les appareils mobiles */
  @media (min-width: 768px) {
    .mainContent {
     /*  margin-top: -50px; Pas de marge sur mobile */
      padding-left: 2px;
      z-index: 99;
    }
  }
  @media (max-width: 768px) {
    .mainContent {
      margin-left: 2px; /* Pas de marge sur mobile */
      margin-right: 2px;
    }
    
    .mainContentWithSidebar {
      margin-left: 0; /* Ajustement pour mobile quand la sidebar est visible */
    }
  }
  .mainContentWithSidebar {
    margin-left: 240px; /* Ajustez cette valeur à la largeur de votre barre latérale */
    
  }
  /* Ajuster la marge du contenu principal quand la sidebar est visible */
  .sidebarVisible .mainContent {
    margin-left: 246px; /* Largeur de la navbar + largeur de la sidebar */
  }
  .sidebarVisible {
    transform: translateX(0);
    opacity: 1;
    pointer-events: all;
    left: 0;
  }
  
  .sidebarHidden {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
  }
  .tabsList {
   
    width: 100%;
  }
  @media (max-width: 768px) {
    .tabsButton {
      width: 100% !important;
      
    }
  }
  .tabsButton{
    width: 75%;
  }

  @media (max-width: 768px) {
    .navBarButton {
     display: none;
     
    }
  }
  .navBarButton{
    width: 25%;
  }