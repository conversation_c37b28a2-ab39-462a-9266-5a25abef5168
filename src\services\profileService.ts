import api from '../lib/api';
import authService, { UserProfile } from './authService';
import { ProfileDataWithFiles } from '@/types/profile';

const API_BASE_URL = '/api/doctors';
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

/**
 * Service for managing doctor profile operations
 */
const profileService = {
  /**
   * Get the current doctor's profile
   */
  async getProfile(): Promise<UserProfile> {
    try {
      try {
        // First try the doctor-specific endpoint
        const response = await api.get(`${API_BASE_URL}/me/`);

        // Process image URLs
        const profileData = response.data;

        // Convert relative image URLs to absolute URLs
        if (profileData.profile_image && !profileData.profile_image.startsWith('http')) {
          profileData.profile_image = `${API_URL}${profileData.profile_image}`;
        }

        if (profileData.profile_image_medium && !profileData.profile_image_medium.startsWith('http')) {
          profileData.profile_image_medium = `${API_URL}${profileData.profile_image_medium}`;
        }

        if (profileData.profile_image_large && !profileData.profile_image_large.startsWith('http')) {
          profileData.profile_image_large = `${API_URL}${profileData.profile_image_large}`;
        }

        console.log('Processed profile image URLs:', {
          profile_image: profileData.profile_image,
          profile_image_medium: profileData.profile_image_medium
        });

        return profileData;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_doctorApiError) {
        console.log('Doctor-specific profile endpoint failed, falling back to auth service');

        // If the doctor-specific endpoint fails, fall back to the general auth endpoint
        const profile = await authService.getProfile();

        // Process image URLs from auth service
        if (profile.profile_image && !profile.profile_image.startsWith('http')) {
          profile.profile_image = `${API_URL}${profile.profile_image}`;
        }

        if (profile.profile_image_medium && !profile.profile_image_medium.startsWith('http')) {
          profile.profile_image_medium = `${API_URL}${profile.profile_image_medium}`;
        }

        if (profile.profile_image_large && !profile.profile_image_large.startsWith('http')) {
          profile.profile_image_large = `${API_URL}${profile.profile_image_large}`;
        }

        return profile;
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  },

  /**
   * Update the current doctor's profile
   * Handles both regular fields and file uploads
   */
  async updateProfile(data: Partial<ProfileDataWithFiles> | Record<string, string | number | boolean | File | null | undefined>): Promise<UserProfile> {
    try {
      // Check if we have any File objects that need to be handled with FormData
      const hasFiles = Object.values(data).some(value => value instanceof File);

      if (hasFiles) {
        // If we have files, use the dedicated image upload endpoint
        return await this.uploadProfileImages(data);
      } else {
        // Regular JSON update for non-file data
        try {
          // First try the doctor-specific endpoint
          const response = await api.patch(`${API_BASE_URL}/update_me/`, data);
          return response.data;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (_doctorApiError) {
          console.log('Doctor-specific update endpoint failed, falling back to auth service');

          // Try different endpoints for profile update
          try {
            // First try the UserDetailView endpoint
            const response = await api.patch(`/api/auth/me/`, data);
            return response.data;
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (_error) {
            console.log('First attempt failed, trying alternative endpoint');
            // If that fails, try the update_profile endpoint
            const response = await api.patch(`/api/auth/update_profile/`, data);
            return response.data;
          }
        }
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  /**
   * Upload profile images
   * Handles file uploads for profile images
   */
  async uploadProfileImages(data: Partial<ProfileDataWithFiles>): Promise<UserProfile> {
    try {
      // Create a FormData object for the file upload
      const formData = new FormData();

      // Add only the file fields to the FormData object
      if (data.profile_image instanceof File) {
        formData.append('profile_image', data.profile_image);
      }

      if (data.profile_image_medium instanceof File) {
        formData.append('profile_image_medium', data.profile_image_medium);
      }

      if (data.profile_image_large instanceof File) {
        formData.append('profile_image_large', data.profile_image_large);
      }

      // Add non-file fields to a separate object
      const nonFileData: Partial<UserProfile> = {};
      Object.entries(data).forEach(([key, value]) => {
        if (!(value instanceof File)) {
          // Use type assertion to handle the assignment
          (nonFileData as Record<string, unknown>)[key] = value;
        }
      });

      try {
        // First try to upload the images
        console.log('Uploading profile images...');

        // Use the general update endpoint with multipart/form-data
        // Add non-file fields to the FormData
        Object.entries(nonFileData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (typeof value === 'object') {
              formData.append(key, JSON.stringify(value));
            } else {
              formData.append(key, String(value));
            }
          }
        });

        // Try different endpoints for profile update with images
        let response;
        try {
          // First try the UserDetailView endpoint
          response = await api.patch(`/api/auth/me/`, formData);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (_error) {
          console.log('First attempt failed, trying alternative endpoint');
          // If that fails, try the update_profile endpoint
          response = await api.patch(`/api/auth/update_profile/`, formData);
        }

        return response.data;
      } catch (error) {
        console.error('Error uploading profile images:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error uploading profile images:', error);
      throw error;
    }
  },

  /**
   * Update profile image
   * Simplified method specifically for profile image upload
   */
  async updateProfileImage(formData: FormData): Promise<UserProfile> {
    try {
      console.log('Uploading profile image...');

      // Try different endpoints for profile image update
      let response;
      try {
        // First try the doctor-specific endpoint
        response = await api.patch(`${API_BASE_URL}/update_me/`, formData);
        console.log('Doctor-specific endpoint succeeded:', response.data);
      } catch (doctorApiError) {
        console.log('Doctor-specific endpoint failed, trying auth endpoints', doctorApiError);

        try {
          // Try the UserDetailView endpoint
          response = await api.patch(`/api/auth/me/`, formData);
          console.log('Auth /me endpoint succeeded:', response.data);
        } catch (error) {
          console.log('First auth attempt failed, trying alternative endpoint', error);
          // If that fails, try the update_profile endpoint
          response = await api.patch(`/api/auth/update_profile/`, formData);
          console.log('Auth update_profile endpoint succeeded:', response.data);
        }
      }

      // Log the response data to help with debugging
      console.log('Profile image upload successful, response data:', response.data);

      // Ensure we're returning a valid UserProfile object
      if (!response.data) {
        throw new Error('Server returned empty response');
      }

      // Convert relative image URLs to absolute URLs and add a timestamp to force image refresh
      if (response.data.profile_image) {
        // Check if the URL is relative (doesn't start with http)
        if (!response.data.profile_image.startsWith('http')) {
          response.data.profile_image = `${API_URL}${response.data.profile_image}?t=${Date.now()}`;
        } else {
          response.data.profile_image = `${response.data.profile_image}?t=${Date.now()}`;
        }
      }

      if (response.data.profile_image_medium) {
        // Check if the URL is relative (doesn't start with http)
        if (!response.data.profile_image_medium.startsWith('http')) {
          response.data.profile_image_medium = `${API_URL}${response.data.profile_image_medium}?t=${Date.now()}`;
        } else {
          response.data.profile_image_medium = `${response.data.profile_image_medium}?t=${Date.now()}`;
        }
      }

      console.log('Processed image URLs:', {
        profile_image: response.data.profile_image,
        profile_image_medium: response.data.profile_image_medium
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading profile image:', error);
      throw error;
    }
  },

  async updatePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      try {
        // First try the doctor-specific endpoint
        await api.post(`${API_BASE_URL}/change_password/`, {
          current_password: currentPassword,
          new_password: newPassword,
        });
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_doctorApiError) {
        console.log('Doctor-specific password endpoint failed, falling back to auth service');

        // Fall back to the general auth endpoint
        await api.post(`/api/auth/password/change/`, {
          current_password: currentPassword,
          new_password: newPassword,
        });
      }
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  },
};

export default profileService;
