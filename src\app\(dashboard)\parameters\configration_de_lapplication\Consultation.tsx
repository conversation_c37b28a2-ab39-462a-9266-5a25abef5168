import React, { useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Group,
  Text,
  Table,
  Switch,
  ActionIcon,
  Modal,
  TextInput,
  Stack,
  Badge,
  Tabs,
} from '@mantine/core';
import {
  IconPlus,
  IconStethoscope,
  IconEdit,
  IconArrowLeft,
  IconX,
  IconFileText,
  IconColorPicker,
  IconActivity,
  IconMedicalCross,
  IconBabyCarriage,
  IconFolder,
} from '@tabler/icons-react';

// Types pour les données
interface ConsultationTemplate {
  id: string;
  editionDate: string;
  title: string;
  color: string;
  isDefaultConsultation: boolean;
  isDefaultControl: boolean;
  isDisabled: boolean;
}

interface ModalFormData {
  title: string;
  color: string;
  activeTab: string;
  blocs: Array<{ id: string; name: string; color: string }>;
  procedures: Array<{ id: string; name: string }>;
}

const Consultation = () => {
  // États pour la gestion des données
  const [templates, setTemplates] = useState<ConsultationTemplate[]>([
    {
      id: '1',
      editionDate: '27/10/2023 12:45',
      title: 'Contrôle',
      color: '#af3a77',
      isDefaultConsultation: false,
      isDefaultControl: true,
      isDisabled: false,
    },
    {
      id: '2',
      editionDate: '08/04/2025 14:13',
      title: 'Consultation',
      color: '#126a8d',
      isDefaultConsultation: true,
      isDefaultControl: false,
      isDisabled: false,
    },
  ]);

  // États pour les modals
  const [modalOpened, setModalOpened] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ConsultationTemplate | null>(null);
  const [scrollDisabled, setScrollDisabled] = useState(false);

  // États pour le formulaire du modal
  const [formData, setFormData] = useState<ModalFormData>({
    title: '',
    color: '#4f3a77',
    activeTab: 'titre',
    blocs: [],
    procedures: [],
  });

  // États pour les modals de bloc et procédure
  const [newBlocModalOpened, setNewBlocModalOpened] = useState(false);
  const [newProcedureModalOpened, setNewProcedureModalOpened] = useState(false);
  const [newBlocName, setNewBlocName] = useState('');
  const [newBlocColor, setNewBlocColor] = useState('#ff0000');
  const [newProcedureName, setNewProcedureName] = useState('');

  // Fonctions pour gérer les modals
  const openNewModal = () => {
    setEditingTemplate(null);
    setFormData({
      title: '',
      color: '#4f3a77',
      activeTab: 'titre',
      blocs: [],
      procedures: [],
    });
    setModalOpened(true);
  };

  const openEditModal = (template: ConsultationTemplate) => {
    setEditingTemplate(template);
    setFormData({
      title: template.title,
      color: template.color,
      activeTab: 'titre',
      blocs: [],
      procedures: [],
    });
    setModalOpened(true);
  };

  const closeModal = () => {
    setModalOpened(false);
    setEditingTemplate(null);
  };

  // Fonctions pour gérer les switches
  const handleSwitchChange = (templateId: string, field: keyof ConsultationTemplate, value: boolean) => {
    setTemplates(templates.map(template => {
      if (template.id === templateId) {
        // Si on active une consultation par défaut, désactiver les autres
        if (field === 'isDefaultConsultation' && value) {
          return { ...template, [field]: value };
        }
        // Si on active un contrôle par défaut, désactiver les autres
        if (field === 'isDefaultControl' && value) {
          return { ...template, [field]: value };
        }
        return { ...template, [field]: value };
      }
      // Désactiver les autres si on active une consultation/contrôle par défaut
      if (field === 'isDefaultConsultation' && value) {
        return { ...template, isDefaultConsultation: false };
      }
      if (field === 'isDefaultControl' && value) {
        return { ...template, isDefaultControl: false };
      }
      return template;
    }));
  };

  // Fonctions pour gérer les blocs et procédures
  const handleAddBloc = () => {
    if (newBlocName.trim()) {
      const newBloc = {
        id: Date.now().toString(),
        name: newBlocName.trim(),
        color: newBlocColor,
      };
      setFormData(prev => ({
        ...prev,
        blocs: [...prev.blocs, newBloc]
      }));
      setNewBlocName('');
      setNewBlocColor('#ff0000');
      setNewBlocModalOpened(false);
    }
  };

  const handleAddProcedure = () => {
    if (newProcedureName.trim()) {
      const newProcedure = {
        id: Date.now().toString(),
        name: newProcedureName.trim(),
      };
      setFormData(prev => ({
        ...prev,
        procedures: [...prev.procedures, newProcedure]
      }));
      setNewProcedureName('');
      setNewProcedureModalOpened(false);
    }
  };

  const handleRemoveBloc = (id: string) => {
    setFormData(prev => ({
      ...prev,
      blocs: prev.blocs.filter(bloc => bloc.id !== id)
    }));
  };

  const handleRemoveProcedure = (id: string) => {
    setFormData(prev => ({
      ...prev,
      procedures: prev.procedures.filter(procedure => procedure.id !== id)
    }));
  };

  // Fonction de sauvegarde
  const handleSave = () => {
    if (editingTemplate) {
      // Mise à jour du template existant
      setTemplates(templates.map(template =>
        template.id === editingTemplate.id
          ? { ...template, title: formData.title, color: formData.color }
          : template
      ));
    } else {
      // Création d'un nouveau template
      const newTemplate: ConsultationTemplate = {
        id: Date.now().toString(),
        editionDate: new Date().toLocaleDateString('fr-FR') + ' ' + new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }),
        title: formData.title,
        color: formData.color,
        isDefaultConsultation: false,
        isDefaultControl: false,
        isDisabled: false,
      };
      setTemplates([...templates, newTemplate]);
    }
    closeModal();
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <IconStethoscope size={24} className="text-blue-600" />
            </div>
            <div>
              <Text size="xl" fw={600} className="text-gray-900">
                Modèles des consultations
              </Text>
            </div>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={openNewModal}
            className="bg-blue-500 hover:bg-blue-600 text-white"
            size="sm"
          >
            Nouveau
          </Button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 p-6">
        <Card shadow="sm" padding="lg" radius="md" className="bg-white">
          {/* Tableau */}
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Date de modification</Table.Th>
                <Table.Th>Titre</Table.Th>
                <Table.Th>Couleur</Table.Th>
                <Table.Th className="text-center">Consultation par défaut</Table.Th>
                <Table.Th className="text-center">Contrôle par défaut</Table.Th>
                <Table.Th className="text-center">Désactivé</Table.Th>
                <Table.Th></Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {templates.map((template) => (
                <Table.Tr key={template.id}>
                  <Table.Td>{template.editionDate}</Table.Td>
                  <Table.Td>{template.title}</Table.Td>
                  <Table.Td>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: template.color }}
                      />
                      <Text size="sm">{template.color}</Text>
                    </div>
                  </Table.Td>
                  <Table.Td className="text-center">
                    <Switch
                      checked={template.isDefaultConsultation}
                      onChange={(event) =>
                        handleSwitchChange(template.id, 'isDefaultConsultation', event.currentTarget.checked)
                      }
                      disabled={template.isDisabled}
                      color="blue"
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="text-center">
                    <Switch
                      checked={template.isDefaultControl}
                      onChange={(event) =>
                        handleSwitchChange(template.id, 'isDefaultControl', event.currentTarget.checked)
                      }
                      disabled={template.isDisabled}
                      color="blue"
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td className="text-center">
                    <Switch
                      checked={template.isDisabled}
                      onChange={(event) =>
                        handleSwitchChange(template.id, 'isDisabled', event.currentTarget.checked)
                      }
                      color="blue"
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td>
                    <ActionIcon
                      variant="subtle"
                      color="blue"
                      onClick={() => openEditModal(template)}
                      disabled={template.isDisabled}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>
      </div>

      {/* Modal pour créer/éditer un modèle */}
      <Modal.Root opened={modalOpened} onClose={closeModal} size="xl">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <div className="flex items-center gap-2">
                <IconArrowLeft size={20} className="text-white" />
                <Text fw={600} c="white" className="text-lg">
                  Modèle consultation
                </Text>
                <div className="ml-auto flex items-center gap-2">
                  <Switch
                    checked={scrollDisabled}
                    onChange={(event) => setScrollDisabled(event.currentTarget.checked)}
                    size="sm"
                    color="white"
                  />
                  <Text size="sm" c="white">Désactiver le scroll automatique</Text>
                </div>
              </div>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#2d89bd]" />
          </Modal.Header>
          <Modal.Body p="md">
            <Stack gap="md">
              {/* Onglets */}
              <Tabs value={formData.activeTab} onChange={(value) => setFormData(prev => ({ ...prev, activeTab: value || 'titre' }))}>
                <Tabs.List>
                  <Tabs.Tab
                    value="titre"
                    leftSection={<IconFileText size={16} />}
                    className="text-red-600"
                  >
                    Titre *
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="couleur"
                    leftSection={<IconColorPicker size={16} />}
                  >
                    Couleur
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="fiche-medical"
                    leftSection={<IconStethoscope size={16} />}
                  >
                    Fiche médical
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="biometrie"
                    leftSection={<IconActivity size={16} />}
                  >
                    Biométrie
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="diagnostique"
                    leftSection={<IconMedicalCross size={16} />}
                  >
                    Diagnostique et Pathologies
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="suivi-grossesse"
                    leftSection={<IconBabyCarriage size={16} />}
                  >
                    Suivi de grossesse
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="gestionnaire"
                    leftSection={<IconFolder size={16} />}
                  >
                    Gestionnaire de fichiers
                  </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="titre" pt="md">
                  <TextInput
                    label="Titre du modèle"
                    placeholder="Entrez le titre"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.currentTarget.value }))}
                    required
                  />
                </Tabs.Panel>

                <Tabs.Panel value="couleur" pt="md">
                  <div>
                    <Text size="sm" fw={500} mb="xs">Couleur du modèle</Text>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={formData.color}
                        onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                        className="w-10 h-10 rounded border border-gray-300 cursor-pointer"
                      />
                      <Text size="sm" c="dimmed">{formData.color}</Text>
                    </div>
                  </div>
                </Tabs.Panel>

                {/* Autres onglets peuvent être ajoutés ici */}
              </Tabs>

              {/* Boutons d'ajout */}
              <div className="flex gap-4 mt-6">
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={() => setNewBlocModalOpened(true)}
                  className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
                  size="sm"
                >
                  Ajouter un nouveau bloc
                </Button>
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={() => setNewProcedureModalOpened(true)}
                  className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
                  size="sm"
                >
                  Ajouter une nouvelle procédure
                </Button>
              </div>

              {/* Zone de contenu avec blocs et procédures */}
              <div className="min-h-[200px] border border-gray-200 rounded-lg p-4 bg-gray-50">
                {/* Affichage des blocs */}
                {formData.blocs.length > 0 && (
                  <div className="mb-4">
                    <Text size="sm" fw={500} className="mb-2">Blocs de saisie :</Text>
                    <div className="flex flex-wrap gap-2">
                      {formData.blocs.map((bloc) => (
                        <Badge
                          key={bloc.id}
                          color={bloc.color}
                          variant="filled"
                          rightSection={
                            <ActionIcon
                              size="xs"
                              color="white"
                              radius="xl"
                              variant="transparent"
                              onClick={() => handleRemoveBloc(bloc.id)}
                            >
                              <IconX size={10} />
                            </ActionIcon>
                          }
                        >
                          {bloc.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Affichage des procédures */}
                {formData.procedures.length > 0 && (
                  <div className="mb-4">
                    <Text size="sm" fw={500} className="mb-2">Procédures :</Text>
                    <div className="flex flex-wrap gap-2">
                      {formData.procedures.map((procedure) => (
                        <Badge
                          key={procedure.id}
                          color="blue"
                          variant="light"
                          rightSection={
                            <ActionIcon
                              size="xs"
                              color="blue"
                              radius="xl"
                              variant="transparent"
                              onClick={() => handleRemoveProcedure(procedure.id)}
                            >
                              <IconX size={10} />
                            </ActionIcon>
                          }
                        >
                          {procedure.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Message si aucun élément */}
                {formData.blocs.length === 0 && formData.procedures.length === 0 && (
                  <div className="text-center text-gray-500 py-10">
                    <Text size="sm">
                      Aucun bloc ou procédure ajouté. Utilisez les boutons ci-dessus pour commencer.
                    </Text>
                  </div>
                )}
              </div>

              {/* Boutons d'action */}
              <Group justify="flex-end" mt="md">
                <Button variant="outline" color="red" onClick={closeModal}>
                  Annuler
                </Button>
                <Button
                  onClick={handleSave}
                  className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
                >
                  Enregistrer et quitter
                </Button>
                <Button
                  onClick={handleSave}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Enregistrer
                </Button>
              </Group>
            </Stack>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>

      {/* Modal pour ajouter un nouveau bloc */}
      <Modal.Root opened={newBlocModalOpened} onClose={() => setNewBlocModalOpened(false)} size="md">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#ff6b35", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="white" className="flex gap-2 text-lg">
                <IconPlus size={20} />
                Bloc de saisie
              </Text>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#e55a2b]" />
          </Modal.Header>
          <Modal.Body p="md">
            <Stack gap="md">
              <TextInput
                label="Nom du bloc"
                placeholder="Entrez le nom du bloc"
                value={newBlocName}
                onChange={(e) => setNewBlocName(e.currentTarget.value)}
                required
              />
              <div>
                <Text size="sm" fw={500} mb="xs">Couleur</Text>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={newBlocColor}
                    onChange={(e) => setNewBlocColor(e.target.value)}
                    className="w-10 h-10 rounded border border-gray-300 cursor-pointer"
                  />
                  <Text size="sm" c="dimmed">{newBlocColor}</Text>
                </div>
              </div>
              <Group justify="flex-end" mt="md">
                <Button variant="default" onClick={() => setNewBlocModalOpened(false)}>
                  Annuler
                </Button>
                <Button
                  onClick={handleAddBloc}
                  disabled={!newBlocName.trim()}
                  className="bg-[#ff6b35] hover:bg-[#e55a2b] text-white"
                >
                  Ajouter
                </Button>
              </Group>
            </Stack>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>

      {/* Modal pour ajouter une nouvelle procédure */}
      <Modal.Root opened={newProcedureModalOpened} onClose={() => setNewProcedureModalOpened(false)} size="md">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#00BFFF", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="white" className="flex gap-2 text-lg">
                <IconPlus size={20} />
                Procédure
              </Text>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#00A5E6]" />
          </Modal.Header>
          <Modal.Body p="md">
            <Stack gap="md">
              <TextInput
                label="Nom de la procédure"
                placeholder="Entrez le nom de la procédure"
                value={newProcedureName}
                onChange={(e) => setNewProcedureName(e.currentTarget.value)}
                required
              />
              <Group justify="flex-end" mt="md">
                <Button variant="default" onClick={() => setNewProcedureModalOpened(false)}>
                  Annuler
                </Button>
                <Button
                  onClick={handleAddProcedure}
                  disabled={!newProcedureName.trim()}
                  className="bg-[#00BFFF] hover:bg-[#00A5E6] text-white"
                >
                  Ajouter
                </Button>
              </Group>
            </Stack>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </div>
  );
};

export default Consultation;
