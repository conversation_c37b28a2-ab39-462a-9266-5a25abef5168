import React from 'react';
import { Button, Menu, Tooltip, Avatar, Group, Text, Radio, rem } from '@mantine/core';
import { CheckIcon } from '@mantine/core';

interface TreatmentOption {
  id: string;
  label: string;
  icon: React.ReactNode;
  targetPath?: string;
}

interface TreatmentMenuProps {
  label: string;
  tooltip: string;
  icon: React.ReactNode;
  shortCode: string;
  options: TreatmentOption[];
  activeButton: string | null;
  onButtonClick: (buttonId: string, targetPath?: string) => void;
  isActive?: boolean;
}

export const TreatmentMenu: React.FC<TreatmentMenuProps> = ({
  label,
  tooltip,
  icon,
  shortCode,
  options,
  activeButton,
  onButtonClick,
  isActive = false
}) => {
  const isMenuActive = options.some(option => activeButton === option.id);

  return (
    <Menu shadow="md" width={210} trigger="hover" openDelay={100} closeDelay={400}>
      <Tooltip
        label={tooltip}
        withArrow
        className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
      >
        <Menu.Target>
          <Button
            styles={{
              root: {
                position: 'relative',
                color: 'white',
                height: '35px',
                width: '35px',
                padding: 0,
                borderRadius: '0.5rem'
              },
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
              }}
            >
              <span
                className={
                  isMenuActive || isActive
                    ? "block h-[35px] w-[35px] rounded-md bg-[#3799CE] p-1 hover:bg-[#3799CE]"
                    : "block h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-1 hover:bg-[#3799CE]"
                }
              >
                {icon}
              </span>
            </div>
            <span
              style={{
                position: 'absolute',
                bottom: '0px',
                right: '0px',
                fontSize: '8px',
                fontWeight: '800',
                backgroundColor: 'white',
                borderTopLeftRadius: '0.5rem',
                borderBottomRightRadius: '0.5rem',
                color: '#3799CE',
                padding: '3px 0px 1px 2px',
              }}
              className="h-[14px] w-[14px]"
            >
              {shortCode}
            </span>
          </Button>
        </Menu.Target>
      </Tooltip>
      <Menu.Dropdown>
        <Menu.Label>{label}</Menu.Label>
        {options.map((option) => (
          <Menu.Item
            key={option.id}
            className={
              activeButton === option.id
                ? "bg-[var(--mantine-color-gray-1)] h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg mb-1"
                : "h-[41.325px] w-[200.4px] px-[7px] py-[12px] rounded-lg hover:bg-[var(--mantine-color-gray-1)] mb-1"
            }
            onClick={() => onButtonClick(option.id, option.targetPath)}
          >
            <Group className="h-[28px] w-[186.4px]">
              <Radio
                checked={activeButton === option.id}
                onChange={() => {}}
                icon={CheckIcon}
              />
              <div className="flex">
                <Avatar
                  color="blue"
                  radius="sm"
                  style={{ width: "28px", height: "28px" }}
                  px={0.5}
                >
                  {option.icon}
                </Avatar>
                <Text fw={500} ml={6}>{option.label}</Text>
              </div>
            </Group>
          </Menu.Item>
        ))}
      </Menu.Dropdown>
    </Menu>
  );
};

// Composant pour bouton simple (sans menu déroulant)
interface SimpleTreatmentButtonProps {
  label: string;
  tooltip: string;
  icon: React.ReactNode;
  shortCode: string;
  buttonId: string;
  activeButton: string | null;
  onButtonClick: (buttonId: string, targetPath?: string) => void;
  targetPath?: string;
}

export const SimpleTreatmentButton: React.FC<SimpleTreatmentButtonProps> = ({
  label,
  tooltip,
  icon,
  shortCode,
  buttonId,
  activeButton,
  onButtonClick,
  targetPath
}) => {
  return (
    <Tooltip
      label={tooltip}
      withArrow
      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
    >
      <Button
        styles={{
          root: {
            position: 'relative',
            color: 'white',
            height: '35px',
            width: '35px',
            padding: 0,
            borderRadius: '0.5rem'
          },
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
          }}
        >
          <span
            className={
              activeButton === buttonId
                ? "block h-[35px] w-[35px] rounded-md bg-[#3799CE] p-0.5 hover:bg-[#3799CE]"
                : "block h-[35px] w-[35px] rounded-md bg-[#5A5A5A] p-0.5 hover:bg-[#3799CE]"
            }
            onClick={() => onButtonClick(buttonId, targetPath)}
          >
            {icon}
          </span>
        </div>
        <span
          style={{
            position: 'absolute',
            bottom: '0px',
            right: '0px',
            fontSize: '8px',
            fontWeight: '800',
            backgroundColor: 'white',
            borderTopLeftRadius: '0.5rem',
            borderBottomRightRadius: '0.5rem',
            color: '#3799CE',
            padding: '3px 0px 1px 2px',
          }}
          className="h-[14px] w-[14px]"
        >
          {shortCode}
        </span>
      </Button>
    </Tooltip>
  );
};
