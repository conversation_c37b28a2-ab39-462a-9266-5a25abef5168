// frontend/dental_medicine/src/components/content/dental/specialties/Surgery/SurgeryTab.tsx

import React, { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Tabs, rem, Button, Group, Text, Dialog, Menu, Container, Flex } from '@mantine/core';
import {  IconSettings, IconPhoto, IconSquareRoundedPlusFilled } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { DentalSpecialtyTabProps, SaveManagerRef, ModificationState, SVGPathStyle } from '../../shared/types';
import { useSaveManager } from '../../shared/SaveManager';
import SurgicalControls from './SurgicalControls';
import SurgicalProcedures from './SurgicalProcedures';
import { DentalSvgWrapper } from '../../shared/DentalSvgWrapper';
import { Dantal, DantalB } from '@/utils/Tdantal';

export const SurgeryTab = forwardRef<SaveManagerRef, DentalSpecialtyTabProps>(({
  onModificationChange
}, ref) => {

  // États locaux pour la chirurgie
  const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>({});
  const [highlightedPaths] = useState<Record<string, SVGPathStyle>>({});
  const [clickedIds] = useState<string[]>([]);
  const [activeButton, setActiveButton] = useState<string>('');
  const [targetPath, setTargetPath] = useState<string>('');
  const [opened, { close }] = useDisclosure(false);

  // État de modification pour cette spécialité
  const modificationState: ModificationState = {
    hiddenPaths,
    highlightedPaths,
    clickedIds,
    activeButton,
    targetPath
  };

  // Gestionnaire de sauvegarde
  const { save, hasChanges, SaveManagerComponent } = useSaveManager(
    modificationState,
    onModificationChange,
    'surgical'
  );

  // Exposer les méthodes via ref
  useImperativeHandle(ref, () => ({
    triggerSave: async () => {
      await save();
    },
    hasUnsavedChanges: () => hasChanges()
  }), [save, hasChanges]);

  // Gestionnaire de clic sur SVG
  const handleSvgClick = useCallback((svgId: string) => {
    const togglePathVisibility = (pathId: string, visible: boolean) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths(prev => {
        const newHiddenPaths = { ...prev, [key]: visible };

        // Sauvegarder automatiquement si onModificationChange est disponible
        if (onModificationChange) {
          onModificationChange(svgId, pathId, !visible, highlightedPaths)
            .catch(error => console.error('Erreur sauvegarde chirurgie:', error));
        }

        return newHiddenPaths;
      });
    };

    // Appliquer l'intervention selon le bouton actif
    if (activeButton) {
      console.log(`⚔️ Application de l'intervention ${activeButton} sur la dent ${svgId}`);

      switch (activeButton) {
        case 'extraction':
          console.log('🦷 Application d\'extraction');
          togglePathVisibility('53', false);
          break;
        case 'bone_graft':
          console.log('🦴 Application de greffe osseuse');
          togglePathVisibility('59', false);
          break;
        case 'implant_surgery':
          console.log('🔩 Application de chirurgie implant');
          togglePathVisibility('56', false);
          break;
        case 'sinus_lift':
          console.log('⬆️ Application de sinus lift');
          togglePathVisibility('62', false);
          break;
        case 'periodontal_surgery':
          console.log('🦷 Application de chirurgie parodontale');
          togglePathVisibility('64', false);
          break;
        case 'apicoectomy':
          console.log('🔪 Application d\'apicectomie');
          togglePathVisibility('65', false);
          break;
        default:
          if (targetPath) {
            togglePathVisibility(targetPath, false);
          }
          break;
      }
    }
  }, [activeButton, targetPath, onModificationChange, highlightedPaths]);

  // Gestionnaire de changement de bouton
  const handleButtonClick = useCallback((buttonId: string) => {
    setActiveButton(buttonId);
    console.log(`⚔️ Bouton chirurgie activé: ${buttonId}`);
  }, []);

  // Gestionnaire de changement de path cible
  const handleTargetPathChange = useCallback((pathId: string) => {
    setTargetPath(pathId);
    console.log(`🎯 Path cible chirurgie: ${pathId}`);
  }, []);

  return (
    <>
      {/* Composant de sauvegarde invisible */}
      {SaveManagerComponent}

      <div className="my-4">
        <Container>
          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px' }} align="center">
            <div style={{ position: 'absolute', right: 16 }}>
              <Menu withinPortal position="bottom-end" shadow="sm">
                <Menu.Target>
                  <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                    Procédures Chirurgicales
                  </Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item>Extraction</Menu.Item>
                  <Menu.Item>Implant</Menu.Item>
                  <Menu.Item>Greffe osseuse</Menu.Item>
                  <Menu.Item>Sinus lift</Menu.Item>
                  <Menu.Item>Chirurgie parodontale</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </div>

            {/* Contrôles spécifiques à la chirurgie */}
            <SurgicalControls
              activeButton={activeButton}
              onButtonClick={handleButtonClick}
              onTargetPathChange={handleTargetPathChange}
            />
          </Flex>

          {/* SVG Dentaire - Toutes les 32 dents */}
          <div className="dental-svg-container">
            <div className="max-w-[920px] mt-2 mx-auto">
              {/* Mâchoire supérieure (dents 1-16) */}
              <div className="flex h-full px-0 max-h-[160px] mb-2">
                {Dantal.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                  />
                ))}
              </div>
              {/* Mâchoire inférieure (dents 17-32) */}
              <div className="flex h-full px-0 max-h-[160px]">
                {DantalB.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                  />
                ))}
              </div>
            </div>
          </div>
        </Container>

        {/* Onglets de procédures */}
        <div className="border-base-200 mx-4 border-t mt-4">
          <Tabs variant="unstyled" defaultValue="All Procedures">
            <Tabs.List grow className="space-x-0 gap-0 mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
              >
                Toutes les Procédures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
              >
                Planifiées
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={<IconPhoto style={{ width: rem(16), height: rem(16) }} />}
              >
                Terminées
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="All Procedures">
              <SurgicalProcedures
                type="all"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Planned">
              <SurgicalProcedures
                type="planned"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Completed">
              <SurgicalProcedures
                type="completed"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>
          </Tabs>
        </div>

        {/* Dialog de sauvegarde */}
        <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
            <Text size="sm" mb="xs" fw={500}>
              Sauvegarder les Modifications Chirurgicales
            </Text>
          </Group>
          <Group align="flex-end">
            <Button
              w="100%"
              onClick={async () => {
                await save();
                close();
              }}
              className="hover:bg-[#3799CE]/90"
            >
              Sauvegarder
            </Button>
          </Group>
        </Dialog>
      </div>
    </>
  );
});

SurgeryTab.displayName = 'SurgeryTab';
