import { DentalSvg } from '@/types/dental';

// Tooth numbering and naming system according to FDI World Dental Federation notation 12ans
export const TOOTH_NUMBERS = {
  // Permanent teeth (Adult)
  11: 'Upper Right Central Incisor',
  12: 'Upper Right Lateral Incisor',
  13: 'Upper Right Canine',
  14: 'Upper Right First Premolar',
  15: 'Upper Right Second Premolar',
  16: 'Upper Right First Molar',
  17: 'Upper Right Second Molar',
  18: 'Upper Right Third Molar',
  21: 'Upper Left Central Incisor',
  22: 'Upper Left Lateral Incisor',
  23: 'Upper Left Canine',
  24: 'Upper Left First Premolar',
  25: 'Upper Left Second Premolar',
  26: 'Upper Left First Molar',
  27: 'Upper Left Second Molar',
  28: 'Upper Left Third Molar',
  31: 'Lower Left Central Incisor',
  32: 'Lower Left Lateral Incisor',
  33: 'Lower Left Canine',
  34: 'Lower Left First Premolar',
  35: 'Lower Left Second Premolar',
  36: 'Lower Left First Molar',
  37: 'Lower Left Second Molar',
  38: 'Lower Left Third Molar',
  41: 'Lower Right Central Incisor',
  42: 'Lower Right Lateral Incisor',
  43: 'Lower Right Canine',
  44: 'Lower Right First Premolar',
  45: 'Lower Right Second Premolar',
  46: 'Lower Right First Molar',
  47: 'Lower Right Second Molar',
  48: 'Lower Right Third Molar',
  // Primary teeth (Children)
  51: 'Upper Right Primary Central Incisor',
  52: 'Upper Right Primary Lateral Incisor',
  53: 'Upper Right Primary Canine',
  54: 'Upper Right Primary First Molar',
  55: 'Upper Right Primary Second Molar',
  61: 'Upper Left Primary Central Incisor',
  62: 'Upper Left Primary Lateral Incisor',
  63: 'Upper Left Primary Canine',
  64: 'Upper Left Primary First Molar',
  65: 'Upper Left Primary Second Molar',
  71: 'Lower Left Primary Central Incisor',
  72: 'Lower Left Primary Lateral Incisor',
  73: 'Lower Left Primary Canine',
  74: 'Lower Left Primary First Molar',
  75: 'Lower Left Primary Second Molar',
  81: 'Lower Right Primary Central Incisor',
  82: 'Lower Right Primary Lateral Incisor',
  83: 'Lower Right Primary Canine',
  84: 'Lower Right Primary First Molar',
  85: 'Lower Right Primary Second Molar',
} as const;

export const Dantal: DentalSvg[] = [
    // Tooth 55: 'Upper Right Primary Second Molar',
    {
      svg_id: "4",
      tooth_number: 55,
      tooth_name: TOOTH_NUMBERS[55],
      tooth_type: "Second Molar",
      quadrant: "upper_right",
      tooth_position: 4,
      is_permanent: true,
      width:"53.7px",
      position:"0 0 45.5 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M30.7,71.2c-2.9-1-9.5-2.3-19.1,0.6c-1.2,0.4-2.2,1.2-2.7,2.3C7,78.2,5.5,82.3,4.2,86.6c-0.6,2-0.8,4.1-0.8,6.1  v7.1c0,1.7,1,3.3,2.6,4l13,6c1.1,0.5,2.3,0.6,3.4,0.2c5-1.7,9.4-4.6,13-8.4c2.1-2.3,3.2-5.3,3.2-8.4c0-2.9-0.4-5.7-1.2-8.5L33,73.1  C32.5,72.3,31.7,71.5,30.7,71.2z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",

          path: "M32.4,72.4l-4.8-32.8c-0.7-4.9-2.1-9.8-4-14.4l-3.5-8.4c-0.5-1.2-1.7-2-3-2l0,0c-1.1,0-2.1,0.8-2.4,1.9l-1.9,7  c-1.7,6.2-2.6,12.6-2.8,19L9.2,73.6C9.2,73.7,18.1,66.3,32.4,72.4z",
        },

        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",

          path: "M23.4,65.3h1.7c1.2,0,2.2-1,2.2-2.2c0-0.1,0-0.1,0-0.2c-0.6-8-2.7-33.5-7.5-40.8c-0.2-0.4-0.7-0.5-1.1-0.2  c-0.3,0.2-0.4,0.5-0.4,0.8c1.3,7,4.7,28,3,40.3c-0.2,1.1,0.6,2.1,1.7,2.3C23.2,65.3,23.3,65.3,23.4,65.3z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M15,33.8c-1.2,3.6-2.9,12.5-1,31.7c0.1,1.3,1.3,2.3,2.6,2.3h1.1c1.3,0,2.3-1,2.3-2.3c0-0.1,0-0.2,0-0.3l0,0  c-0.1-0.6-0.2-1.1-0.4-1.7c-0.8-2.8-3.4-12.3-2.9-16.7c0,0,0.9-8.6,0.2-12.9c-0.1-0.5-0.7-0.8-1.2-0.7C15.3,33.3,15.1,33.5,15,33.8z  ",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },

        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",

          path: "M26.4,135.7c-1,3.1-1.1,6.4-0.4,9.6c0.1,0.5-0.2,0.9-0.6,1c-0.2,0-0.3,0-0.5,0c-2.6-1.2-5.7-1.4-8.4-0.5  c-0.4,0.1-0.9-0.1-1-0.6c0-0.2,0-0.3,0-0.5c0.7-2.1,1.6-5.9,0.4-9.1c-0.2-0.4,0.1-0.9,0.5-1.1c0.2-0.1,0.3-0.1,0.5,0  c2.8,0.6,5.8,0.7,8.6,0.2c0.4,0,0.8,0.3,0.9,0.7C26.5,135.5,26.5,135.6,26.4,135.7z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",

          path: "M20.7,120.9c-3.5,1.5-6.7,3.8-9.4,6.6c-0.6,0.7-0.6,1.8,0,2.5l5.2,6.1c0,0,3.3-2.4,9.3,0l5.5-6.4  c0.8-0.9,0.7-2.3-0.2-3.1l-5.7-5c-0.7-0.6-1.6-1-2.6-1h-0.6C21.7,120.6,21.2,120.7,20.7,120.9z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",

          path: "M26.9,134.1l4.8-4.9c0.6-0.7,1.7-0.7,2.4-0.1c2.5,2.2,7.1,8.5,2.1,21.4c-0.6,1.6-2.3,2.5-4,1.9  c-0.5-0.2-1-0.5-1.3-0.9l-2.7-3.1c-1-1-1.6-2.2-1.8-3.6l-0.8-6.7C25.4,136.6,25.9,135.1,26.9,134.1z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",

          path: "M33.4,153.2l-6-6.1c-0.5-0.5-1-0.9-1.6-1.1c-1.9-0.8-5.9-2-9.6,0c-0.9,0.5-1.7,1.2-2.3,2l-4.4,5.9  c-0.6,0.8-0.4,1.9,0.3,2.5l0,0c3.7,2.7,14.1,8.9,23.6-0.9C34,154.8,34,153.8,33.4,153.2z",
        },

        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",

          path: "M9.7,129.5c-2.9,2.7-11,11.7-2.4,24.3c0.5,0.7,1.4,0.8,2.1,0.4c0.1-0.1,0.2-0.1,0.2-0.2  c1.6-1.7,4.1-4.7,5.2-6.1c0.5-0.6,0.8-1.2,1.1-1.9c0.8-2.2,2.2-7.3-0.2-10.9c-0.1-0.2-2.9-3.7-4.4-5.4C11,129.1,10.2,129,9.7,129.5  C9.7,129.4,9.7,129.5,9.7,129.5z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M30.7,71.2c-2.9-1-9.5-2.3-19.1,0.6c-1.2,0.4-2.2,1.2-2.7,2.3C7,78.2,5.5,82.3,4.2,86.6c-0.6,2-0.8,4.1-0.8,6.1  v7.1c0,1.7,1,3.3,2.6,4l13,6c1.1,0.5,2.3,0.6,3.4,0.2c5-1.7,9.4-4.6,13-8.4c2.1-2.3,3.2-5.3,3.2-8.4c0-2.9-0.4-5.7-1.2-8.5L33,73.1  C32.5,72.3,31.7,71.5,30.7,71.2z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(5 22)",
        },


        {
          id: "18",
          code: "st11",
          path: "M195.21,72c-2.74-.94-9-2.16-18,.57a4.17,4.17,0,0,0-2.55,2.16,87.53,87.53,0,0,0-4.43,11.74,19.77,19.77,0,0,0-.76,5.73v6.66a4.11,4.11,0,0,0,2.45,3.76l12.28,5.64a4.22,4.22,0,0,0,3.21.18,30.38,30.38,0,0,0,12.28-7.88,11.65,11.65,0,0,0,3-7.89,28.47,28.47,0,0,0-1.14-8l-4.15-10.89A4.07,4.07,0,0,0,195.21,72Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-165.29 0)"
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-1 2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M30.7,71.2c-2.9-1-9.5-2.3-19.1,0.6c-1.2,0.4-2.2,1.2-2.7,2.3C7,78.2,5.5,82.3,4.2,86.6c-0.6,2-0.8,4.1-0.8,6.1  v7.1c0,1.7,1,3.3,2.6,4l13,6c1.1,0.5,2.3,0.6,3.4,0.2c5-1.7,9.4-4.6,13-8.4c2.1-2.3,3.2-5.3,3.2-8.4c0-2.9-0.4-5.7-1.2-8.5L33,73.1  C32.5,72.3,31.7,71.5,30.7,71.2z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(5 22)",
        },
{id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-6 8)",style: { cursor: "pointer" },position: "",},
{id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-6 8)",style: { cursor: "pointer" },position: "",},
{id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-6 8)",style: { cursor: "pointer" },position: "",},
 // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-2 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-2  10) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(8 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(0  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 48) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 48) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
      transforms:"translate(-6 7) ",
    },
     //RootGuttaPerchaMode
     {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M34.29,37.61c.17,3.37.17,6.76,0,10.13s-.5,6.73-1,10a151.84,151.84,0,0,1-3.87,18.92,161.17,161.17,0,0,1-12.7,32.68c-.24.47-.65.47-.9,0a2,2,0,0,1,0-1.68,152.61,152.61,0,0,0,6.89-15.18,167.2,167.2,0,0,0,5.64-16.85,148,148,0,0,0,3.86-18.43c.44-3.18.77-6.42,1-9.69s.27-6.59.32-9.88h0c0-.38.18-.67.38-.66s.33.27.35.61Z",
      transforms:"translate(-10 2) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M16.49,112.42,13,119.83c-.57,1.21-1.57,1.33-2.23.26h0a4.86,4.86,0,0,1-.14-4.14l3.51-7.4c.58-1.22,1.58-1.34,2.23-.27h0A4.83,4.83,0,0,1,16.49,112.42Z",
      transforms:"translate(-10 2) ",
    },
     //PostCare
     {
      id: "36",
      code: "st17",
      path: "M185.93,60.9l.74-6.57a21.51,21.51,0,0,0,.11-3.11l-.08-2L183.15,22.7a1.1,1.1,0,0,1,1-1.26h.37c.29,0,.87.76,1,1,.81,2.64,3.26,8.3,4.37,16,1,7.18,3.26,15.89,3,25.49-.08,3.06-2.07,12.34-2.39,13.7l-1.33,5.61c-.2.86-.67,1.42-1.18,1.42H181.5c-.37,0-.7-.33-.9-.88a43.73,43.73,0,0,1-2.51-14.68s.13-25.56.28-34.42c0-1.42,2-2.46,3.1-1.5a3.13,3.13,0,0,1,1,1.53c.54,2.88.11,9.56.16,15.4a45.61,45.61,0,0,0,1.18,10.1C184.26,61.91,185.26,62.33,185.93,60.9Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-165 0)",
    },
     //Veneer
     {
      id: "37",
      code: "st19",
      path: "M172.29,65.8a94.53,94.53,0,0,0-4.7,12.5,21.3,21.3,0,0,0-.8,6.1v7.1a4.38,4.38,0,0,0,2.6,4l13,6a4.5,4.5,0,0,0,3.4.2,32.18,32.18,0,0,0,13-8.4,12.41,12.41,0,0,0,3.2-8.4,30.52,30.52,0,0,0-1.2-8.5l-4.4-11.6",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-163 8)",
    },
     //Onlay
     {
      id: "38",
      code: "st19",
      path: "M176.59,121.32s-12.49,9-3.05,23.79a1.69,1.69,0,0,0,.6.4,4.92,4.92,0,0,1,2.3,2.4,4.56,4.56,0,0,0,.35.63c2.9,2.27,13.86,9.55,23.23-1.59a1.58,1.58,0,0,0,.26-.7,5.54,5.54,0,0,1,.4-1.39l0-.05a2.35,2.35,0,0,1,.78-1,4.36,4.36,0,0,0,1.56-1.91c1.54-3.6,4.58-12.93-1.55-19.7a7.16,7.16,0,0,0-1.5-1.3,5.58,5.58,0,0,1-1.6-1.46c-.88-1.22-2.79-3.15-6.91-5.67a5,5,0,0,0-4.47-.42,21.88,21.88,0,0,0-8.88,6.49,7.15,7.15,0,0,1-1.55,1.46Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-166 8)",

    },


    {
      id: "39",
      code: "st20",
      path: "M172,82.16h4.14a9.13,9.13,0,0,1,3.47.67l4.76,2a6.86,6.86,0,0,0,2.4.51l3.19.1a11.07,11.07,0,0,0,4.91-.93h0a8.39,8.39,0,0,1,3.29-.7l5.1-.06A1.57,1.57,0,0,1,205,85l-.06,2.61a4.14,4.14,0,0,1-.86,2.42h0A28.06,28.06,0,0,1,198.73,96s-6.7,5.51-11,5.48c-2.23,0-7.67-3.18-8.75-3.51-.89-.28-3.78-1.93-4.67-2.26-1.31-.5-2.89-1.14-3.44-2.67s-.27-3.67-.41-5.23c0,0,0-2.83,0-4.46A1.37,1.37,0,0,1,172,82.16Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-166 8)",

    },
     //CrownPermanent
     {
      id: "40",
      code: "st19",
      path: "M172.29,65.8a94.53,94.53,0,0,0-4.7,12.5,21.3,21.3,0,0,0-.8,6.1v7.1a4.38,4.38,0,0,0,2.6,4l13,6a4.5,4.5,0,0,0,3.4.2,32.18,32.18,0,0,0,13-8.4,12.41,12.41,0,0,0,3.2-8.4,30.52,30.52,0,0,0-1.2-8.5l-4.4-11.6",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-163 8)",

    },
    {
      id: "41",
      code: "st19",
      path: "M173.33,121.32s-12.49,9-3.05,23.79a1.75,1.75,0,0,0,.6.4,5,5,0,0,1,2.3,2.4,4.82,4.82,0,0,0,.35.63c2.9,2.27,13.86,9.55,23.23-1.59a1.61,1.61,0,0,0,.26-.7,5.54,5.54,0,0,1,.4-1.39v-.05a2.32,2.32,0,0,1,.78-1,4.41,4.41,0,0,0,1.56-1.91c1.54-3.6,4.58-12.93-1.55-19.7a7.16,7.16,0,0,0-1.5-1.3,5.58,5.58,0,0,1-1.6-1.46c-.88-1.22-2.79-3.15-6.91-5.67a5,5,0,0,0-4.47-.42,21.94,21.94,0,0,0-8.88,6.49,7.17,7.17,0,0,1-1.55,1.46Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-163 8)",
    },
     //CrownGold 4
     {
      id: "43",
      code: "st43",
      path: "M200.78,75.36c-6.22,6.88-16.26,16.15-29.25,21.93l-1.36-.57C183.39,90.83,193.9,81.05,200.29,74",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-163 8) ",
    },
    {
      id: "44",
      code: "st43",
      path: "M202.45,84.5c-5.78,6.67-14.08,14.38-23.12,16.49l-1.24-.6c9.77-2.06,18.42-10.2,24.27-17.16",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-163 8) ",
    },
      //CrownZirconia 4
      {
        id: "45",   code: "st26", path :"M194.56,63.43h0a2.79,2.79,0,0,0-1.39-1.54c0-.7-.06-2.45-.77-2.77a20.37,20.37,0,0,0-4.76-1.47V30.29a1.86,1.86,0,0,0,0-.34l-1.35-5.35c-.31-1.7-.94-2.76-1.66-2.76s-1.35,1.06-1.66,2.76L181.58,30a1.27,1.27,0,0,0-.05.34V57.54c-2.65.32-4.71,1.06-5.22,2a4.65,4.65,0,0,0-.29,2.23,3,3,0,0,0-1.41,1.8m7.75-33.09,1.32-5.24a.29.29,0,0,1,0-.09c.18-1.06.53-1.74.88-1.74s.7.68.89,1.74a.29.29,0,0,0,0,.09l1.22,4.86-4.35,2.54Zm0,3.79,4.45-2.6v1.61l-4.45,2.59Zm0,3.23,4.45-2.59v1.6l-4.45,2.59Zm0,3.23,4.45-2.59v1.6l-4.45,2.59Zm0,3.23,4.45-2.59V43l-4.45,2.6Zm0,3.23,4.45-2.59v1.6l-4.45,2.6Zm0,3.24,4.45-2.6v1.6L182.36,52Zm0,3.23,4.45-2.6v1.6l-4.45,2.6Zm0,3.23,4.45-2.6v3.24a19.11,19.11,0,0,0-2.19-.16h-.16c-.72,0-1.43,0-2.1.09Zm-5.48,3.77c.29-.52,2.92-1.75,7.74-1.73a19.38,19.38,0,0,1,7.58,1.71,3,3,0,0,1,.13.83,24.09,24.09,0,0,0-8-1.49c-.06,0-4.64,0-7.51,1.35A2.08,2.08,0,0,1,176.88,60.65Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-163 8) ",},
       {
        id: "46",   code: "st26", path :"M200.78,76.72l-4.4-11.6a4.26,4.26,0,0,0-1.85-1.69H176.38l-1.4.39a4.44,4.44,0,0,0-2.7,2.3,94.53,94.53,0,0,0-4.7,12.5,21.3,21.3,0,0,0-.8,6.1v7.1a4.38,4.38,0,0,0,2.6,4l13,6a4.5,4.5,0,0,0,3.4.2,32.18,32.18,0,0,0,13-8.4,12.41,12.41,0,0,0,3.2-8.4A30.52,30.52,0,0,0,200.78,76.72Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-163 8) ",},
          {
            id: "47",
            code: "st26",
            path: "M173.33,121.32s-12.49,9-3.05,23.79a1.75,1.75,0,0,0,.6.4,5,5,0,0,1,2.3,2.4,4.82,4.82,0,0,0,.35.63c2.9,2.27,13.86,9.55,23.23-1.59a1.61,1.61,0,0,0,.26-.7,5.54,5.54,0,0,1,.4-1.39v-.05a2.32,2.32,0,0,1,.78-1,4.41,4.41,0,0,0,1.56-1.91c1.54-3.6,4.58-12.93-1.55-19.7a7.16,7.16,0,0,0-1.5-1.3,5.58,5.58,0,0,1-1.6-1.46c-.88-1.22-2.79-3.15-6.91-5.67a5,5,0,0,0-4.47-.42,21.94,21.94,0,0,0-8.88,6.49,7.17,7.17,0,0,1-1.55,1.46Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-163 8)",
          },
           //Denture 4
           {
            id: "48",
            code: "st19",
            path: "M197.35,62.9c-2.9-1-9.5-2.3-19.1.6a4.44,4.44,0,0,0-2.7,2.3,94.53,94.53,0,0,0-4.7,12.5,21.3,21.3,0,0,0-.8,6.1v7.1a4.39,4.39,0,0,0,2.6,4l13,6a4.5,4.5,0,0,0,3.4.2,32.18,32.18,0,0,0,13-8.4,12.41,12.41,0,0,0,3.2-8.4,30.52,30.52,0,0,0-1.2-8.5l-4.4-11.6A4.31,4.31,0,0,0,197.35,62.9Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-166 8)",

          },
          {
            id: "49",
            code: "st19",
            path: "M176.59,121.32s-12.49,9-3.05,23.79a1.69,1.69,0,0,0,.6.4,4.92,4.92,0,0,1,2.3,2.4,4.56,4.56,0,0,0,.35.63c2.9,2.27,13.86,9.55,23.23-1.59a1.58,1.58,0,0,0,.26-.7,5.54,5.54,0,0,1,.4-1.39l0-.05a2.35,2.35,0,0,1,.78-1,4.36,4.36,0,0,0,1.56-1.91c1.54-3.6,4.58-12.93-1.55-19.7a7.16,7.16,0,0,0-1.5-1.3,5.58,5.58,0,0,1-1.6-1.46c-.88-1.22-2.79-3.15-6.91-5.67a5,5,0,0,0-4.47-.42,21.88,21.88,0,0,0-8.88,6.49,7.15,7.15,0,0,1-1.55,1.46Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-166 8)",

          },
          {
            id: "50",
            code: "st50",
            polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(0 16)",},
              //Bridge 4
              {
                id: "51",
                code: "st0",
                rect: {
                  x: "0.5",
                  y: "66.57",
                  width: "62.4318",
                  height: "2.8"
                },
                style: { cursor: "pointer" },position: "",
                 transforms:"translate(0 20)",
              },
              {
                id: "52",
                code: "st0",
                path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-4 0)",
              },
                //Implant 4
                {
                  id: "54",
                  code: "st9",
                  path: "M10.68,64.21V75.34s4.71,8.45,11.52,9.88c0,0,10-3.87,12.43-10.5.14-.09,0-4.5,0-4.5Z",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-5.22 -3.18)"
                },

                {
                  id: "55",
                  code: "st9",
                  polygon: "10.4 20.77 24.47 24.34 25.45 33.8 9.41 29.1 10.4 20.77",
                  style: { cursor: "pointer" },position: "",
                },
                {
                  id: "56",
                  code: "st9",
                  polygon: "8.96 34.54 26.25 39.24 27.4 48.77 7.97 42.64 8.96 34.54",
                  style: { cursor: "pointer" },position: "",
                },
                {
                  id: "57",
                  code: "st9",
                  polygon: "7.29 48.05 28.18 53.94 29.41 62.81 6.24 56.44 7.29 48.05",
                  style: { cursor: "pointer" },position: "",
                },
                {
                  id: "58",
                  code: "st9",
                  path: "M16.78,17.26l11.94,4.1c-.84-6.2-1.45-10.58-1.54-10.78-2.65-9.48-5.61-8-7.46-5.58a11.06,11.06,0,0,0-2.07,5.35l-.87,6.91",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-5.22 -3.18)"
                },
                {
                  id: "59",
                  code: "st10",
                  path: "M33,71.37c-2.9-1-9.5-2.3-19.1.6a4.43,4.43,0,0,0-2.7,2.3,94.18,94.18,0,0,0-4.7,12.5,21.32,21.32,0,0,0-.8,6.1V100a4.38,4.38,0,0,0,2.6,4l13,6a4.47,4.47,0,0,0,3.4.2,32.12,32.12,0,0,0,13-8.4,12.41,12.41,0,0,0,3.2-8.4,25.43,25.43,0,0,0-1.2-8.5l-4.4-11.6A4.35,4.35,0,0,0,33,71.37Z",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-5.22 -3.18)"
                },
                {
                  id: "60",
                  code: "st10",
                  path: "M12,129.5s12.34-19,21.82-1.75a2.73,2.73,0,0,0,.87,1c2.45,1.63,11.75,8.59,2.4,22a5.93,5.93,0,0,0-1,4.2h0s-10,13-24.59,0C11.17,155-1.46,143.26,12,129.5Z",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-5.22 -3.18)"
                },
                 //Bone 4
          {

            id: "61",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -80) ",
          },
          {

            id: "62",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -72) ",
          },
          {

            id: "63",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -64) ",
          },
          {

            id: "64",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -56) ",
          },
          {

            id: "65",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -48) ",
          },
          {

            id: "66",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -40) ",
          },
          {
            id: "67",
            code: "st67",
            path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.48),translate(100 240),rotate(-180)"
          },
          //Resection 4
          {
            id: "68",
            code: "st22",
            rect: {
              x: "25.54",
              y: "16.61",
              width: "3.93",
              height: "37.43"
            },
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-23 60) rotate(-65.8)",
          },
            //TeethCrown 4
            {
              id: "69",
              code: "st67",
              path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
              style: { cursor: "pointer" },position: "",
               transforms:"scale(0.6),translate(83.5 186),rotate(-180)"
            },
      ],
    },
     // Tooth 54: 'Upper Right Primary First Molar',
    {
      svg_id: "5",
      tooth_number: 54,
      tooth_name: TOOTH_NUMBERS[54],
      tooth_type: "First Molar",
      quadrant: "upper_right",
      tooth_position: 5,
      is_permanent: true,
      width:"52.8875px",
      position:"0 0 44.8 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M33.9,70.5c-6-1.6-12.3-1.8-18.4-0.6c-2.2,0.4-4.1,1.9-5.1,4C9,77,7,81.6,5.9,84c-0.5,1.1-0.8,2.2-1,3.4l-0.4,2  c-0.6,3.2,0.1,6.6,1.9,9.3c1.6,2.4,3.5,4.7,5.5,6.8c1.9,2,4.2,3.6,6.7,4.6l0,0c0.2,0.1,0.4,0.1,0.6,0.1h1.7c2.5,0,5-0.9,6.9-2.5  L40,97.8c3.5-2.7,5-7.3,3.9-11.6c-0.1-0.3-0.2-0.5-0.2-0.8l-6.2-12.2C36.8,72,35.5,70.9,33.9,70.5z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M37.3,72.9c0,0-9.5-27.4-6.8-55.8c0.3-2.8-1.1-5.5-3.5-6.8c-1.9-1.1-4.3-0.9-6.1,0.5c-1.9,1.4-3.2,3.4-3.6,5.7  l-2,9.1c-1.2,5.7-1.9,11.5-2.1,17.4l-1,28.6C12.2,71.6,25.7,64.6,37.3,72.9z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M18.9,66.3c0.8,0,1.4-0.6,1.5-1.4l0.4-22.2c0-0.9,0-1.8,0-2.6C20.6,36.9,20.4,27.5,23,20  c0.2-0.7-0.1-1.4-0.8-1.6c-0.5-0.2-1.1,0-1.4,0.4c-0.2,0.3-0.4,0.7-0.6,1.1c0,0-5.2,15.1-4.6,25.4c0,0.8,0,2.2,0,3v16.8  c0,0.6,0.5,1.2,1.1,1.2l0,0L18.9,66.3z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M31.9,63.8c-1.7-7.1-6.1-27.9-4.2-39.1c0-0.1,0-0.3-0.1-0.3l0,0c-0.5-0.5-1.2-0.5-1.7-0.1  c-0.2,0.1-0.3,0.3-0.3,0.5c-1,3.2-2.9,9.6-3,14.6c-0.1,5.5-0.2,19.1-0.3,24.5c0,1.2,1,2.2,2.2,2.3h0.1H30c1.1,0,2-0.9,2-2  C31.9,64,31.9,63.9,31.9,63.8z",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },


        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M28.6,136.2c-1.2,0-4.7,1-8.1,0.5s-1.6-1.8-1.4,1.3c0.4,2.3,0.2,4.7-0.5,7c-0.2,0.5,0.1,1.1,0.7,1.3  c0.2,0.1,0.4,0.1,0.5,0c3-0.8,6.1-1,9.2-0.4c0.8,0.1,1.5-0.4,1.6-1.2c0-0.2,0-0.4,0-0.6c-0.6-2.3-0.8-4.7-0.7-7.1  C30,136.3,29.3,136.2,28.6,136.2z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M35.4,127.4c-2.9-1.7-5.5-3.7-8-6c0,0-2.3-1.9-5.7,0.6l-7.7,5.8c-0.6,0.5-0.8,1.4-0.3,2l0.1,0.1l5.6,5.9  c0.5,0.6,1.3,0.9,2,0.8l6.5-0.2c0.6,0,1.2-0.3,1.6-0.7l6-5.7c0.7-0.6,0.7-1.7,0-2.3C35.6,127.6,35.5,127.5,35.4,127.4z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M37.7,128.8c3.1,2,10.4,8.7,1.9,23.8c-0.5,0.9-1.6,1.2-2.5,0.7c-0.1-0.1-0.3-0.2-0.4-0.3c-2.9-3-7.6-9-7.9-13.3  c0,0-1.1-2.8,6.6-10.6C36,128.4,37,128.3,37.7,128.8z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M17.4,147c-1.4,2.5-3.1,4.8-5.1,6.8c-0.7,0.7-0.7,1.8-0.1,2.5l0.1,0.1c3.5,3,12.6,9.3,24.4-0.5  c1-0.8,1.1-2.3,0.3-3.2l-0.1-0.1c-1.9-1.9-4.8-4.9-5.9-6c-0.3-0.3-0.6-0.5-1-0.7c-3.5-1.6-7.5-1.6-11.1-0.2  C18.3,145.9,17.8,146.4,17.4,147z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M11.6,130.2c0.5-0.5,1.3-0.5,1.8-0.1c3,2.5,12.9,11.6-1.3,23.7c-0.2,0.2-0.5,0.3-0.8,0.2c-0.8,0-1.5-0.4-1.9-1  C7.5,150.2,1.4,139.9,11.6,130.2z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M33.9,70.5c-6-1.6-12.3-1.8-18.4-0.6c-2.2,0.4-4.1,1.9-5.1,4C9,77,7,81.6,5.9,84c-0.5,1.1-0.8,2.2-1,3.4l-0.4,2  c-0.6,3.2,0.1,6.6,1.9,9.3c1.6,2.4,3.5,4.7,5.5,6.8c1.9,2,4.2,3.6,6.7,4.6l0,0c0.2,0.1,0.4,0.1,0.6,0.1h1.7c2.5,0,5-0.9,6.9-2.5  L40,97.8c3.5-2.7,5-7.3,3.9-11.6c-0.1-0.3-0.2-0.5-0.2-0.8l-6.2-12.2C36.8,72,35.5,70.9,33.9,70.5z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },

        {
          id: "18",
          code: "st11",
          path: "M243.43,71.39A39,39,0,0,0,226,70.82a6.76,6.76,0,0,0-4.82,3.76c-1.32,2.9-3.21,7.22-4.25,9.47a12.11,12.11,0,0,0-.95,3.19l-.38,1.88a11.88,11.88,0,0,0,1.8,8.72,48,48,0,0,0,5.2,6.38,18.15,18.15,0,0,0,6.33,4.31h0a1.15,1.15,0,0,0,.57.1h1.61a10.3,10.3,0,0,0,6.52-2.35L249.2,97a10.44,10.44,0,0,0,3.69-10.89,2,2,0,0,1-.19-.75l-5.86-11.44A5.55,5.55,0,0,0,243.43,71.39Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-210.29 0)"
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M33.9,70.5c-6-1.6-12.3-1.8-18.4-0.6c-2.2,0.4-4.1,1.9-5.1,4C9,77,7,81.6,5.9,84c-0.5,1.1-0.8,2.2-1,3.4l-0.4,2  c-0.6,3.2,0.1,6.6,1.9,9.3c1.6,2.4,3.5,4.7,5.5,6.8c1.9,2,4.2,3.6,6.7,4.6l0,0c0.2,0.1,0.4,0.1,0.6,0.1h1.7c2.5,0,5-0.9,6.9-2.5  L40,97.8c3.5-2.7,5-7.3,3.9-11.6c-0.1-0.3-0.2-0.5-0.2-0.8l-6.2-12.2C36.8,72,35.5,70.9,33.9,70.5z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
{id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
{id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
 // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(1 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(1  10) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(8 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(1  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-16 48) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-16 48) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
      transforms:"translate(-6 4) ",
    },
     //RootGuttaPerchaMode
     {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M234.15,30.6c0-.34.17-.6.35-.61s.37.28.38.65v0c.05,3.29.14,6.62.32,9.88s.53,6.51,1,9.69A148,148,0,0,0,240,68.66a170.62,170.62,0,0,0,5.64,16.85,153.66,153.66,0,0,0,6.89,15.18,2,2,0,0,1,0,1.68c-.25.45-.66.44-.9,0A161.17,161.17,0,0,1,239,69.67a151.84,151.84,0,0,1-3.87-18.92c-.45-3.29-.77-6.64-1-10s-.14-6.76,0-10.13Z",
      transforms:"translate(-216 -8) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M252.09,101.27h0c.66-1.07,1.65-1,2.23.26l3.51,7.41a4.83,4.83,0,0,1-.14,4.14h0c-.65,1.07-1.65,1-2.23-.26L252,105.41A4.83,4.83,0,0,1,252.09,101.27Z",
      transforms:"translate(-216 -8) ",
    },
     //PostCare
     {
      id: "36",
      code: "st17",
      path: "M232.68,59.85s-.21-6.76-.21-7.85l.1-11.53a30.47,30.47,0,0,1,.8-6.66l1.79-7.7a2.24,2.24,0,0,1,2.39-1.77c.17,0,.29.06.31.14.74,2.88.56,9.38.89,18.9.22,6.22,3.22,12.85,3,21.5-.07,2.4-4,20.19-4.48,20.19h-7.92c-.36,0-.69-.34-.89-.91A48.36,48.36,0,0,1,226,68.77a.28.28,0,0,0,0-.09,159.33,159.33,0,0,1-.41-28.17A90.13,90.13,0,0,1,230.75,18c.14.14,1.85.08,2.16,1.74.71,3.88-1.58,14.86-2.28,24.3a61.63,61.63,0,0,0,.74,15.49C231.82,61.31,232,61.35,232.68,59.85Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-210.3 0)",
    },
    //Veneer
    {
      id: "37",
      code: "st19",
      path: "M223.34,64.06c-1.4,3.1-4.45,9.47-5.55,11.87a13.55,13.55,0,0,0-1,3.4l-.4,2a12.71,12.71,0,0,0,1.9,9.3,51.43,51.43,0,0,0,5.5,6.8,19.08,19.08,0,0,0,6.7,4.6h0a1.25,1.25,0,0,0,.6.1h1.7a10.81,10.81,0,0,0,6.9-2.5l12.2-9.9a11.13,11.13,0,0,0,3.9-11.6,2.41,2.41,0,0,1-.2-.8l-6.2-12.2",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-212 8)",
    },
      //Onlay
      {
        id: "38",
        code: "st19",
        path: "M224,121.77s-13.7,10.54-1,24.68c0,0,12,15.27,26.19,0,0,0,14.42-16.86-1.37-25.95-.12.12-6-4.6-8.57-6.65a3.94,3.94,0,0,0-2.63-.87h0a4.54,4.54,0,0,0-2.65,1Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-212 8)",

      },
      {
        id: "39",
        code: "st20",
        path: "M218.16,84.47h4.43a11.6,11.6,0,0,1,3.72.59l5.11,1.7a8.6,8.6,0,0,0,2.57.44l3.42.1a14.64,14.64,0,0,0,5.27-.81h0a10.94,10.94,0,0,1,3.53-.62l9.46,0c1,0,.06,1.09-.35,1.66l-1.22,1.7c-.28.4-.57,1.25-1,1.56s-9.21,6.42-9.63,6.66c-1.58.89-3.68,2.7-7.16,3.77a12.89,12.89,0,0,1-5.6.56c-2.25-.41-4.29-2.22-4.93-2.59-.82-.48-4.44-3.81-4.44-3.81s-3.64-3.39-4.41-6c0,0-.41-2.46-.42-3.87C216.53,84.92,217.25,84.47,218.16,84.47Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-212 8)",

      },
      //CrownPermanent 5
      {
        id: "40",
        code: "st19",
        path: "M220.08,64.06c-1.4,3.1-4.45,9.47-5.55,11.87a13.43,13.43,0,0,0-1,3.4l-.4,2a12.72,12.72,0,0,0,1.9,9.3,52.08,52.08,0,0,0,5.5,6.8,19,19,0,0,0,6.7,4.6h0a1.23,1.23,0,0,0,.6.1h1.7a10.81,10.81,0,0,0,6.9-2.5l12.2-9.9a11.13,11.13,0,0,0,3.9-11.6,2.41,2.41,0,0,1-.2-.8l-6.2-12.2",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-208 8)",

      },
      {
        id: "41",
        code: "st19",
        path: "M220.74,121.77s-13.7,10.54-1,24.68c0,0,12,15.27,26.19,0,0,0,14.42-16.86-1.37-25.95-.12.12-6-4.6-8.57-6.65a3.92,3.92,0,0,0-2.63-.87h0a4.49,4.49,0,0,0-2.65,1Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-208 8)",
      },
      //CrownGold 5
      {
        id: "43",
        code: "st43",
        path: "M251.15,74.22c-6.67,7.09-17.84,17.1-31.5,23.31,0,0-.89-.89-.88-.9C232.31,90,243.72,80,250.46,72.85",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-210 4) ",
      },
      {
        id: "44",
        code: "st43",
        path: "M253,84.13c-6.44,7.14-16.3,15.88-26.39,18.13l-1.39-.64c10.91-2.2,21.53-12.08,28.06-19.55",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-210 4) ",
      },
      //CrownZirconia 5
      {
        id: "45",   code: "st26", path :"M244,63.3h0a3,3,0,0,0-1.59-1.6c0-.72-.07-2.53-.87-2.85a25.22,25.22,0,0,0-5.43-1.52V29.08a1.27,1.27,0,0,0-.05-.34l-1.54-5.53c-.34-1.76-1.07-2.84-1.88-2.84s-1.54,1.08-1.89,2.84l-1.54,5.53a1.27,1.27,0,0,0,0,.34V57.21c-3,.33-5.37,1.1-5.95,2.06a4.2,4.2,0,0,0-.33,2.3,3.14,3.14,0,0,0-1.61,1.86m8.82-34.16,1.51-5.41a.44.44,0,0,1,0-.1c.21-1.08.6-1.79,1-1.79s.8.71,1,1.79a.3.3,0,0,0,0,.1l1.39,5-5,2.62Zm0,3.91,5.07-2.68v1.66l-5.07,2.68Zm0,3.34,5.07-2.68v1.65l-5.07,2.68Zm0,3.33,5.07-2.67v1.65l-5.07,2.68Zm0,3.34,5.07-2.68v1.66l-5.07,2.67Zm0,3.33,5.07-2.67V45.5l-5.07,2.68Zm0,3.34,5.07-2.68v1.66l-5.07,2.67Zm0,3.34,5.07-2.68v1.65l-5.07,2.68Zm0,3.33,5.07-2.68V57.2a22,22,0,0,0-2.49-.17h-.18c-.82,0-1.63,0-2.4.09Zm-6.24,3.9c.33-.54,3.33-1.82,8.82-1.79a24.11,24.11,0,0,1,8.63,1.76,2.68,2.68,0,0,1,.15.86,29.79,29.79,0,0,0-9.1-1.54c-.07,0-5.28,0-8.56,1.39A1.81,1.81,0,0,1,223.86,60.43Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-210  8) ",},
       {
        id: "46",   code: "st26", path :"M252.52,78.38a2.41,2.41,0,0,1-.2-.8l-6.2-12.2a5.79,5.79,0,0,0-1.73-1.82H221a7,7,0,0,0-2,2.52c-1.4,3.1-3.4,7.7-4.5,10.1a13.43,13.43,0,0,0-1,3.4l-.4,2a12.72,12.72,0,0,0,1.9,9.3,52.08,52.08,0,0,0,5.5,6.8,19,19,0,0,0,6.7,4.6,1.23,1.23,0,0,0,.6.1h1.7a10.81,10.81,0,0,0,6.9-2.5l12.2-9.9A11.13,11.13,0,0,0,252.52,78.38Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-210 8) ",},
      {
        id: "47",
        code: "st26",
        path: "M220.74,121.77s-13.7,10.54-1,24.68c0,0,12,15.27,26.19,0,0,0,14.42-16.86-1.37-25.95-.12.12-6-4.6-8.57-6.65a3.92,3.92,0,0,0-2.63-.87h0a4.49,4.49,0,0,0-2.65,1Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-208 8)",
      },
        //Denture 5
        {
          id: "48",
          code: "st19",
          path: "M223.34,64.06c-1.4,3.1-4.45,9.47-5.55,11.87a13.55,13.55,0,0,0-1,3.4l-.4,2a12.71,12.71,0,0,0,1.9,9.3,51.43,51.43,0,0,0,5.5,6.8,19.08,19.08,0,0,0,6.7,4.6h0a1.25,1.25,0,0,0,.6.1h1.7a10.81,10.81,0,0,0,6.9-2.5l12.2-9.9a11.13,11.13,0,0,0,3.9-11.6,2.41,2.41,0,0,1-.2-.8l-6.2-12.2",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-212 8)",

        },
        {
          id: "49",
          code: "st19",
          path: "M224,121.77s-13.7,10.54-1,24.68c0,0,12,15.27,26.19,0,0,0,14.42-16.86-1.37-25.95-.12.12-6-4.6-8.57-6.65a3.94,3.94,0,0,0-2.63-.87h0a4.54,4.54,0,0,0-2.65,1Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-212 8)",

        },
        {
          id: "50",
          code: "st50",
          polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 16)",
        },
         //Bridge 5
         {
          id: "51",
          code: "st0",
          rect: {
            x: "0.5",
            y: "66.57",
            width: "62.4318",
            height: "2.8"
          },
          style: { cursor: "pointer" },position: "",
           transforms:"translate(0 20)",
        },
        {
          id: "52",
          code: "st0",
          path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-1 0)",
        },
          //Implant 5
          {
            id: "54",
            code: "st9",
            path: "M14.73,65.12V76.25s4.71,8.45,11.52,9.88c0,0,10-3.87,12.44-10.51.13-.09,0-4.49,0-4.49Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-5.78 -4.09)"
          },

          {
            id: "55",
            code: "st9",
            polygon: "13.9 20.77 27.96 24.34 28.95 33.8 12.91 29.1 13.9 20.77",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "56",
            code: "st9",
            polygon: "12.46 34.54 29.74 39.24 30.9 48.77 11.46 42.64 12.46 34.54",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "57",
            code: "st9",
            polygon: "10.79 48.05 31.68 53.94 32.91 62.81 9.74 56.44 10.79 48.05",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "58",
            code: "st9",
            path: "M20.83,18.17l11.94,4.1c-.84-6.21-1.45-10.58-1.54-10.78-2.65-9.49-5.61-8-7.45-5.59a11.15,11.15,0,0,0-2.08,5.36l-.87,6.91",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-5.78 -4.09)"
          },
          {
            id: "59",
            code: "st10",
            path: "M35.89,71.51a40.85,40.85,0,0,0-18.4-.6,7.13,7.13,0,0,0-5.1,4C11,78,9,82.61,7.89,85a13.31,13.31,0,0,0-1,3.4l-.4,2a12.71,12.71,0,0,0,1.9,9.3,51.12,51.12,0,0,0,5.5,6.8,19.13,19.13,0,0,0,6.7,4.6h0a1.27,1.27,0,0,0,.6.1h1.7a10.83,10.83,0,0,0,6.9-2.5L42,98.81a11.16,11.16,0,0,0,3.9-11.6,2.35,2.35,0,0,1-.2-.8l-6.2-12.2A5.93,5.93,0,0,0,35.89,71.51Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-5.78 -4.09)"
          },
          {
            id: "60",
            code: "st10",
            path: "M37,127.34l-6.64-5.17a4,4,0,0,0-2.24-.83l-.93,0a6.37,6.37,0,0,0-3.94,1.13l-8.54,5.92S.33,140.43,12.5,153.86a5.77,5.77,0,0,0,1.79-.06l-1,.2s12.62,14.55,26,1.14c0,.05,15.15-18-1.28-27.16A8,8,0,0,1,37,127.34Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-5.78 -4.09)"
          },
           //Bone 5
           {

            id: "61",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -80) ",
          },
          {

            id: "62",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -72) ",
          },
          {

            id: "63",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -64) ",
          },
          {

            id: "64",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -56) ",
          },
          {

            id: "65",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -48) ",
          },
          {

            id: "66",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -40) ",
          },
          {
            id: "67",
            code: "st67",
            path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.48),translate(100 238),rotate(-180)"

          },
            //Resection 5
            {
              id: "68",
              code: "st22",
              rect: {
                x: "25.54",
                y: "16.61",
                width: "3.93",
                height: "40.43"
              },
              style: { cursor: "pointer" },position: "",
              transforms:"translate(-23 60) rotate(-65.8)",
            },
             //TeethCrown 5
             {
              id: "69",
              code: "st67",
              path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
              style: { cursor: "pointer" },position: "",
               transforms:"scale(0.6),translate(90 186),rotate(-180)"
            },
      ],
    },
     // Tooth 53: 'Upper Right Primary Canine',
    {
      svg_id: "6",
      tooth_number: 53,
      tooth_name: TOOTH_NUMBERS[53],
      tooth_type: "canine",
      quadrant: "upper_right",
      tooth_position: 6,
      is_permanent: true,
      width:"52.425px",
      position:"0 0 44.4 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M32.2,71.2l6.4,12.2c1.3,2.4,1.9,5.1,1.9,7.9v2.3c0,1.8-0.5,3.5-1.5,5c-3.2,5-7.8,8.9-13.1,11.5	c-2,0.9-4.4,1.1-6.5,0.4c-4.7-1.4-9-4-12.2-7.7c-1.9-2.3-2.9-5.1-2.9-8L4.2,90c-0.1-2,0.5-4,1.6-5.6l0.6-0.9c1.3-2,2-4.3,1.9-6.6	l-0.1-2c-0.1-1.9,1-3.5,2.8-4.2l2.9-1.2c0,0,5.2-1.8,16.1,0.1C30.9,69.8,31.7,70.4,32.2,71.2z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M31.8,70.7c0,0-8.5-28.6-5.5-39.4l2.1-10.7c0.2-1,0.2-1.9-0.1-2.9l0,0c-0.4-1.3-1.3-2.4-2.6-2.9l-3.3-1.3	c-1.6-0.6-3.4-0.1-4.4,1.3c-2,2.9-5.4,9.2-7.8,21c-1.1,5.5-1.8,11-2.1,16.6c-0.4,7.2-0.9,19,0.3,21.1C15,68,24.1,67,31.8,70.7z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M17,66.2h3.3c1.7,0,3.1-1.3,3.1-3v-0.1c0-3.6-0.4-7.3-1.1-10.8c-0.7-3.1-1.1-6.2-1-9.4L21.8,27c0-1.9,1.1-4.8-3-4.4l0,0c-0.3-0.2-1.1,0.7-1.2,1c-0.9,2.9-3.8,14.8-3.8,39.3C13.7,64.7,15.2,66.2,17,66.2z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },

        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M23.1,139.1l7.4-15.6c0.4-0.8,0-1.9-0.8-2.3c-0.1,0-0.1-0.1-0.2-0.1c-3-1-9.1-2.3-14.8,0.7	c-0.7,0.3-0.9,1.1-0.6,1.8l7.5,15.5c0.2,0.4,0.7,0.6,1.1,0.4C22.9,139.4,23,139.2,23.1,139.1z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23,139.5l6.2-13.5c0.3-0.7,0-1.6-0.7-1.9c-0.1,0-0.1,0-0.2-0.1c-2.5-0.9-7.6-2-12.4,0.6	c-0.6,0.3-0.8,1-0.5,1.6l6.3,13.3c0.2,0.4,0.6,0.5,0.9,0.3C22.8,139.7,22.9,139.6,23,139.5z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.8,139.5l7.3-16.4c0.3-0.7,1.1-1.1,1.8-1c2.9,0.3,5.4,2.1,6.5,4.8c2.1,5-0.2,16.8-4.6,22.8	c-0.5,0.6-1.3,0.7-2,0.3l-0.1-0.1l-8.5-8.3C22.6,141.1,22.5,140.2,22.8,139.5z",
        },
         {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M21.3,138.7L14.4,123c-0.2-0.6-0.9-0.8-1.5-0.6c-0.1,0-0.2,0.1-0.2,0.1l-4.4,3.4c-0.9,0.7-1.4,1.8-1.4,2.9v3.6	c0,2.2,0.4,4.3,1.2,6.4l3.8,9.9c0.3,0.8,1.1,1.1,1.9,0.9c0.2-0.1,0.4-0.2,0.5-0.4l6.3-6.6C21.6,141.7,21.9,140.1,21.3,138.7z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M13.6,149l8-7.5c0.6-0.6,1.5-0.5,2.1,0.1l7.7,8c0.7,0.8,0.8,1.9,0.2,2.8l-3.8,5.6c-0.8,1.2-2.1,1.9-3.5,1.9	h-2.4c-0.7,0-1.3-0.3-1.8-0.8l-6.6-7.4C12.8,150.9,12.8,149.7,13.6,149z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M32.2,71.2l6.4,12.2c1.3,2.4,1.9,5.1,1.9,7.9v2.3c0,1.8-0.5,3.5-1.5,5c-3.2,5-7.8,8.9-13.1,11.5	c-2,0.9-4.4,1.1-6.5,0.4c-4.7-1.4-9-4-12.2-7.7c-1.9-2.3-2.9-5.1-2.9-8L4.2,90c-0.1-2,0.5-4,1.6-5.6l0.6-0.9c1.3-2,2-4.3,1.9-6.6	l-0.1-2c-0.1-1.9,1-3.5,2.8-4.2l2.9-1.2c0,0,5.2-1.8,16.1,0.1C30.9,69.8,31.7,70.4,32.2,71.2z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        //Onlay
        {
          id: "38",
          code: "st19",
          path: "M272.07,114.23l-3.17,2.4a8.49,8.49,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.48,10.48,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.86,9.86,0,0,1-2.11-.7,18.3,18.3,0,0,0-16.14.31A2.45,2.45,0,0,0,272.07,114.23Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-258 8)",

        },
        {
          id: "39",
          code: "st20",
          path: "M264.46,84.86h4a9.74,9.74,0,0,1,3.39.59l4.64,1.72a7.48,7.48,0,0,0,2.34.44l3.12.09a12,12,0,0,0,4.79-.81h0a9.17,9.17,0,0,1,3.21-.62l8.62,0c.94,0,.46,2,.34,2.76l-.68,2A12.8,12.8,0,0,1,297,92.84c-.19.16-1.42,1.57-2.87,3a39.75,39.75,0,0,1-4,3.33c-1.37.86-5.56,3.53-8.51,3.3a3.75,3.75,0,0,0-.67,0,21.15,21.15,0,0,1-4.66-.56,16.25,16.25,0,0,1-4.85-2.22c-.75-.48-5-3.35-5-3.35a16.93,16.93,0,0,1-2.24-3.42,16.37,16.37,0,0,1-.83-3.11s-.21-2.46-.22-3.88C263.13,85.33,263.64,84.86,264.46,84.86Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-258 8)",

        },
        {
          id: "18",
          code: "st11",
          path: "M288.83,72.41,295,83.81a15.11,15.11,0,0,1,1.82,7.38v2.14A8.18,8.18,0,0,1,295.34,98a30.73,30.73,0,0,1-12.55,10.74,8.89,8.89,0,0,1-6.22.37,24.81,24.81,0,0,1-11.69-7.19,11.48,11.48,0,0,1-2.77-7.47L262,90a8.42,8.42,0,0,1,1.53-5.23l.58-.84a10.43,10.43,0,0,0,1.82-6.17l-.1-1.86a4,4,0,0,1,2.68-3.93l2.78-1.12s5-1.68,15.42.1A3.38,3.38,0,0,1,288.83,72.41Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-257.29 0)"
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 -2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M32.2,71.2l6.4,12.2c1.3,2.4,1.9,5.1,1.9,7.9v2.3c0,1.8-0.5,3.5-1.5,5c-3.2,5-7.8,8.9-13.1,11.5	c-2,0.9-4.4,1.1-6.5,0.4c-4.7-1.4-9-4-12.2-7.7c-1.9-2.3-2.9-5.1-2.9-8L4.2,90c-0.1-2,0.5-4,1.6-5.6l0.6-0.9c1.3-2,2-4.3,1.9-6.6	l-0.1-2c-0.1-1.9,1-3.5,2.8-4.2l2.9-1.2c0,0,5.2-1.8,16.1,0.1C30.9,69.8,31.7,70.4,32.2,71.2z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
      // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-2 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-2  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(8 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(1  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 48) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 48) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
      transforms:"translate(-6 4) ",
    },
     //RootGuttaPerchaMode
     {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M323.08,25.85c0-.38.22-.67.41-.68s.39.34.38.77h0c-.13,3.76-.21,7.56-.2,11.29s.22,7.43.53,11.09a175.51,175.51,0,0,0,3.16,21.19A200.81,200.81,0,0,0,332.55,89a181.63,181.63,0,0,0,6.63,17.66,2.39,2.39,0,0,1-.1,1.91c-.3.5-.74.47-1-.07a190,190,0,0,1-12-37.9A178.84,178.84,0,0,1,323,48.86c-.31-3.77-.47-7.6-.48-11.46s.21-7.72.57-11.55Z",
      transforms:"translate(-304 -8) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M338.64,107.31h0c.76-1.18,1.84-1,2.4.42l3.4,8.62a5.86,5.86,0,0,1-.38,4.71h0c-.77,1.19-1.85,1-2.41-.41L338.26,112A5.81,5.81,0,0,1,338.64,107.31Z",
      transforms:"translate(-304 -8) ",
    },
     //PostCare
     {
      id: "36",
      code: "st17",
      path: "M268.9,70.67a40.89,40.89,0,0,0,.82,5.2l1.54,6.85c.23,1,.77,1.72,1.37,1.72h7.49c.42,0,.81-.39,1.05-1.06A60.54,60.54,0,0,0,283.7,66a11.67,11.67,0,0,0-.07-1.32l-4.76-40.59a1.8,1.8,0,0,0-1.32-1.52,4.18,4.18,0,0,0-.53-.09,2.53,2.53,0,0,0-2.71,1.85C271.82,33.29,267,50.12,268.9,70.67Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-257.7 0)",
    },
     //Veneer
     {
      id: "37",
      code: "st19",
      path: "M290.85,63.21l6.4,12.2a16.46,16.46,0,0,1,1.9,7.9v2.3a8.9,8.9,0,0,1-1.5,5,32.29,32.29,0,0,1-13.1,11.5,9.12,9.12,0,0,1-6.5.4,25.7,25.7,0,0,1-12.2-7.7,12.52,12.52,0,0,1-2.9-8l-.1-4.8a9.15,9.15,0,0,1,1.6-5.6l.6-.9a11.31,11.31,0,0,0,1.9-6.6l-.1-2a4.19,4.19,0,0,1,2.8-4.2",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-258 8)",
    },
        //CrownPermanent 6
        {
          id: "40",
          code: "st19",
          path: "M287.59,63.21l6.4,12.2a16.46,16.46,0,0,1,1.9,7.9v2.3a8.9,8.9,0,0,1-1.5,5,32.29,32.29,0,0,1-13.1,11.5,9.12,9.12,0,0,1-6.5.4,25.7,25.7,0,0,1-12.2-7.7,12.52,12.52,0,0,1-2.9-8l-.1-4.8a9.15,9.15,0,0,1,1.6-5.6l.6-.9a11.31,11.31,0,0,0,1.9-6.6l-.1-2a4.19,4.19,0,0,1,2.8-4.2",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-256 8)",

        },
        {
          id: "41",
          code: "st19",
          path: "M268.81,114.23l-3.17,2.4a8.49,8.49,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.12,10.12,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.31,18.31,0,0,0-16.14.31A2.33,2.33,0,0,0,268.81,114.23Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-256 8)",
        },
        //CrownGold 6
        {
          id: "43",
          code: "st43",
          path: "M295,76.22c-6.22,6.89-16.57,16.16-29.55,21.93l-.91-.63c13.22-5.89,23.37-15.37,29.77-22.4",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-256 6) ",
        },
        {
          id: "44",
          code: "st43",
          path: "M296.25,85.74c-5.77,6.66-14.32,14.56-23.36,16.67l-1.45-.6c9.78-2.06,19-10.55,24.81-17.51",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-256 6) ",
        },
         //CrownZirconia 6
         {
          id: "45",   code: "st26", path :"M287.67,63.3h0a3,3,0,0,0-1.59-1.61c0-.73-.07-2.55-.86-2.88a25,25,0,0,0-5.41-1.54V28.77a1.34,1.34,0,0,0,0-.35l-1.53-5.57c-.35-1.77-1.07-2.87-1.88-2.87s-1.53,1.1-1.88,2.87L273,28.42a1.34,1.34,0,0,0,0,.35V57.16c-3,.33-5.35,1.11-5.92,2.07a4.28,4.28,0,0,0-.33,2.32,3.16,3.16,0,0,0-1.6,1.88M273.84,29l1.49-5.46a.29.29,0,0,0,0-.09c.21-1.1.61-1.81,1-1.81s.79.71,1,1.81a.29.29,0,0,1,0,.09l1.39,5.07-4.93,2.64Zm0,3.95,5-2.7v1.67l-5,2.7Zm0,3.36,5-2.7v1.67l-5,2.7Zm0,3.37,5-2.7v1.67l-5,2.7Zm0,3.37,5-2.71V42l-5,2.71Zm0,3.36,5-2.7v1.67l-5,2.7Zm0,3.37,5-2.7V48.7l-5,2.71Zm0,3.36,5-2.7v1.67l-5,2.7Zm0,3.37,5-2.7v3.37A19.75,19.75,0,0,0,276.4,57h-.18c-.82,0-1.62,0-2.38.1Zm-6.21,3.93c.33-.54,3.31-1.83,8.77-1.8A23.65,23.65,0,0,1,285,60.37a2.69,2.69,0,0,1,.15.87,29.25,29.25,0,0,0-9.05-1.55c-.08,0-5.27,0-8.52,1.4A1.87,1.87,0,0,1,267.63,60.4Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-256 8) ",},
         {
          id: "46",   code: "st26", path :"M294,75.42l-6.22-11.86H265a4.16,4.16,0,0,0-1.4,3.36l.1,2a11.31,11.31,0,0,1-1.9,6.6l-.6.9a9.15,9.15,0,0,0-1.6,5.6l.1,4.8a12.52,12.52,0,0,0,2.9,8,25.7,25.7,0,0,0,12.2,7.7,9.12,9.12,0,0,0,6.5-.4,32.29,32.29,0,0,0,13.1-11.5,8.9,8.9,0,0,0,1.5-5v-2.3A16.46,16.46,0,0,0,294,75.42Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-256 8) ",},
         {
          id: "47",
          code: "st26",
          path: "M268.81,114.23l-3.17,2.4a8.49,8.49,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.12,10.12,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.31,18.31,0,0,0-16.14.31A2.33,2.33,0,0,0,268.81,114.23Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-256 8)",
        },
        //Denture 6
        {
          id: "48",
          code: "st19",
          path: "M290.85,63.21l6.4,12.2a16.46,16.46,0,0,1,1.9,7.9v2.3a8.9,8.9,0,0,1-1.5,5,32.29,32.29,0,0,1-13.1,11.5,9.12,9.12,0,0,1-6.5.4,25.7,25.7,0,0,1-12.2-7.7,12.52,12.52,0,0,1-2.9-8l-.1-4.8a9.15,9.15,0,0,1,1.6-5.6l.6-.9a11.31,11.31,0,0,0,1.9-6.6l-.1-2a4.19,4.19,0,0,1,2.8-4.2",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-258 8)",

        },
        {
          id: "49",
          code: "st19",
          path: "M272.07,114.23l-3.17,2.4a8.49,8.49,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.48,10.48,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.86,9.86,0,0,1-2.11-.7,18.3,18.3,0,0,0-16.14.31A2.45,2.45,0,0,0,272.07,114.23Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-258 8)",

        },
        {
          id: "50",
          code: "st50",
          polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(1 16)",
        },
         //Bridge 6
         {
          id: "51",
          code: "st0",
          rect: {
            x: "0.5",
            y: "66.57",
            width: "62.4318",
            height: "2.8"
          },
          style: { cursor: "pointer" },position: "",
           transforms:"translate(0 20)",
        },
        {
          id: "52",
          code: "st0",
          path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-2.5 0)",
        },
        //Implant 6
        {
          id: "54",
          code: "st9",
          path: "M9.76,62.32V73.45s4.72,8.45,11.52,9.88c0,0,10-3.87,12.44-10.5.13-.09,0-4.5,0-4.5Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-5.51 -1.29)"
        },

        {
          id: "55",
          code: "st9",
          polygon: "9.2 20.77 23.26 24.34 24.24 33.8 8.21 29.1 9.2 20.77",
          style: { cursor: "pointer" },position: "",
        },
        {
          id: "56",
          code: "st9",
          polygon: "7.76 34.54 25.04 39.24 26.2 48.77 6.76 42.64 7.76 34.54",
          style: { cursor: "pointer" },position: "",
        },
        {
          id: "57",
          code: "st9",
          polygon: "6.08 48.05 26.98 53.94 28.21 62.81 5.04 56.44 6.08 48.05",
          style: { cursor: "pointer" },position: "",
        },
        {
          id: "58",
          code: "st9",
          path: "M15.86,15.37l11.94,4.1C27,13.27,26.35,8.89,26.26,8.69c-2.65-9.48-5.61-8-7.45-5.58a11.21,11.21,0,0,0-2.08,5.35l-.87,6.91",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-5.51 -1.29)"
        },
        {
          id: "59",
          code: "st10",
          path: "M34,71.74l6.4,12.2a16.46,16.46,0,0,1,1.9,7.9v2.3a8.9,8.9,0,0,1-1.5,5,32.36,32.36,0,0,1-13.1,11.5,9.12,9.12,0,0,1-6.5.4A25.78,25.78,0,0,1,9,103.34a12.5,12.5,0,0,1-2.9-8L6,90.54a9.18,9.18,0,0,1,1.6-5.6l.6-.9a11.27,11.27,0,0,0,1.9-6.6l-.1-2a4.2,4.2,0,0,1,2.8-4.2l2.9-1.2s5.2-1.8,16.1.1A3.56,3.56,0,0,1,34,71.74Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-5.51 -1.29)"
        },
        {
          id: "60",
          code: "st10",
          path: "M33.78,122.1s-12-6.79-22.83,3.06a6.92,6.92,0,0,0-2.13,3.92c-.58,3.35-.68,10,3.89,19.22a9.18,9.18,0,0,0,2.07,2.76c1.7,1.55,4.84,4.91,6.75,7a6.36,6.36,0,0,0,4,2h0a3.71,3.71,0,0,0,3.52-1.68L33.87,151S50,128.46,33.78,122.1Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-5.51 -1.29)"
        },
         //Bone 6
         {

          id: "61",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
         transforms:"translate(0 -80) ",
        },
        {

          id: "62",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
         transforms:"translate(0 -72) ",
        },
        {

          id: "63",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
         transforms:"translate(0 -64) ",
        },
        {

          id: "64",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
         transforms:"translate(0 -56) ",
        },
        {

          id: "65",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
         transforms:"translate(0 -48) ",
        },
        {

          id: "66",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
         transforms:"translate(0 -40) ",
        },
        {
          id: "67",
          code: "st67",
          path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
          style: { cursor: "pointer" },position: "",
           transforms:"scale(0.48),translate(100 238),rotate(-180)"
        },
         //Resection 6
         {
          id: "68",
          code: "st22",
          rect: {
            x: "25.54",
            y: "16.61",
            width: "3.93",
            height: "40.43"
          },
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-25 60) rotate(-65.8)",
        },
        //TeethCrown 6
        {
          id: "69",
          code: "st67",
          path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
          style: { cursor: "pointer" },position: "",
           transforms:"scale(0.6),translate(86 186),rotate(-180)"
        },
      ],
    },
    // Tooth  52: 'Upper Right Primary Lateral Incisor',
    {
      svg_id: "7",
      tooth_number: 52,
      tooth_name: TOOTH_NUMBERS[52],
      tooth_type: "incisor",
      quadrant: "upper_right",
      tooth_position: 7,
      is_permanent: true,
      width:"48.35px",
      position:"0 0 40.9 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M13,68.1c-1.3,0.2-2.4,1.2-2.7,2.4L5.9,86.8c-0.7,2.3-1.1,4.7-1.2,7.1c-0.2,4.3,1.7,8.5,5.1,11.1  c5.7,4.4,12.9,6.5,20.1,5.9c1.2-0.4,2.4-0.9,3.5-1.5c3.4-1.8,5.5-5.3,5.5-9.2v-4.1c0-1.4-0.2-2.7-0.6-4l0,0  c-0.9-2.8-1.5-5.7-1.8-8.7l-1.1-11c0-0.3-0.1-0.7-0.2-1c-0.5-1.5-1.9-2.6-3.4-2.8C26.9,68.2,17.7,67.4,13,68.1z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M10.5,69.1l5.9-54.3c0.1-1.2,0.6-2.3,1.3-3.2l0,0c0.9-1.2,2.5-1.5,3.8-0.8l0,0c0.3,0.1,0.6,0.3,0.9,0.3l1.8,0.4  c1.6,0.4,3,1.5,3.8,2.9l0.4,0.7c0.4,0.7,0.6,1.5,0.7,2.4l5.5,52.6C34.6,70.2,14.2,64.4,10.5,69.1z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M18.6,62.2h8.2l-1.1-39.7c0-1.4-0.4-2.8-1.2-4c-0.6-1-1.4-1.8-2.3-2.5c-0.6-0.4-1.4-0.2-1.7,0.4  c-0.1,0.2-0.2,0.4-0.2,0.6L18.6,62.2z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },

        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M22.1,139.3l7.1-14.3c0.4-0.9,0.2-2-0.5-2.6l-0.8-0.7c-0.4-0.3-0.8-0.5-1.3-0.4l-11.7,0.3  c-0.4,0-0.8,0.2-1.1,0.5l-0.3,0.3c-0.6,0.6-0.7,1.4-0.4,2.1l7,14.8c0.2,0.5,0.9,0.8,1.4,0.5C21.8,139.7,22,139.5,22.1,139.3z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.3,138.5l6.1-11.6c0.4-0.7,0.2-1.6-0.5-2.1l-0.7-0.6c-0.3-0.2-0.7-0.4-1.1-0.4l-10,0.3  c-0.3,0-0.7,0.1-0.9,0.4l-0.2,0.2c-0.5,0.5-0.6,1.1-0.3,1.7l6,12.1c0.2,0.4,0.7,0.6,1.2,0.4C22,138.9,22.2,138.7,22.3,138.5z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M29.5,123.2l-7.6,16c-0.3,0.6-0.2,1.4,0.3,1.9l7.4,8.3c0.5,0.6,1.4,0.6,2,0.1c0.1-0.1,0.3-0.3,0.3-0.5l5.1-11.3  c0.3-0.6,0.5-1.3,0.6-2c0.6-3.2,1.6-12-6.4-13.4C30.6,122,29.8,122.5,29.5,123.2z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M13,148.5c3-2.7,8.4-7.4,8.4-7.4l7.8,7.9c1.1,1.1,1.3,2.9,0.3,4.2l-4.1,5.4c-0.6,0.8-1.5,1.2-2.4,1.2h-2.8  c-0.8,0-1.5-0.4-2-1l-5.4-6.7C11.9,151.1,12,149.4,13,148.5z",
        },
         {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M20.7,138.9l-7.5-15.7c-0.3-0.5-0.9-0.9-1.5-0.7c-2.6,0.5-9.1,2.9-5.7,15c0,0.1,0.1,0.3,0.1,0.4l4.4,10.8  c0.3,0.7,1,1,1.6,0.7c0.2-0.1,0.3-0.2,0.4-0.3l7.6-7.6C21,140.8,21.1,139.8,20.7,138.9z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M13,68.1c-1.3,0.2-2.4,1.2-2.7,2.4L5.9,86.8c-0.7,2.3-1.1,4.7-1.2,7.1c-0.2,4.3,1.7,8.5,5.1,11.1  c5.7,4.4,12.9,6.5,20.1,5.9c1.2-0.4,2.4-0.9,3.5-1.5c3.4-1.8,5.5-5.3,5.5-9.2v-4.1c0-1.4-0.2-2.7-0.6-4l0,0  c-0.9-2.8-1.5-5.7-1.8-8.7l-1.1-11c0-0.3-0.1-0.7-0.2-1c-0.5-1.5-1.9-2.6-3.4-2.8C26.9,68.2,17.7,67.4,13,68.1z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(5.25 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M309.48,82.82h3.72a8,8,0,0,1,3.12.62l4.28,1.83a6.18,6.18,0,0,0,2.15.47l2.87.1A9.75,9.75,0,0,0,330,85h0a7.19,7.19,0,0,1,3-.65l8.22.08c.87,0,.54,2.33.42,3.1l.16,3.08c-.24.43.19,3.17-.15,3.5-.17.18-1.05,3.42-2.38,4.89-1.59,1.75-4.9,3-5.09,3.11-1.26.91-6,.35-8.69.11a6.56,6.56,0,0,1-.87-.21c-.52,0-2.43-.69-4.25-1.11a14,14,0,0,1-4.47-2.36,18.66,18.66,0,0,1-2.51-1.4A12.64,12.64,0,0,1,311.3,95a18.12,18.12,0,0,1-2.06-3.63,19.77,19.77,0,0,1-.77-3.3s-.2-2.61-.2-4.12A1.1,1.1,0,0,1,309.48,82.82Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-304 8)",

        },
       // Onlay
        {
          id: "38",
          code: "st19",
          path: "M313.87,115.12l-2,1.47a8.41,8.41,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.12,9.12,0,0,0,1.29,1.74c1.28,1.36,4.28,4.89,6.34,7.33a7.06,7.06,0,0,0,5.41,2.5h0a3.4,3.4,0,0,0,2.67-1.29l5.89-7.59a15.74,15.74,0,0,0,1.69-2.66c2.74-5.39,10.85-23.54-2.4-26-.74-.13-1.41-.54-2.15-.69-2.48-.53-7.64-.38-14.5.15A8.25,8.25,0,0,0,313.87,115.12Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-304 8)",

        },
        {
          id: "18",
          code: "st11",
          path: "M315,69.44a3.17,3.17,0,0,0-2.55,2.24l-4.15,15.23a26.24,26.24,0,0,0-1.13,6.63A12.31,12.31,0,0,0,312,103.91a27.58,27.58,0,0,0,19,5.51,20.16,20.16,0,0,0,3.31-1.4,9.63,9.63,0,0,0,5.19-8.59V95.6a12.28,12.28,0,0,0-.57-3.74h0a39.28,39.28,0,0,1-1.7-8.13l-1-10.27a3.47,3.47,0,0,0-.18-.94,4,4,0,0,0-3.21-2.61C328.11,69.53,319.43,68.79,315,69.44Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-301.29 0)"
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-2 -2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M13,68.1c-1.3,0.2-2.4,1.2-2.7,2.4L5.9,86.8c-0.7,2.3-1.1,4.7-1.2,7.1c-0.2,4.3,1.7,8.5,5.1,11.1  c5.7,4.4,12.9,6.5,20.1,5.9c1.2-0.4,2.4-0.9,3.5-1.5c3.4-1.8,5.5-5.3,5.5-9.2v-4.1c0-1.4-0.2-2.7-0.6-4l0,0  c-0.9-2.8-1.5-5.7-1.8-8.7l-1.1-11c0-0.3-0.1-0.7-0.2-1c-0.5-1.5-1.9-2.6-3.4-2.8C26.9,68.2,17.7,67.4,13,68.1z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(5.25 22)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
          {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
          {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-5 8)",style: { cursor: "pointer" },position: "",},
         // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-1 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-1  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(8 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(-1  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 48) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 48) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
      transforms:"translate(-6 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
      transforms:"translate(-6 4) ",
    },
     //RootGuttaPerchaMode
     {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M371.7,33.61c0-.33.19-.59.37-.59s.34.3.32.67h0c-.11,3.31-.18,6.65-.16,9.93s.19,6.55.46,9.76A153.42,153.42,0,0,0,375.48,72a172.21,172.21,0,0,0,4.58,17.15,157.9,157.9,0,0,0,5.84,15.54,2,2,0,0,1-.09,1.68c-.26.45-.64.42-.86-.06A166.78,166.78,0,0,1,374.4,73a159.54,159.54,0,0,1-2.78-19.15c-.27-3.32-.42-6.69-.42-10.09s.18-6.78.5-10.16Z",
      transforms:"translate(-347 -2) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M385.43,105.29h0c.67-1,1.62-.88,2.11.37l3,7.58a5.15,5.15,0,0,1-.33,4.15h0c-.68,1-1.63.88-2.12-.37l-3-7.58A5.1,5.1,0,0,1,385.43,105.29Z",
      transforms:"translate(-347 -2) ",
    },
    //PostCare
    {
      id: "36",
      code: "st17",
      path: "M317.25,70.67a39.18,39.18,0,0,0,.82,5.2l1.53,6.85c.24,1,.78,1.72,1.38,1.72h7.49c.42,0,.81-.39,1-1.06a56,56,0,0,0,2.92-17.93l-4.67-42.76a9.57,9.57,0,0,0-3.23-6.2c-1.18-1-2.32-1.71-2.61-1C319.26,22.39,315.42,50.73,317.25,70.67Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-301.5 0)",
    },
     //Veneer
     {
      id: "37",
      code: "st19",
      path: "M313.51,62.57l-4.4,16.3a28.45,28.45,0,0,0-1.2,7.1,13.24,13.24,0,0,0,5.1,11.1,29,29,0,0,0,20.1,5.9,22.49,22.49,0,0,0,3.5-1.5,10.35,10.35,0,0,0,5.5-9.2v-4.1a13.51,13.51,0,0,0-.6-4h0a41.78,41.78,0,0,1-1.8-8.7l-1.1-11a3.78,3.78,0,0,0-.2-1",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-304 8)",
    },
            //CrownPermanent 7
            {
              id: "40",
              code: "st19",
              path: "M310.25,62.57l-4.4,16.3a28.53,28.53,0,0,0-1.2,7.1,13.24,13.24,0,0,0,5.1,11.1,29,29,0,0,0,20.1,5.9,22.08,22.08,0,0,0,3.5-1.5,10.35,10.35,0,0,0,5.5-9.2v-4.1a13.56,13.56,0,0,0-.6-4h0a41.65,41.65,0,0,1-1.8-8.7l-1.1-11a3.78,3.78,0,0,0-.2-1",
              style: { cursor: "pointer" },position: "",
              transforms:"translate(-300 8)",

            },
            {
              id: "41",
              code: "st19",
              path: "M310.61,115.12l-2,1.47a8.44,8.44,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.12,9.12,0,0,0,1.29,1.74c1.28,1.36,4.28,4.89,6.34,7.33a7.06,7.06,0,0,0,5.41,2.5h0a3.4,3.4,0,0,0,2.67-1.29l5.89-7.59a15.74,15.74,0,0,0,1.69-2.66c2.74-5.39,10.85-23.54-2.4-26-.74-.13-1.41-.54-2.15-.69-2.48-.53-7.64-.38-14.5.15A8.3,8.3,0,0,0,310.61,115.12Z",
              style: { cursor: "pointer" },position: "",
              transforms:"translate(-300 8)",
            },
              //CrownGold 7
              {
                id: "43",
                code: "st43",
                path: "M337,73.93c-6.22,6.88-17.62,17.15-28.72,22.11l-.94-1.19C318.67,89.78,330.6,79.06,337,72",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-300 8) ",
              },
              {
                id: "44",
                code: "st43",
                path: "M338.49,83.12c-5.77,6.67-14.07,14.38-24.51,16.95l-1.24-.6c11.17-2.52,19.82-10.66,25.67-17.62",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-300 8) ",
              },
               //CrownZirconia 7
               {
                id: "45",   code: "st26", path :"M335.5,63.3h0a3.23,3.23,0,0,0-1.78-1.59c0-.72-.08-2.52-1-2.85a30.84,30.84,0,0,0-6.07-1.51V29.21a1.34,1.34,0,0,0-.05-.35l-1.72-5.51c-.39-1.75-1.19-2.83-2.11-2.83s-1.72,1.08-2.11,2.83L319,28.86a1,1,0,0,0-.05.35v28c-3.38.34-6,1.1-6.65,2.05a3.82,3.82,0,0,0-.37,2.3,3.3,3.3,0,0,0-1.8,1.85m9.86-34L321.65,24l0-.09c.23-1.09.67-1.79,1.12-1.79s.9.7,1.13,1.79c0,0,0,.06,0,.09l1.57,5L320,31.62Zm0,3.9,5.67-2.67v1.65L320,34.94Zm0,3.32L325.64,34v1.64L320,38.26Zm0,3.33,5.67-2.67v1.65L320,41.59Zm0,3.32,5.67-2.67v1.65L320,44.91Zm0,3.33,5.67-2.67v1.65L320,48.23Zm0,3.32,5.67-2.67v1.65L320,51.56Zm0,3.32,5.67-2.67v1.65L320,54.88Zm0,3.33,5.67-2.67v3.33a24.94,24.94,0,0,0-2.79-.16h-.2c-.92,0-1.82,0-2.68.09Zm-7,3.88c.37-.54,3.72-1.81,9.85-1.78a29.82,29.82,0,0,1,9.65,1.75,2.58,2.58,0,0,1,.16.86,37.27,37.27,0,0,0-10.16-1.54c-.08,0-5.91,0-9.57,1.39A1.75,1.75,0,0,1,313,60.44Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-300 8) ",},
               {
                id: "46",   code: "st26", path :"M336.69,75.41l-1.1-11a3.56,3.56,0,0,0-.16-.84H310.2l-4.12,15.25a28.53,28.53,0,0,0-1.2,7.1A13.24,13.24,0,0,0,310,97a29,29,0,0,0,20.1,5.9,22.08,22.08,0,0,0,3.5-1.5,10.35,10.35,0,0,0,5.5-9.2v-4.1a13.56,13.56,0,0,0-.6-4A41.65,41.65,0,0,1,336.69,75.41Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-300 8) ",},
               {
                id: "47",
                code: "st26",
                path: "M310.61,115.12l-2,1.47a8.44,8.44,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.12,9.12,0,0,0,1.29,1.74c1.28,1.36,4.28,4.89,6.34,7.33a7.06,7.06,0,0,0,5.41,2.5h0a3.4,3.4,0,0,0,2.67-1.29l5.89-7.59a15.74,15.74,0,0,0,1.69-2.66c2.74-5.39,10.85-23.54-2.4-26-.74-.13-1.41-.54-2.15-.69-2.48-.53-7.64-.38-14.5.15A8.3,8.3,0,0,0,310.61,115.12Z",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-300 8)",
              },
              //Denture 7
              {
                id: "48",
                code: "st19",
                path: "M313.51,62.57l-4.4,16.3a28.45,28.45,0,0,0-1.2,7.1,13.24,13.24,0,0,0,5.1,11.1,29,29,0,0,0,20.1,5.9,22.49,22.49,0,0,0,3.5-1.5,10.35,10.35,0,0,0,5.5-9.2v-4.1a13.51,13.51,0,0,0-.6-4h0a41.78,41.78,0,0,1-1.8-8.7l-1.1-11a3.78,3.78,0,0,0-.2-1",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-304 8)",

              },
              {
                id: "49",
                code: "st19",
                path: "M313.87,115.12l-2,1.47a8.41,8.41,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.12,9.12,0,0,0,1.29,1.74c1.28,1.36,4.28,4.89,6.34,7.33a7.06,7.06,0,0,0,5.41,2.5h0a3.4,3.4,0,0,0,2.67-1.29l5.89-7.59a15.74,15.74,0,0,0,1.69-2.66c2.74-5.39,10.85-23.54-2.4-26-.74-.13-1.41-.54-2.15-.69-2.48-.53-7.64-.38-14.5.15A8.25,8.25,0,0,0,313.87,115.12Z",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-304 8)",

              },
              {
                id: "50",
                code: "st50",
                polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(1 16)",
              },
               //Bridge 7
               {
                id: "51",
                code: "st0",
                rect: {
                  x: "0.5",
                  y: "66.57",
                  width: "62.4318",
                  height: "2.8"
                },
                style: { cursor: "pointer" },position: "",
                 transforms:"translate(-3 20)",
              },
              {
                id: "52",
                code: "st0",
                path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
                style: { cursor: "pointer" },position: "",
                transforms:"translate(3 0)",
              },
                //Implant 7
                {
                  id: "54",
                  code: "st10",
                  path: "M30,122s-10.17-4.06-15.94.07a5.06,5.06,0,0,1-1.82.81c-3.65.85-14.74,5.33-.24,27.52l5,7.46a5.32,5.32,0,0,0,4.79,2.31h0a5.3,5.3,0,0,0,3.75-2l4.55-5.63c.11-.14.22-.28.32-.43C32.18,149.58,46.83,126.38,30,122Z",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-4.25 -2.36)"
                },
                {
                  id: "55",
                  code: "st9",
                  path: "M11,63.39V74.52S15.67,83,22.48,84.4c0,0,10-3.87,12.43-10.51.14-.09,0-4.49,0-4.49Z",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-4.25 -2.36)"
                },
                {
                  id: "56",
                  code: "st9",
                  polygon: "11.66 20.77 25.72 24.34 26.7 33.8 10.66 29.1 11.66 20.77",
                  style: { cursor: "pointer" },position: "",
                },
                {
                  id: "57",
                  code: "st9",
                  polygon: "10.22 34.54 27.5 39.24 28.65 48.77 9.22 42.64 10.22 34.54",
                  style: { cursor: "pointer" },position: "",
                },
                {
                  id: "58",
                  code: "st9",
                  polygon: "8.54 48.05 29.44 53.94 30.66 62.81 7.49 56.44 8.54 48.05",
                  style: { cursor: "pointer" },position: "",
                },
                {
                  id: "59",
                  code: "st9",
                  path: "M17.06,16.44,29,20.54C28.16,14.33,27.55,10,27.46,9.76,24.81.27,21.85,1.74,20,4.17a11.13,11.13,0,0,0-2.07,5.36l-.87,6.91",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-4.25 -2.36)"
                },
                {
                  id: "60",
                  code: "st10",
                  path: "M13.46,69.05a3.35,3.35,0,0,0-2.7,2.4l-4.4,16.3a28.53,28.53,0,0,0-1.2,7.1,13.24,13.24,0,0,0,5.1,11.1,29,29,0,0,0,20.1,5.9,21.69,21.69,0,0,0,3.5-1.5,10.33,10.33,0,0,0,5.5-9.2v-4.1a13.36,13.36,0,0,0-.6-4h0a42,42,0,0,1-1.8-8.7l-1.1-11a3.55,3.55,0,0,0-.2-1,4.25,4.25,0,0,0-3.4-2.8C27.36,69.15,18.16,68.35,13.46,69.05Z",
                  style: { cursor: "pointer" },position: "",
                  transforms:"translate(-4.25 -2.36)"
                },
                 //Bone 7
          {

            id: "61",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -80) ",
          },
          {

            id: "62",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -72) ",
          },
          {

            id: "63",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -64) ",
          },
          {

            id: "64",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -56) ",
          },
          {

            id: "65",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -48) ",
          },
          {

            id: "66",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -40) ",
          },
          {
            id: "67",
            code: "st67",
            path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.46),translate(98 245),rotate(-180)"
          },
              //Resection 7
              {
                id: "68",
                code: "st22",
                rect: {
                  x: "25.54",
                  y: "16.61",
                  width: "3.93",
                  height: "40.43"
                },
                style: { cursor: "pointer" },position: "",
                transforms:"translate(-23 60) rotate(-65.8)",
              },
              //TeethCrown 7
              {
                id: "69",
                code: "st67",
                path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
                style: { cursor: "pointer" },position: "",
                 transforms:"scale(0.55),translate(90 200),rotate(-180)"
              },
      ],
    },
    // Tooth 51: 'Upper Right Primary Central Incisor',
    {
      svg_id: "8",
      tooth_number: 51,
      tooth_name: TOOTH_NUMBERS[51],
      tooth_type: "Central Incisor",
      quadrant: "upper_right",
      tooth_position: 8,
      is_permanent: true,
      width:"54.75px",
      position:"0 0 46.4 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M11.1,72.7c-1.7,5.1-5.2,16.9-5.2,26.4c0,3,1.3,5.8,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6  c1.2,0,2.3-0.3,3.4-0.8c2.9-1.5,8.1-5.2,5.8-11.4c-0.2-0.7-4.5-19.1-6-26c-0.4-1.6-1.7-2.9-3.4-3.1c-4.9-0.7-15.2-2-21.2-0.8  C13.3,69.3,11.7,70.7,11.1,72.7z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M39.8,72.9c0,0,1.2-44.6-15.1-62.5c0,0-3.9-3.2-5.5,5.4c0,0.7-7.1,54.3-7.1,54.3S29.5,66.5,39.8,72.9z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M25.1,64.5l0.7,0.1c2.2,0.2,4.1-1.4,4.2-3.6c0-0.1,0-0.2,0-0.3c0-9.1-0.4-30.4-3.9-37.9c-0.2-0.4-0.6-0.5-1-0.4  c-0.2,0.1-0.4,0.3-0.4,0.6c-0.6,5-2.1,18.9-3.2,37.4C21.4,62.5,23,64.4,25.1,64.5z",
        },
        {
          id: "4",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M35.1,121.9c0,0-9.3-4.2-16.7,0.7c0,0.4,8.4,18.4,8.4,18.4L35.1,121.9z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M33.5,125.2c0,0-7.6-3.4-13.6,0.5c0,0.3,6.8,14.9,6.8,14.9L33.5,125.2z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M34.4,123.2l-7,16.5c-0.3,0.6-0.1,1.3,0.4,1.8l8.5,8.4c0.5,0.5,1.3,0.5,1.8,0c0.1-0.1,0.2-0.2,0.2-0.3  c2.7-5.8,10.9-25.5-2.1-27.3C35.4,122.2,34.7,122.6,34.4,123.2z",
        },

        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M25.5,141.6l-7.4,7.4c-0.6,0.6-0.7,1.6-0.2,2.4l5.3,8.2c0.4,0.7,1.2,1.1,2,1l2.7-0.2c0.8-0.1,1.6-0.5,2.1-1.2  l5.4-6.9c0.7-0.9,0.6-2.2-0.2-3.1l-7.8-7.6C26.9,141.1,26.1,141.1,25.5,141.6z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M26.6,140.7l-8.2-18c0,0-5.6-1.2-7.6,5.8c0,0-2.2,15.9,6.3,21.8L26.6,140.7z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M11.1,72.7c-1.7,5.1-5.2,16.9-5.2,26.4c0,3,1.3,5.8,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6  c1.2,0,2.3-0.3,3.4-0.8c2.9-1.5,8.1-5.2,5.8-11.4c-0.2-0.7-4.5-19.1-6-26c-0.4-1.6-1.7-2.9-3.4-3.1c-4.9-0.7-15.2-2-21.2-0.8  C13.3,69.3,11.7,70.7,11.1,72.7z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M355.62,84.63h4.2a10,10,0,0,1,3.53.63l4.84,1.82a7.83,7.83,0,0,0,2.44.47l3.24.1a12.54,12.54,0,0,0,5-.86h0a9,9,0,0,1,3.34-.66l9.31.09c1,0,1.28,2.37,1.15,3.14l1,3.33c-.27.43-.23,4-.61,4.34a15.93,15.93,0,0,1-3.76,3.76,17.81,17.81,0,0,1-4.49,1.85c-1.67.4-7,.15-10.05-.08-.24,0-1.95,0-1.95,0-.59,0-2.79.23-4.89.1-2.47-.15-5.42-.61-6-1L358.7,99.5A22.64,22.64,0,0,1,356,97.22a25.4,25.4,0,0,1-2.18-3.72c-.5-1.06.25-2.78,0-3.75,0,0,.17-2.56.39-4A1.26,1.26,0,0,1,355.62,84.63Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-348 8)",

        },
         // Onlay
        {
          id: "38",
          code: "st19",
          path: "M365,113.3l-3.17,2.4a8.46,8.46,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.48,10.48,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.65-23.53-2.37-26a9.86,9.86,0,0,1-2.11-.7,18.3,18.3,0,0,0-16.14.31A2.45,2.45,0,0,0,365,113.3Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-348 8)",

        },
        {
          id: "18",
          code: "st11",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
          path: "M11.1,72.7c-1.7,5.1-5.2,16.9-5.2,26.4c0,3,1.3,5.8,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6  c1.2,0,2.3-0.3,3.4-0.8c2.9-1.5,8.1-5.2,5.8-11.4c-0.2-0.7-4.5-19.1-6-26c-0.4-1.6-1.7-2.9-3.4-3.1c-4.9-0.7-15.2-2-21.2-0.8  C13.3,69.3,11.7,70.7,11.1,72.7z",
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 -2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M11.1,72.7c-1.7,5.1-5.2,16.9-5.2,26.4c0,3,1.3,5.8,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6  c1.2,0,2.3-0.3,3.4-0.8c2.9-1.5,8.1-5.2,5.8-11.4c-0.2-0.7-4.5-19.1-6-26c-0.4-1.6-1.7-2.9-3.4-3.1c-4.9-0.7-15.2-2-21.2-0.8  C13.3,69.3,11.7,70.7,11.1,72.7z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-2 8)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-2 8)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-2 8)",style: { cursor: "pointer" },position: "",},
       // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(3 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(3  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(14 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(-1  8) ",
  },
   //RootTemporary
   {
    id: "28",
    code: "st0",
    path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-14 48) ",
  },
  {
    id: "29",
    code: "st0",
    path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-14 48) ",
  },
   //RootCalcium
   {
    id: "30",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
    transforms:"translate(0 8) ",
  },
  {
    id: "31",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
    transforms:"translate(0 8) ",
  },
  {
    id: "32",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
    transforms:"translate(0 8) ",
  },
  {
    id: "33",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
    transforms:"translate(0 8) ",
  },
   //RootGuttaPerchaMode
   {
    id: "34",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M425.09,24c0-.39.22-.67.42-.68s.38.34.37.77h0c-.13,3.76-.21,7.55-.19,11.28s.22,7.44.52,11.09a175.66,175.66,0,0,0,3.17,21.2,198,198,0,0,0,5.18,19.5,184.27,184.27,0,0,0,6.63,17.66,2.33,2.33,0,0,1-.1,1.9c-.3.51-.73.48-1-.06a189.8,189.8,0,0,1-11.95-37.9A181.1,181.1,0,0,1,425,47c-.31-3.77-.47-7.6-.48-11.46s.21-7.72.57-11.55Z",
    transforms:"translate(-401 -2) ",
  },
  {
    id: "35",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M440.65,105.41h0c.77-1.18,1.84-1,2.4.42l3.4,8.62a5.79,5.79,0,0,1-.38,4.71h0c-.77,1.19-1.84,1-2.4-.42l-3.4-8.62A5.79,5.79,0,0,1,440.65,105.41Z",
    transforms:"translate(-401 -2) ",
  },
   //PostCare
   {
    id: "36",
    code: "st17",
    path: "M365.23,71.32a40.89,40.89,0,0,0,.82,5.2l1.54,6.84c.23,1,.77,1.73,1.37,1.73h7.49c.42,0,.81-.4,1.05-1.06.88-2.51,1.72-9.15,1.72-17.83l-4.54-43.4c-.18-1.34-3.61-1.72-3.9-.44C369.12,29.67,363,46.85,365.23,71.32Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-346 0)",
  },
   //Veneer
   {
    id: "37",
    code: "st19",
    path: "M360.05,62.81c-1.7,5.1-6.64,18.69-6.64,28.19a10.17,10.17,0,0,0,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6a8,8,0,0,0,3.4-.8c2.9-1.5,8.1-5.2,5.8-11.4-.2-.7-4.5-19.1-6-26",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-348 8)",
  },
  //CrownPermanent 8
  {
    id: "40",
    code: "st19",
    path: "M356.79,62.81c-1.7,5.1-6.64,18.69-6.64,28.19a10.17,10.17,0,0,0,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3H381a8,8,0,0,0,3.4-.8c2.9-1.5,8.1-5.2,5.8-11.4-.2-.7-4.5-19.1-6-26",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-345 8)",

  },
  {
    id: "41",
    code: "st19",
    path: "M361.74,113.3l-3.17,2.4a8.47,8.47,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.12,10.12,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.65-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.31,18.31,0,0,0-16.14.31A2.33,2.33,0,0,0,361.74,113.3Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-345 8)",
  },
  //CrownGold 8
  {
    id: "43",
    code: "st43",
    path: "M387.78,77c-6.65,7.23-18.18,17.62-32.06,23.68l-1.05-.83c14.13-6.18,26-17.12,32.83-24.5",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-345 6) ",
  },
  {
    id: "44",
    code: "st43",
    path: "M389.65,86c-6.22,7-15.18,15.37-24.92,17.59l-3.2-.19c10.61-1.84,21.72-11.4,28-18.75",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-345 6) ",
  },
  //CrownZirconia 8
  {
    id: "45",   code: "st26", path :"M384.5,65.15h0a3.49,3.49,0,0,0-2-1.57c0-.72-.09-2.5-1.11-2.82a39.38,39.38,0,0,0-6.91-1.51V31.37a1,1,0,0,0-.06-.35l-1.95-5.45c-.45-1.74-1.36-2.81-2.4-2.81s-2,1.07-2.4,2.81l-2,5.45a1,1,0,0,0-.06.35V59.14c-3.84.33-6.83,1.09-7.56,2a3.38,3.38,0,0,0-.42,2.27,3.54,3.54,0,0,0-2,1.84m11.22-33.74,1.92-5.33a.3.3,0,0,0,0-.1c.27-1.07.77-1.77,1.29-1.77s1,.7,1.28,1.77l0,.1,1.77,4.95-6.31,2.59Zm0,3.87,6.45-2.64V34.4l-6.45,2.65Zm0,3.3,6.45-2.65V37.7l-6.45,2.64Zm0,3.29,6.45-2.64V41l-6.45,2.64Zm0,3.29,6.45-2.64v1.63l-6.45,2.65Zm0,3.3,6.45-2.65v1.64l-6.45,2.64Zm0,3.29,6.45-2.64v1.63l-6.45,2.65Zm0,3.3,6.45-2.65v1.64l-6.45,2.64Zm0,3.29,6.45-2.64v3.3c-1-.1-2.07-.16-3.17-.17h-.22c-1.05,0-2.08,0-3.06.1Zm-7.93,3.85c.42-.53,4.23-1.79,11.21-1.77a38.44,38.44,0,0,1,11,1.74,1.93,1.93,0,0,1,.19.85,48.26,48.26,0,0,0-11.57-1.52c-.09,0-6.73,0-10.89,1.37A1.49,1.49,0,0,1,358.89,62.32Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-345 8) ",},
   {
    id: "46",   code: "st26", path :"M384.66,65.28H355.6c-1.76,5.35-5,16.66-5,25.84a10.17,10.17,0,0,0,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6a8.16,8.16,0,0,0,3.4-.8c2.9-1.5,8.1-5.2,5.8-11.4C390.38,90.23,386.22,72.43,384.66,65.28Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-345 8) ",},
  {
    id: "47",
    code: "st26",
    path: "M361.74,113.3l-3.17,2.4a8.47,8.47,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.12,10.12,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.65-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.31,18.31,0,0,0-16.14.31A2.33,2.33,0,0,0,361.74,113.3Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-345 8)",
  },
   //Denture 8
   {
    id: "48",
    code: "st19",
    path: "M360.05,62.81c-1.7,5.1-6.64,18.69-6.64,28.19a10.17,10.17,0,0,0,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6a8,8,0,0,0,3.4-.8c2.9-1.5,8.1-5.2,5.8-11.4-.2-.7-4.5-19.1-6-26",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-348 8)",

  },
  {
    id: "49",
    code: "st19",
    path: "M365,113.3l-3.17,2.4a8.46,8.46,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.32a10.48,10.48,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.37,16.37,0,0,0,1.67-2.67c2.69-5.39,10.65-23.53-2.37-26a9.86,9.86,0,0,1-2.11-.7,18.3,18.3,0,0,0-16.14.31A2.45,2.45,0,0,0,365,113.3Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-348 8)",

  },
  {
    id: "50",
    code: "st50",
    polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(4 16)",
  },
   //Bridge 8
   {
    id: "51",
    code: "st0",
    rect: {
      x: "0.5",
      y: "66.57",
      width: "62.4318",
      height: "2.8"
    },
    style: { cursor: "pointer" },position: "",
     transforms:"translate(0 20)",
  },
  {
    id: "52",
    code: "st0",
    path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(0.5 0)",
  },
     //Implant 8
     {
      id: "54",
      code: "st10",
      path: "M10,72.54c-1.7,5.1-5.2,16.9-5.2,26.4a10.16,10.16,0,0,0,3.5,7.7c2.5,2.2,6.4,4.7,10.7,4.3h16.6a8.08,8.08,0,0,0,3.4-.8c2.9-1.5,8.1-5.2,5.8-11.4-.2-.7-4.5-19.1-6-26a4.05,4.05,0,0,0-3.4-3.1c-4.9-.7-15.2-2-21.2-.8A5.16,5.16,0,0,0,10,72.54Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.28 -2.18)"
    },
    {
      id: "55",
      code: "st9",
      path: "M10.83,63.22V74.34s5.44,8.45,13.3,9.88c0,0,11.54-3.87,14.35-10.5a42,42,0,0,0,0-4.49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.28 -2.18)"
    },
    {
      id: "56",
      code: "st9",
      polygon: "12.27 20.77 28.5 24.34 29.63 33.8 11.12 29.1 12.27 20.77",
      style: { cursor: "pointer" },position: "",
    },
    {
      id: "57",
      code: "st9",
      polygon: "10.6 34.54 30.55 39.24 31.88 48.77 9.45 42.64 10.6 34.54",
      style: { cursor: "pointer" },position: "",
    },
    {
      id: "58",
      code: "st9",
      polygon: "8.67 48.05 32.79 53.94 34.2 62.81 7.46 56.44 8.67 48.05",
      style: { cursor: "pointer" },position: "",
    },
    {
      id: "59",
      code: "st9",
      path: "M17.88,16.26l13.78,4.1c-1-6.2-1.68-10.58-1.78-10.77C26.82.1,23.41,1.57,21.28,4a10.29,10.29,0,0,0-2.4,5.35l-1,6.91",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.28 -2.18)"
    },
    {
      id: "60",
      code: "st10",
      path: "M33.83,121.9s-10.16-4.06-15.93.08a5.32,5.32,0,0,1-1.82.81c-3.66.84-14.74,5.32-.25,27.51l5.06,7.46a5.32,5.32,0,0,0,4.79,2.32h0a5.37,5.37,0,0,0,3.75-2L34,152.49c.11-.14.21-.28.31-.43C36,149.44,50.66,126.25,33.83,121.9Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.28 -2.18)"
    },
     //Bone 8
     {

      id: "61",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -80) ",
    },
    {

      id: "62",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -72) ",
    },
    {

      id: "63",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -64) ",
    },
    {

      id: "64",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -56) ",
    },
    {

      id: "65",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -48) ",
    },
    {

      id: "66",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -40) ",
    },
    {
      id: "67",
      code: "st67",
      path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
      style: { cursor: "pointer" },position: "",
       transforms:"scale(0.48),translate(100 238),rotate(-180)"
    },
     //Resection 8
     {
      id: "68",
      code: "st22",
      rect: {
        x: "25.54",
        y: "16.61",
        width: "3.93",
        height: "45.43"
      },
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-23 60) rotate(-65.8)",
    },
    //TeethCrown 8
    {
      id: "69",
      code: "st67",
      path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
      style: { cursor: "pointer" },position: "",
       transforms:"scale(0.6),translate(92 190),rotate(-180)"
    },
      ],
    },
     // Tooth 61: 'Upper Left Primary Central Incisor',
    {
      svg_id: "9",
      tooth_number: 61,
      tooth_name: TOOTH_NUMBERS[61],
      tooth_type: "Central Incisor",
      quadrant: "upper_left",
      tooth_position: 1,
      is_permanent: true,
      width:"57.425px",
      position:"0 0 48.7 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M39.8,70.2c0,0,1.4-3-24.1-0.9c-1.9,0.2-3.4,1.6-3.6,3.5c-1.2,9.4-2.9,18.8-5.1,28c-0.8,3.2,0.5,6.5,3.2,8.3  c1,0.7,2.1,1.2,3.3,1.7c0.8,0.3,1.7,0.5,2.5,0.6c3.5,0.3,14,1,21.7-1c1.3-0.3,2.6-1,3.6-1.9l1.1-0.9c2.5-2,3.9-5.1,3.9-8.2v-7.2  c0-1.5-0.1-3.1-0.4-4.6C45.2,83.1,43.4,74.3,39.8,70.2z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M40.6,71.3c0.5,0.4-3.6-26.2-5.2-36.9c-0.5-2.9-0.8-5.9-1-8.9l-0.7-9.9c-0.1-0.8-0.2-1.6-0.5-2.4  c-0.5-1.8-2.1-3.2-4-3.4c-3.9-0.2-8.9,11.6-8.9,11.6s-5.4,13.7-5.8,24.9c0,1-0.1,1.9-0.1,2.9L12.6,71C12.6,71,33.1,65.4,40.6,71.3z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M25.9,64.7c3.1,0,5.7-2.5,5.7-5.7c0-0.2,0-0.4,0-0.6c-0.6-5.8-1.5-13.7-1.5-14S29.3,30,29,25  c-0.1-1.1-0.4-2.2-1-3.1l0,0c-0.2-0.3-0.6-0.4-0.9-0.2c-0.2,0.1-0.3,0.3-0.3,0.4l-1.9,8.1l-1.1,6.9c-0.9,5.6-1.5,11.2-1.6,16.8  L22,60.7C21.9,62.9,23.6,64.7,25.9,64.7C25.9,64.7,25.9,64.8,25.9,64.7L25.9,64.7z",
        },
        {
          id: "4",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M35.1,121.9c0,0-9.3-4.2-16.7,0.7c0,0.4,8.4,18.4,8.4,18.4L35.1,121.9z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M33.5,125.2c0,0-7.6-3.4-13.6,0.5c0,0.3,6.8,14.9,6.8,14.9L33.5,125.2z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M36.8,148.2c3.1-5.4,11.9-22.1-0.9-25.2c-0.6-0.2-1.3,0.2-1.6,0.7l-8.5,17l8.7,7.9c0.6,0.6,1.6,0.5,2.2-0.1  C36.7,148.4,36.7,148.3,36.8,148.2z",
        },

        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M25.8,140.2l-9.1,10c-0.4,0.4-0.5,1.1-0.4,1.6c1.2,3.8,4.1,6.9,7.8,8.4c1.1,0.5,2.4,0.4,3.4-0.2  c2.5-1.3,7-4.4,8.2-9.2c0.2-0.8-0.1-1.6-0.7-2.2L25.8,140.2z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M26,140.9c0.1-0.2-6-13.8-7.6-17.7c-0.3-0.7-1-1.1-1.7-1c-1.6,0.1-4.3,0.8-6.4,4.3c-0.4,0.6-0.6,1.3-0.7,2  c-0.4,3.4-0.8,14.4,6.4,22.3L26,140.9z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M39.8,70.2c0,0,1.4-3-24.1-0.9c-1.9,0.2-3.4,1.6-3.6,3.5c-1.2,9.4-2.9,18.8-5.1,28c-0.8,3.2,0.5,6.5,3.2,8.3  c1,0.7,2.1,1.2,3.3,1.7c0.8,0.3,1.7,0.5,2.5,0.6c3.5,0.3,14,1,21.7-1c1.3-0.3,2.6-1,3.6-1.9l1.1-0.9c2.5-2,3.9-5.1,3.9-8.2v-7.2  c0-1.5-0.1-3.1-0.4-4.6C45.2,83.1,43.4,74.3,39.8,70.2z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(7 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M409,84.91h4.21a10.09,10.09,0,0,1,3.53.63l4.84,1.82a7.5,7.5,0,0,0,2.43.47l3.25.1a12.49,12.49,0,0,0,5-.86h0a9,9,0,0,1,3.35-.66l9.3.09c1,0,1,2.36.88,3.13L445.32,93c-.27.42-1.13,3.52-1.52,3.85a17.94,17.94,0,0,1-4,3.85,25.22,25.22,0,0,1-5.78,2.15,15.36,15.36,0,0,1-5.92.48c-.24,0-2.38.11-2.38.11-.59,0-3.05.13-5.14,0-2.47-.15-6.1-.19-6.71-.59-.37-.24-3.19-1.44-4.33-2.22a6.42,6.42,0,0,1-2.26-2.89,9.71,9.71,0,0,1-.74-3.85A13.63,13.63,0,0,1,407.2,90s.61-2.56.82-4C408.11,85.41,408.12,84.91,409,84.91Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-399 8)",

        },
         // Onlay
        {
          id: "38",
          code: "st19",
          path: "M415.81,114l-3.16,2.4a8.42,8.42,0,0,0-3.33,6.36c-.17,3.84.49,10,4.58,17.32a9.67,9.67,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.72,16.72,0,0,0,1.67-2.66c2.69-5.4,10.65-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.32,18.32,0,0,0-16.15.31A2.26,2.26,0,0,0,415.81,114Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-399 8)",

        },
        {
          id: "18",
          code: "st11",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
          path: "M39.8,70.2c0,0,1.4-3-24.1-0.9c-1.9,0.2-3.4,1.6-3.6,3.5c-1.2,9.4-2.9,18.8-5.1,28c-0.8,3.2,0.5,6.5,3.2,8.3  c1,0.7,2.1,1.2,3.3,1.7c0.8,0.3,1.7,0.5,2.5,0.6c3.5,0.3,14,1,21.7-1c1.3-0.3,2.6-1,3.6-1.9l1.1-0.9c2.5-2,3.9-5.1,3.9-8.2v-7.2  c0-1.5-0.1-3.1-0.4-4.6C45.2,83.1,43.4,74.3,39.8,70.2z",
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 -2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M39.8,70.2c0,0,1.4-3-24.1-0.9c-1.9,0.2-3.4,1.6-3.6,3.5c-1.2,9.4-2.9,18.8-5.1,28c-0.8,3.2,0.5,6.5,3.2,8.3  c1,0.7,2.1,1.2,3.3,1.7c0.8,0.3,1.7,0.5,2.5,0.6c3.5,0.3,14,1,21.7-1c1.3-0.3,2.6-1,3.6-1.9l1.1-0.9c2.5-2,3.9-5.1,3.9-8.2v-7.2  c0-1.5-0.1-3.1-0.4-4.6C45.2,83.1,43.4,74.3,39.8,70.2z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(7 22)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-0 8)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-0 8)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-0 8)",style: { cursor: "pointer" },position: "",},
       // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(3 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(3  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(16 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(4  8) ",
  },
   //RootTemporary
   {
    id: "28",
    code: "st0",
    path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-12 48) ",
  },
  {
    id: "29",
    code: "st0",
    path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-12 48) ",
  },
   //RootCalcium
   {
    id: "30",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
    transforms:"translate(0 8) ",
  },
  {
    id: "31",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
    transforms:"translate(0 8) ",
  },
  {
    id: "32",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
    transforms:"translate(0 8) ",
  },
  {
    id: "33",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
    transforms:"translate(0 8) ",
  },
  //RootGuttaPerchaMode
  {
    id: "34",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M471.32,20.16c0-.38.22-.67.41-.67s.39.34.38.76v0c-.13,3.76-.21,7.55-.19,11.28s.21,7.44.52,11.09a173.7,173.7,0,0,0,3.17,21.2,197,197,0,0,0,5.18,19.49A179.69,179.69,0,0,0,487.42,101a2.39,2.39,0,0,1-.1,1.91c-.3.51-.74.48-1-.07a190.14,190.14,0,0,1-12-37.89,181.41,181.41,0,0,1-3.15-21.76c-.31-3.78-.47-7.61-.48-11.47s.21-7.71.57-11.55Z",
    transforms:"translate(-451 -4) ",
  },
  {
    id: "35",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M486.88,101.63h0c.77-1.19,1.84-1,2.4.42l3.4,8.62a5.87,5.87,0,0,1-.38,4.71h0c-.77,1.18-1.85,1-2.4-.42l-3.4-8.62A5.79,5.79,0,0,1,486.88,101.63Z",
    transforms:"translate(-451 -4) ",
  },
   //PostCare
   {
    id: "36",
    code: "st17",
    path: "M420.3,72.41a42.24,42.24,0,0,0,.71,4.76l1.32,6.27c.2,1,.66,1.59,1.18,1.59h6.43c.36,0,.69-.37.9-1a54.66,54.66,0,0,0,2.5-16.43L428.17,28.1A26,26,0,0,0,426.24,22c-.78-.73-1.7-.76-1.92-.17C422,28.16,418.73,54.13,420.3,72.41Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-400 0)",
  },
   //Veneer
   {
    id: "37",
    code: "st19",
    path: "M411.55,64.81a265.92,265.92,0,0,1-5.1,28,7.7,7.7,0,0,0,3.2,8.3,17.45,17.45,0,0,0,3.3,1.7,11.39,11.39,0,0,0,2.5.6c3.5.3,14,1,21.7-1a8.41,8.41,0,0,0,3.6-1.9l1.1-.9a10.57,10.57,0,0,0,3.9-8.2v-7.2a24.51,24.51,0,0,0-.4-4.6c-.66-4.24-2.29-12.28-5.49-16.63",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-399 8)",
  },
   //CrownPermanent 9
   {
    id: "40",
    code: "st19",
    path: "M408.29,64.81a265.92,265.92,0,0,1-5.1,28,7.7,7.7,0,0,0,3.2,8.3,17.75,17.75,0,0,0,3.3,1.7,12,12,0,0,0,2.5.6c3.5.3,14,1,21.7-1a8.41,8.41,0,0,0,3.6-1.9l1.1-.9a10.57,10.57,0,0,0,3.9-8.2v-7.2a24.51,24.51,0,0,0-.4-4.6c-.66-4.24-2.29-12.28-5.49-16.63",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-396 8)",

  },
  {
    id: "41",
    code: "st19",
    path: "M412.55,114l-3.16,2.4a8.45,8.45,0,0,0-3.33,6.36c-.17,3.84.49,10,4.58,17.32a10.12,10.12,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.28,16.28,0,0,0,1.67-2.66c2.69-5.4,10.65-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.33,18.33,0,0,0-16.15.31A2,2,0,0,0,412.55,114Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-396 8)",
  },
  //CrownGold 9
  {
    id: "43",
    code: "st43",
    path: "M442.3,78.07c-7.26,7.47-19.64,18.11-34.78,24.37l-1.11-.9c15.42-6.39,28.25-17.55,35.7-25.17",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-396 8) ",
  },
  {
    id: "44",
    code: "st43",
    path: "M442.89,87.42c-6,6.77-14.72,14.59-24.18,16.73l-3.19-.19c10.23-2.09,21.16-10.77,27.28-17.84",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-396 8) ",
  },
    //CrownZirconia 9
    {
      id: "45",   code: "st26", path :"M437,63.43h0a3.46,3.46,0,0,0-2-1.58c0-.71-.08-2.49-1.08-2.82a38.5,38.5,0,0,0-6.74-1.5V29.64a1,1,0,0,0-.06-.34l-1.91-5.46c-.43-1.73-1.33-2.81-2.34-2.81s-1.92,1.08-2.35,2.81l-1.91,5.46a1,1,0,0,0-.05.34V57.42c-3.76.33-6.67,1.09-7.39,2a3.43,3.43,0,0,0-.41,2.27,3.46,3.46,0,0,0-2,1.84m11-33.74,1.87-5.34s0-.06,0-.09c.26-1.07.76-1.77,1.26-1.77s1,.7,1.25,1.77l0,.09,1.73,5L419.74,32Zm0,3.87L426,31.05v1.63l-6.29,2.64Zm0,3.29L426,34.34V36l-6.29,2.65Zm0,3.3L426,37.63v1.64l-6.29,2.64Zm0,3.29L426,40.93v1.63l-6.29,2.65Zm0,3.3L426,44.22v1.64l-6.29,2.64Zm0,3.29L426,47.52v1.63l-6.29,2.64Zm0,3.29L426,50.81v1.63l-6.29,2.65Zm0,3.3L426,54.1v3.3c-1-.09-2-.15-3.09-.16h-.22c-1,0-2,0-3,.09ZM412,60.6c.41-.54,4.13-1.8,10.95-1.77a36.34,36.34,0,0,1,10.72,1.74,2.09,2.09,0,0,1,.19.85,45.67,45.67,0,0,0-11.3-1.52c-.09,0-6.57,0-10.64,1.37A1.47,1.47,0,0,1,412,60.6Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-396 8) ",},
     {
      id: "46",   code: "st26", path :"M442.09,79.62c-.65-4.1-2.2-11.76-5.19-16.19H408.74a3.85,3.85,0,0,0-.45,1.39,265.92,265.92,0,0,1-5.1,28,7.7,7.7,0,0,0,3.2,8.3,17.75,17.75,0,0,0,3.3,1.7,12,12,0,0,0,2.5.6c3.5.3,14,1,21.7-1a8.41,8.41,0,0,0,3.6-1.9l1.1-.9a10.57,10.57,0,0,0,3.9-8.2v-7.2A24.51,24.51,0,0,0,442.09,79.62Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-396 8) ",},
    {
      id: "47",
      code: "st26",
      path: "M412.55,114l-3.16,2.4a8.45,8.45,0,0,0-3.33,6.36c-.17,3.84.49,10,4.58,17.32a10.12,10.12,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.28,16.28,0,0,0,1.67-2.66c2.69-5.4,10.65-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.33,18.33,0,0,0-16.15.31A2,2,0,0,0,412.55,114Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-396 8)",
    },
    //Denture 9

    {
      id: "48",
      code: "st19",
      path: "M411.55,64.81a265.92,265.92,0,0,1-5.1,28,7.7,7.7,0,0,0,3.2,8.3,17.45,17.45,0,0,0,3.3,1.7,11.39,11.39,0,0,0,2.5.6c3.5.3,14,1,21.7-1a8.41,8.41,0,0,0,3.6-1.9l1.1-.9a10.57,10.57,0,0,0,3.9-8.2v-7.2a24.51,24.51,0,0,0-.4-4.6c-.66-4.24-2.29-12.28-5.49-16.63",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-399 8)",

    },
    {
      id: "49",
      code: "st19",
      path: "M415.81,114l-3.16,2.4a8.42,8.42,0,0,0-3.33,6.36c-.17,3.84.49,10,4.58,17.32a9.67,9.67,0,0,0,1.35,1.84c1.29,1.41,4.18,4.86,6.16,7.25a6.92,6.92,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.61-1.29l5.8-7.59a16.72,16.72,0,0,0,1.67-2.66c2.69-5.4,10.65-23.53-2.37-26a9.68,9.68,0,0,1-2.11-.7,18.32,18.32,0,0,0-16.15.31A2.26,2.26,0,0,0,415.81,114Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-399 8)",

    },
    {
      id: "50",
      code: "st50",
      polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(6 16)",
    },
    //Bridge 9
    {
      id: "51",
      code: "st0",
      rect: {
        x: "0.5",
        y: "66.57",
        width: "62.4318",
        height: "2.8"
      },
      style: { cursor: "pointer" },position: "",
       transforms:"translate(0 20)",
    },
    {
      id: "52",
      code: "st0",
      path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(3 0)",
    },
    //Implant 9
    {
      id: "54",
      code: "st9",
      path: "M18.68,19.69l14.11-3.45-1-6.57a12.25,12.25,0,0,0-2.3-5.56c-1.72-2.26-4.39-3.91-7.34,1a17.6,17.6,0,0,0-2.23,6.39Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.97 -1.92)"
    },

    {
      id: "55",
      code: "st9",
      polygon: "12.77 24 28.6 19.84 29.81 28.47 11.39 33.1 12.77 24",
      style: { cursor: "pointer" },position: "",
    },
    {
      id: "56",
      code: "st9",
      polygon: "10.63 38.16 30.5 33.4 31.62 41.5 9.34 46.65 10.63 38.16",
      style: { cursor: "pointer" },position: "",
    },
    {
      id: "57",
      code: "st9",
      polygon: "8.52 52.09 32.36 46.88 33.43 54.69 7.18 60.93 8.52 52.09",
      style: { cursor: "pointer" },position: "",
    },
    {
      id: "58",
      code: "st9",
      path: "M11.22,69.58l28.65-7L39.54,72A5.36,5.36,0,0,1,39,74.22a16.48,16.48,0,0,1-6.55,6.7,12.48,12.48,0,0,1-11.14.51,24.75,24.75,0,0,1-8.11-5.64,7.12,7.12,0,0,1-1.89-4.64C11.26,70.32,11.22,69.58,11.22,69.58Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.97 -1.92)"
    },
    {
      id: "59",
      code: "st10",
      path: "M38.5,70.4s1.4-3-24.1-.9A4,4,0,0,0,10.8,73a265,265,0,0,1-5.1,28,7.7,7.7,0,0,0,3.2,8.3,17.1,17.1,0,0,0,3.3,1.7,11.36,11.36,0,0,0,2.5.6c3.5.3,14,1,21.7-1a8.36,8.36,0,0,0,3.6-1.9l1.1-.9A10.58,10.58,0,0,0,45,99.6V92.4a23.83,23.83,0,0,0-.4-4.6C43.9,83.3,42.1,74.5,38.5,70.4Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.97 -1.92)"
    },
    {
      id: "60",
      code: "st10",
      path: "M33.13,122.64c-1.42-1.11-6.93-4.67-15-.67a3.68,3.68,0,0,1-1.9.37c-1.54-.12-4.78.39-7.31,5.86a26.3,26.3,0,0,0,6.35,22.6s1.22,7.09,7.72,9.26a5.4,5.4,0,0,0,4.52-.53c2.59-1.59,7.06-4.8,7.33-9a3.41,3.41,0,0,1,.93-2l.3-.33S50.16,126.18,34,123A2.07,2.07,0,0,1,33.13,122.64Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-4.97 -1.92)"
    },
     //Bone 9
     {

      id: "61",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -80) ",
    },
    {

      id: "62",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -72) ",
    },
    {

      id: "63",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -64) ",
    },
    {

      id: "64",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -56) ",
    },
    {

      id: "65",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -48) ",
    },
    {

      id: "66",  code: "st21",
      rect: {
        x: "2",
        y: "99.6",
        width: "48",
        height: "1.5"
      },

      style: { cursor: "pointer" },position: "",
     transforms:"translate(0 -40) ",
    },
    {
      id: "67",
      code: "st67",
      path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
      style: { cursor: "pointer" },position: "",
       transforms:"scale(0.48),translate(100 238),rotate(-180)"
    },
      //Resection 9
      {
        id: "68",
        code: "st22",
        rect: {
          x: "25.54",
          y: "16.61",
          width: "3.93",
          height: "45.43"
        },
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-23 60) rotate(-65.8)",
      },
      //TeethCrown 9
      {
        id: "69",
        code: "st67",
        path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
        style: { cursor: "pointer" },position: "",
         transforms:"scale(0.6),translate(96 190),rotate(-180)"
      },
      ],
    },
    //62: 'Upper Left Primary Lateral Incisor',
    {
      svg_id: "10",
      tooth_number: 62,
      tooth_name: TOOTH_NUMBERS[62],
      tooth_type: "Lateral Incisor",
      quadrant: "upper_left",
      tooth_position: 2,
      is_permanent: true,
      width:"49.975px",
      position:"0 0 42.3 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M31.8,68.9c-3-0.4-8.6-0.7-19.2,0.6c-1.4,0.2-2.4,1.4-2.3,2.7c0.2,4.4,0.3,14.3-1.9,23.3  c-0.6,2.3-0.9,4.7-0.9,7.2v1c0,1.4,0.7,2.6,1.9,3.3l4.2,2.3c2.9,1.6,6.2,2.2,9.4,1.6c4.2-0.7,8.2-2.1,11.9-4.1  c4-2.4,6.4-6.8,6.1-11.5l-0.2-5.1c-0.1-2-0.5-4-1.1-5.9l-4-12.1C35,70.5,33.6,69.2,31.8,68.9z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M35.6,72.2l-5.1-51.9c-0.1-0.9-0.3-1.8-0.6-2.7l-1.2-4c-0.5-1.7-2.1-2.8-3.9-2.8h-0.4c-0.6,0-1.3-0.1-1.9-0.2  c-1-0.2-3.1-0.1-4.8,2.7c-0.7,1.2-1.2,2.6-1.3,4l-6,54.1C10.3,71.3,27.9,65.1,35.6,72.2z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M26.8,62.4c0.2-0.7,0.2-1.4,0.3-2.1c0.2-6-0.8-29.8-0.8-29.8l-0.6-12.1c-0.1-0.9-0.8-1.6-1.7-1.5  c-0.3,0-0.7,0.2-0.9,0.4c-0.9,0.8-1.5,1.9-1.9,3c-0.5,1.3-0.8,2.7-0.8,4.1l-1.3,32.3c-0.1,3.2,2.3,5.4,5.7,5.9c0,0,2,0.4,2.1,0  C26.8,62.4,26.8,62.4,26.8,62.4z",
        },
        {
          id: "4",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M23.1,139.1l7.4-15.6c0.4-0.8,0-1.9-0.8-2.3c-0.1,0-0.1-0.1-0.2-0.1c-3-1-9.1-2.3-14.8,0.7	c-0.7,0.3-0.9,1.1-0.6,1.8l7.5,15.5c0.2,0.4,0.7,0.6,1.1,0.4C22.9,139.4,23,139.2,23.1,139.1z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23,139.5l6.2-13.5c0.3-0.7,0-1.6-0.7-1.9c-0.1,0-0.1,0-0.2-0.1c-2.5-0.9-7.6-2-12.4,0.6	c-0.6,0.3-0.8,1-0.5,1.6l6.3,13.3c0.2,0.4,0.6,0.5,0.9,0.3C22.8,139.7,22.9,139.6,23,139.5z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.8,139.5l7.3-16.4c0.3-0.7,1.1-1.1,1.8-1c2.9,0.3,5.4,2.1,6.5,4.8c2.1,5-0.2,16.8-4.6,22.8	c-0.5,0.6-1.3,0.7-2,0.3l-0.1-0.1l-8.5-8.3C22.6,141.1,22.5,140.2,22.8,139.5z",
        },
         {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M21.3,138.7L14.4,123c-0.2-0.6-0.9-0.8-1.5-0.6c-0.1,0-0.2,0.1-0.2,0.1l-4.4,3.4c-0.9,0.7-1.4,1.8-1.4,2.9v3.6	c0,2.2,0.4,4.3,1.2,6.4l3.8,9.9c0.3,0.8,1.1,1.1,1.9,0.9c0.2-0.1,0.4-0.2,0.5-0.4l6.3-6.6C21.6,141.7,21.9,140.1,21.3,138.7z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M13.6,149l8-7.5c0.6-0.6,1.5-0.5,2.1,0.1l7.7,8c0.7,0.8,0.8,1.9,0.2,2.8l-3.8,5.6c-0.8,1.2-2.1,1.9-3.5,1.9	h-2.4c-0.7,0-1.3-0.3-1.8-0.8l-6.6-7.4C12.8,150.9,12.8,149.7,13.6,149z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M31.8,68.9c-3-0.4-8.6-0.7-19.2,0.6c-1.4,0.2-2.4,1.4-2.3,2.7c0.2,4.4,0.3,14.3-1.9,23.3  c-0.6,2.3-0.9,4.7-0.9,7.2v1c0,1.4,0.7,2.6,1.9,3.3l4.2,2.3c2.9,1.6,6.2,2.2,9.4,1.6c4.2-0.7,8.2-2.1,11.9-4.1  c4-2.4,6.4-6.8,6.1-11.5l-0.2-5.1c-0.1-2-0.5-4-1.1-5.9l-4-12.1C35,70.5,33.6,69.2,31.8,68.9z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M460,84.13h3.56a7.62,7.62,0,0,1,3,.6l4.09,1.73a5.73,5.73,0,0,0,2.07.45l2.74.1a9.35,9.35,0,0,0,4.22-.83h0a7,7,0,0,1,2.83-.62l7.86.08c.83,0,.48,2.29.37,3L490,91.89c-.23.4-1.85,3.54-2.17,3.86a16.32,16.32,0,0,1-2.93,2.51,22.06,22.06,0,0,1-4.85,2.16,30.87,30.87,0,0,1-3.87,1.26c-.2,0-3.27.57-3.27.57a16.53,16.53,0,0,1-3.19.25,15.43,15.43,0,0,1-5.39-1.56,32.83,32.83,0,0,0-3.75-2.08,4.71,4.71,0,0,1-1.5-1.13,3.89,3.89,0,0,1-1-2.87c0-.52.06-1.21.05-2.11a15.11,15.11,0,0,1,.37-3.74c0-.05.51-2.44.69-3.84C459.26,84.61,459.26,84.13,460,84.13Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-450 8)",

        },
         // Onlay
        {
          id: "38",
          code: "st19",
          path: "M465,113.76l-3.17,2.4a8.47,8.47,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.31a9.79,9.79,0,0,0,1.35,1.84c1.3,1.42,4.18,4.87,6.16,7.26a7,7,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.62-1.3l5.79-7.58a16.8,16.8,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.83,9.83,0,0,1-2.11-.69,18.3,18.3,0,0,0-16.14.31A3,3,0,0,0,465,113.76Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-450 8)",

        },
        {
          id: "18",
          code: "st11",
          path: "M480.06,71c-2.77-.37-7.93-.65-17.69.55a2.41,2.41,0,0,0-2.12,2.5,82.32,82.32,0,0,1-1.75,21.5,26,26,0,0,0-.83,6.65v.92a3.46,3.46,0,0,0,1.75,3l3.87,2.12A13,13,0,0,0,472,109.8a35.2,35.2,0,0,0,11-3.79,11.55,11.55,0,0,0,5.62-10.61l-.19-4.71a21.94,21.94,0,0,0-1-5.45l-3.69-11.16A4.81,4.81,0,0,0,480.06,71Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-449 0)"
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 -3)",
        },
        {
          id: "20",
          code: "st25",
          path: "M31.8,68.9c-3-0.4-8.6-0.7-19.2,0.6c-1.4,0.2-2.4,1.4-2.3,2.7c0.2,4.4,0.3,14.3-1.9,23.3  c-0.6,2.3-0.9,4.7-0.9,7.2v1c0,1.4,0.7,2.6,1.9,3.3l4.2,2.3c2.9,1.6,6.2,2.2,9.4,1.6c4.2-0.7,8.2-2.1,11.9-4.1  c4-2.4,6.4-6.8,6.1-11.5l-0.2-5.1c-0.1-2-0.5-4-1.1-5.9l-4-12.1C35,70.5,33.6,69.2,31.8,68.9z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-3 8)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-3 8)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-3 8)",style: { cursor: "pointer" },position: "",},
        // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(1.5 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(1.5  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(10 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(2  8) ",
  },
   //RootTemporary
   {
    id: "28",
    code: "st0",
    path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-15 48) ",
  },
  {
    id: "29",
    code: "st0",
    path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-15 48) ",
  },
   //RootCalcium
   {
    id: "30",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "31",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "32",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "33",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
    transforms:"translate(-6 6) ",
  },
    //RootGuttaPerchaMode
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M522.06,25.21c.36,3.65.54,7.33.55,11s-.19,7.33-.5,10.93a165.28,165.28,0,0,1-3.21,20.75,174.74,174.74,0,0,1-12,36.14c-.24.52-.68.55-1,.07a2.15,2.15,0,0,1-.09-1.82,168.21,168.21,0,0,0,6.66-16.84,182.08,182.08,0,0,0,5.23-18.6,160,160,0,0,0,3.22-20.21c.31-3.48.49-7,.54-10.57s0-7.17-.16-10.75h0c0-.41.15-.74.37-.73s.38.27.41.64Z",
      transforms:"translate(-494 -2) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M506.69,107.38l-3.41,8.22c-.56,1.35-1.64,1.53-2.4.4h0a5.25,5.25,0,0,1-.37-4.49l3.41-8.22c.56-1.35,1.64-1.53,2.4-.4h0A5.25,5.25,0,0,1,506.69,107.38Z",
      transforms:"translate(-494 -2) ",
    },
    //PostCare
   {
    id: "36",
    code: "st17",
    path: "M467.5,71.2c.11,1.76.3,3.47.56,5.12l1,6.74c.15,1,.52,1.7.92,1.7h5c.28,0,.54-.39.7-1.05a78.66,78.66,0,0,0,2-17.66S475,18.59,474.32,17.6c-.94-1.32-3.45-1.59-3.68-.71C468.85,23.66,466.28,51.57,467.5,71.2Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-449 0)",
  },
    //Veneer
    {
      id: "37",
      code: "st19",
      path: "M460.4,63.93c.2,4.4.3,14.3-1.9,23.3a28.64,28.64,0,0,0-.9,7.2v1a3.75,3.75,0,0,0,1.9,3.3l4.2,2.3a14.06,14.06,0,0,0,9.4,1.6,38.29,38.29,0,0,0,11.9-4.1A12.5,12.5,0,0,0,491.1,87l-.2-5.1a23.2,23.2,0,0,0-1.1-5.9l-4-12.1",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-450 8)",
    },
       //CrownPermanent 10
       {
        id: "40",
        code: "st19",
        path: "M457.14,63.93c.2,4.4.3,14.3-1.9,23.3a28.64,28.64,0,0,0-.9,7.2v1a3.75,3.75,0,0,0,1.9,3.3l4.2,2.3a14.06,14.06,0,0,0,9.4,1.6,38.29,38.29,0,0,0,11.9-4.1A12.5,12.5,0,0,0,487.84,87l-.2-5.1a23.2,23.2,0,0,0-1.1-5.9l-4-12.1",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-447 8)",

      },
      {
        id: "41",
        code: "st19",
        path: "M461.74,113.76l-3.17,2.4a8.47,8.47,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.31a10.12,10.12,0,0,0,1.35,1.84c1.3,1.42,4.18,4.87,6.16,7.26a7,7,0,0,0,5.33,2.5h0a3.28,3.28,0,0,0,2.62-1.3l5.79-7.58a16.8,16.8,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.83,9.83,0,0,1-2.11-.69,18.31,18.31,0,0,0-16.14.31A3.11,3.11,0,0,0,461.74,113.76Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-447 8)",
      },
       //CrownGold 10
       {
        id: "43",
        code: "st43",
        path: "M487.56,76.26C481.33,83.15,470,93.78,457,99.56L455.6,99C468.82,93.1,480.57,82.06,487,75",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-447 8) ",
      },
      {
        id: "44",
        code: "st43",
        path: "M488.63,86.07c-5.77,6.67-14.75,15-23.79,17.07l-1.77-.33c9.78-2.06,19.71-11.25,25.56-18.22",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-447 8) ",
      },
       //CrownZirconia 10
       {
        id: "45",   code: "st26", path :"M482.72,64h0A3.2,3.2,0,0,0,481,62.41c0-.72-.08-2.5-1-2.82a29.84,29.84,0,0,0-6-1.5V30.2a1.21,1.21,0,0,0-.05-.34l-1.71-5.46c-.38-1.74-1.18-2.81-2.09-2.81s-1.7,1.07-2.09,2.81l-1.7,5.46a.93.93,0,0,0,0,.34V58c-3.36.33-6,1.09-6.6,2a3.83,3.83,0,0,0-.36,2.28,3.25,3.25,0,0,0-1.79,1.83m9.78-33.73L469,25a.29.29,0,0,0,0-.09c.24-1.08.67-1.78,1.12-1.78s.89.7,1.12,1.78c0,0,0,.06,0,.09l1.55,5-5.5,2.58Zm0,3.86,5.62-2.64v1.63l-5.62,2.65Zm0,3.3,5.62-2.65v1.64l-5.62,2.64Zm0,3.29,5.62-2.64v1.63l-5.62,2.65Zm0,3.3,5.62-2.65v1.64l-5.62,2.64Zm0,3.29,5.62-2.64v1.63l-5.62,2.64Zm0,3.29,5.62-2.64V49.7l-5.62,2.65Zm0,3.3,5.62-2.65V53l-5.62,2.64Zm0,3.29,5.62-2.64V58a24.4,24.4,0,0,0-2.76-.16H470c-.91,0-1.8,0-2.66.09Zm-6.91,3.85c.36-.53,3.69-1.79,9.77-1.77a29.55,29.55,0,0,1,9.56,1.74,2.2,2.2,0,0,1,.17.85,36.91,36.91,0,0,0-10.08-1.52c-.08,0-5.86,0-9.49,1.38A1.75,1.75,0,0,1,460.41,61.15Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-447 8) ",},
       {
        id: "46",   code: "st26", path :"M488,82.22a24.19,24.19,0,0,0-1.1-5.9l-4-12.1h-25.4c.2,4.4.3,14.3-1.9,23.3a28.19,28.19,0,0,0-.9,7.2v1a3.73,3.73,0,0,0,1.9,3.3l4.2,2.3a14.06,14.06,0,0,0,9.4,1.6,38,38,0,0,0,11.9-4.1,12.5,12.5,0,0,0,6.1-11.5Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-447 8) ",},
       {
        id: "47",
        code: "st26",
        path: "M461.74,113.76l-3.17,2.4a8.47,8.47,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.31a10.12,10.12,0,0,0,1.35,1.84c1.3,1.42,4.18,4.87,6.16,7.26a7,7,0,0,0,5.33,2.5h0a3.28,3.28,0,0,0,2.62-1.3l5.79-7.58a16.8,16.8,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.83,9.83,0,0,1-2.11-.69,18.31,18.31,0,0,0-16.14.31A3.11,3.11,0,0,0,461.74,113.76Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-447 8)",
      },
        //Denture 10
        {
          id: "48",
          code: "st19",
          path: "M460.4,63.93c.2,4.4.3,14.3-1.9,23.3a28.64,28.64,0,0,0-.9,7.2v1a3.75,3.75,0,0,0,1.9,3.3l4.2,2.3a14.06,14.06,0,0,0,9.4,1.6,38.29,38.29,0,0,0,11.9-4.1A12.5,12.5,0,0,0,491.1,87l-.2-5.1a23.2,23.2,0,0,0-1.1-5.9l-4-12.1",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-450 8)",

        },
        {
          id: "49",
          code: "st19",
          path: "M465,113.76l-3.17,2.4a8.47,8.47,0,0,0-3.33,6.37c-.16,3.84.49,10,4.58,17.31a9.79,9.79,0,0,0,1.35,1.84c1.3,1.42,4.18,4.87,6.16,7.26a7,7,0,0,0,5.33,2.5h0a3.3,3.3,0,0,0,2.62-1.3l5.79-7.58a16.8,16.8,0,0,0,1.67-2.67c2.69-5.39,10.66-23.53-2.37-26a9.83,9.83,0,0,1-2.11-.69,18.3,18.3,0,0,0-16.14.31A3,3,0,0,0,465,113.76Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-450 8)",

        },
        {
          id: "50",
          code: "st50",
          polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 16)",
        },
         //Bridge 10
         {
          id: "51",
          code: "st0",
          rect: {
            x: "0.5",
            y: "66.57",
            width: "62.4318",
            height: "2.8"
          },
          style: { cursor: "pointer" },position: "",
           transforms:"translate(0 20)",
        },
        {
          id: "52",
          code: "st0",
          path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 0)",
        },
          //Implant 10
          {
            id: "54",
            code: "st9",
            path: "M18.68,19.69l14.11-3.45-1-6.57a12.25,12.25,0,0,0-2.3-5.56c-1.72-2.26-4.39-3.91-7.34,1a17.6,17.6,0,0,0-2.23,6.39Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-4.97 -1.92)"
          },

          {
            id: "55",
            code: "st9",
            polygon: "12.77 24 28.6 19.84 29.81 28.47 11.39 33.1 12.77 24",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "56",
            code: "st9",
            polygon: "10.63 38.16 30.5 33.4 31.62 41.5 9.34 46.65 10.63 38.16",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "57",
            code: "st9",
            polygon: "8.52 52.09 32.36 46.88 33.43 54.69 7.18 60.93 8.52 52.09",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "58",
            code: "st9",
            path: "M11.22,69.58l28.65-7L39.54,72A5.36,5.36,0,0,1,39,74.22a16.48,16.48,0,0,1-6.55,6.7,12.48,12.48,0,0,1-11.14.51,24.75,24.75,0,0,1-8.11-5.64,7.12,7.12,0,0,1-1.89-4.64C11.26,70.32,11.22,69.58,11.22,69.58Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-4.97 -1.92)"
          },
          {
            id: "59",
            code: "st10",
            path: "M38.5,70.4s1.4-3-24.1-.9A4,4,0,0,0,10.8,73a265,265,0,0,1-5.1,28,7.7,7.7,0,0,0,3.2,8.3,17.1,17.1,0,0,0,3.3,1.7,11.36,11.36,0,0,0,2.5.6c3.5.3,14,1,21.7-1a8.36,8.36,0,0,0,3.6-1.9l1.1-.9A10.58,10.58,0,0,0,45,99.6V92.4a23.83,23.83,0,0,0-.4-4.6C43.9,83.3,42.1,74.5,38.5,70.4Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-4.97 -1.92)"
          },
          {
            id: "60",
            code: "st10",
            path: "M33.13,122.64c-1.42-1.11-6.93-4.67-15-.67a3.68,3.68,0,0,1-1.9.37c-1.54-.12-4.78.39-7.31,5.86a26.3,26.3,0,0,0,6.35,22.6s1.22,7.09,7.72,9.26a5.4,5.4,0,0,0,4.52-.53c2.59-1.59,7.06-4.8,7.33-9a3.41,3.41,0,0,1,.93-2l.3-.33S50.16,126.18,34,123A2.07,2.07,0,0,1,33.13,122.64Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-4.97 -1.92)"
          },
           //Bone 10
           {

            id: "61",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -80) ",
          },
          {

            id: "62",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -72) ",
          },
          {

            id: "63",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -64) ",
          },
          {

            id: "64",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -56) ",
          },
          {

            id: "65",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -48) ",
          },
          {

            id: "66",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -40) ",
          },
          {
            id: "67",
            code: "st67",
            path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.46),translate(100 244),rotate(-180)"
          },
          //Resection 10
          {
            id: "68",
            code: "st22",
            rect: {
              x: "25.54",
              y: "16.61",
              width: "3.93",
              height: "41.43"
            },
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-23 60) rotate(-65.8)",
          },
          //TeethCrown 10
          {
            id: "69",
            code: "st67",
            path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.54),translate(94 210),rotate(-180)"
          },
      ],
    },
    // Tooth63: 'Upper Left Primary Canine',
    {
      svg_id: "11",
      tooth_number: 63,
      tooth_name: TOOTH_NUMBERS[63],
      tooth_type: "canine",
      quadrant: "upper_left",
      tooth_position: 3,
      is_permanent: true,
      width:"51.2625px",
      position:"0 0 43.4 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M34.1,71.5l-0.7-0.4c-1.8-1-3.8-1.5-5.8-1.4l-10.4,0.4c-2.5,0.1-4.7,1.5-5.8,3.8L5.8,85c-1,1.9-1.5,4.1-1.5,6.3  v2.2c0,2.2,0.8,4.2,2.3,5.8c3.6,4,7.6,7.6,12.1,10.6c2.1,1.3,4.6,1.5,6.8,0.5c4.8-1.9,9.2-4.8,12.8-8.5c1.8-1.9,2.8-4.4,2.7-7  c0-1-0.1-2.1-0.1-3.1c-0.1-2.9-0.9-5.8-2.2-8.4l0,0c-1.2-2.4-1.9-5-2-7.7v-0.2C36.5,73.8,35.6,72.3,34.1,71.5z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M36.4,74.5l0.5-16.6c0.3-7.5-0.3-15-1.7-22.3c-1.8-9.5-5-20.5-10.7-22.7c-2.1-0.2-4.2,0.2-6,1.4  c-1.8,1.1-2.9,3.1-2.9,5.3c0,3.2,0.4,6.3,1.1,9.4c1.5,6.9,1.9,14,1.1,21c-0.9,7.6-2.7,15.1-5.3,22.3C12.6,72.8,34.2,68.4,36.4,74.5z  ",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M28.9,66c1.1,0,2-0.9,2-1.9c0.1-5.6,0.2-16.9,0-20.3c-0.2-3.1-2.1-19.8-4.7-20.5c-0.7-0.2-1.5,0.1-2,0.7l0,0  c-0.5,0.7-0.7,1.4-0.7,2.2l0.7,15.2c0.1,2.5,0,5.1-0.4,7.6l-0.9,6.2c-0.2,1-0.3,1.7-0.6,2.7l-0.2,0.7c-0.4,1.5-0.6,3.1-0.7,4.7l0,0  c0,1.5,1.2,2.6,2.6,2.6L28.9,66z",
        },
        {
          id: "4",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M23.1,139.1l7.4-15.6c0.4-0.8,0-1.9-0.8-2.3c-0.1,0-0.1-0.1-0.2-0.1c-3-1-9.1-2.3-14.8,0.7	c-0.7,0.3-0.9,1.1-0.6,1.8l7.5,15.5c0.2,0.4,0.7,0.6,1.1,0.4C22.9,139.4,23,139.2,23.1,139.1z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23,139.5l6.2-13.5c0.3-0.7,0-1.6-0.7-1.9c-0.1,0-0.1,0-0.2-0.1c-2.5-0.9-7.6-2-12.4,0.6	c-0.6,0.3-0.8,1-0.5,1.6l6.3,13.3c0.2,0.4,0.6,0.5,0.9,0.3C22.8,139.7,22.9,139.6,23,139.5z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.8,139.5l7.3-16.4c0.3-0.7,1.1-1.1,1.8-1c2.9,0.3,5.4,2.1,6.5,4.8c2.1,5-0.2,16.8-4.6,22.8	c-0.5,0.6-1.3,0.7-2,0.3l-0.1-0.1l-8.5-8.3C22.6,141.1,22.5,140.2,22.8,139.5z",
        },
         {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M21.3,138.7L14.4,123c-0.2-0.6-0.9-0.8-1.5-0.6c-0.1,0-0.2,0.1-0.2,0.1l-4.4,3.4c-0.9,0.7-1.4,1.8-1.4,2.9v3.6	c0,2.2,0.4,4.3,1.2,6.4l3.8,9.9c0.3,0.8,1.1,1.1,1.9,0.9c0.2-0.1,0.4-0.2,0.5-0.4l6.3-6.6C21.6,141.7,21.9,140.1,21.3,138.7z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M13.6,149l8-7.5c0.6-0.6,1.5-0.5,2.1,0.1l7.7,8c0.7,0.8,0.8,1.9,0.2,2.8l-3.8,5.6c-0.8,1.2-2.1,1.9-3.5,1.9	h-2.4c-0.7,0-1.3-0.3-1.8-0.8l-6.6-7.4C12.8,150.9,12.8,149.7,13.6,149z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M34.1,71.5l-0.7-0.4c-1.8-1-3.8-1.5-5.8-1.4l-10.4,0.4c-2.5,0.1-4.7,1.5-5.8,3.8L5.8,85c-1,1.9-1.5,4.1-1.5,6.3  v2.2c0,2.2,0.8,4.2,2.3,5.8c3.6,4,7.6,7.6,12.1,10.6c2.1,1.3,4.6,1.5,6.8,0.5c4.8-1.9,9.2-4.8,12.8-8.5c1.8-1.9,2.8-4.4,2.7-7  c0-1-0.1-2.1-0.1-3.1c-0.1-2.9-0.9-5.8-2.2-8.4l0,0c-1.2-2.4-1.9-5-2-7.7v-0.2C36.5,73.8,35.6,72.3,34.1,71.5z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M501.64,81.42h4.1a9.81,9.81,0,0,1,3.44.62l4.71,1.82a7.41,7.41,0,0,0,2.38.47l3.16.1a11.89,11.89,0,0,0,4.86-.86h0a8.58,8.58,0,0,1,3.26-.65l9.06.08c1,0,.27.53.43,3.17l-.38,2.52a6.42,6.42,0,0,1-.75,2.18,17.16,17.16,0,0,1-2.15,3c-1.19,1.25-1.37,1.28-3.14,2.88a34.18,34.18,0,0,1-5.31,3.65c-2,.91-1.87,1.43-5.37,2.2-.19,0-1.5.11-2,.13a1.34,1.34,0,0,1-.4-.06c-2.16-.46-5.82-3.59-7.52-5.13-.32-.29-2.38-2.16-3.41-3.16a26.1,26.1,0,0,1-4.38-5.06,9.35,9.35,0,0,1-1.56-6.79C500.8,81.92,500.81,81.42,501.64,81.42Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-496 8)",

        },
         // Onlay
        {
          id: "38",
          code: "st19",
          path: "M507.14,113.53l-2,1.47a8.39,8.39,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.5,9.5,0,0,0,1.29,1.73c1.28,1.36,4.28,4.9,6.34,7.33a7.07,7.07,0,0,0,5.42,2.5h0a3.36,3.36,0,0,0,2.66-1.29l5.89-7.59a16.44,16.44,0,0,0,1.7-2.66c2.73-5.38,10.84-23.53-2.41-26-.74-.14-1.41-.54-2.15-.7-2.48-.53-7.64-.38-14.5.16A8,8,0,0,0,507.14,113.53Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-496 8)",

        },
        {
          id: "18",
          code: "st11",
          path: "M528.67,73.65l-.67-.36A10.72,10.72,0,0,0,522.5,72l-9.87.36a6.33,6.33,0,0,0-5.51,3.41l-5.32,9.94a11.79,11.79,0,0,0-1.42,5.65v2a7.28,7.28,0,0,0,2.18,5.2,60.59,60.59,0,0,0,11.49,9.5,7.14,7.14,0,0,0,6.45.44,35.06,35.06,0,0,0,12.15-7.61,8.39,8.39,0,0,0,2.57-6.27c0-.9-.1-1.89-.1-2.78A17.47,17.47,0,0,0,533,84.31h0a16.09,16.09,0,0,1-1.9-6.9v-.18A4.55,4.55,0,0,0,528.67,73.65Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-495.29 0)"
        },
        {
          id: "20",
          code: "st25",
          path: "M34.1,71.5l-0.7-0.4c-1.8-1-3.8-1.5-5.8-1.4l-10.4,0.4c-2.5,0.1-4.7,1.5-5.8,3.8L5.8,85c-1,1.9-1.5,4.1-1.5,6.3  v2.2c0,2.2,0.8,4.2,2.3,5.8c3.6,4,7.6,7.6,12.1,10.6c2.1,1.3,4.6,1.5,6.8,0.5c4.8-1.9,9.2-4.8,12.8-8.5c1.8-1.9,2.8-4.4,2.7-7  c0-1-0.1-2.1-0.1-3.1c-0.1-2.9-0.9-5.8-2.2-8.4l0,0c-1.2-2.4-1.9-5-2-7.7v-0.2C36.5,73.8,35.6,72.3,34.1,71.5z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 -2)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
       // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(0.5 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(0.5  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(10 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(2  8) ",
  },
   //RootTemporary
   {
    id: "28",
    code: "st0",
    path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-17 48) ",
  },
  {
    id: "29",
    code: "st0",
    path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-17 48) ",
  },
   //RootCalcium
   {
    id: "30",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "31",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "32",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "33",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
    transforms:"translate(-6 6) ",
  },
   //RootGuttaPerchaMode
   {
    id: "34",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M522.06,25.21c.36,3.65.54,7.33.55,11s-.19,7.33-.5,10.93a165.28,165.28,0,0,1-3.21,20.75,174.74,174.74,0,0,1-12,36.14c-.24.52-.68.55-1,.07a2.15,2.15,0,0,1-.09-1.82,168.21,168.21,0,0,0,6.66-16.84,182.08,182.08,0,0,0,5.23-18.6,160,160,0,0,0,3.22-20.21c.31-3.48.49-7,.54-10.57s0-7.17-.16-10.75h0c0-.41.15-.74.37-.73s.38.27.41.64Z",
    transforms:"translate(-492 -2) ",
  },
  {
    id: "35",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M506.69,107.38l-3.41,8.22c-.56,1.35-1.64,1.53-2.4.4h0a5.25,5.25,0,0,1-.37-4.49l3.41-8.22c.56-1.35,1.64-1.53,2.4-.4h0A5.25,5.25,0,0,1,506.69,107.38Z",
    transforms:"translate(-492 -2) ",
  },
   //PostCare
   {
    id: "36",
    code: "st17",
    path: "M525.93,72.78c-.09,1.65.15,3.4,0,4.94L525,83.94c-.13,1-.32,1.28-.64,1.28h-4.54c-4.2-.22-4-.55-4.15-1.16-.39-1.91-.22-6.08-.15-12a24.87,24.87,0,0,1,.77-6.13c.08-.3.15-.63.21-1,.83-4.75,2.06-15.13,2.62-24.09a83.8,83.8,0,0,0-.64-15.15c.17-.53,2.87-5,3.2-3.58.6,2.67,2.75,8.48,3.3,16.1C525.74,48.8,526.5,62.11,525.93,72.78Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-495 0)",
  },
   //Veneer
   {
    id: "37",
    code: "st19",
    path: "M507.63,66,502,77.12a13.54,13.54,0,0,0-1.5,6.3v2.2a8.29,8.29,0,0,0,2.3,5.8,64,64,0,0,0,12.1,10.6,7.15,7.15,0,0,0,6.8.5,36.74,36.74,0,0,0,12.8-8.5,9.67,9.67,0,0,0,2.7-7c0-1-.1-2.1-.1-3.1a20.71,20.71,0,0,0-2.2-8.4h0a18.6,18.6,0,0,1-2-7.7v-.2a5.24,5.24,0,0,0-.54-1.87c-.37-.71-.63-.85-.8-.94",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-496 8)",
  },

     //CrownPermanent 11
     {
      id: "40",
      code: "st19",
      path: "M504.37,66l-5.63,11.12a13.54,13.54,0,0,0-1.5,6.3v2.2a8.29,8.29,0,0,0,2.3,5.8,64,64,0,0,0,12.1,10.6,7.15,7.15,0,0,0,6.8.5,36.78,36.78,0,0,0,12.8-8.5,9.64,9.64,0,0,0,2.7-7c0-1-.1-2.1-.1-3.1a20.67,20.67,0,0,0-2.2-8.4h0a18.6,18.6,0,0,1-2-7.7v-.2a5.1,5.1,0,0,0-.54-1.87c-.37-.71-.63-.85-.8-.94",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-492 8)",

    },
    {
      id: "41",
      code: "st19",
      path: "M503.88,113.53l-2,1.47a8.37,8.37,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.85,9.85,0,0,0,1.29,1.73c1.28,1.36,4.28,4.9,6.34,7.33a7.07,7.07,0,0,0,5.42,2.5h0a3.36,3.36,0,0,0,2.66-1.29l5.89-7.59a16.44,16.44,0,0,0,1.7-2.66c2.73-5.38,10.84-23.53-2.41-26-.74-.14-1.41-.54-2.15-.7-2.48-.53-7.64-.38-14.5.16A8,8,0,0,0,503.88,113.53Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-492 8)",
    },
    //CrownGold 11
    {
      id: "43",
      code: "st43",
      path: "M532.78,75.63C527,82.18,517.1,91.44,505,96.93l-1-.72c12.32-5.59,22.12-14.9,28.07-21.58",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-494 6) ",
    },
    {
      id: "44",
      code: "st43",
      path: "M534.7,84.26c-5.77,6.67-14.88,15.34-23.92,17.44l-1-.85c9.77-2.06,19.07-11,24.92-17.92",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-494 6) ",
    },
//CrownZirconia 11
{
  id: "45",   code: "st26", path :"M528.61,64.29h0A3,3,0,0,0,527,62.73c0-.71-.07-2.47-.87-2.79a26.24,26.24,0,0,0-5.38-1.48V30.92a1.27,1.27,0,0,0,0-.34l-1.52-5.39c-.35-1.71-1.06-2.77-1.87-2.77s-1.53,1.06-1.87,2.77L514,30.58a1.27,1.27,0,0,0-.05.34V58.35c-3,.33-5.31,1.08-5.89,2a4.07,4.07,0,0,0-.32,2.24,3.07,3.07,0,0,0-1.6,1.81m8.74-33.31,1.49-5.27s0-.06,0-.09c.21-1.07.6-1.75,1-1.75s.79.68,1,1.75a.29.29,0,0,0,0,.09l1.38,4.89-4.91,2.56Zm0,3.82,5-2.61v1.61l-5,2.61Zm0,3.25,5-2.61v1.61l-5,2.61Zm0,3.25,5-2.61v1.62l-5,2.61Zm0,3.26,5-2.61v1.61l-5,2.61Zm0,3.25,5-2.61v1.61l-5,2.61Zm0,3.25,5-2.61v1.61l-5,2.62Zm0,3.25,5-2.61v1.62l-5,2.61Zm0,3.26,5-2.61v3.25a21.71,21.71,0,0,0-2.47-.15h-.17c-.82,0-1.62,0-2.38.09Zm-6.18,3.8c.33-.53,3.3-1.77,8.73-1.75A24.07,24.07,0,0,1,526,61.46a2.57,2.57,0,0,1,.15.84,30,30,0,0,0-9-1.5c-.07,0-5.24,0-8.48,1.36A1.75,1.75,0,0,1,508.67,61.49Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-494 8) ",},
{
  id: "46",   code: "st26", path :"M534.28,83.81a20.67,20.67,0,0,0-2.2-8.4,18.76,18.76,0,0,1-2-7.7v-.2a5.12,5.12,0,0,0-2.6-4l-.17-.09H506.85a6.62,6.62,0,0,0-2.07,2.49L499.18,77a13.54,13.54,0,0,0-1.5,6.3v2.2a8.34,8.34,0,0,0,2.3,5.8,64,64,0,0,0,12.1,10.6,7.18,7.18,0,0,0,6.8.5,36.78,36.78,0,0,0,12.8-8.5,9.64,9.64,0,0,0,2.7-7C534.38,85.91,534.28,84.81,534.28,83.81Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-494 8) ",},
{
  id: "47",
  code: "st26",
  path: "M503.88,113.53l-2,1.47a8.37,8.37,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.85,9.85,0,0,0,1.29,1.73c1.28,1.36,4.28,4.9,6.34,7.33a7.07,7.07,0,0,0,5.42,2.5h0a3.36,3.36,0,0,0,2.66-1.29l5.89-7.59a16.44,16.44,0,0,0,1.7-2.66c2.73-5.38,10.84-23.53-2.41-26-.74-.14-1.41-.54-2.15-.7-2.48-.53-7.64-.38-14.5.16A8,8,0,0,0,503.88,113.53Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-492 8)",
},
 //Denture 11
 {
  id: "48",
  code: "st19",
  path: "M507.63,66,502,77.12a13.54,13.54,0,0,0-1.5,6.3v2.2a8.29,8.29,0,0,0,2.3,5.8,64,64,0,0,0,12.1,10.6,7.15,7.15,0,0,0,6.8.5,36.74,36.74,0,0,0,12.8-8.5,9.67,9.67,0,0,0,2.7-7c0-1-.1-2.1-.1-3.1a20.71,20.71,0,0,0-2.2-8.4h0a18.6,18.6,0,0,1-2-7.7v-.2a5.24,5.24,0,0,0-.54-1.87c-.37-.71-.63-.85-.8-.94",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-496 8)",

},
{
  id: "49",
  code: "st19",
  path: "M507.14,113.53l-2,1.47a8.39,8.39,0,0,0-3.39,6.37c-.17,3.84.5,10,4.67,17.34a9.5,9.5,0,0,0,1.29,1.73c1.28,1.36,4.28,4.9,6.34,7.33a7.07,7.07,0,0,0,5.42,2.5h0a3.36,3.36,0,0,0,2.66-1.29l5.89-7.59a16.44,16.44,0,0,0,1.7-2.66c2.73-5.38,10.84-23.53-2.41-26-.74-.14-1.41-.54-2.15-.7-2.48-.53-7.64-.38-14.5.16A8,8,0,0,0,507.14,113.53Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-496 8)",

},
{
  id: "50",
  code: "st50",
  polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(2 16)",
},
//Bridge 11
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 20)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3 0)",
},
  //Implant 11
  {
    id: "54",
    code: "st9",
    path: "M14.79,18.85l13-3.45-.93-6.57a12.76,12.76,0,0,0-2.1-5.56c-1.58-2.26-4-3.91-6.74,1a18.44,18.44,0,0,0-2,6.38Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-4.76 -1.08)"
  },

  {
    id: "55",
    code: "st9",
    polygon: "9.16 24 23.7 19.84 24.81 28.47 7.9 33.1 9.16 24",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "56",
    code: "st9",
    polygon: "7.19 38.16 25.43 33.4 26.46 41.5 6.01 46.65 7.19 38.16",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "57",
    code: "st9",
    polygon: "5.26 52.09 27.14 46.88 28.13 54.69 4.03 60.93 5.26 52.09",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "58",
    code: "st9",
    path: "M8.06,68.73l25.4-7-.29,9.44a6,6,0,0,1-.5,2.22,15.78,15.78,0,0,1-5.8,6.69,9.94,9.94,0,0,1-9.88.52A22.21,22.21,0,0,1,9.8,75a7.62,7.62,0,0,1-1.67-4.64C8.1,69.48,8.06,68.73,8.06,68.73Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-4.76 -1.08)"
  },
  {
    id: "59",
    code: "st10",
    path: "M29.56,68.72c-3-.4-8.6-.7-19.2.6A2.61,2.61,0,0,0,8.06,72c.2,4.4.3,14.3-1.9,23.3a28.41,28.41,0,0,0-.9,7.2v1a3.74,3.74,0,0,0,1.9,3.3l4.2,2.3a14.07,14.07,0,0,0,9.4,1.6,38.2,38.2,0,0,0,11.9-4.1,12.5,12.5,0,0,0,6.1-11.5l-.2-5.1a23.64,23.64,0,0,0-1.1-5.9l-4-12.1A5.22,5.22,0,0,0,29.56,68.72Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-4.76 -1.08)"
  },
  {
    id: "60",
    code: "st10",
    path: "M30.93,122.26c-1.19-.67-8.53-4.42-15.54-.4a2.76,2.76,0,0,1-1,.36c-2.75.33-15.48,3.15-3,26.09a3.36,3.36,0,0,0,2.59,1.75l.12,0,0,.23a2.71,2.71,0,0,0,.41,2.09l4.53,6.6a1.29,1.29,0,0,0,1,.57l2.57.2a1.76,1.76,0,0,0,1.39-.5L31.28,152a1.79,1.79,0,0,0,.53-1.27v-1.06l.46-.06a1.64,1.64,0,0,0,1.24-.8c2.3-3.88,12.31-22.35-2.19-26.4A1.88,1.88,0,0,1,30.93,122.26Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-4.76 -1.08)"
  },
   //Bone 11
   {

    id: "61",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
   transforms:"translate(0 -80) ",
  },
  {

    id: "62",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
   transforms:"translate(0 -72) ",
  },
  {

    id: "63",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
   transforms:"translate(0 -64) ",
  },
  {

    id: "64",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
   transforms:"translate(0 -56) ",
  },
  {

    id: "65",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
   transforms:"translate(0 -48) ",
  },
  {

    id: "66",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
   transforms:"translate(0 -40) ",
  },
  {
    id: "67",
    code: "st67",
    path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
    style: { cursor: "pointer" },position: "",
     transforms:"scale(0.48),translate(100 238),rotate(-180)"
  },
     //Resection 11
     {
      id: "68",
      code: "st22",
      rect: {
        x: "25.54",
        y: "16.61",
        width: "3.93",
        height: "41.43"
      },
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-23 60) rotate(-65.8)",
    },
    //TeethCrown 11
    {
      id: "69",
      code: "st67",
      path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
      style: { cursor: "pointer" },position: "",
       transforms:"scale(0.6),translate(88 190),rotate(-180)"
    },
      ],
    },
    // Tooth 64: 'Upper Left Primary First Molar',
    {
      svg_id: "12",
      tooth_number: 64,
      tooth_name: TOOTH_NUMBERS[64],
       tooth_type: "First Molar",
      quadrant: "upper_left",
      tooth_position: 4,
      is_permanent: true,
      width:"51.2625px",
      position:"0 0 43.4 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M10.5,71.9L4.9,83.2c-0.8,1.6-1.2,3.3-1.2,5v2c0,2.2,0.7,4.4,2,6.2c2.9,4,8.8,10.7,17,13.2c0,0,2.1,0.9,6.1,0.1  c1.5-0.3,2.9-1,4-2c2.3-2.1,6.5-6.1,8.6-10.3c0.9-1.9,1.3-3.9,1.2-6l-0.2-3.2c0-0.6-0.1-1.2-0.2-1.8c-0.6-2.7-2.4-10.2-6-14.4  c-0.8-0.9-2-1.5-3.2-1.5l-7.1-0.8c-1.5-0.1-2.9-0.1-4.4,0l-8.8,0.8C11.8,70.6,10.9,71.1,10.5,71.9z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M35.2,70.7c0.2,0.1,0.2-11,0.2-21.2c-0.1-11.3-1.7-22.6-4.9-33.5l-0.7-2.5c-0.3-1.2-1-2.2-2-2.9  c-2-1.5-5.6-3.4-8.3-0.9c-1.2,1.2-1.8,2.8-1.7,4.4c0.2,4.8,0.9,18,0.3,26.7c-0.2,2.9-0.6,5.8-1.3,8.6l-6,22.2  C10.6,71.8,21.2,67.2,35.2,70.7z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M33.5,63.6c0,0-0.3-36.8-4.9-44.5c-0.4-0.7-1.3-0.9-2-0.5c-0.3,0.2-0.5,0.4-0.6,0.7l0,0  c-0.1,0.2-0.1,0.4-0.1,0.6l2.6,44.7c0,0.7,0.6,1.3,1.2,1.4C31,66.1,33,66,33.5,63.6z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M25.1,64.9c0.2-6.5,0.7-32.1-2.3-39.9c-0.3-0.5-0.9-0.7-1.3-0.4c-0.2,0.1-0.4,0.3-0.5,0.6  c-0.5,2.1-0.9,6.8-0.8,17c0,2.6-0.2,5.2-0.8,7.8l-3.2,14.2c-0.2,1,0.4,2,1.4,2.2c0.1,0,0.3,0,0.4,0h5.6  C24.4,66.4,25.1,65.7,25.1,64.9z",
        },
         {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M26.6,136c-2.4,1.2-5.1,1.3-7.6,0.2c-0.4-0.2-0.8,0-1,0.4c-0.1,0.2-0.1,0.4,0,0.6c0.7,2.3,0.6,4.7-0.3,6.9  c-0.3,0.6,0,1.3,0.6,1.5c0.2,0.1,0.5,0.1,0.7,0.1c2.1-0.5,5.1-1,7.5-0.3c0.5,0.2,1.1-0.2,1.2-0.7c0-0.2,0.1-0.3,0-0.5  c-0.8-2.4-0.9-4.9-0.3-7.4c0.1-0.4-0.1-0.7-0.4-0.9C27,135.9,26.8,136,26.6,136z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M32.1,127.5l-7.2-6.1c0,0-3.4-1.6-5.5,0c-1.3,1-4.1,2.8-6.1,4.2c-1.1,0.8-1.4,2.3-0.7,3.4  c0,0.1,0.1,0.1,0.1,0.2l5,6.4c0.5,0.6,1.3,1,2.1,1.1l3,0.2c0.2,0,0.4,0,0.7,0c1.5-0.1,2.9-0.4,4.3-0.9c1.3-0.7,2.9-3,4.6-5.1  C33.2,129.9,33.1,128.4,32.1,127.5L32.1,127.5z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M34.7,129.8c3.5,3,12.7,12.6,2.8,23.7c-0.7,0.8-2,0.9-2.8,0.2c-4-3.2-13.8-12.6-2.4-23.8  C32.9,129.2,34,129.1,34.7,129.8z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M8,154.3c-1.2-1.6,4.9-5.5,7-7.4c0.7-0.6,1.5-1.1,2.3-1.3c3.5-1,7.2-0.7,10.4,0.8c0.5,0.2,1,0.6,1.4,1.1l4.9,6  c1,1.2,0.8,2.9-0.4,3.9l0,0C29.5,161.1,16.3,165.2,8,154.3z",
        },
         {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M17.5,135.8l-6-6.7c-0.6-0.7-1.6-0.8-2.3-0.2c-2.7,2.2-8.1,8.5-2.7,21.7c0.5,1.1,1.7,1.7,2.9,1.2  c0.2-0.1,0.3-0.2,0.5-0.3l6-4.6c0.7-0.6,1.2-1.4,1.4-2.3l1-6.5C18.3,137.2,18.1,136.4,17.5,135.8z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M10.5,71.9L4.9,83.2c-0.8,1.6-1.2,3.3-1.2,5v2c0,2.2,0.7,4.4,2,6.2c2.9,4,8.8,10.7,17,13.2c0,0,2.1,0.9,6.1,0.1  c1.5-0.3,2.9-1,4-2c2.3-2.1,6.5-6.1,8.6-10.3c0.9-1.9,1.3-3.9,1.2-6l-0.2-3.2c0-0.6-0.1-1.2-0.2-1.8c-0.6-2.7-2.4-10.2-6-14.4  c-0.8-0.9-2-1.5-3.2-1.5l-7.1-0.8c-1.5-0.1-2.9-0.1-4.4,0l-8.8,0.8C11.8,70.6,10.9,71.1,10.5,71.9z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M545.67,83.19l5.64.06a12,12,0,0,1,3.5.51l4.81,1.51a9,9,0,0,0,2.42.39l3.22.08a14.36,14.36,0,0,0,5-.71h0a10.44,10.44,0,0,1,3.31-.54l9.24.07c.73,0,.52.24.43,1.28a8.3,8.3,0,0,1-.89,3.11,15,15,0,0,1-.75,1.35c-.58.81-.84,1.1-1.61,2.2a21.76,21.76,0,0,1-3,3.48c-1.87,1.87-3.21,3.14-3.41,3.29-2.62,1.94-3.55,2-7.18,2.23a8.39,8.39,0,0,1-2.8-.55c-2.19-.44-6.27-2.79-8-4.06-.33-.24-2.24-2.1-3.29-2.92a29.91,29.91,0,0,1-4-4.44c-1.94-2.51-2.56-3.91-2.81-5.12C545.24,83.64,544.82,83.19,545.67,83.19Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-542 8)",

        },
         // Onlay
        {
          id: "38",
          code: "st19",
          path: "M559.88,114.13l-9.63,6.78s-9.65,7.92-2.12,22.5c.73.44,3.4,12.27,18.87,9.5.3-.06,8.66-1.54,12.14-8.09,0,0,10.8-12.64-5.16-23.36,0,0-4.71-4.72-6.92-6.93a4.65,4.65,0,0,0-2.68-1.34l-.36-.06A5.82,5.82,0,0,0,559.88,114.13Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-542 8)",

        },
        {
          id: "18",
          code: "st11",
          path: "M550.91,72.53l-5.23,10.56a10.43,10.43,0,0,0-1.12,4.68v1.87a10,10,0,0,0,1.86,5.79c2.72,3.74,8.23,10,15.89,12.34,0,0,2,.84,5.71.09a7.77,7.77,0,0,0,3.73-1.87c2.15-2,6.08-5.7,8-9.62a11.58,11.58,0,0,0,1.12-5.61l-.18-3a10.8,10.8,0,0,0-.19-1.69c-.56-2.52-2.24-9.53-5.61-13.46a4.13,4.13,0,0,0-3-1.4l-6.63-.75a31.47,31.47,0,0,0-4.12,0l-8.22.75A2.65,2.65,0,0,0,550.91,72.53Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-539.29 0)"
        },
        {
          id: "20",
          code: "st25",
          path: "M10.5,71.9L4.9,83.2c-0.8,1.6-1.2,3.3-1.2,5v2c0,2.2,0.7,4.4,2,6.2c2.9,4,8.8,10.7,17,13.2c0,0,2.1,0.9,6.1,0.1  c1.5-0.3,2.9-1,4-2c2.3-2.1,6.5-6.1,8.6-10.3c0.9-1.9,1.3-3.9,1.2-6l-0.2-3.2c0-0.6-0.1-1.2-0.2-1.8c-0.6-2.7-2.4-10.2-6-14.4  c-0.8-0.9-2-1.5-3.2-1.5l-7.1-0.8c-1.5-0.1-2.9-0.1-4.4,0l-8.8,0.8C11.8,70.6,10.9,71.1,10.5,71.9z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 2)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
        // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(1.5 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(1.5  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(10 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(2  8) ",
  },
   //RootTemporary
   {
    id: "28",
    code: "st0",
    path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-16 48) ",
  },
  {
    id: "29",
    code: "st0",
    path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-16 48) ",
  },
   //RootCalcium
   {
    id: "30",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "31",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "32",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "33",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
    transforms:"translate(-6 6) ",
  },
   //RootGuttaPerchaMode
   {
    id: "34",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M522.06,25.21c.36,3.65.54,7.33.55,11s-.19,7.33-.5,10.93a165.28,165.28,0,0,1-3.21,20.75,174.74,174.74,0,0,1-12,36.14c-.24.52-.68.55-1,.07a2.15,2.15,0,0,1-.09-1.82,168.21,168.21,0,0,0,6.66-16.84,182.08,182.08,0,0,0,5.23-18.6,160,160,0,0,0,3.22-20.21c.31-3.48.49-7,.54-10.57s0-7.17-.16-10.75h0c0-.41.15-.74.37-.73s.38.27.41.64Z",
    transforms:"translate(-492 -4) ",
  },
  {
    id: "35",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M506.69,107.38l-3.41,8.22c-.56,1.35-1.64,1.53-2.4.4h0a5.25,5.25,0,0,1-.37-4.49l3.41-8.22c.56-1.35,1.64-1.53,2.4-.4h0A5.25,5.25,0,0,1,506.69,107.38Z",
    transforms:"translate(-492 -2) ",
  },
   //PostCare
   {
    id: "36",
    code: "st17",
    path: "M566.8,57.63l.45-5.85c0-.85-.19-2.06-.19-3.27l-.27-4.5-1-21.45-.17-2.36a1.9,1.9,0,0,1,2.72-1.14S574.26,38,573,64.39c-.1,2.16-1,6-1.1,8.28-.11,1.7-2.55,10.91-2.55,10.91-.16,1-.53,1.64-.94,1.64h-8.87c-.28,0-3.82-11.93-3.82-20.19l3.67-20.86,1.17-20c0-.41.16-.69.3-.5s1.18.33,1.74,1.57c1,2.29,1.85,7.48,1.89,17.5a21.3,21.3,0,0,0,0,2.65c.35,2.24-.25,8,.42,11.55C565.26,59,566.26,59.29,566.8,57.63Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-539.7 0)",
  },
   //Veneer
   {
    id: "37",
    code: "st19",
    path: "M551.55,63.81,546,75.11a11,11,0,0,0-1.2,5v2a10.63,10.63,0,0,0,2,6.2c2.9,4,8.8,10.7,17,13.2,0,0,2.1.9,6.1.1a8.43,8.43,0,0,0,4-2c2.3-2.1,6.5-6.1,8.6-10.3a12.43,12.43,0,0,0,1.2-6l-.2-3.2a12.09,12.09,0,0,0-.2-1.8c-.6-2.7-2.4-10.2-6-14.4",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-542 8)",
  },
       //CrownPermanent 12
       {
        id: "40",
        code: "st19",
        path: "M548.29,63.81l-5.55,11.3a11,11,0,0,0-1.2,5v2a10.61,10.61,0,0,0,2,6.2c2.9,4,8.8,10.7,17,13.2,0,0,2.1.9,6.1.1a8.43,8.43,0,0,0,4-2c2.3-2.1,6.5-6.1,8.6-10.3a12.43,12.43,0,0,0,1.2-6l-.2-3.2a12.09,12.09,0,0,0-.2-1.8c-.6-2.7-2.4-10.2-6-14.4",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-538 8)",

      },
      {
        id: "41",
        code: "st19",
        path: "M556.62,114.13,547,120.91s-9.65,7.92-2.12,22.5c.73.44,3.4,12.27,18.87,9.5.3-.06,8.66-1.54,12.14-8.09,0,0,10.8-12.64-5.16-23.36l-6.92-6.93a4.65,4.65,0,0,0-2.68-1.34l-.36-.06A5.84,5.84,0,0,0,556.62,114.13Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-538 8)",
      },
        //CrownGold 12
        {
          id: "43",
          code: "st43",
          path: "M579.55,74.82C573.33,81.71,563.29,91,550.3,96.75l-.78-.83c13.22-5.88,23.15-15.4,29.54-22.43",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-538 6) ",
        },
        {
          id: "44",
          code: "st43",
          path: "M580.83,83.67c-5.77,6.66-14.79,14.91-23.83,17l-1.24-.6c9.77-2.06,19.22-11,25.07-18",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-538 6) ",
        },
        //CrownZirconia 12
        {
          id: "45",   code: "st26", path :"M573.56,63.41h0a3.08,3.08,0,0,0-1.73-1.57c0-.72-.07-2.5-.94-2.83a30.48,30.48,0,0,0-5.91-1.5V29.62a1.33,1.33,0,0,0-.05-.34l-1.68-5.46c-.37-1.73-1.16-2.8-2-2.8s-1.67,1.07-2.05,2.8l-1.67,5.46a1,1,0,0,0-.05.34V57.4c-3.29.33-5.85,1.09-6.48,2a3.79,3.79,0,0,0-.35,2.27,3.23,3.23,0,0,0-1.76,1.84m9.6-33.74,1.64-5.34a.29.29,0,0,0,0-.09c.23-1.07.66-1.77,1.1-1.77s.87.7,1.1,1.77c0,0,0,.06,0,.09l1.52,5L558.43,32Zm0,3.87L564,31v1.63l-5.52,2.65Zm0,3.3L564,34.32V36l-5.52,2.65Zm0,3.29L564,37.61v1.64l-5.52,2.64Zm0,3.29L564,40.91v1.63l-5.52,2.65Zm0,3.3L564,44.2v1.64l-5.52,2.64Zm0,3.29L564,47.5v1.63l-5.52,2.64Zm0,3.29L564,50.79v1.63l-5.52,2.65Zm0,3.3L564,54.08v3.3c-.86-.09-1.77-.15-2.71-.16h-.19c-.9,0-1.78,0-2.62.09Zm-6.78,3.85c.35-.54,3.62-1.79,9.59-1.77a28.34,28.34,0,0,1,9.39,1.74,2.27,2.27,0,0,1,.16.85,35.6,35.6,0,0,0-9.9-1.52c-.07,0-5.75,0-9.31,1.37A1.54,1.54,0,0,1,551.65,60.58Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-538 8) ",},
         {
          id: "46",   code: "st26", path :"M580,78.42c-.6-2.7-2.4-10.2-6-14.4a3.64,3.64,0,0,0-.54-.5H548.58a2.32,2.32,0,0,0-.3.4l-5.55,11.3a11,11,0,0,0-1.2,5v2a10.61,10.61,0,0,0,2,6.2c2.9,4,8.8,10.7,17,13.2,0,0,2.1.9,6.1.1a8.43,8.43,0,0,0,4-2c2.3-2.1,6.5-6.1,8.6-10.3a12.43,12.43,0,0,0,1.2-6l-.2-3.2A12.09,12.09,0,0,0,580,78.42Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-538 8) ",},
        {
          id: "47",
          code: "st26",
          path: "M556.62,114.13,547,120.91s-9.65,7.92-2.12,22.5c.73.44,3.4,12.27,18.87,9.5.3-.06,8.66-1.54,12.14-8.09,0,0,10.8-12.64-5.16-23.36l-6.92-6.93a4.65,4.65,0,0,0-2.68-1.34l-.36-.06A5.84,5.84,0,0,0,556.62,114.13Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-538 8)",
        },
         //Denture 12
         {
          id: "48",
          code: "st19",
          path: "M551.55,63.81,546,75.11a11,11,0,0,0-1.2,5v2a10.63,10.63,0,0,0,2,6.2c2.9,4,8.8,10.7,17,13.2,0,0,2.1.9,6.1.1a8.43,8.43,0,0,0,4-2c2.3-2.1,6.5-6.1,8.6-10.3a12.43,12.43,0,0,0,1.2-6l-.2-3.2a12.09,12.09,0,0,0-.2-1.8c-.6-2.7-2.4-10.2-6-14.4",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-542 8)",

        },
        {
          id: "49",
          code: "st19",
          path: "M559.88,114.13l-9.63,6.78s-9.65,7.92-2.12,22.5c.73.44,3.4,12.27,18.87,9.5.3-.06,8.66-1.54,12.14-8.09,0,0,10.8-12.64-5.16-23.36,0,0-4.71-4.72-6.92-6.93a4.65,4.65,0,0,0-2.68-1.34l-.36-.06A5.82,5.82,0,0,0,559.88,114.13Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-542 8)",

        },
        {
          id: "50",
          code: "st50",
          polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 16)",
        },
        //Bridge 12
        {
          id: "51",
          code: "st0",
          rect: {
            x: "0.5",
            y: "66.57",
            width: "62.4318",
            height: "2.8"
          },
          style: { cursor: "pointer" },position: "",
           transforms:"translate(0 20)",
        },
        {
          id: "52",
          code: "st0",
          path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-3 0)",
        },
          //Implant 12
          {
            id: "54",
            code: "st9",
            path: "M22,20.46,34.9,17,34,10.44a12.78,12.78,0,0,0-2.11-5.56c-1.58-2.25-4-3.9-6.74,1a18.76,18.76,0,0,0-2,6.39Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-8.57 -2.7)"
          },

          {
            id: "55",
            code: "st9",
            polygon: "12.52 24 27.05 19.84 28.16 28.47 11.25 33.1 12.52 24",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "56",
            code: "st9",
            polygon: "10.55 38.16 28.79 33.4 29.82 41.5 9.37 46.65 10.55 38.16",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "57",
            code: "st9",
            polygon: "8.62 52.09 30.5 46.88 31.49 54.69 7.38 60.93 8.62 52.09",
            style: { cursor: "pointer" },position: "",
          },
          {
            id: "58",
            code: "st9",
            path: "M15.22,70.35l25.4-7-.29,9.44a5.79,5.79,0,0,1-.5,2.21,15.74,15.74,0,0,1-5.8,6.7,9.94,9.94,0,0,1-9.88.51A22,22,0,0,1,17,76.56a7.53,7.53,0,0,1-1.68-4.64C15.26,71.09,15.22,70.35,15.22,70.35Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-8.57 -2.7)"
          },
          {
            id: "59",
            code: "st10",
            path: "M35.44,123.36c-1.19-.67-8.53-4.43-15.53-.41a2.71,2.71,0,0,1-1,.37c-2.75.33-15.48,3.15-3.05,26.08a3.39,3.39,0,0,0,2.59,1.76h.12l-.05.24a2.73,2.73,0,0,0,.41,2.08l4.53,6.61a1.28,1.28,0,0,0,1,.56l2.57.21a1.78,1.78,0,0,0,1.39-.5l7.44-7.27a1.76,1.76,0,0,0,.53-1.26v-1.07l.46-.05a1.68,1.68,0,0,0,1.25-.81c2.29-3.88,12.3-22.34-2.2-26.4A3.12,3.12,0,0,1,35.44,123.36Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-8.57 -2.7)"
          },
          {
            id: "60",
            code: "st10",
            path: "M38.87,71l-.7-.4a10.84,10.84,0,0,0-5.8-1.4l-10.4.4a6.64,6.64,0,0,0-5.8,3.8l-5.6,11.1a13.61,13.61,0,0,0-1.5,6.3V93a8.34,8.34,0,0,0,2.3,5.8,64.37,64.37,0,0,0,12.1,10.6,7.17,7.17,0,0,0,6.8.5,36.78,36.78,0,0,0,12.8-8.5,9.65,9.65,0,0,0,2.7-7c0-1-.1-2.1-.1-3.1a20.55,20.55,0,0,0-2.2-8.4h0a18.72,18.72,0,0,1-2-7.7V75A5.1,5.1,0,0,0,38.87,71Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-8.57 -2.7)"
          },
           //Bone 12
           {

            id: "61",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -80) ",
          },
          {

            id: "62",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -72) ",
          },
          {

            id: "63",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -64) ",
          },
          {

            id: "64",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -56) ",
          },
          {

            id: "65",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -48) ",
          },
          {

            id: "66",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -40) ",
          },
          {
            id: "67",
            code: "st67",
            path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.48),translate(100 238),rotate(-180)"
          },
           //Resection 12
           {
            id: "68",
            code: "st22",
            rect: {
              x: "25.54",
              y: "16.61",
              width: "3.93",
              height: "41.43"
            },
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-23 60) rotate(-65.8)",
          },
          //TeethCrown 12
          {
            id: "69",
            code: "st67",
            path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.61),translate(85 186),rotate(-180)"
          },
      ],
    },
    //Tooth 65: 'Upper Left Primary Second Molar',,
    {
      svg_id: "13",
      tooth_number: 65,
      tooth_name: TOOTH_NUMBERS[65],
       tooth_type: "Second Molar",
      quadrant: "upper_left",
      tooth_position: 5,
      is_permanent: true,
      width:"49.5125px",
      position:"0 0 41.9 172",
      paths: [

        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M11,73.3l-0.1,0.8c-0.2,2.1-0.7,4.1-1.6,6l-1.9,4.2c-1,2.3-1.5,4.7-1.5,7.2v3.1c0,2,0.7,4,1.9,5.6  c1.8,2.4,4.8,5.8,8.3,7.5c0.3,0.5,3.4,1.9,5.2,2.6c0.9,0.3,1.8,0.3,2.7,0l13.1-4.9c1.7-0.6,3-1.9,3.8-3.5l0,0  c0.3-0.7,0.5-1.5,0.4-2.2L41,92.1c-0.1-2.5-0.7-4.9-1.7-7.2l-3.5-8.1c-0.7-1.6-1.1-3.3-1.2-5v-0.1c0,0-11.9-3.6-21.2-1.2  C12,70.9,11.1,72,11,73.3z",
        },
        {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M34.5,71.8c0,0,0.8-42-4.1-54.7c-0.7-1.5-2.5-2.2-4-1.5c-0.5,0.2-0.9,0.6-1.2,1.1C21,23,12.5,39.5,12,71.3  C12.6,71.3,25,69.9,34.5,71.8z",
        },

         {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M28.8,68.1c1,0,1.8-0.8,1.8-1.8l0.5-23.9c0,0,0.1-6.6-1.4-9.6c-0.6-1.2-2.5-0.5-2.4,0.8  c0.3,7.4,1.1,20.6-1.7,27.6l-1.1,3.7c-0.3,0.9-0.1,1.9,0.4,2.6l0,0c0.2,0.3,0.6,0.5,1,0.5L28.8,68.1z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M21.2,64.8c0.6,0,1-0.4,1-1c0.1-3.2,0.5-13.3,0.9-20.7c0.3-5.7,1.3-11.4,2.9-17c0.3-1.1,0.6-2.1,0.8-2.7  c0.2-0.5,0.2-1.1,0.2-1.6v-0.4c0-0.6-0.5-1.2-1.2-1.2c-0.1,0-0.2,0-0.3,0l0,0c-0.2,0.1-0.4,0.2-0.5,0.4c-1.1,2.3-7.8,16.6-7.8,30  c0,0.3-0.2,7.8-0.3,11.7c0,1.3,1,2.3,2.2,2.4c0,0,0,0,0.1,0L21.2,64.8z",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M26.6,136c-2.4,1.2-5.1,1.3-7.6,0.2c-0.4-0.2-0.8,0-1,0.4c-0.1,0.2-0.1,0.4,0,0.6c0.7,2.3,0.6,4.7-0.3,6.9  c-0.3,0.6,0,1.3,0.6,1.5c0.2,0.1,0.5,0.1,0.7,0.1c2.1-0.5,5.1-1,7.5-0.3c0.5,0.2,1.1-0.2,1.2-0.7c0-0.2,0.1-0.3,0-0.5  c-0.8-2.4-0.9-4.9-0.3-7.4c0.1-0.4-0.1-0.7-0.4-0.9C27,135.9,26.8,136,26.6,136z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M32.1,127.5l-7.2-6.1c0,0-3.4-1.6-5.5,0c-1.3,1-4.1,2.8-6.1,4.2c-1.1,0.8-1.4,2.3-0.7,3.4  c0,0.1,0.1,0.1,0.1,0.2l5,6.4c0.5,0.6,1.3,1,2.1,1.1l3,0.2c0.2,0,0.4,0,0.7,0c1.5-0.1,2.9-0.4,4.3-0.9c1.3-0.7,2.9-3,4.6-5.1  C33.2,129.9,33.1,128.4,32.1,127.5L32.1,127.5z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M34.7,129.8c3.5,3,12.7,12.6,2.8,23.7c-0.7,0.8-2,0.9-2.8,0.2c-4-3.2-13.8-12.6-2.4-23.8  C32.9,129.2,34,129.1,34.7,129.8z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M8,154.3c-1.2-1.6,4.9-5.5,7-7.4c0.7-0.6,1.5-1.1,2.3-1.3c3.5-1,7.2-0.7,10.4,0.8c0.5,0.2,1,0.6,1.4,1.1l4.9,6  c1,1.2,0.8,2.9-0.4,3.9l0,0C29.5,161.1,16.3,165.2,8,154.3z",
        },
         {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M17.5,135.8l-6-6.7c-0.6-0.7-1.6-0.8-2.3-0.2c-2.7,2.2-8.1,8.5-2.7,21.7c0.5,1.1,1.7,1.7,2.9,1.2  c0.2-0.1,0.3-0.2,0.5-0.3l6-4.6c0.7-0.6,1.2-1.4,1.4-2.3l1-6.5C18.3,137.2,18.1,136.4,17.5,135.8z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M11,73.3l-0.1,0.8c-0.2,2.1-0.7,4.1-1.6,6l-1.9,4.2c-1,2.3-1.5,4.7-1.5,7.2v3.1c0,2,0.7,4,1.9,5.6  c1.8,2.4,4.8,5.8,8.3,7.5c0.3,0.5,3.4,1.9,5.2,2.6c0.9,0.3,1.8,0.3,2.7,0l13.1-4.9c1.7-0.6,3-1.9,3.8-3.5l0,0  c0.3-0.7,0.5-1.5,0.4-2.2L41,92.1c-0.1-2.5-0.7-4.9-1.7-7.2l-3.5-8.1c-0.7-1.6-1.1-3.3-1.2-5v-0.1c0,0-11.9-3.6-21.2-1.2  C12,70.9,11.1,72,11,73.3z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
          id: "39",
          code: "st20",
          path: "M595.42,84.77l5.19.06a10,10,0,0,1,3.22.52l4.42,1.5a7.4,7.4,0,0,0,2.23.39l3,.08a12.2,12.2,0,0,0,4.56-.71h0a9.17,9.17,0,0,1,3-.54l8.49.07c.68,0,.41.24.4,1.28,0,2.36.2,2.67.09,3.73-.43,4.15-.4,3.13-2.8,5.09-.79.64-1.92.65-5.17,2.13a75.85,75.85,0,0,1-7.55,2.93,13.89,13.89,0,0,1-2.76.77c-4.08-.68-9-4.44-11-6.22a30.32,30.32,0,0,1-3.89-4.48c-1.78-2.51-1.53-3.2-1.74-5.38C595.1,85.21,594.65,84.77,595.42,84.77Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-590 8)",

        },
         // Onlay
        {
          id: "38",
          code: "st19",
          path: "M607.65,113.17,598.39,120s-9.27,7.91-2,22.5c.71.43,3.28,12.27,18.14,9.5.29-.07,8.32-1.54,11.67-8.1,0,0,10.38-12.64-5-23.36,0,0-4.53-4.71-6.65-6.93a4.53,4.53,0,0,0-2.58-1.34l-.34,0A5.41,5.41,0,0,0,607.65,113.17Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-589 8)",

        },
        {
          id: "18",
          code: "st11",
          path: "M599.37,74.74l-.09.72a15.68,15.68,0,0,1-1.48,5.37l-1.75,3.76A15.6,15.6,0,0,0,594.67,91v2.78a8.27,8.27,0,0,0,1.75,5c1.66,2.15,4.42,5.19,7.65,6.71.28.45,3.13,1.7,4.79,2.33a3.93,3.93,0,0,0,2.49,0l12.08-4.39a6.26,6.26,0,0,0,3.5-3.13h0a3.75,3.75,0,0,0,.37-2l-.28-6.8a17.17,17.17,0,0,0-1.57-6.44l-3.22-7.25a12.72,12.72,0,0,1-1.11-4.48v-.09s-11-3.22-19.54-1.07A2.86,2.86,0,0,0,599.37,74.74Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-587 0)"
        },
        {
          id: "20",
          code: "st25",
          path: "M11,73.3l-0.1,0.8c-0.2,2.1-0.7,4.1-1.6,6l-1.9,4.2c-1,2.3-1.5,4.7-1.5,7.2v3.1c0,2,0.7,4,1.9,5.6  c1.8,2.4,4.8,5.8,8.3,7.5c0.3,0.5,3.4,1.9,5.2,2.6c0.9,0.3,1.8,0.3,2.7,0l13.1-4.9c1.7-0.6,3-1.9,3.8-3.5l0,0  c0.3-0.7,0.5-1.5,0.4-2.2L41,92.1c-0.1-2.5-0.7-4.9-1.7-7.2l-3.5-8.1c-0.7-1.6-1.1-3.3-1.2-5v-0.1c0,0-11.9-3.6-21.2-1.2  C12,70.9,11.1,72,11,73.3z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 22)",
        },
        {
           id: "19",
          code: "st16",
          path: "M30.26,128.65S19.32,131,15.12,133.8a2,2,0,0,0,.12,3.42c2.07,1.29,6.46,3.32,15.49,5.39A2.48,2.48,0,0,1,32.65,145h0a2.08,2.08,0,0,1-1.56,2c-3.66,1-12.85,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 2)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
          {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
          {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-4 8)",style: { cursor: "pointer" },position: "",},
         // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-0.5 10) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-0.5  10) ",
  },
   //RestoratinAmalgam
   {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M20.85,94.67c.5,0,1-.07,1.53-.09a3.66,3.66,0,0,0,2.47-1.13c.52-.57,1.16-1.29,1.79-2.06.45-.54.89-1.08,1.27-1.59a3.43,3.43,0,0,0-.31-4.61c-1.31-1.18-2.7-.74-3.81-.38a6.58,6.58,0,0,1-2.09.42c-1.62,0-2.58-1-2.49-4.15a.83.83,0,0,0-.68-.79h0a4.46,4.46,0,0,0-3.07.35,2.69,2.69,0,0,0-1.74,2.78C14.35,88,12,88.28,9.89,88.5a6.24,6.24,0,0,0-.78.13s.07.1.2.54c1,3.39,2.7,4.83,4.81,5.39a3.61,3.61,0,0,0,3.29-.78l.25-.23a1.32,1.32,0,0,1,1.74,0c.45.36.94.75,1.45,1.14Z",
    transforms:"translate(10 8) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M14.53,94.14c3.07.68,5.58-2.13,6.32-3.06,2,.92,4.09,1.67,5.45,2.16.53.19,1,.38,1.14.43a.43.43,0,1,0,.45-.73,12.68,12.68,0,0,0-1.29-.5c-.66-.24-1.56-.56-2.56-1a8.88,8.88,0,0,0,4.51-3.33.43.43,0,0,0-.11-.6.46.46,0,0,0-.25-.08.41.41,0,0,0-.35.18s-2.62,3.68-5.65,3a.38.38,0,0,0-.15,0c-2.25-1-4.57-2.17-5.66-3.4a.41.41,0,0,0-.32-.14.48.48,0,0,0-.29.11.44.44,0,0,0,0,.61A14.46,14.46,0,0,0,20,90.71c-.78.94-2.92,3.13-5.33,2.6a.43.43,0,0,0-.51.33A.44.44,0,0,0,14.53,94.14Z",
    transforms:"translate(2  8) ",
  },
   //RootTemporary
   {
    id: "28",
    code: "st0",
    path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-17 48) ",
  },
  {
    id: "29",
    code: "st0",
    path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-17 48) ",
  },
   //RootCalcium
   {
    id: "30",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M36.8,104.34a.8.8,0,0,1,.52,1l-.88,2.72a.8.8,0,0,1-1.52-.49l.88-2.72a.78.78,0,0,1,1-.51Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "31",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M39.89,108.57a.8.8,0,0,1-1-.5L37.82,105a.8.8,0,1,1,1.51-.51l1.06,3.11a.81.81,0,0,1-.5,1Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "32",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M43.45,106a.81.81,0,0,1-1.11.17L40,104.47a.8.8,0,0,1,.94-1.29l2.31,1.69a.8.8,0,0,1,.17,1.11Z",
    transforms:"translate(-6 6) ",
  },
  {
    id: "33",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M40.91,101l-5,1.8a.41.41,0,0,1-.55-.38V97.92a.55.55,0,0,0-.2-.42L33,95.66a1.07,1.07,0,0,1-.4-.83l-.13-8.12a.55.55,0,0,1,1-.27l3.41,6a.54.54,0,0,0,.32.25l3.22,1a1.08,1.08,0,0,1,.79,1.07l0,6a.41.41,0,0,1-.27.38Z",
    transforms:"translate(-6 6) ",
  },
   //RootGuttaPerchaMode
   {
    id: "34",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M522.06,25.21c.36,3.65.54,7.33.55,11s-.19,7.33-.5,10.93a165.28,165.28,0,0,1-3.21,20.75,174.74,174.74,0,0,1-12,36.14c-.24.52-.68.55-1,.07a2.15,2.15,0,0,1-.09-1.82,168.21,168.21,0,0,0,6.66-16.84,182.08,182.08,0,0,0,5.23-18.6,160,160,0,0,0,3.22-20.21c.31-3.48.49-7,.54-10.57s0-7.17-.16-10.75h0c0-.41.15-.74.37-.73s.38.27.41.64Z",
    transforms:"translate(-492 0) ",
  },
  {
    id: "35",
    code: "st34",
    style: { cursor: "pointer" },position: "",
    path: "M506.69,107.38l-3.41,8.22c-.56,1.35-1.64,1.53-2.4.4h0a5.25,5.25,0,0,1-.37-4.49l3.41-8.22c.56-1.35,1.64-1.53,2.4-.4h0A5.25,5.25,0,0,1,506.69,107.38Z",
    transforms:"translate(-492 0) ",
  },
  //PostCare
  {
    id: "36",
    code: "st17",
    path: "M610.81,62.89,614,58.14a5.81,5.81,0,0,0,.63-2.72V52l.1-19.39c0-.73,1.15-1.16,1.76-.78a4.92,4.92,0,0,1,1.31,2,9.6,9.6,0,0,1,.44,2.75,233.57,233.57,0,0,1-.17,28.2c-.16,3.07-.43,6.38-.82,9.91a27,27,0,0,1-.79,4.08L615,84.17c-.22.82-.74,1.35-1.3,1.35h-7.13a1.17,1.17,0,0,1-1-.83,37,37,0,0,1-2.78-14.07L604,50.53A62,62,0,0,1,609,29l.07-.23c.93-2.1,1.57-4.69,2.41-6.36a3.92,3.92,0,0,1,2.09-2.1,2.37,2.37,0,0,1,1.2,2.32c-.59,4.84-4,16.49-4.52,26.34C610.26,49.53,610.07,64.26,610.81,62.89Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-587 0)",
  },
   //Veneer
   {
    id: "37",
    code: "st19",
    path: "M600.47,63.81l-.35,1.43a14.55,14.55,0,0,0-.25,1.47A17.94,17.94,0,0,1,598.35,72l-1.9,4.2a17.84,17.84,0,0,0-1.5,7.2v3.1a9.45,9.45,0,0,0,1.9,5.6c1.8,2.4,4.8,5.8,8.3,7.5.3.5,3.4,1.9,5.2,2.6a4.16,4.16,0,0,0,2.7,0l13.1-4.9a6.9,6.9,0,0,0,3.8-3.5h0a4.41,4.41,0,0,0,.4-2.2l-.3-7.6a20.07,20.07,0,0,0-1.7-7.2l-3.5-8.1a15.08,15.08,0,0,1-1.05-3.59q-.1-.7-.15-1.41v-.1",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-590 8)",
  },
      //CrownPermanent 13
      {
        id: "40",
        code: "st19",
        path: "M597.21,63.81l-.35,1.43c-.11.49-.19,1-.25,1.47A17.74,17.74,0,0,1,595.09,72l-1.9,4.2a17.84,17.84,0,0,0-1.5,7.2v3.1a9.45,9.45,0,0,0,1.9,5.6c1.8,2.4,4.8,5.8,8.3,7.5.3.5,3.4,1.9,5.2,2.6a4.16,4.16,0,0,0,2.7,0l13.1-4.9a6.94,6.94,0,0,0,3.8-3.5h0a4.39,4.39,0,0,0,.4-2.2l-.3-7.6a20.12,20.12,0,0,0-1.7-7.2l-3.5-8.1a15,15,0,0,1-1.05-3.59q-.1-.7-.15-1.41v-.1",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-586 8)",

      },
      {
        id: "41",
        code: "st19",
        path: "M604.39,113.17,595.13,120s-9.27,7.91-2,22.5c.71.43,3.28,12.27,18.14,9.5.29-.07,8.32-1.54,11.67-8.1,0,0,10.38-12.64-5-23.36,0,0-4.53-4.71-6.65-6.93a4.53,4.53,0,0,0-2.58-1.34h-.34A5.41,5.41,0,0,0,604.39,113.17Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-586 8)",
      },
        //CrownGold 13
        {
          id: "43",
          code: "st43",
          path: "M624.69,75.32c-6.22,6.89-14.78,15.48-27.76,21.25l-1.13-.68C609,90,617.82,81,624.21,74",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-586 6) ",
        },
        {
          id: "44",
          code: "st43",
          path: "M627.06,84c-5.77,6.67-15.31,14.65-24.36,16.75l-.87-.44c9.67-2.45,19.3-10.63,25.15-17.59",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-586 6) ",
        },
           //CrownZirconia 13
           {
            id: "45",   code: "st26", path :"M620.33,64.3h0a3.06,3.06,0,0,0-1.65-1.57c0-.72-.07-2.5-.9-2.82a26.92,26.92,0,0,0-5.63-1.51V30.52a1.34,1.34,0,0,0,0-.35l-1.59-5.45c-.36-1.74-1.11-2.81-2-2.81S607,23,606.6,24.72L605,30.17a1.34,1.34,0,0,0,0,.35V58.29c-3.14.33-5.57,1.09-6.17,2a4,4,0,0,0-.34,2.28,3.14,3.14,0,0,0-1.67,1.83m9.14-33.73,1.56-5.34,0-.1c.21-1.07.63-1.77,1-1.77s.83.7,1.05,1.77c0,0,0,.07,0,.1l1.45,5-5.15,2.58Zm0,3.86,5.26-2.64v1.63l-5.26,2.65Zm0,3.3,5.26-2.65v1.64l-5.26,2.64Zm0,3.29,5.26-2.64v1.63l-5.26,2.65Zm0,3.3,5.26-2.65v1.63l-5.26,2.65Zm0,3.29,5.26-2.65v1.64l-5.26,2.64Zm0,3.29,5.26-2.64V50l-5.26,2.65Zm0,3.3,5.26-2.65v1.64L605.92,56Zm0,3.29L611.18,55v3.3a21.4,21.4,0,0,0-2.58-.16h-.19c-.85,0-1.69,0-2.49.09Zm-6.46,3.85c.34-.53,3.45-1.79,9.14-1.77a26.19,26.19,0,0,1,8.95,1.74,2.59,2.59,0,0,1,.15.85,32.47,32.47,0,0,0-9.43-1.52c-.08,0-5.48,0-8.87,1.38A1.81,1.81,0,0,1,599.46,61.47Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-586 8) ",},
           {
            id: "46",   code: "st26", path :"M596.78,64.44l-.1,1.68a17.86,17.86,0,0,1-1.6,6l-1.9,4.2a17.84,17.84,0,0,0-1.5,7.2v3.1a9.45,9.45,0,0,0,1.9,5.6c1.8,2.4,4.8,5.8,8.3,7.5.3.5,3.4,1.9,5.2,2.6a4.16,4.16,0,0,0,2.7,0l13.1-4.9a6.87,6.87,0,0,0,3.8-3.5h0a4.39,4.39,0,0,0,.4-2.2l-.3-7.6a20.12,20.12,0,0,0-1.7-7.2l-3.5-8.1a14.65,14.65,0,0,1-1.2-5v-.1S596.89,63.09,596.78,64.44Z" ,style: { cursor: "pointer" },position: "", transforms:"translate(-586 8) ",},
           {
            id: "47",
            code: "st26",
            path: "M604.39,113.17,595.13,120s-9.27,7.91-2,22.5c.71.43,3.28,12.27,18.14,9.5.29-.07,8.32-1.54,11.67-8.1,0,0,10.38-12.64-5-23.36,0,0-4.53-4.71-6.65-6.93a4.53,4.53,0,0,0-2.58-1.34h-.34A5.41,5.41,0,0,0,604.39,113.17Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-586 8)",
          },
          //Denture 13
          {
            id: "48",
            code: "st19",
            path: "M600.47,63.81l-.35,1.43a14.55,14.55,0,0,0-.25,1.47A17.94,17.94,0,0,1,598.35,72l-1.9,4.2a17.84,17.84,0,0,0-1.5,7.2v3.1a9.45,9.45,0,0,0,1.9,5.6c1.8,2.4,4.8,5.8,8.3,7.5.3.5,3.4,1.9,5.2,2.6a4.16,4.16,0,0,0,2.7,0l13.1-4.9a6.9,6.9,0,0,0,3.8-3.5h0a4.41,4.41,0,0,0,.4-2.2l-.3-7.6a20.07,20.07,0,0,0-1.7-7.2l-3.5-8.1a15.08,15.08,0,0,1-1.05-3.59q-.1-.7-.15-1.41v-.1",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-590 8)",

          },
          {
            id: "49",
            code: "st19",
            path: "M607.65,113.17,598.39,120s-9.27,7.91-2,22.5c.71.43,3.28,12.27,18.14,9.5.29-.07,8.32-1.54,11.67-8.1,0,0,10.38-12.64-5-23.36,0,0-4.53-4.71-6.65-6.93a4.53,4.53,0,0,0-2.58-1.34l-.34,0A5.41,5.41,0,0,0,607.65,113.17Z",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-590 8)",

          },
          {
            id: "50",
            code: "st50",
            polygon: "21.97 116.37 34.7 97.44 27.18 97.44 23.16 76 19.4 76 16.14 97.36 8.54 97.32 21.97 116.37",
            style: { cursor: "pointer" },position: "",
            transforms:"translate(2 16)",
          },
                  //Bridge 13
                  {
                    id: "51",
                    code: "st0",
                    rect: {
                      x: "0.5",
                      y: "66.57",
                      width: "62.4318",
                      height: "2.8"
                    },
                    style: { cursor: "pointer" },position: "",
                     transforms:"translate(0 20)",
                  },
                  {
                    id: "52",
                    code: "st0",
                    path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
                    style: { cursor: "pointer" },position: "",
                    transforms:"translate(-3 0)",
                  },
                   //Implant 13
                   {
                    id: "54",
                    code: "st9",
                    path: "M20.53,21.16l12.95-3.45-.93-6.58a12.78,12.78,0,0,0-2.11-5.56c-1.57-2.25-4-3.9-6.73,1A18.56,18.56,0,0,0,21.66,13Z",
                    style: { cursor: "pointer" },position: "",
                    transforms:"translate(-6.45 -3.39)"
                  },

                  {
                    id: "55",
                    code: "st9",
                    polygon: "13.21 24 27.74 19.84 28.85 28.47 11.94 33.1 13.21 24",
                    style: { cursor: "pointer" },position: "",
                  },
                  {
                    id: "56",
                    code: "st9",
                    polygon: "11.24 38.16 29.48 33.4 30.51 41.5 10.06 46.65 11.24 38.16",
                    style: { cursor: "pointer" },position: "",
                  },
                  {
                    id: "57",
                    code: "st9",
                    polygon: "9.3 52.09 31.19 46.88 32.17 54.69 8.07 60.93 9.3 52.09",
                    style: { cursor: "pointer" },position: "",
                  },
                  {
                    id: "58",
                    code: "st9",
                    path: "M13.8,71l25.4-7-.3,9.44a5.78,5.78,0,0,1-.49,2.21,15.85,15.85,0,0,1-5.81,6.7,9.92,9.92,0,0,1-9.87.51,22,22,0,0,1-7.19-5.64,7.65,7.65,0,0,1-1.68-4.64C13.84,71.79,13.8,71,13.8,71Z",
                    style: { cursor: "pointer" },position: "",
                    transforms:"translate(-6.45 -3.39)"
                  },
                  {
                    id: "59",
                    code: "st10",
                    path: "M13.75,72.23l-5.6,11.3a11.15,11.15,0,0,0-1.2,5v2a10.66,10.66,0,0,0,2,6.2c2.9,4,8.8,10.7,17,13.2,0,0,2.1.9,6.1.1a8.43,8.43,0,0,0,4-2c2.3-2.1,6.5-6.1,8.6-10.3a12.43,12.43,0,0,0,1.2-6l-.2-3.2a11,11,0,0,0-.2-1.8c-.6-2.7-2.4-10.2-6-14.4a4.41,4.41,0,0,0-3.2-1.5l-7.1-.8a32.3,32.3,0,0,0-4.4,0l-8.8.8A2.8,2.8,0,0,0,13.75,72.23Z",
                    style: { cursor: "pointer" },position: "",
                    transforms:"translate(-6.45 -3.39)"
                  },
                  {
                    id: "60",
                    code: "st10",
                    path: "M36.39,128.32l-7-6.36a5.28,5.28,0,0,0-1.84-1.09,4.24,4.24,0,0,0-3.64.16c-.64.37-7.81,5.15-7.81,5.15S5.83,133.5,8.19,142.84c.06.39,1.94,6.77,2.29,7.94a.53.53,0,0,0,.22.29h0a2.8,2.8,0,0,1,1.13,3.23h0s10.64,14.53,26.07,2.59a4.59,4.59,0,0,0,.27-1.06,2.65,2.65,0,0,1,2.58-2.15h.4s12.61-11.52-4.32-25Z",
                    style: { cursor: "pointer" },position: "",
                    transforms:"translate(-6.45 -3.39)"
                  },
                   //Bone 13
          {

            id: "61",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -80) ",
          },
          {

            id: "62",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -72) ",
          },
          {

            id: "63",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -64) ",
          },
          {

            id: "64",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -56) ",
          },
          {

            id: "65",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -48) ",
          },
          {

            id: "66",  code: "st21",
            rect: {
              x: "2",
              y: "99.6",
              width: "48",
              height: "1.5"
            },

            style: { cursor: "pointer" },position: "",
           transforms:"translate(0 -40) ",
          },
          {
            id: "67",
            code: "st67",
            path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.48),translate(100 238),rotate(-180)"
          },
          //Resection 13
          {
            id: "68",
            code: "st22",
            rect: {
              x: "25.54",
              y: "16.61",
              width: "3.93",
              height: "41.43"
            },
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-23 60) rotate(-65.8)",
          },
          //TeethCrown 13
          {
            id: "69",
            code: "st67",
            path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.57),translate(90 200),rotate(-180)"
          },
      ],
    },
   
];
export const DantalB : DentalSvg[] = [
 
 

  //Tooth 85: 'Lower Right Primary Second Molar',
  {
    svg_id: "29",
     tooth_number: 85,
      tooth_name: TOOTH_NUMBERS[85],
       tooth_type: "Second Molar",
      quadrant: "Lower Right",
      tooth_position: 5,
      is_permanent: true,
    width:"53.7px",
    position:"0 0 45.5 172",
    paths: [
      {
        id: "1",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M10.1,94.7c3.4,1,10.8,2.3,22.1,0.6l1.4-8.7c0.2-1.3,0.6-2.7,1.1-3.9l1.2-3c0.8-2.1,1.2-4.2,1.2-6.4v-4.7  c0-2.2-1-4.3-2.7-5.7l-10.5-8.5c-0.8-0.7-2-0.8-3-0.4l-2.6,1.2c-0.3,0.2-0.7,0.3-1.1,0.3C13.6,56-6,60.2,7,92  C7.6,93.3,8.7,94.3,10.1,94.7z",
      },
      {
        id: "2",
        code: "st1",
        style: { cursor: "pointer" },position: "",
        path: "M31.8,97.3c-3.9,6.8-8.6,41.3-13.9,49.8c-0.5,0.7-1.3,1.2-2.2,1.2h-1.7c-1,0-1.7-0.8-1.8-1.7  c-0.2-8.2-1.3-45.5-4-53c0,0,16.1,2,21.3,2C30.5,95.7,33,95.3,31.8,97.3z",
      },
      {
        id: "3",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "M21.9,102.4v12.3c0,1.1-0.2,2.2-0.4,3.2c-1.3,4.5-4.8,17.8-5.3,23.9c-0.1,0.7-0.7,1.3-1.4,1.2l0,0  c-0.6,0-1-0.4-1-1c0,0,0-0.1,0-0.1l1.4-17c0.1-0.7,0.2-1.4,0.4-2.1l2-7.1c0.2-0.7,0.3-1.4,0.4-2.1c0.2-2.5,0.8-8.7,0.4-11.3  c-0.1-0.6,0.3-1.1,0.9-1.2c0,0,0.1,0,0.1,0h1.4C21.4,101.3,21.9,101.8,21.9,102.4z",
      },
      {
        id: "4",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "5",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },{
        id: "6",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "7",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "8",
        code: "st4",
        style: { cursor: "pointer" },position: "",
        path: "M25,19.7l-1.9,0.6c-1.4,0.4-2.8,0.4-4.1,0.1l-2.5-0.7c-0.5-0.2-1.1,0.1-1.3,0.6c-0.1,0.3-0.1,0.5,0,0.8  c0.8,2.1,1.7,5.6,0.2,8.6c-0.2,0.3,0,0.8,0.3,0.9c0.2,0.1,0.4,0.1,0.5,0l2.9-1.1c0.9-0.4,2-0.4,2.9,0l2.1,0.8  c0.7,0.3,1.4-0.1,1.7-0.7c0.1-0.3,0.1-0.6,0-0.9c-0.8-2.3-1.4-5.6-0.2-8c0.2-0.3,0.1-0.7-0.2-0.9C25.3,19.7,25.1,19.7,25,19.7z",
      },
      {
        id: "9",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M26.8,19l6.2-5.5c1.1-1,1.2-2.7,0.2-3.8c-0.1-0.1-0.2-0.2-0.3-0.3C29,6.3,20.6,1.6,11.1,8.6  c-1.7,1.3-2.1,3.7-0.8,5.5c0,0.1,0.1,0.1,0.1,0.2c1.3,1.5,2.6,3.4,3.4,4.4c0.3,0.5,0.8,0.8,1.4,1l4.6,1.3c0.5,0.1,1,0.1,1.5,0  l4.5-1.4C26.2,19.4,26.5,19.2,26.8,19z",
      },
      {
        id: "10",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M32.7,13.3l-4.8,5.1c-1,1-1.6,2.2-2,3.6c-0.8,3.2-0.2,6.7,1.8,9.4l2.9,4.5c0.7,1,2.1,1.4,3.1,0.7  c0.1-0.1,0.2-0.1,0.2-0.2c3.4-2.9,8.9-10.2,0.9-22.8c-0.4-0.6-1.2-0.8-1.9-0.4C32.9,13.1,32.8,13.2,32.7,13.3z",
      },
      {
        id: "11",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M25.7,30.6l-4.9-1.4c-0.7-0.2-1.4-0.1-2.1,0.1l-3,1.2c-0.6,0.2-1.1,0.6-1.4,1.1l-2.2,3.1  c-1,1.4-0.7,3.4,0.7,4.4l6.4,5c1.1,0.8,2.6,0.9,3.7,0.2l2.5-1.7l3.8-2.4c1.5-0.9,2-2.9,1-4.5c0,0-0.1-0.1-0.1-0.1l-2.7-3.8  C27,31.2,26.4,30.8,25.7,30.6z",
      },

      {
        id: "12",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M14.9,19.3l-5.4-7.1C9,11.5,8,11.4,7.4,11.9c-0.1,0.1-0.2,0.2-0.3,0.3C4.3,16-1.5,26.3,8.7,36.5  c0.6,0.6,1.5,0.6,2,0c0,0,0.1-0.1,0.1-0.1l4-4.6c0.3-0.3,0.5-0.7,0.6-1.2l0.8-4.7c0.2-1.2,0.2-2.5-0.2-3.7l-0.6-1.9  C15.3,20,15.1,19.6,14.9,19.3z",
      },
      {
        id: "13",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "14",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "15",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "16",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "17",
        code: "st24",
        path: "M10.1,94.7c3.4,1,10.8,2.3,22.1,0.6l1.4-8.7c0.2-1.3,0.6-2.7,1.1-3.9l1.2-3c0.8-2.1,1.2-4.2,1.2-6.4v-4.7  c0-2.2-1-4.3-2.7-5.7l-10.5-8.5c-0.8-0.7-2-0.8-3-0.4l-2.6,1.2c-0.3,0.2-0.7,0.3-1.1,0.3C13.6,56-6,60.2,7,92  C7.6,93.3,8.7,94.3,10.1,94.7z",
        style: { cursor: "pointer" },
        position: "0 0",
        transforms:"scale(0.8),translate(5 20)",
      },
       // Onlay
       {
        id: "38",
        code: "st19",
        path: "M199,8.91a13.25,13.25,0,0,0-4.59-4.67c-4.62-2.75-13.85-6.38-21,2.69,0,.16-14.87,12-.08,26,.27,0,7.51,5.62,9.71,7.33a2.49,2.49,0,0,0,1.43.53h.25a2.57,2.57,0,0,0,1.43-.37l6.74-4a6.12,6.12,0,0,0,2.41-2.65h0a3.05,3.05,0,0,1,1.21-1.33C199.18,30.84,207.93,24.16,199,8.91Z",
        style: { cursor: "pointer" },position: "",
         transforms:"translate(-164 4)"
      },
      {
        id: "39",
        code: "st20",
        path: "M200.8,64.63a7.43,7.43,0,0,0-2.7-5.7l-10.5-8.5a2.94,2.94,0,0,0-3-.4l-2.6,1.2a2,2,0,0,1-1.1.3c-2.46.35-12.83,2.54-14.31,14.79v.14a1.37,1.37,0,0,0,1.53,1.19h4.15a9.35,9.35,0,0,0,3.47-.67l4.76-2a6.62,6.62,0,0,1,2.4-.51l3.19-.1a11,11,0,0,1,4.91.93,8.48,8.48,0,0,0,3.29.7l5.1.06a1.56,1.56,0,0,0,1.41-.57Z",
        style: { cursor: "pointer" },position: "",
         transforms:"translate(-164 4)"
      },
      {
        id: "18",
        code: "st180",
        path: "M10.1,94.7c3.4,1,10.8,2.3,22.1,0.6l1.4-8.7c0.2-1.3,0.6-2.7,1.1-3.9l1.2-3c0.8-2.1,1.2-4.2,1.2-6.4v-4.7  c0-2.2-1-4.3-2.7-5.7l-10.5-8.5c-0.8-0.7-2-0.8-3-0.4l-2.6,1.2c-0.3,0.2-0.7,0.3-1.1,0.3C13.6,56-6,60.2,7,92  C7.6,93.3,8.7,94.3,10.1,94.7z",
        style: { cursor: "pointer" },position: "",
        transforms:"scale(0.95),translate(1 4)",
      },
      {
         id: "19",
        code: "st16",
        path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-3 -2)",
      },
      {
        id: "20",
        code: "st25",
        path: "M10.1,94.7c3.4,1,10.8,2.3,22.1,0.6l1.4-8.7c0.2-1.3,0.6-2.7,1.1-3.9l1.2-3c0.8-2.1,1.2-4.2,1.2-6.4v-4.7  c0-2.2-1-4.3-2.7-5.7l-10.5-8.5c-0.8-0.7-2-0.8-3-0.4l-2.6,1.2c-0.3,0.2-0.7,0.3-1.1,0.3C13.6,56-6,60.2,7,92  C7.6,93.3,8.7,94.3,10.1,94.7z",
        style: { cursor: "pointer" },
        position: "0 0",
        transforms:"scale(0.8),translate(5 20)",
      },
      {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-8 -4)",style: { cursor: "pointer" },position: "",},
      {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-8 -4)",style: { cursor: "pointer" },position: "",},
      {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-8 -4)",style: { cursor: "pointer" },position: "",},
              // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-3.5 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-3.5 -2) ",
  },
     //RestoratinAmalgam
     {
      id: "26",
      code: "st26",
      style: { cursor: "pointer" },position: "",
      path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
      transforms:"translate(-24 0) ",
    },
    //RestoratinGlassIonomer
    {
      id: "27",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
      transforms:"translate(-2  8) ",
    },
       //RootTemporary
       {
        id: "28",
        code: "st0",
        path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-20 38) ",
      },
      {
        id: "29",
        code: "st0",
        path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-20 38) ",
      },
       //RootCalcium
    {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 4) ",
    },
 // GuttaPercha
 {
  id: "34",
  code: "st34",
  style: { cursor: "pointer" },position: "",
  path: "M12.35,131.73a100.31,100.31,0,0,1,0-10.69c.21-3.57.59-7.1,1.14-10.57a143.08,143.08,0,0,1,4.64-20A158.78,158.78,0,0,1,33.33,56a.58.58,0,0,1,1.08,0,1.85,1.85,0,0,1,0,1.77,154.36,154.36,0,0,0-8.27,16A165.25,165.25,0,0,0,19.4,91.58,140,140,0,0,0,14.78,111q-.79,5-1.17,10.22c-.21,3.45-.33,7-.39,10.43h0c0,.4-.21.71-.45.69s-.4-.28-.42-.64Z",
  transforms:"translate(4 0) ",
},
{
  id: "35",
  code: "st34",
  style: { cursor: "pointer" },position: "",
  path: "M33.69,52.81,37.9,45c.7-1.28,1.89-1.41,2.68-.28h0a4.57,4.57,0,0,1,.17,4.37L36.54,56.9c-.69,1.28-1.89,1.41-2.68.28h0A4.6,4.6,0,0,1,33.69,52.81Z",
  transforms:"translate(4 0) ",
},
//PostCare
{
  id: "36",
  code: "st17",
  path: "M190.09,95.33c-.12-1.79-.33-3.53-.61-5.2l-1.14-6.85c-.17-1-.57-1.72-1-1.72h-5.56c-.31,0-.6.39-.78,1.06A80.1,80.1,0,0,0,179.1,100c0,.44,0,.88.05,1.32l0,41.19a.6.6,0,0,0,1.15.25C183.37,135.68,191.27,112.52,190.09,95.33Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-165 0)",
},
 //Veneer
 {
  id: "37",
  code: "st19",
  path: "M195.9,91.33l1.4-8.7a17,17,0,0,1,1.1-3.9l1.2-3a17.73,17.73,0,0,0,1.2-6.4v-4.7a7.43,7.43,0,0,0-2.7-5.7l-10.5-8.5a2.94,2.94,0,0,0-3-.4l-2.6,1.2a2,2,0,0,1-1.1.3c-3.5.5-23.1,4.7-10.1,36.5a5.25,5.25,0,0,0,1.1,1.6",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-164  5)",
},
    //CrownPermanent 20
    {
      id: "40",
      code: "st19",
      path: "M195.9,91.33l1.4-8.7a17,17,0,0,1,1.1-3.9l1.2-3a17.73,17.73,0,0,0,1.2-6.4v-4.7a7.43,7.43,0,0,0-2.7-5.7l-10.5-8.5a2.94,2.94,0,0,0-3-.4l-2.6,1.2a2,2,0,0,1-1.1.3c-3.5.5-23.1,4.7-10.1,36.5a5.25,5.25,0,0,0,1.1,1.6",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-163 3.5)",

    },
    {
      id: "41",
      code: "st19",
      path: "M199,8.91a13.25,13.25,0,0,0-4.59-4.67c-4.62-2.75-13.85-6.38-21,2.69,0,.16-14.87,12-.08,26,.27,0,7.51,5.62,9.71,7.33a2.49,2.49,0,0,0,1.43.53h.25a2.57,2.57,0,0,0,1.43-.37l6.74-4a6.12,6.12,0,0,0,2.41-2.65h0a3.05,3.05,0,0,1,1.21-1.33C199.18,30.84,207.93,24.16,199,8.91Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-163 5)",
    },
         //CrownGold 20
         {
          id: "43",
          code: "st43",
          path: "M175.33,89c17.93-10.21,30.08-26.56,30.21-26.73l.57,1.29c-.13.18-11.77,15.87-29.69,26.06",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-169 3) ",
        },
        {
          id: "44",
          code: "st43",
          path: "M187.1,90.34c10.41-6.3,18.12-17.6,18.29-17.73l.28,1.07c-.57.64-7.89,11-17.49,17",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-170 5) ",
        },
        //CrownZirconia 20
        {
          id: "45",   code: "st26",
           path :"M201.33,87.85l.84-5.22a17,17,0,0,1,1.1-3.9l1.2-3a17.73,17.73,0,0,0,1.2-6.4v-4.7a7.43,7.43,0,0,0-2.7-5.7l-10.5-8.5a2.94,2.94,0,0,0-3-.4l-2.6,1.2a2,2,0,0,1-1.1.3c-3.49.5-23,4.69-10.17,36.32Z" ,style: { cursor: "pointer" },position: "",
            transforms:"translate(-170 5) ",},
         {
          id: "46",   code: "st26",
           path :"M175.63,88h0a3.21,3.21,0,0,0,1.81,1.57c0,.72.07,2.5,1,2.82a31.88,31.88,0,0,0,6.16,1.51v27.88a1,1,0,0,0,.06.35l1.74,5.45c.4,1.74,1.21,2.81,2.14,2.81s1.75-1.07,2.14-2.81l1.75-5.45a1.34,1.34,0,0,0,.05-.35V94c3.43-.33,6.09-1.09,6.75-2a3.75,3.75,0,0,0,.37-2.28,3.27,3.27,0,0,0,1.83-1.83m-10,33.73-1.71,5.34a.3.3,0,0,0,0,.1c-.24,1.07-.69,1.77-1.15,1.77s-.9-.7-1.14-1.77l0-.1-1.58-5,5.63-2.59Zm0-3.86-5.75,2.64v-1.63l5.75-2.65Zm0-3.3-5.75,2.65v-1.64l5.75-2.64Zm0-3.29-5.75,2.64v-1.63l5.75-2.64Zm0-3.29-5.75,2.64v-1.63l5.75-2.65Zm0-3.3-5.75,2.65v-1.64l5.75-2.64Zm0-3.29-5.75,2.64v-1.63l5.75-2.65Zm0-3.3-5.75,2.65V99l5.75-2.64Zm0-3.29-5.75,2.64V94a25.49,25.49,0,0,0,2.82.16h.2q1.41,0,2.73-.09Zm7.07-3.85c-.37.53-3.77,1.79-10,1.77a30.89,30.89,0,0,1-9.79-1.74,2.2,2.2,0,0,1-.17-.85,38.65,38.65,0,0,0,10.32,1.52c.08,0,6,0,9.71-1.37A1.54,1.54,0,0,1,198.47,90.81Z" ,style: { cursor: "pointer" },position: "",
           transforms:"translate(-170 5) ",
          },
        {
          id: "47",
          code: "st26",
          path: "M199,8.91a13.25,13.25,0,0,0-4.59-4.67c-4.62-2.75-13.85-6.38-21,2.69,0,.16-14.87,12-.08,26,.27,0,7.51,5.62,9.71,7.33a2.49,2.49,0,0,0,1.43.53h.25a2.57,2.57,0,0,0,1.43-.37l6.74-4a6.12,6.12,0,0,0,2.41-2.65h0a3.05,3.05,0,0,1,1.21-1.33C199.18,30.84,207.93,24.16,199,8.91Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-163 5)",
        },
        //Denture 20
        {
          id: "48",
          code: "st19",
          path: "M195.9,91.33l1.4-8.7a17,17,0,0,1,1.1-3.9l1.2-3a17.73,17.73,0,0,0,1.2-6.4v-4.7a7.43,7.43,0,0,0-2.7-5.7l-10.5-8.5a2.94,2.94,0,0,0-3-.4l-2.6,1.2a2,2,0,0,1-1.1.3c-3.5.5-23.1,4.7-10.1,36.5a5.25,5.25,0,0,0,1.1,1.6",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-163 3.5)",

        },
        {
          id: "49",
          code: "st19",
          path: "M199,8.91a13.25,13.25,0,0,0-4.59-4.67c-4.62-2.75-13.85-6.38-21,2.69,0,.16-14.87,12-.08,26,.27,0,7.51,5.62,9.71,7.33a2.49,2.49,0,0,0,1.43.53h.25a2.57,2.57,0,0,0,1.43-.37l6.74-4a6.12,6.12,0,0,0,2.41-2.65h0a3.05,3.05,0,0,1,1.21-1.33C199.18,30.84,207.93,24.16,199,8.91Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-163 5)",
        },
        {
          id: "50",
          code: "st50",
          polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-1 6)",
        },
         //Bridge 20
         {
          id: "51",
          code: "st0",
          rect: {
            x: "0.5",
            y: "66.57",
            width: "62.4318",
            height: "2.8"
          },
          style: { cursor: "pointer" },position: "",
           transforms:"translate(0 12)",
        },
        {
          id: "52",
          code: "st0",
          path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-5 -8)",
        },
        //Implant 20
        {
          id: "54",
          code: "st9",
          path: "M18.88,134.9l6.48,2.74-.54,5a13.31,13.31,0,0,1-1.13,4.22c-.83,1.71-2.08,2.95-3.39-.85a20,20,0,0,1-.95-4.88Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-3.77 -4.28)"
        },

        {
          id: "55",
          code: "st9",
          polygon: "14.75 125.86 22.02 129.15 22.68 122.58 14.22 118.92 14.75 125.86",
          style: { cursor: "pointer" },position: "",
        },
        {
          id: "56",
          code: "st9",
          polygon: "13.93 115.05 23.06 118.83 23.68 112.66 13.44 108.57 13.93 115.05",
          style: { cursor: "pointer" },position: "",
        },
        {
          id: "57",
          code: "st9",
          polygon: "13.13 104.42 24.09 108.57 24.68 102.62 12.62 97.67 13.13 104.42",
          style: { cursor: "pointer" },position: "",
        },
        {
          id: "58",
          code: "st9",
          path: "M16.11,96.82l12.71,5.55,0-7.19a6.34,6.34,0,0,0-.22-1.7,12.31,12.31,0,0,0-2.84-5.15,3.61,3.61,0,0,0-5-.47,13.28,13.28,0,0,0-3.7,4.24,7.87,7.87,0,0,0-.9,3.52C16.14,96.25,16.11,96.82,16.11,96.82Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-3.77 -4.28)"
        },
        {
          id: "59",
          code: "st10",
          path: "M12,94.64c3.4,1,10.8,2.3,22.1.6l1.4-8.7a17.39,17.39,0,0,1,1.1-3.9l1.2-3a17.71,17.71,0,0,0,1.2-6.4v-4.7a7.41,7.41,0,0,0-2.7-5.7l-10.5-8.5a2.94,2.94,0,0,0-3-.4l-2.6,1.2a2,2,0,0,1-1.1.3c-3.5.5-23.1,4.7-10.1,36.5A4.51,4.51,0,0,0,12,94.64Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-3.77 -4.28)"
        },
        {
          id: "60",
          code: "st10",
          path: "M35.69,12.76s.71-1.07-.28-2.89c0,0-12.52-11.81-23.32.57a3.39,3.39,0,0,1-2.61,1.15h0s-12.61,13.28,1.42,25a.86.86,0,0,0,1,0h0a.81.81,0,0,1,1.27.38c.45,1.44,2.09,4,7.63,7.41a2.2,2.2,0,0,0,1.2.31c.26,0,.56,0,.8,0a1.75,1.75,0,0,0,.93-.19c1.78-.9,8.13-4.26,8.74-6.79A1.21,1.21,0,0,1,34,36.77a3.8,3.8,0,0,1,.76.32s12-7.06,1.8-23.85Z",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-3.77 -4.28)"
        },
        //Bone 20
        {

          id: "61",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 -7) ",
        },
        {

          id: "62",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 1) ",
        },
        {

          id: "63",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 9) ",
        },
        {

          id: "64",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 15) ",
        },
        {

          id: "65",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 25) ",
        },
        {

          id: "66",  code: "st21",
          rect: {
            x: "2",
            y: "99.6",
            width: "48",
            height: "1.5"
          },

          style: { cursor: "pointer" },position: "",
        transforms:"translate(0 33) ",
        },
        {
          id: "67",
          code: "st67",
          path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
          style: { cursor: "pointer" },position: "",
             transforms:"scale(0.5),translate(-5 82)"
        },
          //Resection 20
          {
            id: "68",
            code: "st22",
            rect: {
              x: "25.54",
              y: "16.61",
              width: "3.93",
              height: "40.43"
            },
            style: { cursor: "pointer" },position: "",
            transforms:"translate(-26 124)  rotate(-65.8)",
          },
          //TeethCrown 20
          {
            id: "69",
            code: "st67",
            path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
            style: { cursor: "pointer" },position: "",
             transforms:"scale(0.6),translate(-15 90)"
          },
    ],
  },
 //Tooth  84: 'Lower Right Primary First Molar',
  {
    svg_id: "28",
     tooth_number: 84,
      tooth_name: TOOTH_NUMBERS[84],
      tooth_type: "First Molar",
      quadrant: "Lower Right",
      tooth_position: 6,
      is_permanent: true,
    width:"54.75px",
    position:"0 0 46.4 172",
    paths: [
      {
        id: "1",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M14.9,92.5l-6.1-13c-0.6-1.2-0.9-2.5-0.9-3.8v-3.2c0-1.8,0.6-3.5,1.6-5c2.3-3.2,7-8.7,12.2-10.1  c0.4-0.1,0.7-0.3,1.1-0.5c0.9-0.6,2.1-0.8,3.2-0.5c0.9,0.2,1.7,0.8,2.2,1.7c3.2,4.8,7.2,9.1,11.8,12.5c2.6,1.9,4,5,3.7,8.1  c-0.4,4.1-1.8,8.1-4.1,11.6c-1.1,1.6-2.7,2.7-4.6,3.2c-3.7,1-10.8,2.4-18.1,0.6C16.1,93.9,15.3,93.3,14.9,92.5z",
      },
      {
        id: "2",
        code: "st1",
        style: { cursor: "pointer" },position: "",
        path: "M36.6,94.5c-1.2,8.9-7.4,50.1-18.4,54.1c0,0-5.1-0.4-3.1-10.4c0,0,3.3-15.1-0.1-27.3c-0.3-1.1-0.4-2.1-0.4-3.2  v-12c0-1.2,0.9-2.1,2.1-2.1c0.2,0,0.3,0,0.5,0.1c5.9,1.4,12.1,1.3,18.1-0.2c0.6-0.2,1.2,0.2,1.4,0.8C36.6,94.2,36.7,94.4,36.6,94.5z  ",
      },
      {
        id: "3",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "M22,135.9c2.7-8.7,8.4-27.9,7.8-32.9c0,0-0.9-3.9-5.2-2.5c-1,0.3-1.7,1.3-1.8,2.4l-1.8,32.8  c0,0.3,0.3,0.5,0.6,0.5C21.7,136.2,21.9,136.1,22,135.9z",
      },
      {
        id: "4",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "5",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },{
        id: "6",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "7",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
       {
        id: "8",
        code: "st4",
        style: { cursor: "pointer" },position: "",
        path: "M29.9,19.4c-2.6-0.3-4.8-2.2-8.3,0.2c-0.6,0.4-0.9,1.1-0.7,1.8c0.6,2.5,0.3,5.2-0.7,7.6c-0.2,0.4,0,0.9,0.5,1.1  c0.2,0.1,0.4,0.1,0.6,0c2.2-0.8,5.7-1.5,8.9-0.3c0.5,0.2,1,0,1.2-0.5c0.1-0.2,0.1-0.4,0-0.6c-0.7-2.1-1.4-5.2-0.5-7.8  c0.2-0.6-0.1-1.2-0.7-1.5C30.1,19.4,30,19.4,29.9,19.4z",
      },
      {
        id: "9",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M18.5,18.2l-5.4-5.8c-0.8-0.9-0.8-2.3,0.1-3.1c0.1,0,0.1-0.1,0.2-0.1c3.9-3.1,13.9-8.7,25.4,1.5  c0.7,0.6,0.8,1.7,0.1,2.4c0,0,0,0-0.1,0.1l-5.6,5.6c-0.6,0.6-1.4,0.8-2.2,0.6c-2.2-0.5-6.7-1.4-9.7-0.4  C20.2,19.2,19.2,18.9,18.5,18.2z",
      },
      {
        id: "10",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M35.5,35.6l-3-3c-0.9-0.8-1.5-1.9-1.9-3c-1.1-3-1.1-6.3,0.2-9.2c0.4-0.8,0.9-1.5,1.5-2l6-5.7  c0.7-0.7,1.8-0.6,2.5,0.1c0.1,0.1,0.2,0.2,0.3,0.4c2.4,4.4,7,15.1-1.1,22.7C38.7,36.9,36.7,36.8,35.5,35.6z",
      },

      {
        id: "11",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M31.5,31.3c-2-1.2-7.6-3.9-12.4,0.3c-0.1,0.1-0.3,0.3-0.4,0.4l-2.2,2.5c-1.3,1.5-1.4,3.7-0.2,5.2  c3.1,4.1,9.8,10.3,18.6,1.4c1.5-1.5,1.6-3.8,0.4-5.5l-2.6-3.4C32.3,31.9,31.9,31.6,31.5,31.3z",
      },
      {
        id: "12",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M18.9,18.9l-6.2-7.2c-0.5-0.6-1.5-0.7-2.1-0.2c-0.1,0.1-0.2,0.2-0.3,0.3c-2.9,4.3-9.1,16.4,3.8,24.7  c0.7,0.5,1.6,0.2,2.1-0.5c0,0,0,0,0,0l2.4-4c0.5-0.9,0.9-1.9,1.1-3l0.5-3.3c0.2-1.3,0.1-2.6-0.2-3.9l-0.4-1.4  C19.6,19.8,19.3,19.3,18.9,18.9z",
      },
      {
        id: "13",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "14",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "15",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "16",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "17",
        code: "st24",
        path: "M14.9,92.5l-6.1-13c-0.6-1.2-0.9-2.5-0.9-3.8v-3.2c0-1.8,0.6-3.5,1.6-5c2.3-3.2,7-8.7,12.2-10.1  c0.4-0.1,0.7-0.3,1.1-0.5c0.9-0.6,2.1-0.8,3.2-0.5c0.9,0.2,1.7,0.8,2.2,1.7c3.2,4.8,7.2,9.1,11.8,12.5c2.6,1.9,4,5,3.7,8.1  c-0.4,4.1-1.8,8.1-4.1,11.6c-1.1,1.6-2.7,2.7-4.6,3.2c-3.7,1-10.8,2.4-18.1,0.6C16.1,93.9,15.3,93.3,14.9,92.5z",
        style: { cursor: "pointer" },
        position: "0 0",
        transforms:"scale(0.8),translate(6 20)",
      },
       // Onlay
       {
        id: "38",
        code: "st19",
        path: "M246.26,9.19s-10.45-16-27.19-1.45c.36,0-15.33,14.07,2.45,26.22.17-.22,4.8,9.36,11.32,8.72,0,0,9.14-1.12,10.19-7.74a1.91,1.91,0,0,1,1.65-1.66l.68-.07a3.84,3.84,0,0,0,2.55-1.32C250.45,28.94,255.5,20.74,246.26,9.19Z",
        style: { cursor: "pointer" },position: "",
         transforms:"translate(-207 4)"
      },
      {
        id: "39",
        code: "st20",
        path: "M247,66.63a50.27,50.27,0,0,1-11.8-12.5,3.35,3.35,0,0,0-2.2-1.7,4.55,4.55,0,0,0-.81-.13,8.55,8.55,0,0,0-2.38.62h0a5.18,5.18,0,0,1-1.1.5l-.41.13c-1.32.59-2.63,1.26-3.57,1.72a33.63,33.63,0,0,0-8.22,8.25,9.09,9.09,0,0,0-1.47,3.6v3.29a1.39,1.39,0,0,0,1.53,1.2h4.15a9,9,0,0,0,3.47-.67l4.76-2a7.11,7.11,0,0,1,2.4-.51l3.19-.1a11.17,11.17,0,0,1,4.91.93,8.3,8.3,0,0,0,3.29.7l5.1.06a1.55,1.55,0,0,0,1.57-.8A9,9,0,0,0,247,66.63Z",
        style: { cursor: "pointer" },position: "",
         transforms:"translate(-207 4)"
      },
      {
        id: "18",
        code: "st180",
        path: "M14.9,92.5l-6.1-13c-0.6-1.2-0.9-2.5-0.9-3.8v-3.2c0-1.8,0.6-3.5,1.6-5c2.3-3.2,7-8.7,12.2-10.1  c0.4-0.1,0.7-0.3,1.1-0.5c0.9-0.6,2.1-0.8,3.2-0.5c0.9,0.2,1.7,0.8,2.2,1.7c3.2,4.8,7.2,9.1,11.8,12.5c2.6,1.9,4,5,3.7,8.1  c-0.4,4.1-1.8,8.1-4.1,11.6c-1.1,1.6-2.7,2.7-4.6,3.2c-3.7,1-10.8,2.4-18.1,0.6C16.1,93.9,15.3,93.3,14.9,92.5z",
        style: { cursor: "pointer" },position: "",
        transforms:"scale(0.95),translate(1 4)",
      },
      {
        id: "20",
        code: "st25",
        path: "M14.9,92.5l-6.1-13c-0.6-1.2-0.9-2.5-0.9-3.8v-3.2c0-1.8,0.6-3.5,1.6-5c2.3-3.2,7-8.7,12.2-10.1  c0.4-0.1,0.7-0.3,1.1-0.5c0.9-0.6,2.1-0.8,3.2-0.5c0.9,0.2,1.7,0.8,2.2,1.7c3.2,4.8,7.2,9.1,11.8,12.5c2.6,1.9,4,5,3.7,8.1  c-0.4,4.1-1.8,8.1-4.1,11.6c-1.1,1.6-2.7,2.7-4.6,3.2c-3.7,1-10.8,2.4-18.1,0.6C16.1,93.9,15.3,93.3,14.9,92.5z",
        style: { cursor: "pointer" },
        position: "0 0",
        transforms:"scale(0.8),translate(6 20)",
      },
      {
         id: "19",
        code: "st16",
        path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(1.5 -2)",
      },
         // --------------------


         {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-1.5 -4)",style: { cursor: "pointer" },position: "",},
         {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-1.5 -4)",style: { cursor: "pointer" },position: "",},
         {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-1.5 -4)",style: { cursor: "pointer" },position: "",},

          // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(2.5 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(2.5 -2) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-17 1) ",
  },
  //RestoratinGlassIonomer
  {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
     //RootTemporary
     {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-14 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-14 38) ",
    },
    //RootCalcium
    {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 6) ",
    },
    // GuttaPercha
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M12.35,131.73a100.31,100.31,0,0,1,0-10.69c.21-3.57.59-7.1,1.14-10.57a143.08,143.08,0,0,1,4.64-20A158.78,158.78,0,0,1,33.33,56a.58.58,0,0,1,1.08,0,1.85,1.85,0,0,1,0,1.77,154.36,154.36,0,0,0-8.27,16A165.25,165.25,0,0,0,19.4,91.58,140,140,0,0,0,14.78,111q-.79,5-1.17,10.22c-.21,3.45-.33,7-.39,10.43h0c0,.4-.21.71-.45.69s-.4-.28-.42-.64Z",
      transforms:"translate(7 8) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M33.69,52.81,37.9,45c.7-1.28,1.89-1.41,2.68-.28h0a4.57,4.57,0,0,1,.17,4.37L36.54,56.9c-.69,1.28-1.89,1.41-2.68.28h0A4.6,4.6,0,0,1,33.69,52.81Z",
      transforms:"translate(4 12) ",
    },
    //PostCare
{
  id: "36",
  code: "st17",
  path: "M239.8,91.81a28.15,28.15,0,0,0-.62-3.87L238,82.84c-.17-.78-.58-1.28-1-1.28h-5.66c-.32,0-.61.29-.79.79a41.05,41.05,0,0,0-2.2,13.35l1.54,28.68a27.55,27.55,0,0,1-.25,5.29c-.22,1.51-1.19,4.28-.41,4.94a1.25,1.25,0,0,0,1.18.15,2.9,2.9,0,0,0,1.91-1.84C234.74,126.31,241.11,105.8,239.8,91.81Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-209 0)",
},
 //Veneer
 {
  id: "37",
  code: "st19",
  path: "M223,89.74c-.89-.4-.6-.51-1.1-1.21l-6.1-13a8.5,8.5,0,0,1-.9-3.8v-3.2a9,9,0,0,1,1.6-5c2.3-3.2,7-8.7,12.2-10.1a5.18,5.18,0,0,0,1.1-.5,4,4,0,0,1,3.2-.5,3.35,3.35,0,0,1,2.2,1.7A50.27,50.27,0,0,0,247,66.63a9,9,0,0,1,3.7,8.1,25.7,25.7,0,0,1-4.1,11.6c-1.1,1.59-1.33,1.75-3.34,2.76",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-207 5)",
},
 //CrownPermanent 21
 {
  id: "40",
  code: "st19",
  path: "M223,89.74c-.89-.4-.6-.51-1.1-1.21l-6.1-13a8.5,8.5,0,0,1-.9-3.8v-3.2a9,9,0,0,1,1.6-5c2.3-3.2,7-8.7,12.2-10.1a5.18,5.18,0,0,0,1.1-.5,4,4,0,0,1,3.2-.5,3.35,3.35,0,0,1,2.2,1.7A50.27,50.27,0,0,0,247,66.63a9,9,0,0,1,3.7,8.1,25.7,25.7,0,0,1-4.1,11.6c-1.1,1.59-1.33,1.75-3.34,2.76",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-208 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M246.26,9.19s-10.45-16-27.19-1.45c.36,0-15.33,14.07,2.45,26.22.17-.22,4.8,9.36,11.32,8.72,0,0,9.14-1.12,10.19-7.74a1.91,1.91,0,0,1,1.65-1.66l.68-.07a3.84,3.84,0,0,0,2.55-1.32C250.45,28.94,255.5,20.74,246.26,9.19Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-208 5)",
},
   //CrownGold 21
   {
    id: "43",
    code: "st43",
    path: "M223.9,83.55a133.49,133.49,0,0,0,22.68-22.11l1.08,1a128.73,128.73,0,0,1-23.18,22.39",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-212 3.5) ",
  },
  {
    id: "44",
    code: "st43",
    path: "M229,89.15c13.45-8.58,22.14-23.3,22.36-23.48l1,.68c-.74.86-9.48,15-21.88,23.19",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-212 3.5) ",
  },
  //CrownZirconia 21
  {
    id: "45",   code: "st26",
     path :"M250.09,87.85a7.69,7.69,0,0,0,1.38-1.52,25.7,25.7,0,0,0,4.1-11.6,9,9,0,0,0-3.7-8.1,50.27,50.27,0,0,1-11.8-12.5,3.35,3.35,0,0,0-2.2-1.7,4,4,0,0,0-3.2.5,5.18,5.18,0,0,1-1.1.5c-5.2,1.4-9.9,6.9-12.2,10.1a9,9,0,0,0-1.6,5v3.2a8.5,8.5,0,0,0,.9,3.8l5.78,12.32Z" ,style: { cursor: "pointer" },position: "",
      transforms:"translate(-214 5) ",},
   {
    id: "46",   code: "st26",
     path :"M226.74,88.12h0a3.09,3.09,0,0,0,1.67,1.58c0,.71.07,2.49.92,2.82A28.22,28.22,0,0,0,235,94v27.89a1.27,1.27,0,0,0,0,.34l1.62,5.46c.36,1.73,1.12,2.81,2,2.81s1.61-1.08,2-2.81l1.61-5.46a1,1,0,0,0,.05-.34V94.13c3.18-.33,5.64-1.09,6.25-2a4,4,0,0,0,.34-2.27A3.21,3.21,0,0,0,250.59,88m-9.26,33.73-1.58,5.34s0,.06,0,.09c-.22,1.08-.64,1.77-1.06,1.77s-.84-.69-1.06-1.77c0,0,0-.06,0-.09l-1.47-5,5.21-2.59Zm0-3.87L236,120.51v-1.64l5.32-2.64Zm0-3.29L236,117.21v-1.63l5.32-2.65Zm0-3.3L236,113.92v-1.64l5.32-2.64Zm0-3.29L236,110.62V109l5.32-2.65Zm0-3.3L236,107.33V105.7l5.32-2.65Zm0-3.29L236,104V102.4l5.32-2.64Zm0-3.29L236,100.74V99.11l5.32-2.65Zm0-3.3L236,97.45v-3.3c.83.09,1.71.15,2.61.16h.19q1.31,0,2.52-.09ZM247.88,91c-.35.53-3.49,1.79-9.25,1.76A26.55,26.55,0,0,1,229.56,91a2.29,2.29,0,0,1-.15-.85A33,33,0,0,0,239,91.66c.07,0,5.55,0,9-1.38A1.84,1.84,0,0,1,247.88,91Z" ,style: { cursor: "pointer" },position: "",
     transforms:"translate(-214 5) ",
    },
  {
    id: "47",
    code: "st26",
    path: "M246.26,9.19s-10.45-16-27.19-1.45c.36,0-15.33,14.07,2.45,26.22.17-.22,4.8,9.36,11.32,8.72,0,0,9.14-1.12,10.19-7.74a1.91,1.91,0,0,1,1.65-1.66l.68-.07a3.84,3.84,0,0,0,2.55-1.32C250.45,28.94,255.5,20.74,246.26,9.19Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-208 5)",
  },
  //Denture 21
  {
    id: "48",
    code: "st19",
    path: "M223,89.74c-.89-.4-.6-.51-1.1-1.21l-6.1-13a8.5,8.5,0,0,1-.9-3.8v-3.2a9,9,0,0,1,1.6-5c2.3-3.2,7-8.7,12.2-10.1a5.18,5.18,0,0,0,1.1-.5,4,4,0,0,1,3.2-.5,3.35,3.35,0,0,1,2.2,1.7A50.27,50.27,0,0,0,247,66.63a9,9,0,0,1,3.7,8.1,25.7,25.7,0,0,1-4.1,11.6c-1.1,1.59-1.33,1.75-3.34,2.76",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-208 3.5)",

  },
  {
    id: "49",
    code: "st19",
    path: "M246.26,9.19s-10.45-16-27.19-1.45c.36,0-15.33,14.07,2.45,26.22.17-.22,4.8,9.36,11.32,8.72,0,0,9.14-1.12,10.19-7.74a1.91,1.91,0,0,1,1.65-1.66l.68-.07a3.84,3.84,0,0,0,2.55-1.32C250.45,28.94,255.5,20.74,246.26,9.19Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-208 5)",
  },
  {
    id: "50",
    code: "st50",
    polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(1 6)",
  },
  //Bridge 21
  {
    id: "51",
    code: "st0",
    rect: {
      x: "0.5",
      y: "66.57",
      width: "62.4318",
      height: "2.8"
    },
    style: { cursor: "pointer" },position: "",
     transforms:"translate(0 12)",
  },
  {
    id: "52",
    code: "st0",
    path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(2 -8)",
  },
   //Implant 21
   {
    id: "54",
    code: "st9",
    path: "M18.58,132.33l6.48,2.73-.55,5a13.31,13.31,0,0,1-1.13,4.22c-.82,1.71-2.08,2.94-3.38-.85a19.11,19.11,0,0,1-.95-4.89Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-2.68 -4.07)"
  },

  {
    id: "55",
    code: "st9",
    polygon: "15.54 123.51 22.81 126.8 23.48 120.23 15.02 116.56 15.54 123.51",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "56",
    code: "st9",
    polygon: "14.72 112.69 23.86 116.48 24.47 110.31 14.23 106.21 14.72 112.69",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "57",
    code: "st9",
    polygon: "13.92 102.07 24.88 106.21 25.48 100.27 13.41 95.31 13.92 102.07",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "58",
    code: "st9",
    path: "M15.8,94.25,28.52,99.8l0-7.19a6.71,6.71,0,0,0-.23-1.7,12.09,12.09,0,0,0-2.84-5.15,3.61,3.61,0,0,0-5-.47,13.28,13.28,0,0,0-3.69,4.24,7.83,7.83,0,0,0-.9,3.52C15.83,93.68,15.8,94.25,15.8,94.25Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-2.68 -4.07)"
  },
  {
    id: "59",
    code: "st10",
    path: "M12,92.5l-6.1-13A8.49,8.49,0,0,1,5,75.7V72.5a9,9,0,0,1,1.6-5c2.3-3.2,7-8.7,12.2-10.1a5.36,5.36,0,0,0,1.1-.5,4,4,0,0,1,3.2-.5,3.37,3.37,0,0,1,2.2,1.7,50.14,50.14,0,0,0,11.8,12.5,9,9,0,0,1,3.7,8.1,25.71,25.71,0,0,1-4.1,11.6,8,8,0,0,1-4.6,3.2c-3.7,1-10.8,2.4-18.1.6A3.18,3.18,0,0,1,12,92.5Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-2.68 -4.07)"
  },
  {
    id: "60",
    code: "st10",
    path: "M33.56,8.93C29.1,5.35,19.87.5,10.05,10.6a2.13,2.13,0,0,1-1.44.64h0a1,1,0,0,0-.72.36C6,13.92-3.68,27.42,11.81,36.74c0,0,.74.75.74.75s8,16.17,19.44,2.94a3.46,3.46,0,0,0,.72-3.06v-.06a.91.91,0,0,1,1.28-1,3.9,3.9,0,0,0,.65.28c.23,0,13.81-5.25,3.29-22.57A19.21,19.21,0,0,0,33.56,8.93Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-2.68 -4.07)"
  },
  //Bone 21
  {

    id: "61",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 -7) ",
  },
  {

    id: "62",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 1) ",
  },
  {

    id: "63",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 9) ",
  },
  {

    id: "64",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 15) ",
  },
  {

    id: "65",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 25) ",
  },
  {

    id: "66",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
  transforms:"translate(0 33) ",
  },
  {
    id: "67",
    code: "st67",
    path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
    style: { cursor: "pointer" },position: "",
        transforms:"scale(0.5),translate(-5 82)"
  },
    //Resection 21
    {
      id: "68",
      code: "st22",
      rect: {
        x: "25.54",
        y: "16.61",
        width: "3.93",
        height: "40.43"
      },
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-22 124)  rotate(-65.8)",
    },
    //TeethCrown 21
    {
      id: "69",
      code: "st67",
      path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
      style: { cursor: "pointer" },position: "",
       transforms:"scale(0.6),translate(-7 90)"
    },
    ],
  },
  //Tooth 83: 'Lower Right Primary Canine',
  {
    svg_id: "27",
     tooth_number: 83,
      tooth_name: TOOTH_NUMBERS[83],
      tooth_type: " Canine",
      quadrant: "Lower Right ",
      tooth_position: 7,
      is_permanent: true,
    width:"52.425px",
    position:"0 0 44.4 172",
    paths: [
      {
        id: "1",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M27.9,94.8L35,73.9c0,0,5.8-18.3-14.3-17.3c-1.4,0.1-2.7,0.5-3.8,1.3l-9.7,7c-1.6,1.2-2.6,3-2.6,5  c0,4,1.4,11.8,8.6,24.8C13.2,94.8,17.7,96.1,27.9,94.8z",
      },

       {
        id: "2",
        code: "st1",
        style: { cursor: "pointer" },position: "",
        path: "M28.8,96.2c0.1,8.5-0.1,43.1-7.7,51.1c-1,1.2-2.7,1.5-4.1,0.8c-1-0.5-2-1.6-1.6-4c0.1-0.7,0.1-1.3,0.1-2  c-0.2-4.3-0.5-19.3-1.7-25.6c-0.2-1-0.3-2-0.4-3.1l-0.9-16.8c-0.1-1,0.7-1.9,1.7-1.9c0.1,0,0.2,0,0.4,0c4.1,0.5,8.3,0.5,12.4-0.1  c0.9-0.1,1.7,0.5,1.9,1.4C28.8,96.1,28.8,96.2,28.8,96.2z",
      },
      {
        id: "3",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "M20.5,140.5c2.2-6.8,3.3-13.9,3.1-21.1c0-1,0-2,0-3l0.9-14.7c0.1-0.8-0.6-1.6-1.4-1.6c0,0-0.1,0-0.1,0h-2.9  c-1,0-1.7,0.8-1.7,1.7c0,0,0,0,0,0v12.8c0,0.8,0,1.6,0.1,2.4c0.3,4.1,0.1,17.2,0.5,23.2c0,0.4,0.3,0.8,0.8,0.8  C20.1,141.1,20.4,140.9,20.5,140.5z",
      },
      {
        id: "4",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "5",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },{
        id: "6",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "7",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },

       {
        id: "8",
        code: "st4",
        style: { cursor: "pointer" },position: "",
        path: "M19.5,26.3l-7.4,15.9c-0.3,0.6,0,1.4,0.6,1.7c0.1,0,0.2,0.1,0.3,0.1c4.7,1.1,9.6,1.1,14.4,0.1  c0.8-0.2,1.3-1.1,1.1-1.9c0-0.1-0.1-0.2-0.1-0.3l-7.6-15.6c-0.2-0.3-0.6-0.5-1-0.3C19.7,26,19.6,26.1,19.5,26.3z",
      },
      {
        id: "9",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M20.7,24c2-2.2,6.9-6.8,8.8-8.6c0.5-0.4,0.6-1.1,0.3-1.6c-2-3.9-10.2-17.4-19.5,0.1C10,14.3,10,15,10.5,15.4  l8.2,8.6C19.2,24.5,20.1,24.6,20.7,24C20.7,24,20.7,24,20.7,24z",
      },
      {
        id: "10",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M20.8,25.8l7.3,16.9c0.4,0.9,1.4,1.4,2.4,1.1c2.4-0.7,6.3-2.5,6.5-7.7c0,0-0.4-13.6-5.7-19.6  c-0.7-0.8-1.8-0.8-2.6-0.2c0,0,0,0,0,0l-7.2,6.6C20.6,23.8,20.4,24.9,20.8,25.8z",
      },
      {
        id: "11",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M19.3,26.2l-6.5,14c-0.3,0.6,0,1.2,0.6,1.5c0.1,0,0.1,0.1,0.2,0.1c4.2,1,8.5,1,12.6,0.1c0.7-0.2,1.2-1,1-1.7  c0-0.1-0.1-0.2-0.1-0.3l-6.7-13.7c-0.2-0.3-0.5-0.4-0.8-0.3C19.5,26,19.4,26.1,19.3,26.2z",
      },
      {
        id: "12",
        code: "st0",
        style: { cursor: "pointer" },position: "",
        path: "M19.4,24l-8.1-7.6c-0.6-0.6-1.6-0.5-2.1,0.1c-0.1,0.1-0.2,0.2-0.2,0.3c-1.7,3.2-5,10.5-4.8,18.3  c0.1,1.6,0.6,3.2,1.5,4.5c1,1.5,2.3,2.8,3.9,3.7c1.3,0.6,2.9,0.1,3.5-1.3c0,0,0-0.1,0.1-0.1l6.7-15.9C20.1,25.4,20,24.6,19.4,24z",
      },
      {
        id: "13",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "14",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "15",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "16",
        code: "st3",
        style: { cursor: "pointer" },position: "",
        path: "",
      },
      {
        id: "17",
        code: "st24",
        path: "M27.9,94.8L35,73.9c0,0,5.8-18.3-14.3-17.3c-1.4,0.1-2.7,0.5-3.8,1.3l-9.7,7c-1.6,1.2-2.6,3-2.6,5  c0,4,1.4,11.8,8.6,24.8C13.2,94.8,17.7,96.1,27.9,94.8z",
        style: { cursor: "pointer" },
        position: "0 0",
        transforms:"scale(0.8),translate(5.25 19.5)",
      },
       // Onlay
       {
        id: "38",
        code: "st19",
        path: "M286,10.19c-1.26-2.79-9.26-18.72-19.61.09,0,.17-9.16,16.6-4.63,25.08.16.27,3.44,5.3,6.54,4.23a33,33,0,0,0,15,.31,4.58,4.58,0,0,1,1.84-.1,6.42,6.42,0,0,0,6.39-2.86s6.49-5-5.06-25.89C286.26,10.77,286.11,10.48,286,10.19Z",
        style: { cursor: "pointer" },position: "",
         transforms:"translate(-256 4)"
      },
      {
        id: "39",
        code: "st20",
        path: "M289.91,57.28l-3.41-2.77A18.81,18.81,0,0,0,277.06,53a7.31,7.31,0,0,0-3.8,1.3l-9.7,7a6.28,6.28,0,0,0-2.6,5c0,.34,0,.73,0,1.12a1.37,1.37,0,0,0,1,.26h4.16A9.43,9.43,0,0,0,269.6,67l4.76-2a6.57,6.57,0,0,1,2.4-.51l3.19-.1a11.08,11.08,0,0,1,4.91.93,8.41,8.41,0,0,0,3.29.7l4,0C292.28,63.38,292,60,289.91,57.28Z",
        style: { cursor: "pointer" },position: "",
         transforms:"translate(-256 4)"
      },
      {
        id: "18",
        code: "st180",
        path: "M27.9,94.8L35,73.9c0,0,5.8-18.3-14.3-17.3c-1.4,0.1-2.7,0.5-3.8,1.3l-9.7,7c-1.6,1.2-2.6,3-2.6,5  c0,4,1.4,11.8,8.6,24.8C13.2,94.8,17.7,96.1,27.9,94.8z",
        style: { cursor: "pointer" },position: "",
        transforms:"scale(0.95),translate(1 4)",
      },
      {
         id: "19",
        code: "st16",
        path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-4 0)",
      },
      {
        id: "20",
        code: "st25",
        path: "M27.9,94.8L35,73.9c0,0,5.8-18.3-14.3-17.3c-1.4,0.1-2.7,0.5-3.8,1.3l-9.7,7c-1.6,1.2-2.6,3-2.6,5  c0,4,1.4,11.8,8.6,24.8C13.2,94.8,17.7,96.1,27.9,94.8z",
        style: { cursor: "pointer" },
        position: "0 0",
        transforms:"scale(0.8),translate(5.25 19.5)",
      },
      {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-7 -5)",style: { cursor: "pointer" },position: "",},
      {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-7 -5)",style: { cursor: "pointer" },position: "",},
      {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-7 -5)",style: { cursor: "pointer" },position: "",},

       // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-3.5 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-3.5 -2) ",
  },
      //RestoratinAmalgam
     {
      id: "26",
      code: "st26",
      style: { cursor: "pointer" },position: "",
      path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
      transforms:"translate(-17 1) ",
    },
     //RestoratinGlassIonomer
  {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(-2  8) ",
  },
     //RootTemporary
     {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-19.5 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-19.5 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M12.35,131.73a100.31,100.31,0,0,1,0-10.69c.21-3.57.59-7.1,1.14-10.57a143.08,143.08,0,0,1,4.64-20A158.78,158.78,0,0,1,33.33,56a.58.58,0,0,1,1.08,0,1.85,1.85,0,0,1,0,1.77,154.36,154.36,0,0,0-8.27,16A165.25,165.25,0,0,0,19.4,91.58,140,140,0,0,0,14.78,111q-.79,5-1.17,10.22c-.21,3.45-.33,7-.39,10.43h0c0,.4-.21.71-.45.69s-.4-.28-.42-.64Z",
      transforms:"translate(6 2) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M33.69,52.81,37.9,45c.7-1.28,1.89-1.41,2.68-.28h0a4.57,4.57,0,0,1,.17,4.37L36.54,56.9c-.69,1.28-1.89,1.41-2.68.28h0A4.6,4.6,0,0,1,33.69,52.81Z",
      transforms:"translate(2.5 7) ",
    },
     //PostCare
{
  id: "36",
  code: "st17",
  path: "M284.2,93.17a43.29,43.29,0,0,0-.55-4.38l-1-5.78c-.16-.88-.53-1.45-.93-1.45h-5.06c-.28,0-.55.33-.71.89a58.2,58.2,0,0,0-2,15.14l1.38,32.49a40.4,40.4,0,0,1,.15,6.12c-.2,1.72-.43,4.38.26,5.13.29.31,2.75-1.41,2.83-1.77C280.17,132.22,285.37,109,284.2,93.17Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-258 0)",
},
 //Veneer
 {
  id: "37",
  code: "st19",
  path: "M284.26,91.17l7.1-20.9s5.8-18.3-14.3-17.3a7.31,7.31,0,0,0-3.8,1.3l-9.7,7a6.28,6.28,0,0,0-2.6,5c0,4,1.4,11.8,8.6,24.8",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-256 4)",
},
//CrownPermanent 22
{
  id: "40",
  code: "st19",
  path: "M284.26,91.17l7.1-20.9s5.8-18.3-14.3-17.3a7.31,7.31,0,0,0-3.8,1.3l-9.7,7a6.28,6.28,0,0,0-2.6,5c0,4,1.4,11.8,8.6,24.8",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-256 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M286,10.19c-1.26-2.79-9.26-18.72-19.61.09,0,.17-9.16,16.6-4.63,25.08.16.27,3.44,5.3,6.54,4.23a33,33,0,0,0,15,.31,4.58,4.58,0,0,1,1.84-.1,6.42,6.42,0,0,0,6.39-2.86s6.49-5-5.06-25.89C286.26,10.77,286.11,10.48,286,10.19Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-256 5)",
},
 //CrownGold 22
 {
  id: "43",
  code: "st43",
  path: "M269.74,82.79A131.48,131.48,0,0,0,295.31,57.1l.8,1.4A127.87,127.87,0,0,1,270.3,84.05",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-263 1.5) ",
},
{
  id: "44",
  code: "st43",
  path: "M273.19,89.72c13.45-8.58,23.93-24.53,24.16-24.72l-.14,2c-.74.86-11,15.44-23.43,23.6",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-262 1.5) ",
},
 //CrownZirconia 22
 {
  id: "45",   code: "st26",
   path :"M281.77,52.63a7.31,7.31,0,0,0-3.8,1.3l-9.7,7a6.25,6.25,0,0,0-2.6,5c0,3.69,1.21,10.64,7.06,21.92H290l6.09-17.92S301.87,51.63,281.77,52.63Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-262 5) ",},
 {
  id: "46",   code: "st26",
   path :"M272.37,88h0a2.8,2.8,0,0,0,1.23,1.57c0,.72,0,2.5.67,2.82a16.22,16.22,0,0,0,4.19,1.51v27.88a2,2,0,0,0,0,.35l1.19,5.45c.27,1.74.83,2.81,1.46,2.81s1.18-1.07,1.45-2.81l1.19-5.45a2,2,0,0,0,0-.35V94c2.34-.33,4.14-1.09,4.59-2a5.4,5.4,0,0,0,.26-2.28,3,3,0,0,0,1.24-1.83m-6.81,33.73-1.16,5.34s0,.06,0,.1c-.16,1.07-.46,1.77-.77,1.77s-.62-.7-.78-1.77c0,0,0-.07,0-.1l-1.08-5,3.83-2.59Zm0-3.86-3.91,2.64v-1.63l3.91-2.65Zm0-3.3-3.91,2.65v-1.64l3.91-2.64Zm0-3.29-3.91,2.64v-1.63l3.91-2.64Zm0-3.29-3.91,2.64v-1.63l3.91-2.65Zm0-3.3-3.91,2.65v-1.64l3.91-2.64Zm0-3.29-3.91,2.64v-1.63l3.91-2.65Zm0-3.3-3.91,2.65V99l3.91-2.64Zm0-3.29-3.91,2.64V94a11.94,11.94,0,0,0,1.92.16h.14a18.34,18.34,0,0,0,1.85-.09Zm4.81-3.85c-.25.53-2.56,1.79-6.8,1.77a15.28,15.28,0,0,1-6.66-1.74,3.54,3.54,0,0,1-.11-.85,18.56,18.56,0,0,0,7,1.52s4.08,0,6.6-1.37A2.12,2.12,0,0,1,287.9,90.81Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-262 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M286,10.19c-1.26-2.79-9.26-18.72-19.61.09,0,.17-9.16,16.6-4.63,25.08.16.27,3.44,5.3,6.54,4.23a33,33,0,0,0,15,.31,4.58,4.58,0,0,1,1.84-.1,6.42,6.42,0,0,0,6.39-2.86s6.49-5-5.06-25.89C286.26,10.77,286.11,10.48,286,10.19Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-256 5)",
},
 //Denture 22
 {
  id: "48",
  code: "st19",
  path: "M284.26,91.17l7.1-20.9s5.8-18.3-14.3-17.3a7.31,7.31,0,0,0-3.8,1.3l-9.7,7a6.28,6.28,0,0,0-2.6,5c0,4,1.4,11.8,8.6,24.8",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-256 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M286,10.19c-1.26-2.79-9.26-18.72-19.61.09,0,.17-9.16,16.6-4.63,25.08.16.27,3.44,5.3,6.54,4.23a33,33,0,0,0,15,.31,4.58,4.58,0,0,1,1.84-.1,6.42,6.42,0,0,0,6.39-2.86s6.49-5-5.06-25.89C286.26,10.77,286.11,10.48,286,10.19Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-256 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-2.5 6)",
},
 //Bridge 22
 {
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-4.5 -8)",
},
   //Implant 22
   {
    id: "54",
    code: "st9",
    path: "M19.4,133.71l6.48,2.74-.54,5a13.31,13.31,0,0,1-1.13,4.22c-.83,1.71-2.08,3-3.39-.84a20.19,20.19,0,0,1-.95-4.89Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-6.58 -4.64)"
  },

  {
    id: "55",
    code: "st9",
    polygon: "12.46 124.31 19.73 127.6 20.4 121.03 11.93 117.36 12.46 124.31",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "56",
    code: "st9",
    polygon: "11.64 113.5 20.77 117.28 21.39 111.11 11.15 107.02 11.64 113.5",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "57",
    code: "st9",
    polygon: "10.84 102.87 21.8 107.02 22.4 101.07 10.33 96.12 10.84 102.87",
    style: { cursor: "pointer" },position: "",
  },
  {
    id: "58",
    code: "st9",
    path: "M16.63,95.63l12.71,5.56,0-7.2a6.27,6.27,0,0,0-.22-1.69,12.28,12.28,0,0,0-2.84-5.16,3.62,3.62,0,0,0-5-.47,13.15,13.15,0,0,0-3.69,4.24,7.78,7.78,0,0,0-.91,3.53C16.66,95.07,16.63,95.63,16.63,95.63Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-6.58 -4.64)"
  },
  {
    id: "59",
    code: "st10",
    path: "M30.38,94.91,37.48,74s5.8-18.3-14.3-17.3a7.22,7.22,0,0,0-3.8,1.3l-9.7,7a6.25,6.25,0,0,0-2.6,5c0,4,1.4,11.8,8.6,24.8C15.68,94.91,20.18,96.21,30.38,94.91Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-6.58 -4.64)"
  },
  {
    id: "60",
    code: "st10",
    path: "M32.63,13.72c-1.86-3.62-9.84-17.1-18.58-.41a4.94,4.94,0,0,0-.47,3.16h0A6.44,6.44,0,0,0,11,18.9c-2.17,4.53-7.39,17.47.49,23.55a5.44,5.44,0,0,0,3.27,1.13,7.15,7.15,0,0,1,2.38.38c2.15.79,6.16,1.44,12.93-.12.67-.15,1.34-.29,2-.38,3.52-.49,16.63-3.83,1.63-27.83C33.32,15,33,14.37,32.63,13.72Z",
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-6.58 -4.64)"
  },
  //Bone 22
  {

    id: "61",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 -7) ",
  },
  {

    id: "62",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 1) ",
  },
  {

    id: "63",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 9) ",
  },
  {

    id: "64",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 15) ",
  },
  {

    id: "65",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
    transforms:"translate(0 25) ",
  },
  {

    id: "66",  code: "st21",
    rect: {
      x: "2",
      y: "99.6",
      width: "48",
      height: "1.5"
    },

    style: { cursor: "pointer" },position: "",
  transforms:"translate(0 33) ",
  },
  {
    id: "67",
    code: "st67",
    path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
    style: { cursor: "pointer" },position: "",
    transforms:"scale(0.5),translate(-5 82)"
  },
   //Resection 22
   {
    id: "68",
    code: "st22",
    rect: {
      x: "25.54",
      y: "16.61",
      width: "3.93",
      height: "36.43"
    },
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-22 124)  rotate(-65.8)",
  },
  //TeethCrown 22
  {
    id: "69",
    code: "st67",
    path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
    style: { cursor: "pointer" },position: "",
     transforms:"scale(0.56),translate(-11 90)"
  },
    ],
  },
  //Tooth 82: 'Lower Right Primary Lateral Incisor',
  {
      svg_id: "26",
       tooth_number: 82,
      tooth_name: TOOTH_NUMBERS[82],
      tooth_type: "Lateral Incisor",
      quadrant: "Lower Right ",
      tooth_position: 8,
      is_permanent: true,
      width:"48.35px",
      position:"0 0 40.9 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.2,93.5l5.3-16c0.4-1.4,1-2.7,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5C32.8,58.4,32,58,31.2,58  c-3.5,0-13.9-0.2-19.6-1.4c-1-0.2-2,0.4-2.3,1.3c-0.6,1.7-0.8,3.4-0.8,5.2c0,1,0.1,1.9,0.2,2.9c0.5,4.2,2.4,19.1,4.2,25.7  c0.5,1.9,2,3.4,3.9,4c3.1,1,6.5,0.9,9.5-0.3C27.2,95.1,27.9,94.4,28.2,93.5z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M27.1,94.7c0.2-0.1,0.4,0.1,0.5,0.3c0,0,0,0.1,0,0.1c0.1,5.9,0.5,49-10.1,57.3c-0.6,0.4-1.4,0.3-1.8-0.3  c-0.1-0.2-0.2-0.4-0.2-0.7l-2.7-57.6c0.3-1.8,1.4,1,2.9,1.6C17.9,96.4,21.1,96.1,27.1,94.7z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M23.4,100.1c0.2,3.7,0.7,14.6-0.8,21.6c-0.6,2.7-1,5.5-1.2,8.2c-0.1,4.3-0.7,8.6-1.6,12.9  c-0.1,0.4-0.5,0.7-0.9,0.6c-0.4-0.1-0.6-0.4-0.6-0.8v-21.1l1-21.1c0-0.9,0.8-1.6,1.7-1.6H22C22.7,98.8,23.4,99.3,23.4,100.1z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        // --------------------
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M21.7,26.8l-7,16.1c-0.1,0.2,0,0.4,0.2,0.5c0,0,0,0,0.1,0c1.8,0.4,9,2,15.6,0c0.2-0.1,0.3-0.3,0.2-0.4  c0,0,0,0,0,0l-7-16.1c-0.3-0.6-0.9-0.8-1.5-0.6C22,26.3,21.8,26.5,21.7,26.8z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.6,24.7L13,16.9c-0.6-0.5-0.8-1.4-0.5-2.1c2-4.5,9.4-18.3,18.7-0.7c0.6,1.2,0.3,2.6-0.6,3.5L22.6,24.7z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23.6,23.8c1.9-1.6,5.3-5.2,7.2-7.3c0.6-0.7,1.7-0.7,2.3-0.1c0.1,0.1,0.2,0.2,0.3,0.3c1.9,2.9,5.5,9.1,5.7,14.7  c0,0.7,0.1,1.4,0.2,2.1c0.4,2.2,0.2,7.1-7.5,9.5c-1.1,0.4-2.4-0.2-2.8-1.3l-6-16C22.8,25,23,24.2,23.6,23.8z",
        },

        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M21.9,25.5l-5.1,12.7c-0.2,0.5,0,1.1,0.4,1.4c0.1,0,0.1,0.1,0.2,0.1c3.3,0.9,6.7,0.9,10,0.1  c0.6-0.2,0.9-0.9,0.8-1.5c0-0.1,0-0.2-0.1-0.2l-5.3-12.4c-0.1-0.3-0.4-0.4-0.7-0.2C22,25.3,21.9,25.4,21.9,25.5z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.4,25.1l-9.5-8.6c-0.6-0.6-1.6-0.5-2.1,0.1c-0.1,0.1-0.2,0.3-0.3,0.4c-1.8,4.3-3.2,8.6-4.3,13.1  c0,0-2.5,10.2,6.3,12.9c1,0.3,2.1-0.2,2.5-1.1L22.4,25.1z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M28.2,93.5l5.3-16c0.4-1.4,1-2.7,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5C32.8,58.4,32,58,31.2,58  c-3.5,0-13.9-0.2-19.6-1.4c-1-0.2-2,0.4-2.3,1.3c-0.6,1.7-0.8,3.4-0.8,5.2c0,1,0.1,1.9,0.2,2.9c0.5,4.2,2.4,19.1,4.2,25.7  c0.5,1.9,2,3.4,3.9,4c3.1,1,6.5,0.9,9.5-0.3C27.2,95.1,27.9,94.4,28.2,93.5z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(5.25 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M330.54,11c-1.26-2.79-9.26-18.73-19.62.09,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.32,4.4,4.4,0,0,1,1.84-.1A6.4,6.4,0,0,0,336,37.79s6.49-5-5-25.89C330.82,11.62,330.67,11.33,330.54,11Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-300 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M335.57,59.68a12,12,0,0,0-2.77-4.9,2.73,2.73,0,0,0-2.1-.9c-3.5,0-13.9-.2-19.6-1.4a2,2,0,0,0-1.14.1l-1.13,1.14,0,.06A15.12,15.12,0,0,0,308,59a28.06,28.06,0,0,0,.2,2.9c.05.42.12,1,.2,1.6h1.8a6.77,6.77,0,0,0,3.38-.9l4.65-2.71a5,5,0,0,1,2.34-.69l3.11-.14a8.28,8.28,0,0,1,4.79,1.26,6.3,6.3,0,0,0,3.21,1l3.85.06C335.56,60.79,335.57,60.24,335.57,59.68Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-300 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M28.2,93.5l5.3-16c0.4-1.4,1-2.7,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5C32.8,58.4,32,58,31.2,58  c-3.5,0-13.9-0.2-19.6-1.4c-1-0.2-2,0.4-2.3,1.3c-0.6,1.7-0.8,3.4-0.8,5.2c0,1,0.1,1.9,0.2,2.9c0.5,4.2,2.4,19.1,4.2,25.7  c0.5,1.9,2,3.4,3.9,4c3.1,1,6.5,0.9,9.5-0.3C27.2,95.1,27.9,94.4,28.2,93.5z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },

        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-2 0)",
        },
        {
          id: "20",
          code: "st25",
          path: "M28.2,93.5l5.3-16c0.4-1.4,1-2.7,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5C32.8,58.4,32,58,31.2,58  c-3.5,0-13.9-0.2-19.6-1.4c-1-0.2-2,0.4-2.3,1.3c-0.6,1.7-0.8,3.4-0.8,5.2c0,1,0.1,1.9,0.2,2.9c0.5,4.2,2.4,19.1,4.2,25.7  c0.5,1.9,2,3.4,3.9,4c3.1,1,6.5,0.9,9.5-0.3C27.2,95.1,27.9,94.4,28.2,93.5z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(5.25 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-6 -4)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-6 -4)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-6 -4)",style: { cursor: "pointer" },position: "",},

       // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-2.5 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-2.5 -2) ",
  },
       //RestoratinAmalgam
     {
      id: "26",
      code: "st26",
      style: { cursor: "pointer" },position: "",
      path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
      transforms:"translate(-15 1) ",
    },
     //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
     //RootTemporary
     {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-19 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-19 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M12.35,131.73a100.31,100.31,0,0,1,0-10.69c.21-3.57.59-7.1,1.14-10.57a143.08,143.08,0,0,1,4.64-20A158.78,158.78,0,0,1,33.33,56a.58.58,0,0,1,1.08,0,1.85,1.85,0,0,1,0,1.77,154.36,154.36,0,0,0-8.27,16A165.25,165.25,0,0,0,19.4,91.58,140,140,0,0,0,14.78,111q-.79,5-1.17,10.22c-.21,3.45-.33,7-.39,10.43h0c0,.4-.21.71-.45.69s-.4-.28-.42-.64Z",
      transforms:"translate(6 2) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M33.69,52.81,37.9,45c.7-1.28,1.89-1.41,2.68-.28h0a4.57,4.57,0,0,1,.17,4.37L36.54,56.9c-.69,1.28-1.89,1.41-2.68.28h0A4.6,4.6,0,0,1,33.69,52.81Z",
      transforms:"translate(0 12) ",
    },
         //PostCare
{
  id: "36",
  code: "st17",
  path: "M326.27,93.93c-.11-1.6-.29-3.17-.52-4.67l-1-6.15c-.15-.94-.5-1.55-.88-1.55h-4.78c-.26,0-.51.35-.66,1a69.27,69.27,0,0,0-1.86,16.12l1.3,34.61a48.87,48.87,0,0,1,.14,6.51c-.19,1.83-.41,4.67.25,5.47.27.33,2.59-1.51,2.67-1.89C322.47,135.52,327.37,110.82,326.27,93.93Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-301 0)",
},
 //Veneer
 {
  id: "37",
  code: "st19",
  path: "M326.68,90.72a6,6,0,0,0,1.38-2.59l4.84-14.85a27.58,27.58,0,0,1,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5a2.73,2.73,0,0,0-2.1-.9c-3.5,0-13.9-.2-19.6-1.4a2,2,0,0,0-2.3,1.3A15.12,15.12,0,0,0,308,59a28.06,28.06,0,0,0,.2,2.9c.5,4.2,2.78,20.35,4.58,26.95",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-300 5)",
},
//CrownPermanent 23
{
  id: "40",
  code: "st19",
  path: "M326.68,90.72a6,6,0,0,0,1.38-2.59l4.84-14.85a27.58,27.58,0,0,1,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5a2.73,2.73,0,0,0-2.1-.9c-3.5,0-13.9-.2-19.6-1.4a2,2,0,0,0-2.3,1.3A15.12,15.12,0,0,0,308,59a28.06,28.06,0,0,0,.2,2.9c.5,4.2,2.78,20.35,4.58,26.95",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-300 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M330.54,11c-1.26-2.79-9.26-18.73-19.62.09,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.32,4.4,4.4,0,0,1,1.84-.1A6.4,6.4,0,0,0,336,37.79s6.49-5-5-25.89C330.82,11.62,330.67,11.33,330.54,11Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-300 5)",
},
 //CrownGold 23
 {
  id: "43",
  code: "st43",
  path: "M316.64,86.93c13.45-8.58,24.41-25.24,24.64-25.42l.12,1.82c-.74.87-12.16,16.7-24.57,24.86",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-304 5) ",
},
{
  id: "44",
  code: "st43",
  path: "M314.92,78.82a130.05,130.05,0,0,0,23.39-23.89l.79,1.32a125.83,125.83,0,0,1-23.92,24.08",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-304 5) ",
},
 //CrownZirconia 23
 {
  id: "45",   code: "st26",
   path :"M337.67,55a2.73,2.73,0,0,0-2.1-.9c-3.5,0-13.9-.2-19.6-1.4a2,2,0,0,0-2.3,1.3,15,15,0,0,0-.8,5.2,28.06,28.06,0,0,0,.2,2.9c.5,4.2,2.4,19.1,4.2,25.7v0H333l4.74-14.32a27.58,27.58,0,0,1,1.6-4C340.57,66.93,342.77,60.63,337.67,55Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-306 5) ",},
 {
  id: "46",   code: "st26",
   path :"M317.27,88h0a2.75,2.75,0,0,0,1.1,1.56c0,.7.05,2.46.61,2.78a14.1,14.1,0,0,0,3.76,1.48v27.47a1.27,1.27,0,0,0,0,.34l1.06,5.38c.24,1.71.74,2.76,1.31,2.76s1.07-1.05,1.31-2.76l1.06-5.38a1.27,1.27,0,0,0,0-.34V93.9c2.09-.33,3.72-1.08,4.12-2a5.72,5.72,0,0,0,.23-2.24A3,3,0,0,0,333,87.85m-6.12,33.24-1.05,5.26s0,.06,0,.09c-.15,1.06-.42,1.74-.7,1.74s-.55-.68-.7-1.74c0,0,0-.06,0-.09l-1-4.89,3.44-2.55Zm0-3.81-3.52,2.6v-1.61l3.52-2.6Zm0-3.25-3.52,2.61V115l3.52-2.61Zm0-3.25-3.52,2.61v-1.61l3.52-2.6Zm0-3.24-3.52,2.61v-1.61l3.52-2.61Zm0-3.25-3.52,2.61v-1.61l3.52-2.61Zm0-3.24-3.52,2.61v-1.61l3.52-2.61Zm0-3.25-3.52,2.61V98.8l3.52-2.61Zm0-3.24-3.52,2.6V93.91a9.67,9.67,0,0,0,1.73.16h.12a15.16,15.16,0,0,0,1.67-.09Zm4.32-3.79c-.22.52-2.3,1.76-6.11,1.74a12.72,12.72,0,0,1-6-1.72A3.51,3.51,0,0,1,319,90a15.52,15.52,0,0,0,6.31,1.5,14,14,0,0,0,5.94-1.36A2.48,2.48,0,0,1,331.23,90.77Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-306 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M330.54,11c-1.26-2.79-9.26-18.73-19.62.09,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.32,4.4,4.4,0,0,1,1.84-.1A6.4,6.4,0,0,0,336,37.79s6.49-5-5-25.89C330.82,11.62,330.67,11.33,330.54,11Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-300 5)",
},
 //Denture 23
 {
  id: "48",
  code: "st19",
  path: "M326.68,90.72a6,6,0,0,0,1.38-2.59l4.84-14.85a27.58,27.58,0,0,1,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5a2.73,2.73,0,0,0-2.1-.9c-3.5,0-13.9-.2-19.6-1.4a2,2,0,0,0-2.3,1.3A15.12,15.12,0,0,0,308,59a28.06,28.06,0,0,0,.2,2.9c.5,4.2,2.78,20.35,4.58,26.95",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-300 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M330.54,11c-1.26-2.79-9.26-18.73-19.62.09,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.32,4.4,4.4,0,0,1,1.84-.1A6.4,6.4,0,0,0,336,37.79s6.49-5-5-25.89C330.82,11.62,330.67,11.33,330.54,11Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-300 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-2.5 6)",
},
 //Bridge 23
 {
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-4 -8)",
},
 //Implant 23
 {
  id: "54",
  code: "st9",
  path: "M17.26,134.08l6.48,2.73-.55,5A12.81,12.81,0,0,1,22.06,146c-.82,1.71-2.07,2.94-3.38-.85a19.55,19.55,0,0,1-.95-4.88Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.64 -5.05)"
},

{
  id: "55",
  code: "st9",
  polygon: "11.26 124.27 18.54 127.56 19.2 120.99 10.74 117.32 11.26 124.27",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "10.45 113.46 19.58 117.24 20.2 111.07 9.96 106.98 10.45 113.46",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "9.64 102.83 20.61 106.98 21.2 101.03 9.13 96.08 9.64 102.83",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M14.49,96l12.71,5.55,0-7.19a6.71,6.71,0,0,0-.22-1.7,12.31,12.31,0,0,0-2.84-5.15,3.62,3.62,0,0,0-5-.47,13.28,13.28,0,0,0-3.69,4.24,7.83,7.83,0,0,0-.9,3.52C14.52,95.43,14.49,96,14.49,96Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.64 -5.05)"
},
{
  id: "59",
  code: "st10",
  path: "M28.06,93.48l5.3-16a27.81,27.81,0,0,1,1.6-4c1.2-2.6,3.4-8.9-1.7-14.5a2.72,2.72,0,0,0-2.1-.9c-3.5,0-13.9-.2-19.6-1.4A2,2,0,0,0,9.26,58a15.28,15.28,0,0,0-.8,5.2,28.25,28.25,0,0,0,.2,2.9c.5,4.2,2.4,19.1,4.2,25.7a5.8,5.8,0,0,0,3.9,4,14.09,14.09,0,0,0,9.5-.3A3.24,3.24,0,0,0,28.06,93.48Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.64 -5.05)"
},
{
  id: "60",
  code: "st10",
  path: "M32.9,17.24a30.42,30.42,0,0,1-2.1-3.51C28.32,8.93,20.54-3.1,12.46,16c.16.28-15.43,24.14,1.78,27.13.43-.18,11,3.27,15.55.87a15.42,15.42,0,0,1,2.46-1C36.4,41.59,46.07,36.17,32.9,17.24Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.64 -5.05)"
},
//Bone 23
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
  transforms:"scale(0.5),translate(-5 82)"
},
  //Resection 23
  {
    id: "68",
    code: "st22",
    rect: {
      x: "25.54",
      y: "16.61",
      width: "3.93",
      height: "36.43"
    },
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-22 124)  rotate(-65.8)",
  },
  //TeethCrown 23
  {
    id: "69",
    code: "st67",
    path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
    style: { cursor: "pointer" },position: "",
     transforms:"scale(0.56),translate(-11 90)"
  },
      ],
    },
    //81: 'Lower Right Primary Central Incisor',
    {
      svg_id: "25",
       tooth_number: 81,
      tooth_name: TOOTH_NUMBERS[81],
         tooth_type: "Central Incisor",
      quadrant: "Lower Right ",
      tooth_position: 9,
      is_permanent: true,
      width:"54.75px",
      position:"0 0 46.4 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M33.1,92.7c1.9-2.3,7.2-10.2,7.3-28.7c0-1.9-1.1-3.7-2.8-4.5c-8.1-4-30.5-12-21.2,31.4c0.2,0.8,0.6,1.5,1.2,2  c2,1.6,6.9,4.5,14.1,1C32.3,93.6,32.7,93.2,33.1,92.7z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M33.1,92.7l-4.8,28.9c-0.3,1.8-0.5,3.5-0.6,5.3c-0.3,5.9-1.9,20.8-9.9,21.8l0,0c-1.7-0.6-2.7-2.4-2.4-4.2  l0.9-6.2c0.1-0.7,0.2-1.3,0.2-2l0.5-9.4c0-0.6,0-1.2,0-1.8v-33C18.4,94.2,21.1,96.1,33.1,92.7z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M25.4,102.1c1.4,4.4-0.5,30.8-5.4,40.2c-0.3,0.6-0.9,1-1.6,1h-0.1c-0.3,0-0.6-0.3-0.6-0.6c0-0.1,0-0.1,0-0.2  c1.1-3.2,5.1-16.2,3.6-39.7c0-0.7,0.5-1.2,1.1-1.3c0,0,0,0,0.1,0l0.7-0.1c0.5-0.1,0.9-0.1,1.4,0l0,0C25,101.4,25.3,101.7,25.4,102.1  z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
            // --------------------
            {
              id: "8",
              code: "st4",
              style: { cursor: "pointer" },position: "",
              path: "M26.9,26.7l-7,15.6c-0.3,0.6,0,1.4,0.6,1.7c0,0,0.1,0,0.1,0c4.6,1.6,9.7,1.7,14.3,0.1c0.7-0.3,1.1-1.1,0.8-1.9  c0,0,0-0.1-0.1-0.1l-7.6-15.5c-0.2-0.4-0.6-0.5-1-0.3C27,26.5,26.9,26.6,26.9,26.7z",
            },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.4,24l8.3-8.5c0.4-0.4,0.5-1,0.2-1.5C35,10.3,26.4-4.8,18,14.3c-0.2,0.4-0.1,0.9,0.1,1.3  c2.7,3,5.5,5.8,8.5,8.5C27.1,24.5,27.9,24.5,28.4,24z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M29.2,23l5.8-5.9c0.9-1,2.5-1,3.5-0.1c0.2,0.2,0.4,0.4,0.5,0.6c3.7,7,11.3,24.4-1.5,25.9c-0.4,0-0.8-0.2-1-0.6  l-7.8-16.2C27.9,25.5,28.2,24,29.2,23z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M27.2,25.5l-6.5,14c-0.3,0.6,0,1.2,0.6,1.5c0.1,0,0.1,0.1,0.2,0.1c4.2,1,8.5,1,12.6,0.1c0.7-0.2,1.2-1,1-1.7  c0-0.1-0.1-0.2-0.1-0.3l-6.7-13.7c-0.2-0.3-0.5-0.4-0.8-0.3C27.3,25.3,27.2,25.4,27.2,25.5z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M27.3,24.8l-8.4-8.9c-0.5-0.6-1.4-0.6-2-0.1c-0.1,0.1-0.2,0.2-0.3,0.4c-2.9,5.7-10.7,23.4,0.2,27.5  c1.1,0.4,2.4-0.1,2.9-1.1L27.6,27C27.9,26.3,27.8,25.4,27.3,24.8z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M33.1,92.7c1.9-2.3,7.2-10.2,7.3-28.7c0-1.9-1.1-3.7-2.8-4.5c-8.1-4-30.5-12-21.2,31.4c0.2,0.8,0.6,1.5,1.2,2  c2,1.6,6.9,4.5,14.1,1C32.3,93.6,32.7,93.2,33.1,92.7z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M382.5,10.31c-1.27-2.79-9.27-18.73-19.62.09,0,.16-9.16,16.59-4.63,25.08.16.27,3.45,5.29,6.54,4.22a32.93,32.93,0,0,0,15,.32,4.35,4.35,0,0,1,1.83-.1A6.42,6.42,0,0,0,388,37.06s6.49-5-5.05-25.89C382.77,10.89,382.63,10.6,382.5,10.31Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-346 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M383.3,55c-6.51-3.21-22.25-9-23.36,11.36a1.39,1.39,0,0,0,.61.06h4.16a9,9,0,0,0,3.47-.67l4.76-2a7,7,0,0,1,2.4-.51l3.19-.1a11.22,11.22,0,0,1,4.91.93,8.28,8.28,0,0,0,2.48.66c.11-1.64.17-3.38.18-5.23A5,5,0,0,0,383.3,55Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-346 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M33.1,92.7c1.9-2.3,7.2-10.2,7.3-28.7c0-1.9-1.1-3.7-2.8-4.5c-8.1-4-30.5-12-21.2,31.4c0.2,0.8,0.6,1.5,1.2,2  c2,1.6,6.9,4.5,14.1,1C32.3,93.6,32.7,93.2,33.1,92.7z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },

        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(2 0)",
        },
        {
          id: "20",
          code: "st24",
          path: "M33.1,92.7c1.9-2.3,7.2-10.2,7.3-28.7c0-1.9-1.1-3.7-2.8-4.5c-8.1-4-30.5-12-21.2,31.4c0.2,0.8,0.6,1.5,1.2,2  c2,1.6,6.9,4.5,14.1,1C32.3,93.6,32.7,93.2,33.1,92.7z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-2 -6)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-2 -6)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-2 -6)",style: { cursor: "pointer" },position: "",},
           // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(2.5 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(2.5 -2) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-11 0) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-13 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-13 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M277.65,138.32c-.07.34-.29.59-.5.57s-.39-.35-.35-.74h0c.43-3.46.81-6.94,1.08-10.39s.34-6.88.29-10.27a141,141,0,0,0-1.78-19.89,164.41,164.41,0,0,0-4.08-18.53,158.24,158.24,0,0,0-5.79-17,1.84,1.84,0,0,1,.26-1.74.56.56,0,0,1,1,.16,160,160,0,0,1,10,36.22,145.38,145.38,0,0,1,1.72,20.4c0,3.51-.08,7.06-.37,10.62a101.66,101.66,0,0,1-1.51,10.58Z",
      transforms:"translate(-255 2) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M267.16,61.55h0c-.92,1-2.07.72-2.56-.65l-3-8.31a4.66,4.66,0,0,1,.77-4.3h0c.93-1,2.07-.72,2.56.64l3,8.31A4.68,4.68,0,0,1,267.16,61.55Z",
      transforms:"translate(-255 2) ",
    },
           //PostCare
{
  id: "36",
  code: "st17",
  path: "M376.57,93.77a26.08,26.08,0,0,0-.56-4.39l-1-5.77c-.16-.89-.53-1.46-.93-1.46H369c-.29,0-.55.34-.71.9-.59,2.12-1.32,7.55-1.32,14.88l.6,29.74a19.72,19.72,0,0,1-.57,5.62c-.13.54-.29,1.3-.42,2-.18.93-.4,1.84-.67,2.75-.5,1.72-1.23,4.55-.75,5.06.12.12.94-.22,1.88-.68a7,7,0,0,0,3.76-4.84C372.81,128,376.7,108.09,376.57,93.77Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-347.5 0)",
},
//Veneer
{
  id: "37",
  code: "st19",
  path: "M378.8,88.2c1.9-2.3,7.2-10.2,7.3-28.7a5,5,0,0,0-2.8-4.5c-8.1-4-30.5-12-21.2,31.4a3.76,3.76,0,0,0,1.2,2",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-346 5)",
},
//CrownPermanent 24
{
  id: "40",
  code: "st19",
  path: "M378.8,88.2c1.9-2.3,7.2-10.2,7.3-28.7a5,5,0,0,0-2.8-4.5c-8.1-4-30.5-12-21.2,31.4a3.76,3.76,0,0,0,1.2,2",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-345 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M382.5,10.31c-1.27-2.79-9.27-18.73-19.62.09,0,.16-9.16,16.59-4.63,25.08.16.27,3.45,5.29,6.54,4.22a32.93,32.93,0,0,0,15,.32,4.35,4.35,0,0,1,1.83-.1A6.42,6.42,0,0,0,388,37.06s6.49-5-5.05-25.89C382.77,10.89,382.63,10.6,382.5,10.31Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-345 5)",
},
 //CrownGold 24
 {
  id: "43",
  code: "st43",
  path: "M365.59,82.77A131.29,131.29,0,0,0,390.67,57.5l.65,1.52a126,126,0,0,1-25.41,25.19",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-351 3) ",
},
{
  id: "44",
  code: "st43",
  path: "M367,88.13c13.45-8.58,23.74-24.17,24-24.35l.36,1.46c-.74.86-11.19,15.71-23.6,23.87",
  style: { cursor: "pointer" },position: "",
transforms:"translate(-351 3) ",
},
 //CrownZirconia 24
 {
  id: "45",   code: "st26",
   path :"M367.32,87.85h17c2.25-3.13,6.54-11.19,6.63-27.82a5,5,0,0,0-2.8-4.5c-8.1-4-30.5-12-21.2,31.4A3.8,3.8,0,0,0,367.32,87.85Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-351 5) ",},
 {
  id: "46",   code: "st26",
   path :"M367.32,88h0a2.78,2.78,0,0,0,1.2,1.57c0,.72.05,2.5.65,2.82a15.65,15.65,0,0,0,4.07,1.51v27.88a2,2,0,0,0,0,.35l1.15,5.45c.26,1.74.8,2.81,1.42,2.81s1.15-1.07,1.41-2.81l1.16-5.45a2,2,0,0,0,0-.35V94c2.27-.33,4-1.09,4.46-2a5.39,5.39,0,0,0,.25-2.28,3.05,3.05,0,0,0,1.21-1.83m-6.62,33.73-1.13,5.34s0,.06,0,.1c-.15,1.07-.45,1.77-.75,1.77s-.6-.7-.76-1.77a.3.3,0,0,0,0-.1l-1-5,3.72-2.59Zm0-3.86-3.8,2.64v-1.63l3.8-2.65Zm0-3.3-3.8,2.65v-1.64l3.8-2.64Zm0-3.29-3.8,2.64v-1.63l3.8-2.64Zm0-3.29-3.8,2.64v-1.63l3.8-2.65Zm0-3.3-3.8,2.65v-1.64l3.8-2.64Zm0-3.29-3.8,2.64v-1.63l3.8-2.65Zm0-3.3L374,100.6V99l3.8-2.64Zm0-3.29L374,97.3V94a11.2,11.2,0,0,0,1.86.16H376a17.49,17.49,0,0,0,1.8-.09Zm4.68-3.85c-.25.53-2.5,1.79-6.61,1.77a14.53,14.53,0,0,1-6.48-1.74,3.07,3.07,0,0,1-.11-.85,17.63,17.63,0,0,0,6.82,1.52c.06,0,4,0,6.42-1.37A2.63,2.63,0,0,1,382.43,90.81Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-351 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M382.5,10.31c-1.27-2.79-9.27-18.73-19.62.09,0,.16-9.16,16.59-4.63,25.08.16.27,3.45,5.29,6.54,4.22a32.93,32.93,0,0,0,15,.32,4.35,4.35,0,0,1,1.83-.1A6.42,6.42,0,0,0,388,37.06s6.49-5-5.05-25.89C382.77,10.89,382.63,10.6,382.5,10.31Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-345 5)",
},
 //Denture 24
 {
  id: "48",
  code: "st19",
  path: "M378.8,88.2c1.9-2.3,7.2-10.2,7.3-28.7a5,5,0,0,0-2.8-4.5c-8.1-4-30.5-12-21.2,31.4a3.76,3.76,0,0,0,1.2,2",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-345 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M382.5,10.31c-1.27-2.79-9.27-18.73-19.62.09,0,.16-9.16,16.59-4.63,25.08.16.27,3.45,5.29,6.54,4.22a32.93,32.93,0,0,0,15,.32,4.35,4.35,0,0,1,1.83-.1A6.42,6.42,0,0,0,388,37.06s6.49-5-5.05-25.89C382.77,10.89,382.63,10.6,382.5,10.31Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-345 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(5 6)",
},
//Bridge 24
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(1.5 -8)",
},
//Implant 24
{
  id: "54",
  code: "st9",
  path: "M12.4,130.94l7.82,2.76-.65,5a11.31,11.31,0,0,1-1.34,4.22c-1,1.7-2.5,2.94-4.08-.86A16.82,16.82,0,0,1,13,137.17Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.02 -4.07)"
},

{
  id: "55",
  code: "st9",
  polygon: "8.93 122.12 17.7 125.43 18.48 118.86 8.27 115.17 8.93 122.12",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "7.91 111.3 18.92 115.11 19.65 108.94 7.29 104.82 7.91 111.3",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "6.9 100.67 20.13 104.85 20.82 98.91 6.27 93.91 6.9 100.67",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M8.93,92.85l15.34,5.6-.06-7.2a5.59,5.59,0,0,0-.28-1.69,11.79,11.79,0,0,0-3.44-5.17,5,5,0,0,0-6-.49,14.37,14.37,0,0,0-4.44,4.23A6.79,6.79,0,0,0,9,91.65C9,92.29,8.93,92.85,8.93,92.85Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.02 -4.07)"
},
{
  id: "59",
  code: "st10",
  path: "M25.47,92.7c1.9-2.3,7.2-10.2,7.3-28.7A5,5,0,0,0,30,59.5c-8.1-4-30.5-12-21.2,31.4a3.74,3.74,0,0,0,1.2,2c2,1.6,6.9,4.5,14.1,1A5.26,5.26,0,0,0,25.47,92.7Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.02 -4.07)"
},
{
  id: "60",
  code: "st10",
  path: "M29.55,16.52a18.4,18.4,0,0,1-.9-1.84c-1.7-4-9.76-20.79-18.58.37.3.45-16,25.8,1.71,28.45.39,0,7.14,3.93,14.21,1a12.88,12.88,0,0,1,5.49-1.11C35.94,43.53,43.06,40.81,29.55,16.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.02 -4.07)"
},
//Bone 24
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.5),translate(-5 82)"
},
  //Resection 24
  {
    id: "68",
    code: "st22",
    rect: {
      x: "25.54",
      y: "16.61",
      width: "3.93",
      height: "36.43"
    },
    style: { cursor: "pointer" },position: "",
    transforms:"translate(-20 124)  rotate(-65.8)",
  },
  //TeethCrown 24
  {
    id: "69",
    code: "st67",
    path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
    style: { cursor: "pointer" },position: "",
     transforms:"scale(0.56),translate(-5 90)"
  },
      ],
    },
    //71: 'Lower Left Primary Central Incisor',
    {
      svg_id: "24",
       tooth_number: 71,
      tooth_name: TOOTH_NUMBERS[71],
      tooth_type: "Central Incisor",
      quadrant: "Lower Left ",
      tooth_position: 1,
      is_permanent: true,
      width:"57.425px",
      position:"0 0 48.7 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M35.3,92c1.7-6.7,7.5-33.5-7.9-36.7c-0.2,0-0.3,0-0.5-0.1c-2.1-0.2-15.8-1.2-15.7,8c0,0-0.4,18.7,6.2,29.7  c0.7,1.1,1.8,1.7,3,1.9c3,0.3,8.8,0.7,12.9-0.7C34.2,93.8,35,93,35.3,92z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M35.1,92.4c0,0-2.4,33.3,0.9,45.8c0.4,1.6,0.6,3.2,0.6,4.8v2.6c0.2,1.3-0.8,2.4-2,2.6c-0.1,0-0.1,0-0.2,0l0,0  c-1.7,0-3.3-0.8-4.3-2.2c-1.2-1.7-2.2-3.6-3-5.6c-1.5-3.8-2.3-7.8-2.4-11.9c-0.1-4.5-0.9-9-2.4-13.2c-0.4-1.1-0.7-2.2-0.9-3.4  l-3.2-17.7C18.2,94.1,30,97.9,35.1,92.4z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M30.4,100.7c0,0,0.8,32.3,3.6,40.3c0.2,0.5-0.1,1-0.6,1.2c-0.1,0-0.2,0-0.3,0l0,0c-0.4,0-0.8-0.3-0.9-0.6  c-1.3-3.9-8.1-25.2-7.1-38.5c0.1-1.4,1.1-2.5,2.4-2.8C29.1,100,30.6,99.7,30.4,100.7z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
         // --------------------
         {
          id: "8",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M24.5,23.8l8.2-7.9c0.5-0.5,1.2-0.4,1.7,0c0.1,0.1,0.1,0.2,0.2,0.3C37.5,21.7,46.4,41,33.8,43  c-0.8,0.1-1.5-0.3-1.8-1l-7.8-16.3C23.9,25.1,24,24.3,24.5,23.8z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M25,23.8l8.9-8c0.6-0.5,0.8-1.4,0.4-2.2c-2.4-4.2-10.8-16.9-19.9,0.1c-0.4,0.7-0.3,1.5,0.3,2.1l7.9,7.9  C23.2,24.4,24.3,24.4,25,23.8z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23.2,25.4l-5.4,13.3c-0.1,0.3,0,0.7,0.3,0.8c0,0,0.1,0,0.1,0c1.9,0.5,7.3,1.6,12.4,0c0.3-0.1,0.5-0.5,0.4-0.8  c0,0,0-0.1,0-0.1l-6.7-13.4c-0.2-0.3-0.5-0.4-0.8-0.3C23.4,25.2,23.3,25.3,23.2,25.4z",
        },
        {
          id: "11",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M22.9,26.4l-6.6,16.4c-0.2,0.4,0,0.8,0.4,1c0,0,0.1,0,0.1,0c2.3,0.6,8.9,2,15.2,0c0.4-0.1,0.6-0.6,0.5-1  c0,0,0-0.1,0-0.1l-8.2-16.4c-0.2-0.4-0.6-0.5-1-0.3C23.1,26.1,23,26.3,22.9,26.4z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M22.8,23.8l-7.1-8.6c-0.6-0.7-1.5-0.8-2.2-0.2c-0.2,0.1-0.3,0.3-0.4,0.5c-3.2,6.4-11.5,25.1,2.1,27.7  c0.7,0.1,1.4-0.3,1.6-0.9l6.3-16.5C23.4,25.1,23.3,24.4,22.8,23.8z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M35.3,92c1.7-6.7,7.5-33.5-7.9-36.7c-0.2,0-0.3,0-0.5-0.1c-2.1-0.2-15.8-1.2-15.7,8c0,0-0.4,18.7,6.2,29.7  c0.7,1.1,1.8,1.7,3,1.9c3,0.3,8.8,0.7,12.9-0.7C34.2,93.8,35,93,35.3,92z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M427.8,10.26c-1.26-2.79-9.26-18.73-19.61.09,0,.16-9.16,16.59-4.63,25.08.16.26,3.44,5.29,6.54,4.22a33,33,0,0,0,15,.32,4.41,4.41,0,0,1,1.84-.1A6.44,6.44,0,0,0,433.29,37s6.49-5-5.06-25.89C428.08,10.84,427.93,10.55,427.8,10.26Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-396 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M432,60.56c-1.38-4.61-4.14-8.22-9.21-9.28a.87.87,0,0,1-.5-.1c-2.1-.2-15.8-1.2-15.7,8,0,0,0,1.66.09,4.26a9.22,9.22,0,0,0,2.78-.64l4.76-2a7.06,7.06,0,0,1,2.4-.51l3.19-.1a11,11,0,0,1,4.91.93,8.3,8.3,0,0,0,3.29.7l4,0C432,61.45,432,61,432,60.56Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-396 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M35.3,92c1.7-6.7,7.5-33.5-7.9-36.7c-0.2,0-0.3,0-0.5-0.1c-2.1-0.2-15.8-1.2-15.7,8c0,0-0.4,18.7,6.2,29.7  c0.7,1.1,1.8,1.7,3,1.9c3,0.3,8.8,0.7,12.9-0.7C34.2,93.8,35,93,35.3,92z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },

        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(0 0)",
        },
        {
          id: "20",
          code: "st25",
          path: "M35.3,92c1.7-6.7,7.5-33.5-7.9-36.7c-0.2,0-0.3,0-0.5-0.1c-2.1-0.2-15.8-1.2-15.7,8c0,0-0.4,18.7,6.2,29.7  c0.7,1.1,1.8,1.7,3,1.9c3,0.3,8.8,0.7,12.9-0.7C34.2,93.8,35,93,35.3,92z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-4 -4)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-4 -4)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-4 -4)",style: { cursor: "pointer" },position: "",},
            // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(1 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(1 -2) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-13 0) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-14 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-14 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M277.65,138.32c-.07.34-.29.59-.5.57s-.39-.35-.35-.74h0c.43-3.46.81-6.94,1.08-10.39s.34-6.88.29-10.27a141,141,0,0,0-1.78-19.89,164.41,164.41,0,0,0-4.08-18.53,158.24,158.24,0,0,0-5.79-17,1.84,1.84,0,0,1,.26-1.74.56.56,0,0,1,1,.16,160,160,0,0,1,10,36.22,145.38,145.38,0,0,1,1.72,20.4c0,3.51-.08,7.06-.37,10.62a101.66,101.66,0,0,1-1.51,10.58Z",
      transforms:"translate(-250 0) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M267.16,61.55h0c-.92,1-2.07.72-2.56-.65l-3-8.31a4.66,4.66,0,0,1,.77-4.3h0c.93-1,2.07-.72,2.56.64l3,8.31A4.68,4.68,0,0,1,267.16,61.55Z",
      transforms:"translate(-250 0) ",
    },
          //PostCare
{
  id: "36",
  code: "st17",
  path: "M419.37,93.17a26,26,0,0,1,.55-4.38L421,83c.16-.88.52-1.45.93-1.45H427c.28,0,.55.33.71.89a58.2,58.2,0,0,1,2,15.14L428.46,125a14.91,14.91,0,0,0,.69,6.46c.21.75.78,4.1.84,4.67.12,1,.79,3.15,1.14,4.8a.69.69,0,0,1-1.23.56c-.88-1.16-2-2.74-3-4.13a17.35,17.35,0,0,1-2.52-5.88C422.5,122.89,419.22,107.36,419.37,93.17Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-397 0)",
},
//Veneer
{
  id: "37",
  code: "st19",
  path: "M430.7,88c1.7-6.7,7.5-33.5-7.9-36.7a.87.87,0,0,1-.5-.1c-2.1-.2-15.8-1.2-15.7,8,0,0-.4,18.7,6.2,29.7",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-395 5)",
},
//CrownPermanent 25
{
  id: "40",
  code: "st19",
  path: "M430.7,88c1.7-6.7,7.5-33.5-7.9-36.7a.87.87,0,0,1-.5-.1c-2.1-.2-15.8-1.2-15.7,8,0,0-.4,18.7,6.2,29.7",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-395 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M427.8,10.26c-1.26-2.79-9.26-18.73-19.61.09,0,.16-9.16,16.59-4.63,25.08.16.26,3.44,5.29,6.54,4.22a33,33,0,0,0,15,.32,4.41,4.41,0,0,1,1.84-.1A6.44,6.44,0,0,0,433.29,37s6.49-5-5.06-25.89C428.08,10.84,427.93,10.55,427.8,10.26Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-396 5)",
},
 //CrownGold 25
 {
  id: "43",
  code: "st43",
  path: "M412.77,77.17a131.79,131.79,0,0,0,21.58-22.53l.65,1.52a125.4,125.4,0,0,1-21.86,22.48",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-400 5) ",
},
{
  id: "44",
  code: "st43",
  path: "M415.2,85.13c13.45-8.59,22.27-23.25,22.49-23.43l.37,1.46c-.74.87-10,14.87-22.37,23",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-400 5) ",
},
 //CrownZirconia 25
 {
  id: "45",   code: "st26",
   path :"M435.61,87.85c1.75-7,7.33-33.35-7.94-36.52a.87.87,0,0,1-.5-.1c-2.1-.2-15.8-1.2-15.7,8,0,0-.37,17.52,5.59,28.62Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-401 5) ",},
 {
  id: "46",   code: "st26",
   path :"M417.28,88.24h0a2.81,2.81,0,0,0,1.28,1.59c0,.72.05,2.52.7,2.85a17.81,17.81,0,0,0,4.37,1.51v28.15a1.79,1.79,0,0,0,0,.34l1.24,5.51c.28,1.75.86,2.84,1.52,2.84s1.24-1.09,1.52-2.84l1.23-5.51a1.22,1.22,0,0,0,0-.34v-28c2.43-.33,4.32-1.09,4.78-2a5.07,5.07,0,0,0,.27-2.3,3.07,3.07,0,0,0,1.3-1.85m-7.1,34.05-1.21,5.39s0,.06,0,.09c-.17,1.08-.49,1.79-.81,1.79s-.64-.71-.81-1.79c0,0,0-.06,0-.09l-1.12-5,4-2.61Zm0-3.91-4.08,2.67v-1.65l4.08-2.67Zm0-3.32-4.08,2.67V116l4.08-2.67Zm0-3.33-4.08,2.67v-1.65l4.08-2.66Zm0-3.32L424.38,111V109.3l4.08-2.67Zm0-3.32-4.08,2.67V106l4.08-2.67Zm0-3.33-4.08,2.67v-1.65l4.08-2.67Zm0-3.32L424.38,101V99.33l4.08-2.67Zm0-3.33-4.08,2.67V94.32a13,13,0,0,0,2,.16h.15c.66,0,1.31,0,1.93-.09Zm5-3.88c-.26.54-2.67,1.81-7.08,1.78a16.12,16.12,0,0,1-6.95-1.76,2.86,2.86,0,0,1-.12-.85,20,20,0,0,0,7.32,1.54c.06,0,4.25,0,6.88-1.39A2.16,2.16,0,0,1,433.47,91.1Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-401 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M427.8,10.26c-1.26-2.79-9.26-18.73-19.61.09,0,.16-9.16,16.59-4.63,25.08.16.26,3.44,5.29,6.54,4.22a33,33,0,0,0,15,.32,4.41,4.41,0,0,1,1.84-.1A6.44,6.44,0,0,0,433.29,37s6.49-5-5.06-25.89C428.08,10.84,427.93,10.55,427.8,10.26Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-396 5)",
},
 //Denture 25
 {
  id: "48",
  code: "st19",
  path: "M430.7,88c1.7-6.7,7.5-33.5-7.9-36.7a.87.87,0,0,1-.5-.1c-2.1-.2-15.8-1.2-15.7,8,0,0-.4,18.7,6.2,29.7",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-395 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M427.8,10.26c-1.26-2.79-9.26-18.73-19.61.09,0,.16-9.16,16.59-4.63,25.08.16.26,3.44,5.29,6.54,4.22a33,33,0,0,0,15,.32,4.41,4.41,0,0,1,1.84-.1A6.44,6.44,0,0,0,433.29,37s6.49-5-5.06-25.89C428.08,10.84,427.93,10.55,427.8,10.26Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-396 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 6)",
},
//Bridge 25
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(1 -8)",
},
//Implant 25
{
  id: "54",
  code: "st9",
  path: "M30.07,133.52l-8.73,2.77.71,5a10.49,10.49,0,0,0,1.49,4.22c1.1,1.7,2.78,2.93,4.55-.87a15.26,15.26,0,0,0,1.31-4.89Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-6.13 -4.67)"
},

{
  id: "55",
  code: "st9",
  polygon: "24.45 124.09 14.65 127.42 13.79 120.85 25.19 117.14 24.45 124.09",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "25.6 113.27 13.3 117.11 12.51 110.94 26.3 106.78 25.6 113.27",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "26.74 102.64 11.98 106.84 11.21 100.9 27.47 95.88 26.74 102.64",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M34,95.42,16.89,101,17,93.85a4.83,4.83,0,0,1,.31-1.7A11.83,11.83,0,0,1,21.13,87a6.11,6.11,0,0,1,6.68-.5,15.44,15.44,0,0,1,4.94,4.22,6.18,6.18,0,0,1,1.19,3.52C34,94.85,34,95.42,34,95.42Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-6.13 -4.67)"
},
{
  id: "59",
  code: "st10",
  path: "M34,92.6c1.7-6.7,7.5-33.5-7.9-36.7a.9.9,0,0,1-.5-.1c-2.1-.2-15.8-1.2-15.7,8,0,0-.4,18.7,6.2,29.7a4.28,4.28,0,0,0,3,1.9c3,.3,8.8.7,12.9-.7A3,3,0,0,0,34,92.6Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-6.13 -4.67)"
},
{
  id: "60",
  code: "st10",
  path: "M33.93,16.74c-.24-.49-.47-1-.68-1.53C31.78,11.5,24.1-5.27,12.71,15c-.4.5-15.31,25.64,3.1,28.8.09,0,7.52,1.58,13.76-.25a22.92,22.92,0,0,1,3.75-.78C37.08,42.32,44.9,38.92,33.93,16.74Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-6.13 -4.67)"
},
//Bone 25
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
    transforms:"scale(0.5),translate(-5 82)"
},
 //Resection 25
 {
  id: "68",
  code: "st22",
  rect: {
    x: "25.54",
    y: "16.61",
    width: "3.93",
    height: "36.43"
  },
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-16 124)  rotate(-65.8)",
},
//TeethCrown 25
{
  id: "69",
  code: "st67",
  path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.56),translate(-5 90)"
},
      ],
    },
   // 72: 'Lower Left Primary Lateral Incisor',
    {
      svg_id: "23",
       tooth_number: 72,
      tooth_name: TOOTH_NUMBERS[72],
      tooth_type: "Lateral Incisor",
      quadrant: "Lower Left ",
      tooth_position: 1,
      is_permanent: true,
      width:"49.975px",
      position:"0 0 42.3 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M34.5,94.8c0.5-0.1,2.8-16.5,3.3-24c0.1-1.3,0.6-2.5,1.4-3.6c0.9-1.5,1.4-3.3,1.4-5.1c-0.1-2.6-2.2-4.7-4.9-4.6  c-0.1,0-0.3,0-0.4,0c-4,0.4-11.7,1-15.6,0.3c-2.5-0.4-4.9,0.9-6,3.2c-1.8,4.5-2.3,19.2,5.1,32c0.8,1.5,3.4,2.2,5.1,2.4  C27.5,95.7,31,95.5,34.5,94.8z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M34.5,95v50.7c0,0.7-0.2,1.3-0.5,1.9c-0.3,0.7-0.6,1.4-0.6,2.1c-0.1,1.2-1.2,2-2.3,1.9c-0.4,0-0.7-0.2-1-0.4  c-3.7-2.7-10.1-15.1-10.7-56.1c0-0.4,0.4-0.8,0.8-0.8c0.2,0,0.3,0.1,0.5,0.2c0.8,0.5,5.4,2.3,13.2,0c0.3-0.1,0.6,0.1,0.6,0.4  C34.5,94.9,34.5,95,34.5,95z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M29.2,100.8l1.8,42c0.1,0.5-0.3,1.1-0.8,1.1c-0.3,0-0.7-0.1-0.9-0.4l-0.1-0.1L26,122.3l-2.4-20.6  c-0.1-1.3,0.8-2.4,2-2.6c0.1,0,0.2,0,0.3,0h1.4C28.4,99,29.2,99.8,29.2,100.8z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        // --------------------
        {
          id: "8",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M25.2,26.4l-6.3,16.4c-0.2,0.6,0.1,1.3,0.7,1.5c0,0,0.1,0,0.1,0c2.6,0.6,8.6,1.7,14,0.1  c0.7-0.2,1.1-0.9,0.9-1.6c0-0.1,0-0.1-0.1-0.2l-7.9-16.4c-0.2-0.4-0.7-0.5-1.1-0.3C25.4,26.1,25.2,26.3,25.2,26.4z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.2,23.6l7.6-7.2c0.8-0.8,1-2.1,0.5-3.1c-2.5-4.7-10.1-16.1-18.6,0.2c-0.5,1-0.3,2.1,0.4,2.9l6.4,7  C25.5,24.5,27.1,24.5,28.2,23.6C28.1,23.6,28.2,23.6,28.2,23.6z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M26.2,23.7l9.9-7.4c0.5-0.4,1.2-0.3,1.6,0.2c0.1,0.1,0.1,0.2,0.1,0.3c2.1,4.9,8.5,21.8,0.2,26.8  c-1,0.6-2.3,0.3-2.9-0.7c0,0,0-0.1-0.1-0.1l-9.2-17.5C25.6,24.8,25.7,24.1,26.2,23.7z",
        },



        {
          id: "11",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M25.5,26.4l-4.9,12.8c-0.2,0.5,0.1,1,0.5,1.2c0,0,0.1,0,0.1,0c2,0.5,6.7,1.3,10.9,0.1c0.5-0.2,0.8-0.7,0.7-1.2  c0-0.1,0-0.1-0.1-0.1l-6.2-12.8c-0.2-0.3-0.6-0.4-0.9-0.2C25.7,26.2,25.6,26.3,25.5,26.4z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M25.3,24.7l-8-9.1c-0.5-0.6-1.4-0.6-1.9-0.1c-0.1,0.1-0.3,0.3-0.3,0.5c-2.7,6-10,24.5,2.1,27.8  c0.7,0.2,1.4-0.2,1.7-0.8l6.8-16.8C25.7,25.6,25.6,25.1,25.3,24.7z",
        },
        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M34.5,94.8c0.5-0.1,2.8-16.5,3.3-24c0.1-1.3,0.6-2.5,1.4-3.6c0.9-1.5,1.4-3.3,1.4-5.1c-0.1-2.6-2.2-4.7-4.9-4.6  c-0.1,0-0.3,0-0.4,0c-4,0.4-11.7,1-15.6,0.3c-2.5-0.4-4.9,0.9-6,3.2c-1.8,4.5-2.3,19.2,5.1,32c0.8,1.5,3.4,2.2,5.1,2.4  C27.5,95.7,31,95.5,34.5,94.8z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M480.05,10.52c-1.26-2.79-9.26-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.4,6.4,0,0,0,6.38-2.86s6.49-5-5-25.89C480.33,11.1,480.18,10.81,480.05,10.52Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-444 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M481.11,53.71a1.93,1.93,0,0,0-.24,0l-3,0c-4.21.38-10.69.8-14.17.16a5.63,5.63,0,0,0-6,3.25,20.55,20.55,0,0,0-1,6.22h1.81a7.93,7.93,0,0,0,3.69-.91l5.07-2.71a6,6,0,0,1,2.55-.69l3.4-.14a9.64,9.64,0,0,1,5.22,1.26,7.21,7.21,0,0,0,3.5,1l2.23,0a10.56,10.56,0,0,0,.43-2.91A4.77,4.77,0,0,0,481.11,53.71Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-444 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M34.5,94.8c0.5-0.1,2.8-16.5,3.3-24c0.1-1.3,0.6-2.5,1.4-3.6c0.9-1.5,1.4-3.3,1.4-5.1c-0.1-2.6-2.2-4.7-4.9-4.6  c-0.1,0-0.3,0-0.4,0c-4,0.4-11.7,1-15.6,0.3c-2.5-0.4-4.9,0.9-6,3.2c-1.8,4.5-2.3,19.2,5.1,32c0.8,1.5,3.4,2.2,5.1,2.4  C27.5,95.7,31,95.5,34.5,94.8z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },
        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(1.5 0)",
        },
        {
          id: "20",
          code: "st25",
          path: "M34.5,94.8c0.5-0.1,2.8-16.5,3.3-24c0.1-1.3,0.6-2.5,1.4-3.6c0.9-1.5,1.4-3.3,1.4-5.1c-0.1-2.6-2.2-4.7-4.9-4.6  c-0.1,0-0.3,0-0.4,0c-4,0.4-11.7,1-15.6,0.3c-2.5-0.4-4.9,0.9-6,3.2c-1.8,4.5-2.3,19.2,5.1,32c0.8,1.5,3.4,2.2,5.1,2.4  C27.5,95.7,31,95.5,34.5,94.8z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-3 -5)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-3 -5)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-3 -5)",style: { cursor: "pointer" },position: "",},
          // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(1 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(1 -2) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-15 1) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-14 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-14 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M277.65,138.32c-.07.34-.29.59-.5.57s-.39-.35-.35-.74h0c.43-3.46.81-6.94,1.08-10.39s.34-6.88.29-10.27a141,141,0,0,0-1.78-19.89,164.41,164.41,0,0,0-4.08-18.53,158.24,158.24,0,0,0-5.79-17,1.84,1.84,0,0,1,.26-1.74.56.56,0,0,1,1,.16,160,160,0,0,1,10,36.22,145.38,145.38,0,0,1,1.72,20.4c0,3.51-.08,7.06-.37,10.62a101.66,101.66,0,0,1-1.51,10.58Z",
      transforms:"translate(-250 0) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M267.16,61.55h0c-.92,1-2.07.72-2.56-.65l-3-8.31a4.66,4.66,0,0,1,.77-4.3h0c.93-1,2.07-.72,2.56.64l3,8.31A4.68,4.68,0,0,1,267.16,61.55Z",
      transforms:"translate(-250 0) ",
    },
            //PostCare
{
  id: "36",
  code: "st17",
  path: "M467.06,93.17a26,26,0,0,1,.55-4.38l1-5.78c.16-.88.52-1.45.93-1.45h5.06c.28,0,.55.33.7.89a57.61,57.61,0,0,1,2,15.14L476.15,125a14.37,14.37,0,0,0,.37,5.29,11.87,11.87,0,0,1,.36,2.51c.05,1.36.13,3.3.17,3.66.12,1.05.1,6.33.27,8,.07.73-2,.55-2.46,0-.87-1.15-.71-4-1.22-5.59-.65-2.05-1.09-5.21-1.56-7.31C470.19,122.89,466.91,107.36,467.06,93.17Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-444 4)",
},
//Veneer
{
  id: "37",
  code: "st19",
  path: "M478.5,91.36c.5-.11,2.8-16.74,3.3-24.34a7.1,7.1,0,0,1,1.4-3.65,10.25,10.25,0,0,0,1.4-5.18,4.74,4.74,0,0,0-4.84-4.66h-.46c-4,.41-11.7,1-15.6.3a5.63,5.63,0,0,0-6,3.25c-1.8,4.56-2,20.4,5.7,33.25",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-444 4)",
},
//CrownPermanent 26
{
  id: "40",
  code: "st19",
  path: "M478.5,91.36c.5-.11,2.8-16.74,3.3-24.34a7.1,7.1,0,0,1,1.4-3.65,10.25,10.25,0,0,0,1.4-5.18,4.74,4.74,0,0,0-4.84-4.66h-.46c-4,.41-11.7,1-15.6.3a5.63,5.63,0,0,0-6,3.25c-1.8,4.56-2,20.4,5.7,33.25",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-444.5 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M480.05,10.52c-1.26-2.79-9.26-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.4,6.4,0,0,0,6.38-2.86s6.49-5-5-25.89C480.33,11.1,480.18,10.81,480.05,10.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-446 5)",
},
//CrownGold 26
{
  id: "43",
  code: "st43",
  path: "M462.5,77.79a130.9,130.9,0,0,0,24.12-24.55l1.05.83a129.27,129.27,0,0,1-24.79,25.11",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-449 5) ",
},
{
  id: "44",
  code: "st43",
  path: "M465.15,85.15c13.46-8.59,24.51-25.75,24.74-25.93l-.74,3.22c-.74.86-11,15.58-23.45,23.75",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-449 5) ",
},
 //CrownZirconia 26
 {
  id: "45",   code: "st26",
   path :"M484.63,53.53h-.46c-4,.4-11.7,1-15.6.3a5.64,5.64,0,0,0-6,3.2c-1.74,4.36-2.27,18.31,4.44,30.82h17.06c.88-5.09,2.23-15.48,2.6-21a6.93,6.93,0,0,1,1.4-3.6,10,10,0,0,0,1.4-5.1A4.71,4.71,0,0,0,484.63,53.53Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-452 5) ",},
 {
  id: "46",   code: "st26",
   path :"M466.22,87.9h0a2.77,2.77,0,0,0,1.25,1.56c0,.71,0,2.48.68,2.8a17,17,0,0,0,4.25,1.49v27.63a1.87,1.87,0,0,0,0,.34l1.21,5.41c.27,1.72.83,2.78,1.47,2.78s1.21-1.06,1.48-2.78l1.2-5.41a1.27,1.27,0,0,0,0-.34V93.86c2.36-.33,4.2-1.08,4.65-2a5.15,5.15,0,0,0,.26-2.26A3,3,0,0,0,484,87.78m-6.9,33.42-1.18,5.29s0,.06,0,.1c-.16,1.06-.47,1.75-.79,1.75s-.62-.69-.79-1.75c0,0,0-.07,0-.1l-1.1-4.91L477.1,119Zm0-3.83-4,2.62v-1.62l4-2.62Zm0-3.26-4,2.62v-1.62l4-2.62Zm0-3.27-4,2.62v-1.62l4-2.62Zm0-3.26-4,2.62v-1.62l4-2.62Zm0-3.26-4,2.62v-1.62l4-2.62Zm0-3.27-4,2.62v-1.62l4-2.62Zm0-3.26-4,2.62V98.79l4-2.62Zm0-3.27-4,2.62V93.87a12.22,12.22,0,0,0,1.95.16h.14c.64,0,1.27,0,1.88-.09ZM482,90.71c-.26.53-2.61,1.78-6.9,1.75a15.66,15.66,0,0,1-6.75-1.72,2.93,2.93,0,0,1-.12-.85,19,19,0,0,0,7.12,1.51c.05,0,4.13,0,6.69-1.36A2.22,2.22,0,0,1,482,90.71Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-452 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M480.05,10.52c-1.26-2.79-9.26-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.4,6.4,0,0,0,6.38-2.86s6.49-5-5-25.89C480.33,11.1,480.18,10.81,480.05,10.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-446 5)",
},
 //Denture 26
 {
  id: "48",
  code: "st19",
  path: "M478.5,91.36c.5-.11,2.8-16.74,3.3-24.34a7.1,7.1,0,0,1,1.4-3.65,10.25,10.25,0,0,0,1.4-5.18,4.74,4.74,0,0,0-4.84-4.66h-.46c-4,.41-11.7,1-15.6.3a5.63,5.63,0,0,0-6,3.25c-1.8,4.56-2,20.4,5.7,33.25",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-444.5 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M480.05,10.52c-1.26-2.79-9.26-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.4,6.4,0,0,0,6.38-2.86s6.49-5-5-25.89C480.33,11.1,480.18,10.81,480.05,10.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-446 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(3 6)",
},
//Bridge 26
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(1 -8)",
},
//Implant 26
{
  id: "54",
  code: "st9",
  path: "M29.23,132.12l-8.73,2.77.71,5a10.32,10.32,0,0,0,1.5,4.21c1.09,1.7,2.77,2.93,4.55-.87a15.79,15.79,0,0,0,1.31-4.89Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-7.59 -5.1)"
},

{
  id: "55",
  code: "st9",
  polygon: "22.15 122.27 12.36 125.6 11.5 119.03 22.9 115.32 22.15 122.27",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "23.31 111.45 11.01 115.29 10.21 109.12 24 104.97 23.31 111.45",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "24.45 100.82 9.68 105.02 8.92 99.08 25.17 94.06 24.45 100.82",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M33.17,94,16.05,99.65l.08-7.2a5.06,5.06,0,0,1,.31-1.69,11.72,11.72,0,0,1,3.85-5.17,6.09,6.09,0,0,1,6.69-.5,15.16,15.16,0,0,1,4.93,4.22,6.26,6.26,0,0,1,1.2,3.52C33.14,93.46,33.17,94,33.17,94Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-7.59 -5.1)"
},
{
  id: "59",
  code: "st10",
  path: "M35.39,17.17c-.25-.5-.48-1-.68-1.53-1.48-3.72-9.15-20.49-20.54-.22-.4.5-15.31,25.64,3.1,28.8.09,0,7.52,1.58,13.75-.24a22.06,22.06,0,0,1,3.76-.79C38.54,42.74,46.36,39.34,35.39,17.17Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-7.59 -5.1)"
},
{
  id: "60",
  code: "st10",
  path: "M32.59,94.8c.5-.1,2.8-16.5,3.3-24a7,7,0,0,1,1.4-3.6,10,10,0,0,0,1.4-5.1,4.72,4.72,0,0,0-4.9-4.6h-.4c-4,.4-11.7,1-15.6.3a5.64,5.64,0,0,0-6,3.2c-1.8,4.5-2.3,19.2,5.1,32,.8,1.5,3.4,2.2,5.1,2.4A37.4,37.4,0,0,0,32.59,94.8Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-7.59 -5.1)"
},
//Bone 26
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
  transforms:"scale(0.5),translate(-5 82)"
},
 //Resection 26
 {
  id: "68",
  code: "st22",
  rect: {
    x: "25.54",
    y: "16.61",
    width: "3.93",
    height: "33.43"
  },
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-16 124)  rotate(-65.8)",
},
//TeethCrown 26
{
  id: "69",
  code: "st67",
  path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.56),translate(-9 90)"
},
      ],
    },
    //  73: 'Lower Left Primary Canine',
    {
      svg_id: "22",
       tooth_number: 73,
      tooth_name: TOOTH_NUMBERS[73],
      tooth_type: " Canine",
      quadrant: "Lower Left ",
      tooth_position: 2,
      is_permanent: true,
      width:"51.2625px",
      position:"0 0 43.4 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M30.6,92l5.6-15.7c0-0.1,0.1-0.2,0.1-0.2c0.6-1.3,5.8-13.5-11-19.6l-0.4-0.1c-2.1-0.4-19.1-3.2-17.8,13.3  c0,0.3,0.1,0.7,0.3,1c3.2,6.6,5.4,13.7,6.6,21c0.2,1.2,1.3,2.1,2.5,2.1h11.8C29.3,93.7,30.3,93,30.6,92z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M30.1,93.5L29.7,115c0,1.7-0.1,3.4-0.4,5.1c-0.9,5.6-2.8,19.2-1.8,24.9c0.3,1.5-0.4,3-1.7,3.7  c-1.2,0.7-2.9,0.8-4.9-1.5c-0.4-0.4-0.6-1-0.8-1.5l-2-10.5c-0.2-0.9-0.5-1.7-0.9-2.6l-1.5-2.8c-0.5-1-0.7-2.1-0.6-3.1  c0.5-4.3,2.4-19.8-0.8-34.1C19.6,93.9,24.9,94.2,30.1,93.5z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M24,100.5c-1.6-1-4.2-2.1-5.4,1c0,0,0.3,27.8,3.1,37.8c0.2,0.6,0.8,0.9,1.4,0.8c0.5-0.1,0.8-0.6,0.8-1.1  l-0.1-11c0-2.1,0.2-4.1,0.6-6.1l0.8-4.1c0.1-0.3,0.1-0.6,0.1-0.9v-13.9C25.3,101.9,24.8,101,24,100.5z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
         // --------------------
         {
          id: "8",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M21.8,26.4l-7.3,17.3c-0.2,0.3,0,0.8,0.4,0.9c0,0,0.1,0,0.1,0c5.2,1.2,10.5,1.2,15.7,0c0.4-0.1,0.6-0.5,0.5-0.9  c0,0,0-0.1,0-0.1l-8.1-17.4c-0.2-0.3-0.6-0.5-0.9-0.3C22,26.1,21.9,26.2,21.8,26.4z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23.3,24.5l8.6-8.3c0.6-0.6,0.7-1.4,0.4-2.1C30,9.7,21.5-4,12.4,14.1c-0.4,0.8-0.2,1.7,0.4,2.3l8,8.2  C21.5,25.2,22.6,25.2,23.3,24.5z",
        },


        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M23.8,24.5l8.7-8.7c0.4-0.4,1-0.3,1.4,0.1c0.1,0.1,0.1,0.1,0.1,0.2c2.2,5.1,9.7,23.9-1.3,27.4  c-0.8,0.2-1.7-0.2-2-1l-7.3-16.3C23.2,25.7,23.3,25,23.8,24.5z",
        },
        {
          id: "11",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M22,26.3l-6.1,13.4c-0.1,0.3,0,0.6,0.3,0.7c0,0,0.1,0,0.1,0c4.3,0.9,8.8,0.9,13.1,0c0.3-0.1,0.5-0.4,0.4-0.7  c0,0,0-0.1,0-0.1L23,26.3c-0.1-0.3-0.5-0.4-0.8-0.3C22.1,26.1,22,26.2,22,26.3z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M21.6,24.1l-8.2-8.6c-0.4-0.4-1.1-0.4-1.5,0c-0.1,0.1-0.1,0.1-0.2,0.2c-2.8,4.9-12.3,23.3,1.4,27.5  c0.8,0.2,1.6-0.2,1.9-0.9l7-16.4C22.2,25.3,22.1,24.6,21.6,24.1z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M30.6,92l5.6-15.7c0-0.1,0.1-0.2,0.1-0.2c0.6-1.3,5.8-13.5-11-19.6l-0.4-0.1c-2.1-0.4-19.1-3.2-17.8,13.3  c0,0.3,0.1,0.7,0.3,1c3.2,6.6,5.4,13.7,6.6,21c0.2,1.2,1.3,2.1,2.5,2.1h11.8C29.3,93.7,30.3,93,30.6,92z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M525.13,9.52c-1.26-2.79-9.27-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.41,6.41,0,0,0,6.38-2.86s6.49-5-5.05-25.89C525.41,10.1,525.26,9.81,525.13,9.52Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-493 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M530.11,62.9v-.12c-1.11-3.63-4.22-7.54-11.69-10.25l-.4-.1c-1.79-.34-14.46-2.43-17.3,7.27-.18.71-.32,1.46-.45,2.26a18.15,18.15,0,0,0-.1,2.61,1.8,1.8,0,0,0,.52,0h4.82a12.11,12.11,0,0,0,4-.67l5.52-2a9,9,0,0,1,2.78-.51l3.7-.1a14.63,14.63,0,0,1,5.7.93A11.16,11.16,0,0,0,530.11,62.9Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-493 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M30.6,92l5.6-15.7c0-0.1,0.1-0.2,0.1-0.2c0.6-1.3,5.8-13.5-11-19.6l-0.4-0.1c-2.1-0.4-19.1-3.2-17.8,13.3  c0,0.3,0.1,0.7,0.3,1c3.2,6.6,5.4,13.7,6.6,21c0.2,1.2,1.3,2.1,2.5,2.1h11.8C29.3,93.7,30.3,93,30.6,92z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },
        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-2 0)",
        },
        {
          id: "20",
          code: "st24",
          path: "M30.6,92l5.6-15.7c0-0.1,0.1-0.2,0.1-0.2c0.6-1.3,5.8-13.5-11-19.6l-0.4-0.1c-2.1-0.4-19.1-3.2-17.8,13.3  c0,0.3,0.1,0.7,0.3,1c3.2,6.6,5.4,13.7,6.6,21c0.2,1.2,1.3,2.1,2.5,2.1h11.8C29.3,93.7,30.3,93,30.6,92z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-5 -6)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-5 -6)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-5 -6)",style: { cursor: "pointer" },position: "",},
           // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(0 -5) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(0 -5) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-14 3) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-16 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-16 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 5) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 5) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 5) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 5) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M277.65,138.32c-.07.34-.29.59-.5.57s-.39-.35-.35-.74h0c.43-3.46.81-6.94,1.08-10.39s.34-6.88.29-10.27a141,141,0,0,0-1.78-19.89,164.41,164.41,0,0,0-4.08-18.53,158.24,158.24,0,0,0-5.79-17,1.84,1.84,0,0,1,.26-1.74.56.56,0,0,1,1,.16,160,160,0,0,1,10,36.22,145.38,145.38,0,0,1,1.72,20.4c0,3.51-.08,7.06-.37,10.62a101.66,101.66,0,0,1-1.51,10.58Z",
      transforms:"translate(-250 0) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M267.16,61.55h0c-.92,1-2.07.72-2.56-.65l-3-8.31a4.66,4.66,0,0,1,.77-4.3h0c.93-1,2.07-.72,2.56.64l3,8.31A4.68,4.68,0,0,1,267.16,61.55Z",
      transforms:"translate(-250 0) ",
    },
              //PostCare
{
  id: "36",
  code: "st17",
  path: "M511.87,90.18a16.63,16.63,0,0,1,.56-3.48l1-4.58c.15-.71.52-1.16.92-1.16h5.07c.28,0,.54.27.7.71a37.08,37.08,0,0,1,2,12L521,115.39c-.11,2.07-.11,5.49-.08,6.17,0,.48.05,1.06.08,1.59a11.82,11.82,0,0,1-.17,2.85c-.52,2.8-1.61,8.82-1.86,11.26a2.23,2.23,0,0,1-1,1.65l-.07,0a1.23,1.23,0,0,1-1.77-.77c-.73-2.88-1.42-10.07-1.62-11.11C514.12,125.35,511.73,101.44,511.87,90.18Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-494.5 0)",
},
//Veneer
{
  id: "37",
  code: "st19",
  path: "M523.72,88l5.6-15.7a.35.35,0,0,1,.1-.2c.6-1.3,5.8-13.5-11-19.6l-.4-.1c-2.1-.4-19.1-3.2-17.8,13.3a2,2,0,0,0,.3,1,76.73,76.73,0,0,1,6.6,21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-493 5)",
},
//CrownPermanent 27
{
  id: "40",
  code: "st19",
  path: "M523.72,88l5.6-15.7a.35.35,0,0,1,.1-.2c.6-1.3,5.8-13.5-11-19.6l-.4-.1c-2.1-.4-19.1-3.2-17.8,13.3a2,2,0,0,0,.3,1,76.73,76.73,0,0,1,6.6,21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-493 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M525.13,9.52c-1.26-2.79-9.27-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.41,6.41,0,0,0,6.38-2.86s6.49-5-5.05-25.89C525.41,10.1,525.26,9.81,525.13,9.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-493 5)",
},
//CrownGold 27
{
  id: "43",
  code: "st43",
  path: "M509.54,79.22a123.47,123.47,0,0,0,22.22-22.66l1,1.12A121.6,121.6,0,0,1,510,80.55",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-498 5) ",
},
{
  id: "44",
  code: "st43",
  path: "M511.37,87.22c13.45-8.59,23.74-24.7,24-24.89l.45,1.45c-.74.86-11.64,16.54-24,24.7",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-498 5) ",
},
//CrownZirconia 27
{
  id: "45",   code: "st26",
   path :"M523.37,52.53l-.4-.1c-2.1-.4-19.1-3.2-17.8,13.3a2,2,0,0,0,.3,1,76.73,76.73,0,0,1,6.6,21l0,.12h16.63l5.54-15.52a.35.35,0,0,1,.1-.2C535,70.83,540.17,58.63,523.37,52.53Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-501 5) ",},
 {
  id: "46",   code: "st26",
   path :"M511.72,87.75h0a2.79,2.79,0,0,0,1.2,1.57c0,.72.06,2.5.66,2.82a15.88,15.88,0,0,0,4.11,1.51v27.89a2,2,0,0,0,0,.34l1.16,5.46c.26,1.73.81,2.8,1.43,2.8s1.16-1.07,1.42-2.8l1.16-5.46a1.32,1.32,0,0,0,0-.34V93.76c2.28-.33,4.06-1.09,4.49-2a5.36,5.36,0,0,0,.25-2.27,3.08,3.08,0,0,0,1.22-1.84m-6.67,33.74-1.13,5.33a.36.36,0,0,0,0,.1c-.16,1.07-.46,1.77-.76,1.77s-.61-.7-.76-1.77c0,0,0-.07,0-.1l-1.06-5,3.75-2.59Zm0-3.87-3.83,2.64V118.5l3.83-2.65Zm0-3.3-3.83,2.65V115.2l3.83-2.64Zm0-3.29-3.83,2.64v-1.63l3.83-2.64Zm0-3.29-3.83,2.64v-1.63l3.83-2.65Zm0-3.3L518.39,107v-1.64l3.83-2.64Zm0-3.29-3.83,2.64V102l3.83-2.65Zm0-3.3-3.83,2.65V98.74l3.83-2.65Zm0-3.29-3.83,2.65v-3.3a14.15,14.15,0,0,0,1.88.16h.14c.62,0,1.23,0,1.81-.09Zm4.71-3.85c-.24.53-2.51,1.79-6.65,1.77a14.61,14.61,0,0,1-6.53-1.74,3.54,3.54,0,0,1-.11-.85,17.89,17.89,0,0,0,6.87,1.52c.06,0,4,0,6.47-1.37A2.12,2.12,0,0,1,526.93,90.58Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-501 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M525.13,9.52c-1.26-2.79-9.27-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.41,6.41,0,0,0,6.38-2.86s6.49-5-5.05-25.89C525.41,10.1,525.26,9.81,525.13,9.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-493 5)",
},
//Denture 27
{
  id: "48",
  code: "st19",
  path: "M523.72,88l5.6-15.7a.35.35,0,0,1,.1-.2c.6-1.3,5.8-13.5-11-19.6l-.4-.1c-2.1-.4-19.1-3.2-17.8,13.3a2,2,0,0,0,.3,1,76.73,76.73,0,0,1,6.6,21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-493 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M525.13,9.52c-1.26-2.79-9.27-18.72-19.62.1,0,.16-9.15,16.59-4.63,25.08.17.26,3.45,5.29,6.55,4.22a33,33,0,0,0,15,.31,4.57,4.57,0,0,1,1.84-.1,6.41,6.41,0,0,0,6.38-2.86s6.49-5-5.05-25.89C525.41,10.1,525.26,9.81,525.13,9.52Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-493 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-1 6)",
},
//Bridge 27
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-2 -8)",
},
//Implant 27
{
  id: "54",
  code: "st9",
  path: "M27,131.94l-8.73,2.77.71,5a10.44,10.44,0,0,0,1.49,4.22c1.1,1.7,2.78,2.93,4.56-.87a15.51,15.51,0,0,0,1.3-4.89Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.39 -3.8)"
},

{
  id: "55",
  code: "st9",
  polygon: "22.09 123.38 12.29 126.71 11.44 120.15 22.84 116.43 22.09 123.38",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "23.25 112.56 10.95 116.4 10.15 110.23 23.94 106.08 23.25 112.56",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "24.39 101.93 9.62 106.14 8.86 100.19 25.11 95.17 24.39 101.93",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M30.91,93.84,13.79,99.47l.08-7.2a5,5,0,0,1,.31-1.69A11.72,11.72,0,0,1,18,85.41a6.07,6.07,0,0,1,6.68-.5,15.09,15.09,0,0,1,4.94,4.22,6.25,6.25,0,0,1,1.2,3.51C30.87,93.28,30.91,93.84,30.91,93.84Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.39 -3.8)"
},
{
  id: "59",
  code: "st10",
  path: "M33.19,15.88c-.25-.5-.47-1-.68-1.54C31,10.63,23.36-6.14,12,14.13c-.4.5-15.31,25.64,3.1,28.8.09,0,7.52,1.58,13.75-.25a23,23,0,0,1,3.76-.78C36.34,41.45,44.16,38.05,33.19,15.88Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.39 -3.8)"
},
{
  id: "60",
  code: "st10",
  path: "M30.29,92l5.6-15.7a.35.35,0,0,1,.1-.2c.6-1.3,5.8-13.5-11-19.6l-.4-.1c-2.1-.4-19.1-3.2-17.8,13.3a1.93,1.93,0,0,0,.3,1,76.66,76.66,0,0,1,6.6,21,2.57,2.57,0,0,0,2.5,2.1h11.7A2.72,2.72,0,0,0,30.29,92Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-5.39 -3.8)"
},
//Bone 27
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
  transforms:"scale(0.5),translate(-5 82)"
},
//Resection 27
{
  id: "68",
  code: "st22",
  rect: {
    x: "25.54",
    y: "16.61",
    width: "3.93",
    height: "36.43"
  },
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-22 124)  rotate(-65.8)",
},
//TeethCrown 27
{
  id: "69",
  code: "st67",
  path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.56),translate(-9 90)"
},
      ],
    },
    //74: 'Lower Left Primary First Molar',
    {
      svg_id: "21",
       tooth_number: 74,
      tooth_name: TOOTH_NUMBERS[74],
      tooth_type: "First Molarr",
      quadrant: "Lower Left  ",
      tooth_position: 3,
      is_permanent: true,
      width:"51.2625px",
      position:"0 0 43.4 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M32.5,91.3L39,77.9c0.2-0.3,0.4-0.7,0.6-1c0.4-0.6,1.1-2.2,0.6-5.2c-0.3-2-1.4-3.8-3-5l-12.4-9.5  c-1.5-1.3-3.8-1.3-5.3,0c-3.9,4.7-8.2,9.2-12.7,13.4c-1.8,1.6-2.9,4-2.9,6.5v2.4c0,1.3,0.3,2.5,0.9,3.7l4.4,8.6  c0.5,1,1.5,1.8,2.6,2.2c2.7,0.8,8.3,1.8,17.5-0.2C30.8,93.4,31.9,92.5,32.5,91.3z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M32,93c0,0,2.4,18.2,0.9,25.9c-0.4,2.3-0.6,4.7-0.6,7c0,2.5-0.1,5.4-0.2,7.7c-0.1,1.9,0,3.8,0.2,5.7  c0.4,3.7,0.5,10.2-3.9,9c-1.4-0.4-2.6-1.5-3.3-2.8C21.8,139,8.2,113.8,9.5,93C9.5,93,17.4,96.6,32,93z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M20.6,100.6l1.2-0.1c0.7,0,1.5-0.1,2.2-0.2s1.5,0,2.1,4.1c0,0.8,1.1,20,1.6,30.3c0,0.7-0.5,1.3-1.2,1.3  c-0.5,0-1-0.3-1.2-0.7c-3.1-7.2-7.8-20-7.4-32.1C17.9,101.7,19.1,100.6,20.6,100.6z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        // ------------------
        {
          id: "8",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.4,18.7c0.4-0.6-7.1-0.1-7.1-0.1h-1.8c-1.5,0.1-2.7,1.4-2.6,2.9c0,0.2,0.1,0.5,0.1,0.7  c0.5,1.7,0.6,3.4,0.3,5.2c-0.2,0.7,0.2,1.5,0.9,1.7c0.2,0.1,0.4,0.1,0.7,0c2.5-0.6,5.1-0.4,7.5,0.4c0.7,0.2,1.5-0.3,1.6-1  c0.1-0.2,0.1-0.5,0-0.7C27.2,25.3,26.6,21.5,28.4,18.7z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.8,18.8l6.7-7.2c0.5-0.5,0.4-1.3-0.1-1.8c0,0,0,0,0,0C32.1,6.9,20.8-1.4,8.2,11.4c-0.5,0.5-0.5,1.3,0,1.8  c0,0,0.1,0.1,0.2,0.1l7.8,5.6c0.3,0.2,0.8,0.3,1.2,0.2c1.3-0.5,4.7-1.4,10.2,0.1C28,19.3,28.5,19.1,28.8,18.8z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.3,29.8l-0.5-1.6c-0.6-2-0.6-4.2-0.1-6.2l0.5-1.8c0.2-0.8,0.6-1.6,1.2-2.2l5.7-6.2c0.4-0.4,1.1-0.5,1.5-0.1  c0.1,0.1,0.2,0.2,0.2,0.3c2.3,4,8.2,16.7-1.9,24.5c-0.7,0.5-1.7,0.4-2.2-0.3c0,0,0,0,0,0L28.9,31C28.6,30.6,28.4,30.2,28.3,29.8z",
        },
        {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.2,30.9c0,0-4.7-5.6-12-0.1c-0.1,0.1-0.2,0.2-0.3,0.3l-4.1,4.3c-0.9,0.9-0.9,2.4,0,3.3l4.7,4.7  c0.7,0.7,1.6,1.1,2.6,1.1h2.2c1.3,0,2.5-0.4,3.5-1.1l6.8-4.5c1.1-0.7,1.4-2.2,0.7-3.3c-0.1-0.1-0.1-0.2-0.2-0.3L28.2,30.9z",
        },

        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M15.6,31.4c0.7-0.2,4.2-8,1.3-12.2l-8.3-6.9c-0.5-0.4-1.2-0.3-1.6,0.1c-0.1,0.1-0.1,0.1-0.1,0.2  C4.7,17-1.6,31.3,9,36.5c0.6,0.3,1.3,0.2,1.7-0.3L15.6,31.4z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M32.5,91.3L39,77.9c0.2-0.3,0.4-0.7,0.6-1c0.4-0.6,1.1-2.2,0.6-5.2c-0.3-2-1.4-3.8-3-5l-12.4-9.5  c-1.5-1.3-3.8-1.3-5.3,0c-3.9,4.7-8.2,9.2-12.7,13.4c-1.8,1.6-2.9,4-2.9,6.5v2.4c0,1.3,0.3,2.5,0.9,3.7l4.4,8.6  c0.5,1,1.5,1.8,2.6,2.2c2.7,0.8,8.3,1.8,17.5-0.2C30.8,93.4,31.9,92.5,32.5,91.3z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M573.7,7.63s-9.41-14.76-26.14-1.48a9.51,9.51,0,0,0-2.34,2.75c-2.23,3.95-7.71,15,1.41,23a6.22,6.22,0,0,1,.91.94,101.46,101.46,0,0,0,7.85,7.77,2.35,2.35,0,0,0,1.61.63h2.34a3.43,3.43,0,0,0,2-.63l9.55-6.73S587.29,23.93,573.7,7.63Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-538 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M575.5,62.53,563.1,53a4,4,0,0,0-2.84-1,8.51,8.51,0,0,0-2.41.92l-.05,0a144.92,144.92,0,0,1-12.7,13.4,9,9,0,0,0-2,2.68,1.66,1.66,0,0,0,.77.1h4.88a10.77,10.77,0,0,0,4.08-.79l5.59-2.36a7.83,7.83,0,0,1,2.82-.6l3.75-.12a12.84,12.84,0,0,1,5.77,1.1,9.91,9.91,0,0,0,3.87.82l3.82,0A7.72,7.72,0,0,0,575.5,62.53Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-538 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M32.5,91.3L39,77.9c0.2-0.3,0.4-0.7,0.6-1c0.4-0.6,1.1-2.2,0.6-5.2c-0.3-2-1.4-3.8-3-5l-12.4-9.5  c-1.5-1.3-3.8-1.3-5.3,0c-3.9,4.7-8.2,9.2-12.7,13.4c-1.8,1.6-2.9,4-2.9,6.5v2.4c0,1.3,0.3,2.5,0.9,3.7l4.4,8.6  c0.5,1,1.5,1.8,2.6,2.2c2.7,0.8,8.3,1.8,17.5-0.2C30.8,93.4,31.9,92.5,32.5,91.3z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },

        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-2 -2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M32.5,91.3L39,77.9c0.2-0.3,0.4-0.7,0.6-1c0.4-0.6,1.1-2.2,0.6-5.2c-0.3-2-1.4-3.8-3-5l-12.4-9.5  c-1.5-1.3-3.8-1.3-5.3,0c-3.9,4.7-8.2,9.2-12.7,13.4c-1.8,1.6-2.9,4-2.9,6.5v2.4c0,1.3,0.3,2.5,0.9,3.7l4.4,8.6  c0.5,1,1.5,1.8,2.6,2.2c2.7,0.8,8.3,1.8,17.5-0.2C30.8,93.4,31.9,92.5,32.5,91.3z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-6 -4)",style: { cursor: "pointer" },position: "",},
        {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-6 -4)",style: { cursor: "pointer" },position: "",},
        {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-6 -4)",style: { cursor: "pointer" },position: "",},
           // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-2 -2) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-2 -2) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-14 3)",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(2  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-18 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 6) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M277.65,138.32c-.07.34-.29.59-.5.57s-.39-.35-.35-.74h0c.43-3.46.81-6.94,1.08-10.39s.34-6.88.29-10.27a141,141,0,0,0-1.78-19.89,164.41,164.41,0,0,0-4.08-18.53,158.24,158.24,0,0,0-5.79-17,1.84,1.84,0,0,1,.26-1.74.56.56,0,0,1,1,.16,160,160,0,0,1,10,36.22,145.38,145.38,0,0,1,1.72,20.4c0,3.51-.08,7.06-.37,10.62a101.66,101.66,0,0,1-1.51,10.58Z",
      transforms:"translate(-252 0) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M267.16,61.55h0c-.92,1-2.07.72-2.56-.65l-3-8.31a4.66,4.66,0,0,1,.77-4.3h0c.93-1,2.07-.72,2.56.64l3,8.31A4.68,4.68,0,0,1,267.16,61.55Z",
      transforms:"translate(-252 0) ",
    },
//PostCare
{
  id: "36",
  code: "st17",
  path: "M553.34,93a17.38,17.38,0,0,1,.77-4.16l1.45-5.49c.23-.84.73-1.39,1.3-1.39h7.06c.4,0,.77.32,1,.86a38.86,38.86,0,0,1,2.75,14.37l-1.62,26c-.18,2.91-.19,3.33,1,6.13a45.45,45.45,0,0,1,1.18,4.43c.17,1,1.1,3,1.59,4.57.21.69-1.1,1.09-1.72.53-1.22-1.1-2.86-2.61-4.17-3.93a14.26,14.26,0,0,1-3.51-5.58C557.71,121.22,553.14,106.48,553.34,93Z",
  style: { cursor: "pointer" },position: "",
 transforms:"translate(-539 0)",
},
//Veneer
{
  id: "37",
  code: "st19",
  path: "M569.83,88.6l7.47-14.87c.2-.3.4-.7.6-1,.4-.6,1.1-2.2.6-5.2a7.72,7.72,0,0,0-3-5L563.1,53a4.11,4.11,0,0,0-5.3,0,144.92,144.92,0,0,1-12.7,13.4,8.83,8.83,0,0,0-2.9,6.5v2.4a8.05,8.05,0,0,0,.9,3.7l5,9.64",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 5)",
},
//CrownPermanent 28
{
  id: "40",
  code: "st19",
  path: "M569.83,88.6l7.47-14.87c.2-.3.4-.7.6-1,.4-.6,1.1-2.2.6-5.2a7.72,7.72,0,0,0-3-5L563.1,53a4.11,4.11,0,0,0-5.3,0,144.92,144.92,0,0,1-12.7,13.4,8.83,8.83,0,0,0-2.9,6.5v2.4a8.05,8.05,0,0,0,.9,3.7l5,9.64",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M573.7,7.63s-9.41-14.76-26.14-1.48a9.51,9.51,0,0,0-2.34,2.75c-2.23,3.95-7.71,15,1.41,23a6.22,6.22,0,0,1,.91.94,101.46,101.46,0,0,0,7.85,7.77,2.35,2.35,0,0,0,1.61.63h2.34a3.43,3.43,0,0,0,2-.63l9.55-6.73S587.29,23.93,573.7,7.63Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 5)",
},
//CrownGold 28
{
  id: "43",
  code: "st43",
  path: "M551.1,86.73a116.83,116.83,0,0,0,27.68-26.19l1,1.06a115.46,115.46,0,0,1-28,26.26",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-543 5) ",
},
{
  id: "44",
  code: "st43",
  path: "M559.18,90.14c13.45-8.58,23.63-24.51,23.86-24.7l.5,1.34c-.74.86-9.95,15.25-22.35,23.41",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-543 5) ",
},
//CrownZirconia 28
{
  id: "45",   code: "st26",
   path :"M583.37,67.73a7.7,7.7,0,0,0-3-5L568,53.23a4.09,4.09,0,0,0-5.3,0A146.2,146.2,0,0,1,550,66.63a8.8,8.8,0,0,0-2.9,6.5v2.4a8.05,8.05,0,0,0,.9,3.7l4.4,8.6s0,0,0,0h23c.09-.18.17-.36.26-.52l6.5-13.4c.2-.3.4-.7.6-1C583.17,72.33,583.87,70.73,583.37,67.73Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-544 5) ",},
 {
  id: "46",   code: "st26",
   path :"M552.44,88h0a3,3,0,0,0,1.6,1.57c0,.72.07,2.5.87,2.82a25.76,25.76,0,0,0,5.48,1.51v27.88a1.34,1.34,0,0,0,0,.35l1.55,5.45c.35,1.74,1.08,2.81,1.9,2.81s1.55-1.07,1.9-2.81l1.55-5.45a1.34,1.34,0,0,0,0-.35V94c3-.33,5.41-1.09,6-2a4.13,4.13,0,0,0,.33-2.28,3.08,3.08,0,0,0,1.62-1.83m-8.88,33.73-1.52,5.34a.3.3,0,0,0,0,.1c-.21,1.07-.61,1.77-1,1.77s-.8-.7-1-1.77l0-.1-1.4-5,5-2.59Zm0-3.86-5.11,2.64v-1.63l5.11-2.65Zm0-3.3-5.11,2.65v-1.64l5.11-2.64Zm0-3.29-5.11,2.64v-1.63l5.11-2.64Zm0-3.29-5.11,2.64v-1.63l5.11-2.65Zm0-3.3-5.11,2.65v-1.64l5.11-2.64Zm0-3.29-5.11,2.64v-1.63l5.11-2.65Zm0-3.3-5.11,2.65V99l5.11-2.64Zm0-3.29-5.11,2.64V94a20.24,20.24,0,0,0,2.51.16H564c.83,0,1.64,0,2.42-.09Zm6.28-3.85c-.33.53-3.35,1.79-8.88,1.77a24.77,24.77,0,0,1-8.7-1.74A2.59,2.59,0,0,1,555,90a30.77,30.77,0,0,0,9.17,1.52c.07,0,5.32,0,8.62-1.37A1.78,1.78,0,0,1,572.73,90.81Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-544 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M573.7,7.63s-9.41-14.76-26.14-1.48a9.51,9.51,0,0,0-2.34,2.75c-2.23,3.95-7.71,15,1.41,23a6.22,6.22,0,0,1,.91.94,101.46,101.46,0,0,0,7.85,7.77,2.35,2.35,0,0,0,1.61.63h2.34a3.43,3.43,0,0,0,2-.63l9.55-6.73S587.29,23.93,573.7,7.63Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 5)",
},
//Denture 28
{
  id: "48",
  code: "st19",
  path: "M569.83,88.6l7.47-14.87c.2-.3.4-.7.6-1,.4-.6,1.1-2.2.6-5.2a7.72,7.72,0,0,0-3-5L563.1,53a4.11,4.11,0,0,0-5.3,0,144.92,144.92,0,0,1-12.7,13.4,8.83,8.83,0,0,0-2.9,6.5v2.4a8.05,8.05,0,0,0,.9,3.7l5,9.64",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M573.7,7.63s-9.41-14.76-26.14-1.48a9.51,9.51,0,0,0-2.34,2.75c-2.23,3.95-7.71,15,1.41,23a6.22,6.22,0,0,1,.91.94,101.46,101.46,0,0,0,7.85,7.77,2.35,2.35,0,0,0,1.61.63h2.34a3.43,3.43,0,0,0,2-.63l9.55-6.73S587.29,23.93,573.7,7.63Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-1 6)",
},
//Denture 28
{
  id: "48",
  code: "st19",
  path: "M569.83,88.6l7.47-14.87c.2-.3.4-.7.6-1,.4-.6,1.1-2.2.6-5.2a7.72,7.72,0,0,0-3-5L563.1,53a4.11,4.11,0,0,0-5.3,0,144.92,144.92,0,0,1-12.7,13.4,8.83,8.83,0,0,0-2.9,6.5v2.4a8.05,8.05,0,0,0,.9,3.7l5,9.64",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M573.7,7.63s-9.41-14.76-26.14-1.48a9.51,9.51,0,0,0-2.34,2.75c-2.23,3.95-7.71,15,1.41,23a6.22,6.22,0,0,1,.91.94,101.46,101.46,0,0,0,7.85,7.77,2.35,2.35,0,0,0,1.61.63h2.34a3.43,3.43,0,0,0,2-.63l9.55-6.73S587.29,23.93,573.7,7.63Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-538 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-1 6)",
},
//Bridge 28
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3 -8)",
},
//Implant 28
{
  id: "54",
  code: "st9",
  path: "M27.54,133.08l-8.73,2.77.71,5A10.49,10.49,0,0,0,21,145.07c1.1,1.7,2.78,2.93,4.56-.87a15.51,15.51,0,0,0,1.3-4.89Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-4.37 -4.23)"
},

{
  id: "55",
  code: "st9",
  polygon: "23.68 124.09 13.89 127.42 13.03 120.86 24.43 117.14 23.68 124.09",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "24.84 113.27 12.54 117.11 11.74 110.94 25.54 106.79 24.84 113.27",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "25.98 102.64 11.22 106.85 10.45 100.91 26.7 95.88 25.98 102.64",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M31.48,95,14.36,100.6l.08-7.19a5.07,5.07,0,0,1,.31-1.7,11.83,11.83,0,0,1,3.85-5.17,6.11,6.11,0,0,1,6.68-.5,15.33,15.33,0,0,1,4.94,4.22,6.26,6.26,0,0,1,1.2,3.52C31.44,94.41,31.48,95,31.48,95Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-4.37 -4.23)"
},
{
  id: "59",
  code: "st10",
  path: "M34.07,91.05l6.5-13.4c.2-.3.4-.7.6-1,.4-.6,1.1-2.2.6-5.2a7.71,7.71,0,0,0-3-5L26.37,57a4.09,4.09,0,0,0-5.3,0,145.91,145.91,0,0,1-12.7,13.4,8.78,8.78,0,0,0-2.9,6.5v2.4a8.08,8.08,0,0,0,.9,3.7l4.4,8.6a4.73,4.73,0,0,0,2.6,2.2c2.7.8,8.3,1.8,17.5-.2A5.1,5.1,0,0,0,34.07,91.05Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-4.37 -4.23)"
},
{
  id: "60",
  code: "st10",
  path: "M37,11.6S29.45-2.53,12,9.73A12.3,12.3,0,0,0,7.8,14.85c-2.08,4.68-6.63,14.94,2.83,21.37a17.93,17.93,0,0,1,3.54,3.2c.59.69,1.28,1.46,1.94,2.19a8,8,0,0,0,6.56,2.56h0a9.29,9.29,0,0,0,4.77-1.81l6.38-4.72.58-.41c2-1.37,15-11,2.57-24.76",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-4.37 -4.23)"
},
//Bone 28
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.5),translate(-5 82)"
},
//Resection 28
{
  id: "68",
  code: "st22",
  rect: {
    x: "25.54",
    y: "16.61",
    width: "3.93",
    height: "36.43"
  },
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-22 124)  rotate(-65.8)",
},
//TeethCrown 28
{
  id: "69",
  code: "st67",
  path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.59),translate(-13 84)"
},
      ],
    },
    //75: 'Lower Left Primary Second Molar',
    {
      svg_id: "20",
       tooth_number: 75,
      tooth_name: TOOTH_NUMBERS[75],
       tooth_type: "Second Molar",
      quadrant: "Lower Left  ",
      tooth_position: 4,
      is_permanent: true,
      width:"49.5125px",
      position:"0 0 41.9 172",
      paths: [
        {
          id: "1",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M33,95.7c0,0,20.7-37.6-10.7-40.7h-3.6c-0.6,0-1.2,0.1-1.8,0.3C14,56.5,4.9,61,5,71.7c0.1,1.9,0.4,3.7,1.1,5.4  C7.4,80.4,9.7,88,9.2,95.7H33z",
        },

         {
          id: "2",
          code: "st1",
          style: { cursor: "pointer" },position: "",
          path: "M33.7,97.6c-1.2,8.5-5,35.7-2.8,46.6c0.4,1.8-0.7,3.5-2.5,4c-0.4,0.1-0.8,0.1-1.3,0l-0.6-0.1  c-1.9-0.6-3.5-2.1-4.2-4c-3.3-7.8-14.3-35.2-14.5-45.9c0-1.5,1.2-2.7,2.7-2.7c0.1,0,0.2,0,0.3,0c6.9,0.5,13.8,0.6,20.7,0.1  c1.1-0.1,2,0.7,2.1,1.7C33.7,97.3,33.7,97.4,33.7,97.6z",
        },
        {
          id: "3",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "M23.3,100.1c-0.6-0.1-1.1-0.2-1.7-0.2l-2.5,0.1c-0.4,0-0.7,0.3-0.8,0.7l-0.9,8.5c-0.1,0.8,0,1.6,0.3,2.4  c1.7,4,7,17.8,6.9,29.6c0,0.4,0.3,0.8,0.8,0.8c0,0,0,0,0,0l0,0c0.4,0,0.8-0.3,0.8-0.8c0-4.1,0.1-23.7-3.6-29.3  c-0.3-0.4-0.5-0.9-0.5-1.4c-0.1-0.8,0-1.5,0.4-2.2c0.8-1.8,2.8-6.5,1.7-7.9C23.9,100.2,23.6,100.1,23.3,100.1z",
        },
        {
          id: "4",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "5",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },{
          id: "6",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "7",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },

        // --------------------
        {
          id: "8",
          code: "st4",
          style: { cursor: "pointer" },position: "",
          path: "M25.8,18.3c-2-0.1-4.3,0.2-8.4,0.1c-0.5,0-0.9,0.2-0.8,0.7c0.5,3.1,0.4,6.3-0.3,9.4c-0.1,0.5,0.2,1,0.8,1.1  c0.2,0,0.4,0,0.6-0.1c2.6-1.2,5.6-1.2,8.1,0c0.4,0.2,0.9,0.1,1.1-0.3c0.1-0.2,0.1-0.3,0.1-0.5c-0.7-3.2-0.7-6.5-0.1-9.6  C26.9,18.5,26.4,18.3,25.8,18.3z",
        },
        {
          id: "9",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M28.5,17.4c1.1-1.1,2.6-2.7,3.9-4.1c1.3-1.5,1.1-3.7-0.4-5c-0.1-0.1-0.3-0.2-0.4-0.3  c-4.2-2.6-12.2-5.8-20.7,0.9c-1.5,1.2-1.8,3.5-0.6,5c0.1,0.1,0.2,0.2,0.3,0.3l3.8,3.6c0.8,0.8,2,1.2,3.1,0.9  c2.1-0.4,5.6-0.9,7.8-0.4C26.4,18.6,27.6,18.2,28.5,17.4z",
        },
        {
          id: "10",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M30.6,35.5c-2.1-2.3-5.5-7.6-3.8-15.9c0.2-1.2,0.8-2.2,1.7-3l4.7-4.6c0.8-0.8,2.1-0.8,2.9,0  c0.1,0.1,0.2,0.3,0.3,0.4c2.6,4.6,7.1,15.3-2.7,23.3C32.8,36.5,31.4,36.4,30.6,35.5z",
        },


         {
          id: "11",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M31.2,34.7L28.3,31c-0.6-0.8-1.4-1.3-2.3-1.7c-2.2-0.7-6.5-1.5-9.8,1.4L11.3,36c-0.8,0.9-0.8,2.2,0,3.1  c3.3,3.7,12.3,11.5,20.1-1.8C31.9,36.5,31.8,35.5,31.2,34.7z",
        },
        {
          id: "12",
          code: "st0",
          style: { cursor: "pointer" },position: "",
          path: "M15.1,18.3l-5-4.8c-1-1-2.7-1-3.7,0c-0.3,0.3-0.5,0.6-0.6,1c-2.2,6.3-5.2,18.6,4,22.2c0.5,0.2,1,0,1.4-0.3  l6.4-7c0.2-0.2,0.3-0.5,0.3-0.9v-8.1c0-0.7-0.6-1.3-1.3-1.3l0,0l0,0C16,18.9,15.5,18.7,15.1,18.3z",
        },

        {
          id: "13",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "14",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "15",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "16",
          code: "st3",
          style: { cursor: "pointer" },position: "",
          path: "",
        },
        {
          id: "17",
          code: "st24",
          path: "M33,95.7c0,0,20.7-37.6-10.7-40.7h-3.6c-0.6,0-1.2,0.1-1.8,0.3C14,56.5,4.9,61,5,71.7c0.1,1.9,0.4,3.7,1.1,5.4  C7.4,80.4,9.7,88,9.2,95.7H33z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
         // Onlay
         {
          id: "38",
          code: "st19",
          path: "M619.13,4.6c-2.75-1.86-12.15-7.15-21.29.5a2.84,2.84,0,0,0-1,2l0,.68a1.62,1.62,0,0,1-1.48,1.55l-.46,0a2.72,2.72,0,0,0-2.33,1.8c-1.65,4.61-5.33,17.53,3.55,21.09A3.05,3.05,0,0,1,597.85,34c.61,1.66,2.47,4.57,8.72,6.37a7.77,7.77,0,0,0,6.69-1,18.64,18.64,0,0,0,5-6,4.18,4.18,0,0,1,1.52-1.52c3.07-1.78,12-8.58,3-23a2.77,2.77,0,0,0-2.3-1.32h0a.38.38,0,0,1-.39-.36l-.11-1A2.07,2.07,0,0,0,619.13,4.6Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-587 4)"
        },
        {
          id: "39",
          code: "st20",
          path: "M625.25,62.18c0-.37,0-.76,0-1.16-2-5.11-6.77-9.07-16.09-10h-3.6a5.61,5.61,0,0,0-1.77.29l-7.76,5.26a15.42,15.42,0,0,0-3.76,7.05,1.55,1.55,0,0,0,.83.16h4.42a10.32,10.32,0,0,0,3.7-.67l5.08-2a7.67,7.67,0,0,1,2.55-.51l3.41-.1a12.47,12.47,0,0,1,5.23.93,9.55,9.55,0,0,0,3.51.7Z",
          style: { cursor: "pointer" },position: "",
           transforms:"translate(-587 4)"
        },
        {
          id: "18",
          code: "st180",
          path: "M33,95.7c0,0,20.7-37.6-10.7-40.7h-3.6c-0.6,0-1.2,0.1-1.8,0.3C14,56.5,4.9,61,5,71.7c0.1,1.9,0.4,3.7,1.1,5.4  C7.4,80.4,9.7,88,9.2,95.7H33z",
          style: { cursor: "pointer" },position: "",
          transforms:"scale(0.95),translate(1 4)",
        },
        {
           id: "19",
          code: "st16",
          path: "M32.23,13.91s-10.95,2.35-15.14,5.15a2,2,0,0,0,.11,3.42c2.07,1.29,6.47,3.31,15.5,5.39a2.48,2.48,0,0,1,1.92,2.41h0a2.09,2.09,0,0,1-1.56,2c-3.66,1-12.86,3.19-17.58,3.19",
          style: { cursor: "pointer" },position: "",
          transforms:"translate(-3 -2)",
        },
        {
          id: "20",
          code: "st25",
          path: "M33,95.7c0,0,20.7-37.6-10.7-40.7h-3.6c-0.6,0-1.2,0.1-1.8,0.3C14,56.5,4.9,61,5,71.7c0.1,1.9,0.4,3.7,1.1,5.4  C7.4,80.4,9.7,88,9.2,95.7H33z",
          style: { cursor: "pointer" },
          position: "0 0",
          transforms:"scale(0.8),translate(6 19.5)",
        },
        {id: "21", code:"st14", polygon:"21.2,84.9 27.8,83.4 34.4,84.9 27.8,81.1",transforms:"translate(-4 -4)",style: { cursor: "pointer" },position: "",},
                  {id: "22", code:"st14", polygon:"27.8,81.1 34.4,84.9 30,80.2 27.8,73.4",transforms:"translate(-4 -4)",style: { cursor: "pointer" },position: "",},
                  {id: "23", code:"st14", polygon:"27.8,73.4 25.7,80.2 21.2,84.9 27.8,81.1",transforms:"translate(-4 -4)",style: { cursor: "pointer" },position: "",},
                    // RestoratinTemporary
 {
  id: "24",
  code: "st22",
  style: { cursor: "pointer" },position: "",
  path: "M23.56,71.65a.6.6,0,0,0-.61.6v7.1a1.82,1.82,0,0,0,.54,1.3l5,5a.61.61,0,1,0,.86-.86l-5-5a.6.6,0,0,1-.18-.44v-7.1A.61.61,0,0,0,23.56,71.65Z",
  transforms:"translate(-1 -3) ",
},
  {
    id: "25",
    code: "st22",
    style: { cursor: "pointer" },position: "",
    path: "M34.7,78.29A11.25,11.25,0,1,0,15.21,87.4H14a.62.62,0,0,0-.61.61.61.61,0,0,0,.61.61h2.74a.61.61,0,0,0,.61-.61V85.27a.61.61,0,0,0-.61-.6h0a.6.6,0,0,0-.61.6V86.6A10,10,0,1,1,19.38,89a.62.62,0,0,0-.82.26.6.6,0,0,0,.26.82l.05,0a11.31,11.31,0,0,0,4.69,1A12,12,0,0,0,25.14,91,11.26,11.26,0,0,0,34.7,78.29Z",
    transforms:"translate(-1 -3) ",
  },
  //RestoratinAmalgam
  {
    id: "26",
    code: "st26",
    style: { cursor: "pointer" },position: "",
    path: "M37,60.07l-1.54.09A3.55,3.55,0,0,0,33,61.29c-.53.56-1.16,1.29-1.8,2.05-.45.54-.89,1.09-1.27,1.59a3.42,3.42,0,0,0,.32,4.61c1.3,1.18,2.69.74,3.81.38a6.75,6.75,0,0,1,2.08-.42c1.62,0,2.59,1,2.49,4.15a.83.83,0,0,0,.68.79h0a4.4,4.4,0,0,0,3.07-.35,2.69,2.69,0,0,0,1.74-2.78c-.62-4.57,1.76-4.86,3.83-5.08a5.17,5.17,0,0,0,.79-.13,3,3,0,0,1-.21-.54c-1-3.39-2.7-4.83-4.8-5.39a3.6,3.6,0,0,0-3.3.79l-.24.22a1.32,1.32,0,0,1-1.74,0L37,60.07Z",
    transforms:"translate(-14 3) ",
  },
   //RestoratinGlassIonomer
   {
    id: "27",
    code: "st23",
    style: { cursor: "pointer" },position: "",
    path: "M29.73,62.75c-3.07-.68-5.58,2.13-6.32,3.06-2-.92-4.09-1.67-5.45-2.16-.53-.19-1-.38-1.14-.43a.43.43,0,0,0-.45.73,12.68,12.68,0,0,0,1.29.5c.66.24,1.56.56,2.56,1a8.88,8.88,0,0,0-4.51,3.33.43.43,0,0,0,.11.6.46.46,0,0,0,.25.08.41.41,0,0,0,.35-.18s2.62-3.68,5.65-3a.38.38,0,0,0,.15,0c2.25,1,4.57,2.17,5.66,3.4a.41.41,0,0,0,.32.14.48.48,0,0,0,.29-.11.44.44,0,0,0,0-.61,14.46,14.46,0,0,0-4.3-2.86c.78-.94,2.92-3.13,5.33-2.6a.43.43,0,0,0,.51-.33A.44.44,0,0,0,29.73,62.75Z",
    transforms:"translate(-1  8) ",
  },
    //RootTemporary
    {
      id: "28",
      code: "st0",
      path: "M42,39.17V35a2,2,0,0,0-4,0v5a2,2,0,0,0,.59,1.41l3,3a2,2,0,1,0,2.82-2.83Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-16 38) ",
    },
    {
      id: "29",
      code: "st0",
      path: "M75.41,38.58l-6-6a2,2,0,0,0-2.82,2.83L69.17,38H52.83a13,13,0,0,0-25.66,0H6a2,2,0,0,0,0,4H27.17a13,13,0,0,0,25.66,0H69.17l-2.58,2.58a2,2,0,1,0,2.82,2.83l6-6A2,2,0,0,0,76,40,2,2,0,0,0,75.41,38.58ZM40,49a9,9,0,1,1,9-9A9,9,0,0,1,40,49Z",
      style: { cursor: "pointer" },position: "",
      transforms:"translate(-16 38) ",
    },
     //RootCalcium
     {
      id: "30",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M29,51.82,34,50a.4.4,0,0,1,.37,0,.39.39,0,0,1,.18.34v4.53a.55.55,0,0,0,.2.43l2.19,1.84a1.06,1.06,0,0,1,.4.83l.13,8.12a.52.52,0,0,1-.41.52.54.54,0,0,1-.62-.25l-3.41-6a.55.55,0,0,0-.32-.26l-3.22-.94a1.08,1.08,0,0,1-.79-1.07l0-6a.42.42,0,0,1,.27-.39Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "31",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M26.43,46.88a.8.8,0,0,1,1.11-.17l2.31,1.69a.79.79,0,1,1-.94,1.28L26.6,48a.81.81,0,0,1-.17-1.12Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "32",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M30,44.3a.8.8,0,0,1,1,.49l1.06,3.12a.8.8,0,0,1-.49,1,.82.82,0,0,1-1-.5L29.5,45.31a.79.79,0,0,1,.49-1Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "33",
      code: "st23",
      style: { cursor: "pointer" },position: "",
      path: "M33.08,48.53a.8.8,0,0,1-.52-1l.88-2.72A.8.8,0,1,1,35,45.3L34.08,48a.79.79,0,0,1-1,.51Z",
      transforms:"translate(-11 4) ",
    },
    {
      id: "34",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M277.65,138.32c-.07.34-.29.59-.5.57s-.39-.35-.35-.74h0c.43-3.46.81-6.94,1.08-10.39s.34-6.88.29-10.27a141,141,0,0,0-1.78-19.89,164.41,164.41,0,0,0-4.08-18.53,158.24,158.24,0,0,0-5.79-17,1.84,1.84,0,0,1,.26-1.74.56.56,0,0,1,1,.16,160,160,0,0,1,10,36.22,145.38,145.38,0,0,1,1.72,20.4c0,3.51-.08,7.06-.37,10.62a101.66,101.66,0,0,1-1.51,10.58Z",
      transforms:"translate(-254 0) ",
    },
    {
      id: "35",
      code: "st34",
      style: { cursor: "pointer" },position: "",
      path: "M267.16,61.55h0c-.92,1-2.07.72-2.56-.65l-3-8.31a4.66,4.66,0,0,1,.77-4.3h0c.93-1,2.07-.72,2.56.64l3,8.31A4.68,4.68,0,0,1,267.16,61.55Z",
      transforms:"translate(-254 0) ",
    },
    //PostCare
{
  id: "36",
  code: "st17",
  path: "M602,93.06a22,22,0,0,1,.72-4.56l1.37-6c.21-.92.69-1.52,1.22-1.52H612c.37,0,.71.35.92.94a48.76,48.76,0,0,1,2.59,15.76L614,126.16c-.17,3.19-.18,3.65.9,6.73.28.79,1,4.27,1.11,4.86.16,1.1,1,3.29,1.49,5,.2.75-1,1.19-1.61.58-1.15-1.21-2.69-2.86-3.92-4.31a16.16,16.16,0,0,1-3.31-6.12C606.12,124,601.81,107.84,602,93.06Z",
        style: { cursor: "pointer" },position: "",
        transforms:"translate(-588 0)",
},
//Veneer
{
  id: "37",
  code: "st19",
  path: "M619.9,91.63l.24-.35-.24.45s20.7-37.6-10.7-40.7h-3.6a5.53,5.53,0,0,0-1.8.3c-3,1.2-12.1,5.7-11.9,16.3A16.35,16.35,0,0,0,593,73c1.2,3.3,3.6,10.9,3.1,18.6",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-587 5)",
},
//CrownPermanent 29
{
  id: "40",
  code: "st19",
  path: "M619.9,91.63l.24-.35-.24.45s20.7-37.6-10.7-40.7h-3.6a5.53,5.53,0,0,0-1.8.3c-3,1.2-12.1,5.7-11.9,16.3A16.35,16.35,0,0,0,593,73c1.2,3.3,3.6,10.9,3.1,18.6",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-588 3.5)",

},
{
  id: "41",
  code: "st19",
  path: "M619.13,4.6c-2.75-1.86-12.15-7.15-21.29.5a2.84,2.84,0,0,0-1,2l0,.68a1.62,1.62,0,0,1-1.48,1.55l-.46,0a2.72,2.72,0,0,0-2.33,1.8c-1.65,4.61-5.33,17.53,3.55,21.09A3.05,3.05,0,0,1,597.85,34c.61,1.66,2.47,4.57,8.72,6.37a7.77,7.77,0,0,0,6.69-1,18.64,18.64,0,0,0,5-6,4.18,4.18,0,0,1,1.52-1.52c3.07-1.78,12-8.58,3-23a2.77,2.77,0,0,0-2.3-1.32h0a.38.38,0,0,1-.39-.36l-.11-1A2.07,2.07,0,0,0,619.13,4.6Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-588 5)",
},
//CrownGold 29
{
  id: "43",
  code: "st43",
  path: "M600.45,87.49a130,130,0,0,0,29-28.13l.65,1.53A125.07,125.07,0,0,1,600.5,89",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-592 5) ",
},
{
  id: "44",
  code: "st43",
  path: "M608,91.26c13.45-8.58,23.41-24,23.63-24.17l.36,1.46c-.74.87-9.54,14.59-21.94,22.75",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-592 5) ",
},
//CrownZirconia 29
{
  id: "45",   code: "st26",
   path :"M601,87.85h25.66c4.28-9.68,12.59-34.33-12.58-36.82h-3.6a5.53,5.53,0,0,0-1.8.3c-3,1.2-12.1,5.7-11.9,16.3a16.35,16.35,0,0,0,1.1,5.4A52.09,52.09,0,0,1,601,87.85Z" ,style: { cursor: "pointer" },position: "",
    transforms:"translate(-596 5) ",},
 {
  id: "46",   code: "st26",
   path :"M601.11,88.4h0A3.2,3.2,0,0,0,602.89,90c0,.72.08,2.5,1,2.82A31,31,0,0,0,610,94.3v27.88a1.34,1.34,0,0,0,0,.35l1.72,5.45c.39,1.74,1.2,2.81,2.12,2.81s1.72-1.07,2.11-2.81l1.72-5.45a1.34,1.34,0,0,0,.05-.35V94.41c3.39-.33,6-1.09,6.66-2a3.75,3.75,0,0,0,.37-2.28,3.26,3.26,0,0,0,1.81-1.83M616.68,122,615,127.34a.3.3,0,0,0,0,.1c-.24,1.07-.68,1.77-1.13,1.77s-.9-.7-1.13-1.77l0-.1-1.56-5,5.56-2.59Zm0-3.86L611,120.78v-1.63l5.68-2.65Zm0-3.3L611,117.49v-1.64l5.68-2.64Zm0-3.29L611,114.19v-1.63l5.68-2.64Zm0-3.29L611,110.9v-1.63l5.68-2.65Zm0-3.3L611,107.61V106l5.68-2.64Zm0-3.29L611,104.31v-1.63l5.68-2.65Zm0-3.3L611,101V99.38l5.68-2.64Zm0-3.29L611,97.72v-3.3a24.94,24.94,0,0,0,2.79.16h.2c.92,0,1.82,0,2.69-.09Zm7-3.85c-.37.53-3.73,1.79-9.87,1.77a30.24,30.24,0,0,1-9.67-1.74,2.07,2.07,0,0,1-.16-.85,37.62,37.62,0,0,0,10.18,1.52c.08,0,5.92,0,9.59-1.37A1.72,1.72,0,0,1,623.66,91.23Z" ,style: { cursor: "pointer" },position: "",
   transforms:"translate(-596 5) ",
  },
{
  id: "47",
  code: "st26",
  path: "M619.13,4.6c-2.75-1.86-12.15-7.15-21.29.5a2.84,2.84,0,0,0-1,2l0,.68a1.62,1.62,0,0,1-1.48,1.55l-.46,0a2.72,2.72,0,0,0-2.33,1.8c-1.65,4.61-5.33,17.53,3.55,21.09A3.05,3.05,0,0,1,597.85,34c.61,1.66,2.47,4.57,8.72,6.37a7.77,7.77,0,0,0,6.69-1,18.64,18.64,0,0,0,5-6,4.18,4.18,0,0,1,1.52-1.52c3.07-1.78,12-8.58,3-23a2.77,2.77,0,0,0-2.3-1.32h0a.38.38,0,0,1-.39-.36l-.11-1A2.07,2.07,0,0,0,619.13,4.6Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-588 5)",
},
//Denture 29
{
  id: "48",
  code: "st19",
  path: "M619.9,91.63l.24-.35-.24.45s20.7-37.6-10.7-40.7h-3.6a5.53,5.53,0,0,0-1.8.3c-3,1.2-12.1,5.7-11.9,16.3A16.35,16.35,0,0,0,593,73c1.2,3.3,3.6,10.9,3.1,18.6",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-588 3.5)",

},
{
  id: "49",
  code: "st19",
  path: "M619.13,4.6c-2.75-1.86-12.15-7.15-21.29.5a2.84,2.84,0,0,0-1,2l0,.68a1.62,1.62,0,0,1-1.48,1.55l-.46,0a2.72,2.72,0,0,0-2.33,1.8c-1.65,4.61-5.33,17.53,3.55,21.09A3.05,3.05,0,0,1,597.85,34c.61,1.66,2.47,4.57,8.72,6.37a7.77,7.77,0,0,0,6.69-1,18.64,18.64,0,0,0,5-6,4.18,4.18,0,0,1,1.52-1.52c3.07-1.78,12-8.58,3-23a2.77,2.77,0,0,0-2.3-1.32h0a.38.38,0,0,1-.39-.36l-.11-1A2.07,2.07,0,0,0,619.13,4.6Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-588 5)",
},
{
  id: "50",
  code: "st50",
  polygon: "10.37 47.21 17.97 47.17 21.23 68.53 24.99 68.53 29.01 47.09 36.53 47.09 23.8 28.16 10.37 47.21",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-2 6)",
},
//Bridge 29
{
  id: "51",
  code: "st0",
  rect: {
    x: "0.5",
    y: "66.57",
    width: "62.4318",
    height: "2.8"
  },
  style: { cursor: "pointer" },position: "",
   transforms:"translate(0 12)",
},
{
  id: "52",
  code: "st0",
  path: "M17.39,87.47h0v-6a1.69,1.69,0,0,1,1.69-1.68h12a1.69,1.69,0,0,1,1.69,1.68v12a1.69,1.69,0,0,1-1.69,1.69h-12a1.69,1.69,0,0,1-1.69-1.69Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-2 -8)",
},
//Implant 29
{
  id: "54",
  code: "st9",
  path: "M27.29,134.11l-8.73,2.77.71,5a10.49,10.49,0,0,0,1.49,4.22c1.1,1.7,2.78,2.93,4.56-.87a15.51,15.51,0,0,0,1.3-4.89Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.96 -4)"
},

{
  id: "55",
  code: "st9",
  polygon: "23.84 125.35 14.04 128.68 13.19 122.12 24.59 118.4 23.84 125.35",
  style: { cursor: "pointer" },position: "",
},
{
  id: "56",
  code: "st9",
  polygon: "25 114.53 12.7 118.37 11.9 112.2 25.69 108.05 25 114.53",
  style: { cursor: "pointer" },position: "",
},
{
  id: "57",
  code: "st9",
  polygon: "26.14 103.9 11.37 108.11 10.61 102.16 26.86 97.14 26.14 103.9",
  style: { cursor: "pointer" },position: "",
},
{
  id: "58",
  code: "st9",
  path: "M31.23,96l-17.12,5.62.08-7.19a5.07,5.07,0,0,1,.31-1.7,11.83,11.83,0,0,1,3.85-5.17,6.11,6.11,0,0,1,6.68-.5A15.44,15.44,0,0,1,30,91.29a6.26,6.26,0,0,1,1.2,3.52C31.19,95.44,31.23,96,31.23,96Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.96 -4)"
},
{
  id: "59",
  code: "st10",
  path: "M33.35,95.6S54.05,58.1,22.65,55h-3.6a5.66,5.66,0,0,0-1.8.3c-3,1.2-12.1,5.7-11.9,16.3A16.24,16.24,0,0,0,6.45,77c1.2,3.3,3.6,10.9,3.1,18.6Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.96 -4)"
},
{
  id: "60",
  code: "st10",
  path: "M33.86,9.77h0S23-2.09,11,9.77c-.44,1.05-13.3,14.91-1.59,25.91A30.81,30.81,0,0,1,11.6,38c3.12,3.62,12.43,12.79,19,2.26a18,18,0,0,1,4.33-4.72c3.95-3.06,9.87-10.35,2-23.56h-.66A2.42,2.42,0,0,1,33.86,9.77Z",
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-3.96 -4)"
},
//Bone 29
{

  id: "61",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 -7) ",
},
{

  id: "62",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 1) ",
},
{

  id: "63",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 9) ",
},
{

  id: "64",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 15) ",
},
{

  id: "65",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
  transforms:"translate(0 25) ",
},
{

  id: "66",  code: "st21",
  rect: {
    x: "2",
    y: "99.6",
    width: "48",
    height: "1.5"
  },

  style: { cursor: "pointer" },position: "",
transforms:"translate(0 33) ",
},
{
  id: "67",
  code: "st67",
  path: "m94.922 58.781v28.719c0 1.9844-0.76562 3.8438-2.1719 5.25s-3.2656 2.1719-5.25 2.1719h-75c-1.9844 0-3.8438-0.76562-5.25-2.1719s-2.1719-3.2656-2.1719-5.25v-28.719c0-2.0312 1.4375-3.7969 3.4062-4.2188 3.6562-0.76562 7.2969-1.4375 10.969-1.9844 2.6406 4.5938 3.0156 6.7188 3.625 10.125 0.35938 2.0156 0.75 4.2812 1.6719 7.4688 2.8438 9.9844 7.6719 15.047 14.312 15.047 2.3438 0 4.3594-0.84375 5.8438-2.4219 3.4062-3.6562 2.9531-10.219 2.5469-16.031-0.14062-1.9844-0.26562-3.875-0.1875-5.1562 0.14062-2.5156 1.1094-3.0156 2.7344-3.0156s2.5938 0.48438 2.7344 3.0156c0.078125 1.2812-0.046875 3.1719-0.1875 5.1719-0.40625 5.7969-0.85938 12.359 2.5469 16.016 1.4844 1.5781 3.5 2.4219 5.8438 2.4219 6.6406 0 11.469-5.0625 14.312-15.047 0.92188-3.2031 1.3125-5.4688 1.6719-7.4844 0.60938-3.3906 0.98438-5.5 3.6406-10.109 3.6562 0.5625 7.3125 1.2188 10.953 1.9844 1.9688 0.42188 3.4062 2.1875 3.4062 4.2188zm-15.91-12.469c-0.77344-1.",
  style: { cursor: "pointer" },position: "",
    transforms:"scale(0.5),translate(-5 82)"
},
//Resection 29
{
  id: "68",
  code: "st22",
  rect: {
    x: "25.54",
    y: "16.61",
    width: "3.93",
    height: "36.43"
  },
  style: { cursor: "pointer" },position: "",
  transforms:"translate(-22 124)  rotate(-65.8)",
},
//TeethCrown 29
{
  id: "69",
  code: "st67",
  path: " M39.808,16.712  c-0.77-0.298-1.152-1.164-0.855-1.934c0.298-0.77,1.162-1.152,1.932-0.854c2.911,1.127,6.009,1.691,9.115,1.691  c3.106,0,6.205-0.564,9.115-1.691c0.77-0.298,1.635,0.085,1.932,0.854s-0.086,1.636-0.854,1.934c-3.273,1.266-6.737,1.9-10.193,1.9  S43.081,17.978,39.808,16.712z M21.363,44.094c-0.112-0.193-0.232-0.401-0.363-0.631c-5.087-8.935-5.698-18.821-3.013-28.313  C22.25,3.852,30.427,2.046,42.516,9.737c2.391,0.927,4.938,1.391,7.484,1.391c2.547,0,5.095-0.464,7.484-1.391  c12.088-7.69,20.266-5.885,24.528,5.413c2.684,9.492,2.075,19.379-3.014,28.313c ",
  style: { cursor: "pointer" },position: "",
   transforms:"scale(0.59),translate(-13 84)"
},
      ],
    },
   

    
  ];
