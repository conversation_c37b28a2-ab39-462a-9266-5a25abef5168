'use client';

import React from 'react';
import { <PERSON><PERSON>, Text, Button, Group, Stack, Code, Collapse } from '@mantine/core';
import { IconAlertCircle, IconRefresh, IconArrowBack, IconBug } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';

interface PaymentErrorAlertProps {
  title?: string;
  message?: string;
  errorDetails?: Record<string, unknown> | string | Error | null;
  errorCode?: string;
  onRetry?: () => void;
  onBack?: () => void;
  onContactSupport?: () => void;
  onGoToLicenseActivation?: () => void;
}

/**
 * A reusable component for displaying payment errors with detailed information
 * and action buttons for retry, go back, or contact support.
 */
export default function PaymentErrorAlert({
  title = 'Payment Failed',
  message = 'An error occurred during the payment process. Please try again.',
  errorDetails,
  errorCode,
  onRetry,
  onBack,
  onContactSupport,
  onGoToLicenseActivation,
}: PaymentErrorAlertProps) {
  // Check if this is a server error
  const isServerError = errorCode === 'HTTP_500';
  const [showDetails, { toggle }] = useDisclosure(false);

  // Format error details for display
  const formatErrorDetails = () => {
    if (!errorDetails) return null;

    try {
      // If errorDetails is an Error object
      if (errorDetails instanceof Error) {
        return `${errorDetails.name}: ${errorDetails.message}\n${errorDetails.stack || ''}`;
      }

      // If errorDetails is a plain object, format it nicely
      if (typeof errorDetails === 'object' && errorDetails !== null) {
        return JSON.stringify(errorDetails, null, 2);
      }

      // If it's a string, return it directly
      return String(errorDetails);
    } catch {
      // We don't need the error object here, just handling the exception
      return 'Error details could not be displayed';
    }
  };

  // Determine if we should show the technical details button
  const shouldShowDetailsButton = !!errorDetails && process.env.NODE_ENV === 'development';

  return (
    <Alert
      icon={<IconAlertCircle size={24} />}
      title={title}
      color="red"
      radius="md"
      withCloseButton={false}
    >
      <Stack gap="md">
        <Text size="sm">{message}</Text>

        {shouldShowDetailsButton && (
          <>
            <Button
              variant="subtle"
              color="gray"
              size="xs"
              leftSection={<IconBug size={16} />}
              onClick={toggle}

            >
              {showDetails ? 'Hide Technical Details' : 'Show Technical Details'}
            </Button>

            <Collapse in={showDetails}>
              <Code block style={{ whiteSpace: 'pre-wrap', maxHeight: '200px', overflow: 'auto' }}>
                {formatErrorDetails()}
              </Code>
            </Collapse>
          </>
        )}

        <Group justify="space-between" mt="md">
          {onBack && (
            <Button
              variant="subtle"
              color="gray"
              leftSection={<IconArrowBack size={16} />}
              onClick={onBack}
            >
              Go Back
            </Button>
          )}

          <Group justify="flex-start">
            {onContactSupport && (
              <Button
                variant="light"
                color="blue"
                onClick={onContactSupport}
              >
                Contact Support
              </Button>
            )}

            {/* Show license activation button for server errors */}
            {isServerError && onGoToLicenseActivation && (
              <Button
                variant="outline"
                color="green"
                onClick={onGoToLicenseActivation}
              >
                Go to License Activation
              </Button>
            )}

            {onRetry && (
              <Button
                color="red"
                leftSection={<IconRefresh size={16} />}
                onClick={onRetry}
              >
                Try Again
              </Button>
            )}
          </Group>
        </Group>
      </Stack>
    </Alert>
  );
}
