'use client';

import React, { useState, useCallback } from 'react';
import { Group, Stack, Text, Badge, ActionIcon, Tooltip, Modal, ColorPicker, Button } from '@mantine/core';
import { IconEdit, IconPalette, IconMedicalCross } from '@tabler/icons-react';

// Types pour les modifications anatomiques
interface ToothSection {
  id: string;
  name: string;
  type: 'crown' | 'middle' | 'root';
  parts: ToothPart[];
}

interface ToothPart {
  id: string;
  name: string;
  anatomicalCode: string; // Ex: "T11_CR_M" (Tooth 11, Crown, Mesial)
  defaultColor: string;
  currentColor: string;
  isModified: boolean;
  modificationType?: 'filling' | 'crown' | 'cavity' | 'restoration';
  walls: ToothWall[];
}

interface ToothWall {
  id: string;
  name: string;
  wallCode: string; // Ex: "T11_CR_M_BUC" (Tooth 11, Crown, Mesial, Buccal)
  isFilled: boolean;
  fillingMaterial?: string;
  fillingColor?: string;
}

interface ToothAnatomyProps {
  toothNumber: number;
  position: 'upper' | 'lower';
  quadrant: 'right' | 'left';
  onModificationChange: (modifications: ToothModification[]) => void;
}

interface ToothModification {
  id: string;
  toothNumber: number;
  partId: string;
  wallId?: string;
  modificationType: string;
  statusCode: string; // Ex: "T11_CR_M_FILLED_APPLIED"
  isApplied: boolean;
  color?: string;
  material?: string;
  notes?: string;
  timestamp: string;
}

const ToothSVGAnatomy: React.FC<ToothAnatomyProps> = ({
  toothNumber,
  position,
  quadrant,
  onModificationChange,
}) => {
  const [selectedPart, setSelectedPart] = useState<ToothPart | null>(null);
  const [colorPickerOpened, setColorPickerOpened] = useState(false);
  const [modifications, setModifications] = useState<ToothModification[]>([]);

  // Structure anatomique de la dent
  const toothAnatomy: ToothSection[] = [
    {
      id: 'crown',
      name: 'Section Coronaire',
      type: 'crown',
      parts: [
        {
          id: 'mesial',
          name: 'Mésiale',
          anatomicalCode: `T${toothNumber}_CR_M`,
          defaultColor: '#FFFFFF',
          currentColor: '#FFFFFF',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_CR_M_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_CR_M_LIN`, isFilled: false },
            { id: 'occlusal', name: 'Occlusale', wallCode: `T${toothNumber}_CR_M_OCC`, isFilled: false },
          ]
        },
        {
          id: 'distal',
          name: 'Distale',
          anatomicalCode: `T${toothNumber}_CR_D`,
          defaultColor: '#FFFFFF',
          currentColor: '#FFFFFF',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_CR_D_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_CR_D_LIN`, isFilled: false },
            { id: 'occlusal', name: 'Occlusale', wallCode: `T${toothNumber}_CR_D_OCC`, isFilled: false },
          ]
        },
        {
          id: 'central',
          name: 'Centrale',
          anatomicalCode: `T${toothNumber}_CR_C`,
          defaultColor: '#FFFFFF',
          currentColor: '#FFFFFF',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_CR_C_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_CR_C_LIN`, isFilled: false },
            { id: 'occlusal', name: 'Occlusale', wallCode: `T${toothNumber}_CR_C_OCC`, isFilled: false },
          ]
        },
      ]
    },
    {
      id: 'middle',
      name: 'Section Médiane',
      type: 'middle',
      parts: [
        {
          id: 'cervical',
          name: 'Cervicale',
          anatomicalCode: `T${toothNumber}_MD_CV`,
          defaultColor: '#F5F5DC',
          currentColor: '#F5F5DC',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_MD_CV_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_MD_CV_LIN`, isFilled: false },
          ]
        },
        {
          id: 'middle_body',
          name: 'Corps Médian',
          anatomicalCode: `T${toothNumber}_MD_BD`,
          defaultColor: '#F5F5DC',
          currentColor: '#F5F5DC',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_MD_BD_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_MD_BD_LIN`, isFilled: false },
          ]
        },
        {
          id: 'gingival',
          name: 'Gingivale',
          anatomicalCode: `T${toothNumber}_MD_GV`,
          defaultColor: '#FFB6C1',
          currentColor: '#FFB6C1',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_MD_GV_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_MD_GV_LIN`, isFilled: false },
          ]
        },
      ]
    },
    {
      id: 'root',
      name: 'Section Basale (Racines)',
      type: 'root',
      parts: [
        {
          id: 'root_mesial',
          name: 'Racine Mésiale',
          anatomicalCode: `T${toothNumber}_RT_M`,
          defaultColor: '#DEB887',
          currentColor: '#DEB887',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_RT_M_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_RT_M_LIN`, isFilled: false },
          ]
        },
        {
          id: 'root_distal',
          name: 'Racine Distale',
          anatomicalCode: `T${toothNumber}_RT_D`,
          defaultColor: '#DEB887',
          currentColor: '#DEB887',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_RT_D_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_RT_D_LIN`, isFilled: false },
          ]
        },
        {
          id: 'root_central',
          name: 'Racine Centrale',
          anatomicalCode: `T${toothNumber}_RT_C`,
          defaultColor: '#DEB887',
          currentColor: '#DEB887',
          isModified: false,
          walls: [
            { id: 'buccal', name: 'Buccale', wallCode: `T${toothNumber}_RT_C_BUC`, isFilled: false },
            { id: 'lingual', name: 'Linguale', wallCode: `T${toothNumber}_RT_C_LIN`, isFilled: false },
          ]
        },
      ]
    },
  ];

  // Générer le SVG de la dent
  const generateToothSVG = () => {
    const svgWidth = 80;
    const svgHeight = 120;
    
    return (
      <svg width={svgWidth} height={svgHeight} viewBox="0 0 80 120">
        {/* Section Coronaire (Couronne) */}
        <g id="crown-section">
          {/* Partie Mésiale */}
          <path
            id={`tooth-${toothNumber}-crown-mesial`}
            d="M15 20 Q20 10 30 15 L35 35 L20 40 Z"
            fill={toothAnatomy[0].parts[0].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[0].parts[0])}
            style={{ cursor: 'pointer' }}
          />
          
          {/* Partie Centrale */}
          <path
            id={`tooth-${toothNumber}-crown-central`}
            d="M30 15 Q40 10 50 15 L55 35 L35 35 Z"
            fill={toothAnatomy[0].parts[2].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[0].parts[2])}
            style={{ cursor: 'pointer' }}
          />
          
          {/* Partie Distale */}
          <path
            id={`tooth-${toothNumber}-crown-distal`}
            d="M50 15 Q60 10 65 20 L55 40 L55 35 Z"
            fill={toothAnatomy[0].parts[1].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[0].parts[1])}
            style={{ cursor: 'pointer' }}
          />
        </g>

        {/* Section Médiane */}
        <g id="middle-section">
          {/* Cervicale */}
          <rect
            id={`tooth-${toothNumber}-middle-cervical`}
            x="20"
            y="40"
            width="35"
            height="15"
            fill={toothAnatomy[1].parts[0].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[1].parts[0])}
            style={{ cursor: 'pointer' }}
          />
          
          {/* Corps Médian */}
          <rect
            id={`tooth-${toothNumber}-middle-body`}
            x="22"
            y="55"
            width="31"
            height="20"
            fill={toothAnatomy[1].parts[1].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[1].parts[1])}
            style={{ cursor: 'pointer' }}
          />
          
          {/* Gingivale */}
          <ellipse
            id={`tooth-${toothNumber}-middle-gingival`}
            cx="37.5"
            cy="75"
            rx="20"
            ry="8"
            fill={toothAnatomy[1].parts[2].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[1].parts[2])}
            style={{ cursor: 'pointer' }}
          />
        </g>

        {/* Section Basale (Racines) */}
        <g id="root-section">
          {/* Racine Mésiale */}
          <path
            id={`tooth-${toothNumber}-root-mesial`}
            d="M25 75 L20 110 L30 110 L32 75 Z"
            fill={toothAnatomy[2].parts[0].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[2].parts[0])}
            style={{ cursor: 'pointer' }}
          />
          
          {/* Racine Centrale */}
          <path
            id={`tooth-${toothNumber}-root-central`}
            d="M32 75 L35 115 L45 115 L48 75 Z"
            fill={toothAnatomy[2].parts[2].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[2].parts[2])}
            style={{ cursor: 'pointer' }}
          />
          
          {/* Racine Distale */}
          <path
            id={`tooth-${toothNumber}-root-distal`}
            d="M48 75 L50 110 L60 110 L55 75 Z"
            fill={toothAnatomy[2].parts[1].currentColor}
            stroke="#333"
            strokeWidth="1"
            className="tooth-part"
            onClick={() => handlePartClick(toothAnatomy[2].parts[1])}
            style={{ cursor: 'pointer' }}
          />
        </g>

        {/* Numéro de la dent */}
        <text
          x="40"
          y="95"
          textAnchor="middle"
          fontSize="10"
          fill="#333"
          fontWeight="bold"
        >
          {toothNumber}
        </text>
      </svg>
    );
  };

  // Gérer le clic sur une partie de la dent
  const handlePartClick = useCallback((part: ToothPart) => {
    setSelectedPart(part);
    setColorPickerOpened(true);
  }, []);

  // Appliquer une modification
  const applyModification = (part: ToothPart, newColor: string, modificationType: string) => {
    const statusCode = `${part.anatomicalCode}_${modificationType.toUpperCase()}_APPLIED`;
    
    const modification: ToothModification = {
      id: `${Date.now()}-${Math.random()}`,
      toothNumber,
      partId: part.id,
      modificationType,
      statusCode,
      isApplied: true,
      color: newColor,
      timestamp: new Date().toISOString(),
    };

    setModifications(prev => [...prev, modification]);
    onModificationChange([...modifications, modification]);

    // Mettre à jour la couleur de la partie
    part.currentColor = newColor;
    part.isModified = true;
  };

  return (
    <Stack gap="xs" align="center">
      {/* SVG de la dent */}
      <div style={{ position: 'relative' }}>
        {generateToothSVG()}
        
        {/* Indicateur de modifications */}
        {modifications.length > 0 && (
          <Badge
            size="xs"
            color="blue"
            style={{
              position: 'absolute',
              top: -5,
              right: -5,
            }}
          >
            {modifications.length}
          </Badge>
        )}
      </div>

      {/* Informations de la dent */}
      <Group gap="xs">
        <Text size="xs" fw={600}>
          Dent {toothNumber}
        </Text>
        <Badge size="xs" variant="light">
          {position === 'upper' ? 'Sup' : 'Inf'} {quadrant === 'right' ? 'D' : 'G'}
        </Badge>
      </Group>

      {/* Modal de modification */}
      <Modal
        opened={colorPickerOpened}
        onClose={() => setColorPickerOpened(false)}
        title={`Modifier ${selectedPart?.name} - Dent ${toothNumber}`}
        size="md"
      >
        {selectedPart && (
          <Stack gap="md">
            <Text size="sm">
              Code anatomique: <Badge variant="light">{selectedPart.anatomicalCode}</Badge>
            </Text>
            
            <ColorPicker
              value={selectedPart.currentColor}
              onChange={(color) => {
                if (selectedPart) {
                  applyModification(selectedPart, color, 'color_change');
                }
              }}
            />
            
            <Group>
              <Button
                variant="light"
                onClick={() => applyModification(selectedPart, '#C0C0C0', 'filling')}
              >
                Plombage
              </Button>
              <Button
                variant="light"
                onClick={() => applyModification(selectedPart, '#FFD700', 'crown')}
              >
                Couronne
              </Button>
              <Button
                variant="light"
                onClick={() => applyModification(selectedPart, '#8B4513', 'cavity')}
              >
                Carie
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Stack>
  );
};

export default ToothSVGAnatomy;
