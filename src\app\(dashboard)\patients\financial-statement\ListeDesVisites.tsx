import { useState, useRef, } from 'react';
import {PaymentFiltersAndBalances} from './PaymentFiltersAndBalances'
import { useDisclosure } from '@mantine/hooks';
import { IconChevronDown, IconChevronUp, IconSearch, IconSelector ,IconMinus,IconPlus, IconX} from '@tabler/icons-react';
import { DatePickerInput } from '@mantine/dates';
import {PhysicianSelect} from './PhysicianSelect'
import {PaymentModeSelect} from './PaymentModeSelect'
import {
  Center,
  Group,
  keys,
  ScrollArea,
  Table,
  Text,
  TextInput,
  UnstyledButton,
  Select,
  ActionIcon,
  Card,
  //ThemeIcon,
  Tooltip,
  Modal,
  Switch, 
   Title,
   NumberInput,
  Textarea,
    Radio, 
    Button
} from '@mantine/core';
import classes from './TableFinancier.module.css';
import { modals } from '@mantine/modals';
import Icon from '@mdi/react';
import { mdiChevronLeft,mdiChevronRight ,mdiPageLast,mdiPageFirst,mdiDetails,mdiCurrencyUsd,mdiFolderLock,mdiCash,mdiPrinter} from '@mdi/js';
import { AccountBalance } from './AccountBalance';
import { FinancialHeader } from './FinancialHeader';
import { VisitTableLegend } from './VisitTableLegend';
import { ProcedureTableVisit } from './ProcedureTableVisit';
interface Bank {
  id: number;
  value: string;
}
interface TeethProcedure {
  code: string;
  Actes: string;
  Qté: number;
  Honoraire: number;
  Total: number;
  Commentaire?: string;
}
const procedures: TeethProcedure[] = [
  {
    code: 'EXO',
    Actes: 'Extraction simple',
    Qté: 1,
    Honoraire: 500.00,
    Total: 500.00,
    Commentaire: 'Réalisé sur 51',
  },
  {
    code: 'CPS',
    Actes: 'Composite simple',
    Qté: 1,
    Honoraire: 500.00,
    Total: 500.00,
    Commentaire: 'Réalisé sur 55',
  },
  {
    code: 'COZ',
    Actes: 'Coiffe zircone',
    Qté: 1,
    Honoraire: 800,
    Total: 800,
    Commentaire: 'Réalisé sur 85',
  },
  {
    code: 'CRI',
    Actes: 'Curetage interdentaire',
    Qté: 1,
    Honoraire: 500,
    Total: 500,
    Commentaire: 'Réalisé sur toute la bouche',
  },
  {
    code: 'CLC',
    Actes: 'Collage Couronne',
    Qté: 1,
    Honoraire: 500,
    Total: 500,
    Commentaire: 'Réalisé sur 48',
  },
   {
    code: 'TCM',
    Actes: 'traitement canalaire mono',
    Qté: 1,
    Honoraire: 1000 ,
    Total: 1000 ,
    Commentaire: 'Réalisé sur 47',
  },
];
 const mockData = {
    patient: { sign: -1, value: -200.0 },
    organizations: { value: 100.0 },
    balance: { sign: -1, value: -100.0 },
  };
interface RowData {
  date: string;
  montant_du: string;
  montant_encaisse: string;
  avancement: string;
  remise: string;
  etat: string;
  resteAregler: string;
  vide: string;
 
 
}

interface ThProps {
  children: React.ReactNode;
  reversed: boolean;
  sorted: boolean;
  onSort: () => void;
}

function Th({ children, reversed, sorted, onSort }: ThProps) {
  const Icon = sorted ? (reversed ? IconChevronUp : IconChevronDown) : IconSelector;
  return (
    <Table.Th className={classes.th}>
      <UnstyledButton onClick={onSort} className={classes.control}>
        <Group justify="space-between">
          <Text fw={500} fz="sm">
            {children}
          </Text>
          <Center className={classes.icon}>
            <Icon size={16} stroke={1.5} />
          </Center>
        </Group>
      </UnstyledButton>
    </Table.Th>
  );
}

function filterData(data: RowData[], search: string) {
  const query = search.toLowerCase().trim();
  return data.filter((item) =>
    keys(data[0]).some((key) => item[key].toLowerCase().includes(query))
  );
}

function sortData(
  data: RowData[],
  payload: { sortBy: keyof RowData | null; reversed: boolean; search: string }
) {
  const { sortBy } = payload;

  if (!sortBy) {
    return filterData(data, payload.search);
  }

  return filterData(
    [...data].sort((a, b) => {
      if (payload.reversed) {
        return b[sortBy].localeCompare(a[sortBy]);
      }

      return a[sortBy].localeCompare(b[sortBy]);
    }),
    payload.search
  );
}
const data = [
  {
    date: '26/04/2025',
    montant_du: '1 300,00',
    montant_encaisse: '0,00',
    avancement: '1 300,00(100.00 %)',
    remise: '0,00',
    etat: '',
    resteAregler: '1 300,00',
    vide:'',

  },

];

type FinancialStatus = {
  is_exempt: boolean;
  has_remaining_amount: boolean;
  organization_affectation: {
    patient_remaining_amount: number;
  };
};
type Payment = {
  id: number;
  payment_date: string; // ISO date string
  payer: string;
  date: string; // ISO date string
  received_amount: number;
  plan_amount: number;
};
interface Traitement {
  showProcedures: boolean;
  onToggleProcedures: (checked: boolean) => void;
  onConfirm: () => void;
  selectedPlanId: number | null;
  payments: Payment[];
  financialStatus: FinancialStatus;
  paymentsCount: number;
  showForm: boolean;
  readOnly: boolean;
  closePlan: (e: React.MouseEvent<HTMLButtonElement>) => void;
  addPayment: () => void;
  cancel: () => void;
  patientFullname: string;
  discountValue: number;
  newPayment: boolean;
  max: number;
  onApplyDiscount?: () => void;
  banks: Bank[];
  selectedBank: Bank | null;
  onBankChange: (bank: Bank | null) => void;
  disabled?: boolean;
  onAddBank?: () => void;
  dueDateValue: Date | null;
  onDateChange: (date: Date | null) => void;
  isClosed: boolean;
  financialStatusIsExempt: boolean;
  setFinancialStatusIsExempt: (value: boolean) => void;
  entryPaymentFormInvalid: boolean;
  billingVisit: (pay?: boolean) => void;
  submit: (print?: boolean) => void;
  visitId: string;
}

export function ListeDesVisites({
  showProcedures,
  onToggleProcedures,
  onConfirm,
  patientFullname,
  discountValue,
  newPayment,
  max,
  onApplyDiscount,
  banks,

  onBankChange,
  disabled = false,
  onAddBank,
  isClosed,
  readOnly,
  financialStatusIsExempt,
  setFinancialStatusIsExempt,
  entryPaymentFormInvalid,
  billingVisit,
  submit,
  cancel,
 
}: Traitement) {
  // Données pour le tableau
  const tableData = [
    {
      date: '26/04/2025',
      montant_du: '1 300,00',
      montant_encaisse: '0,00',
      avancement: '1 300,00(100.00 %)',
      remise: '0,00',
      etat: '',
      resteAregler: '1 300,00',
      vide:'',
    },
  ];

  const [search, setSearch] = useState('');
  const [sortedData, setSortedData] = useState(tableData);
  const [sortBy, setSortBy] = useState<keyof RowData | null>(null);
  const [reverseSortDirection, setReverseSortDirection] = useState(false);
  const [Reglementopened, { open:ReglementOpen, close:ReglementClose }] = useDisclosure(false);
  const [paymentModeId, setPaymentModeId] = useState<number | null>(null);
  const [physicianId, setPhysicianId] = useState<number | null>(null);

  // Données pour le select des banques
  const bankSelectData = banks.map((bank) => ({ value: bank.id.toString(), label: bank.value }));

  // Mantine Select works with string values; map back to Bank object on change
  const handleBankChange = (val: string | null) => {
    const selectedBankObj = banks.find((b) => b.id.toString() === val) || null;
    onBankChange(selectedBankObj);
  };
    const [isVisible, setIsVisible] = useState(false); // State to control sidebar visibility

const toggleSidebar = () => {
       setIsVisible((prev) => !prev);
    };
    const openConfirmModal = () => {
    modals.openConfirmModal({
      title: 'Confirmation',
      centered: true,
      children: (
        <p>Êtes-vous sûr de vouloir clôturer ce plan de traitement&nbsp;?</p>
      ),
      labels: { confirm: 'Oui', cancel: 'Non' },
      confirmProps: { color: 'red' },
      onConfirm: onConfirm,
    });
  };
  // État pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  // Variables manquantes
  const [paymentDate, setPaymentDate] = useState<Date | null>(new Date());
  const [totalAmount, setTotalAmount] = useState<number | ''>(1300);
  const [amount, setAmount] = useState<number | ''>(1000);
  const [discount, setDiscount] = useState<number | ''>(discountValue);
  const [inputRef] = useState(useRef<HTMLInputElement>(null));

  const handleDiscountChange = (value: string | number) => {
    const numValue = typeof value === 'string' ? (value === '' ? '' : parseFloat(value)) : value;
    setDiscount(numValue);
    if (onApplyDiscount) {
      onApplyDiscount();
    }
  };

  const handleAmountChange = (value: string | number) => {
    const numValue = typeof value === 'string' ? (value === '' ? '' : parseFloat(value)) : value;
    setAmount(numValue);
  };

  const handleTotalChange = (value: string | number) => {
    const numValue = typeof value === 'string' ? (value === '' ? '' : parseFloat(value)) : value;
    setTotalAmount(numValue);
  };

  // Variables manquantes pour les composants
  const [payerValue, setPayerValue] = useState<'P' | 'T' | 'O'>('P');
  const [hasInsurance] = useState(true);
  const [physicianList] = useState([{ id: 1, full_name: "Dr. DEMO DEMO" }]);
// États pour les éléments de la facture
  const [elementsTraitement, ] = useState([
    { code: 'Aucun élément trouvé', description: '', qte: 0, prix: 0, remise: 0, montant: 0 }
  ]);
  // Calcul du total
  const total = elementsTraitement.reduce((sum, item) => sum + item.montant, 0);

  const patient = {
    id: 5,
    full_name: 'Mr CAMAL HASSAN',
    gender: 'M',
    age: 45,
    file_number: '123456',
    default_insurance: 'CNSS',
  };

  const goBack = () => {
    window.history.back();
  };

  const refreshPatientFinancialStatus = (e: React.MouseEvent) => {
    console.log('Refreshing...');
  };

  const setSorting = (field: keyof RowData) => {
    const reversed = field === sortBy ? !reverseSortDirection : false;
    setReverseSortDirection(reversed);
    setSortBy(field);
    setSortedData(sortData(data, { sortBy: field, reversed, search }));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.currentTarget;
    setSearch(value);
    setSortedData(sortData(data, { sortBy, reversed: reverseSortDirection, search: value }));
  };
  const rows = sortedData.map((row) => (
    <Table.Tr key={row.date}>
      <Table.Td>{row.date}</Table.Td>
      <Table.Td>{row.montant_du}</Table.Td>
      <Table.Td>{row.montant_encaisse}</Table.Td>
       <Table.Td>{row.avancement}</Table.Td>
      <Table.Td>{row.remise}</Table.Td>
      <Table.Td>{row.etat}
      
      {/* {row.etat === financial_status.payment_status === Réglé(e) ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#4caf50" }} />: ""};
      {row.etat === financial_status.payment_status === Réglé(e) partiellement ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#ff9800" }} />: ""};
      {row.etat === financial_status.payment_status === Non Réglé(e) ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#f44336" }} />: ""};
      {row.etat === financial_status.payment_status === Dispensé(e)/Clôturé(e) ? <ThemeIcon radius="xl" size="sm" style={{ backgroundColor: "#9e9e9e" }} />: ""}; */}
      </Table.Td>
       <Table.Td>{row.resteAregler}</Table.Td>
     <Table.Td>{row.vide}</Table.Td>
     <Table.Td>

      <Group gap="xs">
      <Tooltip label="Modifier">
      <ActionIcon
        variant="subtle"
        color="#3799ce"
        size="sm"
         onClick={(event) => {
       event.preventDefault();
       toggleSidebar();
       }}
      >
       <Icon path={mdiDetails} size={1} />
      </ActionIcon>
      </Tooltip>
      <Tooltip label="Modifier">
      <ActionIcon
        variant="subtle"
        color="#3799ce"
        size="sm"
        onClick={() => {ReglementOpen()}}
      >
       <Icon path={mdiCurrencyUsd} size={1} />
      </ActionIcon>
      </Tooltip>
      <Tooltip label="Modifier">
      <ActionIcon
        variant="subtle"
        color="#3799ce"
        size="sm"
       //onClick={() => {VouloirOpen()}}
       onClick={openConfirmModal}
      > 
       <Icon path={mdiFolderLock} size={1} />
      </ActionIcon>
      </Tooltip>
      </Group>
     </Table.Td>                      
     
    </Table.Tr>

  ));

  return (
    <>
    <Card   radius="md" withBorder w={'100%'} h={70}>
       <FinancialHeader
      patient={patient}
      patientContainer={true}
      onGoBack={goBack}
      onRefresh={refreshPatientFinancialStatus}
    />

    </Card>
      <PaymentFiltersAndBalances /> 
      <Card  radius="0"  w={'100%'} h={70} shadow="0px">
        <Group>
         <TextInput
        placeholder="Search by any field"
        mb="md"
        leftSection={<IconSearch size={16} stroke={1.5} />}
        value={search}
        onChange={handleSearchChange}
        w={'60%'}
      />
  <AccountBalance {...mockData} />
  </Group>
      </Card>
    <ScrollArea>
     
      <Table horizontalSpacing="md" verticalSpacing="xs" miw={700} layout="fixed"  striped highlightOnHover withTableBorder withColumnBorders>
        <Table.Tbody>
          <Table.Tr>
            <Th
              sorted={sortBy === 'date'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('date')}
            >
              Date
            </Th>
            <Th
              sorted={sortBy === 'montant_du'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('montant_du')}
            >
              Montant dû
            </Th>
            <Th
              sorted={sortBy === 'montant_encaisse'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('montant_encaisse')}
            >
              Montant encaissé
            </Th>
             <Th
              sorted={sortBy === 'avancement'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('avancement')}
            >
              Avancement
            </Th>
             <Th
              sorted={sortBy === 'remise'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('remise')}
            >
              Remise
            </Th>
             <Th
              sorted={sortBy === 'etat'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('etat')}
            >
              État
            </Th>
             <Th
              sorted={sortBy === 'resteAregler'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('resteAregler')}
            >
              Reste à régler
            </Th>
           <Th
              sorted={sortBy === 'vide'}
              reversed={reverseSortDirection}
              onSort={() => setSorting('vide')}
            >
              -
            </Th>
          </Table.Tr>
         
        </Table.Tbody>
        <Table.Tbody>
          {rows.length > 0 ? (
              <>
        {rows}
      </>
             
          ) : (
            <Table.Tr>
              <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                  Aucun élément trouvé.
                </Text>
              </Table.Td>
            </Table.Tr>
          )}
        </Table.Tbody>
         {/* Affichage conditionnel */}
    {isVisible && (
      <Table.Tr>
        <Table.Td colSpan={Object.keys(data[0]).length}>
          <Card>
            <ProcedureTableVisit data={procedures} />
          </Card>
        </Table.Td>
      </Table.Tr>
    )}
     <Table.Tr>
              <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                  .
                </Text>
              </Table.Td>
               <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                4100.00 MAD
                </Text>
              </Table.Td>
               <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                  0.00
                </Text>
              </Table.Td>
               <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                 0.00
                </Text>
              </Table.Td>
               <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                  .
                </Text>
              </Table.Td>
               <Table.Td colSpan={Object.keys(data[0]).length}>
                <Text fw={500} ta="center">
                  4100.00 MAD
                </Text>
              </Table.Td>
            </Table.Tr>
      </Table>

           

      <VisitTableLegend />
 
    </ScrollArea>
     <div className="border-t border-gray-300 bg-gray-50 p-3 w-full">
            <Group justify="space-between" align="center">
              <Group gap="sm" align="center">
                <Text size="sm" className="text-gray-600">Page</Text>
                <Select
                  value={currentPage.toString()}
                  onChange={(value) => setCurrentPage(Number(value) || 1)}
                  data={['1', '2', '3', '4', '5']}
                  size="xs"
                  className="w-16"
                />
                <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                 <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    
                    <Icon path={mdiPageFirst} size={1} />
                  </ActionIcon>
                   <Select
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(Number(value) || 5)}
                  data={['5', '10', '20', '50']}
                  size="xs"
                  className="w-16"
                />
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiPageLast} size={1} />
                  </ActionIcon>
                </Group>
                
               
                <Text size="sm" className="text-gray-600">0 - 0 de 0</Text>
                <Text size="sm" className="text-gray-600">K</Text>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconMinus size={12} />
                    
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <IconPlus size={12} />
                  </ActionIcon>
                </Group>
                <Group gap="xs">
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiChevronLeft} size={1} />
                  </ActionIcon>
                  <ActionIcon size="sm" variant="subtle" color="gray">
                    <Icon path={mdiChevronRight} size={1} />
                  </ActionIcon>
                </Group>
              </Group>

              <Text size="lg" fw={600} className="text-gray-800">
                Total : {total.toFixed(2)}
              </Text>
            </Group>
          </div>
         

    
       <Modal opened={Reglementopened} onClose={ReglementClose} withCloseButton={false} size="xl">
     <Group justify="space-between" px="md" py="sm" bg="gray.1">
      <Group gap="sm">
        <Icon path={mdiCash} size={1} />
        <Title order={4}>
          Recette de {patientFullname} <Text span c="gray">(Clôturer)</Text>
        </Title>
      </Group>

      <Group gap="md">
        <Switch
          label="Afficher détails"
          checked={showProcedures}
          onChange={()=>onToggleProcedures}
        />
        <ActionIcon variant="subtle" onClick={ReglementClose} aria-label="Fermer">
          <IconX />
        </ActionIcon>
      </Group>
    </Group>
    <Group gap="sm">
      <DatePickerInput
      label="Date de paiement"
      value={paymentDate}
      onChange={()=>setPaymentDate}
      readOnly={readOnly}
      clearable
      placeholder="Sélectionner une date"
      withAsterisk
      style={{ width: '100%' }}
    />
    <NumberInput
      label="Montant dû"
      value={total}
      onChange={handleTotalChange}
      disabled={readOnly}
      step={0.01}
      min={0}
      decimalScale={2}
      hideControls
      styles={{ input: { textAlign: 'right' } }}
    />
    <NumberInput
      label="Montant encaissé"
      value={amount}
      onChange={handleAmountChange}
      min={1}
      max={3900}
      step={0.01}
      hideControls
      disabled={readOnly}
      styles={{ input: { textAlign: 'right' } }}
    />
    <NumberInput
      label="Remise"
      value={discount}
      onChange={handleDiscountChange}
      min={0}
      max={max}
      step={0.01}
      hideControls
      disabled={disabled}
      styles={{ input: { textAlign: 'right' } }}
    />
    <NumberInput
      label="Remise (%)"
      value={discount}
      onChange={handleDiscountChange}
      min={0}
      max={100}
      step={0.01}
      hideControls
      disabled={disabled}
      rightSection="%"
      styles={{ input: { textAlign: 'right' } }}
    />
    </Group>
     <Radio.Group
      label="Payeur"
      value={payerValue}
      onChange={(val) => setPayerValue(val as 'P' | 'T' | 'O')}
      disabled={readOnly}
    >
      <Group>
        <Radio value="P" label="Patient" />
        <Radio value="T" label="Tiers payant" disabled={!hasInsurance || readOnly} />
        <Radio value="O" label="Autre" />
      </Group>
    </Radio.Group>
    <PhysicianSelect
  value={physicianId}
  onChange={setPhysicianId}
  physicians={physicianList} // par ex: [{ id: 1, full_name: "Dr. DEMO DEMO" }]
  readOnly={readOnly}
/>
    <Group>
      <PaymentModeSelect
  value={paymentModeId}
  onChange={setPaymentModeId}
  items={[
    { id: 1, value: 'Espèce' },
    { id: 2, value: 'Chèque' },
    { id: 3, value: 'TPE' },
    { id: 4, value: 'Traite' },
  ]}
  readOnly={readOnly}
  onAdd={() => console.log('Ajouter un mode')}
  onClear={() => setPaymentModeId(null)}
/>
 <TextInput
      label="Référence"
      placeholder="Référence du paiement"
      value={value}
      onChange={(event) => onChange(event.currentTarget.value)}
      disabled={readOnly}
    />
     <Group spacing="xs" align="flex-end">
      <Select
        label="Banque"
        placeholder="Sélectionnez une banque"
        data={data}
        value={value ? value.id.toString() : null}
        onChange={handleChange}
        searchable
        clearable
        disabled={disabled}
        nothingFound="Aucune banque trouvée"
        style={{ minWidth: 250 }}
      />
      {!disabled && onAddBank && (
        <>
          {value && (
            <ActionIcon
              color="red"
              variant="filled"
              size="sm"
              onClick={() => onChange(null)}
              aria-label="Annuler la sélection"
            >
              <IconX size={16} />
            </ActionIcon>
          )}
          <ActionIcon
            color="blue"
            variant="filled"
            size="sm"
            onClick={onAddBank}
            aria-label="Ajouter une banque"
          >
            <IconPlus size={16} />
          </ActionIcon>
        </>
      )}
    </Group>
    <DatePickerInput
      label="Date d'échéance"
      placeholder="Sélectionnez une date"
      value={value}
      onChange={onChange}
      disabled={disabled}
      clearable
      style={{ maxWidth: 300 }}
    />
    </Group>
     <Textarea
      label="Commentaire"
      value={value}
      onChange={(event) => onChange(event.currentTarget.value)}
      disabled={disabled}
      id="input_487"
      minRows={1}
      style={{ height: 54 }}
      aria-invalid={false}
    />
    <Group gap="xs" justify="flex-start" wrap="nowrap">
      {!isClosed && (
        <Switch
          label="Dispenser"
          checked={financialStatusIsExempt}
          onChange={(event) => setFinancialStatusIsExempt(event.currentTarget.checked)}
          disabled={readOnly || newPayment}
          color="yellow"
          aria-label="is exempt"
          tabIndex={0}
        />
      )}

      {/* Buttons that are shown only if showProcedures is true (hidden in original) */}
      {showProcedures && (
        <>
          {/* These were ng-hide in original, so hidden */}
          {/* Uncomment if needed */}
          {/*
          <Button
            leftIcon={<IconPrinter size={16} />}
            variant="filled"
            color="blue"
            disabled
            aria-label="print a bill"
            style={{ display: 'none' }} // hidden as in original
          >
            Note d'honoraires
          </Button>

          <Button
            leftIcon={<IconPrinter size={16} />}
            variant="filled"
            color="blue"
            disabled
            aria-label="print an estimate"
            style={{ display: 'none' }} // hidden as in original
          >
            Devis
          </Button>
          */}
        </>
      )}

      {!readOnly && (
        <>
          <Button
            leftSection={<Icon path={mdiPrinter} size={1} />}
            variant="filled"
            color="blue"
            onClick={() => {
              submit(true);
              // Normally here you would trigger printing logic with visitId etc.
            }}
            disabled={entryPaymentFormInvalid}
            aria-label="print a bill"
          >
            Reçu
          </Button>

          <Button
            variant="filled"
            color="blue"
            onClick={() => billingVisit(true)}
            aria-label="save"
          >
            Réglé/Facturer
          </Button>

          <Button
            variant="filled"
            color="blue"
            onClick={() => billingVisit()}
            aria-label="save"
          >
            Facturer
          </Button>

          <Button
            type="submit"
            variant="filled"
            color="blue"
            disabled={entryPaymentFormInvalid}
            aria-label="save"
          >
            Enregistrer
          </Button>
        </>
      )}

      <Button
        variant="outline"
        color="red"
        onClick={cancel}
        aria-label="cancel"
      >
        Annuler
      </Button>
    </Group>
      </Modal>
       
          </>
  );
}

