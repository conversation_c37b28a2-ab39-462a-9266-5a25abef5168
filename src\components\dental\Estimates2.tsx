
import HistoryPlans from "./treatments/HistoryPlans";
import ToothManagement from "./ToothManagement";

import React, { useState,  useRef } from "react";
import {Tabs, Card, Button, Text, Group, Alert, Menu, ActionIcon, rem, Badge, Loader, Tooltip} from "@mantine/core";
import { IconDots, IconEye, IconFileZip, IconTrash, IconArrowBadgeDown, IconMedicalCross, IconDatabase, IconDeviceFloppy, IconCloudCheck, IconRefresh, IconPlus, IconCalendar} from '@tabler/icons-react';
import { IconHeart } from "@tabler/icons-react";
import { EstimatesTabsRef } from './EstimatesTabs';
import { EstimatesTabs} from './EstimatesTabs';
import { useCounter } from '@mantine/hooks';
import { useEstimateReactive } from '../../../hooks/useEstimateReactive';
import { notifications } from '@mantine/notifications';
import Adulte from"./Adulte"
import Ans13EtDemi from"./13_ans_et_demi"
import Ans12 from"./Moins-6ans"
import Ans7 from"./Ans7"
import Moins6ans from"./Moins-6ans"
const Estimates2 = () => {
// function Estimates() {
  const icon = <IconHeart />;
  // Using a constant value for alert display
  const [isAlertVisible, setIsAlertVisible] = useState(false);
  // Utility function to split array into chunks
  const [count, handlers] = useCounter(0, { min: 0.00, max: 10.00});

  // Hook réactif pour la gestion des estimations
  const {
    session,
    isLoading,
    isSaving,
    lastSaved,
    initializeSession,
    saveModification,
    forceSave,
    resetSession,
    modifications,

  } = useEstimateReactive({
    patientId: 'patient-123', // TODO: Récupérer l'ID du patient actuel
    sessionName: 'Session Devis',
    autoSave: true,
    autoSaveDelay: 2000,
    autoInitialize: false, // NOUVEAU: Désactiver l'initialisation automatique
  });

  // SUPPRIMÉ: Initialisation automatique au montage
  // La session sera créée seulement quand l'utilisateur fait une action

  // Fonction pour créer une nouvelle session
  const handleCreateSession = async () => {
    try {
      await initializeSession();
      notifications.show({
        title: '✅ Session créée !',
        message: 'Nouvelle session d\'estimation créée avec succès.',
        color: 'green',
        autoClose: 3000,
      });
    } catch (error) {
      console.error('Erreur lors de la création de session:', error);
      notifications.show({
        title: '❌ Erreur',
        message: 'Impossible de créer la session. Veuillez réessayer.',
        color: 'red',
        autoClose: 5000,
      });
    }
  };

  // Référence pour déclencher la sauvegarde des onglets
  const estimatesTabsRef = useRef<EstimatesTabsRef | null>(null);

  // Fonction pour sauvegarder avec message
  const handleSaveWithMessage = async () => {
    try {
      // Sauvegarder les modifications du système réactif principal
      await forceSave();

      // Déclencher la sauvegarde des onglets spécialisés si disponible
      if (estimatesTabsRef.current?.triggerSave) {
        await estimatesTabsRef.current.triggerSave();
      }

      notifications.show({
        title: '✅ Sauvegarde réussie !',
        message: `${modifications.length} modifications ont été sauvegardées avec succès.`,
        color: 'green',
        autoClose: 3000,
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      notifications.show({
        title: '❌ Erreur de sauvegarde',
        message: 'Impossible de sauvegarder les modifications. Veuillez réessayer.',
        color: 'red',
        autoClose: 5000,
      });
    }
  };
  return (
    <>
    <div className="mt-2 flex w-full">
      <div className="mr-1 w-3/12 bg-[var(--mantine-color-bg)]">
        <div className="h-full p-1">
          <HistoryPlans />
        </div>
      </div>
      <div className="w-11/12 bg-[var(--mantine-color-bg)] ">
      {isAlertVisible && (
      <div className="flex justify-between border-2 p-1 px-1">
        <Alert
          title="SelectTeeth"
          icon={icon}
          withCloseButton
          onClose={() => setIsAlertVisible(false)} // Close alert
          w={"100%"}
          bg={"cyan"}
          color="white"
        ></Alert>
      </div>
    )}

    <Card mt={2} className="border-base-200 border-2" p={0}>
    <Group justify="space-between" >
      <div className="ml-2 my-[10px]">
      <Group>
        <Text fw={500} size="lg">
          Estimate(3)
        </Text>
        {/* Statut de la session */}
        {session ? (
          <>
            <Badge
              variant="light"
              color={isSaving ? "yellow" : "green"}
              leftSection={isSaving ? <Loader size="xs" /> : <IconCloudCheck size={12} />}
            >
              {isSaving ? 'Sauvegarde...' : `${modifications.length} modifications`}
            </Badge>
            {lastSaved && (
              <Text size="xs" c="dimmed">
                Dernière sauvegarde: {lastSaved.toLocaleTimeString()}
              </Text>
            )}
          </>
        ) : (
          <Button
            variant="light"
            color="blue"
            size="sm"
            leftSection={<IconPlus size={14} />}
            onClick={handleCreateSession}
            loading={isLoading}
          >
            Créer une session
          </Button>
        )}
      </Group>
        </div>

          <div className="flex space-x-4 mr-2">
          <Button variant="default" onClick={handlers.increment} className="border-none">Discount</Button>
          <Button variant="default" >{count}</Button>

          {/* Boutons de sauvegarde */}
          <Tooltip label="Sauvegarder maintenant">
            <Button
              variant="light"
              color="blue"
              leftSection={<IconDeviceFloppy size={14} />}
              onClick={handleSaveWithMessage}
              loading={isSaving}
              disabled={!session || modifications.length === 0}
            >
              Sauvegarder
            </Button>
          </Tooltip>

          <Tooltip label="Réinitialiser la session">
            <Button
              variant="light"
              color="red"
              leftSection={<IconRefresh size={14} />}
              onClick={resetSession}
              disabled={!session}
            >
              Reset
            </Button>
          </Tooltip>
          <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <Button variant="default" rightSection={<IconArrowBadgeDown size={14} />}>Set status</Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Download zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Preview all
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Delete all
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          <Menu withinPortal position="bottom-end" shadow="sm">
            <Menu.Target>
              <ActionIcon variant="subtle" color="gray">
                <IconDots style={{ width: rem(16), height: rem(16) }} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                Download zip
              </Menu.Item>
              <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                Preview all
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                color="red"
              >
                Delete all
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          </div>

    </Group>
    {/* <Divider my="md" /> */}
      <Tabs variant="pills" defaultValue="estimates" >
        <Tabs.List>
          <Tabs.Tab value="estimates" leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
            Devis Traditionnels
          </Tabs.Tab>
          <Tabs.Tab value="teeth" leftSection={<IconMedicalCross style={{ width: rem(14), height: rem(14) }} />}>
            Gestion des Dents
          </Tabs.Tab>
          <Tabs.Tab value="database" leftSection={<IconDatabase style={{ width: rem(14), height: rem(14) }} />}>
            Base de Données
          </Tabs.Tab>
          <Tabs.Tab value="age-range" leftSection={<IconCalendar style={{ width: rem(14), height: rem(14) }} />}>
            Tranche d&apos;Âge
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="estimates">
          <div className=" w-full">
            {/* Nouveau système modulaire */}
            {/* <EstimatesTabsNew
              ref={estimatesTabsRef}
              onModificationChange={saveModification}
              session={session}
              isLoading={isLoading}
            /> */}

            {/* Ancien système (pour comparaison) */}

            <EstimatesTabs
              ref={estimatesTabsRef}
              onModificationChange={saveModification}
              session={session}
              isLoading={isLoading}
            />

          </div>
        </Tabs.Panel>

        <Tabs.Panel value="teeth">
          <div className="w-full mt-4">
            <ToothManagement />
          </div>
        </Tabs.Panel>

        <Tabs.Panel value="database">
          <div className="w-full mt-4 p-4">
            <Card withBorder p="xl">
              <Group justify="center" mb="md">
                <IconDatabase size={48} color="blue" />
              </Group>
              <Text ta="center" size="lg" fw={600} mb="md">
                Accès à la Base de Données Backend
              </Text>
              <Text ta="center" c="dimmed" mb="xl">
                Gérez directement les données des dents via l&apos;interface d&apos;administration Django
              </Text>
              <Group justify="center">
                <Button
                  size="lg"
                  leftSection={<IconEye size={20} />}
                  onClick={() => window.open('http://127.0.0.1:8000/admin/dentistry/tooth/', '_blank')}
                >
                  Ouvrir Admin Panel
                </Button>
              </Group>
            </Card>
          </div>
        </Tabs.Panel>

        <Tabs.Panel value="age-range">
          <div className="w-full mt-4 p-4">
            <Card withBorder p="xl">
              <Tabs variant="pills" defaultValue="Moins de 6 ans">
                <Tabs.List justify="center">
                  <Tabs.Tab value="Moins de 6 ans">Moins de 6 ans</Tabs.Tab>
                  <Tabs.Tab value="Moins de 7.5 ans">Moins de 7.5 ans</Tabs.Tab>
                  <Tabs.Tab value="Moins de 12 ans">Moins de 12 ans</Tabs.Tab>
                  <Tabs.Tab value="Moins de 13.5 ans">Moins de 13.5 ans</Tabs.Tab>
                  <Tabs.Tab value="Adulte">Adulte</Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="Moins de 6 ans">
                   <Moins6ans/>
                </Tabs.Panel>
                <Tabs.Panel value="Moins de 7.5 ans">
                 <Ans7/>
                </Tabs.Panel>
                <Tabs.Panel value="Moins de 12 ans">
                 <Ans12/>
                </Tabs.Panel>
                <Tabs.Panel value="Moins de 13.5 ans">
                  <Ans13EtDemi/>
                </Tabs.Panel>
                <Tabs.Panel value="Adulte">
                  <Adulte/>
                </Tabs.Panel>
              </Tabs>
            </Card>
          </div>
        </Tabs.Panel>
      </Tabs>

    </Card>
      </div>

    </div>
  </>
  );
}
export default Estimates2;


