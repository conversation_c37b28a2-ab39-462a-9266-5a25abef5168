import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';

import General from './General';
import Visite from './Visite';
import Flux from './Flux';
import Prescriptions from './Prescriptions';
import Pharmacie from './Pharmacie';
import Facturation from './Facturation';
import AccueilEtAgenda from './AccueilEtAgenda'
import GestionDesListes from './GestionDesListes'
import GestionDesEmplacements from './GestionDesEmplacements'
// Mapping des sous-onglets pour Vente
const subtabMapping: { [key: string]: string } = {
  'general': 'general',
  'Visite': 'Visite',
  'Flux': 'Flux',
  'Prescriptions': 'Prescriptions',
  'Pharmacie': 'Pharmacie',
  'Facturation': 'Facturation',
  'AccueilEtAgenda':'AccueilEtAgenda',
  'GestionDesListes':'GestionDesListes',
  'GestionDesEmplacements':'GestionDesEmplacements'
};

const Parameters_de_base = () => {
  const [activeTab, setActiveTab] = useState('General');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'General')}
      w={"100%"}
      mt={10}
    >
         <Tabs.List>
           <Tabs.Tab value="General" leftSection={<IconPhoto size={12} />}>
             General
           </Tabs.Tab>
           <Tabs.Tab value="Visite" leftSection={<IconMessageCircle size={12} />}>
             Visite
           </Tabs.Tab>
           <Tabs.Tab value="Flux" leftSection={<IconSettings size={12} />}>
             Flux
           </Tabs.Tab>
           <Tabs.Tab value="Prescriptions" leftSection={<IconSettings size={12} />}>
             Prescriptions
           </Tabs.Tab>
           <Tabs.Tab value="Pharmacie" leftSection={<IconSettings size={12} />}>
             Pharmacie
           </Tabs.Tab>
           <Tabs.Tab value="Facturation" leftSection={<IconSettings size={12} />}>
             Facturation
           </Tabs.Tab>
             <Tabs.Tab value="AccueilEtAgenda" leftSection={<IconSettings size={12} />}>
             Accueil et agenda
           </Tabs.Tab>
             <Tabs.Tab value="GestionDesListes" leftSection={<IconSettings size={12} />}>
             Gestion des listes
           </Tabs.Tab>
             <Tabs.Tab value="GestionDesEmplacements" leftSection={<IconSettings size={12} />}>
             Gestion des emplacements
           </Tabs.Tab>
         </Tabs.List>
   
         <Tabs.Panel value="General" ml={20}>
           <General/>
         </Tabs.Panel>
   
         <Tabs.Panel value="Visite" ml={20}>
           <Visite/>
         </Tabs.Panel>
   
         <Tabs.Panel value="Flux" ml={20}>
           <Flux/>
         </Tabs.Panel>

         <Tabs.Panel value="Prescriptions" ml={20}>
           <Prescriptions/>
         </Tabs.Panel>
         <Tabs.Panel value="Pharmacie" ml={20}>
           <Pharmacie/>
         </Tabs.Panel>
         <Tabs.Panel value="Facturation" ml={20}>
           <Facturation/>
         </Tabs.Panel>
          <Tabs.Panel value="AccueilEtAgenda" ml={20}>
           <AccueilEtAgenda/>
         </Tabs.Panel>
          <Tabs.Panel value="GestionDesListes" ml={20}>
           <GestionDesListes/>
         </Tabs.Panel>
         <Tabs.Panel value="GestionDesEmplacements" ml={20}>
           <GestionDesEmplacements/>
         </Tabs.Panel>
       </Tabs>
  )
};

export default Parameters_de_base;
