import React, { useState } from 'react';
import {
  Card,
  Text,
  Tabs,
  Stack,
  Group,
  Table,
  Button,
  Modal,
  TextInput,
  Select,
  Alert,
} from '@mantine/core';
import {
  IconFileText,
  IconPlus,
  IconX,
  IconSearch,
  IconInfoCircle,
} from '@tabler/icons-react';

// Interface pour les comptes rendus
interface CompteRendu {
  id: string;
  code: string;
  designation: string;
  prefere: boolean;
  desactive: boolean;
}

const ComptesRendus: React.FC = () => {
  // État pour l'onglet actif
  const [activeTab, setActiveTab] = useState<string>('gestion');

  // État pour les données
  const [comptesRendus, setComptesRendus] = useState<CompteRendu[]>([]);

  // États pour les modals
  const [newItemModalOpened, setNewItemModalOpened] = useState(false);

  // États pour les formulaires
  const [newItemForm, setNewItemForm] = useState({
    code: '',
    designation: '',
    procedure: '',
    templateFormulaire: '',
  });

  // Options pour les selects
  const procedureOptions = [
    { value: 'consultation', label: 'Consultation' },
    { value: 'examen', label: 'Examen' },
    { value: 'intervention', label: 'Intervention' },
    { value: 'suivi', label: 'Suivi' },
  ];

  const templateOptions = [
    { value: 'standard', label: 'Template Standard' },
    { value: 'detaille', label: 'Template Détaillé' },
    { value: 'simple', label: 'Template Simple' },
    { value: 'personnalise', label: 'Template Personnalisé' },
  ];

  // Fonction pour gérer la soumission du nouveau compte rendu
  const handleNewItemSubmit = () => {
    if (newItemForm.code.trim() && newItemForm.designation.trim()) {
      const newItem: CompteRendu = {
        id: Date.now().toString(),
        code: newItemForm.code,
        designation: newItemForm.designation,
        prefere: false,
        desactive: false,
      };
      setComptesRendus(prev => [...prev, newItem]);
      setNewItemForm({ code: '', designation: '', procedure: '', templateFormulaire: '' });
      setNewItemModalOpened(false);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-blue-500 text-white px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <IconFileText size={20} />
            <Text size="lg" fw={500}>
              Comptes rendus
            </Text>
          </div>
          {activeTab === 'liste' && (
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => setNewItemModalOpened(true)}
              className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              variant="outline"
            >
              Nouveau
            </Button>
          )}
        </div>
      </div>

      {/* Contenu principal avec onglets */}
      <div className="flex-1">
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'gestion')} className="h-full">
          <Tabs.List className="bg-white border-b border-gray-200 px-6">
            <Tabs.Tab value="gestion" className="text-gray-600 hover:text-blue-600">
              Gestion des modèles
            </Tabs.Tab>
            <Tabs.Tab value="liste" className="text-gray-600 hover:text-blue-600">
              Liste
            </Tabs.Tab>
          </Tabs.List>

          {/* Onglet Gestion des modèles */}
          <Tabs.Panel value="gestion" className="p-6">
            <Card shadow="sm" padding="lg" radius="md" className="bg-white h-full">
              <Stack gap="lg">
                {/* Barre de recherche avec icône */}
                <div className="flex items-center gap-2">
                  <div className="bg-blue-500 p-2 rounded">
                    <IconPlus size={16} className="text-white" />
                  </div>
                  <div className="flex-1 relative">
                    <div className="bg-blue-500 text-white px-3 py-2 rounded flex items-center gap-2">
                      <Text size="sm" fw={500}>Modèles</Text>
                      <IconSearch size={16} />
                    </div>
                  </div>
                </div>

                {/* Message d'information */}
                <Alert
                  icon={<IconInfoCircle size={16} />}
                  color="blue"
                  variant="light"
                  className="bg-blue-50 border-blue-200"
                >
                  <Text size="sm" className="text-blue-700">
                    Aucun élément trouvé.
                  </Text>
                </Alert>

                {/* Message d'avertissement */}
                <Alert
                  icon={<IconInfoCircle size={16} />}
                  color="yellow"
                  variant="light"
                  className="bg-yellow-50 border-yellow-200"
                >
                  <Text size="sm" className="text-yellow-700">
                    Aucun modèle sélectionné
                  </Text>
                </Alert>
              </Stack>
            </Card>
          </Tabs.Panel>

          {/* Onglet Liste */}
          <Tabs.Panel value="liste" className="p-6">
            <Card shadow="sm" padding="lg" radius="md" className="bg-white h-full">
              <Stack gap="lg">
                {/* Tableau des comptes rendus */}
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Code</Table.Th>
                      <Table.Th>Désignation</Table.Th>
                      <Table.Th className="text-center">Préféré</Table.Th>
                      <Table.Th className="text-center">Désactivé</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {comptesRendus.length === 0 ? (
                      <Table.Tr>
                        <Table.Td colSpan={4} className="text-center text-blue-600 py-8">
                          Aucun élément trouvé.
                        </Table.Td>
                      </Table.Tr>
                    ) : (
                      comptesRendus.map((item) => (
                        <Table.Tr key={item.id}>
                          <Table.Td>{item.code}</Table.Td>
                          <Table.Td>{item.designation}</Table.Td>
                          <Table.Td className="text-center">
                            {item.prefere ? '✓' : ''}
                          </Table.Td>
                          <Table.Td className="text-center">
                            {item.desactive ? '✓' : ''}
                          </Table.Td>
                        </Table.Tr>
                      ))
                    )}
                  </Table.Tbody>
                </Table>
              </Stack>
            </Card>
          </Tabs.Panel>
        </Tabs>
      </div>

      {/* Modal Nouveau Compte Rendu */}
      <Modal
        opened={newItemModalOpened}
        onClose={() => setNewItemModalOpened(false)}
        title={
          <div className="flex items-center justify-between w-full bg-blue-500 text-white px-4 py-3 -m-4 mb-4">
            <div className="flex items-center gap-2">
              <IconPlus size={20} />
              <Text size="lg" fw={500}>Comptes rendus</Text>
            </div>
            <IconX 
              size={20} 
              className="cursor-pointer hover:bg-white/20 rounded p-1" 
              onClick={() => setNewItemModalOpened(false)}
            />
          </div>
        }
        size="lg"
        withCloseButton={false}
        styles={{
          header: { backgroundColor: '#3b82f6', margin: 0, padding: 0 },
          title: { width: '100%', margin: 0 },
        }}
      >
        <Stack gap="md" className="mt-4">
          <Group grow>
            <TextInput
              label={<span>Code <span className="text-red-500">*</span></span>}
              value={newItemForm.code}
              onChange={(event) => setNewItemForm(prev => ({ ...prev, code: event.currentTarget.value }))}
              required
              styles={{
                input: { 
                  borderBottom: '2px solid #ef4444', 
                  borderTop: 'none', 
                  borderLeft: 'none', 
                  borderRight: 'none', 
                  borderRadius: 0,
                  backgroundColor: 'transparent'
                }
              }}
            />
            <TextInput
              label={<span>Désignation <span className="text-red-500">*</span></span>}
              value={newItemForm.designation}
              onChange={(event) => setNewItemForm(prev => ({ ...prev, designation: event.currentTarget.value }))}
              required
              styles={{
                input: { 
                  borderBottom: '2px solid #ef4444', 
                  borderTop: 'none', 
                  borderLeft: 'none', 
                  borderRight: 'none', 
                  borderRadius: 0,
                  backgroundColor: 'transparent'
                }
              }}
            />
          </Group>
          
          <Select
            label="Procédure"
            placeholder="Sélectionnez une procédure"
            data={procedureOptions}
            value={newItemForm.procedure}
            onChange={(value) => setNewItemForm(prev => ({ ...prev, procedure: value || '' }))}
            rightSection={<IconPlus size={16} className="text-gray-400" />}
          />
          
          <Select
            label="Template formulaire"
            placeholder="Sélectionnez un template"
            data={templateOptions}
            value={newItemForm.templateFormulaire}
            onChange={(value) => setNewItemForm(prev => ({ ...prev, templateFormulaire: value || '' }))}
            rightSection={<IconPlus size={16} className="text-gray-400" />}
          />

          <Group justify="flex-end" mt="xl">
            <Button
              variant="filled"
              color="gray"
              onClick={handleNewItemSubmit}
              disabled={!newItemForm.code.trim() || !newItemForm.designation.trim()}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={() => setNewItemModalOpened(false)}
            >
              Annuler
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default ComptesRendus;
