
import { ActionIcon, createTheme, Loader , rem } from "@mantine/core";
const theme = createTheme({
  breakpoints: {
    xs: "36em",
    sm: "48em",
    md: "62em",
    lg: "75em",
    xl: "88em",
  },
  colors: {
    blue: [
      '#e7f5ff',
      '#d0ebff',
      '#bdc2de',
      '#4dabf7',
      '#339af0',
      '#228be6',
      '#3799ce',
      '#4c5897',
      '#3799ce',//'#06b1bd',
      '#1971c2',
    ],
    brand: [
      "#e6f7ff",
      "#bae7ff",
      "#91d5ff",
      "#69c0ff",
      "#40a9ff",
      "#1890ff",
      "#096dd9",
      "#0050b3",
      "#003a8c",
      "#002766",
    ],
  },
  primaryColor: 'blue',
  primaryShade: 5,
  defaultRadius: 'md',
  focusRing: "always",
  // fontFamily: 'Arial, Helvetica, sans-serif',
  fontFamily: "DM_Sans,Open Sans, sans-serif",
  headings: {
    // fontFamily: 'Arial, Helvetica, sans-serif',
     fontFamily: "DM_Sans,Open Sans, sans-serif" ,
    sizes: {
      h1: { fontSize: rem(36) },
      h2: { fontSize: rem(30) },
      h3: { fontSize: rem(24) },
      h4: { fontSize: rem(20) },
      h5: { fontSize: rem(18) },
      h6: { fontSize: rem(16) },
    },
  },
  // Default color scheme is set in the MantineProvider
  components: {
    Button: {
      defaultProps: {
        radius: 'md',
      },
    },
    Card: {
      defaultProps: {
        radius: 'md',
        shadow: 'sm',
      },
    },
    ActionIcon: {
      defaultProps: {
        variant: "subtle",
      },
    },
    Loader: {
      defaultProps: {
        type: "bars",
      },
    },
  },
});

export default theme;