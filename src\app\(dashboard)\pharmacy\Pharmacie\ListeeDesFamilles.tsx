'use client';
import React, { useState, useMemo } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Checkbox,
  Text,
  ActionIcon,
  Pagination,
  Select,
  Box,
  Badge,
  Tooltip,
  Modal,
  Stack,
 
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconTrash,
  IconFileText,
  IconFilter,
} from '@tabler/icons-react';

// Interface pour les familles pharmaceutiques
interface FamillePharmaceutique {
  id: string;
  nom: string;
  description: string;
  nombreMedicaments?: number;
  statut?: 'actif' | 'inactif';
}

// Données des familles pharmaceutiques (basées sur l'image de référence)
const famillesData: FamillePharmaceutique[] = [
  {
    id: '1',
    nom: 'endocrinothérapie, autres antagonistes hormonaux et agents apparentés',
    description: 'endocrinothérapie, autres antagonistes hormonaux et agents apparentés',
    nombreMedicaments: 25,
    statut: 'actif'
  },
  {
    id: '2',
    nom: 'neuroleptiques',
    description: 'neuroleptiques',
    nombreMedicaments: 18,
    statut: 'actif'
  },
  {
    id: '3',
    nom: 'antigoutteux hypouricémiant',
    description: 'antigoutteux hypouricémiant',
    nombreMedicaments: 12,
    statut: 'actif'
  },
  {
    id: '4',
    nom: 'antiarythmiques',
    description: 'Médicaments utilisés pour traiter les troubles du rythme cardiaque',
    nombreMedicaments: 15,
    statut: 'actif'
  },
  {
    id: '5',
    nom: 'antibiotiques systémiques',
    description: 'Antibiotiques à usage systémique pour infections bactériennes',
    nombreMedicaments: 45,
    statut: 'actif'
  },
  {
    id: '6',
    nom: 'antidiabétiques',
    description: 'Médicaments pour le traitement du diabète',
    nombreMedicaments: 22,
    statut: 'actif'
  },
  {
    id: '7',
    nom: 'anti-inflammatoires non stéroïdiens',
    description: 'AINS pour le traitement de la douleur et de l\'inflammation',
    nombreMedicaments: 30,
    statut: 'actif'
  },
  {
    id: '8',
    nom: 'antihypertenseurs',
    description: 'Médicaments pour le traitement de l\'hypertension artérielle',
    nombreMedicaments: 35,
    statut: 'actif'
  },
  {
    id: '9',
    nom: 'bronchodilatateurs',
    description: 'Médicaments pour le traitement de l\'asthme et des troubles respiratoires',
    nombreMedicaments: 20,
    statut: 'actif'
  },
  {
    id: '10',
    nom: 'antihistaminiques',
    description: 'Médicaments pour le traitement des allergies',
    nombreMedicaments: 16,
    statut: 'actif'
  }
];

const ListeeDesFamilles = () => {
  // États pour la gestion des données et de l'interface
  const [selectedFamilles, setSelectedFamilles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [opened, { open, close }] = useDisclosure(false);
  const [editingFamille, setEditingFamille] = useState<FamillePharmaceutique | null>(null);

  // Formulaire pour ajouter/modifier une famille
  const form = useForm({
    initialValues: {
      code: '',
      nom: '',
      parent: '',
    },
    validate: {
      code: (value) => (!value ? 'Le code est requis' : null),
      nom: (value) => (!value ? 'Le nom est requis' : null),
    },
  });

  // Options pour le champ Parent (familles existantes)
  const parentOptions = famillesData.map(famille => ({
    value: famille.id,
    label: famille.nom,
  }));

  // Fonctions de gestion de la modale
  const handleAdd = () => {
    setEditingFamille(null);
    form.reset();
    open();
  };

  const handleEdit = (famille: FamillePharmaceutique) => {
    setEditingFamille(famille);
    form.setValues({
      code: famille.id,
      nom: famille.nom,
      parent: '',
    });
    open();
  };

  const handleSubmit = (values: typeof form.values) => {
    // Ici vous pouvez ajouter la logique pour sauvegarder la famille
    notifications.show({
      title: 'Succès',
      message: editingFamille
        ? 'Famille modifiée avec succès'
        : 'Nouvelle famille ajoutée avec succès',
      color: 'green',
    });

    form.reset();
    setEditingFamille(null);
    close();
  };

  // Filtrage et recherche des données
  const filteredFamilles = useMemo(() => {
    return famillesData.filter(famille => {
      const matchesSearch = famille.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           famille.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter = filterStatus === 'all' || famille.statut === filterStatus;
      return matchesSearch && matchesFilter;
    });
  }, [searchQuery, filterStatus]);

  // Pagination
  const totalPages = Math.ceil(filteredFamilles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentFamilles = filteredFamilles.slice(startIndex, endIndex);

  // Gestion de la sélection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFamilles(currentFamilles.map(famille => famille.id));
    } else {
      setSelectedFamilles([]);
    }
  };

  const handleSelectFamille = (familleId: string, checked: boolean) => {
    if (checked) {
      setSelectedFamilles(prev => [...prev, familleId]);
    } else {
      setSelectedFamilles(prev => prev.filter(id => id !== familleId));
    }
  };

  const isAllSelected = currentFamilles.length > 0 &&
    selectedFamilles.length === currentFamilles.length &&
    currentFamilles.every(famille => selectedFamilles.includes(famille.id));

  const isIndeterminate = selectedFamilles.length > 0 &&
    selectedFamilles.length < currentFamilles.length;

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      {/* Header */}
      <Paper p="md" mb="md" withBorder className="bg-slate-600">
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconFileText size={24} className="text-slate-600" />
            <Title order={3} className="text-slate-600">
              Liste des familles
            </Title>
          </Group>
          <Group>
            <Button
              variant="filled"
              className="bg-blue-500 hover:bg-blue-600"
              leftSection={<IconPlus size={16} />}
              onClick={handleAdd}
            >
              Ajouter famille
            </Button>
            <Button
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-slate-600"
              leftSection={<IconFilter size={16} />}
            >
              Filtres
            </Button>
          </Group>
        </Group>
      </Paper>

      {/* Main Content */}
      <Paper p="md" withBorder className="bg-white">
        {/* Search and Filters */}
        <Group mb="md" justify="space-between">
          <Group style={{ flex: 1 }}>
            <TextInput
              placeholder="Rechercher"
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(event) => setSearchQuery(event.currentTarget.value)}
              style={{ flex: 1, maxWidth: 400 }}
            />
            <Select
              placeholder="Statut"
              leftSection={<IconFilter size={16} />}
              data={[
                { value: 'all', label: 'Tous les statuts' },
                { value: 'actif', label: 'Actif' },
                { value: 'inactif', label: 'Inactif' },
              ]}
              value={filterStatus}
              onChange={(value) => setFilterStatus(value || 'all')}
              w={150}
            />
          </Group>
          <Group>
            <Text size="sm" c="dimmed">
              {selectedFamilles.length} sélectionné(s)
            </Text>
            {selectedFamilles.length > 0 && (
              <Group gap="xs">
                <Tooltip label="Modifier la sélection">
                  <ActionIcon variant="subtle" color="blue">
                    <IconEdit size={16} />
                  </ActionIcon>
                </Tooltip>
                <Tooltip label="Supprimer la sélection">
                  <ActionIcon variant="subtle" color="red">
                    <IconTrash size={16} />
                  </ActionIcon>
                </Tooltip>
              </Group>
            )}
          </Group>
        </Group>

        {/* Table */}
        <Box style={{ border: '1px solid #e9ecef', borderRadius: '4px' }}>
          {/* Table Header */}
          <Group p="sm" style={{ backgroundColor: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>
            <Checkbox
              checked={isAllSelected}
              indeterminate={isIndeterminate}
              onChange={(event) => handleSelectAll(event.currentTarget.checked)}
            />
            <Text fw={500} style={{ flex: 1 }}>
              Nom
            </Text>
            <Text fw={500} style={{ flex: 1 }}>
              Description
            </Text>
          </Group>

          {/* Search Row */}
          <Group p="sm" style={{ backgroundColor: '#f1f3f4', borderBottom: '1px solid #e9ecef' }}>
            <Box style={{ width: 40 }}></Box>
            <TextInput
              placeholder="Rechercher"
              style={{ flex: 1 }}
              variant="unstyled"
              size="sm"
              value={searchQuery}
              onChange={(event) => setSearchQuery(event.currentTarget.value)}
            />
            <TextInput
              placeholder="Rechercher"
              style={{ flex: 1 }}
              variant="unstyled"
              size="sm"
              value={searchQuery}
              onChange={(event) => setSearchQuery(event.currentTarget.value)}
            />
          </Group>

          {/* Table Body */}
          {currentFamilles.length === 0 ? (
            <Box p="xl" style={{ textAlign: 'center' }}>
              <Text c="dimmed">Aucune famille trouvée</Text>
            </Box>
          ) : (
            currentFamilles.map((famille) => (
              <Group
                key={famille.id}
                p="sm"
                style={{
                  borderBottom: '1px solid #e9ecef',
                  '&:hover': {
                    backgroundColor: '#f8f9fa',
                  }
                }}
              >
                <Checkbox
                  checked={selectedFamilles.includes(famille.id)}
                  onChange={(event) => handleSelectFamille(famille.id, event.currentTarget.checked)}
                />
                <Box style={{ flex: 1 }}>
                  <Text size="sm" fw={500}>
                    {famille.nom}
                  </Text>
                  {famille.nombreMedicaments && (
                    <Badge size="xs" color="blue" variant="light">
                      {famille.nombreMedicaments} médicaments
                    </Badge>
                  )}
                </Box>
                <Box style={{ flex: 1 }}>
                  <Text size="sm" c="dimmed">
                    {famille.description}
                  </Text>
                </Box>
              </Group>
            ))
          )}
        </Box>

        {/* Pagination */}
        <Group justify="space-between" mt="md">
          <Group>
            <Text size="sm">Page</Text>
            <Select
              size="sm"
              w={60}
              data={Array.from({ length: totalPages }, (_, i) => (i + 1).toString())}
              value={currentPage.toString()}
              onChange={(value) => setCurrentPage(parseInt(value || '1'))}
            />
            <Text size="sm">Lignes par Page</Text>
            <Select
              size="sm"
              w={60}
              data={['15', '25', '50', '100']}
              value={itemsPerPage.toString()}
              onChange={(value) => setItemsPerPage(parseInt(value || '15'))}
            />
            <Text size="sm">
              {startIndex + 1} - {Math.min(endIndex, filteredFamilles.length)} de {filteredFamilles.length}
            </Text>
          </Group>
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
            size="sm"
          />
        </Group>
      </Paper>

      {/* Modal pour ajouter/modifier une famille */}
      <Modal
        opened={opened}
        onClose={close}
        title={
          <Group align="center">
            <IconFileText size={20} color="#1c7ed6" />
            <Text fw={500} c="#1c7ed6">
              Famille d'article
            </Text>
          </Group>
        }
        size="md"
        centered
        styles={{
          header: {
            backgroundColor: '#1c7ed6',
            color: 'white',
          },
          title: {
            color: 'white',
            fontWeight: 500,
          },
        }}
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Code"
              placeholder=""
              {...form.getInputProps('code')}
              required
              styles={{
                label: { color: '#1c7ed6', fontWeight: 500 },
              }}
            />

            <TextInput
              label="Nom"
              placeholder=""
              {...form.getInputProps('nom')}
              required
              styles={{
                label: { color: '#e03131', fontWeight: 500 },
              }}
            />

            <Select
              label="Parent"
              placeholder="Sélectionner un parent"
              data={parentOptions}
              {...form.getInputProps('parent')}
              searchable
              clearable
              styles={{
                label: { fontWeight: 500 },
              }}
            />

            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                color="red"
                onClick={close}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                color="blue"
              >
                Enregistrer
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default ListeeDesFamilles;
