import FilterList from './FilterList';
import StyleRulesTab from './StyleRulesTab';
import {NouvelleRegle} from './NouvelleRegle';
import {
  
  IconChevronUp,
  IconChevronDown,
  IconCheck,
} from '@tabler/icons-react';
import {
  Box,
  Button,
  Group,
ActionIcon,
  Title,
TextInput,
Tooltip,
  Divider,
  Tabs,
  Table,
  Checkbox,
  Badge,
  Menu,
  Text,
  Select,
   Pagination,
   Flex
} from '@mantine/core';

import { Icon } from '@mdi/react';
import { mdiFormatListBulleted,
mdiClose,
mdiMagnify,
mdiFilterVariant,
mdiReload,
mdiFileExcel,
mdiDotsVertical
} from '@mdi/js';
import { useState } from 'react';
import { onExport } from '@/utils/ExportToExcel'; // Chemin vers ta fonction
type OrganizationFormProps = {
  onCancel: () => void;
  onSubmit: (data: any) => void;
  onReload: () => void;
  
};
interface VisiteData {
  id: number;
  date: string;
  nom: string;
  prenom: string;
  dateNaissance: string;
  age: string;
  cin: string;
  telephone: string;
  ville: string;
  assurance: string;
  selected?: boolean;
}
export interface StyleRule {
  uid: string;
  type: "ROW" | "COLUMN";
  target_column: string;
  condition: string;
  style: Record<string, string>;
}


export interface Column {
  id: string;
  label: string;
  field: string;
  isFilter?: boolean;
  order_by: string;
  is_shown: boolean;
}
export function ListeDesConventions({ onCancel, onSubmit,onReload, }: OrganizationFormProps) {
  const [name, ] = useState('');
  const [contactFullName, ] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
 const toggleSidebar = () => {
       setIsSidebarVisible(!isSidebarVisible);
     };
  const isValid = name.trim() !== '';

  const handleSubmit = () => {
    onSubmit({
      name,
      contactFullName,
     
    });
 };


     const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedVisites, setSelectedVisites] = useState<number[]>([]);
 const [tabIndex, setTabIndex] = useState<string | null>('0');
  const [visibleColumns, setVisibleColumns] = useState({
    date: true,
    nom: true,
    prenom: true,
    dateNaissance: true,
    age: true,
    cin: true,
    telephone: true,
    ville: true,
    assurance: true,
    montantTotal: false,
    dateEntree: false,
    motif: false,
    medecinTraitant: false,
  });
const [vm, setVm] = useState({
    styleRules: [],      // [] ou données initiales
    is_create: false,
    columns: [],
    mnModel: {},
    draftRule: {},
  });
  const [isCreate, setIsCreate] = useState(true);

const [columns, ] = useState<Column[]>([
  {
    id: "1",
    label: "Nom",
    field: "last_name",
    order_by: "last_name",
    is_shown: true,
  },
  {
    id: "2",
    label: "Prénom",
    field: "first_name",
    order_by: "first_name",
    is_shown: true,
  },
]);
const [styleRules, setStyleRules] = useState<StyleRule[]>([
  {
    uid: "rule-1",
    type: "COLUMN",
    target_column: "last_name",
    condition: "equals",
    style: { color: "#000", fontWeight: "bold" },
  },
]);
const handleCancel = () => {
  setIsCreate(false);
};

const handleSave = (rule: StyleRule) => {
  setStyleRules((prev) => [...prev, rule]);
};
  // Données d'exemple pour les visites
  const visitesData: VisiteData[] = [
    {
      id: 1,
      date: '16/09/2022',
      nom: 'ABADI',
      prenom: 'SOUAD',
      dateNaissance: '01/01/1986',
      age: '36 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: '',
    },
    {
      id: 2,
      date: '15/09/2022',
      nom: 'BELKASSA',
      prenom: 'MOHAMED',
      dateNaissance: '01/01/1985',
      age: '37 ans 8 mois 16 jours',
      cin: 'BKH46',
      telephone: '0463568',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
    },
    {
      id: 3,
      date: '08/09/2022',
      nom: 'ABADI',
      prenom: 'SOUAD',
      dateNaissance: '01/01/1986',
      age: '36 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: '',
    },
    {
      id: 4,
      date: '25/08/2022',
      nom: 'EZZAYER',
      prenom: 'AMINE',
      dateNaissance: '01/01/1989',
      age: '33 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
    },
    {
      id: 5,
      date: '25/08/2022',
      nom: 'TEEEST',
      prenom: 'TEEEST',
      dateNaissance: '01/01/2003',
      age: '19 ans 8 mois 16 jours',
      cin: '',
      telephone: '06555889',
      ville: 'CASABLANCA',
      assurance: 'CNSS',
    },
    {
      id: 6,
      date: '24/08/2022',
      nom: 'ZZZ',
      prenom: 'ZDS',
      dateNaissance: '01/01/2007',
      age: '15 ans 8 mois 16 jours',
      cin: '',
      telephone: '066416545646',
      ville: 'CASABLANCA',
      assurance: 'CNSS',
    },
    {
      id: 7,
      date: '24/08/2022',
      nom: 'SDXS',
      prenom: 'XCXC',
      dateNaissance: '01/01/2007',
      age: '15 ans 8 mois 16 jours',
      cin: '',
      telephone: '0649',
      ville: 'CASABLANCA',
      assurance: '',
    },
    {
      id: 8,
      date: '20/07/2022',
      nom: 'MBARGA',
      prenom: 'EMILIE',
      dateNaissance: '23/01/1986',
      age: '36 ans 7 mois 25 jours',
      cin: '',
      telephone: '0697256532',
      ville: 'CASABLANCA',
      assurance: '',
    },
    {
      id: 9,
      date: '13/07/2022',
      nom: 'EL KANBI',
      prenom: 'HAMZA',
      dateNaissance: '25/11/2002',
      age: '19 ans 9 mois 20 jours',
      cin: 'BE52747',
      telephone: '06665666',
      ville: 'CASABLANCA',
      assurance: 'CNSS',
    },
    {
      id: 10,
      date: '12/07/2022',
      nom: 'AKONGA MBARGA',
      prenom: 'JOSEPH',
      dateNaissance: '28/03/1989',
      age: '33 ans 5 mois 20 jours',
      cin: '',
      telephone: '697894564',
      ville: 'CASABLANCA',
      assurance: 'AXA',
    },
    {
      id: 11,
      date: '12/07/2022',
      nom: 'ALIMA',
      prenom: 'FRANCE',
      dateNaissance: '13/01/2000',
      age: '22 ans 8 mois 4 jours',
      cin: '',
      telephone: '698745214',
      ville: 'CASABLANCA',
      assurance: 'AXA',
    },
    {
      id: 12,
      date: '08/07/2022',
      nom: 'BENHOUDA',
      prenom: 'ICHRAK',
      dateNaissance: '18/07/1991',
      age: '31 ans 1 mois 30 jours',
      cin: '',
      telephone: '065499248',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
    },
    {
      id: 13,
      date: '06/07/2022',
      nom: 'BENHOUDA',
      prenom: 'ICHRAK',
      dateNaissance: '18/07/1991',
      age: '31 ans 1 mois 30 jours',
      cin: '',
      telephone: '065499248',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
    },
    {
      id: 14,
      date: '06/07/2022',
      nom: 'EZZAYER',
      prenom: 'AMINE',
      dateNaissance: '01/01/1989',
      age: '33 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: 'CNOPS',
    },
    {
      id: 15,
      date: '24/06/2022',
      nom: 'TEST',
      prenom: 'ETAAPES',
      dateNaissance: '01/01/2003',
      age: '19 ans 8 mois 16 jours',
      cin: '',
      telephone: '',
      ville: 'CASABLANCA',
      assurance: '',
    },
  ];

  // Filtrer les données selon le terme de recherche
  const filteredVisites = visitesData.filter(visite => {
    const searchLower = searchTerm.toLowerCase();
    return (
      visite.nom.toLowerCase().includes(searchLower) ||
      visite.prenom.toLowerCase().includes(searchLower) ||
      visite.cin.toLowerCase().includes(searchLower) ||
      visite.telephone.toLowerCase().includes(searchLower) ||
      visite.ville.toLowerCase().includes(searchLower) ||
      visite.assurance.toLowerCase().includes(searchLower)
    );
  });

  // Fonction de tri
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Trier les données
  const sortedVisites = [...filteredVisites].sort((a, b) => {
    if (!sortField) return 0;

    const aValue = a[sortField as keyof VisiteData] || '';
    const bValue = b[sortField as keyof VisiteData] || '';

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Pagination
  const totalPages = Math.ceil(sortedVisites.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentVisites = sortedVisites.slice(startIndex, endIndex);

  // Fonction pour rendre l'icône de tri
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <IconChevronUp size={14} /> :
      <IconChevronDown size={14} />;
  };

  // Gestion de la sélection
  const handleSelectVisite = (id: number) => {
    setSelectedVisites(prev =>
      prev.includes(id)
        ? prev.filter(visiteId => visiteId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedVisites.length === currentVisites.length) {
      setSelectedVisites([]);
    } else {
      setSelectedVisites(currentVisites.map(visite => visite.id));
    }
  };

  // Fonction pour obtenir le badge d'assurance
  const getAssuranceBadge = (assurance: string) => {
    if (!assurance) return null;

    const colors: { [key: string]: string } = {
      'CNOPS': 'blue',
      'CNSS': 'green',
      'AXA': 'orange',
      'RAMED': 'red',
    };

    return (
      <Badge
        color={colors[assurance] || 'gray'}
        variant="light"
        size="sm"
      >
        {assurance}
      </Badge>
    );
  };

  // Fonction pour basculer la visibilité des colonnes
  const toggleColumnVisibility = (column: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column as keyof typeof prev]
    }));
  };
const handleExport = () => {
    onExport(visitesData, 'utilisateurs.xlsx');
  };

    
  return (
    <>
    <Box>
      {/* Toolbar */}
      <Group justify="space-between" px="md" py="xs" bg="var(--mantine-color-blue-light)">
        <Group>
          <Icon path={mdiFormatListBulleted} size={1} />
          <Title order={3}>Liste des conventions</Title>
        </Group>
        <Button
          variant="subtle"
          color="gray"
          onClick={onCancel}
          leftSection={<Icon path={mdiClose} size={0.9} />}
        />
      </Group>
   
   
   <Divider my="sm" />
    <Group justify="space-between">
        <Group>
         <ActionIcon variant="subtle" color="gray" size="lg" className="cursor-pointer"
        onClick={(event) => {
        event.preventDefault();
        toggleSidebar(); // Toggle sidebar visibility
        }}
        >
            <Icon path={mdiFilterVariant} size={1} />
        </ActionIcon>
            <Tooltip label="Recherche">
                <Icon path={mdiMagnify} size={1} />
            </Tooltip>
            <TextInput
                placeholder="Rechercher"
                //onChange={(e) => onSearch(e.currentTarget.value)}
            />
            </Group>
              <Group justify="flex-end">
                    <ActionIcon onClick={onReload}>
                      <Icon path={mdiReload} size={1} />
                    </ActionIcon>
                    <ActionIcon onClick={handleExport}>
                      <Icon path={mdiFileExcel} size={1} />
                    </ActionIcon>
                      {/* Menu de filtres des colonnes */}
                                <Menu shadow="md" width={250}>
                                  <Menu.Target>
                                    <ActionIcon variant="subtle" color="gray" size="lg">
                                        <Icon path={mdiDotsVertical} size={1} color="#3799ce" />
                                    </ActionIcon>
                                  </Menu.Target>
                                  <Menu.Dropdown>
                                    <Menu.Label>Colonnes visibles</Menu.Label>
                                    <Menu.Item
                                      leftSection={visibleColumns.date ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('date')}
                                    >
                                      Date de visite
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.nom ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('nom')}
                                    >
                                      Nom
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.prenom ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('prenom')}
                                    >
                                      Prénom
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.dateNaissance ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('dateNaissance')}
                                    >
                                      Date de naissance
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.age ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('age')}
                                    >
                                      Age
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.cin ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('cin')}
                                    >
                                      CINE
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.telephone ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('telephone')}
                                    >
                                      Téléphone
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.ville ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('ville')}
                                    >
                                      Ville
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.assurance ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('assurance')}
                                    >
                                      Assurance
                                    </Menu.Item>
                    
                                    <Menu.Divider />
                                    <Menu.Label>Colonnes supplémentaires</Menu.Label>
                                    <Menu.Item
                                      leftSection={visibleColumns.montantTotal ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('montantTotal')}
                                    >
                                      Montant total (DHS)
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.dateEntree ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('dateEntree')}
                                    >
                                      Date d&apos;entrée
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.motif ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('motif')}
                                    >
                                      Motif
                                    </Menu.Item>
                                    <Menu.Item
                                      leftSection={visibleColumns.medecinTraitant ? <IconCheck size={14} /> : <div style={{ width: 14 }} />}
                                      onClick={() => toggleColumnVisibility('medecinTraitant')}
                                    >
                                      Médecin traitant
                                    </Menu.Item>
                                  </Menu.Dropdown>
                                </Menu>
                  </Group>
         </Group>
        
<Flex
      
     justify="flex-start"
      align="flex-start"
      direction="row"
    >
   
      {isSidebarVisible && ( 
           <Box   w={'28%'} my={14} h={"100%"}>
         <Tabs value={tabIndex} onChange={setTabIndex}>
                      <Tabs.List>
                        <Tabs.Tab value="0">Filtre avancé</Tabs.Tab>
                        <Tabs.Tab value="1">Règles de mise en forme</Tabs.Tab>
                        <Tabs.Tab value="2">3</Tabs.Tab>
                      </Tabs.List>
                
                      <Tabs.Panel value="0" pt="xs">
                        <FilterList />
                      </Tabs.Panel>
                
                      <Tabs.Panel value="1" pt="xs">
                    <StyleRulesTab
                      styleRules={vm.styleRules}
                      isCreate={vm.is_create}
                      onStartCreate={() => setVm((prev) => ({ ...prev, is_create: true }))}
                      columns={vm.columns}      // ✅ maintenant reconnu
                      model={vm.mnModel}
                      draftRule={vm.draftRule}
                    />
                        <Tabs.Panel value="2" pt="xs">
        
                          {/* if click plus icon shwo tabs */}
                     <NouvelleRegle
                        columns={columns}
                        styleRules={styleRules}
                        isCreate={isCreate}
                        onCancel={handleCancel}
                        onSave={handleSave}
                        />
        
                 </Tabs.Panel>
                </Tabs.Panel>
                      
          </Tabs>
           </Box>
     )}
  
      <Box   className={isSidebarVisible ?  "w-[70%]": "w-full "} my={14}>
      
 <Table
            striped
            highlightOnHover
            withTableBorder={true}
            className="min-w-full"
             withColumnBorders
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr >
                <Table.Th className="w-12 ">
                  <Checkbox
                    checked={selectedVisites.length === currentVisites.length && currentVisites.length > 0}
                    indeterminate={selectedVisites.length > 0 && selectedVisites.length < currentVisites.length}
                    onChange={handleSelectAll}
                    style={{borderRadius: "var(--checkbox-radius, var(--mantine-radius-default) calc(0.25rem * 1))"}}
                  />
                </Table.Th>
                {visibleColumns.date && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('date')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date de visite
                      {renderSortIcon('date')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.nom && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('nom')}
                  >
                    <Group gap="xs" justify="space-between">
                      Nom
                      {renderSortIcon('nom')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.prenom && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('prenom')}
                  >
                    <Group gap="xs" justify="space-between">
                      Prénom
                      {renderSortIcon('prenom')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.dateNaissance && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('dateNaissance')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date de naissance
                      {renderSortIcon('dateNaissance')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.age && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('age')}
                  >
                    <Group gap="xs" justify="space-between">
                      Age
                      {renderSortIcon('age')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.cin && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('cin')}
                  >
                    <Group gap="xs" justify="space-between">
                      CIN
                      {renderSortIcon('cin')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.telephone && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('telephone')}
                  >
                    <Group gap="xs" justify="space-between">
                      Téléphone
                      {renderSortIcon('telephone')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.ville && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ville')}
                  >
                    <Group gap="xs" justify="space-between">
                      Ville
                      {renderSortIcon('ville')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.assurance && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('assurance')}
                  >
                    <Group gap="xs" justify="space-between">
                      Assurance
                      {renderSortIcon('assurance')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.montantTotal && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('montantTotal')}
                  >
                    <Group gap="xs" justify="space-between">
                      Montant total (DHS)
                      {renderSortIcon('montantTotal')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.dateEntree && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('dateEntree')}
                  >
                    <Group gap="xs" justify="space-between">
                      Date d&apos;entrée
                      {renderSortIcon('dateEntree')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.motif && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('motif')}
                  >
                    <Group gap="xs" justify="space-between">
                      Motif
                      {renderSortIcon('motif')}
                    </Group>
                  </Table.Th>
                )}
                {visibleColumns.medecinTraitant && (
                  <Table.Th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('medecinTraitant')}
                  >
                    <Group gap="xs" justify="space-between">
                      Médecin traitant
                      {renderSortIcon('medecinTraitant')}
                    </Group>
                  </Table.Th>
                )}
                <Table.Th className="w-42"></Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {currentVisites.map((visite) => (
                <Table.Tr key={visite.id} className="hover:bg-gray-50">
                  <Table.Td>
                    <Checkbox
                      checked={selectedVisites.includes(visite.id)}
                      onChange={() => handleSelectVisite(visite.id)}
                    />
                  </Table.Td>
                  {visibleColumns.date && (
                    <Table.Td className="font-medium">{visite.date}</Table.Td>
                  )}
                  {visibleColumns.nom && (
                    <Table.Td className="font-medium">{visite.nom}</Table.Td>
                  )}
                  {visibleColumns.prenom && (
                    <Table.Td>{visite.prenom}</Table.Td>
                  )}
                  {visibleColumns.dateNaissance && (
                    <Table.Td className="text-sm text-gray-600">{visite.dateNaissance}</Table.Td>
                  )}
                  {visibleColumns.age && (
                    <Table.Td className="text-sm text-gray-600">{visite.age}</Table.Td>
                  )}
                  {visibleColumns.cin && (
                    <Table.Td className="text-sm">{visite.cin || '-'}</Table.Td>
                  )}
                  {visibleColumns.telephone && (
                    <Table.Td className="text-sm">{visite.telephone || '-'}</Table.Td>
                  )}
                  {visibleColumns.ville && (
                    <Table.Td className="text-sm">{visite.ville}</Table.Td>
                  )}
                  {visibleColumns.assurance && (
                    <Table.Td>
                      {getAssuranceBadge(visite.assurance)}
                    </Table.Td>
                  )}
                  {visibleColumns.montantTotal && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.dateEntree && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.motif && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  {visibleColumns.medecinTraitant && (
                    <Table.Td className="text-sm">-</Table.Td>
                  )}
                  <Table.Td w={"250px"}>
                   
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
     
     
    </Box>
      
    </Flex>
      <Divider my="sm" />

      {/* Actions */}
      <Group justify="flex-end" px="md" pb="md">
        <Button
          variant="filled"
          color="blue"
          onClick={handleSubmit}
          disabled={!isValid}
        >
          Enregistrer
        </Button>
        <Button variant="outline" color="red" onClick={onCancel}>
          Annuler
        </Button>
      </Group>
       {/* Pagination */}
              <div className="p-4 border-t border-gray-200 bg-gray-50">
                <Group justify="space-between" align="center">
                  <Group gap="xs">
                    <Text size="sm" className="text-gray-600">
                      Page
                    </Text>
                    <Select
                      value={currentPage.toString()}
                      onChange={(value) => setCurrentPage(Number(value))}
                      data={Array.from({ length: totalPages }, (_, i) => ({
                        value: (i + 1).toString(),
                        label: (i + 1).toString(),
                      }))}
                      size="sm"
                      className="w-20"
                    />
                    <Text size="sm" className="text-gray-600">
                      Lignes par Page
                    </Text>
                    <Select
                      value={itemsPerPage.toString()}
                      onChange={(value) => {
                        setItemsPerPage(Number(value));
                        setCurrentPage(1);
                      }}
                      data={[
                        { value: '10', label: '10' },
                        { value: '15', label: '15' },
                        { value: '25', label: '25' },
                        { value: '50', label: '50' },
                      ]}
                      size="sm"
                      className="w-20"
                    />
                    <Text size="sm" className="text-gray-600">
                      1 - {Math.min(endIndex, sortedVisites.length)} de {sortedVisites.length}
                    </Text>
                  </Group>
      
                  <Pagination
                    value={currentPage}
                    onChange={setCurrentPage}
                    total={totalPages}
                    size="sm"
                    withEdges
                  />
                </Group>
                  </div>
    </Box>
      
                 </>
  );
}
