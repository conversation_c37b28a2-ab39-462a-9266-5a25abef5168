'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  PasswordInput,
  Paper,
  Title,
  Container,
  Button,
  Text,
  Anchor,
  Alert,
  Stack,
  Center,
  Loader,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconCheck } from '@tabler/icons-react';
import authService from '~/services/authService';

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const token = searchParams ? searchParams.get('token') : null;
  const email = searchParams ? searchParams.get('email') : null;

  const form = useForm({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validate: {
      password: (value) => (value.length < 8 ? 'Password must be at least 8 characters' : null),
      confirmPassword: (value, values) => (value !== values.password ? 'Passwords do not match' : null),
    },
  });

  useEffect(() => {
    const verifyToken = async () => {
      if (!token || !email) {
        setVerifying(false);
        setTokenValid(false);
        return;
      }

      try {
        const isValid = await authService.verifyResetToken(token, email);
        setTokenValid(isValid);
      } catch (error) {
        console.error('Token verification error:', error);
        setTokenValid(false);
      } finally {
        setVerifying(false);
      }
    };

    verifyToken();
  }, [token, email]);

  const handleSubmit = async (values: typeof form.values) => {
    if (!token || !email) {
      notifications.show({
        title: 'Error',
        message: 'Missing token or email. Please try again.',
        color: 'red',
      });
      return;
    }

    try {
      setLoading(true);

      await authService.resetPassword({
        token,
        email,
        password: values.password,
      });

      setSubmitted(true);

      notifications.show({
        title: 'Password Reset Successful',
        message: 'Your password has been reset successfully. You can now log in with your new password.',
        color: 'green',
      });

      // Redirect to login page after a short delay
      setTimeout(() => {
        router.push('/login?passwordReset=true');
      }, 2000);
    } catch (error) {
      console.error('Password reset error:', error);
      notifications.show({
        title: 'Password Reset Failed',
        message: 'Failed to reset your password. Please try again or request a new reset link.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  if (verifying) {
    return (
      <Container size="sm">
        <Center style={{ height: '60vh' }}>
          <Stack align="center" gap="md">
            <Loader size="lg" />
            <Text>Verifying reset token...</Text>
          </Stack>
        </Center>
      </Container>
    );
  }

  if (!token || !email || !tokenValid) {
    return (
      <Container size="sm">
        <Paper radius="md" p="xl" withBorder>
          <Title order={2} ta="center" mt="md" mb="md">
            Invalid or Expired Link
          </Title>

          <Alert icon={<IconAlertCircle size={16} />} title="Invalid Reset Link" color="red" mb="xl">
            The password reset link is invalid or has expired. Please request a new password reset link.
          </Alert>

          <Button component={Link} href="/forgot-password" fullWidth>
            Request New Reset Link
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size="sm">
      <Paper radius="md" p="xl" withBorder style={{
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
        <Title order={2} ta="center" mt="md" mb="md">
          Reset Password
        </Title>

        {submitted ? (
          <Stack>
            <Alert icon={<IconCheck size={16} />} title="Password Reset Successful" color="green">
              Your password has been reset successfully. You will be redirected to the login page.
            </Alert>

            <Button component={Link} href="/login" fullWidth mt="xl">
              Go to Login
            </Button>
          </Stack>
        ) : (
          <>
            <Text c="dimmed" size="sm" ta="center" mb="xl">
              Create a new password for your account
            </Text>

            <form onSubmit={form.onSubmit(handleSubmit)}>
              <Stack>
                <PasswordInput
                  label="New Password"
                  placeholder="Enter your new password"
                  required
                  {...form.getInputProps('password')}
                />

                <PasswordInput
                  label="Confirm Password"
                  placeholder="Confirm your new password"
                  required
                  {...form.getInputProps('confirmPassword')}
                />

                <Button fullWidth mt="xl" type="submit" loading={loading}>
                  Reset Password
                </Button>
              </Stack>
            </form>
          </>
        )}

        <Text ta="center" mt="md">
          Remember your password?{' '}
          <Anchor component={Link} href="/login">
            Back to Login
          </Anchor>
        </Text>
      </Paper>
    </Container>
  );
}
