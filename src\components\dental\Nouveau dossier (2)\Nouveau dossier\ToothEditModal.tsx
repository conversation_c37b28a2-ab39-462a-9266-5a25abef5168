'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  TextInput,
  Select,
  Textarea,
  Button,
  Group,
  Stack,
  Badge,
  Alert,
  LoadingOverlay,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconX, IconMedicalCross } from '@tabler/icons-react';
import { Tooth, toothService } from '../../../services/toothService';

interface ToothEditModalProps {
  tooth: Tooth | null;
  opened: boolean;
  onClose: () => void;
  onSave: (updatedTooth: Tooth) => void;
}

const ToothEditModal: React.FC<ToothEditModalProps> = ({
  tooth,
  opened,
  onClose,
  onSave,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm({
    initialValues: {
      number: '',
      name: '',
      tooth_type: '',
      quadrant: '',
      position: 0,
      is_permanent: true,
      description: '',
    },
    validate: {
      number: (value) => (!value ? 'Le numéro de dent est requis' : null),
      name: (value) => (!value ? 'Le nom de la dent est requis' : null),
      tooth_type: (value) => (!value ? 'Le type de dent est requis' : null),
      quadrant: (value) => (!value ? 'Le quadrant est requis' : null),
    },
  });

  // Update form when tooth changes
  useEffect(() => {
    if (tooth) {
      form.setValues({
        number: tooth.number || '',
        name: tooth.name || '',
        tooth_type: tooth.tooth_type || '',
        quadrant: tooth.quadrant || '',
        position: tooth.position || 0,
        is_permanent: tooth.is_permanent || true,
        description: tooth.description || '',
      });
    }
  }, [tooth]);

  const handleSubmit = async (values: typeof form.values) => {
    if (!tooth) return;

    setLoading(true);
    setError(null);

    try {
      // Prepare data for API
      const updateData = {
        ...tooth,
        number: values.number,
        name: values.name,
        tooth_type: values.tooth_type,
        quadrant: values.quadrant,
        position: values.position,
        is_permanent: values.is_permanent,
        description: values.description,
      };

      // Call API to update tooth
      const updatedTooth = await toothService.updateTooth(tooth.id, updateData);

      // Show success notification
      notifications.show({
        title: 'Succès',
        message: 'Dent mise à jour avec succès',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      // Call parent callback
      onSave(updatedTooth);
      onClose();

    } catch (err) {
      console.error('Error updating tooth:', err);
      setError('Erreur lors de la mise à jour de la dent');
      
      notifications.show({
        title: 'Erreur',
        message: 'Impossible de mettre à jour la dent',
        color: 'red',
        icon: <IconX size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const toothTypeOptions = [
    { value: 'incisor', label: 'Incisive' },
    { value: 'canine', label: 'Canine' },
    { value: 'premolar', label: 'Prémolaire' },
    { value: 'molar', label: 'Molaire' },
    { value: 'wisdom', label: 'Sagesse' },
  ];

  const quadrantOptions = [
    { value: 'upper_right', label: 'Supérieur Droit' },
    { value: 'upper_left', label: 'Supérieur Gauche' },
    { value: 'lower_left', label: 'Inférieur Gauche' },
    { value: 'lower_right', label: 'Inférieur Droit' },
  ];

  const permanentOptions = [
    { value: 'true', label: 'Permanente' },
    { value: 'false', label: 'Temporaire' },
  ];

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconMedicalCross size={20} />
          <span>Modifier la Dent {tooth?.number}</span>
        </Group>
      }
      size="md"
    >
      <LoadingOverlay visible={loading} />
      
      {error && (
        <Alert color="red" mb="md">
          {error}
        </Alert>
      )}

      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          {/* Basic Info */}
          <Group grow>
            <TextInput
              label="Numéro"
              placeholder="Ex: 11, 12, 13..."
              {...form.getInputProps('number')}
            />
            <TextInput
              label="Nom"
              placeholder="Ex: Central Incisor"
              {...form.getInputProps('name')}
            />
          </Group>

          {/* Type and Quadrant */}
          <Group grow>
            <Select
              label="Type de Dent"
              placeholder="Sélectionner le type"
              data={toothTypeOptions}
              {...form.getInputProps('tooth_type')}
            />
            <Select
              label="Quadrant"
              placeholder="Sélectionner le quadrant"
              data={quadrantOptions}
              {...form.getInputProps('quadrant')}
            />
          </Group>

          {/* Position and Permanent */}
          <Group grow>
            <TextInput
              label="Position"
              type="number"
              placeholder="Position dans le quadrant"
              {...form.getInputProps('position')}
            />
            <Select
              label="Type"
              data={permanentOptions}
              value={form.values.is_permanent ? 'true' : 'false'}
              onChange={(value) => form.setFieldValue('is_permanent', value === 'true')}
            />
          </Group>

          {/* Description */}
          <Textarea
            label="Description"
            placeholder="Description de la dent..."
            rows={3}
            {...form.getInputProps('description')}
          />

          {/* Current Status */}
          {tooth && (
            <Group>
              <Badge color="blue" variant="light">
                ID: {tooth.id}
              </Badge>
              <Badge color="green" variant="light">
                Créé: {new Date(tooth.created_at).toLocaleDateString()}
              </Badge>
            </Group>
          )}

          {/* Actions */}
          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={onClose}>
              Annuler
            </Button>
            <Button type="submit" loading={loading}>
              Sauvegarder
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default ToothEditModal;
