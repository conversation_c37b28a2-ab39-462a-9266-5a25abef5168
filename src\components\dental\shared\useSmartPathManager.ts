// frontend/dental_medicine/src/components/content/dental/shared/useSmartPathManager.ts

import { useCallback } from 'react';
import { useTreatmentManager } from './useTreatmentManager';
import { applyTreatmentWithoutStacking, resetToothToBase as resetToothToBaseUtil } from './pathUtils';

interface UseSmartPathManagerProps {
  hiddenPaths: Record<string, boolean>;
  setHiddenPaths: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  onModificationChange?: (svgId: string, pathId: string, isHidden: boolean, highlightedPaths: Record<string, any>) => Promise<void>;
  highlightedPaths: Record<string, any>;
}

interface UseSmartPathManagerReturn {
  applySmartTreatment: (treatmentId: string, svgId: string) => Promise<void>;
  conflictDialog: React.ReactNode;
  resetToothToBase: (svgId: string) => void;
}

/**
 * Hook qui combine le système intelligent avec la gestion des paths SVG
 */
export const useSmartPathManager = ({
  hiddenPaths,
  setHiddenPaths,
  onModificationChange,
  highlightedPaths
}: UseSmartPathManagerProps): UseSmartPathManagerReturn => {

  const {
    applyTreatment,
    conflicts,
    isConflictDialogOpen,
    pendingTreatment,
    resolveConflict,
    closeConflictDialog
  } = useTreatmentManager();

  /**
   * Remet une dent à son état de base (paths 8-16 visibles, autres cachés)
   * Utilise la logique corrigée d'EstimatesTabs.tsx
   */
  const resetToothToBase = useCallback((svgId: string) => {
    console.log(`🔄 Reset de la dent ${svgId} à l'état de base (logique corrigée)`);

    const newHiddenPaths = resetToothToBaseUtil(svgId);

    setHiddenPaths(prev => ({ ...prev, ...newHiddenPaths }));
  }, [setHiddenPaths]);

  /**
   * Applique les changements de visibilité des paths selon le résultat du traitement
   * Utilise la logique corrigée pour éviter l'empilement de couches
   */
  const applyPathChanges = useCallback(async (svgId: string, pathsToShow: string[], pathsToHide: string[]) => {
    console.log(`🎨 Application des changements de paths pour la dent ${svgId} (logique corrigée)`);
    console.log(`📍 Paths à afficher:`, pathsToShow);
    console.log(`🚫 Paths à cacher:`, pathsToHide);

    // Utiliser la logique corrigée qui fait un reset complet puis affiche seulement les paths nécessaires
    const newHiddenPaths: Record<string, boolean> = {};

    // Reset complet de la dent (cacher tous les paths 1-100)
    for (let i = 1; i <= 100; i++) {
      newHiddenPaths[`${svgId}-${i}`] = true;
    }

    // Afficher les paths de base de la dent (8-16)
    [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
      newHiddenPaths[`${svgId}-${pathId}`] = false;
    });

    // Afficher uniquement les paths requis par le traitement
    pathsToShow.forEach(pathId => {
      newHiddenPaths[`${svgId}-${pathId}`] = false;
    });

    setHiddenPaths(prev => ({ ...prev, ...newHiddenPaths }));

    // Sauvegarder les modifications si la fonction est disponible
    if (onModificationChange) {
      try {
        // Sauvegarder chaque changement de path visible
        for (const pathId of pathsToShow) {
          await onModificationChange(svgId, pathId, false, highlightedPaths);
        }
        console.log(`💾 Modifications sauvegardées pour la dent ${svgId}`);
      } catch (error) {
        console.error('Erreur lors de la sauvegarde des paths:', error);
      }
    }
  }, [setHiddenPaths, onModificationChange, highlightedPaths]);

  /**
   * Applique un traitement de manière intelligente avec gestion des paths
   */
  const applySmartTreatment = useCallback(async (treatmentId: string, svgId: string) => {
    console.log(`🧠 Application intelligente du traitement ${treatmentId} sur la dent ${svgId}`);

    const result = await applyTreatment(treatmentId, svgId);

    if (result.success) {
      console.log(`✅ Traitement appliqué avec succès`);

      // Appliquer les changements de visibilité des paths
      await applyPathChanges(svgId, result.pathsToShow, result.pathsToHide);

    } else if (result.conflicts && result.conflicts.length > 0) {
      console.log(`⚠️ Conflits détectés, dialog affiché`);
      // Le dialog sera affiché automatiquement par useTreatmentManager
    }
  }, [applyTreatment, applyPathChanges]);

  // Créer le composant de dialog de conflit avec gestion des paths
  const conflictDialog = React.createElement(
    require('./TreatmentConflictDialog').default,
    {
      isOpen: isConflictDialogOpen,
      conflicts,
      treatmentName: pendingTreatment ?
        require('./treatmentCompatibility').TREATMENTS[pendingTreatment.treatmentId]?.name || pendingTreatment.treatmentId : '',
      toothNumber: pendingTreatment?.svgId || '',
      onResolve: async (action: 'force' | 'cancel' | 'modify') => {
        const result = await resolveConflict(action);

        if (result?.success && pendingTreatment) {
          console.log(`🔨 Application forcée du traitement avec gestion des paths`);

          // Appliquer les changements de paths pour le traitement forcé
          await applyPathChanges(pendingTreatment.svgId, result.pathsToShow, result.pathsToHide);
        }
      },
      onClose: closeConflictDialog
    }
  );

  return {
    applySmartTreatment,
    conflictDialog,
    resetToothToBase
  };
};

// Version React pour l'import
import React from 'react';
