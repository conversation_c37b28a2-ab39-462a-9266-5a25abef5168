import React, { useEffect, useState } from 'react'
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle, IconSettings } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import ListeInventaire from "./ListeInventaire"
import Mouvement from "./Mouvement"

// Mapping des sous-onglets pour Inventaire
const subtabMapping: { [key: string]: string } = {
  'liste-inventaires': 'Inventaire',
  'mouvements-stock': 'Mouvement',
  'mouvements-series': 'settings'
};

const Inventaire = () => {
  const [activeTab, setActiveTab] = useState('Inventaire');
  const searchParams = useSearchParams();

  // Effet pour lire le paramètre subtab et définir l'onglet actif
  useEffect(() => {
    const subtab = searchParams.get('subtab');
    if (subtab && subtabMapping[subtab]) {
      setActiveTab(subtabMapping[subtab]);
    }
  }, [searchParams]);

  return (
    <Tabs
      variant="outline"
      radius="md"
      orientation="vertical"
      value={activeTab}
      onChange={(value) => setActiveTab(value || 'Inventaire')}
      w={"100%"}
      mt={10}
    >
      <Tabs.List>
        <Tabs.Tab value="Inventaire" leftSection={<IconPhoto size={12} />}>
         Liste des inventaires
        </Tabs.Tab>
        <Tabs.Tab value="Mouvement" leftSection={<IconMessageCircle size={12} />}>
          Mouvements du stock
        </Tabs.Tab>
        <Tabs.Tab value="settings" leftSection={<IconSettings size={12} />}>
          Mouvemnts series/Lots
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="Inventaire" ml={20}>
        <ListeInventaire/>
      </Tabs.Panel>

      <Tabs.Panel value="Mouvement" ml={20}>
        <Mouvement/>
      </Tabs.Panel>

      <Tabs.Panel value="settings" ml={20}>
        Settings tab content
      </Tabs.Panel>
    </Tabs>
  )
}

export default Inventaire
