/**
 * Mapping complet des dents selon le système FDI (Fédération Dentaire Internationale)
 * avec noms français corrects pour les dents permanentes et primaires
 */

// Mapping complet des numéros de dents vers leurs noms français
export const TOOTH_NAMES: Record<number, string> = {
  // Dents permanentes - Mâchoire supérieure droite (Quadrant 1)
  11: "Incisive centrale supérieure droite",
  12: "Incisive latérale supérieure droite",
  13: "Canine supérieure droite",
  14: "Première prémolaire supérieure droite",
  15: "Deuxième prémolaire supérieure droite",
  16: "Première molaire supérieure droite",
  17: "Deuxième molaire supérieure droite",
  18: "Troisième molaire supérieure droite",

  // Dents permanentes - Mâchoire supérieure gauche (Quadrant 2)
  21: "Incisive centrale supérieure gauche",
  22: "Incisive latérale supérieure gauche",
  23: "Canine supérieure gauche",
  24: "Première prémolaire supérieure gauche",
  25: "Deuxième prémolaire supérieure gauche",
  26: "Première molaire supérieure gauche",
  27: "Deuxième molaire supérieure gauche",
  28: "Troisième molaire supérieure gauche",

  // Dents permanentes - Mâchoire inférieure gauche (Quadrant 3)
  31: "Incisive centrale inférieure gauche",
  32: "Incisive latérale inférieure gauche",
  33: "Canine inférieure gauche",
  34: "Première prémolaire inférieure gauche",
  35: "Deuxième prémolaire inférieure gauche",
  36: "Première molaire inférieure gauche",
  37: "Deuxième molaire inférieure gauche",
  38: "Troisième molaire inférieure gauche",

  // Dents permanentes - Mâchoire inférieure droite (Quadrant 4)
  41: "Incisive centrale inférieure droite",
  42: "Incisive latérale inférieure droite",
  43: "Canine inférieure droite",
  44: "Première prémolaire inférieure droite",
  45: "Deuxième prémolaire inférieure droite",
  46: "Première molaire inférieure droite",
  47: "Deuxième molaire inférieure droite",
  48: "Troisième molaire inférieure droite",

  // Dents primaires - Mâchoire supérieure droite (Quadrant 5)
  51: "Incisive centrale primaire supérieure droite",
  52: "Incisive latérale primaire supérieure droite",
  53: "Canine primaire supérieure droite",
  54: "Première molaire primaire supérieure droite",
  55: "Deuxième molaire primaire supérieure droite",

  // Dents primaires - Mâchoire supérieure gauche (Quadrant 6)
  61: "Incisive centrale primaire supérieure gauche",
  62: "Incisive latérale primaire supérieure gauche",
  63: "Canine primaire supérieure gauche",
  64: "Première molaire primaire supérieure gauche",
  65: "Deuxième molaire primaire supérieure gauche",

  // Dents primaires - Mâchoire inférieure gauche (Quadrant 7)
  71: "Incisive centrale primaire inférieure gauche",
  72: "Incisive latérale primaire inférieure gauche",
  73: "Canine primaire inférieure gauche",
  74: "Première molaire primaire inférieure gauche",
  75: "Deuxième molaire primaire inférieure gauche",

  // Dents primaires - Mâchoire inférieure droite (Quadrant 8)
  81: "Incisive centrale primaire inférieure droite",
  82: "Incisive latérale primaire inférieure droite",
  83: "Canine primaire inférieure droite",
  84: "Première molaire primaire inférieure droite",
  85: "Deuxième molaire primaire inférieure droite",
};

// Groupes de dents par ensemble
export const DENTAL_GROUPS = {
  upper: [
    // Dents permanentes supérieures
    11, 12, 13, 14, 15, 16, 17, 18,  // Quadrant 1
    21, 22, 23, 24, 25, 26, 27, 28,  // Quadrant 2
    // Dents primaires supérieures
    51, 52, 53, 54, 55,  // Quadrant 5
    61, 62, 63, 64, 65,  // Quadrant 6
  ],
  lower: [
    // Dents permanentes inférieures
    31, 32, 33, 34, 35, 36, 37, 38,  // Quadrant 3
    41, 42, 43, 44, 45, 46, 47, 48,  // Quadrant 4
    // Dents primaires inférieures
    71, 72, 73, 74, 75,  // Quadrant 7
    81, 82, 83, 84, 85,  // Quadrant 8
  ],
  general: Object.keys(TOOTH_NAMES).map(Number)  // Toutes les dents
};

// Types de dents
export const TOOTH_TYPES = {
  incisor: [11, 12, 21, 22, 31, 32, 41, 42, 51, 52, 61, 62, 71, 72, 81, 82],
  canine: [13, 23, 33, 43, 53, 63, 73, 83],
  premolar: [14, 15, 24, 25, 34, 35, 44, 45],
  molar: [16, 17, 26, 27, 36, 37, 46, 47, 54, 55, 64, 65, 74, 75, 84, 85],
  wisdom: [18, 28, 38, 48]
};

// Quadrants
export const QUADRANTS = {
  1: [11, 12, 13, 14, 15, 16, 17, 18],  // Supérieur droit
  2: [21, 22, 23, 24, 25, 26, 27, 28],  // Supérieur gauche
  3: [31, 32, 33, 34, 35, 36, 37, 38],  // Inférieur gauche
  4: [41, 42, 43, 44, 45, 46, 47, 48],  // Inférieur droit
  5: [51, 52, 53, 54, 55],              // Primaire supérieur droit
  6: [61, 62, 63, 64, 65],              // Primaire supérieur gauche
  7: [71, 72, 73, 74, 75],              // Primaire inférieur gauche
  8: [81, 82, 83, 84, 85],              // Primaire inférieur droit
};

// Types TypeScript
export type ToothNumber = keyof typeof TOOTH_NAMES;
export type ToothType = 'incisor' | 'canine' | 'premolar' | 'molar' | 'wisdom' | 'other';
export type Quadrant = 'upper_right' | 'upper_left' | 'lower_left' | 'lower_right';
export type DentalGroup = 'general' | 'upper' | 'lower';

export interface ToothInfo {
  tooth_number: number;
  tooth_name: string;
  tooth_type: ToothType;
  quadrant: Quadrant;
  tooth_position: number;
  is_permanent: boolean;
}

// Fonctions utilitaires
export function getToothName(toothNumber: number): string {
  return TOOTH_NAMES[toothNumber] || `Dent ${toothNumber}`;
}

export function getToothType(toothNumber: number): ToothType {
  for (const [type, numbers] of Object.entries(TOOTH_TYPES)) {
    if (numbers.includes(toothNumber)) {
      return type as ToothType;
    }
  }
  return 'other';
}

export function getQuadrant(toothNumber: number): Quadrant {
  const firstDigit = Math.floor(toothNumber / 10);

  switch (firstDigit) {
    case 1: case 5: return 'upper_right';
    case 2: case 6: return 'upper_left';
    case 3: case 7: return 'lower_left';
    case 4: case 8: return 'lower_right';
    default: return 'upper_right';
  }
}

export function getQuadrantNumber(toothNumber: number): number {
  for (const [quadrant, numbers] of Object.entries(QUADRANTS)) {
    if (numbers.includes(toothNumber)) {
      return parseInt(quadrant);
    }
  }
  return 1;
}

export function isPermanentTooth(toothNumber: number): boolean {
  return toothNumber >= 11 && toothNumber <= 48;
}

export function isPrimaryTooth(toothNumber: number): boolean {
  return toothNumber >= 51 && toothNumber <= 85;
}

export function getTeethByGroup(groupType: DentalGroup): number[] {
  return DENTAL_GROUPS[groupType] || [];
}

export function getQuadrantName(quadrant: number): string {
  const names = {
    1: "Supérieur droit",
    2: "Supérieur gauche",
    3: "Inférieur gauche",
    4: "Inférieur droit",
    5: "Primaire supérieur droit",
    6: "Primaire supérieur gauche",
    7: "Primaire inférieur gauche",
    8: "Primaire inférieur droit",
  };
  return names[quadrant as keyof typeof names] || "Inconnu";
}

export function getToothPosition(toothNumber: number): number {
  return toothNumber % 10 || 8;
}

export function createToothInfo(toothNumber: number): ToothInfo {
  return {
    tooth_number: toothNumber,
    tooth_name: getToothName(toothNumber),
    tooth_type: getToothType(toothNumber),
    quadrant: getQuadrant(toothNumber),
    tooth_position: getToothPosition(toothNumber),
    is_permanent: isPermanentTooth(toothNumber)
  };
}
