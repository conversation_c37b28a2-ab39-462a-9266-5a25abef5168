
'use client';
import React from "react";
import { useState, useEffect } from 'react';
import {  Title,  Text, Alert } from '@mantine/core';
import { IconLicense, IconKey, IconHistory, IconAlertCircle } from '@tabler/icons-react';
import { useSearchParams } from 'next/navigation';
import LicenseInfo from '@/components/license/LicenseInfo';
import LicenseActivation from '@/components/license/LicenseActivation';
import { rem ,Paper} from "@mantine/core";

import MetaSeo from"./MetaSeo"
import "~/styles/tab.css";

function  LicensePage() {
  const iconStyle = { width: rem(14), height: rem(14) };
  const [toggleState, setToggleState] = useState(1);
 
const searchParams = useSearchParams();
  const message = searchParams?.get('message') || null;
  const [, setActiveTab] = useState<string | null>('info');

  // Set the active tab based on the message parameter
  useEffect(() => {
    if (message === 'pending_activation') {
      setActiveTab('activate');
    }
  }, [message]);

  // Get message text based on the message parameter
  const getMessageText = () => {
    switch (message) {
      case 'no_license':
        return 'You do not have a license yet. Please contact support to obtain one.';
      case 'pending_activation':
        return 'Your license is pending activation. Please enter your activation code to activate it.';
      case 'expired':
        return 'Your license has expired. Please renew it to continue using the application.';
      case 'revoked':
        return 'Your license has been revoked. Please contact support for assistance.';
      default:
        return null;
    }
  };

  const messageText = getMessageText();
const icons = [
  { icon: <IconLicense style={iconStyle} key=" License Information" />, label: " LicenseInformation" },
  {
    icon: <IconKey style={iconStyle} key="ActivateLicense" />,
    label: "Activate License",
  },
  {
    icon: <IconHistory style={iconStyle} key="LicenseHistory" />,
    label: "License History",
  },
  {
    icon: <IconAlertCircle style={iconStyle} key="Test-3" />,
    label: "Test-3",
  },
 
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return ( 
      <Paper p="xl" radius="md" withBorder mb="60" w={"100%"}>
<Title order={2} mb="xl">License Management</Title>

      {messageText && (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          color={message === 'pending_activation' ? 'blue' : 'red'}
          mb="xl"
        >
          {messageText}
        </Alert>
      )}
      <LicenseInfo />
    </Paper>
          )

    
     case 2:
      return (  
         <Paper p="xl" radius="md" withBorder mb="60" w={"100%"}>
<LicenseActivation onActivationSuccess={() => setActiveTab('info')} />
    </Paper>
      )
      case 3:
        return (  
                     <Paper p="xl" radius="md" withBorder mb="60" w={"100%"}>
 <Text>License history will be available in a future update.</Text>
    </Paper>
        )
        case 4:
          return <div>Test-4</div>;

    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default LicensePage;

 
     
