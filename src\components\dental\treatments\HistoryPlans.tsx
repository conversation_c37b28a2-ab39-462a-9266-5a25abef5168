import { Tabs, rem, Divider, Group, Box, Text, Checkbox } from "@mantine/core";
import type { CheckboxProps } from "@mantine/core";
import {
  IconCircuitResistor,
  IconPlaneTilt,
  IconBiohazard,
  IconRadioactive,
} from "@tabler/icons-react";
const CheckboxIcon: CheckboxProps["icon"] = ({ indeterminate, ...others }) =>
  indeterminate ? (
    <IconRadioactive {...others} />
  ) : (
    <IconBiohazard {...others} />
  );
const HistoryPlans = () => {
  const iconStyle = { width: rem(16), height: rem(16) };
  return (
    <Box style={{ overflow: "hidden" }} mb={8}>
      <Tabs variant="pills" defaultValue="History">
        <Box maw={"100%"} bg="var(--mantine-color-blue-light)">
          <Tabs.List>
            <Tabs.Tab
              mb={4}
              value="History"
              leftSection={<IconCircuitResistor style={iconStyle} />}
            >
              History
            </Tabs.Tab>
            <Tabs.Tab
              mb={4}
              value="Plans"
              leftSection={<IconPlaneTilt style={iconStyle} />}
            >
              Plans
            </Tabs.Tab>
          </Tabs.List>
        </Box>
        <Tabs.Panel value="History">
          <Divider my="4px" />
          <Group bg={"#ffffff"} p={4}>
            <Text fw={500} mt={5}>
              {" "}
              + new Appointment
            </Text>
          </Group>

          <Divider my="4px" />
          <Group bg={"#ffffff"} p={4}>
            <Checkbox
              icon={CheckboxIcon}
              // label="Custom icon"
              defaultChecked
            />
            <div>
              <Text fw={500}>Nov 8,2024</Text>
              <Text fz="xs" c="dimmed">
                <EMAIL>
              </Text>
            </div>
          </Group>
          <Divider my="4px" />
          <Group bg={"#ffffff"} p={4}>
            <Checkbox
              icon={CheckboxIcon}
              // label="Custom icon"
              defaultChecked
            />
            <div>
              <Text fw={500}>Nov 7,2024</Text>
              <Text fz="xs" c="dimmed">
                <EMAIL>
              </Text>
            </div>
          </Group>
          <Divider my="4px" />
        </Tabs.Panel>

        <Tabs.Panel value="Plans">Plans</Tabs.Panel>
      </Tabs>
    </Box>
  );
};

export default HistoryPlans;
