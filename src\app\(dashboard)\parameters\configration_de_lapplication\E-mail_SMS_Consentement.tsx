'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Tabs,
  Button,
  TextInput,
  Switch,
  Group,
  Stack,
  Text,
  Modal,
  Textarea,
  Table,
  Alert,
  ActionIcon,
  Box,
  Badge,
  Pagination
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconFiles,
  IconMessage,
  IconSearch,
  IconPlus,
  IconTrash,
  IconAlertTriangle,
  IconUpload
} from '@tabler/icons-react';

// Types
interface EmailModel {
  id?: number;
  title: string;
  content: string;
  isReminder: boolean;
  isVideoCall?: boolean;
}

interface SMSModel {
  id?: number;
  title: string;
  content: string;
}

interface SMSHistory {
  id: number;
  date: string;
  phoneNumber: string;
  content: string;
  status: 'sent' | 'failed' | 'pending';
}

interface ConsentModel {
  id?: number;
  title: string;
  content?: string;
}

const EmailSMSConsentement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('email-models');
  const [selectedEmailModel, setSelectedEmailModel] = useState<EmailModel>({
    title: '',
    content: '',
    isReminder: false
  });
  const [selectedSMSModel, setSelectedSMSModel] = useState<SMSModel>({
    title: '',
    content: ''
  });
  const [selectedConsentModel, setSelectedConsentModel] = useState<ConsentModel>({
    title: ''
  });
  const [smsHistoryPage, setSmsHistoryPage] = useState(1);
  const [activeTemplateTab, setActiveTemplateTab] = useState('fullname');

  // Modals
  const [consentModalOpened, { open: openConsentModal, close: closeConsentModal }] = useDisclosure(false);

  // Mock data
  const [emailModels] = useState<EmailModel[]>([]);
  const [smsModels] = useState<SMSModel[]>([]);
  const [smsHistory] = useState<SMSHistory[]>([]);
  const [consentModels] = useState<ConsentModel[]>([]);

  const handleEmailSubmit = () => {
    if (!selectedEmailModel.title.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le titre est obligatoire',
        color: 'red'
      });
      return;
    }

    notifications.show({
      title: 'Succès',
      message: 'Modèle email enregistré avec succès',
      color: 'green'
    });
  };

  const handleSMSSubmit = () => {
    if (!selectedSMSModel.title.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le titre est obligatoire',
        color: 'red'
      });
      return;
    }

    notifications.show({
      title: 'Succès',
      message: 'Modèle SMS enregistré avec succès',
      color: 'green'
    });
  };

  const handleConsentSubmit = () => {
    if (!selectedConsentModel.title.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le titre est obligatoire',
        color: 'red'
      });
      return;
    }

    notifications.show({
      title: 'Succès',
      message: 'Modèle de consentement enregistré avec succès',
      color: 'green'
    });
    closeConsentModal();
  };

  const handleCancel = () => {
    setSelectedEmailModel({ title: '', content: '', isReminder: false });
    setSelectedSMSModel({ title: '', content: '' });
  };

  const renderTemplateButtons = () => {
    const templates = [
      { tab: 'fullname', content: '{{ patient::fullname }}' },
      { tab: 'date', content: '{{ patient::fullname }} {{ appointment::date }}' },
      { tab: 'time', content: '{{ patient::fullname }} {{ appointment::date }} {{ appointment::time }}' }
    ];

    const currentTemplate = templates.find(t => t.tab === activeTemplateTab);

    return (
      <Box className="bg-gray-50 p-4 rounded-lg">
        <Tabs value={activeTemplateTab} onChange={(value) => setActiveTemplateTab(value || 'fullname')}>
          <Tabs.List>
            <Tabs.Tab value="fullname">Nom complet</Tabs.Tab>
            <Tabs.Tab value="date">Date du rendez-vous</Tabs.Tab>
            <Tabs.Tab value="time">Horaire du rendez-vous</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={activeTemplateTab} pt="md">
            <Text size="sm" className="text-blue-600">
              {currentTemplate?.content}
            </Text>
          </Tabs.Panel>
        </Tabs>
      </Box>
    );
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-blue-600 text-white px-6 py-4 flex items-center gap-3">
        <IconFiles size={24} />
        <Title order={2} className="text-white font-medium">
          E-mail, SMS, Consentement
        </Title>
        <div className="flex-1" />
        {activeTab === 'consent-models' && (
          <Button
            leftSection={<IconPlus size={16} />}
            variant="filled"
            color="blue"
            onClick={openConsentModal}
            className="bg-blue-500 hover:bg-blue-400"
          >
            Ajouter un Modèles de consentement
          </Button>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'email-models')} className="flex-1 flex flex-col">
          <Tabs.List className="border-b border-gray-200 px-6">
            <Tabs.Tab value="email-models">Modèles Email</Tabs.Tab>
            <Tabs.Tab value="sms-models">Modèles SMS</Tabs.Tab>
            <Tabs.Tab value="sms-history">Historique SMS</Tabs.Tab>
            <Tabs.Tab value="consent-models">Modèles de consentement</Tabs.Tab>
          </Tabs.List>

          {/* Email Models Tab */}
          <Tabs.Panel value="email-models" className="flex-1 flex">
            <div className="flex flex-1">
              {/* Sidebar */}
              <div className="w-80 border-r border-gray-200 flex flex-col">
                <div className="bg-blue-600 text-white px-4 py-3 flex items-center gap-3">
                  <IconMessage size={20} />
                  <Title order={4} className="text-white">List des modèles</Title>
                  <div className="flex-1" />
                  <ActionIcon variant="subtle" color="white" size="sm">
                    <IconSearch size={16} />
                  </ActionIcon>
                </div>

                <div className="flex-1 p-4">
                  {emailModels.length === 0 ? (
                    <Alert icon={<IconAlertTriangle size={16} />} color="yellow" className="text-sm">
                      Aucune modèles à afficher
                    </Alert>
                  ) : (
                    <Stack gap="xs">
                      {emailModels.map((model) => (
                        <Paper
                          key={model.id}
                          p="sm"
                          className="cursor-pointer hover:bg-gray-50 border"
                          onClick={() => setSelectedEmailModel(model)}
                        >
                          <Text size="sm" fw={500}>{model.title}</Text>
                        </Paper>
                      ))}
                    </Stack>
                  )}
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 flex flex-col">
                <form onSubmit={(e) => { e.preventDefault(); handleEmailSubmit(); }} className="flex-1 flex flex-col">
                  <div className="flex-1 p-6">
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <TextInput
                        label="Titre"
                        placeholder="Titre du modèle"
                        value={selectedEmailModel.title}
                        onChange={(e) => setSelectedEmailModel(prev => ({ ...prev, title: e.target.value }))}
                        required
                        className="col-span-2"
                      />

                      <div className="flex items-end">
                        <Switch
                          label="Pour rappel"
                          checked={selectedEmailModel.isReminder}
                          onChange={(e) => setSelectedEmailModel(prev => ({ ...prev, isReminder: e.currentTarget.checked }))}
                          color="blue"
                        />
                      </div>
                    </div>

                    {!selectedEmailModel.isReminder && (
                      <div className="flex-1">
                        <Text size="sm" fw={500} mb="xs">Contenu du message</Text>
                        <Textarea
                          placeholder="Contenu de l'email..."
                          value={selectedEmailModel.content}
                          onChange={(e) => setSelectedEmailModel(prev => ({ ...prev, content: e.target.value }))}
                          minRows={15}
                          className="h-full"
                        />
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="border-t border-gray-200 p-4 flex items-center gap-3">
                    <ActionIcon
                      variant="filled"
                      color="red"
                      size="lg"
                      disabled={!selectedEmailModel.id}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>

                    <div className="flex-1" />

                    <Button type="submit" variant="filled" color="blue" disabled={!selectedEmailModel.title.trim()}>
                      Enregistrer
                    </Button>

                    <Button variant="filled" color="red" onClick={handleCancel}>
                      Annuler
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </Tabs.Panel>

          {/* SMS Models Tab */}
          <Tabs.Panel value="sms-models" className="flex-1 flex">
            <div className="flex flex-1">
              {/* Sidebar */}
              <div className="w-80 border-r border-gray-200 flex flex-col">
                <div className="bg-blue-600 text-white px-4 py-3 flex items-center gap-3">
                  <IconMessage size={20} />
                  <Title order={4} className="text-white">List des modèles</Title>
                  <div className="flex-1" />
                  <ActionIcon variant="subtle" color="white" size="sm">
                    <IconSearch size={16} />
                  </ActionIcon>
                </div>

                <div className="flex-1 p-4">
                  {smsModels.length === 0 ? (
                    <Alert icon={<IconAlertTriangle size={16} />} color="yellow" className="text-sm">
                      Aucune modèles à afficher
                    </Alert>
                  ) : (
                    <Stack gap="xs">
                      {smsModels.map((model) => (
                        <Paper
                          key={model.id}
                          p="sm"
                          className="cursor-pointer hover:bg-gray-50 border"
                          onClick={() => setSelectedSMSModel(model)}
                        >
                          <Text size="sm" fw={500}>{model.title}</Text>
                        </Paper>
                      ))}
                    </Stack>
                  )}
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 flex flex-col">
                <form onSubmit={(e) => { e.preventDefault(); handleSMSSubmit(); }} className="flex-1 flex flex-col">
                  <div className="flex-1 p-6">
                    <TextInput
                      label="Titre"
                      placeholder="Titre du modèle"
                      value={selectedSMSModel.title}
                      onChange={(e) => setSelectedSMSModel(prev => ({ ...prev, title: e.target.value }))}
                      required
                      className="mb-6"
                    />

                    {/* Template Variables */}
                    {renderTemplateButtons()}

                    <div className="mt-6">
                      <Text size="sm" fw={500} mb="xs">Contenu du message</Text>
                      <Textarea
                        placeholder="Contenu du SMS..."
                        value={selectedSMSModel.content}
                        onChange={(e) => setSelectedSMSModel(prev => ({ ...prev, content: e.target.value }))}
                        minRows={8}
                      />
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="border-t border-gray-200 p-4 flex items-center gap-3">
                    <ActionIcon
                      variant="filled"
                      color="red"
                      size="lg"
                      disabled={!selectedSMSModel.id}
                    >
                      <IconTrash size={16} />
                    </ActionIcon>

                    <div className="flex-1" />

                    <Button type="submit" variant="filled" color="blue" disabled={!selectedSMSModel.title.trim()}>
                      Enregistrer
                    </Button>

                    <Button variant="filled" color="red" onClick={handleCancel}>
                      Annuler
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </Tabs.Panel>

          {/* SMS History Tab */}
          <Tabs.Panel value="sms-history" className="flex-1 p-6">
            <div className="mb-4">
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Date</Table.Th>
                    <Table.Th>Numéro de Tél.</Table.Th>
                    <Table.Th>Contenu du message</Table.Th>
                    <Table.Th>État</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {smsHistory.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={4} className="text-center py-8">
                        <Text c="dimmed">Aucun élément trouvé.</Text>
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    smsHistory.map((item) => (
                      <Table.Tr key={item.id}>
                        <Table.Td>{item.date}</Table.Td>
                        <Table.Td>{item.phoneNumber}</Table.Td>
                        <Table.Td>{item.content}</Table.Td>
                        <Table.Td>
                          <Badge
                            color={item.status === 'sent' ? 'green' : item.status === 'failed' ? 'red' : 'yellow'}
                            variant="light"
                          >
                            {item.status === 'sent' ? 'Envoyé' : item.status === 'failed' ? 'Échec' : 'En attente'}
                          </Badge>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between">
              <Text size="sm" c="dimmed">
                Page 1 - Lignes par Page 20 - 0 de 0
              </Text>
              <Pagination total={1} value={smsHistoryPage} onChange={setSmsHistoryPage} size="sm" />
            </div>
          </Tabs.Panel>

          {/* Consent Models Tab */}
          <Tabs.Panel value="consent-models" className="flex-1 p-6">
            <div className="mb-4">
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Titre</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {consentModels.length === 0 ? (
                    <Table.Tr>
                      <Table.Td className="text-center py-8">
                        <Text c="dimmed">Aucun élément trouvé.</Text>
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    consentModels.map((model) => (
                      <Table.Tr key={model.id}>
                        <Table.Td>{model.title}</Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </div>
          </Tabs.Panel>
        </Tabs>
      </div>

      {/* Consent Model Modal */}
      <Modal
        opened={consentModalOpened}
        onClose={closeConsentModal}
        title={
          <div className="flex items-center gap-2">
            <IconFiles size={20} className="text-blue-600" />
            <Text fw={600} className="text-blue-600">Ajouter un Modèles de consentement</Text>
          </div>
        }
        size="lg"
        centered
      >
        <form onSubmit={(e) => { e.preventDefault(); handleConsentSubmit(); }}>
          <Stack gap="md">
            <TextInput
              label={
                <span>
                  Titre <span className="text-red-500">*</span>
                </span>
              }
              placeholder="Titre du modèle de consentement"
              value={selectedConsentModel.title}
              onChange={(e) => setSelectedConsentModel(prev => ({ ...prev, title: e.target.value }))}
              required
            />

            {/* File Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <div className="flex flex-col items-center gap-2">
                <IconUpload size={32} className="text-gray-400" />
                <Text c="dimmed" size="sm">
                  Glisser et déposer ou cliquer
                </Text>
              </div>
            </div>

            <Group justify="flex-end" gap="sm">
              <Button
                variant="filled"
                color="blue"
                type="submit"
                disabled={!selectedConsentModel.title.trim()}
              >
                Enregistrer
              </Button>
              <Button
                variant="filled"
                color="red"
                onClick={closeConsentModal}
              >
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default EmailSMSConsentement;
