'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  TextInput,
  Paper,
  Title,
  Container,
  Button,
  Text,
  Anchor,
  Alert,
  Stack,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconCheck } from '@tabler/icons-react';
import authService from '~/services/authService';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const form = useForm({
    initialValues: {
      email: '',
    },
    validate: {
      email: (value) => (/^\S+@\S+\.[a-zA-Z]{2,}$/.test(value) ? null : 'Invalid email address'),
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true);

      await authService.forgotPassword(values.email);

      setSubmitted(true);

      notifications.show({
        title: 'Activation Request Submitted',
        message: 'If your email is registered, you will receive license activation instructions.',
        color: 'green',
      });

      // Redirect to license activation page after a short delay
      setTimeout(() => {
        router.push('/license?message=pending_activation');
      }, 2000);
    } catch (error) {
      console.error('Forgot password error:', error);

      // Still show success message even on error for security reasons
      setSubmitted(true);

      notifications.show({
        title: 'Activation Request Submitted',
        message: 'If your email is registered, you will receive license activation instructions.',
        color: 'green',
      });

      // Redirect to license activation page after a short delay even on error
      // This maintains security by not revealing whether the email exists
      setTimeout(() => {
        router.push('/license?message=pending_activation');
      }, 2000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container size="sm">
      <Paper radius="md" p="xl" withBorder style={{
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
        <Title order={2} ta="center" mt="md" mb="md">
          License Activation
        </Title>

        {submitted ? (
          <Stack>
            <Alert icon={<IconCheck size={16} />} title="Activation Request Submitted" color="green">
              If your email is registered, you will receive license activation instructions shortly.
              Please check your email and follow the instructions to activate your license.
            </Alert>

            <Text size="sm" ta="center" mt="md">
              You will be redirected to the license activation page in a moment...
            </Text>

            <Button loading fullWidth mt="md">
              Redirecting...
            </Button>
          </Stack>
        ) : (
          <>
            <Text c="dimmed" size="sm" ta="center" mb="xl">
              Enter your email address to receive your license activation code
            </Text>

            <Alert icon={<IconAlertCircle size={16} />} color="blue" mb="md">
              If your email is registered in our system, you will receive license activation instructions.
              You will be redirected to the license activation page after submission.
            </Alert>

            <form onSubmit={form.onSubmit(handleSubmit)}>
              <Stack>
                <TextInput
                  label="Email"
                  placeholder="<EMAIL>"
                  required
                  {...form.getInputProps('email')}
                />

                <Button fullWidth mt="xl" type="submit" loading={loading}>
                  Send Instructions & Activate License
                </Button>
              </Stack>
            </form>
          </>
        )}

        <Text ta="center" mt="md">
          Already have an activation code?{' '}
          <Anchor component={Link} href="/license?message=pending_activation">
            Go to License Activation
          </Anchor>
        </Text>
      </Paper>
    </Container>
  );
}
