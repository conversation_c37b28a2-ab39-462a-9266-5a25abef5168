'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Select,
} from '@mantine/core';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconScale,
} from '@tabler/icons-react';

const BalanceAgee = () => {
  // États pour les filtres
  const [numeroFacture, setNumeroFacture] = useState('');

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconScale size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Balance âgée
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-64 bg-white border-r border-gray-200"
        >
          <div className="space-y-2">
            {/* Filtre N°. Facture */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  N°. Facture
                </Text>
              </div>
              <div className="p-2">
                <Select
                  placeholder="Sélectionner"
                  data={['+15', '+30', '+60', '+120']}
                  value={numeroFacture}
                  onChange={(value) => setNumeroFacture(value || '')}
                  size="xs"
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Zone principale du tableau */}
        <div className="flex-1 bg-white">
          <Table
            striped={false}
            highlightOnHover={false}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  N°. Facture
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  +15
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  +30
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  +60
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  +120
                </Table.Th>
                {/* Colonnes vides supplémentaires */}
                {Array.from({ length: 6 }, (_, index) => (
                  <Table.Th
                    key={index}
                    className="border-r border-gray-300 bg-gray-100 w-20"
                  />
                ))}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {/* Ligne Total avec fond vert */}
              <Table.Tr className="bg-green-100">
                <Table.Td className="border-r border-gray-300 bg-green-100">
                  <Text size="sm" fw={500} className="text-gray-800">
                    Total
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200">
                  <div className="text-center px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      +15
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200">
                  <div className="text-center px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      +30
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200">
                  <div className="text-center px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      +60
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200">
                  <div className="text-center px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      +120
                    </Text>
                  </div>
                </Table.Td>
                {/* Cellules vides avec fond vert */}
                {Array.from({ length: 6 }, (_, index) => (
                  <Table.Td
                    key={index}
                    className="border-r border-gray-300 bg-green-100"
                  />
                ))}
              </Table.Tr>

              {/* Lignes vides pour remplir l'espace */}
              {Array.from({ length: 25 }, (_, index) => (
                <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                  {Array.from({ length: 11 }, (_, cellIndex) => (
                    <Table.Td
                      key={cellIndex}
                      className="border-r border-gray-300 h-8"
                    />
                  ))}
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
      </div>
    </Box>
  );
};

export default BalanceAgee;
