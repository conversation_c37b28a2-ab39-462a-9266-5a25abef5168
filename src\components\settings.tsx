'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Mantine components
import {
  Title, Text, Paper, Button, Group, Grid, Card, Stack, Switch, Divider,
  Select, LoadingOverlay, ThemeIcon, SimpleGrid, useMantineColorScheme,
  Checkbox, NumberInput, Alert
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { DatePickerInput, TimeInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';

// Icons
import {
  IconArrowLeft, IconUserCircle, IconBell, IconDeviceFloppy, IconAlertCircle,
  IconCheck, IconCalendar, IconClock, IconShield, IconSun, IconMoon,
  IconCalendarTime, IconCircleHalf2
} from '@tabler/icons-react';

// Services
import authService from '@/services/authService';
import userManagementService, { SubscriptionPackage } from '@/services/userManagementService';
import settingsService from '@/services/settingsService';

// Components
import SubscriptionCard from '@/components/settings/SubscriptionCard';
import UserManagementCard from '@/components/settings/UserManagementCard';
import DataMigrationCard from '@/components/settings/DataMigrationCard';
import LanguageSelector from '@/components/settings/LanguageSelector';

// Using the UserSettings interface from the settingsService
interface UserSettings {
  theme: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  appointmentReminders: boolean;
  marketingEmails: boolean;
  language: string;
  timeFormat: string;
  dateFormat: string;
  calendarView: string;
  calendarStartHour: number;
  calendarEndHour: number;
  workingHoursSettings: {
    morningStart: string;
    morningEnd: string;
    afternoonStart: string;
    afternoonEnd: string;
    breakEnabled: boolean;
    breakDuration: number;
  };
  workDaysSettings: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturdayFirstHalf: boolean;
    vacationDays: number;
    holidays: number;
    emergencyDays: number;
    vacationPeriods: {
      startDate: Date | null;
      endDate: Date | null;
      description: string;
    }[];
    holidayDates: {
      date: Date | null;
      description: string;
    }[];
    emergencyDates: {
      date: Date | null;
      reason: string;
    }[];
  };
  privacySettings: {
    showProfileToOtherDoctors: boolean;
    shareAnonymizedDataForResearch: boolean;
    allowPatientFeedback: boolean;
  };
  accessibilitySettings: {
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
    fontSize: number;
  };
}

export default function SettingsPage() {
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [subscription, setSubscription] = useState<SubscriptionPackage | null>(null);
  const [userCounts, setUserCounts] = useState({ assistants: 0, patients: 0 });
  const [settingsLoaded, setSettingsLoaded] = useState(false);
  const [, setIsClient] = useState(false);
  const router = useRouter();
  const { colorScheme, setColorScheme } = useMantineColorScheme();

  const form = useForm({
    initialValues: {
      theme: 'light',
      emailNotifications: true,
      smsNotifications: true,
      appointmentReminders: true,
      marketingEmails: false,
      language: 'fr',
      timeFormat: '12h',
      dateFormat: 'MM/DD/YYYY',
      calendarView: 'month',
      calendarStartHour: 8,
      calendarEndHour: 18,
      workingHoursSettings: {
        morningStart: '09:00',
        morningEnd: '12:00',
        afternoonStart: '13:00',
        afternoonEnd: '17:00',
        breakEnabled: true,
        breakDuration: 60,
      },
      workDaysSettings: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturdayFirstHalf: true,
        vacationDays: 20,
        holidays: 10,
        emergencyDays: 5,
        vacationPeriods: [] as Array<{
          startDate: Date | null;
          endDate: Date | null;
          description: string;
        }>,
        holidayDates: [] as Array<{
          date: Date | null;
          description: string;
        }>,
        emergencyDates: [] as Array<{
          date: Date | null;
          reason: string;
        }>,
      },
      privacySettings: {
        showProfileToOtherDoctors: true,
        shareAnonymizedDataForResearch: true,
        allowPatientFeedback: true,
      },
      accessibilitySettings: {
        highContrast: false,
        largeText: false,
        screenReader: false,
        fontSize: 16,
      },
    },
  });

  // Effect to set client-side flag to prevent hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Effect to update document font size when it changes in the form
  useEffect(() => {
    if (!loading && form.values.accessibilitySettings) {
      document.documentElement.style.fontSize = `${form.values.accessibilitySettings.fontSize}px`;
    }
  }, [loading, form.values.accessibilitySettings]);



  useEffect(() => {
    // Avoid loading settings if already loaded
    if (settingsLoaded) {
      return;
    }

    const fetchSettings = async () => {
      try {
        setLoading(true);

        // Check if user is authenticated
        const isAuth = await authService.validateAuthentication();
        if (!isAuth) {
          router.push('/login');
          return;
        }

        // Fetch user settings from the settings service
        const userSettings = await settingsService.getUserSettings();

        // Update the theme to match the current color scheme
        userSettings.theme = colorScheme;

        setSettings(userSettings);

        // Update the appointment system with the current settings without showing notification
        // This ensures the web-frontend has the latest settings
        await settingsService.updateAppointmentSystem(userSettings, false);
        console.log('Settings updated in localStorage for web-frontend without notification');

        // Fetch subscription data
        const currentSubscription = await userManagementService.getCurrentSubscription();
        setSubscription(currentSubscription);

        // Fetch user counts
        const counts = await userManagementService.getUserCounts();
        setUserCounts(counts);

        // Set form values
        form.setValues({
          theme: userSettings.theme,
          emailNotifications: userSettings.emailNotifications,
          smsNotifications: userSettings.smsNotifications,
          appointmentReminders: userSettings.appointmentReminders,
          marketingEmails: userSettings.marketingEmails,
          language: userSettings.language,
          timeFormat: userSettings.timeFormat,
          dateFormat: userSettings.dateFormat,
          calendarView: userSettings.calendarView,
          calendarStartHour: userSettings.calendarStartHour,
          calendarEndHour: userSettings.calendarEndHour,
          workingHoursSettings: {
            morningStart: userSettings.workingHoursSettings.morningStart,
            morningEnd: userSettings.workingHoursSettings.morningEnd,
            afternoonStart: userSettings.workingHoursSettings.afternoonStart,
            afternoonEnd: userSettings.workingHoursSettings.afternoonEnd,
            breakEnabled: userSettings.workingHoursSettings.breakEnabled,
            breakDuration: userSettings.workingHoursSettings.breakDuration,
          },
          workDaysSettings: {
            monday: userSettings.workDaysSettings.monday,
            tuesday: userSettings.workDaysSettings.tuesday,
            wednesday: userSettings.workDaysSettings.wednesday,
            thursday: userSettings.workDaysSettings.thursday,
            friday: userSettings.workDaysSettings.friday,
            saturdayFirstHalf: userSettings.workDaysSettings.saturdayFirstHalf,
            vacationDays: userSettings.workDaysSettings.vacationDays,
            holidays: userSettings.workDaysSettings.holidays,
            emergencyDays: userSettings.workDaysSettings.emergencyDays,
            vacationPeriods: userSettings.workDaysSettings.vacationPeriods,
            holidayDates: userSettings.workDaysSettings.holidayDates,
            emergencyDates: userSettings.workDaysSettings.emergencyDates,
          },
          privacySettings: {
            showProfileToOtherDoctors: userSettings.privacySettings.showProfileToOtherDoctors,
            shareAnonymizedDataForResearch: userSettings.privacySettings.shareAnonymizedDataForResearch,
            allowPatientFeedback: userSettings.privacySettings.allowPatientFeedback,
          },
          accessibilitySettings: {
            highContrast: userSettings.accessibilitySettings.highContrast,
            largeText: userSettings.accessibilitySettings.largeText,
            screenReader: userSettings.accessibilitySettings.screenReader,
            fontSize: userSettings.accessibilitySettings.fontSize || 16,
          },
        });

        // Mark settings as loaded
        setSettingsLoaded(true);
      } catch (error) {
        console.error('Error fetching settings:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to load settings. Please try again later.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [colorScheme, router, settingsLoaded]);

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setSubmitting(true);

      // Save the settings using the settings service (includes theme)
      const success = await settingsService.saveUserSettings(values as UserSettings);

      if (!success) {
        throw new Error('Failed to save settings');
      }

      // Update the local settings state
      setSettings(values as UserSettings);

      // Show success notification
      notifications.show({
        title: 'Success',
        message: 'Settings updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      // If the working hours settings were changed, update the appointment system
      if (settings && (
        JSON.stringify(settings.workingHoursSettings) !== JSON.stringify(values.workingHoursSettings) ||
        JSON.stringify(settings.workDaysSettings) !== JSON.stringify(values.workDaysSettings)
      )) {
        // Update the appointment system with the new settings but don't show a duplicate notification
        await settingsService.updateAppointmentSystem(values as UserSettings, false);
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update settings. Please try again later.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      <Group mb="xl">
        <Button
          leftSection={<IconArrowLeft size={16} />}
          variant="subtle"
          onClick={() => router.push('/dashboard')}
        >
          Back to Dashboard
        </Button>
        <Title>Doctor Settings</Title>
      </Group>

      {/* Subscription Management Card */}
      <SubscriptionCard
        subscription={subscription}
        userCounts={userCounts}
      />

      {/* User Management Card */}
      <UserManagementCard />

      {/* Data Migration Card */}
      <DataMigrationCard />

      <Paper withBorder p="md" radius="md" pos="relative">
        <LoadingOverlay visible={loading} overlayProps={{ radius: "sm", blur: 2 }} />

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Grid gutter="xl">
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="blue">
                    <IconUserCircle size={20} />
                  </ThemeIcon>
                  <Title order={3}>Appearance</Title>
                </Group>

                <Stack>
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Theme</Text>
                      <Text size="sm" c="dimmed">
                        Choose between light, dark, or auto mode (follows system preference)
                      </Text>
                    </div>
                    <Group>
                      <Button
                        variant={colorScheme === 'light' ? 'filled' : 'outline'}
                        onClick={() => {
                          setColorScheme('light');
                          form.setFieldValue('theme', 'light');
                        }}
                        leftSection={<IconSun size={16} />}
                      >
                        Light
                      </Button>
                      <Button
                        variant={colorScheme === 'dark' ? 'filled' : 'outline'}
                        onClick={() => {
                          setColorScheme('dark');
                          form.setFieldValue('theme', 'dark');
                        }}
                        leftSection={<IconMoon size={16} />}
                      >
                        Dark
                      </Button>
                      <Button
                        variant={colorScheme === 'auto' ? 'filled' : 'outline'}
                        onClick={() => {
                          setColorScheme('auto');
                          form.setFieldValue('theme', 'auto');
                        }}
                        leftSection={<IconCircleHalf2 size={16} />}
                      >
                        Auto
                      </Button>
                    </Group>
                  </Group>

                  <Divider />

                  <LanguageSelector
                    label="Language"
                    description="Select your preferred language for the application"
                  />

                  <Divider />

                  <Title order={4} mt="md">Accessibility</Title>

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>High Contrast</Text>
                      <Text size="sm" c="dimmed">
                        Increase contrast for better visibility
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.accessibilitySettings.highContrast}
                      onChange={(event) => form.setFieldValue('accessibilitySettings.highContrast', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Large Text</Text>
                      <Text size="sm" c="dimmed">
                        Increase text size throughout the application
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.accessibilitySettings.largeText}
                      onChange={(event) => form.setFieldValue('accessibilitySettings.largeText', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <div>
                    <Group justify="space-between">
                      <div>
                        <Text fw={500}>Font Size</Text>
                        <Text size="sm" c="dimmed">
                          Adjust the base font size for the entire application
                        </Text>
                      </div>
                      <Text fw={500}>{form.values.accessibilitySettings.fontSize || 16}px</Text>
                    </Group>
                    <input
                      step={2}
                      className="range range-primary range-xs w-full mt-2"
                      type="range"
                      min="10"
                      max="22"
                      value={form.values.accessibilitySettings.fontSize || 16}
                      onChange={(e) => form.setFieldValue('accessibilitySettings.fontSize', parseInt(e.target.value, 10))}
                    />
                    <div className="flex w-full justify-between px-2 text-xs mt-1">
                      {[...Array<number>(7)].map((_, i) => (
                        <span
                          key={i}
                          className="flex cursor-pointer flex-col items-center"
                          onClick={() => form.setFieldValue('accessibilitySettings.fontSize', 10 + i * 2)}
                        >
                          <span>|</span>
                          {10 + i * 2}px
                        </span>
                      ))}
                    </div>
                  </div>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Screen Reader Support</Text>
                      <Text size="sm" c="dimmed">
                        Optimize for screen readers
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.accessibilitySettings.screenReader}
                      onChange={(event) => form.setFieldValue('accessibilitySettings.screenReader', event.currentTarget.checked)}
                    />
                  </Group>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="green">
                    <IconCalendar size={20} />
                  </ThemeIcon>
                  <Title order={3}>Calendar Settings</Title>
                </Group>

                <Stack>
                  <Select
                    label="Default Calendar View"
                    placeholder="Select default view"
                    data={[
                      { value: 'day', label: 'Day' },
                      { value: 'week', label: 'Week' },
                      { value: 'month', label: 'Month' },
                      { value: 'agenda', label: 'Agenda' },
                    ]}
                    {...form.getInputProps('calendarView')}
                  />

                  <Divider />

                  <Select
                    label="Time Format"
                    placeholder="Select time format"
                    data={[
                      { value: '12h', label: '12-hour (AM/PM)' },
                      { value: '24h', label: '24-hour' },
                    ]}
                    {...form.getInputProps('timeFormat')}
                  />

                  <Divider />

                  <Select
                    label="Date Format"
                    placeholder="Select date format"
                    data={[
                      { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
                      { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
                      { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
                    ]}
                    {...form.getInputProps('dateFormat')}
                  />

                  <Divider />

                  <Group grow>
                    <Select
                      label="Calendar Start Hour"
                      placeholder="Select start hour"
                      data={Array.from({ length: 24 }, (_, i) => ({
                        value: i.toString(),
                        label: form.values.timeFormat === '12h'
                          ? `${i === 0 ? 12 : i > 12 ? i - 12 : i}${i < 12 ? 'AM' : 'PM'}`
                          : `${i.toString().padStart(2, '0')}:00`,
                      }))}
                      value={form.values.calendarStartHour.toString()}
                      onChange={(value) => form.setFieldValue('calendarStartHour', parseInt(value || '8'))}
                    />

                    <Select
                      label="Calendar End Hour"
                      placeholder="Select end hour"
                      data={Array.from({ length: 24 }, (_, i) => ({
                        value: i.toString(),
                        label: form.values.timeFormat === '12h'
                          ? `${i === 0 ? 12 : i > 12 ? i - 12 : i}${i < 12 ? 'AM' : 'PM'}`
                          : `${i.toString().padStart(2, '0')}:00`,
                      }))}
                      value={form.values.calendarEndHour.toString()}
                      onChange={(value) => form.setFieldValue('calendarEndHour', parseInt(value || '18'))}
                    />
                  </Group>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="cyan">
                    <IconClock size={20} />
                  </ThemeIcon>
                  <Title order={3}>Working Hours</Title>
                </Group>

                <Stack>
                  <Title order={4}>Morning Hours</Title>
                  <Text size="sm" c="dimmed" mb="xs">
                    Set your morning working hours
                  </Text>

                  <Group grow>
                    <TimeInput
                      label="Morning Start"
                      placeholder="09:00"
                      value={form.values.workingHoursSettings.morningStart}
                      onChange={(event) => form.setFieldValue('workingHoursSettings.morningStart', event.currentTarget.value)}
                      leftSection={<IconClock size={16} />}
                    />

                    <TimeInput
                      label="Morning End"
                      placeholder="12:00"
                      value={form.values.workingHoursSettings.morningEnd}
                      onChange={(event) => form.setFieldValue('workingHoursSettings.morningEnd', event.currentTarget.value)}
                      leftSection={<IconClock size={16} />}
                    />
                  </Group>

                  <Divider my="md" />

                  <Group justify="space-between" mb="xs">
                    <div>
                      <Text fw={500}>Lunch Break</Text>
                      <Text size="sm" c="dimmed">
                        Enable lunch break between morning and afternoon hours
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.workingHoursSettings.breakEnabled}
                      onChange={(event) => form.setFieldValue('workingHoursSettings.breakEnabled', event.currentTarget.checked)}
                    />
                  </Group>

                  {form.values.workingHoursSettings.breakEnabled && (
                    <NumberInput
                      label="Break Duration (minutes)"
                      placeholder="60"
                      value={form.values.workingHoursSettings.breakDuration}
                      onChange={(value) => form.setFieldValue('workingHoursSettings.breakDuration', typeof value === 'number' ? value : 60)}
                      min={15}
                      max={120}
                      step={15}
                      mb="md"
                    />
                  )}

                  <Divider my="md" />

                  <Title order={4}>Afternoon Hours</Title>
                  <Text size="sm" c="dimmed" mb="xs">
                    Set your afternoon working hours
                  </Text>

                  <Group grow>
                    <TimeInput
                      label="Afternoon Start"
                      placeholder="13:00"
                      value={form.values.workingHoursSettings.afternoonStart}
                      onChange={(event) => form.setFieldValue('workingHoursSettings.afternoonStart', event.currentTarget.value)}
                      leftSection={<IconClock size={16} />}
                    />

                    <TimeInput
                      label="Afternoon End"
                      placeholder="17:00"
                      value={form.values.workingHoursSettings.afternoonEnd}
                      onChange={(event) => form.setFieldValue('workingHoursSettings.afternoonEnd', event.currentTarget.value)}
                      leftSection={<IconClock size={16} />}
                    />
                  </Group>

                  <Alert color="blue" icon={<IconAlertCircle size={16} />} mt="md">
                    These working hours will be used to generate available time slots for appointments.
                    Patients will only be able to book appointments during these hours.
                  </Alert>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="teal">
                    <IconCalendarTime size={20} />
                  </ThemeIcon>
                  <Title order={3}>Work Days & Vacation</Title>
                </Group>

                <Stack>
                  <Title order={4}>Work Days</Title>
                  <Text size="sm" c="dimmed" mb="xs">
                    Select the days you work at your practice
                  </Text>

                  <SimpleGrid cols={{ base: 1, sm: 2 }}>
                    <Checkbox
                      label="Monday"
                      checked={form.values.workDaysSettings.monday}
                      onChange={(event) => form.setFieldValue('workDaysSettings.monday', event.currentTarget.checked)}
                    />
                    <Checkbox
                      label="Tuesday"
                      checked={form.values.workDaysSettings.tuesday}
                      onChange={(event) => form.setFieldValue('workDaysSettings.tuesday', event.currentTarget.checked)}
                    />
                    <Checkbox
                      label="Wednesday"
                      checked={form.values.workDaysSettings.wednesday}
                      onChange={(event) => form.setFieldValue('workDaysSettings.wednesday', event.currentTarget.checked)}
                    />
                    <Checkbox
                      label="Thursday"
                      checked={form.values.workDaysSettings.thursday}
                      onChange={(event) => form.setFieldValue('workDaysSettings.thursday', event.currentTarget.checked)}
                    />
                    <Checkbox
                      label="Friday"
                      checked={form.values.workDaysSettings.friday}
                      onChange={(event) => form.setFieldValue('workDaysSettings.friday', event.currentTarget.checked)}
                    />
                    <Checkbox
                      label="Saturday (First Half)"
                      checked={form.values.workDaysSettings.saturdayFirstHalf}
                      onChange={(event) => form.setFieldValue('workDaysSettings.saturdayFirstHalf', event.currentTarget.checked)}
                    />
                  </SimpleGrid>

                  <Divider my="md" />

                  <Title order={4}>Vacation & Time Off</Title>
                  <Text size="sm" c="dimmed" mb="xs">
                    Configure your annual vacation days and other time off
                  </Text>

                  <Group align="flex-start" grow>
                    <div>
                      <Text fw={500} mb="xs">Summer Vacation Days</Text>
                      <NumberInput
                        value={form.values.workDaysSettings.vacationDays}
                        onChange={(value) => form.setFieldValue('workDaysSettings.vacationDays', typeof value === 'number' ? value : 15)}
                        min={15}
                        max={30}
                        step={1}
                        clampBehavior="strict"
                      />
                      <Text size="xs" c="dimmed" mt={5}>
                        Range: 15-30 days
                      </Text>
                    </div>

                    <div>
                      <Text fw={500} mb="xs">Holiday Days</Text>
                      <NumberInput
                        value={form.values.workDaysSettings.holidays}
                        onChange={(value) => form.setFieldValue('workDaysSettings.holidays', typeof value === 'number' ? value : 10)}
                        min={0}
                        max={20}
                        step={1}
                      />
                    </div>

                    <div>
                      <Text fw={500} mb="xs">Emergency Days</Text>
                      <NumberInput
                        value={form.values.workDaysSettings.emergencyDays}
                        onChange={(value) => form.setFieldValue('workDaysSettings.emergencyDays', typeof value === 'number' ? value : 5)}
                        min={0}
                        max={10}
                        step={1}
                      />
                      <Text size="xs" c="dimmed" mt={5}>
                        For unexpected absences
                      </Text>
                    </div>
                  </Group>

                  <Divider my="md" />

                  <Title order={4}>Vacation Periods</Title>
                  <Text size="sm" c="dimmed" mb="md">
                    Specify exact dates for your vacation periods
                  </Text>

                  {form.values.workDaysSettings.vacationPeriods.map((period, index) => (
                    <Card key={index} withBorder p="sm" mb="sm">
                      <Group mb="xs">
                        <div style={{ flex: 1 }}>
                          <Text fw={500} size="sm">Vacation Period {index + 1}</Text>
                        </div>
                        <Button
                          variant="subtle"
                          color="red"
                          size="xs"
                          onClick={() => {
                            const updatedPeriods = [...form.values.workDaysSettings.vacationPeriods];
                            updatedPeriods.splice(index, 1);
                            form.setFieldValue('workDaysSettings.vacationPeriods', updatedPeriods);
                          }}
                        >
                          Remove
                        </Button>
                      </Group>
                      <Group grow mb="xs">
                        <DatePickerInput
                          label="Start Date"
                          placeholder="Select date"
                          value={period.startDate}
                          onChange={(date) => {
                            const updatedPeriods = [...form.values.workDaysSettings.vacationPeriods];
                            updatedPeriods[index].startDate = date as unknown as Date;
                            form.setFieldValue('workDaysSettings.vacationPeriods', updatedPeriods);
                          }}
                        />
                        <DatePickerInput
                          label="End Date"
                          placeholder="Select date"
                          value={period.endDate}
                          onChange={(date) => {
                            const updatedPeriods = [...form.values.workDaysSettings.vacationPeriods];
                            updatedPeriods[index].endDate = date as unknown as Date;
                            form.setFieldValue('workDaysSettings.vacationPeriods', updatedPeriods);
                          }}
                          minDate={period.startDate || undefined}
                        />
                      </Group>
                      <Select
                        label="Description"
                        placeholder="Select type"
                        data={[
                          { value: 'summer', label: 'Summer Vacation' },
                          { value: 'winter', label: 'Winter Vacation' },
                          { value: 'personal', label: 'Personal Time' },
                          { value: 'other', label: 'Other' },
                        ]}
                        value={period.description}
                        onChange={(value) => {
                          const updatedPeriods = [...form.values.workDaysSettings.vacationPeriods];
                          updatedPeriods[index].description = value || '';
                          form.setFieldValue('workDaysSettings.vacationPeriods', updatedPeriods);
                        }}
                      />
                    </Card>
                  ))}

                  <Button
                    variant="outline"
                    leftSection={<IconCalendarTime size={16} />}
                    onClick={() => {
                      const updatedPeriods = [...form.values.workDaysSettings.vacationPeriods];
                      updatedPeriods.push({
                        startDate: null,
                        endDate: null,
                        description: '',
                      });
                      form.setFieldValue('workDaysSettings.vacationPeriods', updatedPeriods);
                    }}
                    mb="md"
                  >
                    Add Vacation Period
                  </Button>

                  <Divider my="md" />

                  <Title order={4}>Holidays & Emergency Days</Title>
                  <Text size="sm" c="dimmed" mb="md">
                    Specify exact dates for holidays and emergency days
                  </Text>

                  <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="md">
                    <div>
                      <Title order={5} mb="sm">Holidays</Title>
                      {form.values.workDaysSettings.holidayDates.map((holiday, index) => (
                        <Card key={index} withBorder p="sm" mb="sm">
                          <Group mb="xs">
                            <div style={{ flex: 1 }}>
                              <Text fw={500} size="sm">Holiday {index + 1}</Text>
                            </div>
                            <Button
                              variant="subtle"
                              color="red"
                              size="xs"
                              onClick={() => {
                                const updatedHolidays = [...form.values.workDaysSettings.holidayDates];
                                updatedHolidays.splice(index, 1);
                                form.setFieldValue('workDaysSettings.holidayDates', updatedHolidays);
                              }}
                            >
                              Remove
                            </Button>
                          </Group>
                          <DatePickerInput
                            label="Date"
                            placeholder="Select date"
                            value={holiday.date}
                            onChange={(date) => {
                              const updatedHolidays = [...form.values.workDaysSettings.holidayDates];
                              updatedHolidays[index].date = date as unknown as Date;
                              form.setFieldValue('workDaysSettings.holidayDates', updatedHolidays);
                            }}
                            mb="xs"
                          />
                          <Select
                            label="Description"
                            placeholder="Select holiday"
                            data={[
                              { value: 'national', label: 'National Holiday' },
                              { value: 'religious', label: 'Religious Holiday' },
                              { value: 'local', label: 'Local Holiday' },
                              { value: 'other', label: 'Other' },
                            ]}
                            value={holiday.description}
                            onChange={(value) => {
                              const updatedHolidays = [...form.values.workDaysSettings.holidayDates];
                              updatedHolidays[index].description = value || '';
                              form.setFieldValue('workDaysSettings.holidayDates', updatedHolidays);
                            }}
                          />
                        </Card>
                      ))}
                      <Button
                        variant="outline"
                        leftSection={<IconCalendarTime size={16} />}
                        onClick={() => {
                          const updatedHolidays = [...form.values.workDaysSettings.holidayDates];
                          updatedHolidays.push({
                            date: null,
                            description: '',
                          });
                          form.setFieldValue('workDaysSettings.holidayDates', updatedHolidays);
                        }}
                        mb="md"
                        fullWidth
                      >
                        Add Holiday
                      </Button>
                    </div>

                    <div>
                      <Title order={5} mb="sm">Emergency Days</Title>
                      {form.values.workDaysSettings.emergencyDates.map((emergency, index) => (
                        <Card key={index} withBorder p="sm" mb="sm">
                          <Group mb="xs">
                            <div style={{ flex: 1 }}>
                              <Text fw={500} size="sm">Emergency Day {index + 1}</Text>
                            </div>
                            <Button
                              variant="subtle"
                              color="red"
                              size="xs"
                              onClick={() => {
                                const updatedEmergencies = [...form.values.workDaysSettings.emergencyDates];
                                updatedEmergencies.splice(index, 1);
                                form.setFieldValue('workDaysSettings.emergencyDates', updatedEmergencies);
                              }}
                            >
                              Remove
                            </Button>
                          </Group>
                          <DatePickerInput
                            label="Date"
                            placeholder="Select date"
                            value={emergency.date}
                            onChange={(date) => {
                              const updatedEmergencies = [...form.values.workDaysSettings.emergencyDates];
                              updatedEmergencies[index].date = date as unknown as Date;
                              form.setFieldValue('workDaysSettings.emergencyDates', updatedEmergencies);
                            }}
                            mb="xs"
                          />
                          <Select
                            label="Reason"
                            placeholder="Select reason"
                            data={[
                              { value: 'medical', label: 'Medical Emergency' },
                              { value: 'family', label: 'Family Emergency' },
                              { value: 'personal', label: 'Personal Emergency' },
                              { value: 'other', label: 'Other' },
                            ]}
                            value={emergency.reason}
                            onChange={(value) => {
                              const updatedEmergencies = [...form.values.workDaysSettings.emergencyDates];
                              updatedEmergencies[index].reason = value || '';
                              form.setFieldValue('workDaysSettings.emergencyDates', updatedEmergencies);
                            }}
                          />
                        </Card>
                      ))}
                      <Button
                        variant="outline"
                        leftSection={<IconCalendarTime size={16} />}
                        onClick={() => {
                          const updatedEmergencies = [...form.values.workDaysSettings.emergencyDates];
                          updatedEmergencies.push({
                            date: null,
                            reason: '',
                          });
                          form.setFieldValue('workDaysSettings.emergencyDates', updatedEmergencies);
                        }}
                        mb="md"
                        fullWidth
                      >
                        Add Emergency Day
                      </Button>
                    </div>
                  </SimpleGrid>

                  <Alert color="blue" icon={<IconAlertCircle size={16} />} mt="md">
                    These settings will be used to configure your availability in the appointment system.
                    Patients will not be able to book appointments outside your working days or during your vacation time.
                  </Alert>

                  <Button
                    fullWidth
                    mt="md"
                    color="teal"
                    leftSection={<IconDeviceFloppy size={16} />}
                    onClick={async () => {
                      try {
                        // Update the appointment system with the current form values and show notification
                        const success = await settingsService.updateAppointmentSystem(form.values as UserSettings, true);

                        if (!success) {
                          throw new Error('Failed to update appointment system');
                        }

                        // Save the settings to ensure they're persisted
                        await settingsService.saveUserSettings(form.values as UserSettings);

                        // Update the local settings state
                        setSettings(form.values as UserSettings);
                      } catch (error) {
                        console.error('Error applying settings to appointment system:', error);
                        notifications.show({
                          title: 'Error',
                          message: 'Failed to apply settings to appointment system. Please try again later.',
                          color: 'red',
                          icon: <IconAlertCircle size={16} />,
                        });
                      }
                    }}
                  >
                    Apply to Appointment System
                  </Button>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="orange">
                    <IconBell size={20} />
                  </ThemeIcon>
                  <Title order={3}>Notifications</Title>
                </Group>

                <Stack>
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Email Notifications</Text>
                      <Text size="sm" c="dimmed">
                        Receive notifications via email
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.emailNotifications}
                      onChange={(event) => form.setFieldValue('emailNotifications', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>SMS Notifications</Text>
                      <Text size="sm" c="dimmed">
                        Receive notifications via SMS
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.smsNotifications}
                      onChange={(event) => form.setFieldValue('smsNotifications', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Appointment Reminders</Text>
                      <Text size="sm" c="dimmed">
                        Receive reminders about upcoming appointments
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.appointmentReminders}
                      onChange={(event) => form.setFieldValue('appointmentReminders', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Marketing Emails</Text>
                      <Text size="sm" c="dimmed">
                        Receive marketing and promotional emails
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.marketingEmails}
                      onChange={(event) => form.setFieldValue('marketingEmails', event.currentTarget.checked)}
                    />
                  </Group>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="red">
                    <IconShield size={20} />
                  </ThemeIcon>
                  <Title order={3}>Privacy</Title>
                </Group>

                <Stack>
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Show Profile to Other Doctors</Text>
                      <Text size="sm" c="dimmed">
                        Allow other doctors to view your profile
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.privacySettings.showProfileToOtherDoctors}
                      onChange={(event) => form.setFieldValue('privacySettings.showProfileToOtherDoctors', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Share Anonymized Data for Research</Text>
                      <Text size="sm" c="dimmed">
                        Allow anonymized data to be used for research purposes
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.privacySettings.shareAnonymizedDataForResearch}
                      onChange={(event) => form.setFieldValue('privacySettings.shareAnonymizedDataForResearch', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Allow Patient Feedback</Text>
                      <Text size="sm" c="dimmed">
                        Allow patients to leave feedback and ratings
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.privacySettings.allowPatientFeedback}
                      onChange={(event) => form.setFieldValue('privacySettings.allowPatientFeedback', event.currentTarget.checked)}
                    />
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          <Group justify="flex-end" mt="xl">
            <Button variant="outline" onClick={() => form.reset()}>
              Reset
            </Button>
            <Button type="submit" loading={submitting} leftSection={<IconDeviceFloppy size={16} />}>
              Save Settings
            </Button>
          </Group>
        </form>
      </Paper>
    </Paper>
  );
}

