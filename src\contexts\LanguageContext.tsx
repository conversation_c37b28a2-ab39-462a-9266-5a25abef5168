"use client";
import { createContext, useContext, useState, useEffect, type ReactNode } from "react";
import { Locales, FALLBACK_LOCALE, supportedLocales } from "~/i18n/settings";
import settingsService from "~/services/settingsService";
import { applyLanguageSettings } from "~/utils/fontSizeUtils";

interface LanguageContextType {
  language: Locales;
  setLanguage: (lang: Locales) => void;
  saveLanguage: (lang: Locales) => Promise<void>;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Locales>(FALLBACK_LOCALE);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load language from settings on mount
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        setIsLoading(true);
        const settings = await settingsService.getUserSettings();
        
        if (settings && settings.language && supportedLocales.includes(settings.language as Locales)) {
          const userLanguage = settings.language as Locales;
          setLanguage(userLanguage);
          
          // Apply language to i18next and cookies
          await applyLanguageSettings(userLanguage);
        } else {
          // Use fallback language
          setLanguage(FALLBACK_LOCALE);
          await applyLanguageSettings(FALLBACK_LOCALE);
        }
      } catch (error) {
        console.error("Error loading language from settings:", error);
        setLanguage(FALLBACK_LOCALE);
        await applyLanguageSettings(FALLBACK_LOCALE);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguage();
  }, []);



  // Function to save language to backend
  const saveLanguage = async (lang: Locales) => {
    try {
      setIsLoading(true);
      
      // Get current settings
      const settings = await settingsService.getUserSettings();
      
      // Update language
      settings.language = lang;
      
      // Save settings
      const success = await settingsService.saveUserSettings(settings);
      
      if (success) {
        // Apply language settings
        await applyLanguageSettings(lang);
        setLanguage(lang);
        console.log("Language saved to settings:", lang);
      } else {
        throw new Error("Failed to save language settings");
      }
    } catch (error) {
      console.error("Error saving language to settings:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update language immediately when changed (for UI responsiveness)
  const handleSetLanguage = (lang: Locales) => {
    setLanguage(lang);
    applyLanguageSettings(lang);
  };

  return (
    <LanguageContext.Provider 
      value={{ 
        language, 
        setLanguage: handleSetLanguage, 
        saveLanguage, 
        isLoading 
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
