
import { useState } from 'react';
import { MultiSelect, Box, Text, TextInput, Button, Group, ActionIcon } from '@mantine/core';
import { IconAlertCircle, IconPlus } from '@tabler/icons-react';

interface AlertsDropdownProps {
  value: string[];
  onChange: (value: string[]) => void;
  label?: string;
  required?: boolean;
}

export function AlertsDropdown({ 
  value, 
  onChange, 
  label = "Alertes", 
  required = false 
}: AlertsDropdownProps) {
  const [newAlert, setNewAlert] = useState('');
  
  // Initial alerts data
  const [alertsData, setAlertsData] = useState([
    { value: 'allaitante', label: 'Allaitante depuis:' },
    { value: 'aspirine', label: 'Allergique à l\'Aspirine' },
    { value: 'penicilline', label: 'Allergique à la Pénicilline' },
    { value: 'arthrose', label: 'Arthrose' },
    { value: 'anticoagulant', label: 'Cardiaque Anticoagulant' },
    { value: 'prothese', label: 'Cardiaque prothèse valvulaire' },
    { value: 'trouble-rythme', label: 'Cardiaque trouble du rythme' },
    { value: 'diabetique-id', label: 'Diabétique ID' },
    { value: 'diabetique-nid', label: 'Diabétique NID' },
    { value: 'enceinte', label: 'Enceinte depuis:' },
    { value: 'gastralgie', label: 'Gastralgie : ulcère anti-inflamm...' },
    { value: 'hypertension', label: 'Hypertension' },
    { value: 'hypotension', label: 'Hypotension' },
    { value: 'thyroide', label: 'Thyroïde' },
  ]);

  const handleAddAlert = () => {
    if (newAlert.trim() === '') return;
    
    // Create a new alert item
    const newAlertItem = {
      value: newAlert.toLowerCase().replace(/\s+/g, '-'),
      label: newAlert
    };
    
    // Add to alerts data
    setAlertsData([...alertsData, newAlertItem]);
    
    // Optionally select the new item
    onChange([...value, newAlertItem.value]);
    
    // Clear input
    setNewAlert('');
  };

  return (
    <div>
      <MultiSelect
        data={alertsData}
        value={value}
        onChange={onChange}
        label={label}
        placeholder="Sélectionner des alertes médicales"
        searchable
        clearable
        required={required}
        leftSection={<IconAlertCircle size={16} />}
        w={"100%"}
      />
      
      {/* <Group mt={8} align="flex-end">
        <TextInput
          placeholder="Ajouter une nouvelle alerte"
          value={newAlert}
          onChange={(e) => setNewAlert(e.target.value)}
          style={{ flexGrow: 1 }}
        />
        <ActionIcon 
          color="#3799CE" 
          variant="filled" 
          onClick={handleAddAlert}
          disabled={newAlert.trim() === ''}
        >
          <IconPlus size={16} />
        </ActionIcon>
      </Group> */}
    </div>
  );
}
// import { useState } from 'react';
// import { MultiSelect, Box, Text } from '@mantine/core';
// import { IconAlertCircle } from '@tabler/icons-react';
// import type { MultiSelectStylesNames } from '@mantine/core';

// interface AlertsDropdownProps {
//   value: string[];
//   onChange: (value: string[]) => void;
//   label?: string;
//   required?: boolean;
// }

// export function AlertsDropdown({ 
//   value, 
//   onChange, 
//   label = "Alertes", 
//   required = false 
// }: AlertsDropdownProps) {
//   const alertsData = [
//     { value: 'allaitante', label: 'Allaitante depuis:' },
//     { value: 'aspirine', label: 'Allergique à l\'Aspirine' },
//     { value: 'penicilline', label: 'Allergique à la Pénicilline' },
//     { value: 'arthrose', label: 'Arthrose' },
//     { value: 'anticoagulant', label: 'Cardiaque Anticoagulant' },
//     { value: 'prothese', label: 'Cardiaque prothèse valvulaire' },
//     { value: 'trouble-rythme', label: 'Cardiaque trouble du rythme' },
//     { value: 'diabetique-id', label: 'Diabétique ID' },
//     { value: 'diabetique-nid', label: 'Diabétique NID' },
//     { value: 'enceinte', label: 'Enceinte depuis:' },
//     { value: 'gastralgie', label: 'Gastralgie : ulcère anti-inflamm...' },
//     { value: 'hypertension', label: 'Hypertension' },
//     { value: 'hypotension', label: 'Hypotension' },
//     { value: 'thyroide', label: 'Thyroïde' },
//   ];

//   return (
//     <MultiSelect
//     data={alertsData}
//     value={value}
//     onChange={onChange}
//     label={label}
//     placeholder="Sélectionner des alertes médicales"
//     searchable
//     clearable
//     required={required}
//     leftSection={<IconAlertCircle size={16} />}
    
//     classNames={{
//       option: "custom-multiselect-option"
//     }}
//   />
//   );
// }