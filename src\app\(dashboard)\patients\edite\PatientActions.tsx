import { Button, Group, Tooltip,ActionIcon } from '@mantine/core';
import Icon from '@mdi/react';
import {
  mdiBarcode,
  mdiSkipPrevious,
  mdiSkipNext,
  mdiTooth,
  mdiCalendarPlus,
} from '@mdi/js';

type PatientActionsProps = {
  patientId?: string;
  isFormInvalid: boolean;
  isDraft: boolean;
  onPrint?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onStartVisit?: () => void;
  onAppointment?: () => void;
  onCancel?: () => void;
  onSaveQuitNew?: () => void;
  onSaveQuit?: () => void;
  onSubmit?: () => void;
};

export const PatientActions = ({
  patientId,
  isFormInvalid,
  isDraft,
  onPrint,
  onPrevious,
  onNext,
  onStartVisit,
  onAppointment,
  onCancel,
  onSaveQuitNew,
  onSaveQuit,
  onSubmit,
}: PatientActionsProps) => {
  const disabled = isFormInvalid || isDraft;

  return (
    <Group justify="space-between" wrap="wrap" mt="md">
      <Group gap="xs">
        {patientId && (
          <>
            <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings" radius="4px"
          onClick={onPrint}>
            <Icon path={mdiBarcode} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
        <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
          onClick={onPrevious}>
            <Icon path={mdiSkipPrevious} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
            {/* <Button variant="light" size="compact-md" onClick={onPrevious}>
              <Icon path={mdiSkipPrevious} size={1} />
            </Button> */}
  <Tooltip label="Imprimer le code-barres"> 
                <ActionIcon variant="filled" aria-label="Settings"radius="4px"
          onClick={onNext}>
            <Icon path={mdiSkipNext} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
            </Tooltip>
            {/* <Button variant="light" size="compact-md" onClick={onNext}>
              <Icon path={mdiSkipPrevious} size={1} />
            </Button> */}
          </>
        )}
      </Group>

      <Group gap="xs">
        <Tooltip label="Commencer la visite">
          <ActionIcon variant="filled" aria-label="Settings" radius="4px"
          onClick={onStartVisit}
            disabled={disabled}>
          <Icon path={mdiTooth} size={0.75}  style={{ width: '70%', height: '70%' }}  />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Ajouter un rendez-vous">
          <ActionIcon variant="filled" aria-label="Settings"radius="4px"
          onClick={onAppointment}
            disabled={isFormInvalid}>
            <Icon path={mdiCalendarPlus} size={0.75}  style={{ width: '70%', height: '70%' }}  />
        </ActionIcon>
        </Tooltip>
        <Button variant="outline" color="red" onClick={onCancel}>
          Annuler
        </Button>

        {patientId && (
          <Button
            variant="filled"
            color="blue"
            onClick={onSaveQuitNew}
            disabled={isFormInvalid}
          >
            Enregistrer & Nouvelle fiche
          </Button>
        )}

        <Button
          variant="filled"
          color="blue"
          onClick={onSaveQuit}
          disabled={isFormInvalid}
        >
          Enregistrer et quitter
        </Button>

        <Button
          variant="filled"
          color="blue"
          type="submit"
          onClick={onSubmit}
          disabled={isFormInvalid}
        >
          Enregistrer la fiche
        </Button>
      </Group>
    </Group>
  );
};
