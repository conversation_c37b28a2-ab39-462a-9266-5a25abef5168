import api from '../lib/api';
import {
  DentistryAppointment,
  DentistryPatient,
  DentistryDoctor,
  DentistryAppointmentQueryParams,
  DentistryPatientQueryParams,
  DentistryDoctorQueryParams,
  DentistryApiResponse,
  CreateDentistryAppointmentData,
  UpdateDentistryAppointmentData,
  LegacyAppointment,
  EventType
} from '../types/dentistry';

/**
 * Service for interacting with the Dentistry API
 * Provides methods for managing appointments, patients, and doctors
 */
class DentistryService {
  private baseUrl = '/api';

  // Appointment methods
  async getAppointments(params?: DentistryAppointmentQueryParams): Promise<DentistryAppointment[]> {
    try {
      const response = await api.get<DentistryApiResponse<DentistryAppointment>>(
        `${this.baseUrl}/appointments/`,
        { params }
      );

      // Handle different response formats
      if (response.data && Array.isArray(response.data)) {
        // Direct array response
        return response.data;
      } else if (response.data && response.data.results && Array.isArray(response.data.results)) {
        // Paginated response
        return response.data.results;
      } else {
        console.warn('Unexpected response format:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching dentistry appointments:', error);
      throw error;
    }
  }

  async getAppointment(id: string): Promise<DentistryAppointment> {
    try {
      const response = await api.get<DentistryAppointment>(
        `${this.baseUrl}/appointments/${id}/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching appointment ${id}:`, error);
      throw error;
    }
  }

  async createAppointment(data: CreateDentistryAppointmentData): Promise<DentistryAppointment> {
    try {
      const response = await api.post<DentistryAppointment>(
        `${this.baseUrl}/appointments/`,
        data
      );
      return response.data;
    } catch (error) {
      console.error('Error creating appointment:', error);
      throw error;
    }
  }

  async updateAppointment(id: string, data: UpdateDentistryAppointmentData): Promise<DentistryAppointment> {
    try {
      const response = await api.patch<DentistryAppointment>(
        `${this.baseUrl}/appointments/${id}/`,
        data
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating appointment ${id}:`, error);
      throw error;
    }
  }

  async deleteAppointment(id: string): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/appointments/${id}/`);
    } catch (error) {
      console.error(`Error deleting appointment ${id}:`, error);
      throw error;
    }
  }

  // Convenience methods for appointment status changes
  async confirmAppointment(id: string): Promise<DentistryAppointment> {
    return this.updateAppointment(id, { status: 'confirmed' });
  }

  async cancelAppointment(id: string): Promise<DentistryAppointment> {
    return this.updateAppointment(id, { status: 'cancelled' });
  }

  async completeAppointment(id: string, notes?: string): Promise<DentistryAppointment> {
    return this.updateAppointment(id, { status: 'completed', notes });
  }

  async rescheduleAppointment(
    id: string, 
    newDate: string, 
    newTime: string
  ): Promise<DentistryAppointment> {
    return this.updateAppointment(id, {
      appointment_date: newDate,
      appointment_time: newTime,
      status: 'rescheduled'
    });
  }

  async markNoShow(id: string): Promise<DentistryAppointment> {
    return this.updateAppointment(id, { status: 'no_show' });
  }

  // Date-specific appointment methods
  async getTodayAppointments(): Promise<DentistryAppointment[]> {
    const today = new Date().toISOString().split('T')[0];
    return this.getAppointments({ appointment_date: today });
  }

  async getAppointmentsByDate(date: string): Promise<DentistryAppointment[]> {
    return this.getAppointments({ appointment_date: date });
  }

  async getAppointmentsByDateRange(
    startDate: string, 
    endDate: string
  ): Promise<DentistryAppointment[]> {
    return this.getAppointments({
      appointment_date_after: startDate,
      appointment_date_before: endDate
    });
  }

  // Patient-specific appointment methods
  async getPatientAppointments(patientId: string): Promise<DentistryAppointment[]> {
    return this.getAppointments({ patient: patientId });
  }

  // Doctor-specific appointment methods
  async getDoctorAppointments(doctorId: string, date?: string): Promise<DentistryAppointment[]> {
    const params: DentistryAppointmentQueryParams = { doctor: doctorId };
    if (date) {
      params.appointment_date = date;
    }
    return this.getAppointments(params);
  }

  // Patient methods
  async getPatients(params?: DentistryPatientQueryParams): Promise<DentistryPatient[]> {
    try {
      const response = await api.get<DentistryApiResponse<DentistryPatient>>(
        `${this.baseUrl}/patients/`,
        { params }
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching patients:', error);
      throw error;
    }
  }

  async getPatient(id: string): Promise<DentistryPatient> {
    try {
      const response = await api.get<DentistryPatient>(
        `${this.baseUrl}/patients/${id}/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching patient ${id}:`, error);
      throw error;
    }
  }

  // Doctor methods
  async getDoctors(params?: DentistryDoctorQueryParams): Promise<DentistryDoctor[]> {
    try {
      const response = await api.get<DentistryApiResponse<DentistryDoctor>>(
        `${this.baseUrl}/doctors/`,
        { params }
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching doctors:', error);
      throw error;
    }
  }

  async getDoctor(id: string): Promise<DentistryDoctor> {
    try {
      const response = await api.get<DentistryDoctor>(
        `${this.baseUrl}/doctors/${id}/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching doctor ${id}:`, error);
      throw error;
    }
  }

  // Utility methods for converting between backend and legacy frontend formats
  convertToLegacyAppointment(appointment: DentistryAppointment): LegacyAppointment {
    const patient = typeof appointment.patient === 'object' ? appointment.patient : null;
    const doctor = typeof appointment.doctor === 'object' ? appointment.doctor : null;

    const startDateTime = new Date(`${appointment.appointment_date}T${appointment.appointment_time}`);
    const endDateTime = new Date(startDateTime.getTime() + appointment.duration_minutes * 60000);

    // Map appointment types to EventType
    const mapToEventType = (appointmentType: string): EventType => {
      const typeMapping: Record<string, EventType> = {
        'check_up': 'visit',
        'cleaning': 'visit',
        'filling': 'visit',
        'root_canal': 'visit',
        'extraction': 'visit',
        'crown': 'visit',
        'bridge': 'visit',
        'implant': 'visit',
        'dentures': 'visit',
        'orthodontics': 'visit',
        'whitening': 'visit',
        'emergency': 'diagnosis',
        'consultation': 'visit',
        'other': 'visit'
      };
      return typeMapping[appointmentType] || 'visit';
    };

    const eventType = mapToEventType(appointment.appointment_type);

    return {
      id: appointment.id,
      title: patient ? `${patient.first_name} ${patient.last_name}` : 'Unknown Patient',
      start: startDateTime,
      end: endDateTime,
      patientId: typeof appointment.patient === 'string' ? appointment.patient : appointment.patient.id,
      isActive: false,
      resourceId: 1, // Default resource
      first_name: patient?.first_name || '',
      last_name: patient?.last_name || '',
      birth_date: patient?.date_of_birth || '',
      age: patient?.age || 0,
      agenda: '',
      etatCivil: '',
      etatAganda: '',
      cin: '',
      address: patient?.address || '',
      phone_numbers: patient?.phone_number || '',
      email: patient?.email || '',
      docteur: doctor ? `${doctor.first_name} ${doctor.last_name}` : '',
      event_Title: '',
      gender: patient?.gender === 'M' ? 'Homme' : patient?.gender === 'F' ? 'Femme' : 'Autre',
      sociale: '',
      typeConsultation: appointment.appointment_type,
      notes: appointment.notes || '',
      commentairelistedattente: '',
      socialSecurity: '',
      duration: appointment.duration_minutes.toString(),
      comment: appointment.reason || '',
      patientTitle: '',
      appointmentDate: appointment.appointment_date,
      appointmentTime: appointment.appointment_time,
      appointmentEndTime: endDateTime.toTimeString().slice(0, 5),
      consultationDuration: appointment.duration_minutes,
      type: eventType,
      eventType: eventType,
      visitorCount: 0,
      lunchTime: false,
      name: patient?.first_name || '',
      prenom: patient?.last_name || '',
      date: appointment.appointment_date
    };
  }

  // Helper function to validate appointment type
  private validateAppointmentType(type: string): DentistryAppointment['appointment_type'] {
    const validTypes: DentistryAppointment['appointment_type'][] = [
      'check_up', 'cleaning', 'filling', 'root_canal', 'extraction',
      'crown', 'bridge', 'implant', 'dentures', 'orthodontics',
      'whitening', 'emergency', 'consultation', 'other'
    ];

    // Map common legacy types to valid types
    const typeMapping: Record<string, DentistryAppointment['appointment_type']> = {
      'visit': 'consultation',
      'checkup': 'check_up',
      'exam': 'consultation',
      'treatment': 'other',
      'surgery': 'other',
      'follow_up': 'consultation'
    };

    // Check if type is already valid
    if (validTypes.includes(type as DentistryAppointment['appointment_type'])) {
      return type as DentistryAppointment['appointment_type'];
    }

    // Check mapping
    if (typeMapping[type]) {
      return typeMapping[type];
    }

    // Default fallback
    return 'consultation';
  }

  convertFromLegacyAppointment(legacy: LegacyAppointment): CreateDentistryAppointmentData {
    return {
      patient: legacy.patientId,
      doctor: '1', // Default doctor ID - should be set properly
      appointment_date: legacy.appointmentDate,
      appointment_time: legacy.appointmentTime,
      duration_minutes: legacy.consultationDuration,
      appointment_type: this.validateAppointmentType(legacy.typeConsultation || 'consultation'),
      reason: legacy.comment,
      notes: legacy.notes
    };
  }
}

// Export singleton instance
export const dentistryService = new DentistryService();
export default dentistryService;
