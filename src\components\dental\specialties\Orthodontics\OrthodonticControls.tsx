// frontend/dental_medicine/src/components/content/dental/specialties/Orthodontics/OrthodonticControls.tsx

import React from 'react';
import { <PERSON><PERSON>, Tooltip, <PERSON>u, rem ,Group} from '@mantine/core';
import { IconSquareRoundedPlusFilled, IconFileZip, IconEye, IconTrash } from '@tabler/icons-react';
import { DentalControlsProps } from '../../shared/types';

// Interface pour les contrôles orthodontiques (hérite de DentalControlsProps)
type OrthodonticControlsProps = DentalControlsProps;

export const OrthodonticControls: React.FC<OrthodonticControlsProps> = ({
  activeButton,
  onButtonClick,
  onTargetPathChange
}) => {

  const Controls = [
    {
      id: 'TeethCrown',
      label: 'TeethCrown',
      pathId: '69',
      tooltip: 'TeethCrown',
      shortCode: 'Tc',
      icon: (
       <svg xmlns="http://www.w3.org/2000/svg" data-name="11 Tooth extraction" viewBox="-25.5 -51.0 561.0 688.5" x="0px" y="0px">
       <path style={{ fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}d="M76.2,108c0,38.9,12.5,86.6,28.1,130.5c4.4,12.5,18.1,18.8,30.5,14.1c77.4-29,168.6-28.4,245.7,2.1  c12.9,5.1,26.9-1.7,31.1-14.6c16.2-50.3,34.1-130.9,11.3-177.5c-12.3-25.1-34.3-35.1-62.4-35.1c-15,0-29,8-44.8,17  c-15.3,8.8-32.2,18.4-53.9,23.5c-0.5,0.1-1,0.2-1.6,0.3c-33.1,2.6-57.8-10.8-79.4-22.6c-14.1-7.7-26.7-14.5-38.4-14.5  C93.6,31.2,76.2,61.4,76.2,108L76.2,108z M319.3"/>

       </svg>
      )
    },
    {
      id: 'ImplantTeethCrown',
      label: 'ImplantTeethCrown',
      pathId: '',
      tooltip: 'ImplantTeethCrown',
      shortCode: 'It',
      icon: (
       <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="-25.5 -51.0 561.0 688.5" >
       <path style={{ fillRule:"evenodd",clipRule:"evenodd",fill: "#f5f5f5" , stroke:"#5A5A5A" ,strokeWidth:0.25,strokeMiterlimit:10}}
       d="M76.2,108c0,38.9,12.5,86.6,28.1,130.5c4.4,12.5,18.1,18.8,30.5,14.1c77.4-29,168.6-28.4,245.7,2.1
       c12.9,5.1,26.9-1.7,31.1-14.6c16.2-50.3,34.1-130.9,11.3-177.5c-12.3-25.1-34.3-35.1-62.4-35.1c-15,0-29,8-44.8,17
       c-15.3,8.8-32.2,18.4-53.9,23.5c-0.5,0.1-1,0.2-1.6,0.3c-33.1,2.6-57.8-10.8-79.4-22.6c-14.1-7.7-26.7-14.5-38.4-14.5
       C93.6,31.2,76.2,61.4,76.2,108L76.2,108z M319.3,253.1c-42.2-8.2-87-8.1-129.2,0.1c-3.2,34.5-10.8,43.1-17.2,50.4
       c-8.5,9.7-14.3,16.3,7.3,145.1c2.9,17.3,7.6,28.2,12.7,32.5c2.8,2.4,4.6,1.2,7.6-3.2c7.3-10.7,12.7-34.4,12.1-72.2
       c-1.2-86.9,86.5-86.9,85.3,0c-0.5,37.7,4.9,61.4,12.2,72.2c3,4.4,4.8,5.6,7.6,3.2c5.1-4.3,9.8-15.2,12.7-32.5
       c21.2-126.8,15.1-134,6.4-144.3C330.2,296.7,322.5,287.7,319.3,253.1L319.3,253.1z"/>

       </svg>
      )
    },
    {
      id: 'braces',
      label: 'Brackets',
      pathId: '66',
      tooltip: 'Appareils orthodontiques fixes',
      shortCode: 'Br',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <rect x="4" y="8" width="16" height="8" rx="2" fill="#f5f5f5" stroke="#6f42c1" strokeWidth="0.5"/>
          <rect x="6" y="10" width="2" height="4" fill="#6f42c1" opacity="0.8"/>
          <rect x="9" y="10" width="2" height="4" fill="#6f42c1" opacity="0.8"/>
          <rect x="12" y="10" width="2" height="4" fill="#6f42c1" opacity="0.8"/>
          <rect x="15" y="10" width="2" height="4" fill="#6f42c1" opacity="0.8"/>
          <line x1="6" y1="12" x2="17" y2="12" stroke="#6f42c1" strokeWidth="2"/>
          <circle cx="7" cy="12" r="1" fill="#ffffff"/>
          <circle cx="10" cy="12" r="1" fill="#ffffff"/>
          <circle cx="13" cy="12" r="1" fill="#ffffff"/>
          <circle cx="16" cy="12" r="1" fill="#ffffff"/>
        </svg>
      )
    },
    {
      id: 'aligners',
      label: 'Gouttières',
      pathId: '67',
      tooltip: 'Gouttières invisibles (Invisalign)',
      shortCode: 'Go',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M4 10C4 8 6 6 12 6C18 6 20 8 20 10V14C20 16 18 18 12 18C6 18 4 16 4 14V10Z"
            fill="#f5f5f5"
            stroke="#6f42c1"
            strokeWidth="0.5"
            opacity="0.7"
          />
          <path
            d="M6 12C6 11 8 10 12 10C16 10 18 11 18 12V13C18 14 16 15 12 15C8 15 6 14 6 13V12Z"
            fill="#6f42c1"
            opacity="0.3"
          />
          <circle cx="8" cy="12" r="0.5" fill="#6f42c1"/>
          <circle cx="10" cy="12" r="0.5" fill="#6f42c1"/>
          <circle cx="12" cy="12" r="0.5" fill="#6f42c1"/>
          <circle cx="14" cy="12" r="0.5" fill="#6f42c1"/>
          <circle cx="16" cy="12" r="0.5" fill="#6f42c1"/>
        </svg>
      )
    },
    {
      id: 'retainer',
      label: 'Contention',
      pathId: '70',
      tooltip: 'Appareil de contention',
      shortCode: 'Co',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M4 14C4 12 6 10 12 10C18 10 20 12 20 14V16C20 18 18 20 12 20C6 20 4 18 4 16V14Z"
            fill="#f5f5f5"
            stroke="#6f42c1"
            strokeWidth="0.5"
          />
          <line x1="6" y1="14" x2="18" y2="14" stroke="#6f42c1" strokeWidth="2"/>
          <circle cx="8" cy="14" r="1" fill="#6f42c1"/>
          <circle cx="12" cy="14" r="1" fill="#6f42c1"/>
          <circle cx="16" cy="14" r="1" fill="#6f42c1"/>
          <path d="M6 16C6 17 8 18 12 18C16 18 18 17 18 16" stroke="#6f42c1" strokeWidth="1" fill="none"/>
        </svg>
      )
    },
    {
      id: 'expander',
      label: 'Expanseur',
      pathId: '72',
      tooltip: 'Expanseur palatin',
      shortCode: 'Ex',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <rect x="8" y="8" width="8" height="8" rx="2" fill="#f5f5f5" stroke="#6f42c1" strokeWidth="0.5"/>
          <line x1="12" y1="6" x2="12" y2="18" stroke="#6f42c1" strokeWidth="2"/>
          <line x1="6" y1="12" x2="18" y2="12" stroke="#6f42c1" strokeWidth="2"/>
          <path d="M8 10L10 12L8 14" stroke="#6f42c1" strokeWidth="1.5" fill="none"/>
          <path d="M16 10L14 12L16 14" stroke="#6f42c1" strokeWidth="1.5" fill="none"/>
          <circle cx="12" cy="12" r="2" fill="#6f42c1" opacity="0.3"/>
          <rect x="11" y="11" width="2" height="2" fill="#ffffff"/>
        </svg>
      )
    },
    {
      id: 'wire_adjustment',
      label: 'Ajustement',
      pathId: '74',
      tooltip: 'Ajustement des fils orthodontiques',
      shortCode: 'Aj',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            d="M4 12C4 12 8 8 12 12C16 16 20 12 20 12"
            stroke="#6f42c1"
            strokeWidth="2"
            fill="none"
          />
          <circle cx="6" cy="10" r="1" fill="#6f42c1"/>
          <circle cx="9" cy="13" r="1" fill="#6f42c1"/>
          <circle cx="12" cy="12" r="1" fill="#6f42c1"/>
          <circle cx="15" cy="11" r="1" fill="#6f42c1"/>
          <circle cx="18" cy="12" r="1" fill="#6f42c1"/>
          <path d="M10 6L12 8L14 6" stroke="#6f42c1" strokeWidth="1" fill="none"/>
          <path d="M10 18L12 16L14 18" stroke="#6f42c1" strokeWidth="1" fill="none"/>
        </svg>
      )
    },
    {
      id: 'elastic',
      label: 'Élastiques',
      pathId: '76',
      tooltip: 'Élastiques orthodontiques',
      shortCode: 'El',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <ellipse cx="12" cy="12" rx="6" ry="4" fill="#f5f5f5" stroke="#6f42c1" strokeWidth="0.5"/>
          <ellipse cx="12" cy="12" rx="4" ry="2" fill="#6f42c1" opacity="0.3"/>
          <circle cx="8" cy="10" r="1" fill="#6f42c1"/>
          <circle cx="16" cy="10" r="1" fill="#6f42c1"/>
          <circle cx="8" cy="14" r="1" fill="#6f42c1"/>
          <circle cx="16" cy="14" r="1" fill="#6f42c1"/>
          <path d="M8 10Q12 8 16 10" stroke="#6f42c1" strokeWidth="1.5" fill="none"/>
          <path d="M8 14Q12 16 16 14" stroke="#6f42c1" strokeWidth="1.5" fill="none"/>
        </svg>
      )
    }
  ];

  return (
      <Group justify="space-between" mb={4} gap="xl"  w={"70%"}>
          {/* Menu "Tous les Traitements" */}
          <Menu withinPortal position="bottom-end" shadow="sm">
                <Menu.Target>
                  <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                    Tous les Traitements
                  </Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item leftSection={<IconFileZip style={{ width: rem(14), height: rem(14) }} />}>
                    Télécharger zip
                  </Menu.Item>
                  <Menu.Item leftSection={<IconEye style={{ width: rem(14), height: rem(14) }} />}>
                    Aperçu de tous
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                    color="red"
                  >
                    Supprimer tous
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
         <div style={{ margin: '0 auto' }} className="mb-2 flex flex-end p-2 sm:justify-start space-x-2">
        
          {Controls.map((control) => (
          <Tooltip
                key={control.id}
                label={control.tooltip}
                withArrow
                className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  >
            <Button
              styles={{
                root: {
                  position: 'relative',
                  color: 'white',
                  height: '35px', // Adjust button height
                  width: '35px',  // Adjust button width
                  padding: 0,
                  borderRadius: '0.5rem'
                },
              }}
            >
              {/* SVG in the middle */}
              <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
          }}
        >
            <span  className={
            activeButton === control.id
              ? " block  h-[35px] w-[35px] rounded-md bg-[#3799CE]  hover:bg-[#3799CE]"
              : " block  h-[35px] w-[35px] rounded-md bg-[#5A5A5A]  hover:bg-[#3799CE]"
          }
        onClick={() => {
                        onButtonClick(control.id);
                        onTargetPathChange(control.pathId);
                      }}
              >
    {control.icon}
                    </span>
              </div>
              {/* "CL" in the bottom-right corner */}
              <span
                style={{
                  position: 'absolute',
                  bottom: '0px',
                  right: '0px',
                  fontSize: '8px',
                  fontWeight: '800',
                  backgroundColor: 'white',
                  // borderRadius:'0.125rem' ,
                  borderTopLeftRadius: '0.5rem' ,
                  borderBottomRightRadius: '0.5rem' ,
                  color:'#3799CE',
                  padding:'3px  0px 1px 2px' ,
                }}
                className="h-[14px] w-[14px] "
              >
                     {control.shortCode}
              </span>
            </Button>
          </Tooltip>
           ))}
         </div>
        </Group>
  );
};

export default OrthodonticControls;
