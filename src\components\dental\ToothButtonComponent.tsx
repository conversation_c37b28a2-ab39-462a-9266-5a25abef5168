"use client";

import React, { useState } from 'react';
import {
  Card,
  Button,
  Text,
  Group,
  Stack,
  Badge,
  Collapse,
  ColorInput,
  Switch,
  Select,
  Divider,
  ActionIcon,
  Tooltip,
  NumberInput,
  Textarea,
  Tabs
} from '@mantine/core';
import {
  IconPlus,
  IconMinus,
  IconTooth,
  IconPalette,
  IconEye,
  IconEyeOff,
  IconSettings,
  IconStethoscope,
  IconScissors,
  IconDental,
  IconBone
} from '@tabler/icons-react';
import { type ToothInfo } from '@/data/ToothMapping';

// Types pour les spécialisations
interface Specialty {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  minAge: number;
}

// Spécialisations dentaires
const DENTAL_SPECIALTIES: Specialty[] = [
  {
    id: 'esthetic',
    name: 'Dentisterie Esthétique',
    icon: <IconPalette size={16} />,
    color: 'pink',
    minAge: 12
  },
  {
    id: 'therapeutic',
    name: 'Thérapeutique',
    icon: <IconStethoscope size={16} />,
    color: 'blue',
    minAge: 6
  },
  {
    id: 'prosthodontics',
    name: '<PERSON>th<PERSON><PERSON>',
    icon: <IconDental size={16} />,
    color: 'green',
    minAge: 13.5
  },
  {
    id: 'surgery',
    name: 'Chirurgie',
    icon: <IconScissors size={16} />,
    color: 'red',
    minAge: 12
  },
  {
    id: 'orthodontics',
    name: 'Orthodontie',
    icon: <IconBone size={16} />,
    color: 'purple',
    minAge: 7.5
  }
];

// Boutons de remplacement par spécialité
const REPLACEMENT_BUTTONS = {
  esthetic: [
    { id: 'veneer', name: 'Facette', color: '#FFE4E1' },
    { id: 'whitening', name: 'Blanchiment', color: '#F0F8FF' },
    { id: 'bonding', name: 'Collage', color: '#F5F5DC' }
  ],
  therapeutic: [
    { id: 'filling', name: 'Obturation', color: '#C0C0C0' },
    { id: 'root_canal', name: 'Endodontie', color: '#FFB6C1' },
    { id: 'cleaning', name: 'Détartrage', color: '#E0FFFF' }
  ],
  prosthodontics: [
    { id: 'crown', name: 'Couronne', color: '#FFD700' },
    { id: 'bridge', name: 'Bridge', color: '#DDA0DD' },
    { id: 'implant', name: 'Implant', color: '#98FB98' }
  ],
  surgery: [
    { id: 'extraction', name: 'Extraction', color: '#FF6347' },
    { id: 'gum_surgery', name: 'Chirurgie gingivale', color: '#FF69B4' },
    { id: 'bone_graft', name: 'Greffe osseuse', color: '#F0E68C' }
  ],
  orthodontics: [
    { id: 'braces', name: 'Appareil dentaire', color: '#87CEEB' },
    { id: 'retainer', name: 'Contention', color: '#DEB887' },
    { id: 'aligner', name: 'Gouttière', color: '#E6E6FA' }
  ]
};

interface AgeRestriction {
  value: number;
  label: string;
  description: string;
  restrictedTeeth: number[];
}

interface ToothButtonComponentProps {
  tooth: ToothInfo;
  isExpanded: boolean;
  onExpand: () => void;
  ageRestriction: AgeRestriction;
  onSelect?: (toothNumber: number, isSelected: boolean) => void;
  onSpecialtyChange?: (toothNumber: number, specialty: string, data: any) => void;
}

const ToothButtonComponent: React.FC<ToothButtonComponentProps> = ({
  tooth,
  isExpanded,
  onExpand,
  ageRestriction,
  onSelect,
  onSpecialtyChange
}) => {
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('');
  const [toothColor, setToothColor] = useState<string>('#FFFFFF');
  const [isHidden, setIsHidden] = useState<boolean>(false);
  const [isSelected, setIsSelected] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>('');
  const [priority, setPriority] = useState<number>(1);

  // Vérifier si la dent est autorisée pour cette tranche d'âge
  const isToothAllowed = !ageRestriction.restrictedTeeth.includes(tooth.tooth_number);

  // Obtenir les spécialisations disponibles pour cette tranche d'âge
  const getAvailableSpecialties = () => {
    return DENTAL_SPECIALTIES.filter(specialty => 
      ageRestriction.value >= specialty.minAge
    );
  };

  // Obtenir la couleur du quadrant
  const getQuadrantColor = () => {
    const colors = {
      upper_right: '#3B82F6',  // Bleu
      upper_left: '#10B981',   // Vert
      lower_left: '#F59E0B',   // Orange
      lower_right: '#EF4444'   // Rouge
    };
    return colors[tooth.quadrant] || '#6B7280';
  };

  // Gérer la sélection de la dent
  const handleToothSelect = () => {
    const newSelected = !isSelected;
    setIsSelected(newSelected);
    onSelect?.(tooth.tooth_number, newSelected);
  };

  // Gérer le changement de spécialité
  const handleSpecialtyChange = (specialtyId: string, data: any) => {
    setSelectedSpecialty(specialtyId);
    onSpecialtyChange?.(tooth.tooth_number, specialtyId, {
      ...data,
      color: toothColor,
      isHidden,
      notes,
      priority
    });
  };

  return (
    <Card
      withBorder
      p="sm"
      style={{
        opacity: isToothAllowed ? 1 : 0.5,
        backgroundColor: isSelected ? '#E3F2FD' : undefined,
        borderColor: isSelected ? '#2196F3' : undefined,
      }}
    >
      {/* En-tête du bouton de dent */}
      <Group justify="space-between" mb="xs">
        <Group gap="xs">
          <IconTooth size={16} color={getQuadrantColor()} />
          <Text fw={600} size="sm">
            {tooth.tooth_number}
          </Text>
          {tooth.is_permanent ? (
            <Badge size="xs" color="blue" variant="light">P</Badge>
          ) : (
            <Badge size="xs" color="orange" variant="light">T</Badge>
          )}
        </Group>

        <Group gap="xs">
          <ActionIcon
            variant="light"
            color="blue"
            size="sm"
            onClick={handleToothSelect}
            disabled={!isToothAllowed}
          >
            <IconPlus size={14} />
          </ActionIcon>
          
          <ActionIcon
            variant="light"
            color="gray"
            size="sm"
            onClick={onExpand}
            disabled={!isToothAllowed}
          >
            {isExpanded ? <IconMinus size={14} /> : <IconSettings size={14} />}
          </ActionIcon>
        </Group>
      </Group>

      {/* Nom de la dent */}
      <Text size="xs" c="dimmed" mb="sm" lineClamp={2}>
        {tooth.tooth_name}
      </Text>

      {/* Contenu étendu */}
      <Collapse in={isExpanded && isToothAllowed}>
        <Divider mb="md" />
        
        <Tabs defaultValue="specialty" variant="pills" size="xs">
          <Tabs.List mb="sm">
            <Tabs.Tab value="specialty">Spécialités</Tabs.Tab>
            <Tabs.Tab value="appearance">Apparence</Tabs.Tab>
            <Tabs.Tab value="settings">Paramètres</Tabs.Tab>
          </Tabs.List>

          {/* Onglet Spécialités */}
          <Tabs.Panel value="specialty">
            <Stack gap="sm">
              <Text size="xs" fw={500}>Spécialisations disponibles:</Text>
              
              {getAvailableSpecialties().map((specialty) => (
                <Button
                  key={specialty.id}
                  variant={selectedSpecialty === specialty.id ? "filled" : "light"}
                  color={specialty.color}
                  size="xs"
                  leftSection={specialty.icon}
                  onClick={() => setSelectedSpecialty(specialty.id)}
                  fullWidth
                >
                  {specialty.name}
                </Button>
              ))}

              {/* Boutons de remplacement pour la spécialité sélectionnée */}
              {selectedSpecialty && REPLACEMENT_BUTTONS[selectedSpecialty as keyof typeof REPLACEMENT_BUTTONS] && (
                <Stack gap="xs" mt="sm">
                  <Text size="xs" fw={500}>Traitements:</Text>
                  {REPLACEMENT_BUTTONS[selectedSpecialty as keyof typeof REPLACEMENT_BUTTONS].map((button) => (
                    <Button
                      key={button.id}
                      variant="outline"
                      size="xs"
                      style={{ backgroundColor: button.color }}
                      onClick={() => handleSpecialtyChange(selectedSpecialty, { treatment: button.id })}
                      fullWidth
                    >
                      {button.name}
                    </Button>
                  ))}
                </Stack>
              )}
            </Stack>
          </Tabs.Panel>

          {/* Onglet Apparence */}
          <Tabs.Panel value="appearance">
            <Stack gap="sm">
              <ColorInput
                label="Couleur de la dent"
                value={toothColor}
                onChange={setToothColor}
                size="xs"
              />
              
              <Group justify="space-between">
                <Text size="xs">Masquer la dent</Text>
                <Switch
                  checked={isHidden}
                  onChange={(event) => setIsHidden(event.currentTarget.checked)}
                  size="sm"
                />
              </Group>
            </Stack>
          </Tabs.Panel>

          {/* Onglet Paramètres */}
          <Tabs.Panel value="settings">
            <Stack gap="sm">
              <NumberInput
                label="Priorité"
                value={priority}
                onChange={(value) => setPriority(Number(value) || 1)}
                min={1}
                max={5}
                size="xs"
              />
              
              <Textarea
                label="Notes"
                value={notes}
                onChange={(event) => setNotes(event.currentTarget.value)}
                placeholder="Notes sur cette dent..."
                rows={2}
                size="xs"
              />
            </Stack>
          </Tabs.Panel>
        </Tabs>
      </Collapse>

      {/* Indicateur de restriction d'âge */}
      {!isToothAllowed && (
        <Badge size="xs" color="red" variant="light" mt="xs" fullWidth>
          Non disponible pour {ageRestriction.label}
        </Badge>
      )}
    </Card>
  );
};

export default ToothButtonComponent;
