// frontend/dental_medicine/src/components/content/dental/specialties/EstheticDentistry/EstheticDentistryTab.tsx

import React, { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { Tabs, rem, Button, Group, Text, Dialog, Menu, Container, Flex, Badge } from '@mantine/core';
import { IconMessageCircle, IconSettings, IconPhoto, IconSquareRoundedPlusFilled } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { DentalSpecialtyTabProps, SaveManagerRef, ModificationState, SVGPathStyle } from '../../shared/types';
import { useSaveManager } from '../../shared/SaveManager';
import { EstheticControls } from './EstheticControls';
import EstheticProcedures from './EstheticProcedures';
import { DentalSvgWrapper } from '../../shared/DentalSvgWrapper';
import { <PERSON><PERSON>, DantalB } from '../../../../../utils/Tdantal';

export const EstheticDentistryTab = forwardRef<SaveManagerRef, DentalSpecialtyTabProps>(({
  onModificationChange,

}, ref) => {

  // États locaux pour cette spécialité
  const [hiddenPaths, setHiddenPaths] = useState<Record<string, boolean>>({});
  const [highlightedPaths, ] = useState<Record<string, SVGPathStyle>>({});
  const [clickedIds, ] = useState<string[]>([]);
  const [activeButton, setActiveButton] = useState<string>('');
  const [targetPath, setTargetPath] = useState<string>('');
  const [opened, {  close }] = useDisclosure(false);

  // État pour la sélection multiple de dents
  const [selectedTeeth, setSelectedTeeth] = useState<string[]>([]);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState<boolean>(false);

  // État de modification pour cette spécialité
  const modificationState: ModificationState = {
    hiddenPaths,
    highlightedPaths,
    clickedIds,
    activeButton,
    targetPath
  };

  // Gestionnaire de sauvegarde
  const { save, hasChanges, SaveManagerComponent } = useSaveManager(
    modificationState,
    onModificationChange,
    'esthetic'
  );

  // Exposer les méthodes via ref
  useImperativeHandle(ref, () => ({
     triggerSave: async () => {
       await save();
     },
     hasUnsavedChanges: () => hasChanges()
   }), [save, hasChanges]);

  // Gestionnaire de sélection de dents
  const handleToothSelection = useCallback((svgId: string) => {
    if (isMultiSelectMode) {
      setSelectedTeeth(prev => {
        if (prev.includes(svgId)) {
          return prev.filter(id => id !== svgId);
        } else {
          return [...prev, svgId];
        }
      });
    } else {
      setSelectedTeeth([svgId]);
    }
  }, [isMultiSelectMode]);

  // Gestionnaire de clic sur SVG
  const handleSvgClick = useCallback((svgId: string) => {
    // Si en mode sélection multiple, gérer la sélection
    if (isMultiSelectMode) {
      handleToothSelection(svgId);
      return;
    }

    const togglePathVisibility = (pathId: string, visible: boolean) => {
      const key = `${svgId}-${pathId}`;
      setHiddenPaths(prev => {
        const newHiddenPaths = { ...prev, [key]: visible };

        // Sauvegarder automatiquement si onModificationChange est disponible
        if (onModificationChange) {
          onModificationChange(svgId, pathId, !visible, highlightedPaths)
            .catch(error => console.error('Erreur sauvegarde esthétique:', error));
        }

        return newHiddenPaths;
      });
    };



    // Appliquer le traitement esthétique selon le bouton actif
    if (activeButton) {
      console.log(`🎨 Application du traitement ${activeButton} sur la dent ${svgId}`);

      switch (activeButton) {
        case 'whitening':
          console.log('✨ Application de blanchiment');
          togglePathVisibility('20', false);
          break;
        case 'veneer':
          console.log('🦷 Application de facettes esthétiques');
          togglePathVisibility('21', false);
          break;
        case 'bonding':
          console.log('🔗 Application de collage composite');
          togglePathVisibility('22', false);
          break;
        default:
          if (targetPath) {
            togglePathVisibility(targetPath, false);
          }
          break;
      }
    }
  }, [activeButton, targetPath, onModificationChange, highlightedPaths, isMultiSelectMode, handleToothSelection]);

  // Gestionnaire de changement de bouton
  const handleButtonClick = useCallback((buttonId: string) => {
    setActiveButton(buttonId);
    console.log(`🎨 Bouton esthétique activé: ${buttonId}`);
  }, []);

  // Gestionnaire de changement de path cible
  const handleTargetPathChange = useCallback((pathId: string) => {
    setTargetPath(pathId);
    console.log(`🎯 Path cible esthétique: ${pathId}`);
  }, []);

  // Fonctions de gestion de sélection multiple
  const handleSelectAll = useCallback(() => {
    const allTeethIds = [...Dantal, ...DantalB].map(tooth => tooth.svg_id);
    setSelectedTeeth(allTeethIds);
  }, []);

  const handleDeselectAll = useCallback(() => {
    setSelectedTeeth([]);
  }, []);

  const handleApplyToSelected = useCallback(() => {
    if (selectedTeeth.length === 0) return;

    selectedTeeth.forEach(svgId => {
      // Appliquer l'action selon le bouton actif
      switch (activeButton) {
        case 'whitening':
          [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach(pathId => {
            const key = `${svgId}-${pathId}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          });
          break;
        case 'veneer':
          [20, 21, 22, 23].forEach(pathId => {
            const key = `${svgId}-${pathId}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          });
          break;
        case 'bonding':
          [24, 25, 26].forEach(pathId => {
            const key = `${svgId}-${pathId}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          });
          break;
        default:
          if (targetPath) {
            const key = `${svgId}-${targetPath}`;
            setHiddenPaths(prev => ({ ...prev, [key]: false }));
          }
          break;
      }
    });

    // Désélectionner après application
    setSelectedTeeth([]);
    setIsMultiSelectMode(false);
  }, [selectedTeeth, activeButton, targetPath]);

  return (
    <>
      {/* Composant de sauvegarde invisible */}
      {SaveManagerComponent}

      <div className="my-4">
        <Container>
          <Flex style={{ position: 'relative', height: '30px', width: '100%', marginBottom: '15px' }} align="center">
            <div style={{ position: 'absolute', right: 16 }}>
              <Menu withinPortal position="bottom-end" shadow="sm">
                <Menu.Target>
                  <Button variant="default" leftSection={<IconSquareRoundedPlusFilled size={14} />}>
                    Procédures Esthétiques
                  </Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item>Blanchiment</Menu.Item>
                  <Menu.Item>Facettes</Menu.Item>
                  <Menu.Item>Collage composite</Menu.Item>
                  <Menu.Item>Design du sourire</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </div>

            {/* Contrôles spécifiques à l'esthétique */}
            <EstheticControls
              activeButton={activeButton}
              onButtonClick={handleButtonClick}
              onTargetPathChange={handleTargetPathChange}
            />
          </Flex>

          {/* Contrôles de sélection multiple */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Button
                  size="xs"
                  variant={isMultiSelectMode ? "filled" : "outline"}
                  onClick={() => setIsMultiSelectMode(!isMultiSelectMode)}
                  color="blue"
                >
                  {isMultiSelectMode ? "Mode Normal" : "Sélection Multiple"}
                </Button>
                {selectedTeeth.length > 0 && (
                  <Badge color="blue" variant="light">
                    {selectedTeeth.length} dent{selectedTeeth.length > 1 ? 's' : ''} sélectionnée{selectedTeeth.length > 1 ? 's' : ''}
                  </Badge>
                )}
              </div>

              {isMultiSelectMode && (
                <div className="flex gap-2">
                  <Button size="xs" variant="outline" onClick={handleSelectAll}>
                    Tout sélectionner
                  </Button>
                  <Button size="xs" variant="outline" onClick={handleDeselectAll}>
                    Tout désélectionner
                  </Button>
                  {selectedTeeth.length > 0 && activeButton && (
                    <Button size="xs" variant="filled" color="green" onClick={handleApplyToSelected}>
                      Appliquer à la sélection
                    </Button>
                  )}
                </div>
              )}
            </div>

            {isMultiSelectMode && (
              <Text size="xs" c="dimmed">
                Cliquez sur les dents pour les sélectionner, puis choisissez un traitement et cliquez sur &quot;Appliquer à la sélection&quot;
              </Text>
            )}
          </div>

          {/* SVG Dentaire - Toutes les 32 dents */}
          <div className="dental-svg-container">
            <div className="max-w-[920px] mt-2 mx-auto">
              {/* Mâchoire supérieure (dents 1-16) */}
              <div className="flex h-full px-0 max-h-[200px] mb-2">
                {Dantal.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                    isSelected={selectedTeeth.includes(svgData.svg_id)}
                    isMultiSelectMode={isMultiSelectMode}
                  />
                ))}
              </div>
              {/* Mâchoire inférieure (dents 17-32) */}
              <div className="flex h-full px-0 max-h-[160px]">
                {DantalB.map((svgData) => (
                  <DentalSvgWrapper
                    key={svgData.svg_id}
                    svgId={svgData.svg_id}
                    hiddenPaths={hiddenPaths}
                    highlightedPaths={highlightedPaths}
                    onSvgClick={handleSvgClick}
                    isSelected={selectedTeeth.includes(svgData.svg_id)}
                    isMultiSelectMode={isMultiSelectMode}
                  />
                ))}
              </div>
            </div>
          </div>
        </Container>

        {/* Onglets de procédures */}
        <div className="border-base-200 mx-4 border-t mt-4">
          <Tabs variant="unstyled" defaultValue="All Procedures">
            <Tabs.List grow className="space-x-0 gap-0 mt-2">
              <Tabs.Tab
                value="All Procedures"
                leftSection={<IconMessageCircle style={{ width: rem(16), height: rem(16) }} />}
              >
                Toutes les Procédures
              </Tabs.Tab>
              <Tabs.Tab
                value="Planned"
                leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} />}
              >
                Planifiées
              </Tabs.Tab>
              <Tabs.Tab
                value="Completed"
                leftSection={<IconPhoto style={{ width: rem(16), height: rem(16) }} />}
              >
                Terminées
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="All Procedures">
              <EstheticProcedures
                type="all"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Planned">
              <EstheticProcedures
                type="planned"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>

            <Tabs.Panel value="Completed">
              <EstheticProcedures
                type="completed"
                modificationState={modificationState}
                onModificationChange={onModificationChange}
              />
            </Tabs.Panel>
          </Tabs>
        </div>

        {/* Dialog de sauvegarde */}
        <Dialog opened={opened} withCloseButton onClose={close} size="md" radius="md" position={{ top: 5, right: 10 }}>
          <Group align="flex">
            <Text size="sm" mb="xs" fw={500}>
              Sauvegarder les Modifications Esthétiques
            </Text>
          </Group>
          <Group align="flex-end">
            <Button
              w="100%"
              onClick={async () => {
                await save();
                close();
              }}
              className="hover:bg-[#3799CE]/90"
            >
              Sauvegarder
            </Button>
          </Group>
        </Dialog>
      </div>
    </>
  );
});

EstheticDentistryTab.displayName = 'EstheticDentistryTab';
