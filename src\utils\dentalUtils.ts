export const ALLOWED_POSITIONS = new Set([
  "1(3)", "1(5)", "1(7)", 
  "2(7)", "2(5)", "2(3)",
  "3(7)", "3(5)", "3(3)",
  "4(4)", "4(3)", 
  "5(3)", "5(4)", 
  "6(3)", 
  "7(3)", 
  "8(3)", 
  "9(3)", 
  "10(3)",
  "11(3)", 
  "12(4)", "12(3)", 
  "13(4)",  "13(3)", 
  "14(5)", "14(3)",  "14(7)",
  "15(3)","15(7)", "15(5)", 
  "16(7)", "16(5)", "16(3)", 

  "32(3)", "32(5)", "32(6)",
  "31(3)", "31(4)", "31(6)",
  "30(3)", "30(5)", "30(6)",
  "29(3)", "28(3)", "27(3)", "26(3)",
   "25(3)", "24(3)", "23(3)", "22(3)",
  "21(3)", "20(3)",
   "19(3)", "19(4)", "19(6)", 
   "18(5)", "18(3)", "18(6)", 
   "17(6)", "17(4)", "17(3)"
]);

export const isAllowedPosition = (position: string): boolean => {
  return ALLOWED_POSITIONS.has(position);
};

export const ALLOWED_POSITIONS_Permanent = new Set([
  "1(8)", "1(9)", "1(10)", "1(11)", "1(12)", "1(13)", "1(14)", "1(15)", "1(16)", 
  "2(8)", "2(9)", "2(10)", "2(11)", "2(12)", "2(13)", "2(14)", "2(15)", "2(16)", 
  "3(8)", "3(9)", "3(10)", "3(11)", "3(12)", "3(13)", "3(14)", "3(15)", "3(16)", 
  "4(8)", "4(9)", "4(10)", "4(11)", "4(12)", "4(13)", "4(14)", "4(15)", "4(16)", 
  "5(8)", "5(9)", "5(10)", "5(11)", "5(12)", "5(13)", "5(14)", "5(15)", "5(16)", 
  "6(8)", "6(9)", "6(10)", "6(11)", "6(12)", "6(13)", "6(14)", "6(15)", "6(16)", 
  "7(8)", "7(9)", "7(10)", "7(11)", "7(12)", "7(13)", "7(14)", "7(15)", "7(16)", 
  "8(8)", "8(9)", "8(10)", "8(11)", "8(12)", "8(13)", "8(14)", "8(15)", "8(16)", 
  "9(8)", "9(9)", "9(10)", "9(11)", "9(12)", "9(13)", "9(14)", "9(15)", "9(16)", 
  "10(8)", "10(9)", "10(10)", "10(11)", "10(12)", "10(13)", "10(14)", "10(15)", "10(16)", 
  "11(8)", "11(9)", "11(10)", "11(11)", "11(12)", "11(13)", "11(14)", "11(15)", "11(16)", 
  "12(8)", "12(9)", "12(10)", "12(11)", "12(12)", "12(13)", "12(14)", "12(15)", "12(16)", 
  "13(8)", "13(9)", "13(10)", "13(11)", "13(12)", "13(13)", "13(14)", "13(15)", "13(16)", 
  "14(8)", "14(9)", "14(10)", "14(11)", "14(12)", "14(13)", "14(14)", "14(15)", "14(16)", 
  "15(8)", "15(9)", "15(10)", "15(11)", "15(12)", "15(13)", "15(14)", "15(15)", "15(16)", 
  "16(8)", "16(9)", "16(10)", "16(11)", "16(12)", "16(13)", "16(14)", "16(15)", "16(16)",

,   "17(8)", "1(9)", "1(10)", "1(11)", "1(12)", "1(13)", "1(14)", "1(15)", "1(16)", 
  "18(8)", "2(9)", "2(10)", "2(11)", "2(12)", "2(13)", "2(14)", "2(15)", "2(16)", 
  "19(8)", "3(9)", "3(10)", "3(11)", "3(12)", "3(13)", "3(14)", "3(15)", "3(16)", 
  "20(8)", "4(9)", "4(10)", "4(11)", "4(12)", "4(13)", "4(14)", "4(15)", "4(16)", 
  "21(8)", "5(9)", "5(10)", "5(11)", "5(12)", "5(13)", "5(14)", "5(15)", "5(16)", 
  "22(8)", "6(9)", "6(10)", "6(11)", "6(12)", "6(13)", "6(14)", "6(15)", "6(16)", 
  "23(8)", "7(9)", "7(10)", "7(11)", "7(12)", "7(13)", "7(14)", "7(15)", "7(16)", 
  "24(8)", "8(9)", "8(10)", "8(11)", "8(12)", "8(13)", "8(14)", "8(15)", "8(16)", 
  "25(8)", "9(9)", "9(10)", "9(11)", "9(12)", "9(13)", "9(14)", "9(15)", "9(16)", 
  "26(8)", "10(9)", "10(10)", "10(11)", "10(12)", "10(13)", "10(14)", "10(15)", "10(16)", 
  "27(8)", "11(9)", "11(10)", "11(11)", "11(12)", "11(13)", "11(14)", "11(15)", "11(16)", 
  "28(8)", "12(9)", "12(10)", "12(11)", "12(12)", "12(13)", "12(14)", "12(15)", "12(16)", 
  "29(8)", "13(9)", "13(10)", "13(11)", "13(12)", "13(13)", "13(14)", "13(15)", "13(16)", 
  "30(8)", "14(9)", "14(10)", "14(11)", "14(12)", "14(13)", "14(14)", "14(15)", "14(16)", 
  "31(8)", "15(9)", "15(10)", "15(11)", "15(12)", "15(13)", "15(14)", "15(15)", "15(16)", 
  "32(8)", "16(9)", "16(10)", "16(11)", "16(12)", "16(13)", "16(14)", "16(15)", "16(16)",
]);

export const isOnlyallowed = (position: string): boolean => {
  return ALLOWED_POSITIONS_Permanent.has(position);
};

export const ALLOWED_POSITIONS_IsitAvailable = new Set([
  "1(69)", 
  "2(69)", 
  "3(69)", 
  "4(69)", 
  "5(69)", 
  "6(69)", 
  "7(69)", 
  "8(69)", 
  "9(69)", 
  "10(69)",
  "11(3)", 
  "12(4)",
  "13(4)", 
  "14(5)", 
  "15(3)",
  "16(7)", 
  "32(3)", 
  "31(3)", 
  "30(3)", 
  "29(3)", 
  "28(3)", 
  "27(3)", 
  "26(3)", 
  "25(3)", 
  "24(3)", 
  "23(3)", 
  "22(3)", 
  "21(3)", 
   "19(3)", 
   "18(5)",  
   "17(6)", 
]);

export const IsitAvailable = (position: string): boolean => {
  return ALLOWED_POSITIONS.has(position);
};