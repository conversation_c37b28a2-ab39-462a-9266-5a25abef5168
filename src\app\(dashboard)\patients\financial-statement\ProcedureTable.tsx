import { Table } from '@mantine/core';


interface TeethProcedure {
  code: string;
  name: string;
  teeth: string;
  price: number;
  discount: number;
  progress: number;
  comment?: string;
}

interface Props {
  data: TeethProcedure[];
}

export function ProcedureTable({ data }: Props) {
  return (
    <Table horizontalSpacing="md" verticalSpacing="xs" miw={700} layout="fixed"   striped highlightOnHover withTableBorder withColumnBorders>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>Code</Table.Th>
          <Table.Th>Actes</Table.Th>
          <Table.Th>Dent</Table.Th>
          <Table.Th>Honoraire</Table.Th>
          <Table.Th>Remise</Table.Th>
          <Table.Th>Total</Table.Th>
          <Table.Th>%</Table.Th>
          <Table.Th>Commentaire</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {data.length === 0 ? (
          <Table.Tr>
            <Table.Td colSpan={8} style={{ textAlign: 'center' }}>
              Aucun élément trouvé.
            </Table.Td>
          </Table.Tr>
        ) : (
          data.map((item, index) => (
            <Table.Tr key={index}>
              <Table.Td>{item.code}</Table.Td>
              <Table.Td>{item.name}</Table.Td>
              <Table.Td>{item.teeth}</Table.Td>
              <Table.Td>{item.price.toFixed(2)}</Table.Td>
              <Table.Td>{item.discount.toFixed(2)}</Table.Td>
              <Table.Td>{(item.price - item.discount).toFixed(2)}</Table.Td>
              <Table.Td>{item.progress.toFixed(2)}</Table.Td>
              <Table.Td>{item.comment || ''}</Table.Td>
            </Table.Tr>
          ))
        )}
      </Table.Tbody>
    </Table>
  );
}
