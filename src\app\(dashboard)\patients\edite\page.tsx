
"use client";
import { useState, useEffect } from "react";
import React from "react";
import { useSearchParams } from "next/navigation";
//import { rem } from "@mantine/core";
import Icon from '@mdi/react';
import { mdiAccountEdit,mdiAccountPlus } from '@mdi/js';
import AddPatient from "./AddPatient"
import EditePatient from "./EditePatient"
import Assurance from "./Assurance"
import FicheMedicale from "./FicheMedicale"
import {Biometrie} from "./Biometrie"
import {PiecesJointes} from "./PiecesJointes"

import "~/styles/tab.css";
const tabMapping: { [key: string]: number } = {
  'EditePatient': 1,
  'AddPatient': 2,
  'Assurance': 3,
  'FicheMedicale': 4,
   'Biometrie': 5,
  'PiecesJointes': 6,

};
function  AppointmentsPage() {
  // const iconStyle = { width: rem(14), height: rem(14) };
  const [toggleState, setToggleState] = useState(1);
   const searchParams = useSearchParams();
// Effet pour lire les paramètres d'URL et définir l'onglet actif
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tabMapping[tab]) {
      setToggleState(tabMapping[tab]);
    }
  }, [searchParams]);
const icons = [
  { icon: <Icon path={mdiAccountEdit} size={1} key="EditePatient" />, label: "Edite patient" },
  {
    icon: <Icon path={mdiAccountPlus} size={1} key="AddPatient" />,
    label: "Ajouter un patient",
  },
  {
    icon: <Icon path={mdiAccountPlus} size={1} key="Assurance" />,
    label: "Assurance",
  },
  {
    icon: <Icon path={mdiAccountPlus} size={1} key="FicheMedicale" />,
    label: "Fiche médicale",
  },
  {
    icon: <Icon path={mdiAccountPlus} size={1} key="Biometrie" />,
    label: "Biométrie",
  },
   {
    icon: <Icon path={mdiAccountPlus} size={1} key="PiecesJointes" />,
    label: "Pièces jointes",
  },
  
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<div className="w-full"><EditePatient/></div> )
    
     case 2:
      return (  <div className="w-full"><AddPatient/></div>)
      case 3:
         return (  <div className="w-full"><Assurance/></div>)
       case 4:
           return (<div className="w-full"><FicheMedicale/></div>)
            case 5:
           return (<div className="w-full"><Biometrie/></div>)
            case 6:
           return (<div className="w-full"><PiecesJointes/></div>)

    default:
      return null;
  }
};
  return (
    <>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 