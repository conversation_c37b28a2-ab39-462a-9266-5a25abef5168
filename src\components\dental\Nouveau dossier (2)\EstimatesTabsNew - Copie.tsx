// frontend/dental_medicine/src/components/content/dental/EstimatesTabsNew.tsx

import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Tabs, rem, <PERSON>ton, Group, Text } from '@mantine/core';
import {
  IconSparkles,
  IconMedicalCross,
  IconTool,
  IconBraces,
  IconBrain,
  IconCheck,
  IconX
} from '@tabler/icons-react';

import { EstimatesTabsRef, DentalSpecialtyTabProps, SaveManagerRef } from './shared/types';
import { EstheticDentistryTab } from './specialties/EstheticDentistry/EstheticDentistryTab';
import { TherapeuticTab } from './specialties/TherapeuticDentistry/TherapeuticTab';
import { ProsthodonticsTab } from './specialties/Prosthodontics/ProsthodonticsTab';
import { SurgeryTab } from './specialties/Surgery/SurgeryTab';
import { OrthodonticsTab } from './specialties/Orthodontics/OrthodonticsTab';
import { TreatmentDemo } from './shared/TreatmentDemo';
import { SmartSystemStatus } from './shared/SmartSystemStatus';

export const EstimatesTabsNew = forwardRef<EstimatesTabsRef, DentalSpecialtyTabProps>(({
  onModificationChange,
  session,
  isLoading,
}, ref) => {

  // État pour le mode intelligent
  const [isSmartModeEnabled, setIsSmartModeEnabled] = useState(true);
  const [showDemo, setShowDemo] = useState(false);

  // Références pour chaque spécialité
  const estheticRef = useRef<SaveManagerRef>(null);
  const therapeuticRef = useRef<SaveManagerRef>(null);
  const prosthodonticRef = useRef<SaveManagerRef>(null);
  const surgeryRef = useRef<SaveManagerRef>(null);
  const orthodonticRef = useRef<SaveManagerRef>(null);

  /**
   * Fonction utilitaire pour appliquer un traitement sans empilement de couches
   * Basée sur la logique corrigée d'EstimatesTabs.tsx
   */
  const applyTreatmentWithoutStacking = (svgId: string, treatmentPaths: string[]) => {
    const newHiddenPaths: Record<string, boolean> = {};

    // Cacher tous les paths pour cette dent (1-100)
    for (let i = 1; i <= 100; i++) {
      newHiddenPaths[`${svgId}-${i}`] = true;
    }

    // Afficher les paths de base de la dent (8-16)
    [8, 9, 10, 11, 12, 13, 14, 15, 16].forEach((pathId) => {
      newHiddenPaths[`${svgId}-${pathId}`] = false;
    });

    // Afficher uniquement les paths du traitement spécifique
    treatmentPaths.forEach((pathId) => {
      newHiddenPaths[`${svgId}-${pathId}`] = false;
    });

    return newHiddenPaths;
  };

  // Exposer la méthode triggerSave via ref
  useImperativeHandle(ref, () => ({
    triggerSave: async () => {
      console.log("🔄 Déclenchement de la sauvegarde globale...");

      const savePromises: Promise<void>[] = [];

      // Sauvegarder chaque spécialité qui a des modifications
      if (estheticRef.current?.hasUnsavedChanges()) {
        console.log("💄 Sauvegarde Esthétique...");
        savePromises.push(estheticRef.current.triggerSave());
      }

      if (therapeuticRef.current?.hasUnsavedChanges()) {
        console.log("💊 Sauvegarde Thérapeutique...");
        savePromises.push(therapeuticRef.current.triggerSave());
      }

      if (prosthodonticRef.current?.hasUnsavedChanges()) {
        console.log("🔧 Sauvegarde Prothèse...");
        savePromises.push(prosthodonticRef.current.triggerSave());
      }

      if (surgeryRef.current?.hasUnsavedChanges()) {
        console.log("⚔️ Sauvegarde Chirurgie...");
        savePromises.push(surgeryRef.current.triggerSave());
      }

      if (orthodonticRef.current?.hasUnsavedChanges()) {
        console.log("📐 Sauvegarde Orthodontie...");
        savePromises.push(orthodonticRef.current.triggerSave());
      }

      // Attendre que toutes les sauvegardes se terminent
      if (savePromises.length > 0) {
        await Promise.all(savePromises);
        console.log("✅ Toutes les spécialités sauvegardées!");
      } else {
        console.log("💡 Aucune modification à sauvegarder");
      }
    }
  }), []);

  return (
    <div>
      {/* Contrôles du Mode Intelligent */}
      <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200 mt-4">
        <Group justify="space-between" align="center">
          <div className="flex items-center space-x-3">
            <IconBrain className="text-blue-600" size={24} />
            <div>
              <Text size="sm" fw={600} className="text-blue-800">
                Système de Traitements Intelligents
              </Text>
              <Text size="xs" className="text-blue-600">
                Gestion automatique des conflits et compatibilités
              </Text>
            </div>
          </div>
          <Group>
            <Button
              variant={isSmartModeEnabled ? "filled" : "outline"}
              color={isSmartModeEnabled ? "green" : "gray"}
              size="sm"
              leftSection={isSmartModeEnabled ? <IconCheck size={16} /> : <IconX size={16} />}
              onClick={() => setIsSmartModeEnabled(!isSmartModeEnabled)}
            >
              {isSmartModeEnabled ? "Activé" : "Désactivé"}
            </Button>
            <Button
              variant="light"
              color="blue"
              size="sm"
              leftSection={<IconBrain size={16} />}
              onClick={() => setShowDemo(!showDemo)}
            >
              {showDemo ? "Masquer Démo" : "Voir Démo"}
            </Button>
            <SmartSystemStatus isEnabled={isSmartModeEnabled} />
          </Group>
        </Group>
      </div>

      <Tabs variant="outline" defaultValue={showDemo ? "demo" : "esthetic"} orientation="horizontal">
        <Tabs.List grow>
          {showDemo && (
            <Tabs.Tab
              value="demo"
              leftSection={<IconBrain style={{ width: rem(16), height: rem(16) }} />}
            >
              🧠 Démo Intelligente
            </Tabs.Tab>
          )}

          <Tabs.Tab
            value="esthetic"
            leftSection={<IconSparkles style={{ width: rem(16), height: rem(16) }} />}
          >
            Dentisterie Esthétique
          </Tabs.Tab>

          <Tabs.Tab
            value="therapeutic"
            leftSection={<IconMedicalCross style={{ width: rem(16), height: rem(16) }} />}
          >
            Thérapie
          </Tabs.Tab>

          <Tabs.Tab
            value="prosthodontic"
            leftSection={<IconTool style={{ width: rem(16), height: rem(16) }} />}
          >
            Prothèses Thérapeutiques
          </Tabs.Tab>

          <Tabs.Tab
            value="surgery"
            leftSection={<IconTool style={{ width: rem(16), height: rem(16) }} />}
          >
            Chirurgie et Orthopédie
          </Tabs.Tab>

          <Tabs.Tab
            value="orthodontic"
            leftSection={<IconBraces style={{ width: rem(16), height: rem(16) }} />}
          >
            Orthodontie
          </Tabs.Tab>
        </Tabs.List>

        {/* Onglet Démonstration Intelligente */}
        {showDemo && (
          <Tabs.Panel value="demo">
            <TreatmentDemo />
          </Tabs.Panel>
        )}

        {/* Onglet Dentisterie Esthétique */}
        <Tabs.Panel value="esthetic">
        <EstheticDentistryTab
          ref={estheticRef}
          onModificationChange={onModificationChange}
          session={session}
          isLoading={isLoading}
        />
      </Tabs.Panel>

      {/* Onglet Thérapie */}
      <Tabs.Panel value="therapeutic">
        <TherapeuticTab
          ref={therapeuticRef}
          onModificationChange={onModificationChange}
          session={session}
          isLoading={isLoading}
        />
      </Tabs.Panel>

      {/* Onglet Prothèses */}
      <Tabs.Panel value="prosthodontic">
        <ProsthodonticsTab
          ref={prosthodonticRef}
          onModificationChange={onModificationChange}
          session={session}
          isLoading={isLoading}
        />
      </Tabs.Panel>

      {/* Onglet Chirurgie */}
      <Tabs.Panel value="surgery">
        <SurgeryTab
          ref={surgeryRef}
          onModificationChange={onModificationChange}
          session={session}
          isLoading={isLoading}
        />
      </Tabs.Panel>

      {/* Onglet Orthodontie */}
      <Tabs.Panel value="orthodontic">
        <OrthodonticsTab
          ref={orthodonticRef}
          onModificationChange={onModificationChange}
          session={session}
          isLoading={isLoading}
        />
      </Tabs.Panel>
    </Tabs>
    </div>
  );
});

EstimatesTabsNew.displayName = 'EstimatesTabsNew';

export default EstimatesTabsNew;
