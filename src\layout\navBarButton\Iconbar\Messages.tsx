"use client";

import {
  <PERSON><PERSON>,
  Flex,
  Indicator,
  Stack,
  Text,
  useMantineTheme,
  useMantineColorScheme,
  Menu,
  ActionIcon,
  Tooltip,
} from "@mantine/core";
import { IconMessageCircle } from "@tabler/icons-react";

const ICON_SIZE = 20;

const MESSAGES = [
  {
    id: "687725a3-5489-486e-9ffd-180df00f8e10",
    first_name: "<PERSON><PERSON>",
    last_name: "<PERSON><PERSON><PERSON>",
    message:
      "Vivamus vestibulum sagittis sapien. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Etiam vel augue.",
  },
  {
    id: "75103d7a-b26b-4e1f-8d7e-2117867d05b8",
    first_name: "<PERSON><PERSON><PERSON><PERSON>",
    last_name: "<PERSON><PERSON><PERSON>",
    message:
      "In eleifend quam a odio. In hac habitasse platea dictumst. Maecenas ut massa quis augue luctus tincidunt. Nulla mollis molestie lorem. Quisque ut erat.",
  },
  {
    id: "08bff541-e731-445f-a2e1-7f08e3e30357",
    first_name: "Bald",
    last_name: "Vant",
    message:
      "Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Vivamus vestibulum sagittis sapien. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Etiam vel augue. Vestibulum rutrum rutrum neque.",
  },
];

const Messages = () => {
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();

  // Set default value for primaryColor
  const primaryColor = theme.primaryColor || "blue";

  // Use fallback color if needed
  const primaryColorShade =
    theme.colors[primaryColor] && theme.colors[primaryColor][7]
      ? theme.colors[primaryColor][7]
      : theme.colors.blue[7];

  const messages = MESSAGES.map((m) => (
    <Menu.Item
      key={m.id}
      style={{
        borderBottom: `1px solid ${
          colorScheme === "dark"
            ? theme.colors.blue[7]
            : theme.colors.blue[3]
        }`,
      }}
    >
      <Flex gap="sm" align="center">
        <Avatar
          src={null}
          alt={`${m.first_name} ${m.last_name}`}
          variant="filled"
          size="sm"
          color={primaryColorShade} // Use the fallback color
        >
          {m.first_name && m.first_name.length > 0 ? m.first_name.charAt(0) : ''}
          {m.last_name && m.last_name.length > 0 ? m.last_name.charAt(0) : ''}
        </Avatar>
        <Stack gap={1}>
          <Text fz="sm" fw={600}>
            {m.first_name} {m.last_name}
          </Text>
          <Text lineClamp={2} fz="xs" c="dimmed">
            {m.message}
          </Text>
        </Stack>
      </Flex>
    </Menu.Item>
  ));

  return (
    <>
      <Menu shadow="lg" width={320} zIndex={1000010}>
        <Menu.Target>
          <Indicator processing size={10} offset={6}>
            <Tooltip
              label="Messages"
              withArrow
              style={{color:"var(--mantine-color-text)"}}
            >
              <ActionIcon
                size="lg"
                title="Messages"
                className="h-10   navBarButtonicon"
                style={{color:" light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-0))"}}
              >
                <IconMessageCircle size={ICON_SIZE} className=" navBarButtonicon "/>
              </ActionIcon>
            </Tooltip>
          </Indicator>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label tt="uppercase" ta="center" fw={600}>
            {MESSAGES.length} new notifications
          </Menu.Label>
          {messages}
          <Menu.Item tt="uppercase" ta="center" fw={600}>
            Show all messages
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </>
  );
};

export default Messages;
