"use client";
import React, { forwardRef, useState, useRef, useImperativeHandle } from "react";
// -----------------Part-1----------------
import { <PERSON><PERSON>, Group, Text } from '@mantine/core';
import { SmartSystemStatus } from './shared/SmartSystemStatus';
import {
  IconBrain,
  IconCheck,
  IconX
} from '@tabler/icons-react';
import { Tabs,rem} from '@mantine/core';
import {  SaveManagerRef } from './shared/types';
import { EstheticDentistryTabNew } from './specialties/EstheticDentistry/EstheticDentistryTabNew';
import classes from '@/style/EstimatesTabs.module.css';
import { ProsthodonticsTabNew } from './specialties/Prosthodontics/ProsthodonticsTabNew';
import { TherapeuticTabNew } from './specialties/TherapeuticDentistry/TherapeuticTabNew';
import { SurgeryTabNew } from './specialties/Surgery/SurgeryTabNew';
import { OrthodonticsTabNew } from './specialties/Orthodontics/OrthodonticsTabNew';
export interface EstimatesTabsRef {
  triggerSave: () => Promise<void>;
}
// Types pour les props
interface EstimatesTabsProps {
  onModificationChange?: (svgId: string, pathId: string, isVisible: boolean, highlightedPaths?: Record<string, unknown>) => Promise<void>;
  session?:  unknown;
  isLoading?: boolean;
}
export const EstimatesTabs = forwardRef<EstimatesTabsRef, EstimatesTabsProps>(({
onModificationChange,
  session,
  isLoading,
}, ref) => {
// -----------------Part-1----------------
    const [isSmartModeEnabled, setIsSmartModeEnabled] = useState(true);
     const [showDemo, setShowDemo] = useState(false);
  // Références pour chaque spécialité
   const estheticRef = useRef<SaveManagerRef>(null);
   const therapeuticRef = useRef<SaveManagerRef>(null);
   const prosthodonticRef = useRef<SaveManagerRef>(null);
   const surgeryRef = useRef<SaveManagerRef>(null);
   const orthodonticRef = useRef<SaveManagerRef>(null);

   // Exposer les méthodes via ref
   useImperativeHandle(ref, () => ({
     triggerSave: async () => {
       // Déclencher la sauvegarde pour tous les onglets
       const savePromises = [
         estheticRef.current?.triggerSave(),
         therapeuticRef.current?.triggerSave(),
         prosthodonticRef.current?.triggerSave(),
         surgeryRef.current?.triggerSave(),
         orthodonticRef.current?.triggerSave(),
       ].filter(Boolean);

       await Promise.all(savePromises);
     }
   }));
   return (
   <>
{/* -----------------Part-1---------------- */}
    <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200 mt-4">
           <Group justify="space-between" align="center">
             <div className="flex items-center space-x-3">
               <IconBrain className="text-blue-600" size={24} />
               <div>
                 <Text size="sm" fw={600} className="text-blue-800">
                   Système de Traitements Intelligents
                 </Text>
                 <Text size="xs" className="text-blue-600">
                   Gestion automatique des conflits et compatibilités
                 </Text>
               </div>
             </div>
             <Group>
               <Button
                 variant={isSmartModeEnabled ? "filled" : "outline"}
                 color={isSmartModeEnabled ? "green" : "gray"}
                 size="sm"
                 leftSection={isSmartModeEnabled ? <IconCheck size={16} /> : <IconX size={16} />}
                 onClick={() => setIsSmartModeEnabled(!isSmartModeEnabled)}
               >
                 {isSmartModeEnabled ? "Activé" : "Désactivé"}
               </Button>
               <Button
                 variant="light"
                 color="blue"
                 size="sm"
                 leftSection={<IconBrain size={16} />}
                 onClick={() => setShowDemo(!showDemo)}
               >
                 {showDemo ? "Masquer Démo" : "Voir Démo"}
               </Button>
               <SmartSystemStatus isEnabled={isSmartModeEnabled} />
             </Group>
           </Group>
         </div>
      <Tabs
             variant="unstyled"
             defaultValue="Therapy"
             classNames={classes}
           >
      <Tabs.List grow className="space-x-0 gap-0 mx-4">
         <Tabs.Tab
              value="esthetic"
              leftSection={
                < svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 -30 104 130" x="0px" y="0px" style={{ width: rem(40), height: rem(32) }} >
              <path  d="M100.02,23c-0.49-1.27-1.61-2.06-2.96-2c-3.51,0.08-6.45-0.74-8.74-2.47c-10.53-7.91-18.07-8.33-31.01-1.73   c-3.33,1.7-7.3,1.7-10.63,0c-12.92-6.59-20.43-6.18-30.92,1.67c-2.33,1.74-5.32,2.6-8.86,2.53c-1.34,0.01-2.47,0.76-2.94,2.04   C3.5,24.28,3.66,26.28,5.45,28c-0.39,0.61-0.63,1.34-0.63,2.12v2.43c0,2.6,2.11,4.71,4.7,4.71h2.3c0.66,0,1.26-0.25,1.73-0.64   c1,2.42,3.39,4.13,6.17,4.13h1.42c1.51,0,2.84-0.72,3.7-1.82c0.99,2.44,3.39,4.17,6.19,4.17h0.22v6.56l-6.81,6.5   c-2.2,2.12-3.47,5.09-3.47,8.14v26.75c0,0.42,0.34,0.75,0.75,0.75h12.93c0.41,0,0.75-0.33,0.75-0.75V66.22   c0-0.76,0.19-1.51,0.56-2.18l3.66-6.61c0.18-0.32,0.34-0.66,0.51-0.99h2.28c2.59,0,4.69-2.11,4.69-4.7v-4.48h1.04   c1.66,0,3.12-0.87,3.95-2.17c0.84,1.3,2.29,2.17,3.96,2.17h4.48c3.29,0,6.03-2.39,6.58-5.52c0.85,0.85,2.01,1.37,3.3,1.37h2.75   c2.8,0,5.2-1.73,6.19-4.18c0.86,1.1,2.19,1.82,3.69,1.82h1.43c2.78,0,5.17-1.71,6.17-4.13c0.47,0.39,1.07,0.64,1.73,0.64h2.29   c2.6,0,4.71-2.11,4.71-4.71v-2.43c0-0.83-0.26-1.6-0.7-2.24C100.09,26.44,100.62,24.57,100.02,23z M59.35,23.73   c-2.26,1.16-4.8,1.76-7.35,1.76c-2.56,0-5.1-0.6-7.36-1.76c-5.57-2.84-9.65-4.1-13.23-4.1c-0.42,0-0.75-0.34-0.75-0.75   c0-0.42,0.33-0.75,0.75-0.75c3.88,0,8.04,1.27,13.91,4.27c4.1,2.09,9.25,2.09,13.35,0c5.78-2.95,10.08-4.27,13.91-4.27   c0.42,0,0.75,0.34,0.75,0.75s-0.33,0.75-0.75,0.75C69,19.63,64.91,20.9,59.35,23.73z M38.66,43.03c0.41,0,0.75,0.33,0.75,0.75   c0,0.41-0.34,0.75-0.75,0.75h-0.8v0.79c0,0.42-0.34,0.75-0.75,0.75c-0.42,0-0.75-0.33-0.75-0.75v-0.79h-0.8   c-0.41,0-0.75-0.34-0.75-0.75c0-0.42,0.34-0.75,0.75-0.75h0.8v-0.8c0-0.42,0.33-0.75,0.75-0.75c0.41,0,0.75,0.33,0.75,0.75v0.8   H38.66z M13.04,34.53c0,0.68-0.55,1.23-1.22,1.23h-2.3c-1.76,0-3.2-1.44-3.2-3.21v-2.43c0-1.36,1.11-2.47,2.47-2.47h1.76   c1.38,0,2.49,1.11,2.49,2.47V34.53z M24.35,36.05c0,1.77-1.44,3.2-3.21,3.2h-1.42c-2.85,0-5.18-2.32-5.18-5.18v-1.71   c0-2.04,1.66-3.7,3.7-3.7h2.41c2.04,0,3.7,1.66,3.7,3.7V36.05z M31.03,41.6c-2.85,0-5.18-2.31-5.18-5.16v-3.89   c0-2.04,1.66-3.7,3.7-3.7h3.73c2.04,0,3.7,1.66,3.7,3.7v4.76h-0.53c-2.57,0-4.69,1.86-5.11,4.29H31.03z M25.6,90.3h-3.13v-26   c0-2.65,1.1-5.22,3.01-7.06l6.01-5.73c0.3,1.11,0.88,2.12,1.7,2.94l-4.12,3.95c-2.21,2.11-3.47,5.08-3.47,8.13V90.3z M38.31,56.7   l-3.66,6.61c-0.49,0.89-0.75,1.9-0.75,2.91V90.3h-6.8V66.53c0-2.65,1.09-5.22,3.01-7.05L34.79,55c1.74-1.65,3.69-3.02,5.79-4.08   C40.09,52.94,39.32,54.88,38.31,56.7z M51.34,42.57c0,1.76-1.44,3.19-3.2,3.19H47.1v-3.27c0-2.86-2.32-5.18-5.18-5.18h-3.44v-3.99   c0-2.03,1.66-3.69,3.7-3.69h5.47c2.03,0,3.69,1.66,3.69,3.69V42.57z M65.71,40.59c0,2.85-2.32,5.17-5.18,5.17h-4.48   c-1.77,0-3.21-1.43-3.21-3.2v-9.24c0-2.03,1.66-3.69,3.7-3.69h5.48c2.03,0,3.69,1.66,3.69,3.69V40.59z M78.34,36.43   c0,2.86-2.32,5.18-5.18,5.18h-2.75c-1.76,0-3.2-1.44-3.2-3.2v-5.86c0-2.04,1.66-3.69,3.7-3.69h3.73c2.04,0,3.7,1.65,3.7,3.69V36.43   z M89.65,34.07c0,2.86-2.33,5.18-5.18,5.18h-1.43c-1.76,0-3.2-1.43-3.2-3.2v-3.69c0-2.04,1.66-3.7,3.7-3.7h2.41   c2.04,0,3.7,1.66,3.7,3.7V34.07z M97.87,32.55c0,1.77-1.44,3.21-3.21,3.21h-2.29c-0.67,0-1.22-0.55-1.22-1.23v-4.41   c0-1.36,1.11-2.47,2.49-2.47h1.76c1.36,0,2.47,1.11,2.47,2.47V32.55z"/><path d="M66.54,64.99l-0.8,0v-0.8c0-0.41-0.34-0.75-0.75-0.75s-0.75,0.34-0.75,0.75v0.8l-0.8,0c-0.41,0-0.75,0.34-0.75,0.75   c0,0.41,0.34,0.75,0.75,0.75l0.8,0v0.8c0,0.41,0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75v-0.8l0.8,0c0.41,0,0.75-0.34,0.75-0.75   C67.29,65.32,66.95,64.99,66.54,64.99z"/>
              <path  d="M12.31,56.83h-0.8v-0.8c0-0.41-0.34-0.75-0.75-0.75s-0.75,0.34-0.75,0.75v0.8h-0.8c-0.41,0-0.75,0.34-0.75,0.75   c0,0.41,0.34,0.75,0.75,0.75h0.8v0.8c0,0.41,0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75v-0.8h0.8c0.41,0,0.75-0.34,0.75-0.75   C13.06,57.17,12.72,56.83,12.31,56.83z"/>
              <path  d="M45.67,81.59h-0.8v-0.8c0-0.41-0.34-0.75-0.75-0.75s-0.75,0.34-0.75,0.75v0.8h-0.8c-0.41,0-0.75,0.34-0.75,0.75   c0,0.41,0.34,0.75,0.75,0.75h0.8v0.8c0,0.41,0.34,0.75,0.75,0.75s0.75-0.34,0.75-0.75v-0.8h0.8c0.41,0,0.75-0.34,0.75-0.75   C46.42,81.92,46.09,81.59,45.67,81.59z"/>
            </svg>
            }
            >
              {/* Dentisterie Esthétique */}
              Cosmetic Dentistry
            </Tabs.Tab>
            <Tabs.Tab
              value="therapeutic"
              leftSection={
                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0px" y="0px" viewBox="0 0 64 80" style={{ width: rem(40), height: rem(32) }}>
                <path d="M31.93,64c-0.88,0-1.72-0.36-2.34-1.01c-3.01-3.18-3.16-10.8-2.98-15.06c0.06-1.38-0.31-2.72-1.07-3.87
                c-2.35-3.55-3.68-6.97-3.93-10.16c-0.04-0.53,0.36-1,0.89-1.04c0.55-0.03,1,0.36,1.04,0.89c0.23,2.86,1.44,5.97,3.61,9.25
                c0.99,1.49,1.47,3.23,1.39,5.02c-0.28,6.73,0.61,11.7,2.45,13.64c0.28,0.29,0.68,0.44,1.07,0.4c0.22-0.02,0.62-0.12,0.89-0.55
                c0.62-1.01,1.26-3.32,0.75-8.57l0-2.27c0-1.67,0.66-3.24,1.86-4.41c1.2-1.17,2.77-1.78,4.47-1.73c3.28,0.09,5.95,2.94,5.95,6.35
                v1.96c-0.51,5.38,0.12,7.67,0.74,8.66c0.27,0.43,0.68,0.53,0.9,0.56c0.39,0.04,0.79-0.11,1.07-0.4
                c1.84-1.95,2.74-6.92,2.46-13.64c-0.08-1.78,0.4-3.52,1.38-5.01c5.76-8.71,3.06-13.47,2.94-13.67
                  c-3.23-6.61-11.08-4.98-14.3-3.97c-0.87,0.27-1.78,0.27-2.66,0c-0.89-0.28-2.3-0.65-3.94-0.82c-0.53-0.06-0.92-0.54-0.86-1.07      c0.06-0.53,0.53-0.92,1.07-0.86c1.8,0.19,3.34,0.6,4.32,0.9c0.49,0.15,1.01,0.15,1.5,0c2.89-0.9,12.62-3.21,16.58,4.89      c0.1,0.15,3.47,5.83-3.02,15.66c-0.75,1.15-1.12,2.49-1.06,3.87c0.18,4.26,0.03,11.87-2.99,15.06c-0.7,0.73-1.66,1.1-2.67,0.99      c-0.97-0.1-1.83-0.63-2.35-1.45c-1.14-1.82-1.49-5.11-1.04-9.78l0-1.87c0-2.37-1.83-4.35-4.07-4.41      c-1.14-0.05-2.24,0.39-3.06,1.18c-0.82,0.8-1.28,1.87-1.28,3.01v2.17c0.45,4.55,0.1,7.84-1.04,9.68      c-0.52,0.83-1.37,1.36-2.34,1.46C32.15,63.99,32.04,64,31.93,64z"/>
                <path d="M24.67,35.02c-6.06,0-10.99-4.93-10.99-10.99s4.93-10.99,10.99-10.99s10.99,4.93,10.99,10.99S30.73,35.02,24.67,35.02z
                  M24.67,14.97c-4.99,0-9.05,4.06-9.05,9.05c0,4.99,4.06,9.05,9.05,9.05c4.99,0,9.05-4.06,9.05-9.05
                  C33.73,19.03,29.66,14.97,24.67,14.97z"/>

                <path d="M41.41,16.95c-0.4,0-0.76-0.22-0.95-0.58l-2.05-3.94L34.95,9.9C34.66,9.69,34.5,9.36,34.5,9c0-0.35,0.17-0.68,0.46-0.88
                  l3.49-2.5l2.02-3.79c0.38-0.7,1.5-0.7,1.88,0l2.02,3.79l3.49,2.51c0.28,0.19,0.45,0.53,0.45,0.88c0,0.35-0.16,0.69-0.45,0.89
                  l0,0l-3.46,2.53l-2.05,3.95C42.18,16.72,41.81,16.95,41.41,16.95z M40.64,15.47C40.64,15.47,40.64,15.48,40.64,15.47
                  L40.64,15.47z M42.18,15.46L42.18,15.46C42.18,15.47,42.18,15.47,42.18,15.46z M37.04,9.02l2.68,1.96
                  c0.13,0.1,0.27,0.26,0.34,0.41l1.35,2.6l1.37-2.64c0.07-0.14,0.2-0.29,0.33-0.38l2.67-1.95l-2.7-1.94
                  c-0.12-0.09-0.25-0.24-0.32-0.37l-1.34-2.52l-1.35,2.52c-0.07,0.13-0.2,0.29-0.32,0.37L37.04,9.02z M46.72,9.7
                  c0,0,0.01,0.01,0.01,0.01L46.72,9.7z M36.09,9.69L36.09,9.69C36.09,9.7,36.09,9.7,36.09,9.69z M47.29,9.11L47.29,9.11
                  L47.29,9.11z"/>
                  <path d="M11.16,44.04C11.16,44.04,11.16,44.04,11.16,44.04c-0.39,0-0.74-0.22-0.92-0.57l-1.47-2.82l-2.48-1.81
                      c-0.25-0.18-0.43-0.55-0.43-0.86c0-0.34,0.16-0.66,0.43-0.85l2.51-1.8l1.44-2.71c0.17-0.32,0.56-0.59,0.93-0.55
                      c0.39,0,0.74,0.22,0.92,0.57l1.44,2.7l2.49,1.79c0.25,0.18,0.44,0.55,0.44,0.86c0,0.34-0.16,0.66-0.43,0.86c0,0,0,0,0,0      l-2.47,1.81l-1.47,2.83C11.92,43.8,11.52,44.04,11.16,44.04z M8.42,38l1.66,1.21c0.13,0.09,0.25,0.24,0.32,0.38l0.76,1.46      l0.77-1.48c0.06-0.12,0.17-0.25,0.28-0.34L13.9,38l-1.68-1.2c-0.13-0.09-0.26-0.24-0.33-0.38l-0.74-1.38l-0.75,1.41      c-0.06,0.12-0.17,0.25-0.27,0.33L8.42,38z M15.46,38.07L15.46,38.07L15.46,38.07z M15.46,38.07L15.46,38.07L15.46,38.07z"/>
                  <path d="M11.92,13.61C11.91,13.61,11.91,13.61,11.92,13.61c-0.4,0-0.75-0.22-0.93-0.57L9.26,9.72L6.34,7.6
                        C6.09,7.41,5.9,7.04,5.9,6.73c0-0.34,0.16-0.67,0.44-0.87l2.95-2.12l1.7-3.19C11.16,0.24,11.56,0,11.92,0c0,0,0,0,0,0
                        c0.39,0,0.74,0.22,0.93,0.57l1.7,3.18l2.93,2.11c0.25,0.18,0.45,0.57,0.44,0.88c0,0.34-0.17,0.66-0.44,0.86l-2.91,2.12      l-1.73,3.32C12.68,13.36,12.28,13.61,11.92,13.61z M8.45,6.73l2.11,1.54c0.13,0.09,0.26,0.24,0.32,0.38l1.03,1.98l1.04-1.99      c0.07-0.13,0.19-0.27,0.31-0.36l2.12-1.55L13.25,5.2c-0.13-0.09-0.26-0.25-0.33-0.39l-1-1.88l-1.01,1.9      c-0.07,0.14-0.21,0.29-0.34,0.38L8.45,6.73z M7.48,6.03L7.48,6.03C7.49,6.03,7.48,6.03,7.48,6.03z"/>
                    <path  d="M22.92,28.45c-0.26,0-0.5-0.1-0.69-0.28l-3.41-3.41c-0.38-0.38-0.38-0.99,0-1.37c0.38-0.38,0.99-0.38,1.37,0l2.72,2.72      l6.23-6.23c0.38-0.38,0.99-0.38,1.37,0c0.38,0.38,0.38,0.99,0,1.37l-6.91,6.91C23.42,28.35,23.18,28.45,22.92,28.45z"/>
                    </svg>
              }
            >
              {/* Thérapie */}
              Therapy
            </Tabs.Tab>
            <Tabs.Tab
              value="prosthodontic"
              // leftSection={<IconTool style={{ width: rem(16), height: rem(16) }} />}
                leftSection={
                <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 402.69 610.00125"  style={{ width: rem(40), height: rem(32) }}>
                    <path d="M322.956,357.198c-6.048,1.779-32.579,8.889-78.254,11.492c-0.077,0.005-0.154,0.007-0.231,0.007   c-2.104,0-3.868-1.645-3.989-3.772c-0.126-2.205,1.56-4.096,3.766-4.221c30.147-1.719,51.983-5.506,64.667-8.271   c-19.938-6.924-41.488-16.722-41.722-42.216V293.39H154.376v16.791c-0.234,25.527-21.783,35.328-41.722,42.253   c12.684,2.765,34.52,6.552,64.667,8.271c2.206,0.125,3.892,2.016,3.766,4.221c-0.121,2.128-1.885,3.772-3.989,3.772   c-0.077,0-0.154-0.002-0.231-0.007c-45.675-2.603-72.206-9.713-78.254-11.492c-8.754,3.076-16.338,6.325-19.88,10.882   c-12.409,15.962-4.057,63.677,11.007,86.318c12.123,18.224,26.963,31.646,36.927,33.401c2.921,0.515,5.319,0.043,7.332-1.439   c4.39-3.236,3.459-14.606,2.558-25.603c-1.214-14.818-2.589-31.613,5.855-43.429c13.349-18.678,35.714-27.758,68.373-27.758   c32.66,0,55.025,9.08,68.374,27.759c8.443,11.814,7.068,28.609,5.854,43.428c-0.901,10.996-1.832,22.366,2.558,25.603   c2.011,1.482,4.409,1.954,7.332,1.439c9.964-1.755,24.804-15.178,36.927-33.401c15.063-22.642,23.416-70.356,11.007-86.318   C339.293,363.523,331.71,360.274,322.956,357.198z"/>
                    <path d="M376.797,34.469c-14.703-16.611-34.464-26.93-51.572-26.93c-18.476,0-38.178,8.056-57.23,15.847   c-19.003,7.77-38.652,15.805-57.21,15.805c-8.212,0-15.967-1.495-23.41-3.884c9.488,16.549,24.755,27.248,46.436,32.547   c2.146,0.524,3.461,2.689,2.937,4.835c-0.446,1.827-2.082,3.051-3.883,3.051c-0.314,0-0.634-0.037-0.952-0.115   c-26.979-6.593-45.646-21.586-55.559-44.531c-7.788-3.415-15.29-7.534-22.686-11.598C136.227,9.911,118.191,0,97.269,0   C58.042,0,0,38.163,0,120.075c0,55.972,38.131,101.874,60.86,124.254c9.459,9.315,22.029,14.445,35.396,14.445l208.383-0.001   c15.407,0,29.753-6.854,39.356-18.804c19.033-23.68,51.838-70.567,57.707-118.531C407.205,76.467,388.46,47.646,376.797,34.469z"/>
                </svg>
                              }
            >
              {/* Prothèses Thérapeutiques */}
              Prosthodontics
            </Tabs.Tab>
            <Tabs.Tab
              value="surgery"
                leftSection={
                  <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 128 160" style={{ width: rem(40), height: rem(32) }} >
                    <path
                    d="M112.03587,37.41332V19.39526a8.588,8.588,0,0,0-8.57783-8.57784H48.5599a5.14671,5.14671,0,0,0,0,10.29341h1.71556v1.71556a5.15233,5.15233,0,0,0,5.14671,5.1467H72.57783a5.15233,5.15233,0,0,0,5.14671-5.1467V21.11083h24.01793V39.42919a8.90556,8.90556,0,0,0-1.70541,2.63691L91.69692,62.32454a5.46153,5.46153,0,0,1-5.0645,3.391H45.12876V58.8533h12.009a8.58807,8.58807,0,0,0,8.57784-8.57784V48.5599H48.37A11.96752,11.96752,0,0,0,51.991,39.98206v-5.1467H24.542v5.1467A11.96792,11.96792,0,0,0,28.163,48.5599H21.11083V27.97309a3.43113,3.43113,0,0,1,6.86226,0v3.43114h3.43114V27.97309a6.86227,6.86227,0,0,0-13.72454,0V48.5599H10.81742v1.71556a8.588,8.588,0,0,0,8.57784,8.57784h12.009v13.389L13.21989,91.64a8.85112,8.85112,0,0,0-2.40247,6.07485v.29161a8.88271,8.88271,0,0,0,15.39152,6.0447L41.35512,87.74077A8.61755,8.61755,0,0,0,46.26434,89.699L42.13657,103.458H36.55093a8.58793,8.58793,0,0,0-8.57784,8.57783v5.14671h72.05382v-5.14671a8.588,8.588,0,0,0-8.57784-8.57783H85.86343L81.72864,89.6756a8.5707,8.5707,0,0,0,7.83146-6.80436h7.312a8.86118,8.86118,0,0,0,8.27125-5.60062l11.40259-28.503a8.87456,8.87456,0,0,0-4.51007-11.35432ZM74.2934,22.82639A1.71666,1.71666,0,0,1,72.57783,24.542H55.42217a1.71666,1.71666,0,0,1-1.71557-1.71557V21.11083H74.2934Zm-25.7335-5.1467a1.71557,1.71557,0,1,1,0-3.43113H103.458a5.15232,5.15232,0,0,1,5.1467,5.1467V36.58433c-.112-.00419-.21706-.0334-.33-.0334a8.89448,8.89448,0,0,0-3.10108.57706V21.11083a3.43568,3.43568,0,0,0-3.43114-3.43114ZM27.97309,39.98206V38.2665H48.5599v1.71556a8.58808,8.58808,0,0,1-8.57784,8.57784H36.55093A8.588,8.588,0,0,1,27.97309,39.98206Z"
                  />
                  </svg>
                              }
            >
              {/* Chirurgie et Orthopédie */}
              Surgery
            </Tabs.Tab>
            <Tabs.Tab
              value="orthodontic"
              // leftSection={<IconBraces style={{ width: rem(16), height: rem(16) }} />}
              leftSection={
                <svg xmlns="http://www.w3.org/2000/svg"  version="1.1" x="0px" y="0px" viewBox="0 0 100 125" style={{ width: rem(40), height: rem(32) }}>
                <path d="M87,39h-5.112c-1.602-6.041-5.718-12.975-11.068-12.975c-5.16,0-9.173,6.415-10.889,12.275   c-1.725-5.864-5.728-12.275-10.872-12.275c-5.16,0-9.173,6.414-10.888,12.274c-1.725-5.863-5.729-12.274-10.874-12.274   c-5.366,0-9.491,6.938-11.082,12.975H12v2h3.775c-0.179,1.025-0.275,1.995-0.275,2.861c0,3.043,1.2,5.152,3.567,6.166   C20.953,50.835,23.308,51,26.19,51h0.547h1.667c3.649,0,7.971-0.063,9.799-3.229c0.595,1,1.468,1.76,2.625,2.255   C42.714,50.835,45.069,51,47.952,51h0.547h1.667c3.647,0,7.97-0.063,9.797-3.229c0.596,1,1.47,1.76,2.626,2.255   C64.476,50.835,66.83,51,69.713,51h0.547h1.666c4.764,0,10.691-0.088,10.691-7.076c0-0.883-0.101-1.875-0.286-2.924H87V39z    M70.819,29.05c3.195,0,6.422,5.122,7.924,9.95H75v-2h-9v2h-3.125C64.369,34.176,67.61,29.05,70.819,29.05z M49.059,29.05   c3.195,0,6.421,5.122,7.923,9.95H53v-2h-9v2h-2.885C42.608,34.176,45.85,29.05,49.059,29.05z M27.297,29.05   c3.196,0,6.422,5.122,7.925,9.95H32v-2h-9v2h-3.646C20.847,34.176,24.088,29.05,27.297,29.05z M28.404,48h-1.107H26.19   c-2.446,0-4.582-0.135-5.941-0.718c-0.908-0.389-1.749-1.154-1.749-3.446c0-0.854,0.125-1.822,0.336-2.836H23v2h9v-2h3.745   c0.22,1.042,0.351,2.037,0.351,2.911C36.096,47.376,34.363,48,28.404,48z M50.166,48h-1.107h-1.107   c-2.447,0-4.583-0.135-5.942-0.718c-0.908-0.389-1.749-1.154-1.749-3.446c0-0.854,0.125-1.822,0.336-2.836H44v2h9v-2h4.505   c0.221,1.042,0.351,2.037,0.351,2.911C57.855,47.376,56.123,48,50.166,48z M79.617,43.911c0,3.465-1.732,4.089-7.691,4.089h-1.106   h-1.106c-2.447,0-4.582-0.135-5.942-0.718c-0.909-0.389-1.749-1.154-1.749-3.446c0-0.854,0.126-1.822,0.336-2.836H66v2h9v-2h4.267   C79.487,42.042,79.617,43.037,79.617,43.911z"/>
                <path d="M77.458,58.919c0-6.044-5.265-5.919-9.108-5.919h-0.454h-1.382c-2.812,0-6.375-0.056-8.09,2.273   C56.708,52.944,53.144,53,50.332,53h-0.454h-1.382c-2.812,0-6.375-0.056-8.09,2.274C38.69,52.944,35.127,53,32.314,53H31.86h-1.382   c-3.844,0-9.107-0.125-9.107,5.919c0,0.638,0.063,1.341,0.18,2.081H18v2h3.987c1.353,5.046,4.85,10.799,9.409,10.799   c4.162,0,7.439-4.795,9.009-9.469c1.57,4.674,4.846,9.469,9.008,9.469s7.439-4.794,9.01-9.468c1.57,4.674,4.847,9.468,9.008,9.468   c4.56,0,8.058-5.753,9.41-10.799H80v-2h-2.722C77.395,60.26,77.458,59.557,77.458,58.919z M66.514,56h0.451h1.385   c5.081,0,6.108,0.419,6.108,2.95c0,0.624-0.082,1.32-0.22,2.05H71v-1h-7v1h-3.368c-0.142-0.741-0.226-1.449-0.226-2.081   C60.406,56.388,61.434,56,66.514,56z M48.496,56h0.451h1.385c5.082,0,6.109,0.419,6.109,2.95c0,0.624-0.082,1.32-0.22,2.05H54v-1   h-8v1h-3.386c-0.141-0.741-0.226-1.449-0.226-2.081C42.389,56.388,43.416,56,48.496,56z M24.371,58.919   c0-2.531,1.027-2.919,6.107-2.919h0.451h1.385c5.081,0,6.108,0.419,6.108,2.95c0,0.624-0.082,1.32-0.22,2.05H35v-1h-7v1h-3.403   C24.456,60.259,24.371,59.551,24.371,58.919z M31.396,70.861c-2.526,0-5.074-4.014-6.289-7.861H28v2h7v-2h2.695   C36.483,66.847,33.928,70.861,31.396,70.861z M49.414,70.861c-2.526,0-5.074-4.014-6.289-7.861H46v2h8v-2h1.713   C54.501,66.847,51.946,70.861,49.414,70.861z M67.432,70.861c-2.525,0-5.074-4.014-6.289-7.861H64v2h7v-2h2.729   C72.518,66.847,69.963,70.861,67.432,70.861z"/>
                </svg>
              }
            >
              {/* Orthodontie */}
              Ortho
            </Tabs.Tab>
      </Tabs.List>
         {/* Onglet Dentisterie Esthétique */}
              <Tabs.Panel value="esthetic">
              <EstheticDentistryTabNew
                ref={estheticRef}
                onModificationChange={onModificationChange}
                session={session}
                isLoading={isLoading}
              />
            </Tabs.Panel>
            {/* Onglet Thérapie */}
                  <Tabs.Panel value="therapeutic">
                    <TherapeuticTabNew
                      ref={therapeuticRef}
                      onModificationChange={onModificationChange}
                      session={session}
                      isLoading={isLoading}
                    />
                  </Tabs.Panel>
                {/* Onglet Prothèses */}
                  <Tabs.Panel value="prosthodontic">
                    <ProsthodonticsTabNew
                      ref={prosthodonticRef}
                      onModificationChange={onModificationChange}
                      session={session}
                      isLoading={isLoading}
                    />
                  </Tabs.Panel>
                   {/* Onglet Chirurgie */}
                         <Tabs.Panel value="surgery">
                          <SurgeryTabNew
                            ref={surgeryRef}
                            onModificationChange={onModificationChange}
                            session={session}
                            isLoading={isLoading}
                          />
                        </Tabs.Panel>

                        {/* Onglet Orthodontie */}
                         <Tabs.Panel value="orthodontic">
                          <OrthodonticsTabNew
                            ref={orthodonticRef}
                            onModificationChange={onModificationChange}
                            session={session}
                            isLoading={isLoading}
                          />
                        </Tabs.Panel>
       </Tabs>
         {/* -----------------EndPart-2---------------- */}
   </>)
  });
  EstimatesTabs.displayName = 'EstimatesTabs';

  export default EstimatesTabs;