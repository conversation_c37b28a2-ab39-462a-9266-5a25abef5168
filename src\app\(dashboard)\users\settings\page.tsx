'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Stack,
  Switch,
  Select,
  Divider,

  Grid,
  Paper,
  LoadingOverlay,
  SegmentedControl,
  NumberInput,
  ColorSwatch,
  SimpleGrid,
  ThemeIcon,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconAlertCircle,
  IconCheck,

  IconLanguage,
  IconClock,
  IconCalendar,

  IconArrowLeft,

  IconShield,
  IconDeviceFloppy,
  IconUserCircle,
  IconSettings,
  IconBellRinging,
  IconEye,

} from '@tabler/icons-react';

interface UserSettings {
  theme: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  appointmentReminders: boolean;
  marketingEmails: boolean;
  language: string;
  timeFormat: string;
  dateFormat: string;
  calendarView: string;
  calendarStartHour: number;
  calendarEndHour: number;
  privacySettings: {
    showProfileToOtherPatients: boolean;
    allowDoctorsToSeeHistory: boolean;
    shareAnonymizedDataForResearch: boolean;
  };
  accessibilitySettings: {
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
  };
}

export default function PatientSettingsPage() {
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const router = useRouter();

  const form = useForm({
    initialValues: {
      theme: 'light',
      emailNotifications: true,
      smsNotifications: true,
      appointmentReminders: true,
      marketingEmails: false,
      language: 'en',
      timeFormat: '12h',
      dateFormat: 'MM/DD/YYYY',
      calendarView: 'month',
      calendarStartHour: 8,
      calendarEndHour: 18,
      privacySettings: {
        showProfileToOtherPatients: false,
        allowDoctorsToSeeHistory: true,
        shareAnonymizedDataForResearch: false,
      },
      accessibilitySettings: {
        highContrast: false,
        largeText: false,
        screenReader: false,
      },
    },
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);

        // In a real app, you would fetch user settings from an API
        // For now, we'll use mock data
        const mockSettings: UserSettings = {
          theme: 'light',
          emailNotifications: true,
          smsNotifications: true,
          appointmentReminders: true,
          marketingEmails: false,
          language: 'en',
          timeFormat: '12h',
          dateFormat: 'MM/DD/YYYY',
          calendarView: 'month',
          calendarStartHour: 8,
          calendarEndHour: 18,
          privacySettings: {
            showProfileToOtherPatients: false,
            allowDoctorsToSeeHistory: true,
            shareAnonymizedDataForResearch: false,
          },
          accessibilitySettings: {
            highContrast: false,
            largeText: false,
            screenReader: false,
          },
        };

        // Set form values
        form.setValues({
          theme: mockSettings.theme,
          emailNotifications: mockSettings.emailNotifications,
          smsNotifications: mockSettings.smsNotifications,
          appointmentReminders: mockSettings.appointmentReminders,
          marketingEmails: mockSettings.marketingEmails,
          language: mockSettings.language,
          timeFormat: mockSettings.timeFormat,
          dateFormat: mockSettings.dateFormat,
          calendarView: mockSettings.calendarView,
          calendarStartHour: mockSettings.calendarStartHour,
          calendarEndHour: mockSettings.calendarEndHour,
          privacySettings: mockSettings.privacySettings,
          accessibilitySettings: mockSettings.accessibilitySettings,
        });
      } catch (error) {
        console.error('Error fetching settings:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to load settings. Please try again later.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [form]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSubmit = async (_values: typeof form.values) => {
    try {
      setSubmitting(true);

      // In a real app, you would call an API to update the settings
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));

      notifications.show({
        title: 'Success',
        message: 'Settings updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      console.error('Error updating settings:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update settings. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Container size="lg" py="xl">
      <Group mb="xl">
        <Button
          leftSection={<IconArrowLeft size={16} />}
          variant="subtle"
          onClick={() => router.push('/dashboard')}
        >
          Back to Dashboard
        </Button>
        <Title>Patient Settings</Title>
      </Group>

      <Paper withBorder p="md" radius="md" pos="relative">
        <LoadingOverlay visible={loading} overlayProps={{ radius: "sm", blur: 2 }} />

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Grid gutter="xl">
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="blue">
                    <IconUserCircle size={20} />
                  </ThemeIcon>
                  <Title order={3}>Appearance</Title>
                </Group>

                <Stack gap="md">
                  <div>
                    <Text fw={500} mb="xs">Theme</Text>
                    <SegmentedControl
                      fullWidth
                      data={[
                        { label: 'Light', value: 'light' },
                        { label: 'Dark', value: 'dark' },
                        { label: 'System', value: 'system' },
                      ]}
                      {...form.getInputProps('theme')}
                    />
                  </div>

                  <Divider />

                  <div>
                    <Text fw={500} mb="xs">Color Scheme</Text>
                    <SimpleGrid cols={5}>
                      {['blue', 'green', 'red', 'orange', 'violet'].map((color) => (
                        <div key={color} style={{ textAlign: 'center' }}>
                          <ColorSwatch
                            color={color}
                            size={30}
                            style={{ margin: '0 auto', cursor: 'pointer' }}
                          />
                          <Text size="xs" mt={5} tt="capitalize">{color}</Text>
                        </div>
                      ))}
                    </SimpleGrid>
                  </div>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="green">
                    <IconBellRinging size={20} />
                  </ThemeIcon>
                  <Title order={3}>Notifications</Title>
                </Group>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Email Notifications</Text>
                      <Text size="sm" c="dimmed">
                        Receive notifications via email
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.emailNotifications}
                      onChange={(event) => form.setFieldValue('emailNotifications', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>SMS Notifications</Text>
                      <Text size="sm" c="dimmed">
                        Receive notifications via text message
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.smsNotifications}
                      onChange={(event) => form.setFieldValue('smsNotifications', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Appointment Reminders</Text>
                      <Text size="sm" c="dimmed">
                        Receive reminders for upcoming appointments
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.appointmentReminders}
                      onChange={(event) => form.setFieldValue('appointmentReminders', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Marketing Emails</Text>
                      <Text size="sm" c="dimmed">
                        Receive promotional emails and newsletters
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.marketingEmails}
                      onChange={(event) => form.setFieldValue('marketingEmails', event.currentTarget.checked)}
                    />
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="orange">
                    <IconSettings size={20} />
                  </ThemeIcon>
                  <Title order={3}>Preferences</Title>
                </Group>

                <Stack gap="md">
                  <Select
                    label="Language"
                    placeholder="Select language"
                    data={[
                      { value: 'en', label: 'English' },
                      { value: 'es', label: 'Spanish' },
                      { value: 'fr', label: 'French' },
                      { value: 'de', label: 'German' },
                      { value: 'ar', label: 'Arabic' },
                    ]}
                    leftSection={<IconLanguage size={16} />}
                    {...form.getInputProps('language')}
                  />

                  <Select
                    label="Time Format"
                    placeholder="Select time format"
                    data={[
                      { value: '12h', label: '12-hour (1:30 PM)' },
                      { value: '24h', label: '24-hour (13:30)' },
                    ]}
                    leftSection={<IconClock size={16} />}
                    {...form.getInputProps('timeFormat')}
                  />

                  <Select
                    label="Date Format"
                    placeholder="Select date format"
                    data={[
                      { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
                      { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
                      { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
                    ]}
                    leftSection={<IconCalendar size={16} />}
                    {...form.getInputProps('dateFormat')}
                  />

                  <Divider />

                  <Select
                    label="Default Calendar View"
                    placeholder="Select calendar view"
                    data={[
                      { value: 'day', label: 'Day' },
                      { value: 'week', label: 'Week' },
                      { value: 'month', label: 'Month' },
                      { value: 'agenda', label: 'Agenda' },
                    ]}
                    {...form.getInputProps('calendarView')}
                  />

                  <Grid>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Calendar Start Hour"
                        placeholder="Start hour"
                        min={0}
                        max={23}
                        {...form.getInputProps('calendarStartHour')}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <NumberInput
                        label="Calendar End Hour"
                        placeholder="End hour"
                        min={1}
                        max={24}
                        {...form.getInputProps('calendarEndHour')}
                      />
                    </Grid.Col>
                  </Grid>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="red">
                    <IconShield size={20} />
                  </ThemeIcon>
                  <Title order={3}>Privacy</Title>
                </Group>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Show Profile to Other Patients</Text>
                      <Text size="sm" c="dimmed">
                        Allow other patients to see your profile
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.privacySettings.showProfileToOtherPatients}
                      onChange={(event) => form.setFieldValue('privacySettings.showProfileToOtherPatients', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Allow Doctors to See History</Text>
                      <Text size="sm" c="dimmed">
                        Allow doctors to view your medical history
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.privacySettings.allowDoctorsToSeeHistory}
                      onChange={(event) => form.setFieldValue('privacySettings.allowDoctorsToSeeHistory', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Share Anonymized Data for Research</Text>
                      <Text size="sm" c="dimmed">
                        Allow anonymized data to be used for medical research
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.privacySettings.shareAnonymizedDataForResearch}
                      onChange={(event) => form.setFieldValue('privacySettings.shareAnonymizedDataForResearch', event.currentTarget.checked)}
                    />
                  </Group>
                </Stack>
              </Card>

              <Card withBorder p="md" radius="md" mb="md">
                <Group mb="md">
                  <ThemeIcon size={32} radius="md" color="violet">
                    <IconEye size={20} />
                  </ThemeIcon>
                  <Title order={3}>Accessibility</Title>
                </Group>

                <Stack gap="md">
                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>High Contrast Mode</Text>
                      <Text size="sm" c="dimmed">
                        Increase contrast for better visibility
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.accessibilitySettings.highContrast}
                      onChange={(event) => form.setFieldValue('accessibilitySettings.highContrast', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Large Text</Text>
                      <Text size="sm" c="dimmed">
                        Increase text size throughout the application
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.accessibilitySettings.largeText}
                      onChange={(event) => form.setFieldValue('accessibilitySettings.largeText', event.currentTarget.checked)}
                    />
                  </Group>

                  <Divider />

                  <Group justify="space-between">
                    <div>
                      <Text fw={500}>Screen Reader Support</Text>
                      <Text size="sm" c="dimmed">
                        Optimize for screen readers
                      </Text>
                    </div>
                    <Switch
                      checked={form.values.accessibilitySettings.screenReader}
                      onChange={(event) => form.setFieldValue('accessibilitySettings.screenReader', event.currentTarget.checked)}
                    />
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          <Group justify="flex-end" mt="xl">
            <Button variant="outline" onClick={() => form.reset()}>
              Reset
            </Button>
            <Button type="submit" loading={submitting} leftSection={<IconDeviceFloppy size={16} />}>
              Save Settings
            </Button>
          </Group>
        </form>
      </Paper>
    </Container>
  );
}
