import React from 'react'

const test = () => {
  return (
  <div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon md-font-icon="mdi-test-tube" md-font-set="mdi" class="md-font mdi mdi-test-tube" role="img" aria-label="mdi-test-tube"></md-icon>
        </div>
        <h2 translate-once="exam_setup">Comptes rendus</h2>
        <span flex="" class="flex"></span>
        <button class="md-button md-ink-ripple ng-hide" type="button" ng-transclude="" aria-label="new template" ng-hide="vm.modelsForm" ng-click="vm.examForm(false, $event)" aria-hidden="true" style="">
            <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
            <span translate-once="exam_setup_new_template" class="ng-scope">Nouveau</span>
        </button>
    </div>
</md-toolbar>

<md-content class="mn-module-body exam-setup-layout layout-fill ng-scope _md layout-column flex" layout-fill="" flex="" layout="column">
    <md-tabs flex="" md-border-bottom="" cg-busy="vm.promise" class="ng-isolate-scope flex"><md-tabs-wrapper> <md-tab-data>
        <md-tab md-on-select="vm.modelsTab(true)" class="ng-scope ng-isolate-scope">
            
            
        </md-tab>

        <md-tab md-on-select="vm.modelsTab(false)" class="ng-scope ng-isolate-scope">
            
            
        </md-tab>
    </md-tab-data> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <!-- ngIf: $mdTabsCtrl.shouldPaginate --> <md-tabs-canvas tabindex="0" ng-focus="$mdTabsCtrl.redirectFocus()" ng-class="{ 'md-paginated': $mdTabsCtrl.shouldPaginate, 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" ng-keydown="$mdTabsCtrl.keydown($event)"> <md-pagination-wrapper ng-class="{ 'md-center-tabs': $mdTabsCtrl.shouldCenterTabs }" md-tab-scroll="$mdTabsCtrl.scroll($event)" role="tablist" aria-label="Use the left and right arrow keys to navigate between tabs" style="transform: translate(0px, 0px);"><!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="0" class="md-tab  md-active" ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-664" md-tab-id="664" aria-selected="true" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-664">
                <span translate-once="exam_setup_models" class="ng-scope">Gestion des modèles</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-tab-item tabindex="-1" class="md-tab " ng-repeat="tab in $mdTabsCtrl.tabs" role="tab" id="tab-item-665" md-tab-id="665" aria-selected="false" aria-disabled="false" ng-click="$mdTabsCtrl.select(tab.getIndex())" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-class="{ 'md-active':    tab.isActive(), 'md-focused':   tab.hasFocus(), 'md-disabled':  tab.scope.disabled }" ng-disabled="tab.scope.disabled" md-swipe-left="$mdTabsCtrl.nextPage()" md-swipe-right="$mdTabsCtrl.previousPage()" md-tabs-template="::tab.label" md-scope="::tab.parent" aria-controls="tab-content-665">
                <span translate-once="exam_setup_list" class="ng-scope">Liste</span>
            </md-tab-item><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> <md-ink-bar style="left: 0px; right: 48px;"></md-ink-bar> </md-pagination-wrapper> <md-tabs-dummy-wrapper aria-hidden="true" class="md-visually-hidden md-dummy-wrapper"> <!-- ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="exam_setup_models" class="ng-scope">Gestion des modèles</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --><md-dummy-tab class="md-tab ng-scope ng-isolate-scope" tabindex="-1" ng-focus="$mdTabsCtrl.hasFocus = true" ng-blur="$mdTabsCtrl.hasFocus = false" ng-repeat="tab in $mdTabsCtrl.tabs" md-tabs-template="::tab.label" md-scope="::tab.parent">
                <span translate-once="exam_setup_list" class="ng-scope">Liste</span>
            </md-dummy-tab><!-- end ngRepeat: tab in $mdTabsCtrl.tabs --> </md-tabs-dummy-wrapper> </md-tabs-canvas> </md-tabs-wrapper> <md-tabs-content-wrapper ng-show="$mdTabsCtrl.hasContent &amp;&amp; $mdTabsCtrl.selectedIndex >= 0" class="_md" aria-hidden="false"> <!-- ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-664" class="_md ng-scope md-active" role="tabpanel" aria-labelledby="tab-item-664" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope">
                <md-content class="md-padding layout-column flex ng-scope _md" mn-bind-html="parameters/views/exam-models-setup.html"><mn-exam-links class="mn-button-group mn-primary flex-nogrow layout-row ng-isolate-scope" template-change="vm.templateSelected" lock="vm.lock"><!-- ngIf: vm.hasLock --><button class="md-raised mn-button-icon-only md-button ng-scope md-ink-ripple" type="button" ng-transclude="" ng-click="vm.lockChange()" aria-label="expand compress" ng-if="vm.hasLock">
    <md-icon md-font-icon="mdi-arrow-collapse" md-font-set="mdi" class="ng-scope md-font mdi mdi-arrow-collapse" role="img" aria-hidden="true"></md-icon>
</button><!-- end ngIf: vm.hasLock -->

<!-- ngRepeat: item in vm.favoritesTemplates track by item.id -->

<!-- ngIf: vm.templates.length > 0 -->
</mn-exam-links>

<div flex="" layout="row" class="layout-row flex">
    <md-sidenav class="mn-module-side-nav md-locked-open mn-top-padding md-sidenav-left flex-nogrow layout-column md-closed ng-isolate-scope _md" md-is-locked-open="vm.lock" tabindex="-1">
        <md-toolbar class="mn-module-header md-primary _md _md-toolbar-transitions">
            <div class="md-toolbar-tools">
                <div class="mn-module-icon">
                    <md-icon md-font-icon="mdi-format-list-bulleted" md-font-set="mdi" class="md-font mdi mdi-format-list-bulleted" role="img" aria-label="mdi-format-list-bulleted"></md-icon>
                </div>
                <h2 translate-once="models">Modèles</h2>
                <span flex="" class="flex"></span>
                <mn-model-search init-items="vm.models" items="vm.items" search-by="title" class="ng-isolate-scope"><button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.showInput()" aria-label="cancel" tabindex="-1">   <md-icon md-font-icon="mdi-magnify" md-font-set="mdi" class="ng-scope md-font mdi mdi-magnify" role="img" aria-hidden="true"></md-icon></button><div class="search-element ng-hide" ng-show="vm.shown" aria-hidden="true">   <input type="search" translate-once-placeholder="search" ng-model="vm.key" ng-model-options="{debounce : 400}" mn-auto-focus="vm.shown" ng-change="vm.handleSearch(vm.key)" class="ng-pristine ng-untouched ng-valid ng-empty" autocomplete="off" aria-invalid="false" placeholder="Rechercher">   <button class="md-icon-button cancel-button md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.cancel()" aria-label="cancel" tabindex="-1">       <md-icon md-font-icon="mdi-close" md-font-set="mdi" class="ng-scope md-font mdi mdi-close" role="img" aria-hidden="true"></md-icon>   </button></div></mn-model-search>
            </div>
        </md-toolbar>
        <md-content flex="" layout="column" cg-busy="vm.promise" class="_md layout-column flex">
            <!-- ngIf: !vm.models || vm.models.length == 0 --><div class="empty-content layout-row layout-align-start-center ng-scope" ng-if="!vm.models || vm.models.length == 0">
                <md-icon md-font-icon="mdi-alert-circle-outline" md-font-set="mdi" aria-label="warning" class="md-font mdi mdi-alert-circle-outline" role="img"></md-icon>
                <span translate-once="no_element_to_show">Aucun élément trouvé.</span>
            </div><!-- end ngIf: !vm.models || vm.models.length == 0 -->
            <md-list flex="" role="list" class="flex">
                <!-- ngRepeat: model in vm.items track by model.id -->
            </md-list>
        <div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div></div></md-content>
    </md-sidenav>

    <!-- uiView: --><div class="mn-top-padding mn-exam-layout ng-scope layout-column flex" flex="" layout="column" ui-view="">
        <div class="empty-content flex-nogrow ng-scope">
            <md-icon md-font-icon="mdi-alert-circle-outline" md-font-set="mdi" aria-label="warning" class="md-font mdi mdi-alert-circle-outline" role="img"></md-icon>
            <span translate-once="exam_setup_init_msg">Aucun modèle sélectionné.</span>
        </div>
    </div>
</div></md-content>
            </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --><!-- ngIf: tab.hasContent --><md-tab-content id="tab-content-665" class="_md ng-scope md-right" role="tabpanel" aria-labelledby="tab-item-665" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content><!-- end ngIf: tab.hasContent --><!-- end ngRepeat: (index, tab) in $mdTabsCtrl.tabs --> </md-tabs-content-wrapper><div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""></div><div class="cg-busy ng-scope ng-hide" ng-show="$cgBusyIsActive()" aria-hidden="true" style=""><div class="cg-loader-spinner" style="position: absolute; inset: 0px;">
    <div></div>
    <div></div>
   
</div></div></md-tabs>
</md-content></div>
<md-tab-content id="tab-content-665" class="_md ng-scope md-active" role="tabpanel" aria-labelledby="tab-item-665" md-swipe-left="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(1)" md-swipe-right="$mdTabsCtrl.swipeContent &amp;&amp; $mdTabsCtrl.incrementIndex(-1)" ng-if="tab.hasContent" ng-repeat="(index, tab) in $mdTabsCtrl.tabs" ng-class="{ 'md-no-transition': $mdTabsCtrl.lastSelectedIndex == null, 'md-active':        tab.isActive(), 'md-left':          tab.isLeft(), 'md-right':         tab.isRight(), 'md-no-scroll':     $mdTabsCtrl.dynamicHeight }" style=""> <!-- ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --><div md-tabs-template="::tab.template" md-connected-if="tab.isActive()" md-scope="::tab.parent" ng-if="$mdTabsCtrl.enableDisconnect || tab.shouldRender()" class="ng-scope ng-isolate-scope" style="">
                <md-content class="md-padding layout-column flex ng-scope _md" mn-bind-html="parameters/views/exam-list-setup.html"><div class="table-container flex">
    <md-table-container>
        <table md-table="" md-progress="vm.promise" class="mn-striped md-table ng-isolate-scope">
            <thead md-head="" class="md-head ng-isolate-scope">
            <tr md-row="" class="md-row">
                <th md-column="" class="md-column ng-isolate-scope">
                    <span translate-once="exam_setup_short_name">Code</span>
                </th>
                <th md-column="" class="md-column ng-isolate-scope">
                    <span translate-once="exam_setup_full_name">Désignation</span>
                </th>
                <!--<th md-column>-->
                <!--<span translate-once="exam_setup_type"></span>-->
                <!--</th>-->
                <th md-column="" class="md-column ng-isolate-scope">
                    <span translate-once="exam_setup_is_favorite">Préféré</span>
                </th>
                <th md-column="" class="md-column ng-isolate-scope">
                    <span translate-once="exam_setup_is_disabled">Désactivé</span>
                </th>
                <th md-column="" class="actions-column-2 md-column ng-isolate-scope"></th>
            </tr>
            </thead>
            <thead class="md-table-progress ng-isolate-scope" md-table-progress=""><tr>
  <th colspan="5">
    <md-progress-linear ng-show="deferred()" md-mode="indeterminate" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" class="ng-hide"><div class="md-container md-mode-indeterminate"><div class="md-dashed"></div><div class="md-bar md-bar1"></div><div class="md-bar md-bar2"></div></div></md-progress-linear>
  </th>
</tr></thead><!-- ngIf: !vm.examTemplates || vm.examTemplates.length == 0 --><tbody md-body="" ng-if="!vm.examTemplates || vm.examTemplates.length == 0" class="md-body ng-scope">
            <tr md-row="" class="md-row">
                <td md-cell="" colspan="6" translate-once="no_element_to_show" class="md-cell">Aucun élément trouvé.</td>
                <td md-cell="" ng-hide="true" class="md-cell ng-hide" aria-hidden="true"></td>
            </tr>
            </tbody><!-- end ngIf: !vm.examTemplates || vm.examTemplates.length == 0 -->
            <!-- ngIf: vm.examTemplates.length > 0 -->
        </table>
    </md-table-container>
</div>
</md-content>
            </div><!-- end ngIf: $mdTabsCtrl.enableDisconnect || tab.shouldRender() --> </md-tab-content>
  )
}

export default test
