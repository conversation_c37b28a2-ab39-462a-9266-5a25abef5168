'use client';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  ActionIcon,
  Card,
  Stack,
  Text,
  Badge,
  Modal,
  FileInput,
  Image,
  SimpleGrid,
  Pagination,
  Box,
  Center,
  Textarea,
  Divider,
  NumberInput,
  
  Table,
  ScrollArea,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSearch,
  IconPhoto,
  IconUpload,
  IconEye,
  IconEdit,
  IconTrash,
  IconFilter,
  IconTag,
  IconCalendar,
  IconUser,
  IconPackage,
 
  IconClipboardList,
  IconTrendingUp,
 
  IconAlertTriangle,
  IconCheck,
  IconX,
  IconDownload,
  IconShare,
} from '@tabler/icons-react';

interface StockImageItem {
  id: string;
  productName: string;
  productCode: string;
  imageUrl: string;
  thumbnail: string;
  category: string;
  stockLevel: number;
  minStock: number;
  maxStock: number;
  location: string;
  lastUpdated: Date;
  uploadedBy: string;
  description: string;
  tags: string[];
  status: 'in-stock' | 'low-stock' | 'out-of-stock' | 'overstocked';
}

export default function StockImagesGallery() {
  const [stockImages, setStockImages] = useState<StockImageItem[]>([
    {
      id: '1',
      productName: 'Paracétamol 500mg',
      productCode: 'PAR500',
      imageUrl: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
      category: 'Analgésiques',
      stockLevel: 150,
      minStock: 50,
      maxStock: 500,
      location: 'Étagère A-1',
      lastUpdated: new Date('2024-01-15'),
      uploadedBy: 'Dr. Martin',
      description: 'Médicament contre la douleur et la fièvre',
      tags: ['analgésique', 'fièvre', 'douleur'],
      status: 'in-stock',
    },
    {
      id: '2',
      productName: 'Amoxicilline 250mg',
      productCode: 'AMX250',
      imageUrl: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop',
      category: 'Antibiotiques',
      stockLevel: 25,
      minStock: 30,
      maxStock: 200,
      location: 'Étagère B-2',
      lastUpdated: new Date('2024-01-12'),
      uploadedBy: 'Dr. Dubois',
      description: 'Antibiotique à large spectre',
      tags: ['antibiotique', 'infection'],
      status: 'low-stock',
    },
    {
      id: '3',
      productName: 'Seringues 5ml',
      productCode: 'SER5ML',
      imageUrl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=300&h=200&fit=crop',
      category: 'Matériel médical',
      stockLevel: 0,
      minStock: 100,
      maxStock: 1000,
      location: 'Armoire C-1',
      lastUpdated: new Date('2024-01-10'),
      uploadedBy: 'Infirmière Claire',
      description: 'Seringues stériles à usage unique',
      tags: ['seringue', 'injection', 'stérile'],
      status: 'out-of-stock',
    },
    {
      id: '4',
      productName: 'Masques chirurgicaux',
      productCode: 'MSK001',
      imageUrl: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=800&h=600&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=300&h=200&fit=crop',
      category: 'Protection',
      stockLevel: 2500,
      minStock: 500,
      maxStock: 2000,
      location: 'Réserve D-1',
      lastUpdated: new Date('2024-01-08'),
      uploadedBy: 'Gestionnaire Stock',
      description: 'Masques de protection chirurgicaux',
      tags: ['masque', 'protection', 'chirurgie'],
      status: 'overstocked',
    },
  ]);

  const [selectedImage, setSelectedImage] = useState<StockImageItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  const [opened, { open, close }] = useDisclosure(false);
  const [viewerOpened, { open: openViewer, close: closeViewer }] = useDisclosure(false);
  const [uploadOpened, { open: openUpload, close: closeUpload }] = useDisclosure(false);

  const form = useForm({
    initialValues: {
      productName: '',
      productCode: '',
      category: '',
      stockLevel: 0,
      minStock: 0,
      maxStock: 0,
      location: '',
      description: '',
      tags: '',
      file: null as File | null,
    },
  });

  const categories = [
    'Analgésiques',
    'Antibiotiques',
    'Matériel médical',
    'Protection',
    'Vaccins',
    'Cardiovasculaire',
    'Dermatologie',
  ];

  const statusOptions = [
    { value: 'in-stock', label: 'En stock', color: 'green' },
    { value: 'low-stock', label: 'Stock faible', color: 'orange' },
    { value: 'out-of-stock', label: 'Rupture', color: 'red' },
    { value: 'overstocked', label: 'Surstock', color: 'blue' },
  ];

  const itemsPerPage = 12;

  const filteredImages = stockImages.filter((item) => {
    const matchesSearch = item.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.productCode.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || item.category === selectedCategory;
    const matchesStatus = !selectedStatus || item.status === selectedStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const totalPages = Math.ceil(filteredImages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedImages = filteredImages.slice(startIndex, startIndex + itemsPerPage);

  const getStatusColor = (status: string) => {
    const statusMap = {
      'in-stock': 'green',
      'low-stock': 'orange',
      'out-of-stock': 'red',
      'overstocked': 'blue',
    };
    return statusMap[status as keyof typeof statusMap] || 'gray';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in-stock': return <IconCheck size={16} />;
      case 'low-stock': return <IconAlertTriangle size={16} />;
      case 'out-of-stock': return <IconX size={16} />;
      case 'overstocked': return <IconTrendingUp size={16} />;
      default: return <IconPackage size={16} />;
    }
  };

  const handleImageUpload = async (values: typeof form.values) => {
    if (!values.file) return;

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      const status = values.stockLevel <= values.minStock ? 'low-stock' : 
                    values.stockLevel === 0 ? 'out-of-stock' :
                    values.stockLevel > values.maxStock ? 'overstocked' : 'in-stock';

      const newItem: StockImageItem = {
        id: Date.now().toString(),
        productName: values.productName,
        productCode: values.productCode,
        imageUrl: URL.createObjectURL(values.file),
        thumbnail: URL.createObjectURL(values.file),
        category: values.category,
        stockLevel: values.stockLevel,
        minStock: values.minStock,
        maxStock: values.maxStock,
        location: values.location,
        lastUpdated: new Date(),
        uploadedBy: 'Utilisateur actuel',
        description: values.description,
        tags: values.tags.split(',').map(tag => tag.trim()),
        status,
      };

      setStockImages(prev => [newItem, ...prev]);
      form.reset();
      closeUpload();

      notifications.show({
        title: 'Succès',
        message: 'Produit ajouté avec succès',
        color: 'green',
      });
    } catch {
  notifications.show({
    title: 'Erreur',
    message: 'Erreur lors de l\'ajout du document',
    color: 'red',
  });
    }
  };

  return (
    <Paper shadow="xs" p="md" withBorder mb={60}>
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Group>
          <IconPackage size={24} className="text-blue-600" />
          <Title order={2} className="text-gray-800">
            Galerie Stock avec Images
          </Title>
        </Group>
        <Group>
          <Button
            leftSection={<IconUpload size={16} />}
            onClick={openUpload}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Ajouter Produit
          </Button>
          <Button
            variant="outline"
            leftSection={<IconFilter size={16} />}
            onClick={open}
          >
            Filtres
          </Button>
        </Group>
      </Group>

      {/* Search and Filters */}
      <Grid mb="md">
        <Grid.Col span={4}>
          <TextInput
            placeholder="Rechercher produits..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.currentTarget.value)}
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Select
            placeholder="Catégorie"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Select
            placeholder="Statut stock"
            data={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={2}>
          <Group>
            <ActionIcon
              variant={viewMode === 'grid' ? 'filled' : 'outline'}
              onClick={() => setViewMode('grid')}
            >
              <IconPhoto size={16} />
            </ActionIcon>
            <ActionIcon
              variant={viewMode === 'table' ? 'filled' : 'outline'}
              onClick={() => setViewMode('table')}
            >
              <IconClipboardList size={16} />
            </ActionIcon>
          </Group>
        </Grid.Col>
      </Grid>

      {/* Content Display */}
      {paginatedImages.length === 0 ? (
        <Center h={300}>
          <Stack align="center">
            <IconPackage size={48} className="text-gray-400" />
            <Text c="dimmed">Aucun produit trouvé</Text>
          </Stack>
        </Center>
      ) : viewMode === 'grid' ? (
        <SimpleGrid
          cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
          spacing="md"
          mb="xl"
        >
          {paginatedImages.map((item) => (
            <Card key={item.id} withBorder shadow="sm" className="hover:shadow-md transition-shadow">
              <Card.Section>
                <Box pos="relative">
                  <Image
                    src={item.thumbnail}
                    height={200}
                    alt={item.productName}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedImage(item);
                      openViewer();
                    }}
                  />
                  <Badge
                    pos="absolute"
                    top={8}
                    right={8}
                    color={getStatusColor(item.status)}
                    leftSection={getStatusIcon(item.status)}
                  >
                    {statusOptions.find(s => s.value === item.status)?.label}
                  </Badge>
                </Box>
              </Card.Section>

              <Stack gap="xs" mt="md">
                <Group justify="space-between">
                  <Text fw={500} size="sm" truncate>
                    {item.productName}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {item.productCode}
                  </Text>
                </Group>

                <Text size="xs" c="dimmed" lineClamp={2}>
                  {item.description}
                </Text>

                <Group justify="space-between">
                  <Badge size="xs" color="blue">
                    {item.category}
                  </Badge>
                  <Text size="xs" fw={500}>
                    Stock: {item.stockLevel}
                  </Text>
                </Group>

                <Group justify="space-between" mt="xs">
                  <Text size="xs" c="dimmed">
                    {item.location}
                  </Text>
                  <Group gap="xs">
                    <ActionIcon size="sm" variant="subtle" onClick={() => {
                      setSelectedImage(item);
                      openViewer();
                    }}>
                      <IconEye size={14} />
                    </ActionIcon>
                    <ActionIcon size="sm" variant="subtle">
                      <IconEdit size={14} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>
      ) : (
        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Image</Table.Th>
                <Table.Th>Produit</Table.Th>
                <Table.Th>Code</Table.Th>
                <Table.Th>Catégorie</Table.Th>
                <Table.Th>Stock</Table.Th>
                <Table.Th>Statut</Table.Th>
                <Table.Th>Emplacement</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedImages.map((item) => (
                <Table.Tr key={item.id}>
                  <Table.Td>
                    <Image
                      src={item.thumbnail}
                      width={60}
                      height={40}
                      alt={item.productName}
                      className="cursor-pointer rounded"
                      onClick={() => {
                        setSelectedImage(item);
                        openViewer();
                      }}
                    />
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{item.productName}</Text>
                    <Text size="xs" c="dimmed">{item.description}</Text>
                  </Table.Td>
                  <Table.Td>{item.productCode}</Table.Td>
                  <Table.Td>
                    <Badge size="sm" color="blue">{item.category}</Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{item.stockLevel}</Text>
                    <Text size="xs" c="dimmed">Min: {item.minStock}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={getStatusColor(item.status)}
                      leftSection={getStatusIcon(item.status)}
                    >
                      {statusOptions.find(s => s.value === item.status)?.label}
                    </Badge>
                  </Table.Td>
                  <Table.Td>{item.location}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon size="sm" variant="subtle" onClick={() => {
                        setSelectedImage(item);
                        openViewer();
                      }}>
                        <IconEye size={14} />
                      </ActionIcon>
                      <ActionIcon size="sm" variant="subtle">
                        <IconEdit size={14} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Group justify="center" mt="xl">
          <Pagination
            total={totalPages}
            value={currentPage}
            onChange={setCurrentPage}
          />
        </Group>
      )}

      {/* Upload Modal */}
      <Modal opened={uploadOpened} onClose={closeUpload} title="Ajouter un produit avec image" size="lg">
        <form onSubmit={form.onSubmit(handleImageUpload)}>
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Nom du produit"
                  placeholder="Ex: Paracétamol 500mg"
                  {...form.getInputProps('productName')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Code produit"
                  placeholder="Ex: PAR500"
                  {...form.getInputProps('productCode')}
                  required
                />
              </Grid.Col>
            </Grid>

            <FileInput
              label="Image du produit"
              placeholder="Sélectionner une image"
              accept="image/*"
              leftSection={<IconUpload size={16} />}
              {...form.getInputProps('file')}
              required
            />

            <Select
              label="Catégorie"
              placeholder="Sélectionner une catégorie"
              data={categories}
              {...form.getInputProps('category')}
              required
            />

            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label="Stock actuel"
                  placeholder="0"
                  min={0}
                  {...form.getInputProps('stockLevel')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Stock minimum"
                  placeholder="0"
                  min={0}
                  {...form.getInputProps('minStock')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label="Stock maximum"
                  placeholder="0"
                  min={0}
                  {...form.getInputProps('maxStock')}
                  required
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label="Emplacement"
              placeholder="Ex: Étagère A-1"
              {...form.getInputProps('location')}
              required
            />

            <Textarea
              label="Description"
              placeholder="Description du produit"
              rows={3}
              {...form.getInputProps('description')}
            />

            <TextInput
              label="Tags"
              placeholder="Tags séparés par des virgules"
              {...form.getInputProps('tags')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" onClick={closeUpload}>
                Annuler
              </Button>
              <Button type="submit" leftSection={<IconUpload size={16} />}>
                Ajouter
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Image Viewer Modal */}
      <Modal
        opened={viewerOpened}
        onClose={closeViewer}
        title={selectedImage?.productName}
        size="xl"
        centered
      >
        {selectedImage && (
          <Stack>
            <Image
              src={selectedImage.imageUrl}
              alt={selectedImage.productName}
              fit="contain"
              h={400}
            />

            <Grid>
              <Grid.Col span={8}>
                <Stack gap="xs">
                  <Group>
                    <Text fw={500} size="lg">{selectedImage.productName}</Text>
                    <Badge color="blue">{selectedImage.productCode}</Badge>
                  </Group>

                  <Text size="sm" c="dimmed">{selectedImage.description}</Text>

                  <Group gap="xs">
                    {selectedImage.tags.map((tag, index) => (
                      <Badge key={index} size="sm" variant="light">
                        {tag}
                      </Badge>
                    ))}
                  </Group>

                  <Divider />

                  <Group>
                    <Text size="sm" fw={500}>Stock actuel:</Text>
                    <Text size="sm">{selectedImage.stockLevel}</Text>
                    <Badge
                      color={getStatusColor(selectedImage.status)}
                      leftSection={getStatusIcon(selectedImage.status)}
                    >
                      {statusOptions.find(s => s.value === selectedImage.status)?.label}
                    </Badge>
                  </Group>

                  <Group>
                    <Text size="sm">Min: {selectedImage.minStock}</Text>
                    <Text size="sm">Max: {selectedImage.maxStock}</Text>
                  </Group>
                </Stack>
              </Grid.Col>

              <Grid.Col span={4}>
                <Stack gap="xs">
                  <Group>
                    <IconTag size={16} />
                    <Text size="sm">{selectedImage.category}</Text>
                  </Group>

                  <Group>
                    <IconPackage size={16} />
                    <Text size="sm">{selectedImage.location}</Text>
                  </Group>

                  <Group>
                    <IconUser size={16} />
                    <Text size="sm">{selectedImage.uploadedBy}</Text>
                  </Group>

                  <Group>
                    <IconCalendar size={16} />
                    <Text size="sm">{selectedImage.lastUpdated.toLocaleDateString()}</Text>
                  </Group>
                </Stack>
              </Grid.Col>
            </Grid>

            <Group justify="space-between" mt="md">
              <Group>
                <ActionIcon variant="outline">
                  <IconShare size={16} />
                </ActionIcon>
                <ActionIcon variant="outline">
                  <IconDownload size={16} />
                </ActionIcon>
              </Group>

              <Group>
                <Button variant="outline" leftSection={<IconEdit size={16} />}>
                  Modifier
                </Button>
                <Button variant="outline" color="red" leftSection={<IconTrash size={16} />}>
                  Supprimer
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Filters Modal */}
      <Modal opened={opened} onClose={close} title="Filtres avancés">
        <Stack>
          <Select
            label="Catégorie"
            placeholder="Toutes les catégories"
            data={categories}
            value={selectedCategory}
            onChange={setSelectedCategory}
            clearable
          />

          <Select
            label="Statut du stock"
            placeholder="Tous les statuts"
            data={statusOptions}
            value={selectedStatus}
            onChange={setSelectedStatus}
            clearable
          />

          <DatePickerInput
            label="Date de mise à jour"
            placeholder="Sélectionner une date"
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={close}>
              Fermer
            </Button>
            <Button onClick={close}>
              Appliquer
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Paper>
  );
}
