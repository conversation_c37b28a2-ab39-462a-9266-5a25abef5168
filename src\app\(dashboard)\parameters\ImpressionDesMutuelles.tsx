'use client';

import React, { useState, useEffect } from 'react';
import {
  Group,
  Title,
  Button,
  Paper,
  Stack,
  Flex,
  Text,
  TextInput,
  NumberInput,
  Select,
  Switch,
  ActionIcon,
  Tabs,
  Box,
  ScrollArea,
  ThemeIcon,
  UnstyledButton,
  Loader,
  Center,
  Modal,
  FileInput,
  Image,
  Divider,
  Code,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import Icon from '@mdi/react';
import {
  mdiPrinterSettings,
  mdiPlus,
  mdiFormatListBulleted,
  mdiXml,
  mdiCircleEditOutline,
  mdiDeleteForever,
  mdiDelete,
  mdiContentCopy,
} from '@mdi/js';

// TypeScript interfaces
interface Organization {
  id: number;
  name: string;
}

interface Template {
  id?: number;
  name: string;
  default_font_size: number;
  organization: Organization | null;
  is_default: boolean;
  is_hidden: boolean;
  is_dental: boolean;
  pages: TemplatePage[];
}

interface TemplatePage {
  id?: number;
  image?: string;
  configuration: string;
}

const ImpressionDesMutuelles = () => {
  // State management
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [selectedPage, setSelectedPage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [imageModalOpened, setImageModalOpened] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Form handling
  const form = useForm<Template>({
    initialValues: {
      name: '',
      default_font_size: 12,
      organization: null,
      is_default: false,
      is_hidden: false,
      is_dental: false,
      pages: [{ configuration: '{}' }],
    },
    validate: {
      name: (value) => (value.length < 1 ? 'Name is required' : null),
      organization: (value) => (!value ? 'Organization is required' : null),
    },
  });

  // Mock data - replace with actual API calls
  const mockTemplates: Template[] = [
    {
      id: 1,
      name: 'ATLANTA DENTAL',
      default_font_size: 12,
      organization: { id: 1, name: 'ATLANTA' },
      is_default: false,
      is_hidden: false,
      is_dental: true,
      pages: [{ id: 1, configuration: '{}' }],
    },
    {
      id: 2,
      name: 'AXA',
      default_font_size: 12,
      organization: { id: 2, name: 'AXA' },
      is_default: false,
      is_hidden: false,
      is_dental: false,
      pages: [{ id: 2, configuration: '{}' }],
    },
    // Add more mock templates as needed
  ];

  const mockOrganizations: Organization[] = [
    { id: 1, name: 'AMO' },
    { id: 2, name: 'ATLANTA' },
    { id: 3, name: 'AXA' },
    { id: 4, name: 'CNOPS' },
    { id: 5, name: 'CNSS' },
    // Add more organizations as needed
  ];

  // Effects
  useEffect(() => {
    loadTemplates();
    loadOrganizations();
  }, []);

  useEffect(() => {
    if (selectedTemplate) {
      form.setValues(selectedTemplate);
      setSelectedPage(0);
    }
  }, [selectedTemplate]);

  // API functions (mock implementations)
  const loadTemplates = async () => {
    setLoading(true);
    try {
      // Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setTemplates(mockTemplates);
      if (mockTemplates.length > 0) {
        setSelectedTemplate(mockTemplates[0]);
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to load templates',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadOrganizations = async () => {
    try {
      // Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 300));
      setOrganizations(mockOrganizations);
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to load organizations',
        color: 'red',
      });
    }
  };

  // Event handlers
  const handleNewTemplate = () => {
    const newTemplate: Template = {
      name: '',
      default_font_size: 12,
      organization: null,
      is_default: false,
      is_hidden: false,
      is_dental: false,
      pages: [{ configuration: '{}' }],
    };
    setSelectedTemplate(newTemplate);
    form.setValues(newTemplate);
    setSelectedPage(0);
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
  };

  const handleAddPage = () => {
    if (selectedTemplate) {
      const newPages = [...selectedTemplate.pages, { configuration: '{}' }];
      const updatedTemplate = { ...selectedTemplate, pages: newPages };
      setSelectedTemplate(updatedTemplate);
      form.setFieldValue('pages', newPages);
      setSelectedPage(newPages.length - 1);
    }
  };

  const handleRemovePage = (pageIndex: number) => {
    if (selectedTemplate && selectedTemplate.pages.length > 1) {
      const newPages = selectedTemplate.pages.filter((_, index) => index !== pageIndex);
      const updatedTemplate = { ...selectedTemplate, pages: newPages };
      setSelectedTemplate(updatedTemplate);
      form.setFieldValue('pages', newPages);
      setSelectedPage(Math.max(0, pageIndex - 1));
    }
  };

  const handleImageUpload = (file: File | null, pageIndex: number) => {
    if (file && selectedTemplate) {
      // Convert file to base64 or handle upload
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageData = e.target?.result as string;
        const newPages = [...selectedTemplate.pages];
        newPages[pageIndex] = { ...newPages[pageIndex], image: imageData };
        const updatedTemplate = { ...selectedTemplate, pages: newPages };
        setSelectedTemplate(updatedTemplate);
        form.setFieldValue('pages', newPages);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (values: Template) => {
    setLoading(true);
    try {
      // Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (values.id) {
        // Update existing template
        const updatedTemplates = templates.map(t =>
          t.id === values.id ? { ...values } : t
        );
        setTemplates(updatedTemplates);
        notifications.show({
          title: 'Success',
          message: 'Template updated successfully',
          color: 'green',
        });
      } else {
        // Create new template
        const newTemplate = { ...values, id: Date.now() };
        setTemplates([...templates, newTemplate]);
        setSelectedTemplate(newTemplate);
        notifications.show({
          title: 'Success',
          message: 'Template created successfully',
          color: 'green',
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to save template',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async () => {
    if (selectedTemplate?.id) {
      setLoading(true);
      try {
        // Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 500));

        const updatedTemplates = templates.filter(t => t.id !== selectedTemplate.id);
        setTemplates(updatedTemplates);
        setSelectedTemplate(updatedTemplates.length > 0 ? updatedTemplates[0] : null);

        notifications.show({
          title: 'Success',
          message: 'Template deleted successfully',
          color: 'green',
        });
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to delete template',
          color: 'red',
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDuplicateTemplate = async () => {
    if (selectedTemplate) {
      const duplicatedTemplate: Template = {
        ...selectedTemplate,
        id: undefined,
        name: `${selectedTemplate.name} (Copy)`,
        is_default: false,
      };
      setSelectedTemplate(duplicatedTemplate);
      form.setValues(duplicatedTemplate);
    }
  };

  return (
    <Paper shadow="sm" radius="md" style={{ height: '100vh', overflow: 'hidden' }}>
      {/* Header */}
      <Group p="md" style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
        <ThemeIcon size="lg" variant="light" color="blue">
          <Icon path={mdiPrinterSettings} size={1} />
        </ThemeIcon>
        <Title order={3} flex={1}>
          Configuration des modèles d'impression de feuilles de soins (mutuelles)
        </Title>
        <Button
          leftSection={<Icon path={mdiPlus} size={0.8} />}
          onClick={handleNewTemplate}
          variant="filled"
        >
          Nouvelle template
        </Button>
      </Group>

      {/* Main Content */}
      <Flex style={{ height: 'calc(100vh - 80px)' }}>
        {/* Sidebar */}
        <Paper
          shadow="sm"
          style={{
            width: 300,
            borderRight: '1px solid var(--mantine-color-gray-3)',
            borderRadius: 0,
          }}
        >
          {/* Sidebar Header */}
          <Group p="md" style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
            <ThemeIcon size="md" variant="light" color="blue">
              <Icon path={mdiFormatListBulleted} size={0.8} />
            </ThemeIcon>
            <Text fw={500}>Templates d'impression</Text>
          </Group>

          {/* Templates List */}
          <ScrollArea style={{ height: 'calc(100vh - 160px)' }}>
            {loading ? (
              <Center p="xl">
                <Loader size="sm" />
              </Center>
            ) : (
              <Stack gap={0}>
                {templates.map((template) => (
                  <UnstyledButton
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    p="md"
                    style={{
                      borderBottom: '1px solid var(--mantine-color-gray-2)',
                      backgroundColor:
                        selectedTemplate?.id === template.id
                          ? 'var(--mantine-color-blue-light)'
                          : 'transparent',
                    }}
                  >
                    <Group gap="sm">
                      <ThemeIcon
                        size="sm"
                        variant="light"
                        color={template.is_dental ? 'indigo' : 'orange'}
                        style={{
                          opacity: template.is_hidden ? 0.5 : 1,
                        }}
                      >
                        <Icon path={mdiXml} size={0.6} />
                      </ThemeIcon>
                      <Text
                        size="sm"
                        style={{
                          opacity: template.is_hidden ? 0.5 : 1,
                        }}
                      >
                        {template.name}
                      </Text>
                    </Group>
                  </UnstyledButton>
                ))}
              </Stack>
            )}
          </ScrollArea>
        </Paper>

        {/* Main Form Area */}
        <Box flex={1} p="md">
          {selectedTemplate ? (
            <form onSubmit={form.onSubmit(handleSubmit)}>
              <Stack gap="md">
                {/* Form Header */}
                <Group>
                  <TextInput
                    label="Nom"
                    placeholder="Nom du template"
                    required
                    style={{ flex: 1 }}
                    {...form.getInputProps('name')}
                  />

                  <NumberInput
                    label="Taille de Police"
                    placeholder="12"
                    min={8}
                    max={24}
                    style={{ width: 150 }}
                    {...form.getInputProps('default_font_size')}
                  />

                  <Select
                    label="Organisme"
                    placeholder="Sélectionner un organisme"
                    required
                    style={{ width: 200 }}
                    data={organizations.map(org => ({ value: org.id.toString(), label: org.name }))}
                    value={form.values.organization?.id?.toString() || ''}
                    onChange={(value) => {
                      const org = organizations.find(o => o.id.toString() === value);
                      form.setFieldValue('organization', org || null);
                    }}
                    searchable
                  />

                  <Switch
                    label="Par défaut"
                    {...form.getInputProps('is_default', { type: 'checkbox' })}
                  />

                  <Switch
                    label="Cachée"
                    {...form.getInputProps('is_hidden', { type: 'checkbox' })}
                  />

                  <Switch
                    label="Pour dentaire"
                    {...form.getInputProps('is_dental', { type: 'checkbox' })}
                  />

                  <Box style={{ flex: 1 }} />

                  <Button
                    leftSection={<Icon path={mdiPlus} size={0.8} />}
                    onClick={handleAddPage}
                    variant="filled"
                  >
                    Ajouter une page
                  </Button>
                </Group>

                {/* Page Tabs */}
                <Tabs value={selectedPage.toString()} onTabChange={(value) => setSelectedPage(parseInt(value || '0'))}>
                  <Tabs.List>
                    {selectedTemplate.pages.map((_, index) => (
                      <Tabs.Tab key={index} value={index.toString()}>
                        Page #{index + 1}
                      </Tabs.Tab>
                    ))}
                  </Tabs.List>

                  {selectedTemplate.pages.map((page, index) => (
                    <Tabs.Panel key={index} value={index.toString()}>
                      <Stack gap="md" mt="md">
                        {/* Page Image and Controls */}
                        <Group>
                          <Box
                            style={{
                              width: 100,
                              height: 100,
                              border: '1px solid var(--mantine-color-gray-4)',
                              borderRadius: 4,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer',
                            }}
                            onClick={() => {
                              if (page.image) {
                                setSelectedImage(page.image);
                                setImageModalOpened(true);
                              }
                            }}
                          >
                            {page.image ? (
                              <Image
                                src={page.image}
                                alt="Template page"
                                style={{ maxWidth: '100%', maxHeight: '100%' }}
                              />
                            ) : (
                              <Text size="xs" c="dimmed">
                                No image
                              </Text>
                            )}
                          </Box>

                          <FileInput
                            placeholder="Upload image"
                            accept="image/*"
                            onChange={(file) => handleImageUpload(file, index)}
                          />

                          <Box style={{ flex: 1 }} />

                          {selectedTemplate.pages.length > 1 && (
                            <ActionIcon
                              color="red"
                              variant="filled"
                              onClick={() => handleRemovePage(index)}
                            >
                              <Icon path={mdiDeleteForever} size={0.8} />
                            </ActionIcon>
                          )}
                        </Group>

                        {/* JSON Configuration Editor */}
                        <Box>
                          <Text size="sm" fw={500} mb="xs">
                            Configuration JSON
                          </Text>
                          <Code
                            block
                            style={{
                              minHeight: 200,
                              fontFamily: 'monospace',
                              fontSize: 14,
                            }}
                          >
                            <textarea
                              value={page.configuration}
                              onChange={(e) => {
                                const newPages = [...selectedTemplate.pages];
                                newPages[index] = { ...newPages[index], configuration: e.target.value };
                                const updatedTemplate = { ...selectedTemplate, pages: newPages };
                                setSelectedTemplate(updatedTemplate);
                                form.setFieldValue('pages', newPages);
                              }}
                              style={{
                                width: '100%',
                                height: 200,
                                border: 'none',
                                outline: 'none',
                                background: 'transparent',
                                fontFamily: 'monospace',
                                fontSize: 14,
                                resize: 'vertical',
                              }}
                            />
                          </Code>
                        </Box>
                      </Stack>
                    </Tabs.Panel>
                  ))}
                </Tabs>

                {/* Action Buttons */}
                <Divider my="md" />
                <Group justify="space-between">
                  <ActionIcon
                    color="red"
                    variant="filled"
                    size="lg"
                    onClick={handleDeleteTemplate}
                    disabled={!selectedTemplate?.id || loading}
                  >
                    <Icon path={mdiDelete} size={1} />
                  </ActionIcon>

                  <Group>
                    <ActionIcon
                      color="blue"
                      variant="filled"
                      size="lg"
                      onClick={handleDuplicateTemplate}
                      disabled={!selectedTemplate?.id || loading}
                    >
                      <Icon path={mdiContentCopy} size={1} />
                    </ActionIcon>

                    <Button
                      type="submit"
                      loading={loading}
                      disabled={!form.isValid()}
                    >
                      Enregistrer
                    </Button>
                  </Group>
                </Group>
              </Stack>
            </form>
          ) : (
            <Center style={{ height: 400 }}>
              <Stack align="center" gap="md">
                <Text size="lg" c="dimmed">
                  Sélectionnez un template pour commencer
                </Text>
                <Button onClick={handleNewTemplate} leftSection={<Icon path={mdiPlus} size={0.8} />}>
                  Créer un nouveau template
                </Button>
              </Stack>
            </Center>
          )}
        </Box>
      </Flex>

      {/* Image Modal */}
      <Modal
        opened={imageModalOpened}
        onClose={() => setImageModalOpened(false)}
        title="Image Preview"
        size="lg"
      >
        {selectedImage && (
          <Image
            src={selectedImage}
            alt="Template page preview"
            style={{ maxWidth: '100%' }}
          />
        )}
      </Modal>
    </Paper>
  );
};

export default ImpressionDesMutuelles