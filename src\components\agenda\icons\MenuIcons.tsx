import { 
    
    IconStethoscope,
    IconHistory,
    IconEdit,
    IconCalendarEvent,
    IconUserCircle,
    //  IconUserDetails,
    IconBellPlus,
    IconAlarm,
    IconX
  } from '@tabler/icons-react';

  import { IconId,IconDental } from '@tabler/icons-react';
 
  

  const MenuIcons = {
    sltVisit: ({ size = 16 }: { size?: number }) => (
   

    <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 24 24" height={size} width={size} xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M4 12V3H2v9c0 2.76 2.24 5 5 5h6v-2H7c-1.66 0-3-1.34-3-3zm18.83 5.24c-.38-.72-1.29-.97-2.03-.63l-1.09.5-3.41-6.98a2.01 2.01 0 0 0-1.79-1.12L11 9V3H5v8c0 1.66 1.34 3 3 3h7l3.41 7 3.72-1.7c.77-.36 1.1-1.3.7-2.06z"></path></svg>
  ),
    activeVisit: IconStethoscope,
    lastVisit: IconHistory,
    editAppointment: IconEdit,
    nextAppointment: IconCalendarEvent,
    patientFile: IconUserCircle,
    patientDetails: IconId,
    IconDental: IconDental,
    addAlert: IconBellPlus,
    addReminder: IconAlarm,
    cancel: IconX
  };
  
  export default MenuIcons;