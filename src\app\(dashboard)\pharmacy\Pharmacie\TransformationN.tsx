'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  Grid,
  TextInput,
  Select,
  Table,
  Text,
  NumberInput,
  ScrollArea,
  Tabs,
  Radio,
  Textarea,
  ActionIcon,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconTransform,
  IconList,
  IconSearch,
  IconBarcode,
  IconPaperclip,
  IconMessageCircle,
  IconX,
  IconCheck,
  IconDeviceFloppy,
} from '@tabler/icons-react';

// TypeScript interfaces
interface TransformationItem {
  id: string;
  code: string;
  designation: string;
  qte: number;
  prix: number;
  tva: number;
  depot: string;
  montant: number;
}

interface TransformationData {
  numero: string;
  date: Date | null;
  depot: string;
  type: 'Assemblage' | 'Désassemblage';
  article: string;
  qte: number;
  prix: number;
  items: TransformationItem[];
  commentaire: string;
  montantHT: number;
  montantTVA: number;
  montantTTC: number;
}

const TransformationN = () => {
  const [activeTab, setActiveTab] = useState<string>('details');
  const [transformationData, setTransformationData] = useState<TransformationData>({
    numero: '2',
    date: new Date('2022-09-16'),
    depot: 'Dépôt 1',
    type: 'Assemblage',
    article: '',
    qte: 1,
    prix: 1.00,
    items: [],
    commentaire: '',
    montantHT: 0.00,
    montantTVA: 0.00,
    montantTTC: 0.00,
  });

  const form = useForm({
    initialValues: transformationData,
  });

  const depotOptions = [
    'Dépôt 1',
    'Dépôt 2',
    'Dépôt 3',
  ];

  const handleSave = () => {
    notifications.show({
      title: 'Transformation enregistrée',
      message: 'La transformation a été enregistrée avec succès',
      color: 'green',
    });
  };

  const handleCancel = () => {
    notifications.show({
      title: 'Transformation annulée',
      message: 'La transformation a été annulée',
      color: 'red',
    });
  };

  const handleValidate = () => {
    notifications.show({
      title: 'Transformation validée',
      message: 'La transformation a été validée avec succès',
      color: 'blue',
    });
  };

  const handleRegisterAndQuit = () => {
    notifications.show({
      title: 'Enregistré et quitté',
      message: 'La transformation a été enregistrée et fermée',
      color: 'green',
    });
  };

  const handleRegister = () => {
    notifications.show({
      title: 'Transformation enregistrée',
      message: 'La transformation a été enregistrée',
      color: 'green',
    });
  };

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      {/* Header */}
      <Paper p="md" mb="md" withBorder className="bg-slate-600">
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconTransform size={24} className="text-slate-600" />
            <Title order={3} className="text-slate-600">
              Transformation N°: 2
            </Title>
          </Group>
          <Button
            variant="filled"
            className="bg-blue-500 hover:bg-blue-600"
            leftSection={<IconList size={16} />}
          >
            Transformations
          </Button>
        </Group>
      </Paper>

      {/* Main Content */}
      <Paper p="md" mb="md" withBorder>
        <form onSubmit={form.onSubmit(() => {})}>
          {/* First Row */}
          <Grid mb="md">
            <Grid.Col span={2}>
              <TextInput
                label="N°. Transformation *"
                placeholder="2"
                {...form.getInputProps('numero')}
                required
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <DatePickerInput
                label="Date"
                placeholder="16/09/2022"
                {...form.getInputProps('date')}
                required
              />
            </Grid.Col>
            <Grid.Col span={2}>
              <Select
                label="Dépôt *"
                placeholder="Dépôt 1"
                data={depotOptions}
                {...form.getInputProps('depot')}
                required
                rightSection={<ActionIcon size="sm" variant="subtle"><IconX size={12} /></ActionIcon>}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Text size="sm" fw={500} mb="xs">Type</Text>
              <Radio.Group
                value={form.values.type}
                onChange={(value) => form.setFieldValue('type', value as 'Assemblage' | 'Désassemblage')}
              >
                <Group>
                  <Radio value="Assemblage" label="Assemblage" />
                  <Radio value="Désassemblage" label="Désassemblage" />
                </Group>
              </Radio.Group>
            </Grid.Col>
          </Grid>

          {/* Second Row - Article Search */}
          <Grid mb="md">
            <Grid.Col span={6}>
              <TextInput
                label="Article"
                placeholder=""
                {...form.getInputProps('article')}
                rightSection={<ActionIcon size="sm" variant="subtle"><IconSearch size={16} /></ActionIcon>}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <NumberInput
                label="Qté *"
                placeholder="1"
                {...form.getInputProps('qte')}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <NumberInput
                label="Prix *"
                placeholder="1,00"
                {...form.getInputProps('prix')}
                decimalScale={2}
                fixedDecimalScale
                required
              />
            </Grid.Col>
          </Grid>
        </form>
      </Paper>

      {/* Tabs Section */}
      <Paper p="md" mb="md" withBorder>
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'details')}>
          <Tabs.List mb="md">
            <Tabs.Tab value="details" leftSection={<IconBarcode size={16} />}>
              Détails
            </Tabs.Tab>
            <Tabs.Tab value="pieces-jointes" leftSection={<IconPaperclip size={16} />}>
              Pièces jointes
            </Tabs.Tab>
            <Tabs.Tab value="commentaires" leftSection={<IconMessageCircle size={16} />}>
              Commentaires
            </Tabs.Tab>
          </Tabs.List>

          {/* Action Buttons */}
          <Group justify="flex-end" mb="md">
            <Button
              variant="filled"
              className="bg-blue-500 hover:bg-blue-600"
              leftSection={<IconBarcode size={16} />}
            >
              Code à barres
            </Button>
            <Button
              variant="filled"
              className="bg-blue-500 hover:bg-blue-600"
              leftSection={<IconBarcode size={16} />}
            >
              Article
            </Button>
            <Button
              variant="filled"
              className="bg-blue-500 hover:bg-blue-600"
              leftSection={<IconMessageCircle size={16} />}
            >
              Commentaire
            </Button>
          </Group>

          <Tabs.Panel value="details">
            {/* Table */}
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Désignation</Table.Th>
                    <Table.Th>Qté</Table.Th>
                    <Table.Th>Prix</Table.Th>
                    <Table.Th>Tva</Table.Th>
                    <Table.Th>Dépôt</Table.Th>
                    <Table.Th>Montant</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {transformationData.items.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={7} className="text-center text-gray-500 py-8">
                        Aucun élément trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    transformationData.items.map((item) => (
                      <Table.Tr key={item.id}>
                        <Table.Td>{item.code}</Table.Td>
                        <Table.Td>{item.designation}</Table.Td>
                        <Table.Td>{item.qte}</Table.Td>
                        <Table.Td>{item.prix.toFixed(2)}</Table.Td>
                        <Table.Td>{item.tva}</Table.Td>
                        <Table.Td>{item.depot}</Table.Td>
                        <Table.Td>{item.montant.toFixed(2)}</Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </ScrollArea>

            {/* Pagination */}
            <Group justify="space-between" mt="md">
              <Group>
                <Text size="sm">Page</Text>
                <Select
                  size="sm"
                  w={60}
                  data={['1']}
                  value="1"
                />
                <Text size="sm">Lignes par Page</Text>
                <Select
                  size="sm"
                  w={80}
                  data={['10', '25', '50', '100']}
                  value="10"
                />
                <Text size="sm">0 - 0 de 0</Text>
              </Group>
              <Group>
                <Button size="xs" variant="outline">K</Button>
                <Button size="xs" variant="outline">‹</Button>
                <Button size="xs" variant="outline">›</Button>
                <Button size="xs" variant="outline">⟩</Button>
              </Group>
            </Group>
          </Tabs.Panel>

          <Tabs.Panel value="pieces-jointes">
            <Text>Pièces jointes content</Text>
          </Tabs.Panel>

          <Tabs.Panel value="commentaires">
            <Textarea
              label="Commentaire"
              placeholder="Entrez votre commentaire..."
              {...form.getInputProps('commentaire')}
              minRows={4}
            />
          </Tabs.Panel>
        </Tabs>
      </Paper>

      {/* Bottom Section with Comment and Totals */}
      <Paper p="md" mb="md" withBorder>
        <Grid>
          <Grid.Col span={6}>
            <Textarea
              label="Commentaire"
              placeholder=""
              {...form.getInputProps('commentaire')}
              minRows={6}
            />
          </Grid.Col>
          <Grid.Col span={6}>
            <div className="space-y-4">
              <Group justify="space-between">
                <Text fw={500}>MONTANT HT :</Text>
                <Text fw={500}>{transformationData.montantHT.toFixed(2)}</Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>MONTANT TVA :</Text>
                <Text fw={500}>{transformationData.montantTVA.toFixed(2)}</Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>MONTANT TTC :</Text>
                <Text fw={500}>{transformationData.montantTTC.toFixed(2)}</Text>
              </Group>
            </div>
          </Grid.Col>
        </Grid>
      </Paper>

      {/* Action Buttons */}
      <Paper p="md" withBorder>
        <Group justify="flex-end">
          <Button
            variant="filled"
            color="red"
            onClick={handleCancel}
          >
            Annuler
          </Button>
          <Button
            variant="outline"
            color="gray"
            onClick={handleValidate}
            leftSection={<IconCheck size={16} />}
          >
            Valider
          </Button>
          <Button
            variant="outline"
            color="gray"
            onClick={handleRegisterAndQuit}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Enregistrer et quitter
          </Button>
          <Button
            variant="filled"
            color="gray"
            onClick={handleRegister}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Enregistrer
          </Button>
        </Group>
      </Paper>
    </div>
  );
};

export default TransformationN;
