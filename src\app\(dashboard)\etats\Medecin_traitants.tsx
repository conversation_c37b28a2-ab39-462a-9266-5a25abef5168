'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
} from '@mantine/core';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconUserCheck,
  IconChevronRight,
  IconChevronDown,
} from '@tabler/icons-react';
import ChampsModal from './ChampsModal';

const MedecinTraitants = () => {
  // État pour la modale des champs
  const [champsModalOpened, setChampsModalOpened] = useState(false);

  // État pour les lignes expandues
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set(['total', 'medecin1']));

  // Interface pour les données des médecins
  interface MedecinData {
    id: string;
    medecin: string;
    nomPatient?: string;
    total: number;
    isGroup?: boolean;
    isTotal?: boolean;
    level: number;
    children?: MedecinData[];
  }

  // Données d'exemple correspondant à l'image
  const donneesMedecins: MedecinData[] = [
    {
      id: 'total',
      medecin: 'Total',
      total: 0,
      isTotal: true,
      level: 0,
    }
  ];

  const toggleRow = (id: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedRows(newExpanded);
  };

  const renderRow = (item: MedecinData, _index: number) => {
    const isExpanded = expandedRows.has(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.id}>
        <Table.Tr
          className={`hover:bg-gray-50 ${item.isTotal ? 'bg-green-100' : ''}`}
        >
          <Table.Td className="border-r border-gray-300">
            <div
              className="flex items-center gap-2"
              style={{ paddingLeft: `${item.level * 20}px` }}
            >
              {hasChildren && (
                <ActionIcon
                  size="xs"
                  variant="subtle"
                  onClick={() => toggleRow(item.id)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  {isExpanded ? (
                    <IconChevronDown size={12} />
                  ) : (
                    <IconChevronRight size={12} />
                  )}
                </ActionIcon>
              )}
              <Text
                size="sm"
                fw={item.isTotal || item.isGroup ? 500 : 400}
                className={`${item.isTotal ? 'text-gray-800' : 'text-gray-800'}`}
              >
                {item.medecin}
              </Text>
            </div>
          </Table.Td>
          <Table.Td className="border-r border-gray-300">
            <Text size="sm" className="text-gray-800">
              {item.nomPatient || ''}
            </Text>
          </Table.Td>
          <Table.Td className="border-r border-gray-300">
            {item.isTotal ? (
              <div className="w-full bg-green-500 h-6 rounded flex items-center justify-center">
                <Text size="sm" className="text-white font-medium">
                  Total
                </Text>
              </div>
            ) : item.total > 0 ? (
              <div className="w-20 bg-green-500 h-4 rounded">
                {/* Barre verte pour représenter la valeur */}
              </div>
            ) : (
              <Text size="sm" className="text-gray-800">
                {item.total}
              </Text>
            )}
          </Table.Td>
          {/* Colonnes vides supplémentaires pour correspondre à l'image */}
          {Array.from({ length: 8 }, (_, cellIndex) => (
            <Table.Td
              key={cellIndex}
              className={`border-r border-gray-300 ${item.isTotal ? 'bg-green-100' : ''}`}
            />
          ))}
        </Table.Tr>

        {/* Rendu des enfants si la ligne est expandue */}
        {hasChildren && isExpanded && item.children?.map((child, childIndex) =>
          renderRow(child, childIndex)
        )}
      </React.Fragment>
    );
  };

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconUserCheck size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Médecin traitants
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
                onClick={() => setChampsModalOpened(true)}
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Zone principale du tableau */}
      <div className="bg-white h-[calc(100vh-80px)]">
        <Table
          striped={false}
          highlightOnHover={false}
          withTableBorder={true}
          withColumnBorders={true}
          className="h-full"
        >
          <Table.Thead className="bg-gray-50">
            <Table.Tr>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Médecin
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Nom du patient
              </Table.Th>
              <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                Total
              </Table.Th>
              {/* Colonnes vides supplémentaires pour correspondre à l'image */}
              {Array.from({ length: 8 }, (_, index) => (
                <Table.Th
                  key={index}
                  className="border-r border-gray-300 bg-gray-100 w-20"
                />
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {/* Rendu des données des médecins */}
            {donneesMedecins.map((item, index) => renderRow(item, index))}

            {/* Lignes vides pour remplir l'espace */}
            {Array.from({ length: 15 }, (_, index) => (
              <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                {Array.from({ length: 11 }, (_, cellIndex) => (
                  <Table.Td
                    key={cellIndex}
                    className="border-r border-gray-300 h-8"
                  />
                ))}
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </div>

      {/* Modale des champs */}
      <ChampsModal
        opened={champsModalOpened}
        onClose={() => setChampsModalOpened(false)}
      />
    </Box>
  );
};

export default MedecinTraitants;
