import api from '../lib/api';
import {
  getStoredData,
  storeData,
  STORAGE_KEYS,
  getFromCache,
  storeInCache
} from '~/utils/mockDataStorage';
import { backendToFrontendStatus, frontendToBackendStatus } from '~/utils/statusConverter';

// Backend API response interfaces
export interface BackendUserBase {
  id?: string;
  uuid?: string;
  email: string;
  first_name: string;
  last_name: string;
  user_type?: 'doctor' | 'patient' | 'assistant' | 'staff';
  is_active?: boolean | string;
  is_pending?: boolean | string;
  created_at?: string;
  date_joined?: string;
  created_by?: string;
  assigned_doctor_email?: string;
  phone_number?: string;
  profile_image?: string | { url: string } | null;
  profile_image_url?: string;
  profile_image_medium?: string;
  profile_image_large?: string;
}

export interface BackendAssistant extends BackendUserBase {
  assigned_doctor?: string;
}

export interface BackendStaff extends BackendUserBase {
  assigned_doctor?: string;
}

// API error interface
export interface ApiError {
  message?: string;
  response?: {
    status?: number;
    data?: {
      detail?: string;
      message?: string;
      [key: string]: unknown;
    };
  };
  request?: unknown;
  config?: unknown;
}

// Type for user data record with specific fields
export interface UserDataRecord {
  email?: string;
  password?: string;
  password2?: string;
  first_name?: string;
  last_name?: string;
  user_type?: 'doctor' | 'patient' | 'assistant' | 'staff';
  phone_number?: string;
  status?: 'active' | 'inactive' | 'pending';
  profile_image?: File | null;
  assigned_doctor?: string;
  is_active?: boolean;
  is_pending?: boolean;
  require_password_change?: boolean;
  [key: string]: string | number | boolean | null | undefined | File | string[] | object; // Allow additional properties with specific types
}

export interface UserAccount {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  user_type: 'doctor' | 'patient' | 'assistant' | 'staff';
  status: 'active' | 'inactive' | 'pending';
  created_at: string;
  created_by: string;
  password?: string; // Optional for creation or update
  confirm_password?: string; // Optional for password confirmation
  phone_number: string; // Phone number field
  profile_image?: File | null; // Profile image file
  profile_image_url?: string; // URL to the profile image
  is_active?: boolean; // Backend status flag
  is_pending?: boolean; // Backend status flag
}

export interface SubscriptionPackage {
  id: string;
  name: string;
  max_patients: number;
  max_assistants: number;
  max_staff: number;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  is_current: boolean;
}

// Mock data for user accounts
const mockUserAccounts: UserAccount[] = [
  {
    id: '1',
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Smith',
    user_type: 'doctor',
    status: 'active',
    created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
    created_by: 'self',
    phone_number: '+****************'
  },
  {
    id: '3',
    email: '<EMAIL>',
    first_name: 'Robert',
    last_name: 'Johnson',
    user_type: 'patient',
    status: 'active',
    created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
    created_by: '1',
    phone_number: '+****************'
  }
];

// Mock data for subscription packages
const mockSubscriptionPackages: SubscriptionPackage[] = [
  {
    id: '1',
    name: 'Basic',
    max_patients: 50,
    max_assistants: 1,
    max_staff: 1,
    price_monthly: 99,
    price_yearly: 999,
    features: [
      '50 Patient Accounts',
      '1 Assistant Account',
      '1 Staff Account',
      'Basic Appointment Scheduling',
      'Patient Records Management'
    ],
    is_current: false
  },
  {
    id: '2',
    name: 'Professional',
    max_patients: 200,
    max_assistants: 3,
    max_staff: 3,
    price_monthly: 199,
    price_yearly: 1999,
    features: [
      '200 Patient Accounts',
      '3 Assistant Accounts',
      '3 Staff Accounts',
      'Advanced Appointment Scheduling',
      'Patient Records Management',
      'Billing Integration',
      'Email Notifications'
    ],
    is_current: true
  },
  {
    id: '3',
    name: 'Enterprise',
    max_patients: 500,
    max_assistants: 10,
    max_staff: 10,
    price_monthly: 399,
    price_yearly: 3999,
    features: [
      '500 Patient Accounts',
      '10 Assistant Accounts',
      '10 Staff Accounts',
      'Advanced Appointment Scheduling',
      'Patient Records Management',
      'Billing Integration',
      'Email & SMS Notifications',
      'Custom Branding',
      'Priority Support'
    ],
    is_current: false
  }
];

const userManagementService = {
  // Function to clear cache for a specific doctor
  clearDoctorCache(doctorId: string) {
    const doctorSpecificCacheKey = `${STORAGE_KEYS.REAL_USER_ACCOUNTS}_${doctorId}`;
    const doctorSpecificTimestampKey = `${STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS}_${doctorId}`;

    if (typeof window !== 'undefined') {
      localStorage.removeItem(doctorSpecificCacheKey);
      localStorage.removeItem(doctorSpecificTimestampKey);
      console.log('Cleared cache for doctor:', doctorId);
    }
  },

  async getUserAccounts(): Promise<UserAccount[]> {
    console.log('Fetching user accounts from API');

    // Get the current doctor's ID from localStorage or other auth storage
    const doctorId = typeof window !== 'undefined' ? localStorage.getItem('userId') : null;
    console.log('Current doctor ID:', doctorId);

    if (!doctorId) {
      console.warn('No doctor ID found in localStorage');
      return [];
    }

    // Use doctor-specific cache keys
    const doctorSpecificCacheKey = `${STORAGE_KEYS.REAL_USER_ACCOUNTS}_${doctorId}`;
    const doctorSpecificTimestampKey = `${STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS}_${doctorId}`;

    // Check if we have valid cached data for this specific doctor
    const cachedUsers = getFromCache<UserAccount[]>(
      doctorSpecificCacheKey,
      doctorSpecificTimestampKey
    );

    if (cachedUsers) {
      console.log('Using cached user accounts data for doctor:', doctorId, cachedUsers);
      return cachedUsers;
    }

    try {
      // Fetch both assistants and staff for the logged-in doctor
      let allUsers: UserAccount[] = [];

      // First, fetch assistants
      try {
        console.log('Fetching assistants from API');
        console.log('Current doctor ID for assistants query:', doctorId);

        // Log the request URL for debugging
        const assistantsUrl = `/api/auth/assistants/?assigned_doctor=${doctorId}`;
        console.log('Assistants request URL:', assistantsUrl);

        const assistantsResponse = await api.get('/api/auth/assistants/', {
          params: {
            assigned_doctor: doctorId
          }
        });

        // Log the response status and data
        console.log('API response status for assistants:', assistantsResponse.status);
        console.log('API response data for assistants:', assistantsResponse.data);
        console.log('Number of assistants returned:', Array.isArray(assistantsResponse.data) ? assistantsResponse.data.length : 'Not an array');

        // Transform the assistants response
        const assistants = assistantsResponse.data.map((assistant: BackendAssistant) => {
          // Use the status converter utility
          const status = backendToFrontendStatus(assistant.is_active, assistant.is_pending);

          // Log the status conversion for debugging
          console.log(`Assistant ${assistant.email} status conversion:`, {
            backend: { is_active: assistant.is_active, is_pending: assistant.is_pending },
            frontend: status
          });

          // Log the image fields for debugging
          console.log(`Assistant ${assistant.email} image fields:`, {
            profile_image: assistant.profile_image,
            profile_image_url: assistant.profile_image_url,
            profile_image_medium: assistant.profile_image_medium,
            profile_image_large: assistant.profile_image_large
          });

          // Determine the best image URL to use
          let imageUrl = assistant.profile_image_url;
          if (!imageUrl && assistant.profile_image) {
            // If profile_image is a string (URL), use it
            if (typeof assistant.profile_image === 'string') {
              imageUrl = assistant.profile_image;
            }
            // If it's an object with a URL property, use that
            else if (assistant.profile_image && typeof assistant.profile_image === 'object' && 'url' in assistant.profile_image) {
              imageUrl = assistant.profile_image.url;
            }
          }

          // Fallback to medium or large image if available
          if (!imageUrl) {
            imageUrl = assistant.profile_image_medium || assistant.profile_image_large;
          }

          console.log(`Final image URL for assistant ${assistant.email}:`, imageUrl);

          return {
            id: assistant.id || assistant.uuid,
            email: assistant.email,
            first_name: assistant.first_name,
            last_name: assistant.last_name,
            user_type: 'assistant',
            status: status,
            created_at: assistant.created_at || assistant.date_joined,
            created_by: assistant.assigned_doctor_email || 'system',
            phone_number: assistant.phone_number || '',
            profile_image_url: imageUrl
          };
        });

        allUsers = [...allUsers, ...assistants];
      } catch (assistantsError) {
        console.error('Error fetching assistants from API:', assistantsError);
      }

      // Then, fetch staff members
      try {
        console.log('Fetching staff from API');
        // Use the correct endpoint for staff members
        const staffResponse = await api.get('/api/auth/staff/', {
          params: {
            assigned_doctor: doctorId
          }
        });
        console.log('API response for staff:', staffResponse.data);

        // Transform the staff response
        const staffMembers = staffResponse.data.map((staff: BackendStaff) => {
          // Use the status converter utility
          const status = backendToFrontendStatus(staff.is_active, staff.is_pending);

          // Log the status conversion for debugging
          console.log(`Staff ${staff.email} status conversion:`, {
            backend: { is_active: staff.is_active, is_pending: staff.is_pending },
            frontend: status
          });

          return {
            id: staff.id || staff.uuid,
            email: staff.email,
            first_name: staff.first_name,
            last_name: staff.last_name,
            user_type: 'staff',
            status: status,
            created_at: staff.created_at || staff.date_joined,
            created_by: staff.assigned_doctor_email || 'system',
            phone_number: staff.phone_number || '',
            profile_image_url: staff.profile_image_url || staff.profile_image || undefined
          };
        });

        allUsers = [...allUsers, ...staffMembers];
      } catch (staffError) {
        console.error('Error fetching staff from API:', staffError);
      }

      // If we got users from either endpoint, cache and return them
      if (allUsers.length > 0) {
        console.log('Successfully fetched users:', allUsers);

        // Cache the data with doctor-specific keys
        storeInCache(
          doctorSpecificCacheKey,
          doctorSpecificTimestampKey,
          allUsers
        );

        return allUsers;
      }

      // If both specific endpoints failed, use mock data instead of trying an endpoint that doesn't exist
      console.log('Using mock data for users instead of API call');

      // Create mock data based on user type
      const mockUsers = mockUserAccounts.filter(user => user.user_type === 'assistant' || user.user_type === 'staff');
      console.log('Mock users data:', mockUsers);

      // Return mock data in the same format as the API would
      const response = {
        data: mockUsers.map(user => ({
          id: user.id,
          uuid: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          user_type: user.user_type,
          is_active: user.status === 'active',
          is_pending: user.status === 'pending',
          created_at: user.created_at,
          date_joined: user.created_at,
          created_by: user.created_by,
          phone_number: user.phone_number,
          profile_image_url: user.profile_image_url,
          profile_image: user.profile_image_url
        }))
      };
      console.log('Mock API response for users:', response.data);

      // Filter for assistants and staff
      const users = response.data
        .filter((user: BackendUserBase) => user.user_type === 'assistant' || user.user_type === 'staff')
        .map((user: BackendUserBase) => {
          // Use the status converter utility
          const status = backendToFrontendStatus(user.is_active, user.is_pending);

          // Log the status conversion for debugging
          console.log(`User ${user.email} status conversion:`, {
            backend: { is_active: user.is_active, is_pending: user.is_pending },
            frontend: status
          });

          return {
            id: user.id || user.uuid || `temp_${Date.now()}_${Math.random()}`, // Ensure ID is never undefined
            email: user.email,
            first_name: user.first_name,
            last_name: user.last_name,
            user_type: user.user_type || 'assistant', // Ensure user_type is never undefined
            status: status,
            created_at: user.created_at || user.date_joined || new Date().toISOString(), // Ensure created_at is never undefined
            created_by: user.created_by || 'system',
            phone_number: user.phone_number || '',
            profile_image_url: user.profile_image_url || user.profile_image || undefined
          } as UserAccount;
        });

      // Cache the data with doctor-specific keys
      storeInCache(
        doctorSpecificCacheKey,
        doctorSpecificTimestampKey,
        users
      );

      return users;
    } catch (error) {
      console.error('Error fetching users from API:', error);
      console.log('Falling back to mock data');

      // Fallback to mock data for development/testing
      if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
        // Get stored accounts and filter by current doctor
        const storedAccounts = getStoredData<UserAccount[]>(STORAGE_KEYS.ASSISTANT_ACCOUNTS, mockUserAccounts);
        console.log('Retrieved stored accounts for fallback:', storedAccounts);

        // Filter by current doctor and user type
        // Note: Since UserAccount doesn't have assigned_doctor, we filter by created_by
        const filteredAccounts = storedAccounts.filter(user =>
          (user.user_type === 'assistant' || user.user_type === 'staff') &&
          user.created_by === doctorId
        );

        console.log('Filtered accounts for doctor', doctorId, ':', filteredAccounts);
        return filteredAccounts;
      }

      // If all API calls fail, return an empty array
      return [];
    }
  },

  async createUserAccount(data: Partial<UserAccount> | FormData): Promise<UserAccount | null> {
    // Log data without exposing sensitive information
    if (data instanceof FormData) {
      console.log('Creating user account with FormData containing fields:', Array.from(data.keys()));
    } else {
      console.log('Creating user account with data:', {
        ...data,
        password: data.password ? '[REDACTED]' : undefined,
        confirm_password: data.confirm_password ? '[REDACTED]' : undefined
      });
    }

    try {
      // Check subscription limits first
      const currentSubscription = await this.getCurrentSubscription();
      const currentUsers = await this.getUserAccounts();

      const assistantCount = currentUsers.filter(user => user.user_type === 'assistant').length;
      const patientCount = currentUsers.filter(user => user.user_type === 'patient').length;
      const staffCount = currentUsers.filter(user => user.user_type === 'staff').length;

      // Extract user type from data
      let userType: 'doctor' | 'patient' | 'assistant' | 'staff' = 'assistant';
      if (data instanceof FormData) {
        // Get user_type from FormData
        const formDataUserType = data.get('user_type');
        if (formDataUserType && typeof formDataUserType === 'string') {
          userType = formDataUserType as 'doctor' | 'patient' | 'assistant' | 'staff';
        }
      } else {
        // Get user_type from UserAccount object
        userType = data.user_type || 'assistant';
      }

      // Check subscription limits based on user type
      if (userType === 'assistant' && assistantCount >= currentSubscription.max_assistants) {
        throw new Error(`You have reached the maximum number of assistants (${currentSubscription.max_assistants}) for your subscription package. You need to upgrade your package to add more assistants. Note that deleting assistants will not allow you to create new ones until your package is renewed.`);
      }

      if (userType === 'patient' && patientCount >= currentSubscription.max_patients) {
        throw new Error(`You have reached the maximum number of patients (${currentSubscription.max_patients}) for your subscription package. You need to upgrade your package to add more patients.`);
      }

      if (userType === 'staff' && staffCount >= currentSubscription.max_staff) {
        throw new Error(`You have reached the maximum number of staff (${currentSubscription.max_staff}) for your subscription package. You need to upgrade your package to add more staff. Note that deleting staff will not allow you to create new ones until your package is renewed.`);
      }

      // If data is FormData, we'll send it directly to the API
      if (data instanceof FormData) {
        // Set require_password_change flag to false
        if (data.has('require_password_change')) {
          data.delete('require_password_change');
        }
        data.append('require_password_change', 'false');

        // Add the current doctor's ID for assistants
        const formDataUserType = data.get('user_type');
        if (formDataUserType === 'assistant') {
          const doctorId = localStorage.getItem('userId');
          if (doctorId) {
            console.log('Adding assigned_doctor to FormData:', doctorId);
            data.append('assigned_doctor', doctorId);
          }
        }

        // Handle status conversion for FormData
        if (data.has('status')) {
          const status = data.get('status') as 'active' | 'inactive' | 'pending';

          // Use the status converter utility
          const { is_active, is_pending } = frontendToBackendStatus(status);

          // Log the status conversion for debugging
          console.log(`FormData status conversion:`, {
            frontend: status,
            backend: { is_active, is_pending }
          });

          // Add the converted status to the FormData
          data.append('is_active', is_active.toString());
          data.append('is_pending', is_pending.toString());

          // Remove status field as backend doesn't have it
          // Note: FormData doesn't have a delete method in all browsers, so we can't remove it
        }
      } else {
        // Prepare the data for API call from UserAccount object
        const userData: UserDataRecord = {
          email: data.email,
          password: data.password,
          password2: data.password, // Confirmation password
          first_name: data.first_name,
          last_name: data.last_name,
          user_type: data.user_type,
          phone_number: data.phone_number || '',
          // Set a flag to NOT require password change on first login
          require_password_change: false
        };

        // Add the current doctor's ID for assistants
        if (data.user_type === 'assistant') {
          const doctorId = localStorage.getItem('userId');
          if (doctorId) {
            console.log('Adding assigned_doctor to userData:', doctorId);
            userData.assigned_doctor = doctorId;
          }
        }

        // Handle status conversion
        if (data.status) {
          // Use the status converter utility
          const { is_active, is_pending } = frontendToBackendStatus(data.status);

          // Log the status conversion for debugging
          console.log(`JSON status conversion:`, {
            frontend: data.status,
            backend: { is_active, is_pending }
          });

          // Add the converted status to the userData
          userData.is_active = is_active;
          userData.is_pending = is_pending;
        } else {
          // Default to pending
          const { is_active, is_pending } = frontendToBackendStatus('pending');
          userData.is_active = is_active;
          userData.is_pending = is_pending;
        }

        // Replace data with userData for the rest of the function
        data = userData;
      }

      // For development/testing, use mock data if API call fails
      try {
        // Call the API to create the user
        let response;

        if (data instanceof FormData) {
          // Get user_type from FormData
          const formDataUserType = data.get('user_type');

          // Add password confirmation
          const password = data.get('password');
          if (password && !data.has('password2')) {
            data.append('password2', password as string);
          }

          // Add is_active and is_pending based on status if provided
          if (data.has('status')) {
            const status = data.get('status') as string;
            if (status === 'active') {
              data.append('is_active', 'true');
              data.append('is_pending', 'false');
            } else if (status === 'inactive') {
              data.append('is_active', 'false');
              data.append('is_pending', 'false');
            } else if (status === 'pending') {
              data.append('is_active', 'false');
              data.append('is_pending', 'true');
            }
          } else {
            // Default to pending
            data.append('is_active', 'false');
            data.append('is_pending', 'true');
          }

          // Use the appropriate endpoint based on user type
          try {
            if (formDataUserType === 'assistant') {
              console.log('Creating assistant with FormData');
              response = await api.post('/api/auth/assistants/create/', data, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              });
            } else if (formDataUserType === 'staff') {
              console.log('Creating staff with FormData');

              // Add the current doctor's ID for staff members too
              const doctorId = localStorage.getItem('userId');
              if (doctorId && !data.has('assigned_doctor')) {
                console.log('Adding assigned_doctor to FormData for staff:', doctorId);
                data.append('assigned_doctor', doctorId);
              }

              // Use the staff-specific endpoint
              console.log('Creating staff member with assigned doctor:', data.get('assigned_doctor'));
              response = await api.post('/api/auth/staff/create/', data, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              });
            } else {
              console.log('Creating user with FormData');
              response = await api.post('/api/auth/register/', data, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              });
            }
          } catch (error) {
            console.error('Error creating user:', error);
            throw error;
          }
        } else {
          // Regular JSON data
          // Add password confirmation and status fields
          const userData: UserDataRecord = {
            ...data,
            password2: data.password
          };

          // Convert status to is_active and is_pending
          if (data.status) {
            if (data.status === 'active') {
              userData.is_active = true;
              userData.is_pending = false;
            } else if (data.status === 'inactive') {
              userData.is_active = false;
              userData.is_pending = false;
            } else if (data.status === 'pending') {
              userData.is_active = false;
              userData.is_pending = true;
            }
          } else {
            // Default to pending
            userData.is_active = false;
            userData.is_pending = true;
          }

          // Use the appropriate endpoint based on user type
          try {
            if (data.user_type === 'assistant') {
              console.log('Creating assistant with JSON data');
              response = await api.post('/api/auth/assistants/create/', userData);
            } else if (data.user_type === 'staff') {
              console.log('Creating staff with JSON data');

              // Add the current doctor's ID for staff members too
              const doctorId = localStorage.getItem('userId');
              if (doctorId && !userData.assigned_doctor) {
                console.log('Adding assigned_doctor to userData for staff:', doctorId);
                userData.assigned_doctor = doctorId;
              }

              // Use the staff-specific endpoint
              console.log('Creating staff member with assigned doctor:', userData.assigned_doctor);
              response = await api.post('/api/auth/staff/create/', userData);
            } else {
              console.log('Creating user with JSON data');
              response = await api.post('/api/auth/register/', userData);
            }
          } catch (error) {
            console.error('Error creating user:', error);
            throw error;
          }
        }

        console.log('API response for user creation:', response.data);

        // Use the status converter utility
        const status = backendToFrontendStatus(response.data.is_active, response.data.is_pending);

        // Log the status conversion for debugging
        console.log(`New user ${response.data.email} status conversion:`, {
          backend: { is_active: response.data.is_active, is_pending: response.data.is_pending },
          frontend: status
        });

        // Transform the API response to match our UserAccount interface
        const newUser: UserAccount = {
          id: response.data.id || response.data.uuid,
          email: response.data.email,
          first_name: response.data.first_name,
          last_name: response.data.last_name,
          phone_number: response.data.phone_number || '',
          user_type: response.data.user_type || (data instanceof FormData ? data.get('user_type') as string : data.user_type),
          status: status,
          created_at: response.data.created_at || response.data.date_joined || new Date().toISOString(),
          created_by: response.data.created_by || response.data.assigned_doctor_email || 'system',
          profile_image_url: response.data.profile_image_url || response.data.profile_image || null
        };

        // Log the profile image URL for debugging
        console.log('Profile image URL for new user:', {
          profile_image_url: response.data.profile_image_url,
          profile_image: response.data.profile_image,
          final_url: newUser.profile_image_url,
          is_null: newUser.profile_image_url === null,
          is_undefined: newUser.profile_image_url === undefined,
          is_string_null: newUser.profile_image_url === 'null',
          is_string_undefined: newUser.profile_image_url === 'undefined',
          type: typeof newUser.profile_image_url
        });

        // Fix profile_image_url if it's a string 'null' or 'undefined'
        if (newUser.profile_image_url === 'null' || newUser.profile_image_url === 'undefined') {
          console.log('Fixing profile_image_url that is a string "null" or "undefined"');
          newUser.profile_image_url = undefined;
        }

        // Update the cache with the new user
        try {
          const cachedUsers = getFromCache<UserAccount[]>(
            STORAGE_KEYS.REAL_USER_ACCOUNTS,
            STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS
          );

          if (cachedUsers) {
            // Add the new user to the cached users
            const updatedUsers = [...cachedUsers, newUser];

            // Update the cache
            storeInCache(
              STORAGE_KEYS.REAL_USER_ACCOUNTS,
              STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS,
              updatedUsers
            );

            console.log('Updated cache with new user');
          }
        } catch (cacheError) {
          console.error('Error updating cache with new user:', cacheError);
        }

        return newUser;
      } catch (apiError) {
        console.error('API error creating user:', apiError);

        // Fallback to mock data for development
        if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
          console.log('Using mock data as fallback');

          // Extract data from FormData or use object directly
          let mockUserData: UserDataRecord = {};
          let userType: string = 'assistant';
          let userStatus: 'active' | 'inactive' | 'pending' = 'pending';

          if (data instanceof FormData) {
            // Extract values from FormData
            mockUserData.email = data.get('email') as string || '<EMAIL>';
            mockUserData.first_name = data.get('first_name') as string || 'New';
            mockUserData.last_name = data.get('last_name') as string || 'User';
            mockUserData.phone_number = data.get('phone_number') as string || '';
            userType = data.get('user_type') as string || 'assistant';

            // Get status if available
            const status = data.get('status');
            if (status && typeof status === 'string') {
              userStatus = status as 'active' | 'inactive' | 'pending';
            }
          } else {
            // Use object directly
            mockUserData = data as UserDataRecord;
            userType = data.user_type || 'assistant';
            userStatus = data.status || 'pending';
          }

          const newUser: UserAccount = {
            id: `${mockUserAccounts.length + 1}`,
            email: mockUserData.email || '<EMAIL>',
            first_name: mockUserData.first_name || 'New',
            last_name: mockUserData.last_name || 'User',
            phone_number: mockUserData.phone_number || '',
            user_type: userType as 'doctor' | 'patient' | 'assistant' | 'staff',
            status: userStatus,
            created_at: new Date().toISOString(),
            created_by: '1', // Assuming the doctor is creating the account
            profile_image_url: mockUserData.profile_image ? URL.createObjectURL(mockUserData.profile_image) : undefined
          };

          // Get stored accounts
          const storedAccounts = getStoredData<UserAccount[]>(STORAGE_KEYS.ASSISTANT_ACCOUNTS, mockUserAccounts);

          // Add the new user to the accounts
          storedAccounts.push(newUser);

          // Update stored accounts
          storeData(STORAGE_KEYS.ASSISTANT_ACCOUNTS, storedAccounts);
          console.log('User created and stored successfully:', newUser);

          return newUser;
        }

        throw apiError;
      }
    } catch (error) {
      console.error('Error creating user account:', error);
      throw error;
    }
  },

  async updateUserAccount(id: string, data: Partial<UserAccount> | FormData): Promise<UserAccount | null> {
    console.log(`=== UPDATING USER ACCOUNT ===`);
    console.log(`User ID: ${id}`);
    console.log(`Input data:`, JSON.stringify(data, null, 2));

    // Check if we're using mock data first
    if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
      console.log('=== USING MOCK DATA DIRECTLY ===');

      // Get stored accounts
      const storedAccounts = getStoredData<UserAccount[]>(STORAGE_KEYS.ASSISTANT_ACCOUNTS, mockUserAccounts);

      // Find the user to update
      const userIndex = storedAccounts.findIndex(user => user.id === id);
      if (userIndex === -1) {
        console.log(`User with ID ${id} not found in stored accounts`);
        return null;
      }

      console.log('Found user in stored accounts at index:', userIndex);
      console.log('Original user data:', storedAccounts[userIndex]);

      // Create a copy of the user with the updated data
      const updatedUser: UserAccount = {
        ...storedAccounts[userIndex]
      };

      // If data is FormData, extract values
      if (data instanceof FormData) {
        // Extract values from FormData
        const email = data.get('email');
        const first_name = data.get('first_name');
        const last_name = data.get('last_name');
        const phone_number = data.get('phone_number');
        const status = data.get('status');
        const profile_image = data.get('profile_image');

        // Update user with FormData values
        if (email) updatedUser.email = email as string;
        if (first_name) updatedUser.first_name = first_name as string;
        if (last_name) updatedUser.last_name = last_name as string;
        if (phone_number) updatedUser.phone_number = phone_number as string;
        if (status) updatedUser.status = status as 'active' | 'inactive' | 'pending';

        // Handle profile image (mock implementation)
        if (profile_image && profile_image instanceof File) {
          // In mock mode, we can create a fake URL for the image
          updatedUser.profile_image_url = URL.createObjectURL(profile_image);
          console.log('Mock data: Created URL for profile image:', updatedUser.profile_image_url);
        }
      } else {
        // Regular object update
        Object.assign(updatedUser, data);

        // Handle status conversion for mock data
        if (data.status) {
          updatedUser.status = data.status;
          console.log('Mock data: Updated user status to', data.status);
        }
      }

      // Update the stored accounts
      storedAccounts[userIndex] = updatedUser;

      // Save the updated accounts
      storeData(STORAGE_KEYS.ASSISTANT_ACCOUNTS, storedAccounts);

      console.log('Mock data: Updated user object:', updatedUser);
      console.log('=== MOCK UPDATE SUCCESSFUL ===');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return updatedUser;
    }

    // If not using mock data, proceed with API calls
    try {
      // Define a type for API data that can be either FormData or a record with specific value types
      type ApiDataType = Record<string, string | number | boolean | null | undefined | File | string[] | object>;
      let apiData: ApiDataType | FormData;

      // Check if data is FormData or regular object
      if (data instanceof FormData) {
        console.log('Data is FormData, using as is with some modifications');
        apiData = data;

        // Handle status conversion for FormData
        const status = data.get('status');
        if (status) {
          console.log(`Status conversion for FormData: "${status}" -> is_active/is_pending flags`);

          // Normalize status value to handle string case variations
          let normalizedStatus: 'active' | 'inactive' | 'pending';

          if (typeof status === 'string') {
            const statusLower = status.toLowerCase();
            if (statusLower === 'active') {
              normalizedStatus = 'active';
            } else if (statusLower === 'pending') {
              normalizedStatus = 'pending';
            } else if (statusLower === 'inactive') {
              normalizedStatus = 'inactive';
            } else {
              console.warn(`Unknown status value: "${status}", defaulting to "inactive"`);
              normalizedStatus = 'inactive';
            }
          } else {
            console.warn(`Status is not a string: ${status}, defaulting to "inactive"`);
            normalizedStatus = 'inactive';
          }

          console.log(`Normalized status: "${status}" -> "${normalizedStatus}"`);

          // Use the status converter utility
          const { is_active, is_pending } = frontendToBackendStatus(normalizedStatus);

          // Log the status conversion for debugging
          console.log(`FormData update status conversion:`, {
            frontend: status,
            normalized: normalizedStatus,
            backend: { is_active, is_pending }
          });

          // Add the converted status to the FormData
          apiData.append('is_active', is_active.toString());
          apiData.append('is_pending', is_pending.toString());
          console.log(`Setting is_active=${is_active}, is_pending=${is_pending}`);

          // Note: We can't delete fields from FormData, but the backend should ignore the status field
        }

        // If password is provided, add password confirmation and require password change
        const password = data.get('password');
        if (password && typeof password === 'string' && password.length > 0) {
          console.log('Password provided in FormData, adding confirmation and require_password_change flag');
          apiData.append('password2', password);
          apiData.append('require_password_change', 'false');

          // Log password details for debugging (without revealing the actual password)
          console.log('Password details:', {
            length: password.length,
            has_password2: apiData.has('password2'),
            has_require_password_change: apiData.has('require_password_change')
          });
        }

        console.log('FormData ready for submission with fields:', Array.from(apiData.keys()));
      } else {
        // Regular JSON object
        apiData = { ...data };
        console.log('Initial API data (before conversion):', JSON.stringify(apiData, null, 2));

        // Handle status conversion
        if (apiData.status) {
          console.log(`Status conversion: "${apiData.status}" -> is_active/is_pending flags`);

          // Normalize status value to handle string case variations
          let normalizedStatus: 'active' | 'inactive' | 'pending';

          if (typeof apiData.status === 'string') {
            const statusLower = apiData.status.toLowerCase();
            if (statusLower === 'active') {
              normalizedStatus = 'active';
            } else if (statusLower === 'pending') {
              normalizedStatus = 'pending';
            } else if (statusLower === 'inactive') {
              normalizedStatus = 'inactive';
            } else {
              console.warn(`Unknown status value: "${apiData.status}", defaulting to "inactive"`);
              normalizedStatus = 'inactive';
            }
          } else {
            console.warn(`Status is not a string: ${apiData.status}, defaulting to "inactive"`);
            normalizedStatus = 'inactive';
          }

          console.log(`Normalized status: "${apiData.status}" -> "${normalizedStatus}"`);

          // Use the status converter utility
          const { is_active, is_pending } = frontendToBackendStatus(normalizedStatus);

          // Log the status conversion for debugging
          console.log(`JSON update status conversion:`, {
            frontend: apiData.status,
            normalized: normalizedStatus,
            backend: { is_active, is_pending }
          });

          // Add the converted status to the apiData
          apiData.is_active = is_active;
          apiData.is_pending = is_pending;
          console.log(`Setting is_active=${is_active}, is_pending=${is_pending}`);

          // Remove status from the API data since backend doesn't have this field
          delete apiData.status;
          console.log('Status field removed from API data');

          console.log('Status conversion complete:', { is_active: apiData.is_active, is_pending: apiData.is_pending });
        } else {
          console.log('No status field found in input data, skipping conversion');
        }

        // If password is provided, add password confirmation and require password change
        if (apiData.password) {
          console.log('Password provided, adding confirmation and require_password_change flag');
          apiData.password2 = apiData.password; // Add confirmation password
          apiData.require_password_change = false; // Do not require password change on next login

          // Log password details for debugging (without revealing the actual password)
          console.log('Password details:', {
            length: typeof apiData.password === 'string' ? apiData.password.length : 'unknown',
            has_password2: !!apiData.password2,
            require_password_change: apiData.require_password_change
          });
        } else {
          console.log('No password provided in update data');
        }

        console.log('Final API data ready for submission:', {
          ...apiData,
          password: apiData.password ? '[REDACTED]' : undefined,
          password2: apiData.password2 ? '[REDACTED]' : undefined
        });
      }

      // Try to update the user via the API
      let response;

      // Set up headers for FormData if needed
      const headers: Record<string, string> = {};
      if (!(data instanceof FormData)) {
        headers['Content-Type'] = 'application/json';
      }

      try {
        // Determine the appropriate endpoint based on user_type
        const userType = apiData instanceof FormData
          ? apiData.get('user_type')
          : apiData.user_type;

        console.log(`User type for update: ${userType}`);

        // Make sure user_type is included in the data
        if (apiData instanceof FormData) {
          if (!apiData.has('user_type') && userType) {
            apiData.append('user_type', userType as string);
            console.log(`Added user_type=${userType} to FormData`);
          }
        } else if (!apiData.user_type && userType) {
          apiData.user_type = userType;
          console.log(`Added user_type=${userType} to JSON data`);
        }

        if (userType === 'assistant') {
          // Try the assistants endpoint first for assistant users
          console.log(`ATTEMPT 1: Sending PATCH request to /api/auth/assistants/${id}/`);
          try {
            // Make sure assigned_doctor is included for assistants
            const doctorId = localStorage.getItem('userId');
            if (doctorId) {
              if (apiData instanceof FormData) {
                if (!apiData.has('assigned_doctor')) {
                  apiData.append('assigned_doctor', doctorId);
                  console.log(`Added assigned_doctor=${doctorId} to FormData for assistant update`);
                }
              } else {
                if (!apiData.assigned_doctor) {
                  apiData.assigned_doctor = doctorId;
                  console.log(`Added assigned_doctor=${doctorId} to JSON data for assistant update`);
                }
              }
            }

            // Add password2 if password is provided
            if (apiData instanceof FormData) {
              const password = apiData.get('password');
              if (password && !apiData.has('password2')) {
                apiData.append('password2', password as string);
                console.log('Added password2 to FormData for assistant update');
              }

              // Log all form data fields for debugging
              console.log('FormData fields for assistant update:', Array.from(apiData.entries()).map(([key]) => key));

              // Check if profile_image is present
              if (apiData.has('profile_image')) {
                const profileImage = apiData.get('profile_image');
                console.log('Profile image is present in FormData:',
                  profileImage instanceof File ?
                  `File: ${profileImage.name}, size: ${profileImage.size}` :
                  `Type: ${typeof profileImage}`
                );
              } else {
                console.log('No profile_image in FormData');
              }
            } else if (apiData.password && !apiData.password2) {
              apiData.password2 = apiData.password;
              console.log('Added password2 to JSON data for assistant update');

              // Log JSON data for debugging
              console.log('JSON data for assistant update:', {
                ...apiData,
                password: apiData.password ? '[REDACTED]' : undefined,
                password2: apiData.password2 ? '[REDACTED]' : undefined
              });
            }

            // FormData content type is handled automatically by axios

            response = await api.patch(`/api/auth/assistants/${id}/`, apiData, { headers });
            console.log('ATTEMPT 1 SUCCESSFUL: Response received from assistants endpoint');

            // Log the response data for debugging
            console.log('Response data:', {
              ...response.data,
              profile_image: response.data.profile_image ? 'Present' : 'Not present',
              profile_image_url: response.data.profile_image_url
            });
          } catch (error: unknown) {
            const assistantError = error as ApiError;
            console.log(`Assistant endpoint failed: ${assistantError.message || 'Unknown error'}`);
            console.log('Error details:', assistantError.response?.data || 'No response data');

            // Try the users endpoint as fallback
            console.log(`Trying users endpoint for assistant`);
            response = await api.patch(`/api/auth/users/${id}/`, apiData, { headers });
            console.log('SUCCESSFUL: Assistant updated via users endpoint');
          }
        } else if (userType === 'staff') {
          // Try the staff endpoint first for staff users
          console.log(`ATTEMPT 1: Sending PATCH request to /api/auth/staff/${id}/`);
          try {
            response = await api.patch(`/api/auth/staff/${id}/`, apiData, { headers });
            console.log('ATTEMPT 1 SUCCESSFUL: Response received from staff endpoint');
          } catch (error: unknown) {
            const staffError = error as ApiError;
            console.log(`Staff endpoint failed: ${staffError.message || 'Unknown error'}`);
            console.log('Error details:', staffError.response?.data || 'No response data');

            // Try the users endpoint as fallback
            console.log(`Trying users endpoint for staff`);
            response = await api.patch(`/api/auth/users/${id}/`, apiData, { headers });
            console.log('SUCCESSFUL: Staff updated via users endpoint');
          }
        } else {
          // For other user types, try the users endpoint first
          console.log(`ATTEMPT 1: Sending PATCH request to /api/auth/users/${id}/`);
          response = await api.patch(`/api/auth/users/${id}/`, apiData, { headers });
          console.log('ATTEMPT 1 SUCCESSFUL: Response received from users endpoint');
        }
      } catch (error: unknown) {
        const firstError = error as ApiError;
        console.log(`ATTEMPT 1 FAILED: Error from endpoint:`, firstError.message);
        console.log('Error details:', firstError.response?.data || 'No response data');

        try {
          // Try the general users endpoint as fallback
          console.log(`ATTEMPT 2: Sending PATCH request to /api/auth/users/${id}/`);
          response = await api.patch(`/api/auth/users/${id}/`, apiData, { headers });
          console.log('ATTEMPT 2 SUCCESSFUL: Response received from users endpoint');
        } catch (error: unknown) {
          const secondError = error as ApiError;
          console.log(`ATTEMPT 2 FAILED: Error from users endpoint:`, secondError.message);
          console.log('Error details:', secondError.response?.data || 'No response data');

          // Try the user endpoint
          console.log(`ATTEMPT 3: Sending PATCH request to /api/auth/user/${id}/`);
          try {
            response = await api.patch(`/api/auth/user/${id}/`, apiData, { headers });
            console.log('ATTEMPT 3 SUCCESSFUL: Response received from user endpoint');
          } catch (error: unknown) {
            const thirdError = error as ApiError;
            console.log(`ATTEMPT 3 FAILED: Error from user endpoint:`, thirdError.message);
            console.log('Error details:', thirdError.response?.data || 'No response data');

            // Try the me endpoint as a last resort
            console.log(`ATTEMPT 4: Sending PATCH request to /api/auth/me/`);
            try {
              response = await api.patch(`/api/auth/me/`, apiData, { headers });
              console.log('ATTEMPT 4 SUCCESSFUL: Response received from me endpoint');
            } catch (error: unknown) {
              const fourthError = error as ApiError;
              console.log(`ATTEMPT 4 FAILED: Error from me endpoint:`, fourthError.message);
              console.log('Error details:', fourthError.response?.data || 'No response data');

              // If all attempts failed, rethrow the original error
              throw firstError;
            }
          }
        }
      }

      console.log('=== API RESPONSE RECEIVED ===');
      console.log('Raw response data:', JSON.stringify(response.data, null, 2));

      // Log the raw is_active and is_pending values for debugging
      console.log('Raw is_active value:', response.data.is_active);
      console.log('Raw is_active type:', typeof response.data.is_active);
      console.log('Raw is_pending value:', response.data.is_pending);
      console.log('Raw is_pending type:', typeof response.data.is_pending);

      // For staff users, force is_active to be a boolean
      let is_active = response.data.is_active;
      let is_pending = response.data.is_pending;

      if (response.data.user_type === 'staff') {
        // Convert is_active to boolean if it's a string
        if (typeof is_active === 'string') {
          is_active = is_active.toLowerCase() === 'true';
          console.log('Converted is_active string to boolean:', is_active);
        }

        // Convert is_pending to boolean if it's a string
        if (typeof is_pending === 'string') {
          is_pending = is_pending.toLowerCase() === 'true';
          console.log('Converted is_pending string to boolean:', is_pending);
        }

        // Force is_active to be true if status was set to 'active'
        if (apiData instanceof FormData) {
          const status = apiData.get('status');
          if (status === 'active') {
            is_active = true;
            is_pending = false;
            console.log('Forced is_active=true, is_pending=false based on FormData status=active');
          }
        } else if (apiData.status === 'active') {
          is_active = true;
          is_pending = false;
          console.log('Forced is_active=true, is_pending=false based on JSON status=active');
        }
      }

      // Check if the response already includes a status field
      let userStatus;
      if (response.data.status) {
        userStatus = response.data.status;
        console.log(`Using status directly from response: ${userStatus}`);
      } else {
        // Use the status converter utility with the potentially fixed values
        userStatus = backendToFrontendStatus(is_active, is_pending);
        console.log(`Converted status from is_active/is_pending: ${userStatus}`);
      }

      // Log the status conversion for debugging
      console.log(`Updated user ${response.data.email} status conversion:`, {
        backend: {
          original: { is_active: response.data.is_active, is_pending: response.data.is_pending },
          fixed: { is_active, is_pending }
        },
        response_status: response.data.status,
        frontend: userStatus
      });

      // Create the updated user object
      const updatedUser: UserAccount = {
        id: response.data.id || response.data.uuid,
        email: response.data.email,
        first_name: response.data.first_name,
        last_name: response.data.last_name,
        phone_number: response.data.phone_number || '',
        user_type: response.data.user_type || 'assistant',
        status: userStatus,
        // Add the corrected is_active and is_pending values
        is_active: is_active,
        is_pending: is_pending,
        created_at: response.data.created_at || response.data.date_joined || new Date().toISOString(),
        created_by: response.data.created_by || response.data.assigned_doctor_email || 'system',
        profile_image_url: response.data.profile_image_url || response.data.profile_image || undefined
      };

      // Log the profile image URL for debugging
      console.log('Profile image URL from response:', {
        profile_image_url: response.data.profile_image_url,
        profile_image: response.data.profile_image,
        final_url: updatedUser.profile_image_url,
        is_null: updatedUser.profile_image_url === null,
        is_undefined: updatedUser.profile_image_url === undefined,
        is_string_null: updatedUser.profile_image_url === 'null',
        is_string_undefined: updatedUser.profile_image_url === 'undefined',
        type: typeof updatedUser.profile_image_url
      });

      // Fix profile_image_url if it's a string 'null' or 'undefined'
      if (updatedUser.profile_image_url === 'null' || updatedUser.profile_image_url === 'undefined') {
        console.log('Fixing profile_image_url that is a string "null" or "undefined"');
        updatedUser.profile_image_url = undefined;
      }

      console.log('Transformed user object:', updatedUser);
      console.log('=== UPDATE SUCCESSFUL ===');

      // Update the cache with the updated user
      try {
        const cachedUsers = getFromCache<UserAccount[]>(
          STORAGE_KEYS.REAL_USER_ACCOUNTS,
          STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS
        );

        if (cachedUsers) {
          // Replace the user in the cached users
          const updatedUsers = cachedUsers.map(user =>
            user.id === updatedUser.id ? updatedUser : user
          );

          // Update the cache
          storeInCache(
            STORAGE_KEYS.REAL_USER_ACCOUNTS,
            STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS,
            updatedUsers
          );

          console.log('Updated cache with updated user');
        }
      } catch (cacheError) {
        console.error('Error updating cache with updated user:', cacheError);
      }

      return updatedUser;
    } catch (error: unknown) {
      const apiError = error as ApiError;
      console.error('=== ERROR UPDATING USER ACCOUNT ===');
      console.error('Error message:', apiError.message);

      if (apiError.response) {
        console.error('Response status:', apiError.response.status);
        console.error('Response data:', apiError.response.data);

        // Log detailed error information for debugging
        if (apiError.response.data && typeof apiError.response.data === 'object') {
          Object.entries(apiError.response.data).forEach(([key, value]) => {
            console.error(`Field '${key}' error:`, value);
          });
        }

        // Check for specific error types
        if (apiError.response.status === 400) {
          console.error('Validation error detected. Check field values.');

          // If there's a password error, log it specifically
          if (apiError.response.data && apiError.response.data.password) {
            console.error('Password error:', apiError.response.data.password);
          }

          // If there's an email error, log it specifically
          if (apiError.response.data && apiError.response.data.email) {
            console.error('Email error:', apiError.response.data.email);
          }
        }
      }

      throw error;
    }
  },

  async deleteUserAccount(id: string, userType?: string): Promise<boolean> {
    console.log(`=== DELETING USER ACCOUNT ===`);
    console.log(`User ID: ${id}`);
    console.log(`User Type (if provided): ${userType || 'not specified'}`);

    // Check if we're using mock data first
    if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
      console.log('=== USING MOCK DATA DIRECTLY ===');

      // Get stored accounts
      const storedAccounts = getStoredData<UserAccount[]>(STORAGE_KEYS.ASSISTANT_ACCOUNTS, mockUserAccounts);

      // Find the user to delete
      const userIndex = storedAccounts.findIndex(user => user.id === id);
      if (userIndex === -1) {
        console.log(`User with ID ${id} not found in stored accounts`);
        return false;
      }

      console.log('Found user in stored accounts at index:', userIndex);

      // Remove the user from the accounts
      storedAccounts.splice(userIndex, 1);

      // Update stored accounts
      storeData(STORAGE_KEYS.ASSISTANT_ACCOUNTS, storedAccounts);
      console.log('Mock data: User deleted and storage updated successfully');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return true;
    }

    // If not using mock data, proceed with API calls
    try {
      // Try multiple endpoints based on user type
      let success = false;

      try {
        // Try the appropriate endpoint based on user type
        if (userType === 'assistant') {
          console.log(`Sending DELETE request to /api/auth/assistants/${id}/`);
          try {
            await api.delete(`/api/auth/assistants/${id}/`);
            console.log('SUCCESSFUL: User deleted via assistants endpoint');
            success = true;
          } catch (error: unknown) {
            const assistantError = error as ApiError;
            console.log(`Assistant endpoint failed: ${assistantError.message || 'Unknown error'}`);
            console.log('Error details:', assistantError.response?.data || 'No response data');

            // Check if it's a 404 error (assistant not found)
            if (assistantError.response?.status === 404) {
              console.log('Assistant not found, trying users endpoint');
              await api.delete(`/api/auth/users/${id}/`);
              console.log('SUCCESSFUL: Assistant deleted via users endpoint');
              success = true;
            } else {
              // For other errors, rethrow
              throw assistantError;
            }
          }
        } else if (userType === 'staff') {
          console.log(`Sending DELETE request to /api/auth/staff/${id}/`);
          // Make sure the staff endpoint exists and is correctly implemented
          try {
            await api.delete(`/api/auth/staff/${id}/`);
            console.log('SUCCESSFUL: User deleted via staff endpoint');
            success = true;
          } catch (error: unknown) {
            const staffError = error as ApiError;
            console.log(`Staff endpoint failed: ${staffError.message || 'Unknown error'}`);
            console.log('Error details:', staffError.response?.data || 'No response data');

            // Check if it's a 404 error (staff not found)
            if (staffError.response?.status === 404) {
              console.log('Staff not found, trying users endpoint');
              try {
                await api.delete(`/api/auth/users/${id}/`);
                console.log('SUCCESSFUL: Staff deleted via users endpoint');
                success = true;
              } catch (error: unknown) {
                const userError = error as ApiError;
                console.log(`Users endpoint failed: ${userError.message || 'Unknown error'}`);
                console.log('Error details:', userError.response?.data || 'No response data');
                throw error;
              }
            } else {
              // For other errors, rethrow
              throw staffError;
            }
          }
        } else {
          // For other user types or if type is not specified, try the general endpoint
          console.log(`Sending DELETE request to /api/auth/users/${id}/`);
          await api.delete(`/api/auth/users/${id}/`);
          console.log('SUCCESSFUL: User deleted via users endpoint');
          success = true;
        }
      } catch (error: unknown) {
        const apiError = error as ApiError;
        console.log(`DELETE FAILED: Error:`, apiError.message);
        console.log('Error details:', apiError.response?.data || 'No response data');

        // Try a fallback approach with the me endpoint
        try {
          console.log(`FALLBACK: Sending DELETE request to /api/auth/me/`);
          await api.delete(`/api/auth/me/`);
          console.log('FALLBACK SUCCESSFUL: User deleted via me endpoint');
          success = true;
        } catch (error: unknown) {
          const fallbackError = error as ApiError;
          console.log(`FALLBACK FAILED: Error:`, fallbackError.message);
          console.log('Error details:', fallbackError.response?.data || 'No response data');

          // If all attempts failed, throw the original error
          throw error;
        }
      }

      // If all attempts succeeded, update the cache
      if (success) {
        try {
          const cachedUsers = getFromCache<UserAccount[]>(
            STORAGE_KEYS.REAL_USER_ACCOUNTS,
            STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS
          );

          if (cachedUsers) {
            // Remove the user from the cached users
            const updatedUsers = cachedUsers.filter(user => user.id !== id);

            // Update the cache
            storeInCache(
              STORAGE_KEYS.REAL_USER_ACCOUNTS,
              STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS,
              updatedUsers
            );

            console.log('Updated cache after user deletion');
          }
        } catch (cacheError) {
          console.error('Error updating cache after user deletion:', cacheError);
        }
      }

      console.log('=== DELETE SUCCESSFUL ===');
      return success;
    } catch (error: unknown) {
      const apiError = error as ApiError;
      console.error('=== ERROR DELETING USER ACCOUNT ===');
      console.error('Error message:', apiError.message);

      if (apiError.response) {
        console.error('Response status:', apiError.response.status);
        console.error('Response data:', apiError.response.data);
      }

      throw error;
    }
  },

  async getSubscriptionPackages(): Promise<SubscriptionPackage[]> {
    console.log('Mock getSubscriptionPackages called');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored packages
    const storedPackages = getStoredData<SubscriptionPackage[]>('mock_subscription_packages', mockSubscriptionPackages);
    console.log('Retrieved subscription packages from storage:', storedPackages);

    return storedPackages;
  },

  async getCurrentSubscription(): Promise<SubscriptionPackage> {
    console.log('Mock getCurrentSubscription called');
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored packages
    const storedPackages = getStoredData<SubscriptionPackage[]>('mock_subscription_packages', mockSubscriptionPackages);

    const currentPackage = storedPackages.find(pkg => pkg.is_current);
    console.log('Retrieved current subscription from storage:', currentPackage);

    return currentPackage || storedPackages[0];
  },

  async updateSubscription(packageId: string): Promise<SubscriptionPackage> {
    console.log(`Mock updateSubscription called with packageId: ${packageId}`);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get stored packages
    const storedPackages = getStoredData<SubscriptionPackage[]>('mock_subscription_packages', mockSubscriptionPackages);

    // Reset all packages to not current
    storedPackages.forEach(pkg => pkg.is_current = false);

    // Find the package to update
    const packageIndex = storedPackages.findIndex(pkg => pkg.id === packageId);
    if (packageIndex === -1) throw new Error('Subscription package not found');

    // Set the selected package as current
    storedPackages[packageIndex].is_current = true;

    // Update stored packages
    storeData('mock_subscription_packages', storedPackages);
    console.log('Subscription updated and stored successfully');

    return storedPackages[packageIndex];
  },

  async getUserCounts(): Promise<{ assistants: number; patients: number; staff: number }> {
    console.log('Fetching user counts');

    try {
      // Calculate counts from user accounts
      const users = await this.getUserAccounts();
      const assistantCount = users.filter(user => user.user_type === 'assistant').length;
      const patientCount = users.filter(user => user.user_type === 'patient').length;
      const staffCount = users.filter(user => user.user_type === 'staff').length;

      console.log('User counts calculated:', {
        assistants: assistantCount,
        patients: patientCount,
        staff: staffCount
      });

      return {
        assistants: assistantCount,
        patients: patientCount,
        staff: staffCount
      };
    } catch (error) {
      console.error('Error calculating user counts:', error);

      // If all else fails, return default counts
      return {
        assistants: 0,
        patients: 0,
        staff: 0
      };
    }
  }
};

export default userManagementService;
