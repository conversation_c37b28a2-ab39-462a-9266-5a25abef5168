'use client';
import Icon from '@mdi/react';
import { mdiAccountSwitch } from '@mdi/js';
import React, { useState, useMemo } from 'react';
import {
  Paper,
  Title,
  Group,
  Text,
  Button,
  TextInput,
  Modal,
  Table,
  ActionIcon,
  Alert,
  Stack,
  Card,
  Pagination,
  CloseButton,
  ScrollArea
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {

  IconSearch,
  IconAlertTriangle,
  IconCheck,
  IconX
} from '@tabler/icons-react';
import { useDebouncedValue } from '@mantine/hooks';

// Import du type Patient depuis les données existantes
import { Patient, Patients } from '@/data/patients';

// Interface pour les patients sélectionnés
interface SelectedPatient {
  id: string;
  fullName: string;
  dateNaissance: string;
  age: number;
  cin: string;
  telephone: string;
  ville: string;
  assurance: string;
}

const FusionDesPatients: React.FC = () => {
  // États pour les patients sélectionnés
  const [primaryPatient, setPrimaryPatient] = useState<SelectedPatient | null>(null);
  const [secondaryPatient, setSecondaryPatient] = useState<SelectedPatient | null>(null);

  // États pour les modales
  const [primaryModalOpened, { open: openPrimaryModal, close: closePrimaryModal }] = useDisclosure(false);
  const [secondaryModalOpened, { open: openSecondaryModal, close: closeSecondaryModal }] = useDisclosure(false);

  // États pour la recherche dans les modales
  const [primarySearch, setPrimarySearch] = useState('');
  const [secondarySearch, setSecondarySearch] = useState('');
  const [debouncedPrimarySearch] = useDebouncedValue(primarySearch, 200);
  const [debouncedSecondarySearch] = useDebouncedValue(secondarySearch, 200);

  // États pour la pagination
  const [primaryPage, setPrimaryPage] = useState(1);
  const [secondaryPage, setSecondaryPage] = useState(1);
  const pageSize = 10;

  // Fonction pour calculer l'âge
  const calculateAge = (dateNaissance: string): number => {
    const today = new Date();
    const birthDate = new Date(dateNaissance);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Fonction pour convertir un Patient en SelectedPatient
  const convertToSelectedPatient = (patient: Patient): SelectedPatient => ({
    id: patient.id,
    fullName: `${patient.firstName} ${patient.lastName}`,
    dateNaissance: patient.dateNaissance,
    age: calculateAge(patient.dateNaissance),
    cin: patient.cin,
    telephone: patient.telephone,
    ville: patient.ville,
    assurance: patient.assurance
  });

  // Filtrage des patients pour la modale principale
  const filteredPrimaryPatients = useMemo(() => {
    return Patients.filter((patient) => {
      if (!debouncedPrimarySearch) return true;

      const searchText = debouncedPrimarySearch.toLowerCase();
      return (
        `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchText) ||
        patient.cin.toLowerCase().includes(searchText) ||
        patient.telephone.toLowerCase().includes(searchText) ||
        patient.ville.toLowerCase().includes(searchText) ||
        patient.assurance.toLowerCase().includes(searchText)
      );
    }).filter(patient => patient.id !== secondaryPatient?.id); // Exclure le patient secondaire
  }, [debouncedPrimarySearch, secondaryPatient]);

  // Filtrage des patients pour la modale secondaire
  const filteredSecondaryPatients = useMemo(() => {
    return Patients.filter((patient) => {
      if (!debouncedSecondarySearch) return true;

      const searchText = debouncedSecondarySearch.toLowerCase();
      return (
        `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchText) ||
        patient.cin.toLowerCase().includes(searchText) ||
        patient.telephone.toLowerCase().includes(searchText) ||
        patient.ville.toLowerCase().includes(searchText) ||
        patient.assurance.toLowerCase().includes(searchText)
      );
    }).filter(patient => patient.id !== primaryPatient?.id); // Exclure le patient principal
  }, [debouncedSecondarySearch, primaryPatient]);

  // Pagination pour la modale principale
  const paginatedPrimaryPatients = useMemo(() => {
    const startIndex = (primaryPage - 1) * pageSize;
    return filteredPrimaryPatients.slice(startIndex, startIndex + pageSize);
  }, [filteredPrimaryPatients, primaryPage]);

  // Pagination pour la modale secondaire
  const paginatedSecondaryPatients = useMemo(() => {
    const startIndex = (secondaryPage - 1) * pageSize;
    return filteredSecondaryPatients.slice(startIndex, startIndex + pageSize);
  }, [filteredSecondaryPatients, secondaryPage]);

  // Fonction pour sélectionner le patient principal
  const handleSelectPrimaryPatient = (patient: Patient) => {
    setPrimaryPatient(convertToSelectedPatient(patient));
    closePrimaryModal();
    setPrimarySearch('');
    setPrimaryPage(1);
  };

  // Fonction pour sélectionner le patient secondaire
  const handleSelectSecondaryPatient = (patient: Patient) => {
    setSecondaryPatient(convertToSelectedPatient(patient));
    closeSecondaryModal();
    setSecondarySearch('');
    setSecondaryPage(1);
  };

  // Fonction pour supprimer la sélection du patient principal
  const removePrimaryPatient = () => {
    setPrimaryPatient(null);
  };

  // Fonction pour supprimer la sélection du patient secondaire
  const removeSecondaryPatient = () => {
    setSecondaryPatient(null);
  };

  // Fonction pour effectuer la fusion
  const handleFusion = () => {
    if (!primaryPatient || !secondaryPatient) {
      notifications.show({
        title: 'Erreur',
        message: 'Veuillez sélectionner les deux patients avant de procéder à la fusion.',
        color: 'red',
        icon: <IconX size={16} />
      });
      return;
    }

    // Confirmation de la fusion
    const confirmed = window.confirm(
      `Êtes-vous sûr de vouloir fusionner les données du patient "${secondaryPatient.fullName}" avec celles du patient "${primaryPatient.fullName}" ?\n\nCette action est irréversible.`
    );

    if (confirmed) {
      // Ici, vous implémenteriez la logique de fusion réelle
      // Pour l'instant, on simule le succès
      notifications.show({
        title: 'Fusion réussie',
        message: `Les données du patient "${secondaryPatient.fullName}" ont été fusionnées avec celles du patient "${primaryPatient.fullName}".`,
        color: 'green',
        icon: <IconCheck size={16} />
      });

      // Réinitialiser les sélections
      setPrimaryPatient(null);
      setSecondaryPatient(null);
    }
  };

  // Validation du formulaire
  const isFormValid = primaryPatient && secondaryPatient;

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* En-tête */}
      <Paper shadow="none" p="md" className="bg-blue-600 text-white">
        <Group align="center" gap="md">
         <Icon path={mdiAccountSwitch} size={1} />
          <Title order={2} className="text-white">
            Fusion des patients
          </Title>
        </Group>
      </Paper>

      {/* Contenu principal */}
      <div className="flex-1 p-6">
        <Stack gap="lg">
          {/* Messages d'avertissement */}
          <Alert
            icon={<IconAlertTriangle size={16} />}
            title="Attention - Action irréversible"
            color="red"
            variant="light"
          >
            <Stack gap="xs">
              <Text size="sm" fw={600}>
                Cette action est irréversible.
              </Text>
              <Text size="sm">
                Toutes les données des patients source et destination seront fusionnées,
                l&apos;opération est irréversible après cette opération.
              </Text>
            </Stack>
          </Alert>

          {/* Sélection des patients */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Patient principal */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Stack gap="md">
                <Text fw={600} size="lg" c="blue">
                  Patient principal
                </Text>

                <TextInput
                  placeholder="Cliquez sur la loupe pour sélectionner un patient"
                  value={primaryPatient?.fullName || ''}
                  readOnly
                  rightSection={
                    <Group gap="xs">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={openPrimaryModal}
                        size="sm"
                      >
                        <IconSearch size={16} />
                      </ActionIcon>
                      {primaryPatient && (
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={removePrimaryPatient}
                          size="sm"
                        >
                          <IconX size={16} />
                        </ActionIcon>
                      )}
                    </Group>
                  }
                />

                {primaryPatient && (
                  <Card withBorder p="sm" className="bg-blue-50">
                    <Stack gap="xs">
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Nom complet:</Text>
                        <Text size="sm">{primaryPatient.fullName}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Date de naissance:</Text>
                        <Text size="sm">{primaryPatient.dateNaissance}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Âge:</Text>
                        <Text size="sm">{primaryPatient.age} ans</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>CIN:</Text>
                        <Text size="sm">{primaryPatient.cin}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Téléphone:</Text>
                        <Text size="sm">{primaryPatient.telephone}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Ville:</Text>
                        <Text size="sm">{primaryPatient.ville}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Assurance:</Text>
                        <Text size="sm">{primaryPatient.assurance}</Text>
                      </Group>
                    </Stack>
                  </Card>
                )}
              </Stack>
            </Card>

            {/* Patient à supprimer */}
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Stack gap="md">
                <Text fw={600} size="lg" c="red">
                  Patient à supprimer
                </Text>

                <TextInput
                  placeholder="Cliquez sur la loupe pour sélectionner un patient"
                  value={secondaryPatient?.fullName || ''}
                  readOnly
                  rightSection={
                    <Group gap="xs">
                      <ActionIcon
                        variant="subtle"
                        color="blue"
                        onClick={openSecondaryModal}
                        size="sm"
                      >
                        <IconSearch size={16} />
                      </ActionIcon>
                      {secondaryPatient && (
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={removeSecondaryPatient}
                          size="sm"
                        >
                          <IconX size={16} />
                        </ActionIcon>
                      )}
                    </Group>
                  }
                />

                {secondaryPatient && (
                  <Card withBorder p="sm" className="bg-red-50">
                    <Stack gap="xs">
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Nom complet:</Text>
                        <Text size="sm">{secondaryPatient.fullName}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Date de naissance:</Text>
                        <Text size="sm">{secondaryPatient.dateNaissance}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Âge:</Text>
                        <Text size="sm">{secondaryPatient.age} ans</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>CIN:</Text>
                        <Text size="sm">{secondaryPatient.cin}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Téléphone:</Text>
                        <Text size="sm">{secondaryPatient.telephone}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Ville:</Text>
                        <Text size="sm">{secondaryPatient.ville}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Assurance:</Text>
                        <Text size="sm">{secondaryPatient.assurance}</Text>
                      </Group>
                    </Stack>
                  </Card>
                )}
              </Stack>
            </Card>
          </div>

          {/* Bouton de fusion */}
          <Group justify="center" mt="xl">
            <Button
              size="lg"
              color="red"
              variant="filled"
              disabled={!isFormValid}
              onClick={handleFusion}
              leftSection={<Icon path={mdiAccountSwitch} size={1} />}
            >
              Fusionner
            </Button>
          </Group>
        </Stack>
      </div>

      {/* Modale de sélection du patient principal */}
      <Modal
        opened={primaryModalOpened}
        onClose={closePrimaryModal}
        title="Liste des patients - Sélection du patient principal"
        size="xl"
        centered
      >
        <Stack gap="md">
          <TextInput
            placeholder="Rechercher un patient..."
            value={primarySearch}
            onChange={(e) => setPrimarySearch(e.target.value)}
            leftSection={<IconSearch size={16} />}
            rightSection={
              primarySearch && (
                <CloseButton
                  size="sm"
                  onClick={() => setPrimarySearch('')}
                />
              )
            }
          />

          <ScrollArea h={400}>
            <Table striped highlightOnHover withTableBorder withColumnBorders>
              <Table.Thead>
                <Table.Tr className="bg-blue-50">
                  <Table.Th>Date d.</Table.Th>
                  <Table.Th>Nom</Table.Th>
                  <Table.Th>Prénom</Table.Th>
                  <Table.Th>Date d.</Table.Th>
                  <Table.Th>Âge</Table.Th>
                  <Table.Th>CNIE</Table.Th>
                  <Table.Th>Dernière</Table.Th>
                  <Table.Th>Téléphone</Table.Th>
                  <Table.Th>Ville</Table.Th>
                  <Table.Th>Assurance</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {paginatedPrimaryPatients.length > 0 ? (
                  paginatedPrimaryPatients.map((patient) => (
                    <Table.Tr
                      key={patient.id}
                      className="hover:bg-blue-50 cursor-pointer"
                      onClick={() => handleSelectPrimaryPatient(patient)}
                    >
                      <Table.Td className="text-sm">{patient.dateCreation}</Table.Td>
                      <Table.Td className="text-sm font-medium">{patient.lastName}</Table.Td>
                      <Table.Td className="text-sm">{patient.firstName}</Table.Td>
                      <Table.Td className="text-sm">{patient.dateNaissance}</Table.Td>
                      <Table.Td className="text-sm">{calculateAge(patient.dateNaissance)} ans</Table.Td>
                      <Table.Td className="text-sm">{patient.cin}</Table.Td>
                      <Table.Td className="text-sm">{patient.dernierVisite}</Table.Td>
                      <Table.Td className="text-sm">{patient.telephone}</Table.Td>
                      <Table.Td className="text-sm">{patient.ville}</Table.Td>
                      <Table.Td className="text-sm">{patient.assurance}</Table.Td>
                    </Table.Tr>
                  ))
                ) : (
                  <Table.Tr>
                    <Table.Td colSpan={10} className="text-center text-gray-500 py-8">
                      Aucun patient trouvé
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {filteredPrimaryPatients.length > pageSize && (
            <Group justify="center">
              <Pagination
                total={Math.ceil(filteredPrimaryPatients.length / pageSize)}
                value={primaryPage}
                onChange={setPrimaryPage}
                size="sm"
              />
            </Group>
          )}
        </Stack>
      </Modal>

      {/* Modale de sélection du patient secondaire */}
      <Modal
        opened={secondaryModalOpened}
        onClose={closeSecondaryModal}
        title="Liste des patients - Sélection du patient à supprimer"
        size="xl"
        centered
      >
        <Stack gap="md">
          <TextInput
            placeholder="Rechercher un patient..."
            value={secondarySearch}
            onChange={(e) => setSecondarySearch(e.target.value)}
            leftSection={<IconSearch size={16} />}
            rightSection={
              secondarySearch && (
                <CloseButton
                  size="sm"
                  onClick={() => setSecondarySearch('')}
                />
              )
            }
          />

          <ScrollArea h={400}>
            <Table striped highlightOnHover withTableBorder withColumnBorders>
              <Table.Thead>
                <Table.Tr className="bg-red-50">
                  <Table.Th>Date d.</Table.Th>
                  <Table.Th>Nom</Table.Th>
                  <Table.Th>Prénom</Table.Th>
                  <Table.Th>Date d.</Table.Th>
                  <Table.Th>Âge</Table.Th>
                  <Table.Th>CNIE</Table.Th>
                  <Table.Th>Dernière</Table.Th>
                  <Table.Th>Téléphone</Table.Th>
                  <Table.Th>Ville</Table.Th>
                  <Table.Th>Assurance</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {paginatedSecondaryPatients.length > 0 ? (
                  paginatedSecondaryPatients.map((patient) => (
                    <Table.Tr
                      key={patient.id}
                      className="hover:bg-red-50 cursor-pointer"
                      onClick={() => handleSelectSecondaryPatient(patient)}
                    >
                      <Table.Td className="text-sm">{patient.dateCreation}</Table.Td>
                      <Table.Td className="text-sm font-medium">{patient.lastName}</Table.Td>
                      <Table.Td className="text-sm">{patient.firstName}</Table.Td>
                      <Table.Td className="text-sm">{patient.dateNaissance}</Table.Td>
                      <Table.Td className="text-sm">{calculateAge(patient.dateNaissance)} ans</Table.Td>
                      <Table.Td className="text-sm">{patient.cin}</Table.Td>
                      <Table.Td className="text-sm">{patient.dernierVisite}</Table.Td>
                      <Table.Td className="text-sm">{patient.telephone}</Table.Td>
                      <Table.Td className="text-sm">{patient.ville}</Table.Td>
                      <Table.Td className="text-sm">{patient.assurance}</Table.Td>
                    </Table.Tr>
                  ))
                ) : (
                  <Table.Tr>
                    <Table.Td colSpan={10} className="text-center text-gray-500 py-8">
                      Aucun patient trouvé
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {filteredSecondaryPatients.length > pageSize && (
            <Group justify="center">
              <Pagination
                total={Math.ceil(filteredSecondaryPatients.length / pageSize)}
                value={secondaryPage}
                onChange={setSecondaryPage}
                size="sm"
              />
            </Group>
          )}
        </Stack>
      </Modal>
    </div>
  );
};

export default FusionDesPatients;
