// frontend/dental_medicine/src/components/content/dental/shared/TreatmentDemo.tsx

import React, { useState } from 'react';
import { useTreatmentManager } from './useTreatmentManager';
import TreatmentConflictDialog from './TreatmentConflictDialog';
import { TREATMENTS } from './treatmentCompatibility';
import { testSmartSystem, testPathCalculation, testPerformance } from './testSmartSystem';
import { testLayerStackingFix } from './pathUtils';

export const TreatmentDemo: React.FC = () => {
  const {
    applyTreatment,
    removeTreatment,
    getCurrentTreatments,
    conflicts,
    isConflictDialogOpen,
    pendingTreatment,
    resolveConflict,
    closeConflictDialog
  } = useTreatmentManager();

  const [selectedTooth, setSelectedTooth] = useState<string>('1');

  const handleApplyTreatment = async (treatmentId: string) => {
    const result = await applyTreatment(treatmentId, selectedTooth);

    if (result.success) {
      console.log(`✅ Traitement ${treatmentId} appliqué avec succès!`);
      console.log('Paths à afficher:', result.pathsToShow);
      console.log('Paths à cacher:', result.pathsToHide);
    }
  };

  const handleRemoveTreatment = (treatmentId: string) => {
    const result = removeTreatment(treatmentId, selectedTooth);
    console.log(`🗑️ Traitement ${treatmentId} supprimé`);
    console.log('Paths à afficher:', result.pathsToShow);
    console.log('Paths à cacher:', result.pathsToHide);
  };

  const currentTreatments = getCurrentTreatments(selectedTooth);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        🦷 Démonstration du Système de Traitements Multiples
      </h2>

      {/* Sélection de dent */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-blue-800">Sélection de Dent</h3>
        <div className="flex space-x-2">
          {Array.from({ length: 32 }, (_, i) => (i + 1).toString()).map(toothNum => (
            <button
              key={toothNum}
              onClick={() => setSelectedTooth(toothNum)}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedTooth === toothNum
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-blue-600 border border-blue-300 hover:bg-blue-100'
              }`}
            >
              {toothNum}
            </button>
          ))}
        </div>
      </div>

      {/* Traitements actuels */}
      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-green-800">
          Traitements Appliqués sur la Dent {selectedTooth}
        </h3>
        {currentTreatments.length > 0 ? (
          <div className="space-y-2">
            {currentTreatments.map(treatmentId => {
              const treatment = TREATMENTS[treatmentId];
              return (
                <div key={treatmentId} className="flex items-center justify-between bg-white p-3 rounded-md border border-green-200">
                  <div>
                    <span className="font-medium text-green-700">{treatment?.name || treatmentId}</span>
                    <span className="text-sm text-gray-500 ml-2">({treatment?.category})</span>
                  </div>
                  <button
                    onClick={() => handleRemoveTreatment(treatmentId)}
                    className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
                  >
                    Supprimer
                  </button>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-gray-500 italic">Aucun traitement appliqué</p>
        )}
      </div>

      {/* Traitements disponibles par catégorie */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(
          Object.values(TREATMENTS).reduce((acc, treatment) => {
            if (!acc[treatment.category]) acc[treatment.category] = [];
            acc[treatment.category].push(treatment);
            return acc;
          }, {} as Record<string, typeof TREATMENTS[keyof typeof TREATMENTS][]>)
        ).map(([category, treatments]) => (
          <div key={category} className="p-4 bg-gray-50 rounded-lg">
            <h4 className="text-md font-semibold mb-3 text-gray-700 capitalize">
              {category === 'esthetic' && '🎨 Esthétique'}
              {category === 'preventive' && '🛡️ Préventif'}
              {category === 'restorative' && '🔧 Restauratif'}
              {category === 'endodontic' && '🦷 Endodontique'}
              {category === 'prosthetic' && '🔧 Prothétique'}
              {category === 'surgical' && '⚔️ Chirurgical'}
              {category === 'orthodontic' && '📐 Orthodontique'}
            </h4>
            <div className="space-y-2">
              {treatments.map(treatment => (
                <button
                  key={treatment.id}
                  onClick={() => handleApplyTreatment(treatment.id)}
                  disabled={currentTreatments.includes(treatment.id)}
                  className={`w-full px-3 py-2 text-sm rounded-md transition-colors text-left ${
                    currentTreatments.includes(treatment.id)
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-100'
                  }`}
                >
                  {treatment.name}
                  {treatment.isDestructive && ' ⚠️'}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Dialog de conflit */}
      <TreatmentConflictDialog
        isOpen={isConflictDialogOpen}
        conflicts={conflicts}
        treatmentName={pendingTreatment ? TREATMENTS[pendingTreatment.treatmentId]?.name || pendingTreatment.treatmentId : ''}
        toothNumber={pendingTreatment?.svgId || ''}
        onResolve={resolveConflict}
        onClose={closeConflictDialog}
      />

      {/* Tests automatiques */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">🧪 Tests Automatiques</h3>
        <div className="flex space-x-3 mb-3">
          <button
            onClick={() => {
              console.clear();
              testSmartSystem();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
          >
            Test Complet
          </button>
          <button
            onClick={() => {
              console.clear();
              testPathCalculation();
            }}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
          >
            Test Paths
          </button>
          <button
            onClick={() => {
              console.clear();
              testPerformance();
            }}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm"
          >
            Test Performance
          </button>
          <button
            onClick={() => {
              console.clear();
              testLayerStackingFix();
            }}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
          >
            🔧 Test Empilement
          </button>
        </div>
        <p className="text-sm text-gray-600">
          📝 Les résultats des tests s'affichent dans la console du navigateur (F12)
        </p>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-yellow-800">💡 Instructions</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• <strong>Sélectionnez une dent</strong> en cliquant sur son numéro</li>
          <li>• <strong>Appliquez des traitements</strong> en cliquant sur les boutons</li>
          <li>• <strong>Observez les conflits</strong> quand vous essayez des combinaisons incompatibles</li>
          <li>• <strong>Testez les séquences</strong> : extraction → greffe osseuse → implant → couronne</li>
          <li>• <strong>Essayez les compatibilités</strong> : nettoyage + fluorure + scellant</li>
          <li>• <strong>Lancez les tests</strong> pour vérifier le fonctionnement automatique</li>
        </ul>
      </div>
    </div>
  );
};
