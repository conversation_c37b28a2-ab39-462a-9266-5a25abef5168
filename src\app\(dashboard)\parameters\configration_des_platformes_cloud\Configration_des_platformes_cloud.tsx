'use client';
import { Tabs } from '@mantine/core';
import { IconPhoto, IconMessageCircle,  } from '@tabler/icons-react';
import DataMigrationCard from '@/components/settings/DataMigrationCard';
import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Text,
  Button,
  TextInput,
  Select,
  Modal,
  Stack,
  Card,
  ActionIcon,
  Tooltip
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useForm } from '@mantine/form';
import { DatePickerInput } from '@mantine/dates';
import {
  IconCloud,
  IconPlus,
  IconList,
  IconAlertCircle,
  IconTrash,
  IconEdit,
  IconX
} from '@tabler/icons-react';

// Types pour les plateformes cloud
interface CloudPlatform {
  id: number;
  title: string;
  dns: string;
  token: string;
  tokenType: 'API_KEY' | 'BEARER_TOKEN' | 'BASIC_AUTH';
  validityDuration: Date | null;
  createdAt: Date;
}

// Options pour le dropdown Titre
const titleOptions = [
  { value: 'PRISMA_SMART', label: 'PRISMA_SMART' },
  { value: 'AWS_CLOUD', label: 'AWS Cloud' },
  { value: 'AZURE_CLOUD', label: 'Azure Cloud' },
  { value: 'GOOGLE_CLOUD', label: 'Google Cloud' },
  { value: 'CUSTOM_PLATFORM', label: 'Plateforme personnalisée' }
];

// Options pour le dropdown Token-type
const tokenTypeOptions = [
  { value: 'API_KEY', label: 'API Key' },
  { value: 'BEARER_TOKEN', label: 'Bearer token' },
  { value: 'BASIC_AUTH', label: 'Basic Auth' }
];

const Configration_des_platformes_cloud: React.FC = () => {
  // États pour les plateformes
  const [platforms, setPlatforms] = useState<CloudPlatform[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<CloudPlatform | null>(null);

  // États pour la modale
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const [editingPlatform, setEditingPlatform] = useState<CloudPlatform | null>(null);

  // Formulaire pour la modale
  const form = useForm({
    initialValues: {
      title: '',
      dns: '',
      token: '',
      tokenType: 'API_KEY' as const,
      validityDuration: null as Date | null
    },
    validate: {
      title: (value) => (!value ? 'Le titre est requis' : null),
      dns: (value) => (!value ? 'Le DNS est requis' : null),
      token: (value) => (!value ? 'Le token est requis' : null),
      tokenType: (value) => (!value ? 'Le type de token est requis' : null)
    }
  });

  // Fonction pour ouvrir la modale (nouveau ou édition)
  const handleOpenModal = (platform?: CloudPlatform) => {
    if (platform) {
      setEditingPlatform(platform);
      form.setValues({
        title: platform.title,
        dns: platform.dns,
        token: platform.token,
        tokenType: platform.tokenType,
        validityDuration: platform.validityDuration
      });
    } else {
      setEditingPlatform(null);
      form.reset();
    }
    openModal();
  };

  // Fonction pour fermer la modale
  const handleCloseModal = () => {
    closeModal();
    setEditingPlatform(null);
    form.reset();
  };

  // Fonction pour sauvegarder une plateforme
  const handleSavePlatform = (values: typeof form.values) => {
    if (editingPlatform) {
      // Modification d'une plateforme existante
      setPlatforms(prev => prev.map(p =>
        p.id === editingPlatform.id
          ? { ...p, ...values }
          : p
      ));
      notifications.show({
        title: 'Succès',
        message: 'Plateforme modifiée avec succès',
        color: 'green'
      });
    } else {
      // Création d'une nouvelle plateforme
      const newPlatform: CloudPlatform = {
        id: Date.now(),
        ...values,
        createdAt: new Date()
      };
      setPlatforms(prev => [...prev, newPlatform]);
      notifications.show({
        title: 'Succès',
        message: 'Plateforme ajoutée avec succès',
        color: 'green'
      });
    }
    handleCloseModal();
  };

  // Fonction pour supprimer une plateforme
  const handleDeletePlatform = (platform: CloudPlatform) => {
    const confirmed = window.confirm(`Êtes-vous sûr de vouloir supprimer la plateforme "${platform.title}" ?`);
    if (confirmed) {
      setPlatforms(prev => prev.filter(p => p.id !== platform.id));
      if (selectedPlatform?.id === platform.id) {
        setSelectedPlatform(null);
      }
      notifications.show({
        title: 'Succès',
        message: 'Plateforme supprimée avec succès',
        color: 'green'
      });
    }
  };

  return (

     <Tabs defaultValue="DataMigration">
      <Tabs.List mb={20}>
        <Tabs.Tab value="DataMigration" leftSection={<IconPhoto size={12} />}>
          Migration des données
        </Tabs.Tab>
        <Tabs.Tab value="cloud" leftSection={<IconMessageCircle size={12} />}>
          Cloud
        </Tabs.Tab>
      
      </Tabs.List>

      <Tabs.Panel value="DataMigration">
          <DataMigrationCard />
      </Tabs.Panel>

      <Tabs.Panel value="cloud">
        <div className="flex flex-col h-full bg-gray-50">
      {/* En-tête principal */}
      <Paper shadow="none" p="md" className="bg-blue-600 text-white">
        <Group justify="space-between" align="center">
          <Group align="center" gap="md">
            <IconCloud size={24} />
            <Title order={2} className="text-white">
              Plateformes cloud
            </Title>
          </Group>
          <Button
            variant="subtle"
            color="white"
            leftSection={<IconPlus size={16} />}
            onClick={() => handleOpenModal()}
            className="text-white hover:bg-white/10"
          >
            Plateforme
          </Button>
        </Group>
      </Paper>

      {/* Contenu principal avec sidebar */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar gauche */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header de la sidebar */}
          <Paper shadow="none" p="md" className="bg-blue-500 text-white">
            <Group align="center" gap="md">
              <IconList size={20} />
              <Title order={4} className="text-white">
                Liste des plateformes
              </Title>
            </Group>
          </Paper>

          {/* Contenu de la sidebar */}
          <div className="flex-1 p-4 overflow-y-auto">
            {platforms.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                <IconAlertCircle size={48} className="mb-4 text-gray-400" />
                <Text size="sm" className="text-center">
                  Aucun élément trouvé.
                </Text>
              </div>
            ) : (
              <Stack gap="xs">
                {platforms.map((platform) => (
                  <Card
                    key={platform.id}
                    padding="sm"
                    radius="md"
                    className={`cursor-pointer border transition-colors ${
                      selectedPlatform?.id === platform.id
                        ? 'bg-blue-50 border-blue-300'
                        : 'hover:bg-gray-50 border-gray-200'
                    }`}
                    onClick={() => setSelectedPlatform(platform)}
                  >
                    <Group justify="space-between" align="center">
                      <div className="flex-1">
                        <Text size="sm" fw={500} className="mb-1">
                          {platform.title}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {platform.dns}
                        </Text>
                      </div>
                      <Group gap="xs">
                        <Tooltip label="Modifier">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenModal(platform);
                            }}
                          >
                            <IconEdit size={14} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Supprimer">
                          <ActionIcon
                            variant="subtle"
                            color="red"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeletePlatform(platform);
                            }}
                          >
                            <IconTrash size={14} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Group>
                  </Card>
                ))}
              </Stack>
            )}
          </div>
        </div>

        {/* Zone de contenu principal */}
        <div className="flex-1 bg-white">
          {selectedPlatform ? (
            <div className="p-6">
              <Stack gap="lg">
                <Group justify="space-between" align="center">
                  <Title order={3}>Détails de la plateforme</Title>
                  <Group gap="sm">
                    <Button
                      variant="outline"
                      leftSection={<IconEdit size={16} />}
                      onClick={() => handleOpenModal(selectedPlatform)}
                    >
                      Modifier
                    </Button>
                    <Button
                      variant="outline"
                      color="red"
                      leftSection={<IconTrash size={16} />}
                      onClick={() => handleDeletePlatform(selectedPlatform)}
                    >
                      Supprimer
                    </Button>
                  </Group>
                </Group>

                <Card withBorder padding="lg" radius="md">
                  <Stack gap="md">
                    <Group>
                      <Text fw={600} size="sm" className="w-32">Titre:</Text>
                      <Text size="sm">{selectedPlatform.title}</Text>
                    </Group>
                    <Group>
                      <Text fw={600} size="sm" className="w-32">DNS:</Text>
                      <Text size="sm" className="break-all">{selectedPlatform.dns}</Text>
                    </Group>
                    <Group>
                      <Text fw={600} size="sm" className="w-32">Token:</Text>
                      <Text size="sm" className="break-all font-mono bg-gray-100 px-2 py-1 rounded">
                        {selectedPlatform.token.substring(0, 20)}...
                      </Text>
                    </Group>
                    <Group>
                      <Text fw={600} size="sm" className="w-32">Type de token:</Text>
                      <Text size="sm">{selectedPlatform.tokenType}</Text>
                    </Group>
                    <Group>
                      <Text fw={600} size="sm" className="w-32">Validité:</Text>
                      <Text size="sm">
                        {selectedPlatform.validityDuration
                          ? selectedPlatform.validityDuration.toLocaleDateString()
                          : 'Non définie'
                        }
                      </Text>
                    </Group>
                    <Group>
                      <Text fw={600} size="sm" className="w-32">Créé le:</Text>
                      <Text size="sm">{selectedPlatform.createdAt.toLocaleDateString()}</Text>
                    </Group>
                  </Stack>
                </Card>
              </Stack>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <IconAlertCircle size={64} className="mb-4 text-gray-400" />
              <Text size="lg" fw={500} className="mb-2">
                Aucun détail trouvé.
              </Text>
              <Text size="sm" className="text-center">
                Sélectionnez une plateforme dans la liste pour voir ses détails.
              </Text>
            </div>
          )}
        </div>
      </div>

      {/* Modale de configuration de plateforme */}
      <Modal
        opened={modalOpened}
        onClose={handleCloseModal}
        title={
          <Group align="center" gap="md">
            <IconCloud size={20} className="text-white" />
            <Text c="white" fw={500}>
              {editingPlatform ? 'Modifier la plateforme' : 'Ajouter une plateforme'}
            </Text>
          </Group>
        }
        size="lg"
        centered
        styles={{
          header: {
            backgroundColor: '#339af0',
            color: 'white',
          },
          title: {
            color: 'white',
          },
          close: {
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            },
          },
        }}
      >
        <form onSubmit={form.onSubmit(handleSavePlatform)}>
          <Stack gap="md">
            {/* Titre */}
            <Select
              label="Titre"
              placeholder="Sélectionnez un titre"
              data={titleOptions}
              required
              withAsterisk
              {...form.getInputProps('title')}
            />

            {/* DNS */}
            <TextInput
              label="DNS"
              placeholder="Entrez l'URL DNS"
              required
              withAsterisk
              {...form.getInputProps('dns')}
            />

            {/* Token */}
            <TextInput
              label="Token"
              placeholder="Entrez le token d'authentification"
              required
              withAsterisk
              {...form.getInputProps('token')}
            />

            {/* Token-type et Durée de validité sur la même ligne */}
            <Group grow align="flex-end">
              <Select
                label="Token-type"
                placeholder="Sélectionnez le type"
                data={tokenTypeOptions}
                required
                withAsterisk
                {...form.getInputProps('tokenType')}
              />

              <DatePickerInput
                label="Durée de validité"
                placeholder="Sélectionnez une date"
                {...form.getInputProps('validityDuration')}
                minDate={new Date()}
              />
            </Group>

            {/* Boutons d'action */}
            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={handleCloseModal}
                leftSection={<IconX size={16} />}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                color="red"
                leftSection={<IconCloud size={16} />}
              >
                {editingPlatform ? 'Modifier' : 'Enregistrer'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
      </Tabs.Panel>

      
    </Tabs>

    
  );
};

export default Configration_des_platformes_cloud;
