{"name": "general", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mantine/carousel": "^8.1.1", "@mantine/charts": "^8.1.1", "@mantine/code-highlight": "^8.1.1", "@mantine/core": "^8.1.1", "@mantine/dates": "^8.1.1", "@mantine/dropzone": "^8.1.1", "@mantine/form": "^8.1.1", "@mantine/hooks": "^8.1.1", "@mantine/modals": "^8.1.1", "@mantine/notifications": "^8.1.1", "@mantine/nprogress": "^8.1.1", "@mantine/spotlight": "^8.1.1", "@mantine/tiptap": "^8.1.1", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@tiptap/extension-link": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "axios": "^1.10.0", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel": "^8.5.2", "embla-carousel-react": "^8.5.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-resources-to-backend": "^1.2.1", "lucide-react": "^0.523.0", "mantine-datatable": "^8.1.2", "next": "15.3.4", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-big-calendar": "^1.19.4", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.3", "react-imask": "^7.6.1", "recharts": "^2.15.4", "simplebar-react": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^4", "typescript": "^5"}}