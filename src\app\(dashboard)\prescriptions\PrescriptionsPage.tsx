'use client';

import { useState } from 'react';
import {

  Title,
  Text,
  Paper,
  Group,
  Button,
  Tabs,
  Table,
  Badge,
  Card,
  SimpleGrid,
  ThemeIcon,
  ActionIcon,
  Tooltip,
  TextInput,
  Select,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Checkbox,
  Grid,
  Accordion,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPrescription,
  IconPlus,
  IconSearch,
  IconFilter,
  IconCheck,
  IconPrinter,
  IconSend,
  IconRefresh,
  IconClipboardList,
  IconEdit,

  IconEye,
  IconCalendar,
  IconUser,
  IconPill,
  IconAlertCircle,
  IconFileText,
  IconHistory,
} from '@tabler/icons-react';
import { DatePickerInput } from '@mantine/dates';

export default function PrescriptionsPage() {
  const [activeTab, setActiveTab] = useState<string | null>('active');
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedPrescription, setSelectedPrescription] = useState<any>(null);

  // Mock data for prescriptions
  const prescriptions = [
    {
      id: 'RX-001',
      patient: '<PERSON> Doe',
      medication: 'Amoxicillin 500mg',
      dosage: '1 tablet',
      frequency: 'Three times daily',
      duration: '10 days',
      date: '2023-05-15',
      status: 'active',
      refills: 0
    },
    {
      id: 'RX-002',
      patient: 'Jane Smith',
      medication: 'Lisinopril 10mg',
      dosage: '1 tablet',
      frequency: 'Once daily',
      duration: '30 days',
      date: '2023-05-18',
      status: 'active',
      refills: 2
    },
    {
      id: 'RX-003',
      patient: 'Robert Johnson',
      medication: 'Metformin 500mg',
      dosage: '1 tablet',
      frequency: 'Twice daily',
      duration: '30 days',
      date: '2023-05-20',
      status: 'expired',
      refills: 0
    },
    {
      id: 'RX-004',
      patient: 'Emily Davis',
      medication: 'Ibuprofen 400mg',
      dosage: '1 tablet',
      frequency: 'As needed for pain',
      duration: '7 days',
      date: '2023-05-22',
      status: 'active',
      refills: 0
    },
    {
      id: 'RX-005',
      patient: 'Michael Brown',
      medication: 'Albuterol Inhaler',
      dosage: '2 puffs',
      frequency: 'Every 4-6 hours as needed',
      duration: '30 days',
      date: '2023-05-25',
      status: 'active',
      refills: 1
    },
  ];

  // Mock data for medication history
  const medicationHistory = [
    {
      id: 'MH-001',
      patient: 'John Doe',
      medication: 'Amoxicillin 500mg',
      startDate: '2023-01-15',
      endDate: '2023-01-25',
      reason: 'Bacterial infection',
      notes: 'Patient reported improvement after 3 days'
    },
    {
      id: 'MH-002',
      patient: 'Jane Smith',
      medication: 'Lisinopril 5mg',
      startDate: '2023-02-10',
      endDate: '2023-05-10',
      reason: 'Hypertension',
      notes: 'Dosage increased to 10mg on 2023-05-10'
    },
    {
      id: 'MH-003',
      patient: 'Robert Johnson',
      medication: 'Metformin 250mg',
      startDate: '2023-03-05',
      endDate: '2023-04-05',
      reason: 'Type 2 Diabetes',
      notes: 'Dosage increased to 500mg on 2023-04-05'
    },
  ];

  const handleCreatePrescription = () => {
    setSelectedPrescription(null);
    open();
  };

  const handleViewPrescription = (prescription: any) => {
    setSelectedPrescription(prescription);
    notifications.show({
      title: 'View Prescription',
      message: `Viewing prescription ${prescription.id} for ${prescription.patient}`,
      color: 'blue',
    });
  };

  const handlePrintPrescription = (prescription: any) => {
    notifications.show({
      title: 'Print Prescription',
      message: `Printing prescription ${prescription.id} for ${prescription.patient}`,
      color: 'blue',
    });
  };

  const handleSendPrescription = (prescription: any) => {
    notifications.show({
      title: 'Send Prescription',
      message: `Prescription ${prescription.id} sent to pharmacy for ${prescription.patient}`,
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  const handleRefillPrescription = (prescription: any) => {
    notifications.show({
      title: 'Refill Prescription',
      message: `Refill processed for prescription ${prescription.id}`,
      color: 'green',
      icon: <IconCheck size={16} />,
    });
  };

  const handleUseTemplate = (template: string) => {
    notifications.show({
      title: 'Template Selected',
      message: `Using ${template} template for new prescription`,
      color: 'blue',
    });
    open();
  };

  const handleEditTemplate = (template: string) => {
    notifications.show({
      title: 'Edit Template',
      message: `Editing ${template} template`,
      color: 'blue',
    });
  };

  const handleSubmit = () => {
    notifications.show({
      title: 'Prescription Created',
      message: 'The prescription has been created successfully.',
      color: 'green',
      icon: <IconCheck size={16} />,
    });
    close();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'expired':
        return 'red';
      case 'pending':
        return 'yellow';
      default:
        return 'gray';
    }
  };

  return (
<>
      <Paper p="xl" radius="md" withBorder mb="xl"w={"100%"} >
        <Group justify="space-between" mb="md">
          <div>
            <Title order={2}>Prescriptions</Title>
            <Text c="dimmed">Manage patient prescriptions and medication history</Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleCreatePrescription}
          >
            Create Prescription
          </Button>
        </Group>

        <SimpleGrid cols={{ base: 1, md: 3 }} mt="xl">
          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="blue">
                <IconPrescription size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Active Prescriptions</Text>
                <Text fw={700} size="xl">24</Text>
              </div>
            </Group>
          </Card>

          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="green">
                <IconRefresh size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Pending Refills</Text>
                <Text fw={700} size="xl">3</Text>
              </div>
            </Group>
          </Card>

          <Card withBorder p="md" radius="md">
            <Group>
              <ThemeIcon size="lg" radius="md" color="orange">
                <IconAlertCircle size={20} />
              </ThemeIcon>
              <div>
                <Text size="xs" c="dimmed">Expiring Soon</Text>
                <Text fw={700} size="xl">5</Text>
              </div>
            </Group>
          </Card>
        </SimpleGrid>
      </Paper>
     <Paper p="xl" radius="md" withBorder mb="xl"w={"100%"} >
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List mb="md">
          <Tabs.Tab value="active" leftSection={<IconPrescription size={16} />}>
            Active Prescriptions
          </Tabs.Tab>
          <Tabs.Tab value="history" leftSection={<IconHistory size={16} />}>
            Medication History
          </Tabs.Tab>
          <Tabs.Tab value="templates" leftSection={<IconFileText size={16} />}>
            Prescription Templates
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="active">
          <Paper p="md" withBorder>
            <Group mb="md">
              <TextInput
                placeholder="Search prescriptions"
                leftSection={<IconSearch size={16} />}
                style={{ flex: 1 }}
              />
              <Select
                placeholder="Filter by status"
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: 'all', label: 'All Status' },
                  { value: 'active', label: 'Active' },
                  { value: 'expired', label: 'Expired' },
                  { value: 'pending', label: 'Pending' },
                ]}
                defaultValue="all"
              />
              <DatePickerInput
                placeholder="Date range"
                leftSection={<IconCalendar size={16} />}
                type="range"
              />
            </Group>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Rx #</Table.Th>
                  <Table.Th>Patient</Table.Th>
                  <Table.Th>Medication</Table.Th>
                  <Table.Th>Dosage</Table.Th>
                  <Table.Th>Date</Table.Th>
                  <Table.Th>Refills</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {prescriptions.map((prescription) => (
                  <Table.Tr key={prescription.id}>
                    <Table.Td>{prescription.id}</Table.Td>
                    <Table.Td>{prescription.patient}</Table.Td>
                    <Table.Td>{prescription.medication}</Table.Td>
                    <Table.Td>{prescription.dosage}</Table.Td>
                    <Table.Td>{prescription.date}</Table.Td>
                    <Table.Td>{prescription.refills}</Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(prescription.status)} style={{ textTransform: 'capitalize' }}>
                        {prescription.status}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <Tooltip label="View">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleViewPrescription(prescription)}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Print">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handlePrintPrescription(prescription)}
                          >
                            <IconPrinter size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Send">
                          <ActionIcon
                            variant="subtle"
                            color="blue"
                            onClick={() => handleSendPrescription(prescription)}
                          >
                            <IconSend size={16} />
                          </ActionIcon>
                        </Tooltip>
                        {prescription.status === 'active' && (
                          <Tooltip label="Refill">
                            <ActionIcon
                              variant="subtle"
                              color="green"
                              onClick={() => handleRefillPrescription(prescription)}
                            >
                              <IconRefresh size={16} />
                            </ActionIcon>
                          </Tooltip>
                        )}
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="history">
          <Paper p="md" withBorder>
            <Group mb="md">
              <Select
                placeholder="Select patient"
                leftSection={<IconUser size={16} />}
                data={[
                  { value: 'all', label: 'All Patients' },
                  { value: 'john-doe', label: 'John Doe' },
                  { value: 'jane-smith', label: 'Jane Smith' },
                  { value: 'robert-johnson', label: 'Robert Johnson' },
                  { value: 'emily-davis', label: 'Emily Davis' },
                  { value: 'michael-brown', label: 'Michael Brown' },
                ]}
                defaultValue="all"
                style={{ flex: 1 }}
              />
              <DatePickerInput
                placeholder="Date range"
                leftSection={<IconCalendar size={16} />}
                type="range"
              />
            </Group>

            <Accordion>
              {medicationHistory.map((history) => (
                <Accordion.Item key={history.id} value={history.id}>
                  <Accordion.Control>
                    <Group>
                      <div>
                        <Text fw={500}>{history.medication}</Text>
                        <Text size="sm" c="dimmed">{history.patient} • {history.startDate} to {history.endDate}</Text>
                      </div>
                    </Group>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <Grid>
                      <Grid.Col span={6}>
                        <Text fw={500}>Reason for Prescription:</Text>
                        <Text>{history.reason}</Text>
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <Text fw={500}>Duration:</Text>
                        <Text>{new Date(history.endDate).getTime() - new Date(history.startDate).getTime()} days</Text>
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Text fw={500}>Notes:</Text>
                        <Text>{history.notes}</Text>
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Group mt="md">
                          <Button variant="outline" leftSection={<IconPrescription size={16} />}>
                            View Original Prescription
                          </Button>
                          <Button variant="outline" leftSection={<IconClipboardList size={16} />}>
                            View Related Medical Records
                          </Button>
                        </Group>
                      </Grid.Col>
                    </Grid>
                  </Accordion.Panel>
                </Accordion.Item>
              ))}
            </Accordion>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="templates">
          <Paper p="md" withBorder>
            <Title order={3} mb="md">Prescription Templates</Title>
            <Text c="dimmed" mb="xl">
              Save time by using prescription templates for common medications
            </Text>

            <SimpleGrid cols={{ base: 1, md: 2 }} spacing="md">
              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="blue">
                    <IconPill size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Amoxicillin for Adults</Text>
                    <Text size="sm" c="dimmed">Common antibiotic prescription</Text>
                  </div>
                </Group>
                <Text size="sm" mb="md">
                  <strong>Medication:</strong> Amoxicillin 500mg<br />
                  <strong>Dosage:</strong> 1 tablet<br />
                  <strong>Frequency:</strong> Three times daily<br />
                  <strong>Duration:</strong> 10 days<br />
                  <strong>Refills:</strong> 0
                </Text>
                <Group>
                  <Button
                    variant="light"
                    leftSection={<IconEdit size={16} />}
                    style={{ flex: 1 }}
                    onClick={() => handleEditTemplate('Amoxicillin for Adults')}
                  >
                    Edit
                  </Button>
                  <Button
                    leftSection={<IconPrescription size={16} />}
                    onClick={() => handleUseTemplate('Amoxicillin for Adults')}
                  >
                    Use Template
                  </Button>
                </Group>
              </Card>

              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="green">
                    <IconPill size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Lisinopril for Hypertension</Text>
                    <Text size="sm" c="dimmed">Blood pressure medication</Text>
                  </div>
                </Group>
                <Text size="sm" mb="md">
                  <strong>Medication:</strong> Lisinopril 10mg<br />
                  <strong>Dosage:</strong> 1 tablet<br />
                  <strong>Frequency:</strong> Once daily<br />
                  <strong>Duration:</strong> 30 days<br />
                  <strong>Refills:</strong> 2
                </Text>
                <Group>
                  <Button
                    variant="light"
                    leftSection={<IconEdit size={16} />}
                    style={{ flex: 1 }}
                    onClick={() => handleEditTemplate('Lisinopril for Hypertension')}
                  >
                    Edit
                  </Button>
                  <Button
                    leftSection={<IconPrescription size={16} />}
                    onClick={() => handleUseTemplate('Lisinopril for Hypertension')}
                  >
                    Use Template
                  </Button>
                </Group>
              </Card>

              <Card withBorder p="md">
                <Group mb="md">
                  <ThemeIcon size="lg" radius="md" color="orange">
                    <IconPill size={20} />
                  </ThemeIcon>
                  <div>
                    <Text fw={500}>Metformin for Diabetes</Text>
                    <Text size="sm" c="dimmed">Diabetes medication</Text>
                  </div>
                </Group>
                <Text size="sm" mb="md">
                  <strong>Medication:</strong> Metformin 500mg<br />
                  <strong>Dosage:</strong> 1 tablet<br />
                  <strong>Frequency:</strong> Twice daily with meals<br />
                  <strong>Duration:</strong> 30 days<br />
                  <strong>Refills:</strong> 3
                </Text>
                <Group>
                  <Button
                    variant="light"
                    leftSection={<IconEdit size={16} />}
                    style={{ flex: 1 }}
                    onClick={() => handleEditTemplate('Metformin for Diabetes')}
                  >
                    Edit
                  </Button>
                  <Button
                    leftSection={<IconPrescription size={16} />}
                    onClick={() => handleUseTemplate('Metformin for Diabetes')}
                  >
                    Use Template
                  </Button>
                </Group>
              </Card>

              <Card withBorder p="md" style={{ borderStyle: 'dashed' }}>
                <Group justify="center" style={{ height: '100%' }}>
                  <Button
                    variant="subtle"
                    leftSection={<IconPlus size={16} />}
                    onClick={handleCreatePrescription}
                  >
                    Create New Template
                  </Button>
                </Group>
              </Card>
            </SimpleGrid>
          </Paper>
        </Tabs.Panel>
      </Tabs>
</Paper>
      <Modal opened={opened} onClose={close} title="Create New Prescription" size="lg">
        <div>
          <Select
            label="Patient"
            placeholder="Select patient"
            data={[
              { value: 'john-doe', label: 'John Doe' },
              { value: 'jane-smith', label: 'Jane Smith' },
              { value: 'robert-johnson', label: 'Robert Johnson' },
              { value: 'emily-davis', label: 'Emily Davis' },
              { value: 'michael-brown', label: 'Michael Brown' },
            ]}
            required
            mb="md"
          />

          <Divider my="md" label="Medication Information" labelPosition="center" />

          <Select
            label="Medication"
            placeholder="Select medication"
            data={[
              { value: 'amoxicillin', label: 'Amoxicillin 500mg' },
              { value: 'lisinopril', label: 'Lisinopril 10mg' },
              { value: 'metformin', label: 'Metformin 500mg' },
              { value: 'ibuprofen', label: 'Ibuprofen 400mg' },
              { value: 'albuterol', label: 'Albuterol Inhaler' },
            ]}
            required
            mb="md"
          />

          <Group grow mb="md">
            <TextInput
              label="Dosage"
              placeholder="e.g., 1 tablet"
              required
            />

            <TextInput
              label="Route"
              placeholder="e.g., Oral, Topical"
              required
            />
          </Group>

          <Group grow mb="md">
            <TextInput
              label="Frequency"
              placeholder="e.g., Twice daily"
              required
            />

            <TextInput
              label="Duration"
              placeholder="e.g., 10 days"
              required
            />
          </Group>

          <Group grow mb="md">
            <NumberInput
              label="Quantity"
              placeholder="0"
              min={1}
              required
            />

            <NumberInput
              label="Refills"
              placeholder="0"
              min={0}
              required
            />
          </Group>

          <Divider my="md" label="Additional Information" labelPosition="center" />

          <Textarea
            label="Special Instructions"
            placeholder="Enter any special instructions for the patient"
            mb="md"
          />

          <Checkbox
            label="Take with food"
            mb="xs"
          />

          <Checkbox
            label="Avoid alcohol"
            mb="xs"
          />

          <Checkbox
            label="May cause drowsiness"
            mb="md"
          />

          <Divider my="md" />

          <Group justify="space-between" mb="md">
            <Checkbox
              label="Save as template for future use"
            />

            <Group>
              <Button variant="outline" leftSection={<IconPrinter size={16} />}>
                Print
              </Button>
              <Button variant="outline" leftSection={<IconSend size={16} />}>
                Send to Pharmacy
              </Button>
            </Group>
          </Group>

          <Group justify="flex-end" mt="xl">
            <Button variant="outline" onClick={close}>Cancel</Button>
            <Button onClick={handleSubmit}>Create Prescription</Button>
          </Group>
        </div>
      </Modal>
  </> 
  );
}
