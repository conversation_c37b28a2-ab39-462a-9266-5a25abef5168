.control {
    font-weight: 500;
    display: block;
    width: 100%;
    padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
    font-size: var(--mantine-font-size-sm);
    color:var(--mantine-color-text) !important ;
  
    
  }
  .control:hover {
    font-weight: 500;
    display: block;
    width: 100%;
    padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
    font-size: var(--mantine-font-size-sm);
    background-color: var(--mantine-colorgray);
    color:var(--mantine-color-text) !important ;
    transition: all ease .2s;
    
  } 
  .link {
    --nl-bg: var(--mantine-primary-color-light);
    --nl-hover: var(--mantine-primary-color-light-hover);
    --nl-color: var(--mantine-primary-color-light-color);
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px var(--mantine-spacing-sm);
    user-select: none;
    border-left: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)) !important;
    margin-left: calc(1.625rem* var(--mantine-scale));
    padding-left: calc(0.625rem* var(--mantine-scale));
    font-weight: 500;
    color:var(--mantine-color-text) !important ;
  }
  .link:hover {
    --nl-bg: var(--mantine-primary-color-light);
    --nl-hover: var(--mantine-primary-color-light-hover);
    --nl-color: var(--mantine-primary-color-light-color);
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px var(--mantine-spacing-sm);
    user-select: none;
    border-left: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)) !important;
    margin-left: calc(1.625rem* var(--mantine-scale));
    padding-left: calc(0.625rem* var(--mantine-scale));
    background-color: var(--mantine-colorgray); 
    font-weight: 500;
    color:var(--mantine-color-text) !important ;
 
    /* font-weight: 500;
    display: block;
     width: 100%; 
    padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
    font-size: var(--mantine-font-size-sm);
    color:var(--mantine-color-text) !important ;
    border-left: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    margin-left: var(--mantine-spacing-xl);
    text-decoration: none;
    font-size: var(--mantine-font-size-sm);
    background-color: var(--mantine-colorgray); 
    transition: all ease .2s; */
  
  }
  .chevron {
    transition: transform 200ms ease;
  }
  
