'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Text,
  Button,
  Group,
  Stack,
  Grid,
  Badge,
  ActionIcon,
  Tooltip,
  Alert,
  Loader,
  Center,
  Title,
  Divider,
} from '@mantine/core';
import {
  IconMedicalCross,
  IconExternalLink,
  IconRefresh,
  IconSettings,
  IconEye,
  IconPlugConnected,
  IconEdit,
  IconStethoscope,
} from '@tabler/icons-react';
import { toothService, Tooth, ToothApiResponse } from '../../../services/toothService';
import { testBackendConnection } from '../../../utils/testBackendConnection';
import ToothEditModal from './ToothEditModal';
import ToothTreatmentManager from './ToothTreatmentManager';
import { notifications } from '@mantine/notifications';

// Interface pour les traitements dentaires
interface ToothTreatment {
  id: string;
  tooth: string;
  treatment_type: {
    id: string;
    category: string;
    code: string;
    name: string;
    description: string;
    icon: string;
    color: string;
    duration_minutes: number;
    base_price: number;
    complexity_level: string;
    requires_anesthesia: boolean;
    requires_followup: boolean;
  };
  status: string;
  planned_date: string | null;
  completed_date: string | null;
  notes: string;
  custom_price: number | null;
  surfaces_affected: string[];
  created_at: string;
}

const ToothManagement: React.FC = () => {
  const [teeth, setTeeth] = useState<Tooth[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<string>('Checking...');
  const [editModalOpened, setEditModalOpened] = useState(false);
  const [selectedTooth, setSelectedTooth] = useState<Tooth | null>(null);
  const [treatmentModalOpened, setTreatmentModalOpened] = useState(false);
  const [selectedToothForTreatment, setSelectedToothForTreatment] = useState<Tooth | null>(null);

  // Fetch teeth data
  const fetchTeeth = async () => {
    try {
      setLoading(true);
      setError(null);
      const response: ToothApiResponse = await toothService.getTeethForEstimates();
      setTeeth(response.results || []);
      console.log('✅ Loaded teeth data:', response.results);
    } catch (err) {
      console.error('❌ Error fetching teeth:', err);
      setError('Failed to load teeth data. Using mock data for demonstration.');

      // Use mock data as fallback
      try {
        const mockResponse = await toothService.getTeethForEstimates();
        setTeeth(mockResponse.results || []);
      } catch (mockErr) {
        console.error('❌ Error loading mock data:', mockErr);
        setTeeth([]); // Set empty array as final fallback
      }
    } finally {
      setLoading(false);
    }
  };

  // Test backend connection
  const handleTestConnection = async () => {
    setConnectionStatus('Testing...');
    try {
      const result = await testBackendConnection();
      setConnectionStatus(result.success ? 'Connected ✅' : 'Failed ❌');
    } catch {
      setConnectionStatus('Failed ❌');
    }
  };

  // Handle tooth edit
  const handleEditTooth = (tooth: Tooth) => {
    setSelectedTooth(tooth);
    setEditModalOpened(true);
  };

  // Handle tooth save
  const handleSaveTooth = (updatedTooth: Tooth) => {
    setTeeth(prevTeeth =>
      prevTeeth.map(tooth =>
        tooth.id === updatedTooth.id ? updatedTooth : tooth
      )
    );

    notifications.show({
      title: 'Succès',
      message: 'Dent mise à jour avec succès',
      color: 'green',
    });
  };

  // Handle modal close
  const handleCloseModal = () => {
    setEditModalOpened(false);
    setSelectedTooth(null);
  };

  // Handle treatment management
  const handleManageTreatments = (tooth: Tooth) => {
    setSelectedToothForTreatment(tooth);
    setTreatmentModalOpened(true);
  };

  // Handle treatment save
  const handleSaveTreatments = (treatments: ToothTreatment[]) => {
    // TODO: Sauvegarder les traitements via l'API
    console.log('Saving treatments:', treatments);

    notifications.show({
      title: 'Traitements sauvegardés',
      message: `${treatments.length} traitement(s) planifié(s) pour la dent ${selectedToothForTreatment?.number}`,
      color: 'green',
    });
  };

  // Handle treatment modal close
  const handleCloseTreatmentModal = () => {
    setTreatmentModalOpened(false);
    setSelectedToothForTreatment(null);
  };

  useEffect(() => {
    fetchTeeth();
    handleTestConnection();
  }, []);

  // Group teeth by quadrant - safely handle undefined teeth
  const groupedTeeth = (teeth || []).reduce((acc, tooth) => {
    if (!acc[tooth.quadrant]) {
      acc[tooth.quadrant] = [];
    }
    acc[tooth.quadrant].push(tooth);
    return acc;
  }, {} as Record<string, Tooth[]>);

  // Get color for tooth type
  const getToothTypeColor = (type: string) => {
    switch (type) {
      case 'incisor': return 'blue';
      case 'canine': return 'yellow';
      case 'premolar': return 'green';
      case 'molar': return 'orange';
      case 'wisdom': return 'red';
      default: return 'gray';
    }
  };

  // Get quadrant display name
  const getQuadrantName = (quadrant: string) => {
    switch (quadrant) {
      case 'upper_right': return 'Supérieur Droit';
      case 'upper_left': return 'Supérieur Gauche';
      case 'lower_left': return 'Inférieur Gauche';
      case 'lower_right': return 'Inférieur Droit';
      default: return quadrant;
    }
  };

  if (loading) {
    return (
      <Center h={400}>
        <Stack align="center">
          <Loader size="lg" />
          <Text>Chargement des données des dents...</Text>
        </Stack>
      </Center>
    );
  }

  return (
    <Stack gap="md">
      {/* Header */}
      <Card withBorder p="md">
        <Group justify="space-between">
          <div>
            <Title order={3}>
              <IconMedicalCross size={24} style={{ marginRight: 8 }} />
              Gestion des Dents - Devis
            </Title>
            <Text c="dimmed" size="sm">
              Gérez les données des dents pour les devis et traitements
            </Text>
          </div>
          <Group>
            <Tooltip label="Tester la connexion">
              <ActionIcon variant="light" onClick={handleTestConnection}>
                <IconPlugConnected size={16} />
              </ActionIcon>
            </Tooltip>
            <Tooltip label="Actualiser les données">
              <ActionIcon variant="light" onClick={fetchTeeth}>
                <IconRefresh size={16} />
              </ActionIcon>
            </Tooltip>
            <Button
              leftSection={<IconExternalLink size={16} />}
              variant="filled"
              onClick={toothService.openAdminPanel}
            >
              Ouvrir Admin Panel
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert color="yellow" title="Information">
          {error}
        </Alert>
      )}

      {/* Connection Status */}
      <Card withBorder p="sm">
        <Group justify="space-between">
          <Group>
            <Badge
              color={connectionStatus.includes('✅') ? 'green' : connectionStatus.includes('❌') ? 'red' : 'yellow'}
              variant="light"
            >
              {connectionStatus}
            </Badge>
            <Text size="sm" c="dimmed">
              http://127.0.0.1:8000/admin/dentistry/tooth/
            </Text>
          </Group>
          <Group>
            <Button
              size="xs"
              variant="subtle"
              leftSection={<IconPlugConnected size={14} />}
              onClick={handleTestConnection}
            >
              Test
            </Button>
            <Button
              size="xs"
              variant="subtle"
              leftSection={<IconSettings size={14} />}
              onClick={toothService.openAdminPanel}
            >
              Gérer
            </Button>
          </Group>
        </Group>
      </Card>

      {/* Teeth Grid by Quadrant */}
      {Object.keys(groupedTeeth).length === 0 ? (
        <Card withBorder p="xl">
          <Center>
            <Stack align="center">
              <IconMedicalCross size={48} color="gray" />
              <Title order={4} c="dimmed">Aucune donnée de dents disponible</Title>
              <Text c="dimmed" ta="center">
                {error ?
                  'Impossible de se connecter au backend. Vérifiez que le serveur Django est démarré.' :
                  'Aucune dent trouvée dans la base de données.'
                }
              </Text>
              <Group>
                <Button variant="light" onClick={fetchTeeth}>
                  Réessayer
                </Button>
                <Button variant="light" onClick={toothService.openAdminPanel}>
                  Ouvrir Admin
                </Button>
              </Group>
            </Stack>
          </Center>
        </Card>
      ) : (
        <Grid>
          {Object.entries(groupedTeeth).map(([quadrant, quadrantTeeth]) => (
          <Grid.Col key={quadrant} span={{ base: 12, md: 6 }}>
            <Card withBorder p="md" h="100%">
              <Stack gap="sm">
                <Group justify="space-between">
                  <Text fw={600} size="lg">
                    {getQuadrantName(quadrant)}
                  </Text>
                  <Badge variant="light">
                    {quadrantTeeth.length} dents
                  </Badge>
                </Group>

                <Divider />

                <Stack gap="xs">
                  {quadrantTeeth
                    .sort((a, b) => a.position - b.position)
                    .map((tooth) => (
                      <Card key={tooth.id} withBorder p="xs" radius="sm">
                        <Group justify="space-between">
                          <Group gap="xs">
                            <IconMedicalCross size={16} />
                            <div>
                              <Text size="sm" fw={500}>
                                {tooth.number} - {tooth.name}
                              </Text>
                              <Text size="xs" c="dimmed">
                                {tooth.description}
                              </Text>
                            </div>
                          </Group>
                          <Group gap="xs">
                            <Badge
                              size="xs"
                              color={getToothTypeColor(tooth.tooth_type)}
                              variant="light"
                            >
                              {tooth.tooth_type}
                            </Badge>
                            {tooth.is_permanent && (
                              <Badge size="xs" color="blue" variant="outline">
                                Permanent
                              </Badge>
                            )}
                            <ActionIcon
                              size="xs"
                              variant="light"
                              color="blue"
                              onClick={() => handleEditTooth(tooth)}
                            >
                              <IconEdit size={12} />
                            </ActionIcon>
                            <ActionIcon
                              size="xs"
                              variant="light"
                              color="green"
                              onClick={() => handleManageTreatments(tooth)}
                            >
                              <IconStethoscope size={12} />
                            </ActionIcon>
                          </Group>
                        </Group>
                      </Card>
                    ))}
                </Stack>
              </Stack>
            </Card>
          </Grid.Col>
        ))}
        </Grid>
      )}

      {/* Statistics */}
      <Card withBorder p="md">
        <Title order={4} mb="md">Statistiques</Title>
        <Grid>
          <Grid.Col span={{ base: 6, md: 3 }}>
            <Stack align="center">
              <Text size="xl" fw={700} c="blue">
                {(teeth || []).length}
              </Text>
              <Text size="sm" c="dimmed">Total Dents</Text>
            </Stack>
          </Grid.Col>
          <Grid.Col span={{ base: 6, md: 3 }}>
            <Stack align="center">
              <Text size="xl" fw={700} c="green">
                {(teeth || []).filter(t => t.is_permanent).length}
              </Text>
              <Text size="sm" c="dimmed">Permanentes</Text>
            </Stack>
          </Grid.Col>
          <Grid.Col span={{ base: 6, md: 3 }}>
            <Stack align="center">
              <Text size="xl" fw={700} c="orange">
                {Object.keys(groupedTeeth).length}
              </Text>
              <Text size="sm" c="dimmed">Quadrants</Text>
            </Stack>
          </Grid.Col>
          <Grid.Col span={{ base: 6, md: 3 }}>
            <Stack align="center">
              <Text size="xl" fw={700} c="red">
                {(teeth || []).filter(t => t.tooth_type === 'wisdom').length}
              </Text>
              <Text size="sm" c="dimmed">Sagesse</Text>
            </Stack>
          </Grid.Col>
        </Grid>
      </Card>

      {/* Quick Actions */}
      <Card withBorder p="md">
        <Title order={4} mb="md">Actions Rapides</Title>
        <Group>
          <Button
            variant="light"
            leftSection={<IconEye size={16} />}
            onClick={() => window.open('http://127.0.0.1:8000/admin/dentistry/', '_blank')}
          >
            Voir tous les modèles
          </Button>
          <Button
            variant="light"
            leftSection={<IconSettings size={16} />}
            onClick={() => window.open('http://127.0.0.1:8000/admin/', '_blank')}
          >
            Admin Django
          </Button>
          <Button
            variant="light"
            onClick={fetchTeeth}
          >
            Actualiser données
          </Button>
        </Group>
      </Card>

      {/* Edit Modal */}
      <ToothEditModal
        tooth={selectedTooth}
        opened={editModalOpened}
        onClose={handleCloseModal}
        onSave={handleSaveTooth}
      />

      {/* Treatment Management Modal */}
      <ToothTreatmentManager
        tooth={selectedToothForTreatment}
        opened={treatmentModalOpened}
        onClose={handleCloseTreatmentModal}
        onSave={handleSaveTreatments}
      />
    </Stack>
  );
};

export default ToothManagement;
