import { Locales } from "~/i18n/settings";
import { switchLocaleAction } from "~/actions/switch-locale";
import i18next from "i18next";

// Utility function to apply font size and update CSS variables
export const applyFontSizeSettings = (fontSize: number) => {
  if (typeof window !== 'undefined') {
    const root = document.documentElement;
    const scaleFactor = fontSize / 16; // 16px is the default base size

    // Set the base font size
    root.style.fontSize = `${fontSize}px`;

    // Update Mantine CSS variables to scale with the new font size
    root.style.setProperty('--mantine-font-size-xs', `${0.75 * scaleFactor}rem`);
    root.style.setProperty('--mantine-font-size-sm', `${0.875 * scaleFactor}rem`);
    root.style.setProperty('--mantine-font-size-md', `${1 * scaleFactor}rem`);
    root.style.setProperty('--mantine-font-size-lg', `${1.125 * scaleFactor}rem`);
    root.style.setProperty('--mantine-font-size-xl', `${1.25 * scaleFactor}rem`);

    // Update spacing variables to maintain proportions
    root.style.setProperty('--mantine-spacing-xs', `${0.625 * scaleFactor}rem`);
    root.style.setProperty('--mantine-spacing-sm', `${0.75 * scaleFactor}rem`);
    root.style.setProperty('--mantine-spacing-md', `${1 * scaleFactor}rem`);
    root.style.setProperty('--mantine-spacing-lg', `${1.25 * scaleFactor}rem`);
    root.style.setProperty('--mantine-spacing-xl', `${2 * scaleFactor}rem`);

    console.log(`Font size applied: ${fontSize}px with scale factor ${scaleFactor}`);
  }
};

// Utility function to apply language settings
export const applyLanguageSettings = async (language: Locales) => {
  try {
    // Change i18next language
    if (typeof window !== 'undefined' && i18next.isInitialized) {
      await i18next.changeLanguage(language);
    }

    // Update cookie via server action
    await switchLocaleAction(language);

    // Update HTML lang attribute
    if (typeof window !== 'undefined') {
      document.documentElement.lang = language;
    }

    console.log(`Language applied: ${language}`);
  } catch (error) {
    console.error("Error applying language settings:", error);
    throw error;
  }
};
