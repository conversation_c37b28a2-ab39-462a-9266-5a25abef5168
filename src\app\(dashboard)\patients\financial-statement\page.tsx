
"use client";
import { useState } from "react";
import React from "react";
import Icon from '@mdi/react';
import { mdiAccountClock } from '@mdi/js';
import {EtatFinancier} from "./EtatFinancier"
import {PlansDeTraitement} from './PlansDeTraitement'
import {PlanDeSoins} from "./PlanDeSoins"
import {ListeDesVisites} from './ListeDesVisites'
import {Encaissements} from './Encaissements'

import "~/styles/tab.css";
function  AppointmentsPage() {
  const [toggleState, setToggleState] = useState(1);
const icons = [
  { icon: <Icon path={mdiAccountClock} size={1} key="EtatFinancier" />, label: "EtatFinancier" },
  {
    icon: <Icon path={mdiAccountClock} size={1} key="PlansDeTraitement" />,
    label: "Plans de traitement",
  },
  {
    icon: <Icon path={mdiAccountClock} size={1} key="PlanDeSoins" />,
    label: "Plan de soins",
  },
  {
    icon: <Icon path={mdiAccountClock} size={1} key="ListeDesVisites" />,
    label: "Liste des visites",
  },
  {
    icon: <Icon path={mdiAccountClock} size={1} key="Encaissements" />,
    label: "Encaissements",
  },
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<EtatFinancier/> )
    
     case 2:
      return (  <PlansDeTraitement/>)
      case 3:
        return (  <PlanDeSoins/>)
        case 4:
          return <ListeDesVisites/>;
        case 5:
          return <Encaissements/>;
    default:
      return null;
  }
};
  return (
    <>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 