
"use client";
import { useState, useEffect } from "react";
import React from "react";
import { useSearchParams } from "next/navigation";
import Icon from '@mdi/react';
import { mdiAccountClock ,mdiCoffeeMakerCheck,mdiFileCheck,mdiFileStar,mdiDatabaseClock,mdiFlipVertical,mdiCalendarAccountOutline} from '@mdi/js';
import MetaSeo from"./MetaSeo"
import PatientsPage from "./content"
import Complets from "./Complets"
import Valides from "./Valides"
import Favoris from "./Favoris"
import Archives from './Archives'
import Incomplets from './Incomplets'
import SansVisite from "./SansVisite"
import "~/styles/tab.css";
// Mapping des paramètres URL vers les numéros d'onglets
const tabMapping: { [key: string]: number } = {
  //'general': 1,
  'PatientsPage': 1,
  'Complets': 2,
  'Valides': 3,
  'Favoris': 4,
  'Archives': 5,
  'Incomplets': 6,
   'SansVisite': 7,
 
 
};
function  AppointmentsPage() {
  // const iconStyle = { width: rem(14), height: rem(14) };
  const [toggleState, setToggleState] = useState(1);
 const searchParams = useSearchParams();
 
   // Effet pour lire les paramètres d'URL et définir l'onglet actif
   useEffect(() => {
     const tab = searchParams.get('tab');
     if (tab && tabMapping[tab]) {
       setToggleState(tabMapping[tab]);
     }
   }, [searchParams]);

const icons = [
  { icon: <Icon path={mdiAccountClock} size={1} key="PatientsPage" />, label: "Patients" },
  {
    icon: <Icon path={mdiCoffeeMakerCheck} size={1} key="Complets" />,
    label: "Complets",
  },
  {
    icon: <Icon path={mdiFileCheck} size={1} key="Valides" />,
    label: "Validés(avec visite)",
  },
  {
    icon: <Icon path={mdiFileStar} size={1} key="Favoris" />,
    label: "Favoris",
  },
  {
    icon: <Icon path={mdiDatabaseClock} size={1} key="Archives" />,
    label: "Archivés",
  },
  {
    icon: <Icon path={mdiFlipVertical} size={1} key="Incomplets" />,
    label: "Incomplets",
  },
  {
    icon: <Icon path={mdiCalendarAccountOutline} size={1} key="SansVisite" />,
    label: "Sans visite",
  },
];

const toggleTab = (index: number) => {
  setToggleState(index);
};

const renderTabContent = () => {
  switch (toggleState) {
    case 1:
      return (<PatientsPage/> )
    
     case 2:
      return (  <Complets/>)
      case 3:
        return (  <Valides/>)
        case 4:
          return (<Favoris/>);
          case 5:
          return (<Archives/>);
          case 6:
          return (<Incomplets/>);
  case 7:
          return (<SansVisite/>);

    default:
      return null;
  }
};
  return (
    <>
      <>
      <MetaSeo/>
      </>
      <div className={` grid `}  >
      <div className="tabs tabs-lifted z-10 -mb-[var(--tab-border)] justify-self-start">
        {icons.map((item, index) => (
          <button
            key={index}
            onClick={() => toggleTab(index + 1)}
            className={
              toggleState === index + 1
                ? "tab tab-active flex items-center gap-2"
                : "tab flex items-center gap-2"
            }
            id={`card-type-tab-item-${index + 1}`}
            data-hs-tab={`#card-type-tab-${index + 1}`}
            aria-controls={`card-type-tab-${index + 1}`}
            role="tab"
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
        <div className="tab [--tab-border-color:transparent]" />
      </div>

      <div
        className="rounded-b-box relative overflow-x-auto"
        id={`card-type-tab-${toggleState}`}
        role="tabpanel"
        aria-labelledby={`card-type-tab-item-${toggleState}`}
      >
        <div className="border-base-300 bg-base-100 rounded-b-box flex min-w-full max-w-4xl flex-wrap items-center justify-center gap-2 overflow-x-hidden p-2 [border-width:var(--tab-border)]">
          {renderTabContent()}
        </div>
      </div>
    </div>
    </>
  );
}

export default AppointmentsPage;

 