// frontend/dental_medicine/src/components/content/dental/shared/treatmentManager.ts

import { TREATMENTS, COMPATIBILITY_RULES, TreatmentInfo, TreatmentState } from './treatmentCompatibility';

export interface TreatmentConflict {
  type: 'exclusive' | 'prerequisite' | 'sequence';
  message: string;
  conflictingTreatments: string[];
  suggestedActions: string[];
}

export interface TreatmentApplication {
  success: boolean;
  appliedTreatment?: string;
  conflicts?: TreatmentConflict[];
  warnings?: string[];
  pathsToShow: string[];
  pathsToHide: string[];
}

export class TreatmentManager {
  private treatmentStates: Map<string, string[]> = new Map(); // svgId -> appliedTreatments[]

  /**
   * Obtient les traitements actuellement appliqués sur une dent
   */
  getCurrentTreatments(svgId: string): string[] {
    return this.treatmentStates.get(svgId) || [];
  }

  /**
   * Vérifie si un traitement peut être appliqué sur une dent
   */
  canApplyTreatment(treatmentId: string, svgId: string): TreatmentConflict[] {
    const currentTreatments = this.getCurrentTreatments(svgId);
    const conflicts: TreatmentConflict[] = [];
    const treatment = TREATMENTS[treatmentId];

    if (!treatment) {
      conflicts.push({
        type: 'exclusive',
        message: `Traitement ${treatmentId} non reconnu`,
        conflictingTreatments: [],
        suggestedActions: ['Vérifier le nom du traitement']
      });
      return conflicts;
    }

    // Vérifier les exclusions mutuelles
    for (const exclusiveGroup of COMPATIBILITY_RULES.mutuallyExclusive) {
      if (exclusiveGroup.includes(treatmentId)) {
        const conflicting = currentTreatments.filter(t =>
          exclusiveGroup.includes(t) && t !== treatmentId
        );

        if (conflicting.length > 0) {
          conflicts.push({
            type: 'exclusive',
            message: `${treatment.name} est incompatible avec ${conflicting.map(t => TREATMENTS[t]?.name).join(', ')}`,
            conflictingTreatments: conflicting,
            suggestedActions: [
              'Retirer les traitements conflictuels',
              'Choisir un traitement alternatif'
            ]
          });
        }
      }
    }

    // Vérifier les prérequis
    const prerequisites = COMPATIBILITY_RULES.prerequisites[treatmentId];
    if (prerequisites) {
      const missingPrereqs = prerequisites.filter(prereq =>
        !currentTreatments.includes(prereq)
      );

      if (missingPrereqs.length > 0) {
        conflicts.push({
          type: 'prerequisite',
          message: `${treatment.name} nécessite : ${missingPrereqs.map(p => TREATMENTS[p]?.name).join(', ')}`,
          conflictingTreatments: missingPrereqs,
          suggestedActions: [
            'Appliquer d\'abord les prérequis',
            'Modifier la séquence de traitement'
          ]
        });
      }
    }

    // Vérifier si l'extraction est déjà appliquée
    if (currentTreatments.includes('extraction') && treatmentId !== 'bone_graft' && treatmentId !== 'implant') {
      conflicts.push({
        type: 'exclusive',
        message: 'Impossible d\'appliquer des traitements sur une dent extraite',
        conflictingTreatments: ['extraction'],
        suggestedActions: [
          'Retirer l\'extraction',
          'Appliquer un implant à la place'
        ]
      });
    }

    return conflicts;
  }

  /**
   * Applique un traitement sur une dent
   */
  applyTreatment(treatmentId: string, svgId: string, force: boolean = false): TreatmentApplication {
    const conflicts = this.canApplyTreatment(treatmentId, svgId);
    const treatment = TREATMENTS[treatmentId];

    if (!treatment) {
      return {
        success: false,
        conflicts: [{
          type: 'exclusive',
          message: `Traitement ${treatmentId} non trouvé`,
          conflictingTreatments: [],
          suggestedActions: []
        }],
        pathsToShow: [],
        pathsToHide: []
      };
    }

    // Si il y a des conflits et qu'on ne force pas, retourner les conflits
    if (conflicts.length > 0 && !force) {
      return {
        success: false,
        conflicts,
        pathsToShow: [],
        pathsToHide: []
      };
    }

    // Appliquer le traitement
    const currentTreatments = this.getCurrentTreatments(svgId);

    // Si on force, résoudre les conflits automatiquement
    if (force && conflicts.length > 0) {
      this.resolveConflicts(treatmentId, svgId, conflicts);
    }

    // Ajouter le nouveau traitement
    const updatedTreatments = [...currentTreatments];
    if (!updatedTreatments.includes(treatmentId)) {
      updatedTreatments.push(treatmentId);
    }

    this.treatmentStates.set(svgId, updatedTreatments);

    // Calculer les paths à afficher/cacher
    const pathsToShow = this.calculateVisiblePaths(svgId);
    const allPaths = this.getAllPathsForTooth(svgId);
    const pathsToHide = allPaths.filter(path => !pathsToShow.includes(path));

    return {
      success: true,
      appliedTreatment: treatmentId,
      pathsToShow,
      pathsToHide,
      warnings: conflicts.length > 0 ? ['Conflits résolus automatiquement'] : []
    };
  }

  /**
   * Retire un traitement d'une dent
   */
  removeTreatment(treatmentId: string, svgId: string): TreatmentApplication {
    const currentTreatments = this.getCurrentTreatments(svgId);
    const updatedTreatments = currentTreatments.filter(t => t !== treatmentId);

    this.treatmentStates.set(svgId, updatedTreatments);

    // Recalculer les paths visibles
    const pathsToShow = this.calculateVisiblePaths(svgId);
    const allPaths = this.getAllPathsForTooth(svgId);
    const pathsToHide = allPaths.filter(path => !pathsToShow.includes(path));

    return {
      success: true,
      pathsToShow,
      pathsToHide
    };
  }

  /**
   * Calcule les paths qui doivent être visibles selon les traitements appliqués
   */
  private calculateVisiblePaths(svgId: string): string[] {
    const appliedTreatments = this.getCurrentTreatments(svgId);
    const visiblePaths: string[] = [];

    // Trier les traitements par priorité (plus élevé = affiché au-dessus)
    const sortedTreatments = appliedTreatments
      .map(t => TREATMENTS[t])
      .filter(Boolean)
      .sort((a, b) => b.priority - a.priority);

    // Ajouter les paths de chaque traitement
    for (const treatment of sortedTreatments) {
      visiblePaths.push(...treatment.pathIds);
    }

    // Toujours afficher la dent de base (path 1) sauf si extraction
    if (!appliedTreatments.includes('extraction')) {
      visiblePaths.unshift('1');
    }

    // Retourner les paths uniques
    return [...new Set(visiblePaths)];
  }

  /**
   * Obtient tous les paths possibles pour une dent
   */
  private getAllPathsForTooth(svgId: string): string[] {
    // Retourner tous les paths possibles basés sur les traitements définis
    const allPaths = new Set<string>();

    // Ajouter tous les paths de tous les traitements
    Object.values(TREATMENTS).forEach(treatment => {
      treatment.pathIds.forEach(pathId => allPaths.add(pathId));
    });

    // Ajouter les paths de base (1-16 pour les dents de base)
    for (let i = 1; i <= 16; i++) {
      allPaths.add(i.toString());
    }

    return Array.from(allPaths);
  }

  /**
   * Résout automatiquement les conflits
   */
  private resolveConflicts(treatmentId: string, svgId: string, conflicts: TreatmentConflict[]): void {
    const currentTreatments = this.getCurrentTreatments(svgId);

    for (const conflict of conflicts) {
      if (conflict.type === 'exclusive') {
        // Retirer les traitements conflictuels
        const updatedTreatments = currentTreatments.filter(t =>
          !conflict.conflictingTreatments.includes(t)
        );
        this.treatmentStates.set(svgId, updatedTreatments);
      }
    }
  }

  /**
   * Suggère une séquence de traitement optimale
   */
  suggestTreatmentSequence(targetTreatment: string): string[] {
    // Trouver la séquence qui contient le traitement cible
    for (const [sequenceName, sequence] of Object.entries(COMPATIBILITY_RULES.sequences)) {
      if (sequence.includes(targetTreatment)) {
        return sequence;
      }
    }

    // Si pas de séquence trouvée, retourner juste le traitement
    return [targetTreatment];
  }

  /**
   * Obtient un résumé de l'état des traitements pour une dent
   */
  getTreatmentSummary(svgId: string): TreatmentState {
    const appliedTreatments = this.getCurrentTreatments(svgId);
    const conflictingTreatments: string[] = [];

    // Vérifier les conflits internes
    for (let i = 0; i < appliedTreatments.length; i++) {
      for (let j = i + 1; j < appliedTreatments.length; j++) {
        const conflicts = this.canApplyTreatment(appliedTreatments[j], svgId);
        if (conflicts.length > 0) {
          conflictingTreatments.push(appliedTreatments[j]);
        }
      }
    }

    return {
      svgId,
      appliedTreatments,
      conflictingTreatments,
      suggestedSequence: []
    };
  }
}

// Instance globale du gestionnaire
export const treatmentManager = new TreatmentManager();
