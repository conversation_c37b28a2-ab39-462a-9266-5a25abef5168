// import api from '@/lib/api';

export interface Message {
  id: string;
  sender_id: string;
  sender_name: string;
  receiver_id: string;
  receiver_name: string;
  content: string;
  created_at: string;
  read: boolean;
  specialty_id?: string;
}

export interface Conversation {
  id: string;
  participant_id: string;
  participant_name: string;
  participant_image?: string;
  last_message?: string;
  last_message_time?: string;
  unread_count: number;
  specialty_id?: string;
}

// For now, we'll use mock data since the API might not be implemented yet
const mockMessages: Record<string, Message[]> = {};
const mockConversations: Conversation[] = [];

// Helper function to generate a unique ID
const generateId = () => Math.random().toString(36).substring(2, 15);

// Initialize with some mock data
const initializeMockData = () => {
  // Mock doctors in the same specialty
  const doctors = [
    { id: '1', name: 'Dr. <PERSON>' },
    { id: '2', name: 'Dr. <PERSON>' },
    { id: '3', name: 'Dr. <PERSON>' }
  ];

  // Create mock conversations
  doctors.forEach(doctor => {
    if (doctor.id !== '1') { // Assuming current user is doctor with ID '1'
      const conversationId = `conv_${doctor.id}`;
      mockConversations.push({
        id: conversationId,
        participant_id: doctor.id,
        participant_name: doctor.name,
        unread_count: Math.floor(Math.random() * 3),
        specialty_id: 'cardiology'
      });

      // Create some mock messages for each conversation
      const messages: Message[] = [];
      const messageCount = 3 + Math.floor(Math.random() * 5);

      for (let i = 0; i < messageCount; i++) {
        const isFromCurrentUser = Math.random() > 0.5;
        const timestamp = new Date();
        timestamp.setHours(timestamp.getHours() - (messageCount - i));

        messages.push({
          id: generateId(),
          sender_id: isFromCurrentUser ? '1' : doctor.id,
          sender_name: isFromCurrentUser ? 'Dr. Current User' : doctor.name,
          receiver_id: isFromCurrentUser ? doctor.id : '1',
          receiver_name: isFromCurrentUser ? doctor.name : 'Dr. Current User',
          content: `This is a sample message ${i + 1} in the conversation.`,
          created_at: timestamp.toISOString(),
          read: isFromCurrentUser || Math.random() > 0.3,
          specialty_id: 'cardiology'
        });
      }

      mockMessages[conversationId] = messages;
    }
  });

  // Update last message info in conversations
  mockConversations.forEach(conv => {
    const messages = mockMessages[conv.id] || [];
    if (messages.length > 0) {
      const lastMsg = messages[messages.length - 1];
      conv.last_message = lastMsg.content.length > 30
        ? lastMsg.content.substring(0, 30) + '...'
        : lastMsg.content;
      conv.last_message_time = lastMsg.created_at;
    }
  });
};

// Initialize mock data
initializeMockData();

const messagingService = {
  // Get all conversations for the current user
  async getConversations(specialtyId?: string): Promise<Conversation[]> {
    try {
      // In a real implementation, we would call the API
      // const response = await api.get('/api/messages/conversations/');
      // return response.data;

      // For now, return mock data
      return specialtyId
        ? mockConversations.filter(conv => conv.specialty_id === specialtyId)
        : mockConversations;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      return [];
    }
  },

  // Get messages for a specific conversation
  async getMessages(conversationId: string): Promise<Message[]> {
    try {
      // In a real implementation, we would call the API
      // const response = await api.get(`/api/messages/conversations/${conversationId}/`);
      // return response.data;

      // For now, return mock data
      return mockMessages[conversationId] || [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  },

  // Send a message
  async sendMessage(receiverId: string, content: string, specialtyId?: string): Promise<Message | null> {
    try {
      // In a real implementation, we would call the API
      // const response = await api.post('/api/messages/', { receiver_id: receiverId, content });
      // return response.data;

      // For mock data, find the conversation or create a new one
      let conversationId = mockConversations.find(c => c.participant_id === receiverId)?.id;

      if (!conversationId) {
        // Create a new conversation
        conversationId = `conv_${receiverId}`;
        mockConversations.push({
          id: conversationId,
          participant_id: receiverId,
          participant_name: `Dr. User ${receiverId}`, // Placeholder name
          unread_count: 0,
          specialty_id: specialtyId
        });
        mockMessages[conversationId] = [];
      }

      // Create the new message
      const newMessage: Message = {
        id: generateId(),
        sender_id: '1', // Current user ID
        sender_name: 'Dr. Current User', // Current user name
        receiver_id: receiverId,
        receiver_name: mockConversations.find(c => c.participant_id === receiverId)?.participant_name || `Dr. User ${receiverId}`,
        content,
        created_at: new Date().toISOString(),
        read: false,
        specialty_id: specialtyId
      };

      // Add to mock data
      mockMessages[conversationId].push(newMessage);

      // Update conversation
      const conversation = mockConversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.last_message = content.length > 30 ? content.substring(0, 30) + '...' : content;
        conversation.last_message_time = newMessage.created_at;
      }

      return newMessage;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  },

  // Mark messages as read
  async markAsRead(conversationId: string): Promise<boolean> {
    try {
      // In a real implementation, we would call the API
      // await api.post(`/api/messages/conversations/${conversationId}/read/`);

      // For mock data
      const messages = mockMessages[conversationId] || [];
      messages.forEach(msg => {
        if (msg.receiver_id === '1') { // If current user is the receiver
          msg.read = true;
        }
      });

      // Update unread count
      const conversation = mockConversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.unread_count = 0;
      }

      return true;
    } catch (error) {
      console.error('Error marking messages as read:', error);
      return false;
    }
  },

  // Get doctors in the same specialty
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async getDoctorsInSpecialty(_specialtyId: string): Promise<{ id: string; name: string }[]> {
    try {
      // In a real implementation, we would call the API
      // const response = await api.get(`/api/doctors/?specialty=${_specialtyId}`);
      // return response.data;

      // For now, return mock data regardless of specialty
      return [
        { id: '2', name: 'Dr. Sarah Johnson' },
        { id: '3', name: 'Dr. Michael Williams' },
        { id: '4', name: 'Dr. Emily Davis' },
        { id: '5', name: 'Dr. Robert Brown' }
      ];
    } catch (error) {
      console.error('Error fetching doctors in specialty:', error);
      return [];
    }
  }
};

export default messagingService;
