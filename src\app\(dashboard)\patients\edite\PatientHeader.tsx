import { Group, Title, Button, Text, Tooltip, Menu,  } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import Icon from '@mdi/react';
import {
  mdiArrowLeft,
  mdiCardAccountDetails,
  mdiPlus,
  mdiChartLine,
  mdiPlaylistEdit,
  mdiApps,
  mdiAccountAlert,
  mdiAccountSupervisorCircle,
  mdiCalendarText,
  mdiCashMultiple,
  mdiCurrencyUsd,
  mdiTooth,
  mdiCertificate,
  mdiFormatListBulleted,
} from '@mdi/js';

const PatientHeader = ({
  patient,
  onGoBack,
  onAddMeasurement,
  onShowChart,
  onShowInsurances,
  onOpenMenu,
  onGoToContract,
}: {
  patient: any;
  onGoBack: () => void;
  onAddMeasurement: () => void;
  onShowChart: () => void;
  onShowInsurances: () => void;
  onOpenMenu: () => void;
  onGoToContract: () => void;
}) => {
  return (
    <div className="bg-[#3799ce] text-white px-4 py-3 rounded-t-lg">
    <Group justify="space-between" align="center">
      <Group>
        {patient ? (
          <Icon path={mdiCardAccountDetails} size={1} />
        ) : (
          <Button variant="subtle" onClick={onGoBack}>
            <Icon path={mdiArrowLeft} size={1} />
          </Button>
        )}
        <Title order={2}>Fiche patient</Title>
        <DatePickerInput placeholder="Date de création" />
        23/06/2025
      </Group>

      {patient && (
        <Group>
          <Text>{patient.full_name}</Text>
          <Text>{patient.gender}</Text>
          <Text>{patient.age}</Text>
          <Text>{patient.default_insurance}</Text>
          <Text>{patient.file_number}</Text>
          <Text>{patient.last_visit}</Text>
        </Group>
      )}

      <Group>
        <Tooltip label="Ajouter des biométries">
          <Button variant="default" onClick={onAddMeasurement}>
            <Icon path={mdiPlus} size={1} color={"#3799ce"}/>
          </Button>
        </Tooltip>

        <Tooltip label="Tendances">
          <Button variant="default" onClick={onShowChart}>
            <Icon path={mdiChartLine} size={1} color={"#3799ce"}/>
          </Button>
        </Tooltip>

        <Tooltip label="Assurances patient">
          <Button variant="default" onClick={onShowInsurances}>
            <Icon path={mdiPlaylistEdit} size={1} color={"#3799ce"}/>
          </Button>
        </Tooltip>

        <Menu shadow="md" width={220}>
          <Menu.Target>
            <Button variant="subtle">
              <Icon path={mdiApps} size={1} color={"white"}/>
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item leftSection={<Icon path={mdiAccountAlert} size={0.8} />}>Alerts</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiAccountSupervisorCircle} size={0.8} />}>Relations Patient</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiCalendarText} size={0.8} />}>Planifications</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiCashMultiple} size={0.8} />}>État financier</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiCurrencyUsd} size={0.8} />}>Nouvel encaissement</Menu.Item>
            <Menu.Item leftSection={<Icon path={mdiTooth} size={0.8} />}>Schéma dentaire</Menu.Item>
          </Menu.Dropdown>
        </Menu>

        <Tooltip label="Contrat">
          <Button variant="subtle" onClick={onGoToContract}>
            <Icon path={mdiCertificate} size={1} color={"white"}/>
          </Button>
        </Tooltip>

        <Tooltip label="Liste patients">
          <Button component="a" href="/pratisoft/patient" variant="subtle">
            <Icon path={mdiFormatListBulleted} size={1} color={"white"}/>
          </Button>
        </Tooltip>
      </Group>
    </Group>
    </div>
  );
};

export default PatientHeader;