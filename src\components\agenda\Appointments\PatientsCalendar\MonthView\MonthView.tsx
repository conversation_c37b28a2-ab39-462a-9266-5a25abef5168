"use client";
import { useState, useEffect } from "react";
import { useDisclosure, useMediaQuery } from "@mantine/hooks";
import { Modal, Button, TextInput, ScrollArea, Divider,Text } from "@mantine/core";
import { Select } from "@mantine/core";
import type { SlotInfo } from "react-big-calendar";
import { Calendar, Views } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
// import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";
import type { ToolbarProps } from "react-big-calendar";
import { momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "moment/locale/fr";
import RadioButton from "./RadioButton";
import "./MonthView.css";
import SelectMois from "./SelectMois";
import { showNotification } from "@mantine/notifications";
import {  EventProps } from 'react-big-calendar';
import { CiMenuKebab } from "react-icons/ci";
import { HoverCard, Group ,Box } from '@mantine/core';
import {ToolbarCalendarNav}  from "@/components/agenda/Appointments/ToolbarCalendarNav"
import {
  CalendarClock,
} from "lucide-react";
const calculateAge = (dateDeNaissance: string): number => {
  return moment().diff(moment(dateDeNaissance, "YYYY-MM-DD"), "years");
};
interface Event {
  id: number;
  title: string;
  nom: string;
  prenom: string;
  dateDeNaissance: string;
  age: number;
  selectedOption: string;
  etatCivil: string;
  cin: string;
  adresse: string;
  telephone: string;
  email: string;
  docteur: string;
  consultation: string;
  start: Date;
  end: Date;
  type: EventType;
  clientName?: string;
  description?: string;
  color?: string;
  style?: { backgroundColor: string };
  disabled?: string;
  // timeDifference: string;
  // ville: string;
  // codePostal: string;
  // notes: string;
  events: Event[];
  date: Date;
  onShowMore: (events: Event[], date: Date, e: React.MouseEvent<HTMLDivElement>) => void;
  tooltip: string,
}

type EventType = "visit" | "visitor-counter" | "completed" | "diagnosis";

const localizer = momentLocalizer(moment);
moment.locale("fr");

const MonthView: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [showModel, setShowModel] = useState(false);
  const CloseModel = () => {
    setShowModel(!showModel);
  };
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [eventTitle, setEventTitle] = useState("");
  const [eventDate, setEventDate] = useState("");
  const [eventTime, setEventTime] = useState("");
  const [selectEvent, setSelectEvent] = useState<Event | null>(null);
  const [eventType, setEventType] = useState<EventType>("visit");
  const [eventNom, setEventNom] = useState("");
  const [eventPrenom, setEventPrenom] = useState("");
  const [eventDateDeNaissance, setEventDateDeNaissance] = useState("");
  const [eventAge, setEventAge] = useState<number | null>(null);
  const [eventSelectedOption, setEventSelectedOption] =
    useState<string>("Homme");
  const [eventEtatCivil, setEventEtatCivil] = useState<string>("Célibataire");
  const [eventCin, setEventCin] = useState<string>("");
  const [eventAdresse, setEventAdresse] = useState<string>("");
  const [eventTelephone, setEventTelephone] = useState<string>("");
  const [eventEmail, setEventEmail] = useState<string>("");
  const [eventDocteur, setEventDocteur] = useState<string>("Docteur");
  const [eventConsultation, setEventConsultation] = useState<string>("15 min");
  const [eventIdCounter, setEventIdCounter] = useState(0);

  useEffect(() => {
    if (eventSelectedOption === "Enfant") {
      setEventCin(""); // Clear CIN when "Enfant" is selected
    }
  }, [eventSelectedOption]);

  const handleOptionChange = (value: string) => {
    setEventSelectedOption(value);
  };
  const [firstModalOpened, { open: openFirstModal, close: closeFirstModal }] =
    useDisclosure(false);
  const [
    secondModalOpened,
    { open: openSecondModal, close: closeSecondModal },
  ] = useDisclosure(false);
  const isMobile = useMediaQuery("(max-width: 50em)");
  const firstOpenModal = () => {
    openFirstModal();
  };
  const firstCloseModal = () => {
    closeFirstModal();
  };
  const secondOpenModal = () => {
    openSecondModal();
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = e.target.value;
    setEventDateDeNaissance(date);
    setEventAge(calculateAge(date));
  };
  const handleSelectSlot = (slotInfo: SlotInfo) => {
    firstOpenModal();
    setSelectedDate(slotInfo.start);
    setEventDate(moment(slotInfo.start).format("YYYY-MM-DD"));
    setEventTime(moment(slotInfo.start).format("HH:mm"));
    setSelectEvent(null);
    // setEventType("visit");
  };
  const handleSelectedEvent = (event: Event) => {
    if (event.id === -1) return;
    firstOpenModal();
    setSelectEvent(event);
    setEventTitle(event.title);
    setEventNom(event.nom);
    setEventPrenom(event.prenom);
    setEventDateDeNaissance(event.dateDeNaissance);
    setEventAge(event.age);
    setEventSelectedOption(event.selectedOption);
    setEventEtatCivil(event.etatCivil);
    setEventCin(event.cin);
    setEventAdresse(event.adresse);
    setEventTelephone(event.telephone);
    setEventEmail(event.email);
    setEventDocteur(event.docteur);
    setEventConsultation(event.consultation);
    setEventDate(moment(event.start).format("YYYY-MM-DD"));
    setEventTime(moment(event.start).format("HH:mm"));
    // setEventType(event.type);
    setEventType(event.type || "visit");
  };
  const saveEvent = () => {
    // Validation check for required fields
    if (
      !eventTitle ||
      !eventNom ||
      !eventPrenom ||
      !eventDateDeNaissance ||
      eventAge === null ||
      !eventSelectedOption ||
      !eventEtatCivil ||
      (!eventCin && eventSelectedOption !== "Enfant") || // CIN is required unless "Enfant" is selected
      !eventAdresse ||
      !eventTelephone ||
      !eventEmail ||
      !eventDocteur ||
      !eventConsultation ||
      !eventDate ||
      !eventTime
    ) {
      showNotification({
        title: "Erreur",
        message: "Veuillez remplir tous les champs obligatoires.",
        color: "red",
      });
      return; // Prevent saving the event
    }
    const updatedStartDate = moment(
      `${eventDate} ${eventTime}`,
      "YYYY-MM-DD HH:mm",
    ).toDate();
    const colorMap: Record<EventType, string> = {
      visit: "#34D1BF",
      "visitor-counter": "#F17105",
      completed: "#0496FF",
      diagnosis: "#ED0423",
    };
    const color = colorMap[eventType];
    const eventDetails = {
      title: eventTitle,
      nom: eventNom,
      prenom: eventPrenom,
      dateDeNaissance: eventDateDeNaissance,
      age: eventAge,
      selectedOption: eventSelectedOption,
      etatCivil: eventEtatCivil,
      cin: eventSelectedOption === "Enfant" ? "" : eventCin, // Clear CIN if "Enfant" is selected
      adresse: eventAdresse,
      telephone: eventTelephone,
      email: eventEmail,
      docteur: eventDocteur,
      consultation: eventConsultation,
      start: updatedStartDate,
      end: moment(updatedStartDate)
        .add(parseInt(eventConsultation, 10), "minutes")
        .toDate(),
      type: eventType,
      color,
      style: { backgroundColor: color },
    };
    if (selectEvent) {
      const updatedEvent = {
        ...selectEvent,
        ...eventDetails,
      };
      setEvents(
        events.map((event) =>
          event.id === selectEvent.id ? updatedEvent : event,
        ),
      );
    } else {
      setEvents([...events, { id: eventIdCounter, ...eventDetails } as Event]);
      setEventIdCounter(eventIdCounter + 1);
    }
    resetForm();
  };
  const deleteEvent = () => {
    if (selectEvent) {
      setEvents(events.filter((event) => event.id !== selectEvent.id));
      resetForm();
      // setEventType("visit");
    }
  };
  const resetForm = () => {
    setEventTitle("");
    setEventNom("");
    setEventPrenom("");
    setEventDateDeNaissance("");
    setEventAge(null);
    setEventSelectedOption("Homme");
    setEventEtatCivil("Célibataire");
    setEventCin("");
    setEventAdresse("");
    setEventTelephone("");
    setEventEmail("");
    setEventDocteur("Docteur");
    setEventConsultation("15 min");
    setEventDate("");
    setEventTime("");
    setSelectEvent(null);
    setEventType("visit");
  };

  const CustomToolbarMonth:React.FC<ToolbarProps<Event, object>> = ({
    localizer: { messages },
    label,
    onNavigate,
  }) => {
    const [selectedDateM, setSelectedDateM] = useState(new Date());
    const handleDateChange = (newDate: Date) => {
      setSelectedDateM(newDate);
      // Calculate the difference between the current month and the new selected month
      const currentMonth = moment(label, "MMMM YYYY"); // Parse the current label as a moment object
      const newMonth = moment(newDate);
      const diffMonths = newMonth.diff(currentMonth, "months");
      if (diffMonths !== 0) {
        // Navigate to the new month using onNavigate
        const direction = diffMonths > 0 ? "NEXT" : "PREV";
        for (let i = 0; i < Math.abs(diffMonths); i++) {
          onNavigate(direction);
        }
      }
    };
    return (
      <> 
       <div className="header-nav-base mb-2 p-1">
      <div className="flex justify-between">
      <div className="flex gap-4">
        <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize">
          <CalendarClock className="mr-1 h-3.5 w-3.5" />
          {formattedDate}
        </h3>
        <span className="-mx-6 p-1">|</span>
        <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4" onClick={() => onNavigate("TODAY")}>
          {" "}
          {messages.today}
        </h3>
        <span className="-mx-6 p-1">|</span>
        <div className="mx-auto flex flex-row items-center rounded-l-lg text-center capitalize">
              <button
                className="btn-sm mt-1"
                onClick={() => onNavigate("PREV")}
              >
                <i className="icon icon-chevron-left text-lg"></i>
              </button>
              <span className="text-sm font-medium capitalize">
                <h4 className="rounded-md px-3 py-1.5 text-sm font-medium">
                  {label}
                </h4>
              </span>
              <button
                className="btn-sm mt-1"
                onClick={() => onNavigate("NEXT")}
              >
                <i className="icon icon-chevron-right text-lg"></i>
              </button>
            </div>
            <span className="-mx-6 p-1 mr-1">|</span>
            <span className="-mt-0.5">
            <SelectMois
              date={selectedDateM}
              setter={handleDateChange}
              label={label} // Pass the current label
            />
          </span>
        <div>
    
       </div>
   
      </div>
      <div className="flex gap-4">
     
      <ToolbarCalendarNav/>
      </div>
    </div>
      </div>

      </>
    );
  };
// Helper function for time formatting
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };
  const EventComponent = ({ event}: EventProps<Event>) => {
      return (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }} className="capitalize">
       <div
     className="" //style={{ width: "80%", backgroundColor: "black", borderRadius: "2px" }}
  />
        {/* Main content */}
        <div style={{ display: 'flex', alignItems: 'center' }} >
            <Group ml={1}>
              <Text c="White" size="xs"  >
              {event.nom.slice(0, 8)}/{event.prenom.slice(0, 4)}...{formatTime(event.start)} - {formatTime(event.end)}
              </Text>{" "}
            </Group>  
          <HoverCard shadow="md" data-tip={event.tooltip} data-for="event-tooltip">
                 <HoverCard.Target>
                 <CiMenuKebab strokeWidth="2" fill="white"/>
                 </HoverCard.Target>
                 <HoverCard.Dropdown>
                 <div className="my-2.5">
                 <div className="border-base-200 border-2 border-l-[#3799CE] px-2.5 py-2">
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Nom Prenom:</span>{" "}
                     {event.nom} {event.prenom}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">
                       Date de naissance :
                     </span>{" "}
                     {event.dateDeNaissance}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Age:</span>{" "}
                     {event.age !== null
                       ? event.age.toString()
                       : "Please enter your date of birth"}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">
                       Réservation :
                     </span>{" "}
                     {moment(event.start).format("HH:mm")}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Sexe :</span>{" "}
                     {event.selectedOption}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Etat Civil :</span>
                     {event.etatCivil}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Cin :</span>
                     {event.cin}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Adresse :</span>
                     {event.adresse}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Email :</span>
                     {event.email}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">Docteur :</span>{" "}
                     {event.docteur}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">
                       Consultation :
                     </span>
                     {event.consultation}
                   </p>
                   <p className="m-0 text-sm font-normal leading-6">
                     <span className="text-[#999999]">
                       Date du rendez-vous:
                     </span>{" "}
                     {moment(event.start).format("DD / MM / YYYY")}
                   </p>
                 </div>
                 </div>
                 </HoverCard.Dropdown>
               </HoverCard>
        </div>
        </div>
      );
    };
  // Custom component for the "more" link
   // Create a custom "show more" component
   const CustomShowMore = ({ events, eventSlotsHeight, slotStart, slotEnd, onShowMore, ...props }: any) => {
    // Safe check to ensure onShowMore is a function
    const handleShowMore = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (typeof onShowMore === 'function') {
        onShowMore(slotStart, events, e.target);
      } else {
        console.error('onShowMore is not a function');
      }
    };

    return (
      <div 
        className="rbc-show-more" 
        onClick={handleShowMore}
      >
        +{events.length} more
      </div>
    );
  };
  
  const localizer = momentLocalizer(moment);
  moment.locale("fr");
    const currentDate = new Date();
    // French month names
    const months = [
      "janvier",
      "février",
      "mars",
      "avril",
      "mai",
      "juin",
      "juillet",
      "août",
      "septembre",
      "octobre",
      "novembre",
      "décembre",
    ];
    // French day names
    const days = [
      "dimanche",
      "lundi",
      "mardi",
      "mercredi",
      "jeudi",
      "vendredi",
      "samedi",
    ];
    const messages = {
      today: "Aujourd’hui",
      previous: "Précédent",
      next: "Suivant",
      month: "Mois",
      week: "Semaine",
      day: "Jour",
      agenda: "Agenda",
      noEventsInRange: "Aucun événement prévu",
    };
  const dayOfWeek = days[currentDate.getDay()]; // Get day of the week (0-6)
  const day = currentDate.getDate(); // Get the day of the month (1-31)
  const monthIndex = currentDate.getMonth(); // Get the month (0-11)
  const year = currentDate.getFullYear(); // Get the full year (e.g., 2024)
  // Formulate the date string in French format
  const formattedDate = `${dayOfWeek} ${day} ${months[monthIndex]} ${year}`;
  return (
    <div className="border-base-200">
     
      <Calendar
        className="MonthView"
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        style={{ height: 600 }}
        selectable={true}
        onSelectSlot={handleSelectSlot}
        onSelectEvent={handleSelectedEvent}
        defaultView={Views.MONTH}
        views={[Views.MONTH]}
        min={moment().startOf("year").set({ hour: 8, minute: 15 }).toDate()}
        max={moment().endOf("year").set({ hour: 18, minute: 15 }).toDate()}
        step={15}
        timeslots={1}
        popup={true}
        messages={messages} 
        eventPropGetter={(event) => ({
          style: {
            backgroundColor: event.color,
            //  marginTop: "-15px",
          },
        })}
        components={{
          toolbar: CustomToolbarMonth,
          event: EventComponent,
          agenda: {
            event: EventComponent
          },
          day: {
            event: EventComponent
          },
          month: {
            event: EventComponent,
            dateHeader: ({ label }) => <span>{label}</span>
          },
          week: {
            event: EventComponent
          },
          // This is the correct way to add the showMore component
          showMore: CustomShowMore
        }}
        // components={{
        //   toolbar: CustomToolbarMonth,
        //   event: EventComponent,
        //   showMore: CustomShowMore
        // }}
      //   messages={{
      //     showMore: total => (
      //       <Box style={{cursor: 'auto'}}
      //            onMouseOver={e => {
      //                console.log(`onMouseOver`);
      //            }}
      //            onClick={e => {
      //                e.stopPropagation();
      //                e.preventDefault();
      //            }}>{`+${total} more`}
      //       </Box>
      //     )
      // }}
      />

      <Modal.Root
        opened={firstModalOpened}
        onClose={firstCloseModal}
        size="70%"
        scrollAreaComponent={ScrollArea.Autosize}
      >
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header>
     <Modal.Title>
        {" "}
        <Text fw={600} className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            viewBox="0 0 24 24"
          >
            <g
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            >
              <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
              <circle cx={16} cy={16} r={6}></circle>
            </g>
          </svg>
          {selectEvent
            ? "Modifier rendez-vous"
            : "Ajouter un rendez-vous"}
        </Text>
        <p className="text-muted-foreground text-sm">
          {selectEvent
            ? "Modifiez les détails de l'événement ci-dessous."
            : "Remplissez les détails ci-dessous pour ajouter un nouvel événement."}
        </p>
      </Modal.Title>
            <Modal.CloseButton className="mantine-focus-always" />
          </Modal.Header>
          <Divider my="md" />
          {/* <SimpleBar className="simplebar-scrollable-y h-full"> */}
          <Modal.Body>
            {" "}
            <div className="p-2">
              <div className="grid gap-3 py-4">
                <div className="flex gap-4">
                  <TextInput
                    id="event-title"
                    type="text"
                    placeholder="Titre de l'événement"
                    value={eventTitle}
                    onChange={(e) => setEventTitle(e.currentTarget.value)}
                    required
                    className="input input-bordered mb-2 w-full"
                  />
                  <TextInput
                    id="event-nom"
                    placeholder="Nom *"
                    type="text"
                    value={eventNom}
                    onChange={(e) => setEventNom(e.currentTarget.value)}
                    required
                    className="input input-bordered mb-2 w-full"
                  />
                  <TextInput
                    id="event-prenom"
                    placeholder="Prénom *"
                    type="text"
                    value={eventPrenom}
                    onChange={(e) => setEventPrenom(e.currentTarget.value)}
                    required
                    className="input input-bordered mb-2 w-full"
                  />
                </div>
                <div className="flex gap-4">
                  <TextInput
                    type="date"
                    placeholder="Date de Naissance"
                    id="event-dateDeNaissance"
                    value={eventDateDeNaissance}
                    onChange={handleDateChange}
                    required
                    className="input input-bordered mb-2 w-full"
                  />

                  <TextInput
                    type="text"
                    id="event-age"
                    value={eventAge?.toString() ?? ""}
                    placeholder={
                      eventAge !== null
                        ? eventAge.toString()
                        : "Please enter your date of birth"
                    }
                    readOnly
                    className="input input-bordered mb-2 w-full"
                  />

                  <div className="mb-2 flex items-center space-x-4">
                    <RadioButton
                      label="Homme"
                      value="Homme"
                      checked={eventSelectedOption === "Homme"}
                      onChange={handleOptionChange}
                    />
                    <RadioButton
                      label="Femme"
                      value="Femme"
                      checked={eventSelectedOption === "Femme"}
                      onChange={handleOptionChange}
                    />
                    <RadioButton
                      label="Enfant"
                      value="Enfant"
                      checked={eventSelectedOption === "Enfant"}
                      onChange={handleOptionChange}
                    />
                  </div>
                </div>
                <div className="flex gap-4">
                  <Select
                    value={eventEtatCivil}
                    onChange={(value) => setEventEtatCivil(value ?? "")}
                    placeholder="Votre état civil"
                    data={[
                      { value: "Célibataire", label: "Célibataire" },
                      { value: "Marié", label: "Marié" },
                      { value: "Autre chose", label: "Autre chose" },
                    ]}
                    className="select w-full max-w-xs"
                    required
                  />

                  {eventSelectedOption !== "Enfant" ? (
                    <TextInput
                      id="Cin"
                      placeholder="Cin"
                      value={eventCin}
                      onChange={(e) => setEventCin(e.target.value)}
                      className="input input-bordered mb-2 w-full"
                    />
                  ) : (
                    <TextInput
                      id="Cin"
                      placeholder="Cin"
                      disabled
                      className="input input-bordered mb-2 w-full"
                    />
                  )}

                  <TextInput
                    id="Adresse"
                    placeholder="Adressé par"
                    value={eventAdresse}
                    onChange={(e) => setEventAdresse(e.currentTarget.value)}
                    required
                    className="input input-bordered mb-2 w-full"
                  />
                </div>
                <div className="flex gap-4">
                  <TextInput
                    id="Téléphone"
                    placeholder="Téléphone"
                    value={eventTelephone}
                    onChange={(e) => setEventTelephone(e.target.value)}
                    className="input input-bordered mb-2 w-full"
                  />

                  <TextInput
                    id="Email"
                    placeholder="Email"
                    value={eventEmail}
                    onChange={(e) => setEventEmail(e.target.value)}
                    className="input input-bordered mb-2 w-full"
                  />
                </div>
                <Select
                  value={eventDocteur}
                  onChange={(value) => setEventDocteur(value ?? "")}
                  placeholder="Docteur"
                  data={[
                    { value: "Docteur", label: "Docteur" },
                    { value: "dr.Kader", label: "dr.Kader" },
                    { value: "dr.Kaders", label: "dr.Kaders" },
                  ]}
                  className="w-full"
                />

                <div className="flex gap-4">
                  <label htmlFor="event-Consultation" className="label">
                    Consultation
                  </label>
                  <Select
                    value={eventConsultation}
                    onChange={(value) => setEventConsultation(value ?? "")}
                    placeholder=" 15 min"
                    data={["15 min", "30 min", "45 min"]}
                    className="select w-full max-w-xs"
                  />

                  <button className="mb-2 w-full rounded-md bg-[#3799CE] p-[10px] text-[var(--mantine-Button-label-MB)] hover:bg-[#3799CE]/90">
                    Aganda
                  </button>
                </div>
                <div className="mx-auto flex gap-4">
                  <label htmlFor="event-date" className="label">
                    Date du rendez-vous
                  </label>

                  <TextInput
                    id="event-date"
                    type="date"
                    value={eventDate}
                    onChange={(e) => setEventDate(e.target.value)}
                    className="input input-bordered mb-2 w-64 max-w-64"
                  />
                  <label htmlFor="event-time" className="label">
                    De*
                  </label>
                  <TextInput
                    id="event-time"
                    type="time"
                    value={eventTime}
                    onChange={(e) => setEventTime(e.target.value)}
                    className="input input-bordered mb-2 w-64 max-w-64"
                  />
                  <label htmlFor="event-time" className="label">
                    à*
                  </label>
                  <TextInput
                    id="event-time"
                    type="text"
                    placeholder={
                      eventTime !== null
                        ? moment(eventTime, "HH:mm")
                            .add(parseInt(eventConsultation), "minutes")
                            .format("HH:mm")
                        : "Please enter your date of birth"
                    }
                    readOnly
                    className="input input-bordered mb-2 w-40"
                  />
                </div>
              </div>
              <div className="bg-base-100 px-[24px] py-[4px]">
                <ul className="text-daisy flex flex-wrap gap-x-4 gap-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      id="visit"
                      type="radio"
                      name="eventType"
                      value="visit"
                      aria-labelledby="r1"
                      aria-describedby="r1"
                      className="peer hidden"
                      checked={eventType === "visit"}
                      onChange={(e) =>
                        setEventType(e.target.value as EventType)
                      }
                    />

                    <label
                      htmlFor="visit"
                      className={`${
                        eventType === "visit"
                          ? "peer-checked:text-[#34D1BF]"
                          : "text-[var(--mantine-color-dark-0)]"
                      } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                    >
                      <li className="flex items-center gap-2 text-xs uppercase">
                        <span className="disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                        Visite de malade
                      </li>
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      id="visitor-counter"
                      type="radio"
                      name="eventType"
                      value="visitor-counter"
                      className="peer hidden"
                      checked={eventType === "visitor-counter"}
                      onChange={(e) =>
                        setEventType(e.target.value as EventType)
                      }
                    />

                    <label
                      htmlFor="visitor-counter"
                      className={`${
                        eventType === "visitor-counter"
                          ? "peer-checked:text-[#F17105]"
                          : "text-[var(--mantine-color-dark-0)]"
                      } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                    >
                      <li className="flex items-center gap-2 text-xs uppercase">
                        <span className="disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                        Visitor Counter
                      </li>
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      id="completed"
                      type="radio"
                      name="eventType"
                      value="completed"
                      aria-labelledby="r3"
                      aria-describedby="r3"
                      checked={eventType === "completed"}
                      onChange={(e) =>
                        setEventType(e.target.value as EventType)
                      }
                      className="peer hidden"
                    />
                    <label
                      htmlFor="completed"
                      className={`${
                        eventType === "completed"
                          ? "peer-checked:text-[#0496FF]"
                          : "text-[var(--mantine-color-dark-0)]"
                      } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                    >
                      <li className="flex items-center gap-2 text-xs uppercase">
                        <span className="disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                        Completed
                      </li>
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      id="diagnosis"
                      type="radio"
                      name="eventType"
                      value="diagnosis"
                      checked={eventType === "diagnosis"}
                      onChange={(e) =>
                        setEventType(e.target.value as EventType)
                      } // Explicitly cast the value
                      className="peer hidden"
                    />
                    <label
                      htmlFor="diagnosis"
                      className={`${
                        eventType === "diagnosis"
                          ? "peer-checked:text-[#ED0423]"
                          : "text-[var(--mantine-color-dark-0)]"
                      } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                    >
                      <li className="flex items-center gap-2 text-xs uppercase">
                        <span className="disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                        Re-diagnose
                      </li>
                    </label>
                  </div>
                </ul>
              </div>
              <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                <Button
                  onClick={() => {
                    saveEvent();
                    firstCloseModal();
                  }}
                  className="btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90"
                >
                  {selectEvent ? "Enregistrer" : "Ajouter"}
                </Button>
                {selectEvent && (
                  <Button
                    color="red"
                    onClick={deleteEvent}
                    className="btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90"
                  >
                    Supprimer
                    {/* l&apos;événement */}
                  </Button>
                )}
                <Button
                  onClick={firstCloseModal}
                  // onClick={resetForm}
                  className="btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90"
                >
                  Annuler
                </Button>
                <Button
                  className="btn mb-2 bg-[#1D86BA] text-[var(--mantine-Button-label-MB)] hover:bg-[#1D86BA]/90"
                  onClick={secondOpenModal}
                >
                  Montrer
                </Button>
              </div>
            </div>
          </Modal.Body>
          {/* </SimpleBar> */}
        </Modal.Content>
      </Modal.Root>
      <div className="bg-base-100 px-[24px] py-[20px]">
        <ul className="flex flex-wrap gap-x-4 gap-y-2 text-[var(--mantine-color-dark-0)]">
          {[
            { label: "Visite de malade", color: "disk-teal" },
            { label: "Visitor Counter", color: "disk-orange" },
            { label: "Completed", color: "disk-azure" },
            { label: "Re-diagnose", color: "disk-red" },
          ].map(({ label, color }) => (
            <li
              key={label}
              className="flex items-center gap-2 text-xs uppercase"
            >
              <span
                className={`${color} relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white`}
              />
              {label}
            </li>
          ))}
        </ul>
      </div>
      <Modal.Root
        opened={secondModalOpened}
        onClose={closeSecondModal}
        fullScreen={isMobile}
        transitionProps={{ transition: "fade", duration: 200 }}
      >
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>{eventTitle}</Modal.Title>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Body>
            <div className="my-2.5">
              <div className="border-base-200 border-2 border-l-[#3799CE] px-2.5 py-2">
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Nom Prenom:</span> {eventNom}{" "}
                  {eventPrenom}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Date de naissance :</span>{" "}
                  {eventDateDeNaissance}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Age:</span>{" "}
                  {eventAge !== null
                    ? eventAge.toString()
                    : "Please enter your date of birth"}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Réservation :</span>{" "}
                  {eventTime}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  {/* Sexe : {eventSexe ? "Homme" : "Femme"}*/}
                  <span className="text-[#999999]">Sexe :</span>{" "}
                  {eventSelectedOption}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Etat Civil :</span>
                  {eventEtatCivil}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Cin :</span>
                  {eventCin}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Adresse :</span>
                  {eventAdresse}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Email :</span>
                  {eventEmail}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Docteur :</span>{" "}
                  {eventDocteur}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Consultation :</span>
                  {eventConsultation}
                </p>
                <p className="m-0 text-sm font-normal leading-6">
                  <span className="text-[#999999]">Date du rendez-vous:</span>{" "}
                  {eventDate}
                </p>
                {/* eventTimeDifference:{eventTimeDifference} */}
              </div>
            </div>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </div>
  );
};

export default MonthView;
