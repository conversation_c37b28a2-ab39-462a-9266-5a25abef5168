'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Button,
  TextInput,
  Switch,
  Group,
  Stack,
  Text,
  Modal,
  Alert,
  ActionIcon,
  Divider,
  Select,
  FileInput
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconDashboard,
  IconDownload,
  IconList,
  IconPlus,
  IconUpload
} from '@tabler/icons-react';

// Types
interface FormTemplate {
  id?: number;
  name: string;
  label: string;
  category?: string;
  isHidden: boolean;
  forExam: boolean;
  blocks: FormBlock[];
}

interface FormBlock {
  id: string;
  type: string;
  content: Record<string, unknown>;
}

interface Category {
  id: number;
  name: string;
}

const Formulaires: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate>({
    name: '',
    label: '',
    category: '',
    isHidden: false,
    forExam: false,
    blocks: []
  });
  const [formTemplates] = useState<FormTemplate[]>([]);
  const [categories] = useState<Category[]>([]);
  const [importFile, setImportFile] = useState<File | null>(null);

  // Modals
  const [importModalOpened, { open: openImportModal, close: closeImportModal }] = useDisclosure(false);

  const handleSaveTemplate = () => {
    if (!selectedTemplate.name.trim() || !selectedTemplate.label.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'L\'identifiant et le titre sont obligatoires',
        color: 'red'
      });
      return;
    }

    notifications.show({
      title: 'Succès',
      message: 'Formulaire enregistré avec succès',
      color: 'green'
    });
  };

  const handleImportTemplate = () => {
    if (!importFile) {
      notifications.show({
        title: 'Erreur',
        message: 'Veuillez sélectionner un fichier JSON',
        color: 'red'
      });
      return;
    }

    notifications.show({
      title: 'Succès',
      message: 'Template importé avec succès',
      color: 'green'
    });
    closeImportModal();
    setImportFile(null);
  };

  const isFormValid = selectedTemplate.name.trim() !== '' && selectedTemplate.label.trim() !== '';

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-blue-600 text-white px-6 py-4 flex items-center gap-3">
        <IconDashboard size={24} />
        <Title order={2} className="text-white font-medium">
          Formulaires
        </Title>
        <div className="flex-1" />
        <Button
          leftSection={<IconDownload size={16} />}
          variant="filled"
          color="blue"
          onClick={openImportModal}
          className="bg-blue-500 hover:bg-blue-400"
        >
          Importer
        </Button>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <div className="w-80 border-r border-gray-200 flex flex-col">
          <div className="bg-blue-600 text-white px-4 py-3 flex items-center gap-3">
            <IconList size={20} />
            <Title order={4} className="text-white">Liste des formulaires</Title>
            <div className="flex-1" />
          </div>

          <div className="flex-1 p-4">
            {formTemplates.length === 0 ? (
              <Alert color="blue" className="text-sm">
                Aucun formulaire disponible
              </Alert>
            ) : (
              <Stack gap="xs">
                {formTemplates.map((template) => (
                  <Paper
                    key={template.id}
                    p="sm"
                    className="cursor-pointer hover:bg-gray-50 border"
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <Text size="sm" fw={500}>{template.label}</Text>
                    <Text size="xs" c="dimmed">{template.name}</Text>
                  </Paper>
                ))}
              </Stack>
            )}
          </div>
        </div>

        {/* Main Form */}
        <div className="flex-1 flex flex-col">
          <form onSubmit={(e) => { e.preventDefault(); handleSaveTemplate(); }} className="flex-1 flex flex-col p-6">
            <div className="grid grid-cols-4 gap-4 mb-6">
              <TextInput
                label="Identifiant"
                placeholder="Identifiant du formulaire"
                value={selectedTemplate.name}
                onChange={(e) => setSelectedTemplate(prev => ({ ...prev, name: e.target.value }))}
                required
                className="col-span-1"
              />

              <TextInput
                label="Titre"
                placeholder="Titre du formulaire"
                value={selectedTemplate.label}
                onChange={(e) => setSelectedTemplate(prev => ({ ...prev, label: e.target.value }))}
                required
                className="col-span-1"
              />

              <div className="col-span-1">
                <Select
                  label="Catégorie"
                  placeholder="Sélectionner une catégorie"
                  value={selectedTemplate.category}
                  onChange={(value) => setSelectedTemplate(prev => ({ ...prev, category: value || '' }))}
                  data={categories.map(cat => ({ value: cat.name, label: cat.name }))}
                  rightSection={
                    <ActionIcon variant="subtle" size="sm">
                      <IconPlus size={16} />
                    </ActionIcon>
                  }
                />
              </div>

              <div className="col-span-1 flex items-end gap-4">
                <Switch
                  label="Cachée"
                  checked={selectedTemplate.isHidden}
                  onChange={(e) => setSelectedTemplate(prev => ({ ...prev, isHidden: e.currentTarget.checked }))}
                  color="blue"
                />

                <Switch
                  label="Compte Rendu"
                  checked={selectedTemplate.forExam}
                  onChange={(e) => setSelectedTemplate(prev => ({ ...prev, forExam: e.currentTarget.checked }))}
                  color="blue"
                />
              </div>
            </div>

            <Divider className="mb-6" />

            {/* Form Template Blocks */}
            <div className="flex-1 flex flex-col items-center justify-center">
              <Text c="dimmed" size="lg" className="text-center">
                Zone de construction du formulaire
              </Text>
              <Text c="dimmed" size="sm" className="text-center mt-2">
                Les blocs du formulaire apparaîtront ici
              </Text>
            </div>
          </form>

          {/* Actions */}
          <div className="border-t border-gray-200 p-4 flex items-center justify-end">
            <Button
              type="submit"
              variant="filled"
              color="blue"
              disabled={!isFormValid}
              onClick={handleSaveTemplate}
            >
              Enregistrer
            </Button>
          </div>
        </div>
      </div>

      {/* Import Modal */}
      <Modal
        opened={importModalOpened}
        onClose={closeImportModal}
        title={
          <div className="flex items-center gap-2">
            <IconDownload size={20} className="text-blue-600" />
            <Text fw={600} className="text-blue-600">Importer un template</Text>
          </div>
        }
        size="md"
        centered
      >
        <Stack gap="md">
          <FileInput
            label="Nom du fichier"
            placeholder="Sélectionner un fichier JSON"
            value={importFile}
            onChange={setImportFile}
            accept=".json"
            leftSection={<IconUpload size={16} />}
          />

          <Group justify="flex-end" gap="sm">
            <Button
              variant="filled"
              color="blue"
              onClick={handleImportTemplate}
              disabled={!importFile}
            >
              Ouvrir
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={closeImportModal}
            >
              Annuler
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default Formulaires;
