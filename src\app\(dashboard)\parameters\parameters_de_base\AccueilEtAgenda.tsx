import React, { useState } from 'react';
import {
  Stack,
  Title,
  Tabs,
  Paper,
  Grid,
  Text,
  TextInput,
  Select,
  Switch,
  Button,
  Modal,
  Table,
  ActionIcon,
  Group,
  Checkbox,
  Tooltip,
  ScrollArea,
  Badge,
  ColorSwatch,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconSearch,
  IconCalendar,
  IconSettings,
  IconHome,
  IconX,
  IconBuilding,
  IconPalette,
} from '@tabler/icons-react';

// Types pour les données
interface Motif {
  id: number;
  code: string;
  description: string;
  duration: number;
  color: string;
  colorRayee: string;
}

interface Agenda {
  id: number;
  name: string;
  description: string;
  color: string;
  isResource: boolean;
}

interface Docteur {
  id: number;
  name: string;
  color: string;
}

interface Salle {
  id: number;
  name: string;
  description: string;
  color: string;
  type: 'attente' | 'consultation';
  capacity: number;
}

interface Evenement {
  id: number;
  title: string;
  medecins: string;
  dateDebut: string;
  dateFin: string;
  color: string;
  indisponible: boolean;
  permanent: boolean;
  touteJournee: boolean;
  tousLesJours: boolean;
  cabinet: boolean;
}

const AccueilEtAgenda = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string>('general');

  // États pour les modales
  const [motifModalOpened, setMotifModalOpened] = useState(false);
  const [agendaModalOpened, setAgendaModalOpened] = useState(false);
  const [salleModalOpened, setSalleModalOpened] = useState(false);
  const [evenementModalOpened, setEvenementModalOpened] = useState(false);
  const [editingMotif, setEditingMotif] = useState<Motif | null>(null);
  const [editingAgenda, setEditingAgenda] = useState<Agenda | null>(null);
  const [editingSalle, setEditingSalle] = useState<Salle | null>(null);
  const [editingEvenement, setEditingEvenement] = useState<Evenement | null>(null);

  // États pour les formulaires
  const [motifForm, setMotifForm] = useState({
    code: '',
    description: '',
    duration: 15,
    color: '#9b4d93',
    colorRayee: '#792485',
    agendaDefaut: '',
    servicesDesignes: '',
    couleurSombre: false,
  });

  const [agendaForm, setAgendaForm] = useState({
    name: '',
    description: '',
    color: '#9b4d93',
    servicesDesignes: '',
    isResource: false,
  });

  const [salleForm, setSalleForm] = useState({
    name: '',
    description: '',
    color: '#9b4d93',
    type: 'attente' as 'attente' | 'consultation',
    capacity: 1,
    servicesDesignes: '',
  });

  const [evenementForm, setEvenementForm] = useState({
    title: '',
    dateDebut: '',
    dateFin: '',
    color: '#9b4d93',
    indisponible: false,
    permanent: false,
    touteJournee: false,
    tousLesJours: false,
    cabinet: false,
  });

  // États pour les paramètres généraux
  const [generalSettings, setGeneralSettings] = useState({
    heureOuverture: '08:00',
    heureFermeture: '20:00',
    premierJourSemaine: 'Lundi',
    vueDefautCalendrier: 'Semaine',
    minimumIntervalle: '15 minutes',
    fuseauHoraire: 'Africa/Casablanca',
    cacherJoursFermeture: false,
    curseurAgendaHeureCurrent: true,
    filtre24hCabinet: true,
    utiliserAbbreviation: false,
    utiliserListePatients: false,
    afficherSeulementVisitesTerminees: false,
  });

  // Données de test pour les motifs
  const [motifs, setMotifs] = useState<Motif[]>([
    { id: 1, code: 'CNS', description: 'Consultation', duration: 15, color: '#9b4d93', colorRayee: '#792485' },
    { id: 2, code: 'CTR', description: 'Contrôle', duration: 15, color: '#c05889', colorRayee: '#7d1868' },
    { id: 3, code: 'CHI', description: 'Chirurgie/Paro', duration: 30, color: '#c14317', colorRayee: '#922a12' },
    { id: 4, code: 'COM', description: 'Composite', duration: 15, color: '#2836cd', colorRayee: '#091486' },
    { id: 5, code: 'DS', description: 'Dépose Sutures', duration: 15, color: '#2e7d32', colorRayee: '#1b5e20' },
    { id: 6, code: 'CS', description: '1er Consultation', duration: 15, color: '#e91e63', colorRayee: '#c2185b' },
    { id: 7, code: 'DEV', description: 'Devis', duration: 15, color: 'rgb(23, 160, 131)', colorRayee: '#024f3f' },
    { id: 8, code: 'END', description: 'Endodontie', duration: 60, color: '#a08523', colorRayee: '#564507' },
    { id: 9, code: 'FOR', description: 'Formation', duration: 60, color: '#982552', colorRayee: '#72183c' },
    { id: 10, code: 'IMP', description: 'Implantologie', duration: 60, color: '#388983', colorRayee: '#26645e' },
    { id: 11, code: 'ORT', description: 'Orthodontie', duration: 30, color: '#464098', colorRayee: '#1a7c81' },
    { id: 12, code: 'CM', description: 'FS Chassis/Monta', duration: 15, color: '#6c407a', colorRayee: '#9c4168' },
    { id: 13, code: 'POS', description: 'PA Pose', duration: 15, color: '#b25953', colorRayee: '#9c3e3e' },
    { id: 14, code: 'EMP', description: 'PC TP EMP', duration: 30, color: '#4c8d57', colorRayee: '#366e42' },
    { id: 15, code: 'ARM', description: 'PC ESS Armature', duration: 30, color: '#3291ef', colorRayee: '#0d667b' },
    { id: 16, code: 'SCE', description: 'PC Scellement', duration: 15, color: '#c2185b', colorRayee: '#880e4f' },
    { id: 17, code: 'PRO', description: 'Prophylaxie', duration: 15, color: '#009688', colorRayee: '#00695c' },
  ]);

  // Données de test pour les agendas
  const [agendas, setAgendas] = useState<Agenda[]>([
    { id: 1, name: 'Domicile', description: '', color: '#f4511e', isResource: false },
    { id: 2, name: 'Clinique', description: '', color: '#43a047', isResource: false },
    { id: 3, name: 'Cabinet', description: '', color: '#0097a7', isResource: false },
  ]);

  // Données de test pour les docteurs (Code couleurs médecine)
  const [docteurs, setDocteurs] = useState<Docteur[]>([
    { id: 1, name: 'Docteur', color: '#d50000' },
  ]);

  // Données de test pour les salles
  const [salles, setSalles] = useState<Salle[]>([
    { id: 1, name: 'SLT', description: 'Salle d\'attente', color: '#039be5', type: 'attente', capacity: 30 },
    { id: 2, name: 'FTL', description: 'Fauteuil 1', color: '#ff5722', type: 'consultation', capacity: 1 },
    { id: 3, name: 'FT 1', description: '', color: '#000000', type: 'consultation', capacity: 1 },
    { id: 4, name: 'SALLE DE CONSULTATION 2', description: '', color: '#000000', type: 'consultation', capacity: 1 },
  ]);

  // Données de test pour les événements
  const [evenements, setEvenements] = useState<Evenement[]>([
    {
      id: 1,
      title: 'CONGRES',
      medecins: 'DEMO DEMO',
      dateDebut: '21/04/2025',
      dateFin: '23/04/2025',
      color: '#f52dbe',
      indisponible: true,
      permanent: false,
      touteJournee: true,
      tousLesJours: false,
      cabinet: false
    },
  ]);

  // États pour la recherche
  const [searchTerm, setSearchTerm] = useState('');

  // Filtrer les motifs selon la recherche
  const filteredMotifs = motifs.filter(motif =>
    motif.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    motif.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Jours de la semaine
  const joursSemaine = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];

  // Options pour les selects
  const vueCalendrierOptions = [
    { value: 'Semaine', label: 'Semaine' },
    { value: 'Mois', label: 'Mois' },
    { value: 'Jour', label: 'Jour' },
  ];

  const intervalleOptions = [
    { value: '5 minutes', label: '5 minutes' },
    { value: '10 minutes', label: '10 minutes' },
    { value: '15 minutes', label: '15 minutes' },
    { value: '30 minutes', label: '30 minutes' },
  ];

  const fuseauHoraireOptions = [
    { value: 'Africa/Casablanca', label: 'Africa/Casablanca' },
    { value: 'Europe/Paris', label: 'Europe/Paris' },
    { value: 'UTC', label: 'UTC' },
  ];

  const agendaDefautOptions = [
    { value: 'agenda1', label: 'Agenda par défaut' },
    { value: 'agenda2', label: 'Agenda 2' },
  ];

  const servicesDesignesOptions = [
    { value: 'service1', label: 'Services désignés' },
    { value: 'service2', label: 'Service 2' },
  ];

  // Fonctions pour gérer les modales
  const openMotifModal = (motif?: Motif) => {
    if (motif) {
      setEditingMotif(motif);
      setMotifForm({
        code: motif.code,
        description: motif.description,
        duration: motif.duration,
        color: motif.color,
        colorRayee: motif.colorRayee,
        agendaDefaut: '',
        servicesDesignes: '',
        couleurSombre: false,
      });
    } else {
      setEditingMotif(null);
      setMotifForm({
        code: '',
        description: '',
        duration: 15,
        color: '#9b4d93',
        colorRayee: '#792485',
        agendaDefaut: '',
        servicesDesignes: '',
        couleurSombre: false,
      });
    }
    setMotifModalOpened(true);
  };

  const openAgendaModal = (agenda?: Agenda) => {
    if (agenda) {
      setEditingAgenda(agenda);
      setAgendaForm({
        name: agenda.name,
        description: agenda.description,
        color: agenda.color,
        servicesDesignes: '',
        isResource: agenda.isResource,
      });
    } else {
      setEditingAgenda(null);
      setAgendaForm({
        name: '',
        description: '',
        color: '#9b4d93',
        servicesDesignes: '',
        isResource: false,
      });
    }
    setAgendaModalOpened(true);
  };

  const openSalleModal = (salle?: Salle) => {
    if (salle) {
      setEditingSalle(salle);
      setSalleForm({
        name: salle.name,
        description: salle.description,
        color: salle.color,
        type: salle.type,
        capacity: salle.capacity,
        servicesDesignes: '',
      });
    } else {
      setEditingSalle(null);
      setSalleForm({
        name: '',
        description: '',
        color: '#9b4d93',
        type: 'attente',
        capacity: 1,
        servicesDesignes: '',
      });
    }
    setSalleModalOpened(true);
  };

  const openEvenementModal = (evenement?: Evenement) => {
    if (evenement) {
      setEditingEvenement(evenement);
      setEvenementForm({
        title: evenement.title,
        dateDebut: evenement.dateDebut,
        dateFin: evenement.dateFin,
        color: evenement.color,
        indisponible: evenement.indisponible,
        permanent: evenement.permanent,
        touteJournee: evenement.touteJournee,
        tousLesJours: evenement.tousLesJours,
        cabinet: evenement.cabinet,
      });
    } else {
      setEditingEvenement(null);
      setEvenementForm({
        title: '',
        dateDebut: '',
        dateFin: '',
        color: '#9b4d93',
        indisponible: false,
        permanent: false,
        touteJournee: false,
        tousLesJours: false,
        cabinet: false,
      });
    }
    setEvenementModalOpened(true);
  };

  // Fonctions pour sauvegarder
  const handleSaveMotif = () => {
    if (editingMotif) {
      // Modifier un motif existant
      setMotifs(prev => prev.map(m =>
        m.id === editingMotif.id
          ? { ...m, ...motifForm }
          : m
      ));
    } else {
      // Ajouter un nouveau motif
      const newMotif: Motif = {
        id: Date.now(),
        code: motifForm.code,
        description: motifForm.description,
        duration: motifForm.duration,
        color: motifForm.color,
        colorRayee: motifForm.colorRayee,
      };
      setMotifs(prev => [...prev, newMotif]);
    }
    setMotifModalOpened(false);
  };

  const handleSaveAgenda = () => {
    if (editingAgenda) {
      // Modifier un agenda existant
      setAgendas(prev => prev.map(a =>
        a.id === editingAgenda.id
          ? { ...a, ...agendaForm }
          : a
      ));
    } else {
      // Ajouter un nouvel agenda
      const newAgenda: Agenda = {
        id: Date.now(),
        name: agendaForm.name,
        description: agendaForm.description,
        color: agendaForm.color,
        isResource: agendaForm.isResource,
      };
      setAgendas(prev => [...prev, newAgenda]);
    }
    setAgendaModalOpened(false);
  };

  // Fonction pour supprimer un motif
  const handleDeleteMotif = (id: number) => {
    setMotifs(prev => prev.filter(m => m.id !== id));
  };

  return (
    <Stack gap="lg" className="w-full">
      <Title order={2} className="text-gray-800 flex items-center gap-2">
        <IconHome size={24} className="text-blue-600" />
        Accueil et Agenda
      </Title>

      <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'general')} variant="outline">
        <Tabs.List>
          <Tabs.Tab value="general" leftSection={<IconSettings size={16} />}>
            Général
          </Tabs.Tab>
          <Tabs.Tab value="motifs" leftSection={<IconCalendar size={16} />}>
            Motifs
          </Tabs.Tab>
          <Tabs.Tab value="code-couleurs" leftSection={<IconPalette size={16} />}>
            Code couleurs médecin
          </Tabs.Tab>
          <Tabs.Tab value="agendas" leftSection={<IconCalendar size={16} />}>
            Agendas
          </Tabs.Tab>
          <Tabs.Tab value="salles" leftSection={<IconBuilding size={16} />}>
            Salles
          </Tabs.Tab>
          <Tabs.Tab value="evenements" leftSection={<IconSettings size={16} />}>
            Événements
          </Tabs.Tab>
        </Tabs.List>

        {/* Onglet Général */}
        <Tabs.Panel value="general" pt="md">
          <Paper p="md" withBorder>
            <Stack gap="lg">
              {/* Horaires du centre */}
              <div>
                <Title order={4} className="text-gray-700 mb-md">
                  Horaires du centre
                </Title>
                <Grid>
                  <Grid.Col span={6}>
                    <Text size="sm" fw={500} mb="xs">
                      Heure d'ouverture(s)
                    </Text>
                    <TextInput
                      value={generalSettings.heureOuverture}
                      onChange={(e) => setGeneralSettings(prev => ({
                        ...prev,
                        heureOuverture: e.target.value
                      }))}
                      placeholder="08:00"
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text size="sm" fw={500} mb="xs">
                      Heure de fermeture(s)
                    </Text>
                    <TextInput
                      value={generalSettings.heureFermeture}
                      onChange={(e) => setGeneralSettings(prev => ({
                        ...prev,
                        heureFermeture: e.target.value
                      }))}
                      placeholder="20:00"
                    />
                  </Grid.Col>
                </Grid>

                {/* Jours de fermeture */}
                <div className="mt-4">
                  <Text size="sm" fw={500} mb="xs">
                    Jours de fermeture
                  </Text>
                  <div className="flex gap-2 flex-wrap">
                    {joursSemaine.map((jour) => (
                      <Badge
                        key={jour}
                        variant="light"
                        color="blue"
                        className="cursor-pointer"
                      >
                        {jour}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Paramètres d'affichage */}
              <div>
                <Title order={4} className="text-gray-700 mb-md">
                  Paramètres d'affichage
                </Title>
                <Grid>
                  <Grid.Col span={6}>
                    <Select
                      label="Premier jour de la semaine"
                      value={generalSettings.premierJourSemaine}
                      onChange={(value) => setGeneralSettings(prev => ({
                        ...prev,
                        premierJourSemaine: value || 'Lundi'
                      }))}
                      data={joursSemaine.map(jour => ({ value: jour, label: jour }))}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Select
                      label="Vue par défaut du calendrier"
                      value={generalSettings.vueDefautCalendrier}
                      onChange={(value) => setGeneralSettings(prev => ({
                        ...prev,
                        vueDefautCalendrier: value || 'Semaine'
                      }))}
                      data={vueCalendrierOptions}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Select
                      label="Minimum intervalle de temps"
                      value={generalSettings.minimumIntervalle}
                      onChange={(value) => setGeneralSettings(prev => ({
                        ...prev,
                        minimumIntervalle: value || '15 minutes'
                      }))}
                      data={intervalleOptions}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Select
                      label="Fuseau horaire"
                      value={generalSettings.fuseauHoraire}
                      onChange={(value) => setGeneralSettings(prev => ({
                        ...prev,
                        fuseauHoraire: value || 'Africa/Casablanca'
                      }))}
                      data={fuseauHoraireOptions}
                    />
                  </Grid.Col>
                </Grid>

                {/* Options d'affichage */}
                <Stack gap="xs" mt="md">
                  <Checkbox
                    checked={generalSettings.cacherJoursFermeture}
                    onChange={(e) => setGeneralSettings(prev => ({
                      ...prev,
                      cacherJoursFermeture: e.currentTarget.checked
                    }))}
                    label="Cacher les jours de fermeture"
                  />
                  <Checkbox
                    checked={generalSettings.curseurAgendaHeureCurrent}
                    onChange={(e) => setGeneralSettings(prev => ({
                      ...prev,
                      curseurAgendaHeureCurrent: e.currentTarget.checked
                    }))}
                    label="Curseur d'agenda à l'heure current"
                  />
                  <Checkbox
                    checked={generalSettings.filtre24hCabinet}
                    onChange={(e) => setGeneralSettings(prev => ({
                      ...prev,
                      filtre24hCabinet: e.currentTarget.checked
                    }))}
                    label="Filtre de 24h du cabinet"
                  />
                  <Checkbox
                    checked={generalSettings.utiliserAbbreviation}
                    onChange={(e) => setGeneralSettings(prev => ({
                      ...prev,
                      utiliserAbbreviation: e.currentTarget.checked
                    }))}
                    label="Utiliser abréviation"
                  />
                  <Checkbox
                    checked={generalSettings.utiliserListePatients}
                    onChange={(e) => setGeneralSettings(prev => ({
                      ...prev,
                      utiliserListePatients: e.currentTarget.checked
                    }))}
                    label="Utiliser la liste des patients"
                  />
                  <Checkbox
                    checked={generalSettings.afficherSeulementVisitesTerminees}
                    onChange={(e) => setGeneralSettings(prev => ({
                      ...prev,
                      afficherSeulementVisitesTerminees: e.currentTarget.checked
                    }))}
                    label="Afficher seulement les visites terminées dans 'Historique journalier'"
                  />
                </Stack>
              </div>

              {/* Rappel de rendez-vous */}
              <div>
                <Title order={4} className="text-gray-700 mb-md">
                  Rappel de rendez-vous
                </Title>
                <Text size="sm" fw={500} mb="xs">
                  Durée avant RDV
                </Text>
                <Grid>
                  <Grid.Col span={6}>
                    <Select
                      placeholder="5"
                      data={[
                        { value: '5', label: 'Modèle SMS' },
                        { value: '10', label: '10 minutes' },
                        { value: '15', label: '15 minutes' },
                      ]}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Select
                      placeholder="Modèle Email"
                      data={[
                        { value: 'email1', label: 'Modèle Email' },
                        { value: 'email2', label: 'Email 2' },
                      ]}
                    />
                  </Grid.Col>
                </Grid>
              </div>
            </Stack>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Motifs */}
        <Tabs.Panel value="motifs" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-4">
                <TextInput
                  placeholder="Rechercher"
                  leftSection={<IconSearch size={16} />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openMotifModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter nouveau motif
              </Button>
            </div>

            <ScrollArea>
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Code</Table.Th>
                    <Table.Th>Motif</Table.Th>
                    <Table.Th>Durée (min)</Table.Th>
                    <Table.Th>Couleur</Table.Th>
                    <Table.Th>Couleur rayée</Table.Th>
                    <Table.Th style={{ width: 100 }}></Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {filteredMotifs.map((motif) => (
                    <Table.Tr key={motif.id}>
                      <Table.Td>
                        <Text fw={500}>{motif.code}</Text>
                      </Table.Td>
                      <Table.Td>{motif.description}</Table.Td>
                      <Table.Td>{motif.duration}</Table.Td>
                      <Table.Td>
                        <div className="flex items-center gap-2">
                          <ColorSwatch color={motif.color} size={20} />
                          <Text size="sm">{motif.color}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div className="flex items-center gap-2">
                          <ColorSwatch color={motif.colorRayee} size={20} />
                          <Text size="sm">{motif.colorRayee}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openMotifModal(motif)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteMotif(motif.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Code couleurs médecin */}
        <Tabs.Panel value="code-couleurs" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-4">
                <Select
                  placeholder="Docteur"
                  data={docteurs.map(d => ({ value: d.id.toString(), label: d.name }))}
                  style={{ width: 200 }}
                />
                <div className="flex items-center gap-2">
                  <Text size="sm" fw={500}>Couleur</Text>
                  <ColorSwatch color="#d50000" size={20} />
                </div>
              </div>
              <Button
                variant="filled"
                color="gray"
              >
                Enregistrer
              </Button>
            </div>

            <ScrollArea>
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Docteur</Table.Th>
                    <Table.Th>Couleur</Table.Th>
                    <Table.Th style={{ width: 100 }}></Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {docteurs.map((docteur) => (
                    <Table.Tr key={docteur.id}>
                      <Table.Td>
                        <Text fw={500}>{docteur.name}</Text>
                      </Table.Td>
                      <Table.Td>
                        <div className="flex items-center gap-2">
                          <ColorSwatch color={docteur.color} size={20} />
                          <Text size="sm">{docteur.color}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                            >
                              <IconX size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Agendas */}
        <Tabs.Panel value="agendas" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <Title order={4}>Agendas</Title>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openAgendaModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Nouvel agenda
              </Button>
            </div>

            <ScrollArea>
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Description</Table.Th>
                    <Table.Th>Couleur</Table.Th>
                    <Table.Th>Ressource</Table.Th>
                    <Table.Th style={{ width: 100 }}></Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {agendas.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={5} className="text-center text-gray-500 py-8">
                        Aucun agenda trouvé
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    agendas.map((agenda) => (
                      <Table.Tr key={agenda.id}>
                        <Table.Td>
                          <Text fw={500}>{agenda.name}</Text>
                        </Table.Td>
                        <Table.Td>{agenda.description}</Table.Td>
                        <Table.Td>
                          <div className="flex items-center gap-2">
                            <ColorSwatch color={agenda.color} size={20} />
                            <Text size="sm">{agenda.color}</Text>
                          </div>
                        </Table.Td>
                        <Table.Td>
                          <Badge color={agenda.isResource ? 'green' : 'gray'}>
                            {agenda.isResource ? 'Oui' : 'Non'}
                          </Badge>
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs" justify="center">
                            <Tooltip label="Modifier">
                              <ActionIcon
                                variant="subtle"
                                color="blue"
                                onClick={() => openAgendaModal(agenda)}
                              >
                                <IconEdit size={16} />
                              </ActionIcon>
                            </Tooltip>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Salles */}
        <Tabs.Panel value="salles" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <Title order={4}>Salles</Title>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openSalleModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter salle
              </Button>
            </div>

            <ScrollArea>
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Description</Table.Th>
                    <Table.Th>Couleur</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Capacité</Table.Th>
                    <Table.Th style={{ width: 100 }}></Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {salles.map((salle) => (
                    <Table.Tr key={salle.id}>
                      <Table.Td>
                        <Text fw={500}>{salle.name}</Text>
                      </Table.Td>
                      <Table.Td>{salle.description}</Table.Td>
                      <Table.Td>
                        <div className="flex items-center gap-2">
                          <ColorSwatch color={salle.color} size={20} />
                          <Text size="sm">{salle.color}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={salle.type === 'attente' ? 'blue' : 'pink'}
                          variant="light"
                        >
                          {salle.type === 'attente' ? 'Salle d\'attente' : 'Salle de consultation'}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text>{salle.capacity}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openSalleModal(salle)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Événements */}
        <Tabs.Panel value="evenements" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <Title order={4}>Événements</Title>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openEvenementModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Nouvel événement
              </Button>
            </div>

            <ScrollArea>
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Titre</Table.Th>
                    <Table.Th>Médecins</Table.Th>
                    <Table.Th>Disponibilité</Table.Th>
                    <Table.Th>Début</Table.Th>
                    <Table.Th>Fin</Table.Th>
                    <Table.Th>de</Table.Th>
                    <Table.Th>à</Table.Th>
                    <Table.Th style={{ width: 100 }}></Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {evenements.map((evenement) => (
                    <Table.Tr key={evenement.id}>
                      <Table.Td>
                        <Text fw={500}>{evenement.title}</Text>
                      </Table.Td>
                      <Table.Td>{evenement.medecins}</Table.Td>
                      <Table.Td>
                        <div className="flex items-center gap-2">
                          <ColorSwatch
                            color={evenement.indisponible ? '#f52dbe' : '#4caf50'}
                            size={20}
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>{evenement.dateDebut}</Table.Td>
                      <Table.Td>{evenement.dateFin}</Table.Td>
                      <Table.Td>de</Table.Td>
                      <Table.Td>à</Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openEvenementModal(evenement)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>
      </Tabs>

      {/* Modal pour Motif */}
      <Modal
        opened={motifModalOpened}
        onClose={() => setMotifModalOpened(false)}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconCalendar size={20} />
            <Text fw={600}>Actes</Text>
          </div>
        }
        size="lg"
        withCloseButton={false}
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={<span>Code <span className="text-red-500">*</span></span>}
                value={motifForm.code}
                onChange={(e) => setMotifForm(prev => ({ ...prev, code: e.target.value }))}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={<span>Description <span className="text-red-500">*</span></span>}
                value={motifForm.description}
                onChange={(e) => setMotifForm(prev => ({ ...prev, description: e.target.value }))}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={<span>Durée (min) <span className="text-red-500">*</span></span>}
                value={motifForm.duration.toString()}
                onChange={(e) => setMotifForm(prev => ({ ...prev, duration: parseInt(e.target.value) || 15 }))}
                type="number"
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs">Couleur</Text>
                <div className="flex items-center gap-2">
                  <ColorSwatch
                    color={motifForm.color}
                    size={30}
                    className="cursor-pointer border border-gray-300"
                  />
                </div>
              </div>
            </Grid.Col>
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs">Couleur rayée</Text>
                <div className="flex items-center gap-2">
                  <ColorSwatch
                    color={motifForm.colorRayee}
                    size={30}
                    className="cursor-pointer border border-gray-300"
                  />
                </div>
              </div>
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <Select
                label="Agenda par défaut"
                value={motifForm.agendaDefaut}
                onChange={(value) => setMotifForm(prev => ({ ...prev, agendaDefaut: value || '' }))}
                data={agendaDefautOptions}
                rightSection={<IconPlus size={16} className="cursor-pointer text-blue-500" />}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Services désignés"
                value={motifForm.servicesDesignes}
                onChange={(value) => setMotifForm(prev => ({ ...prev, servicesDesignes: value || '' }))}
                data={servicesDesignesOptions}
              />
            </Grid.Col>
          </Grid>

          <div className="flex items-center justify-between pt-4 border-t">
            <Switch
              checked={motifForm.couleurSombre}
              onChange={(e) => setMotifForm(prev => ({ ...prev, couleurSombre: e.currentTarget.checked }))}
              label="Couleur sombre"
              labelPosition="right"
            />

            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={() => setMotifModalOpened(false)}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSaveMotif}
                disabled={!motifForm.code || !motifForm.description}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Agenda */}
      <Modal
        opened={agendaModalOpened}
        onClose={() => setAgendaModalOpened(false)}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconCalendar size={20} />
            <Text fw={600}>Agenda</Text>
          </div>
        }
        size="lg"
        withCloseButton={false}
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={8}>
              <TextInput
                label={<span>Nom <span className="text-red-500">*</span></span>}
                value={agendaForm.name}
                onChange={(e) => setAgendaForm(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <div>
                <Text size="sm" fw={500} mb="xs">Couleur</Text>
                <div className="flex items-center gap-2">
                  <ColorSwatch
                    color={agendaForm.color}
                    size={30}
                    className="cursor-pointer border border-gray-300"
                  />
                </div>
              </div>
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Description"
                value={agendaForm.description}
                onChange={(e) => setAgendaForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Services désignés"
                value={agendaForm.servicesDesignes}
                onChange={(value) => setAgendaForm(prev => ({ ...prev, servicesDesignes: value || '' }))}
                data={servicesDesignesOptions}
              />
            </Grid.Col>
          </Grid>

          <div className="flex items-center justify-between pt-4 border-t">
            <Switch
              checked={agendaForm.isResource}
              onChange={(e) => setAgendaForm(prev => ({ ...prev, isResource: e.currentTarget.checked }))}
              label="Afficher comme ressource"
              labelPosition="right"
            />

            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={() => setAgendaModalOpened(false)}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSaveAgenda}
                disabled={!agendaForm.name}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Salle */}
      <Modal
        opened={salleModalOpened}
        onClose={() => setSalleModalOpened(false)}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconBuilding size={20} />
            <Text fw={600}>Ajout du salle</Text>
          </div>
        }
        size="lg"
        withCloseButton={false}
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={<span>Nom <span className="text-red-500">*</span></span>}
                value={salleForm.name}
                onChange={(e) => setSalleForm(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label={<span>Capacité <span className="text-red-500">*</span></span>}
                value={salleForm.capacity.toString()}
                onChange={(e) => setSalleForm(prev => ({ ...prev, capacity: parseInt(e.target.value) || 1 }))}
                type="number"
                required
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <div>
                <Text size="sm" fw={500} mb="xs">Couleur</Text>
                <div className="flex items-center gap-2">
                  <ColorSwatch
                    color={salleForm.color}
                    size={30}
                    className="cursor-pointer border border-gray-300"
                  />
                </div>
              </div>
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Description"
                value={salleForm.description}
                onChange={(e) => setSalleForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Services désignés"
                value={salleForm.servicesDesignes}
                onChange={(value) => setSalleForm(prev => ({ ...prev, servicesDesignes: value || '' }))}
                data={servicesDesignesOptions}
              />
            </Grid.Col>
          </Grid>

          <div>
            <Text size="sm" fw={500} mb="xs">Type</Text>
            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="salle-attente"
                  name="salleType"
                  checked={salleForm.type === 'attente'}
                  onChange={() => setSalleForm(prev => ({ ...prev, type: 'attente' }))}
                />
                <label htmlFor="salle-attente">Salle d'attente</label>
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="salle-consultation"
                  name="salleType"
                  checked={salleForm.type === 'consultation'}
                  onChange={() => setSalleForm(prev => ({ ...prev, type: 'consultation' }))}
                />
                <label htmlFor="salle-consultation">Salle de consultation</label>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="red"
                onClick={() => setSalleModalOpened(false)}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                disabled={!salleForm.name}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Événement */}
      <Modal
        opened={evenementModalOpened}
        onClose={() => setEvenementModalOpened(false)}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconCalendar size={20} />
            <Text fw={600}>Gérer événement</Text>
          </div>
        }
        size="lg"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label={<span>Titre <span className="text-red-500">*</span></span>}
            value={evenementForm.title}
            onChange={(e) => setEvenementForm(prev => ({ ...prev, title: e.target.value }))}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={<span>Date début <span className="text-red-500">*</span></span>}
                value={evenementForm.dateDebut}
                onChange={(e) => setEvenementForm(prev => ({ ...prev, dateDebut: e.target.value }))}
                placeholder="DD/MM/YYYY"
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={<span>Date fin <span className="text-red-500">*</span></span>}
                value={evenementForm.dateFin}
                onChange={(e) => setEvenementForm(prev => ({ ...prev, dateFin: e.target.value }))}
                placeholder="DD/MM/YYYY"
                required
              />
            </Grid.Col>
          </Grid>

          <div>
            <Text size="sm" fw={500} mb="xs">Couleur</Text>
            <div className="flex items-center gap-2">
              <ColorSwatch
                color={evenementForm.color}
                size={30}
                className="cursor-pointer border border-gray-300"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <div
                className={`w-4 h-4 rounded-full cursor-pointer ${evenementForm.indisponible ? 'bg-red-500' : 'bg-gray-300'}`}
                onClick={() => setEvenementForm(prev => ({ ...prev, indisponible: !prev.indisponible }))}
              />
              <Text size="sm">Indisponible</Text>
            </div>
            <div className="flex items-center gap-2">
              <div
                className={`w-4 h-4 rounded-full cursor-pointer ${evenementForm.permanent ? 'bg-gray-500' : 'bg-gray-300'}`}
                onClick={() => setEvenementForm(prev => ({ ...prev, permanent: !prev.permanent }))}
              />
              <Text size="sm">Permanent</Text>
            </div>
            <div className="flex items-center gap-2">
              <div
                className={`w-4 h-4 rounded-full cursor-pointer ${evenementForm.touteJournee ? 'bg-blue-500' : 'bg-gray-300'}`}
                onClick={() => setEvenementForm(prev => ({ ...prev, touteJournee: !prev.touteJournee }))}
              />
              <Text size="sm">Toute la journée</Text>
            </div>
            <div className="flex items-center gap-2">
              <div
                className={`w-4 h-4 rounded-full cursor-pointer ${evenementForm.tousLesJours ? 'bg-blue-500' : 'bg-gray-300'}`}
                onClick={() => setEvenementForm(prev => ({ ...prev, tousLesJours: !prev.tousLesJours }))}
              />
              <Text size="sm">Tous les jours</Text>
            </div>
            <div className="flex items-center gap-2">
              <div
                className={`w-4 h-4 rounded-full cursor-pointer ${evenementForm.cabinet ? 'bg-blue-500' : 'bg-gray-300'}`}
                onClick={() => setEvenementForm(prev => ({ ...prev, cabinet: !prev.cabinet }))}
              />
              <Text size="sm">Cabinet</Text>
            </div>
          </div>

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="red"
                onClick={() => setEvenementModalOpened(false)}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                disabled={!evenementForm.title}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default AccueilEtAgenda;
