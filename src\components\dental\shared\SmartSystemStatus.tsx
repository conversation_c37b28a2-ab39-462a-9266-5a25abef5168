// frontend/dental_medicine/src/components/content/dental/shared/SmartSystemStatus.tsx

import React, { useState, useEffect } from 'react';
import { Badge, Group, Text, Tooltip, ActionIcon } from '@mantine/core';
import { IconBrain, IconCheck, IconAlertTriangle, IconInfoCircle } from '@tabler/icons-react';
import { treatmentManager } from './treatmentManager';

interface SmartSystemStatusProps {
  isEnabled: boolean;
}

export const SmartSystemStatus: React.FC<SmartSystemStatusProps> = ({ isEnabled }) => {
  const [stats, setStats] = useState({
    totalTeeth: 32,
    treatedTeeth: 0,
    conflictsResolved: 0,
    compatibleTreatments: 0
  });

  useEffect(() => {
    // Simuler la récupération des statistiques
    const updateStats = () => {
      let treatedCount = 0;
      let conflictCount = 0;
      let compatibleCount = 0;

      // Parcourir toutes les dents pour calculer les statistiques
      for (let i = 1; i <= 32; i++) {
        const treatments = treatmentManager.getCurrentTreatments(i.toString());
        if (treatments.length > 0) {
          treatedCount++;
          
          // Vérifier les conflits
          treatments.forEach(treatment => {
            const conflicts = treatmentManager.canApplyTreatment(treatment, i.toString());
            if (conflicts.length > 0) {
              conflictCount++;
            } else {
              compatibleCount++;
            }
          });
        }
      }

      setStats({
        totalTeeth: 32,
        treatedTeeth: treatedCount,
        conflictsResolved: conflictCount,
        compatibleTreatments: compatibleCount
      });
    };

    // Mettre à jour les stats toutes les 2 secondes
    const interval = setInterval(updateStats, 2000);
    updateStats(); // Mise à jour initiale

    return () => clearInterval(interval);
  }, []);

  if (!isEnabled) {
    return (
      <Badge color="gray" variant="light" size="sm">
        <Group gap={4}>
          <IconBrain size={12} />
          <Text size="xs">Système Désactivé</Text>
        </Group>
      </Badge>
    );
  }

  return (
    <Group gap={8}>
      <Badge color="green" variant="light" size="sm">
        <Group gap={4}>
          <IconBrain size={12} />
          <Text size="xs">IA Activée</Text>
        </Group>
      </Badge>

      <Tooltip label="Dents avec traitements appliqués">
        <Badge color="blue" variant="outline" size="sm">
          <Group gap={4}>
            <IconCheck size={12} />
            <Text size="xs">{stats.treatedTeeth}/{stats.totalTeeth}</Text>
          </Group>
        </Badge>
      </Tooltip>

      {stats.conflictsResolved > 0 && (
        <Tooltip label="Conflits détectés et résolus">
          <Badge color="orange" variant="outline" size="sm">
            <Group gap={4}>
              <IconAlertTriangle size={12} />
              <Text size="xs">{stats.conflictsResolved}</Text>
            </Group>
          </Badge>
        </Tooltip>
      )}

      <Tooltip label="Traitements compatibles appliqués">
        <Badge color="teal" variant="outline" size="sm">
          <Group gap={4}>
            <IconInfoCircle size={12} />
            <Text size="xs">{stats.compatibleTreatments}</Text>
          </Group>
        </Badge>
      </Tooltip>
    </Group>
  );
};
