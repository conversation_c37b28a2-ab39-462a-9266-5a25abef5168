'use client';
import React, { useState } from 'react';
import {
  Title,
  Group,
  ActionIcon,
  Tooltip,
  Table,
  Text,
  Card,
  Box,
  Radio,
  Stack,
  Modal,
  Button,
  Checkbox,
  Divider,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useDisclosure } from '@mantine/hooks';
import {
  IconPrinter,
  IconFileExport,
  IconFileText,
  IconTable,
  IconSettings,
  IconCalendarEvent,
  IconCalendar,
} from '@tabler/icons-react';

const RapportDesRendezVous = () => {
  // États pour les filtres
  const [dateDebut, setDateDebut] = useState<Date | null>(new Date('2022-09-01'));
  const [dateFin, setDateFin] = useState<Date | null>(new Date('2022-09-30'));
  const [sourceDonnees, setSourceDonnees] = useState('rendez-vous');

  // État pour la modale des options de mise en page
  const [optionsOpened, { open: openOptions, close: closeOptions }] = useDisclosure(false);

  // États pour les options de mise en page
  const [showTotauxGeneraux, setShowTotauxGeneraux] = useState(true);
  const [showSousTotaux, setShowSousTotaux] = useState(false);
  const [formeCompacte, setFormeCompacte] = useState(true);

  // Données d'exemple correspondant à l'image
  const donneesRendezVous = [
    { date: '06/09/2022', statut: 'Annulé', nomPatient: '', rate: 1, realise: 1, total: 2 },
    { date: '12/09/2022', statut: '', nomPatient: '', rate: 0, realise: 4, total: 4 },
    { date: '13/09/2022', statut: '', nomPatient: '', rate: 0, realise: 2, total: 2 },
    { date: '14/09/2022', statut: '', nomPatient: '', rate: 0, realise: 2, total: 2 },
    { date: '15/09/2022', statut: '', nomPatient: '', rate: 0, realise: 1, total: 3 },
    { date: '16/09/2022', statut: '', nomPatient: '', rate: 0, realise: 1, total: 1 },
  ];

  // Calcul des totaux
  const totalRate = donneesRendezVous.reduce((sum, item) => sum + item.rate, 0);
  const totalRealise = donneesRendezVous.reduce((sum, item) => sum + item.realise, 0);
  const totalGeneral = donneesRendezVous.reduce((sum, item) => sum + item.total, 0);

  return (
    <Box className="w-full h-full bg-gray-50">
      {/* Header avec titre et boutons d'action */}
      <Card
        shadow="none"
        padding="md"
        radius={0}
        className="bg-slate-600 text-white border-b"
      >
        <Group justify="space-between" align="center">
          <Group align="center" gap="sm">
            <IconCalendarEvent size={20} className="text-white" />
            <Title order={4} className="text-white font-medium">
              Rapport des rendez-vous
            </Title>
          </Group>

          <Group gap="xs">
            <Tooltip label="Imprimer">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconPrinter size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Exporter">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileExport size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Format">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconFileText size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Champs">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
              >
                <IconTable size={18} />
              </ActionIcon>
            </Tooltip>

            <Tooltip label="Options">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                className="text-gray-300 hover:text-white hover:bg-slate-500"
                onClick={openOptions}
              >
                <IconSettings size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Group>
      </Card>

      {/* Contenu principal */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar gauche avec les filtres */}
        <Card
          shadow="none"
          padding="sm"
          radius={0}
          className="w-64 bg-white border-r border-gray-200"
        >
          <Stack gap="md">
            {/* Filtre Date Du */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Du
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  placeholder="01/09/2022"
                  value={dateDebut}
                  onChange={setDateDebut}
                  size="xs"
                  className="w-full"
                  leftSection={<IconCalendar size={14} />}
                  valueFormat="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Filtre Date Au */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Au
                </Text>
              </div>
              <div className="p-2">
                <DatePickerInput
                  placeholder="30/09/2022"
                  value={dateFin}
                  onChange={setDateFin}
                  size="xs"
                  className="w-full"
                  leftSection={<IconCalendar size={14} />}
                  valueFormat="DD/MM/YYYY"
                />
              </div>
            </div>

            {/* Filtre Source de données */}
            <div className="border border-gray-300 rounded">
              <div className="bg-gray-100 px-2 py-1 border-b border-gray-300">
                <Text size="sm" fw={500} className="text-gray-700">
                  Source de données
                </Text>
              </div>
              <div className="p-2">
                <Radio.Group
                  value={sourceDonnees}
                  onChange={setSourceDonnees}
                  size="xs"
                >
                  <Stack gap="xs">
                    <Radio value="rendez-vous" label="Rendez-Vous" />
                    <Radio value="entree-visite" label="Entrée/Visite" />
                  </Stack>
                </Radio.Group>
              </div>
            </div>
          </Stack>
        </Card>

        {/* Zone principale du tableau */}
        <div className="flex-1 bg-white">
          <Table
            striped={false}
            highlightOnHover={false}
            withTableBorder={true}
            withColumnBorders={true}
            className="h-full"
          >
            <Table.Thead className="bg-gray-50">
              <Table.Tr>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Date du rendez-vous
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Statut
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Nom du patient
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Raté
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Réalisé/Consommé
                </Table.Th>
                <Table.Th className="border-r border-gray-300 bg-gray-100 text-gray-700 font-medium text-sm">
                  Total
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {/* Données des rendez-vous */}
              {donneesRendezVous.map((item, index) => (
                <Table.Tr key={index} className="hover:bg-gray-50">
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.date}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.statut}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300">
                    <Text size="sm" className="text-gray-800">
                      {item.nomPatient}
                    </Text>
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-center">
                    {item.rate > 0 && (
                      <Text size="sm" className="text-gray-800">
                        {item.rate}
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-center">
                    {item.realise > 0 && (
                      <Text size="sm" className="text-gray-800">
                        {item.realise}
                      </Text>
                    )}
                  </Table.Td>
                  <Table.Td className="border-r border-gray-300 text-center">
                    <Text size="sm" className="text-gray-800">
                      {item.total}
                    </Text>
                  </Table.Td>
                </Table.Tr>
              ))}

              {/* Ligne Total avec fond vert */}
              <Table.Tr className="bg-green-100">
                <Table.Td className="border-r border-gray-300 bg-green-100">
                  <Text size="sm" fw={500} className="text-gray-800">
                    Total
                  </Text>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-100" />
                <Table.Td className="border-r border-gray-300 bg-green-100" />
                <Table.Td className="border-r border-gray-300 bg-green-200 text-center">
                  <div className="inline-block px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      {totalRate}
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200 text-center">
                  <div className="inline-block px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      {totalRealise}
                    </Text>
                  </div>
                </Table.Td>
                <Table.Td className="border-r border-gray-300 bg-green-200 text-center">
                  <div className="inline-block px-2 py-1 rounded bg-green-500">
                    <Text size="sm" className="text-white font-medium">
                      {totalGeneral}
                    </Text>
                  </div>
                </Table.Td>
              </Table.Tr>

              {/* Lignes vides pour remplir l'espace */}
              {Array.from({ length: 15 }, (_, index) => (
                <Table.Tr key={`empty-${index}`} className="hover:bg-gray-50">
                  {Array.from({ length: 6 }, (_, cellIndex) => (
                    <Table.Td
                      key={cellIndex}
                      className="border-r border-gray-300 h-8"
                    />
                  ))}
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
      </div>

      {/* Modale des options de mise en page */}
      <Modal.Root opened={optionsOpened} onClose={closeOptions} size="md" centered>
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header style={{ background: "#3799CE", padding: "11px" }}>
            <Modal.Title>
              <Text fw={600} c="var(--mantine-color-white)" className="flex gap-2 text-lg">
                <IconSettings size={24} />
                Options de mise en page
              </Text>
            </Modal.Title>
            <Modal.CloseButton className="text-white hover:bg-[#2d89bd]" />
          </Modal.Header>
          <Modal.Body p="md">
            <Stack gap="lg">
              {/* Section LES TOTAUX GÉNÉRAUX */}
              <div>
                <Text fw={500} size="sm" mb="sm" className="text-gray-700">
                  LES TOTAUX GÉNÉRAUX
                </Text>
                <Stack gap="xs">
                  <Checkbox
                    checked={showTotauxGeneraux}
                    onChange={(event) => setShowTotauxGeneraux(event.currentTarget.checked)}
                    label="Afficher les totaux généraux"
                    size="sm"
                  />
                  <div className="ml-6">
                    <Checkbox
                      checked={!showTotauxGeneraux}
                      onChange={(event) => setShowTotauxGeneraux(!event.currentTarget.checked)}
                      label="Ne pas afficher les totaux généraux"
                      size="sm"
                    />
                  </div>
                </Stack>
              </div>

              <Divider />

              {/* Section LES SOUS-TOTAUX */}
              <div>
                <Text fw={500} size="sm" mb="sm" className="text-gray-700">
                  LES SOUS-TOTAUX
                </Text>
                <Stack gap="xs">
                  <Checkbox
                    checked={showSousTotaux}
                    onChange={(event) => setShowSousTotaux(event.currentTarget.checked)}
                    label="Afficher les sous-totaux"
                    size="sm"
                  />
                  <div className="ml-6">
                    <Stack gap="xs">
                      <Checkbox
                        checked={!showSousTotaux}
                        onChange={(event) => setShowSousTotaux(!event.currentTarget.checked)}
                        label="Afficher les lignes de sous-total seulement"
                        size="sm"
                      />
                      <Checkbox
                        checked={!showSousTotaux}
                        onChange={(event) => setShowSousTotaux(!event.currentTarget.checked)}
                        label="Afficher les sous-total des colonnes seulement"
                        size="sm"
                      />
                    </Stack>
                  </div>
                </Stack>
              </div>

              <Divider />

              {/* Section MISE EN PAGE */}
              <div>
                <Text fw={500} size="sm" mb="sm" className="text-gray-700">
                  MISE EN PAGE
                </Text>
                <Stack gap="xs">
                  <Checkbox
                    checked={formeCompacte}
                    onChange={(event) => setFormeCompacte(event.currentTarget.checked)}
                    label="Forme compacte"
                    size="sm"
                  />
                  <div className="ml-6">
                    <Stack gap="xs">
                      <Checkbox
                        checked={!formeCompacte}
                        onChange={(event) => setFormeCompacte(!event.currentTarget.checked)}
                        label="Forme classique"
                        size="sm"
                      />
                      <Checkbox
                        checked={false}
                        label="Plate forme"
                        size="sm"
                      />
                    </Stack>
                  </div>
                </Stack>
              </div>
            </Stack>

            <Group justify="flex-end" mt="xl">
              <Button variant="outline" onClick={closeOptions}>
                ANNULER
              </Button>
              <Button onClick={closeOptions}>
                APPLIQUER
              </Button>
            </Group>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </Box>
  );
};

export default RapportDesRendezVous;
